You have successfully created an Atlassian Plugin!

Here are the SDK commands you'll use immediately:

* atlas-run   -- installs this plugin into the product and starts it on localhost
* atlas-debug -- same as atlas-run, but allows a debugger to attach at port 5005
* atlas-help  -- prints description for all commands in the SDK

Full documentation is always available at:

https://developer.atlassian.com/display/DOCS/Introduction+to+the+Atlassian+Plugin+SDK

<!DOCTYPE html><html lang="en"><head><meta charset="utf-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><title>噢！遇到了一个错误。</title><script type="text/javascript">contextPath = "/jira";</script><link type='text/css' rel='stylesheet' href='/jira/static-assets/metal-all.css' media='all'><script src='/jira/static-assets/jquery-min.js'></script><script src='/jira/static-assets/metal-all.js'></script><meta name="decorator" content="none" /></head><body class=" error-page error500"><script type="text/javascript">document.body.className += " js-enabled";</script><div id="page"><header id="header" role="banner" aria-label="站点"></header><div id="content"><div class="aui-page-panel" ><div class="aui-page-panel-inner"><main role="main" id="main" class="aui-page-panel-content lowerContent" ><div id="error-state"><span class="error-type"></span><h1>很抱歉, 我们已有技术上的问题的上一次操作期间。</h1><h2 class="technical-details-header"><span>请求协助</span></h2><div id="technical-details-content" class="technical-details js-hidden"><p>复制下面内容并发送给Jira管理员</p><div class="technical-details-content" contentEditable readonly><h2>技术细节</h2><p class="referral">日志查询编号： <strong id="log-referral-id">ae4e1371-0e8c-409f-a0c0-f09e6139baf9</strong></p><div class="info-section" id="causePanel"><h2>原因</h2><p>参考URL: <b>http://localhost:2990/jira/secure/admin/workflows/AddWorkflowTransitionFunctionParams!default.jspa?workflowName=test1&amp;workflowMode=draft&amp;descriptorTab=postfunctions&amp;workflowTransition=21&amp;pluginModuleKey=com.eve.eve-jira%3ARunNextTransitionFunction&amp;atl_token=BWP3-NZB2-6EDY-6C7K_46311e3f9ddfa4945adc867e5ff19bed29c8f543_lin</b></p><pre>org.codehaus.jackson.map.JsonMappingException: No serializer found for class com.eve.beans.TransitionBean and no properties discovered to create BeanSerializer (to avoid exception, disable SerializationConfig.Feature.FAIL_ON_EMPTY_BEANS) ) (through reference chain: com.eve.beans.ResultBean[&quot;value&quot;]-&gt;java.util.ArrayList[0])</pre><blockquote id="stacktrace" style="overflow-x: auto;"><pre>org.codehaus.jackson.map.JsonMappingException: No serializer found for class com.eve.beans.TransitionBean and no properties discovered to create BeanSerializer (to avoid exception, disable SerializationConfig.Feature.FAIL_ON_EMPTY_BEANS) ) (through reference chain: com.eve.beans.ResultBean[&quot;value&quot;]-&gt;java.util.ArrayList[0])
	at org.codehaus.jackson.map.ser.impl.UnknownSerializer.failForEmpty(UnknownSerializer.java:52) [jackson-mapper-asl-1.9.13-atlassian-4.jar:1.9.13-atlassian-4]
	at org.codehaus.jackson.map.ser.impl.UnknownSerializer.serialize(UnknownSerializer.java:25) [jackson-mapper-asl-1.9.13-atlassian-4.jar:1.9.13-atlassian-4]
	at org.codehaus.jackson.map.ser.std.StdContainerSerializers$IndexedListSerializer.serializeContents(StdContainerSerializers.java:122) [jackson-mapper-asl-1.9.13-atlassian-4.jar:1.9.13-atlassian-4]
	at org.codehaus.jackson.map.ser.std.StdContainerSerializers$IndexedListSerializer.serializeContents(StdContainerSerializers.java:71) [jackson-mapper-asl-1.9.13-atlassian-4.jar:1.9.13-atlassian-4]
	at org.codehaus.jackson.map.ser.std.AsArraySerializerBase.serialize(AsArraySerializerBase.java:86) [jackson-mapper-asl-1.9.13-atlassian-4.jar:1.9.13-atlassian-4]
	at org.codehaus.jackson.map.ser.BeanPropertyWriter.serializeAsField(BeanPropertyWriter.java:446) [jackson-mapper-asl-1.9.13-atlassian-4.jar:1.9.13-atlassian-4]
	at org.codehaus.jackson.map.ser.std.BeanSerializerBase.serializeFields(BeanSerializerBase.java:150) [jackson-mapper-asl-1.9.13-atlassian-4.jar:1.9.13-atlassian-4]
	at org.codehaus.jackson.map.ser.BeanSerializer.serialize(BeanSerializer.java:112) [jackson-mapper-asl-1.9.13-atlassian-4.jar:1.9.13-atlassian-4]
	at org.codehaus.jackson.map.ser.StdSerializerProvider._serializeValue(StdSerializerProvider.java:610) [jackson-mapper-asl-1.9.13-atlassian-4.jar:1.9.13-atlassian-4]
	at org.codehaus.jackson.map.ser.StdSerializerProvider.serializeValue(StdSerializerProvider.java:256) [jackson-mapper-asl-1.9.13-atlassian-4.jar:1.9.13-atlassian-4]
	at org.codehaus.jackson.map.ObjectMapper.writeValue(ObjectMapper.java:1613) [jackson-mapper-asl-1.9.13-atlassian-4.jar:1.9.13-atlassian-4]
	at org.codehaus.jackson.jaxrs.JacksonJsonProvider.writeTo(JacksonJsonProvider.java:559) [?:?]
	at com.sun.jersey.spi.container.ContainerResponse.write(ContainerResponse.java:302) [?:?]
	at com.sun.jersey.server.impl.application.WebApplicationImpl._handleRequest(WebApplicationImpl.java:1510) [?:?]
	at com.sun.jersey.server.impl.application.WebApplicationImpl.handleRequest(WebApplicationImpl.java:1419) [?:?]
	at com.sun.jersey.server.impl.application.WebApplicationImpl.handleRequest(WebApplicationImpl.java:1409) [?:?]
	at com.sun.jersey.spi.container.servlet.WebComponent.service(WebComponent.java:409) [?:?]
	at com.sun.jersey.spi.container.servlet.ServletContainer.service(ServletContainer.java:558) [?:?]
	at com.atlassian.plugins.rest.module.RestDelegatingServletFilter$JerseyOsgiServletContainer.doFilter(RestDelegatingServletFilter.java:160) [?:?]
	at com.sun.jersey.spi.container.servlet.ServletContainer.doFilter(ServletContainer.java:829) [?:?]
	at com.atlassian.plugins.rest.module.RestDelegatingServletFilter.doFilter(RestDelegatingServletFilter.java:70) [?:?]
	at com.atlassian.plugin.servlet.filter.DelegatingPluginFilter.doFilter(DelegatingPluginFilter.java:62) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.plugin.servlet.filter.IteratingFilterChain.doFilter(IteratingFilterChain.java:37) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.plugin.servlet.filter.ServletFilterModuleContainerFilter.doFilter(ServletFilterModuleContainerFilter.java:55) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.plugin.servlet.filter.ServletFilterModuleContainerFilter.doFilter(ServletFilterModuleContainerFilter.java:43) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.plugin.servlet.filter.DelegatingPluginFilter.doFilter(DelegatingPluginFilter.java:62) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.plugin.servlet.filter.IteratingFilterChain.doFilter(IteratingFilterChain.java:37) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.plugin.servlet.filter.DelegatingPluginFilter.lambda$doFilter$0(DelegatingPluginFilter.java:57) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.plugins.rest.module.servlet.RestServletUtilsUpdaterFilter.doFilterInternal(RestServletUtilsUpdaterFilter.java:23) [?:?]
	at com.atlassian.plugins.rest.module.servlet.RestServletUtilsUpdaterFilter.doFilter(RestServletUtilsUpdaterFilter.java:35) [?:?]
	at com.atlassian.plugin.servlet.filter.DelegatingPluginFilter.doFilter(DelegatingPluginFilter.java:62) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.plugin.servlet.filter.IteratingFilterChain.doFilter(IteratingFilterChain.java:37) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.plugin.servlet.filter.DelegatingPluginFilter.lambda$doFilter$0(DelegatingPluginFilter.java:57) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.applinks.core.rest.context.ContextFilter.doFilter(ContextFilter.java:24) [?:?]
	at com.atlassian.plugin.servlet.filter.DelegatingPluginFilter.doFilter(DelegatingPluginFilter.java:62) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.plugin.servlet.filter.IteratingFilterChain.doFilter(IteratingFilterChain.java:37) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.plugin.servlet.filter.DelegatingPluginFilter.lambda$doFilter$0(DelegatingPluginFilter.java:57) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.applinks.core.rest.context.ContextFilter.doFilter(ContextFilter.java:24) [?:?]
	at com.atlassian.plugin.servlet.filter.DelegatingPluginFilter.doFilter(DelegatingPluginFilter.java:62) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.plugin.servlet.filter.IteratingFilterChain.doFilter(IteratingFilterChain.java:37) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.plugin.servlet.filter.DelegatingPluginFilter.lambda$doFilter$0(DelegatingPluginFilter.java:57) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.applinks.core.rest.context.ContextFilter.doFilter(ContextFilter.java:24) [?:?]
	at com.atlassian.plugin.servlet.filter.DelegatingPluginFilter.doFilter(DelegatingPluginFilter.java:62) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.plugin.servlet.filter.IteratingFilterChain.doFilter(IteratingFilterChain.java:37) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.plugin.servlet.filter.DelegatingPluginFilter.lambda$doFilter$0(DelegatingPluginFilter.java:57) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.applinks.core.rest.context.ContextFilter.doFilter(ContextFilter.java:24) [?:?]
	at com.atlassian.plugin.servlet.filter.DelegatingPluginFilter.doFilter(DelegatingPluginFilter.java:62) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.plugin.servlet.filter.IteratingFilterChain.doFilter(IteratingFilterChain.java:37) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.plugin.servlet.filter.DelegatingPluginFilter.lambda$doFilter$0(DelegatingPluginFilter.java:57) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.applinks.core.rest.context.ContextFilter.doFilter(ContextFilter.java:24) [?:?]
	at com.atlassian.plugin.servlet.filter.DelegatingPluginFilter.doFilter(DelegatingPluginFilter.java:62) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.plugin.servlet.filter.IteratingFilterChain.doFilter(IteratingFilterChain.java:37) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.plugin.servlet.filter.DelegatingPluginFilter.lambda$doFilter$0(DelegatingPluginFilter.java:57) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.mywork.client.filter.ServingRequestsFilter.doFilter(ServingRequestsFilter.java:32) [?:?]
	at com.atlassian.plugin.servlet.filter.DelegatingPluginFilter.doFilter(DelegatingPluginFilter.java:62) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.plugin.servlet.filter.IteratingFilterChain.doFilter(IteratingFilterChain.java:37) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.plugin.servlet.filter.DelegatingPluginFilter.lambda$doFilter$0(DelegatingPluginFilter.java:57) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.jira.plugin.mobile.web.filter.MobileAppRequestFilter.doFilter(MobileAppRequestFilter.java:59) [?:?]
	at com.atlassian.core.filters.AbstractHttpFilter.doFilter(AbstractHttpFilter.java:32) [atlassian-core-7.0.2.jar:?]
	at com.atlassian.plugin.servlet.filter.DelegatingPluginFilter.doFilter(DelegatingPluginFilter.java:62) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.plugin.servlet.filter.IteratingFilterChain.doFilter(IteratingFilterChain.java:37) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.plugin.servlet.filter.DelegatingPluginFilter.lambda$doFilter$0(DelegatingPluginFilter.java:57) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.jira.plugin.mobile.login.MobileLoginSuccessFilter.doFilter(MobileLoginSuccessFilter.java:54) [?:?]
	at com.atlassian.plugin.servlet.filter.DelegatingPluginFilter.doFilter(DelegatingPluginFilter.java:62) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.plugin.servlet.filter.IteratingFilterChain.doFilter(IteratingFilterChain.java:37) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.plugin.servlet.filter.DelegatingPluginFilter.lambda$doFilter$0(DelegatingPluginFilter.java:57) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.diagnostics.internal.platform.monitor.http.HttpRequestMonitoringFilter.doFilter(HttpRequestMonitoringFilter.java:55) [atlassian-diagnostics-platform-1.1.13.jar:?]
	at com.atlassian.plugin.servlet.filter.DelegatingPluginFilter.doFilter(DelegatingPluginFilter.java:62) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.plugin.servlet.filter.IteratingFilterChain.doFilter(IteratingFilterChain.java:37) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.plugin.servlet.filter.DelegatingPluginFilter.lambda$doFilter$0(DelegatingPluginFilter.java:57) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.analytics.client.filter.UniversalAnalyticsFilter.doFilter(UniversalAnalyticsFilter.java:75) [?:?]
	at com.atlassian.analytics.client.filter.AbstractHttpFilter.doFilter(AbstractHttpFilter.java:33) [?:?]
	at com.atlassian.plugin.servlet.filter.DelegatingPluginFilter.doFilter(DelegatingPluginFilter.java:62) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.plugin.servlet.filter.IteratingFilterChain.doFilter(IteratingFilterChain.java:37) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.plugin.servlet.filter.DelegatingPluginFilter.lambda$doFilter$0(DelegatingPluginFilter.java:57) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.web.servlet.plugin.request.RedirectInterceptingFilter.doFilter(RedirectInterceptingFilter.java:21) [?:?]
	at com.atlassian.core.filters.AbstractHttpFilter.doFilter(AbstractHttpFilter.java:32) [atlassian-core-7.0.2.jar:?]
	at com.atlassian.plugin.servlet.filter.DelegatingPluginFilter.doFilter(DelegatingPluginFilter.java:62) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.plugin.servlet.filter.IteratingFilterChain.doFilter(IteratingFilterChain.java:37) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.plugin.servlet.filter.DelegatingPluginFilter.lambda$doFilter$0(DelegatingPluginFilter.java:57) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.prettyurls.filter.PrettyUrlsSiteMeshFixupFilter.doFilter(PrettyUrlsSiteMeshFixupFilter.java:32) [?:?]
	at com.atlassian.plugin.servlet.filter.DelegatingPluginFilter.doFilter(DelegatingPluginFilter.java:62) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.plugin.servlet.filter.IteratingFilterChain.doFilter(IteratingFilterChain.java:37) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.plugin.servlet.filter.DelegatingPluginFilter.lambda$doFilter$0(DelegatingPluginFilter.java:57) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.prettyurls.filter.PrettyUrlsDispatcherFilter.doFilter(PrettyUrlsDispatcherFilter.java:55) [?:?]
	at com.atlassian.plugin.servlet.filter.DelegatingPluginFilter.doFilter(DelegatingPluginFilter.java:62) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.plugin.servlet.filter.IteratingFilterChain.doFilter(IteratingFilterChain.java:37) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.plugin.servlet.filter.DelegatingPluginFilter.lambda$doFilter$0(DelegatingPluginFilter.java:57) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.prettyurls.filter.PrettyUrlsSiteMeshFilter.doFilter(PrettyUrlsSiteMeshFilter.java:80) [?:?]
	at com.atlassian.plugin.servlet.filter.DelegatingPluginFilter.doFilter(DelegatingPluginFilter.java:62) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.plugin.servlet.filter.IteratingFilterChain.doFilter(IteratingFilterChain.java:37) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.plugin.servlet.filter.DelegatingPluginFilter.lambda$doFilter$0(DelegatingPluginFilter.java:57) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.prettyurls.filter.PrettyUrlsMatcherFilter.doFilter(PrettyUrlsMatcherFilter.java:51) [?:?]
	at com.atlassian.plugin.servlet.filter.DelegatingPluginFilter.doFilter(DelegatingPluginFilter.java:62) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.plugin.servlet.filter.IteratingFilterChain.doFilter(IteratingFilterChain.java:37) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.plugin.servlet.filter.DelegatingPluginFilter.lambda$doFilter$0(DelegatingPluginFilter.java:57) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.labs.botkiller.BotKillerFilter.doFilter(BotKillerFilter.java:35) [?:?]
	at com.atlassian.plugin.servlet.filter.DelegatingPluginFilter.doFilter(DelegatingPluginFilter.java:62) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.plugin.servlet.filter.IteratingFilterChain.doFilter(IteratingFilterChain.java:37) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.plugin.servlet.filter.DelegatingPluginFilter.lambda$doFilter$0(DelegatingPluginFilter.java:57) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.labs.httpservice.resource.ResourceFilter.doFilter(ResourceFilter.java:59) [?:?]
	at com.atlassian.plugin.servlet.filter.DelegatingPluginFilter.doFilter(DelegatingPluginFilter.java:62) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.plugin.servlet.filter.IteratingFilterChain.doFilter(IteratingFilterChain.java:37) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.plugin.servlet.filter.ServletFilterModuleContainerFilter.doFilter(ServletFilterModuleContainerFilter.java:55) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.plugin.servlet.filter.ServletFilterModuleContainerFilter.doFilter(ServletFilterModuleContainerFilter.java:43) [atlassian-plugins-servlet-5.3.11.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [catalina.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [catalina.jar:8.5.35]
	at com.atlassian.jira.web.filters.MobileAppRequestFilter.doFilter(MobileAppRequestFilter.java:36) [classes/:?]
	at com.atlassian.core.filters.AbstractHttpFilter.doFilter(AbstractHttpFilter.java:32) [atlassian-core-7.0.2.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [catalina.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [catalina.jar:8.5.35]
	at com.atlassian.jira.web.filters.accesslog.AccessLogFilter.executeRequest(AccessLogFilter.java:93) [classes/:?]
	at com.atlassian.jira.web.filters.accesslog.AccessLogFilter.doFilter(AccessLogFilter.java:79) [classes/:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [catalina.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [catalina.jar:8.5.35]
	at com.atlassian.jira.web.filters.pagebuilder.PageBuilderFilter.doFilter(PageBuilderFilter.java:81) [classes/:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [catalina.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [catalina.jar:8.5.35]
	at com.atlassian.jira.web.filters.CommittedResponseHtmlErrorRecoveryFilter.doFilter(CommittedResponseHtmlErrorRecoveryFilter.java:55) [classes/:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [catalina.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [catalina.jar:8.5.35]
	at com.atlassian.plugin.servlet.filter.IteratingFilterChain.doFilter(IteratingFilterChain.java:39) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.plugin.servlet.filter.DelegatingPluginFilter.lambda$doFilter$0(DelegatingPluginFilter.java:57) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.ratelimiting.internal.filter.RateLimitFilter.doFilter(RateLimitFilter.java:73) [?:?]
	at com.atlassian.plugin.servlet.filter.DelegatingPluginFilter.doFilter(DelegatingPluginFilter.java:62) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.plugin.servlet.filter.IteratingFilterChain.doFilter(IteratingFilterChain.java:37) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.plugin.servlet.filter.DelegatingPluginFilter.lambda$doFilter$0(DelegatingPluginFilter.java:57) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.prettyurls.filter.PrettyUrlsCombinedMatchDispatcherFilter.doFilter(PrettyUrlsCombinedMatchDispatcherFilter.java:56) [?:?]
	at com.atlassian.plugin.servlet.filter.DelegatingPluginFilter.doFilter(DelegatingPluginFilter.java:62) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.plugin.servlet.filter.IteratingFilterChain.doFilter(IteratingFilterChain.java:37) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.plugin.servlet.filter.ServletFilterModuleContainerFilter.doFilter(ServletFilterModuleContainerFilter.java:55) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.plugin.servlet.filter.ServletFilterModuleContainerFilter.doFilter(ServletFilterModuleContainerFilter.java:43) [atlassian-plugins-servlet-5.3.11.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [catalina.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [catalina.jar:8.5.35]
	at com.atlassian.jira.security.xsrf.XsrfTokenAdditionRequestFilter.doFilter(XsrfTokenAdditionRequestFilter.java:46) [classes/:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [catalina.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [catalina.jar:8.5.35]
	at com.atlassian.jira.web.filters.MauEventFilter.doFilter(MauEventFilter.java:49) [classes/:?]
	at com.atlassian.core.filters.AbstractHttpFilter.doFilter(AbstractHttpFilter.java:32) [atlassian-core-7.0.2.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [catalina.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [catalina.jar:8.5.35]
	at com.atlassian.jira.security.JiraSecurityFilter.lambda$doFilter$0(JiraSecurityFilter.java:66) [classes/:?]
	at com.atlassian.seraph.filter.SecurityFilter.doFilter(SecurityFilter.java:242) [atlassian-seraph-4.1.0.jar:?]
	at com.atlassian.jira.security.JiraSecurityFilter.doFilter(JiraSecurityFilter.java:64) [classes/:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [catalina.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [catalina.jar:8.5.35]
	at com.atlassian.security.auth.trustedapps.filter.TrustedApplicationsFilter.doFilter(TrustedApplicationsFilter.java:94) [atlassian-trusted-apps-core-5.0.0.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [catalina.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [catalina.jar:8.5.35]
	at com.atlassian.seraph.filter.BaseLoginFilter.doFilter(BaseLoginFilter.java:148) [atlassian-seraph-4.1.0.jar:?]
	at com.atlassian.jira.web.filters.JiraLoginFilter.doFilter(JiraLoginFilter.java:77) [classes/:?]
	at com.atlassian.core.filters.AbstractHttpFilter.doFilter(AbstractHttpFilter.java:32) [atlassian-core-7.0.2.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [catalina.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [catalina.jar:8.5.35]
	at com.atlassian.plugin.servlet.filter.IteratingFilterChain.doFilter(IteratingFilterChain.java:39) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.plugin.servlet.filter.DelegatingPluginFilter.lambda$doFilter$0(DelegatingPluginFilter.java:57) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.oauth.serviceprovider.internal.servlet.OAuthFilter.doFilter(OAuthFilter.java:67) [?:?]
	at com.atlassian.plugin.servlet.filter.DelegatingPluginFilter.doFilter(DelegatingPluginFilter.java:62) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.plugin.servlet.filter.IteratingFilterChain.doFilter(IteratingFilterChain.java:37) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.plugin.servlet.filter.DelegatingPluginFilter.lambda$doFilter$0(DelegatingPluginFilter.java:57) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.plugins.rest.module.servlet.RestSeraphFilter.doFilter(RestSeraphFilter.java:38) [?:?]
	at com.atlassian.plugin.servlet.filter.DelegatingPluginFilter.doFilter(DelegatingPluginFilter.java:62) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.plugin.servlet.filter.IteratingFilterChain.doFilter(IteratingFilterChain.java:37) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.plugin.servlet.filter.DelegatingPluginFilter.lambda$doFilter$0(DelegatingPluginFilter.java:57) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.pats.web.filter.TokenBasedAuthenticationFilter.doFilter(TokenBasedAuthenticationFilter.java:82) [?:?]
	at com.atlassian.plugin.servlet.filter.DelegatingPluginFilter.doFilter(DelegatingPluginFilter.java:62) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.plugin.servlet.filter.IteratingFilterChain.doFilter(IteratingFilterChain.java:37) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.plugin.servlet.filter.DelegatingPluginFilter.lambda$doFilter$0(DelegatingPluginFilter.java:57) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.prettyurls.filter.PrettyUrlsCombinedMatchDispatcherFilter.doFilter(PrettyUrlsCombinedMatchDispatcherFilter.java:56) [?:?]
	at com.atlassian.plugin.servlet.filter.DelegatingPluginFilter.doFilter(DelegatingPluginFilter.java:62) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.plugin.servlet.filter.IteratingFilterChain.doFilter(IteratingFilterChain.java:37) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.plugin.servlet.filter.ServletFilterModuleContainerFilter.doFilter(ServletFilterModuleContainerFilter.java:55) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.plugin.servlet.filter.ServletFilterModuleContainerFilter.doFilter(ServletFilterModuleContainerFilter.java:43) [atlassian-plugins-servlet-5.3.11.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [catalina.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [catalina.jar:8.5.35]
	at com.atlassian.jira.web.filters.johnson.JiraJohnson503Filter.doFilter(JiraJohnson503Filter.java:79) [classes/:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [catalina.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [catalina.jar:8.5.35]
	at org.tuckey.web.filters.urlrewrite.RuleChain.handleRewrite(RuleChain.java:176) [urlrewritefilter-4.0.3.jar:4.0.3]
	at org.tuckey.web.filters.urlrewrite.RuleChain.doRules(RuleChain.java:145) [urlrewritefilter-4.0.3.jar:4.0.3]
	at org.tuckey.web.filters.urlrewrite.UrlRewriter.processRequest(UrlRewriter.java:92) [urlrewritefilter-4.0.3.jar:4.0.3]
	at org.tuckey.web.filters.urlrewrite.UrlRewriteFilter.doFilter(UrlRewriteFilter.java:394) [urlrewritefilter-4.0.3.jar:4.0.3]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [catalina.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [catalina.jar:8.5.35]
	at com.atlassian.jira.servermetrics.CorrelationIdPopulatorFilter.doFilter(CorrelationIdPopulatorFilter.java:30) [classes/:?]
	at com.atlassian.core.filters.AbstractHttpFilter.doFilter(AbstractHttpFilter.java:32) [atlassian-core-7.0.2.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [catalina.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [catalina.jar:8.5.35]
	at com.atlassian.plugin.servlet.filter.IteratingFilterChain.doFilter(IteratingFilterChain.java:39) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.plugin.servlet.filter.DelegatingPluginFilter.lambda$doFilter$0(DelegatingPluginFilter.java:57) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.analytics.client.filter.JiraAnalyticsFilter.doFilter(JiraAnalyticsFilter.java:30) [?:?]
	at com.atlassian.analytics.client.filter.AbstractHttpFilter.doFilter(AbstractHttpFilter.java:33) [?:?]
	at com.atlassian.plugin.servlet.filter.DelegatingPluginFilter.doFilter(DelegatingPluginFilter.java:62) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.plugin.servlet.filter.IteratingFilterChain.doFilter(IteratingFilterChain.java:37) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.plugin.servlet.filter.DelegatingPluginFilter.lambda$doFilter$0(DelegatingPluginFilter.java:57) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.ratelimiting.internal.filter.RateLimitPreAuthFilter.doFilter(RateLimitPreAuthFilter.java:71) [?:?]
	at com.atlassian.plugin.servlet.filter.DelegatingPluginFilter.doFilter(DelegatingPluginFilter.java:62) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.plugin.servlet.filter.IteratingFilterChain.doFilter(IteratingFilterChain.java:37) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.plugin.servlet.filter.DelegatingPluginFilter.lambda$doFilter$0(DelegatingPluginFilter.java:57) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.web.servlet.plugin.request.RedirectInterceptingFilter.doFilter(RedirectInterceptingFilter.java:21) [?:?]
	at com.atlassian.core.filters.AbstractHttpFilter.doFilter(AbstractHttpFilter.java:32) [atlassian-core-7.0.2.jar:?]
	at com.atlassian.plugin.servlet.filter.DelegatingPluginFilter.doFilter(DelegatingPluginFilter.java:62) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.plugin.servlet.filter.IteratingFilterChain.doFilter(IteratingFilterChain.java:37) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.plugin.servlet.filter.DelegatingPluginFilter.lambda$doFilter$0(DelegatingPluginFilter.java:57) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.web.servlet.plugin.LocationCleanerFilter.doFilter(LocationCleanerFilter.java:36) [?:?]
	at com.atlassian.plugin.servlet.filter.DelegatingPluginFilter.doFilter(DelegatingPluginFilter.java:62) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.plugin.servlet.filter.IteratingFilterChain.doFilter(IteratingFilterChain.java:37) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.plugin.servlet.filter.DelegatingPluginFilter.lambda$doFilter$0(DelegatingPluginFilter.java:57) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.prettyurls.filter.PrettyUrlsCombinedMatchDispatcherFilter.doFilter(PrettyUrlsCombinedMatchDispatcherFilter.java:56) [?:?]
	at com.atlassian.plugin.servlet.filter.DelegatingPluginFilter.doFilter(DelegatingPluginFilter.java:62) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.plugin.servlet.filter.IteratingFilterChain.doFilter(IteratingFilterChain.java:37) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.plugin.servlet.filter.ServletFilterModuleContainerFilter.doFilter(ServletFilterModuleContainerFilter.java:55) [atlassian-plugins-servlet-5.3.11.jar:?]
	at com.atlassian.plugin.servlet.filter.ServletFilterModuleContainerFilter.doFilter(ServletFilterModuleContainerFilter.java:43) [atlassian-plugins-servlet-5.3.11.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [catalina.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [catalina.jar:8.5.35]
	at com.atlassian.jira.web.filters.steps.ChainedFilterStepRunner.doFilter(ChainedFilterStepRunner.java:74) [classes/:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [catalina.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [catalina.jar:8.5.35]
	at com.atlassian.core.filters.cache.AbstractCachingFilter.doFilter(AbstractCachingFilter.java:31) [atlassian-core-7.0.2.jar:?]
	at com.atlassian.core.filters.AbstractHttpFilter.doFilter(AbstractHttpFilter.java:32) [atlassian-core-7.0.2.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [catalina.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [catalina.jar:8.5.35]
	at com.atlassian.core.filters.encoding.AbstractEncodingFilter.doFilter(AbstractEncodingFilter.java:39) [atlassian-core-7.0.2.jar:?]
	at com.atlassian.core.filters.AbstractHttpFilter.doFilter(AbstractHttpFilter.java:32) [atlassian-core-7.0.2.jar:?]
	at com.atlassian.jira.web.filters.PathMatchingEncodingFilter.doFilter(PathMatchingEncodingFilter.java:39) [classes/:?]
	at com.atlassian.core.filters.AbstractHttpFilter.doFilter(AbstractHttpFilter.java:32) [atlassian-core-7.0.2.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [catalina.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [catalina.jar:8.5.35]
	at com.atlassian.jira.web.filters.MultipartBoundaryCheckFilter.doFilter(MultipartBoundaryCheckFilter.java:36) [classes/:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [catalina.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [catalina.jar:8.5.35]
	at com.atlassian.jira.servermetrics.MetricsCollectorFilter.doFilter(MetricsCollectorFilter.java:25) [classes/:?]
	at com.atlassian.core.filters.AbstractHttpFilter.doFilter(AbstractHttpFilter.java:32) [atlassian-core-7.0.2.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [catalina.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [catalina.jar:8.5.35]
	at com.atlassian.jira.web.filters.steps.ChainedFilterStepRunner.doFilter(ChainedFilterStepRunner.java:74) [classes/:?]
	at com.atlassian.jira.web.filters.JiraFirstFilter.doFilter(JiraFirstFilter.java:61) [classes/:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [catalina.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [catalina.jar:8.5.35]
	at com.atlassian.gzipfilter.GzipFilter.doFilterInternal(GzipFilter.java:115) [atlassian-gzipfilter-3.0.0.jar:?]
	at com.atlassian.gzipfilter.GzipFilter.doFilter(GzipFilter.java:92) [atlassian-gzipfilter-3.0.0.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [catalina.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [catalina.jar:8.5.35]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:198) [catalina.jar:8.5.35]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96) [catalina.jar:8.5.35]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:493) [catalina.jar:8.5.35]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:140) [catalina.jar:8.5.35]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:81) [catalina.jar:8.5.35]
	at org.apache.catalina.valves.AbstractAccessLogValve.invoke(AbstractAccessLogValve.java:650) [catalina.jar:8.5.35]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:87) [catalina.jar:8.5.35]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342) [catalina.jar:8.5.35]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:800) [tomcat-coyote.jar:8.5.35]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66) [tomcat-coyote.jar:8.5.35]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:806) [tomcat-coyote.jar:8.5.35]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1498) [tomcat-coyote.jar:8.5.35]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) [tomcat-coyote.jar:8.5.35]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [?:1.8.0_202]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) [tomcat-util.jar:8.5.35]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_202]
</pre></blockquote></div><div class="info-section" id="buildInfoPanel"><h2>Build信息</h2><table class="aui"><tbody><tr><th>运行时间</th><td>N/A</td></tr><tr><th>版本</th><td>8.15.0</td></tr><tr><th>创建号</th><td>815001</td></tr><tr><th>编译日期</th><td>Fri Jan 22 00:00:00 CST 2021</td></tr><tr><th>编译版本</th><td>9cd993cd5d44d1112926026f768b18ff3cb391a3</td></tr><tr><th>Atlassian合作伙伴</th><td>null</td></tr><tr><th>安装类型</th><td>unknown</td></tr><tr><th>服务器 ID</th><td>BWP3-NZB2-6EDY-6C7K</td></tr><tr><th>最后更新</th><td>07/十二月/22 4:30 下午 (v7.1.0-SNAPSHOT#71001)</td></tr></tbody></table></div><div class="info-section" id="serverInfoPanel"><h2>服务器信息</h2><table class="aui"><tbody><tr><th>应用服务器</th><td>Apache Tomcat/8.5.35</td></tr><tr><th>Servlet 版本</th><td>3.1</td></tr></tbody></table></div><div class="info-section" id="filePathsPanel"><h2>文件路径</h2><table class="aui"><tbody><tr><th>位置-atlassian jira 的日志</th><td>D:\CJW\Jira\localhost-jira\target\jira\home\log\atlassian-jira.log</td></tr><tr><th>位置 entityengine xml</th><td>file:/D:/CJW/Jira/localhost-jira/target/container/tomcat8x/cargo-jira-home/webapps/jira/WEB-INF/classes/entityengine.xml</td></tr></tbody></table></div><div class="info-section" id="memInfoPanel"><h2>内存信息</h2><table class="aui"><tbody><tr><th>总内存</th><td>1940 MB</td></tr><tr><th>可用内存</th><td>758 MB</td></tr><tr><th>已使用的内存</th><td>1182 MB</td></tr></tbody></table></div><div class="info-section" id="sysInfoPanel"><h2>系统信息</h2><table class="aui"><tbody><tr><th>系统数据</th><td>星期二, 27 十二月 2022</td></tr><tr><th>系统时间</th><td>13:10:24 +0800</td></tr><tr><th>当前工作目录</th><td>D:\CJW\Jira\localhost-jira\target\container\tomcat8x\cargo-jira-home</td></tr><tr><th>Java版本</th><td>1.8.0_202</td></tr><tr><th>Java供应商</th><td>Oracle Corporation</td></tr><tr><th>JVM 版本</th><td>1.8</td></tr><tr><th>JVM 供应商</th><td>Oracle Corporation</td></tr><tr><th>JVM 实施版本</th><td>25.202-b08</td></tr><tr><th>Java运行时间</th><td>Java(TM) SE Runtime Environment</td></tr><tr><th>Java虚拟机</th><td>Java HotSpot(TM) 64-Bit Server VM</td></tr><tr><th>用户名</th><td>蔡嘉伟</td></tr><tr><th>用户时区</th><td>Asia/Shanghai</td></tr><tr><th>用户环境</th><td>中文 (中国)</td></tr><tr><th>系统编码</th><td>GBK</td></tr><tr><th>操作系统</th><td>Windows 10 10.0</td></tr><tr><th>操作系统架构</th><td>amd64</td></tr><tr><th>应用程序服务器容器</th><td></td></tr><tr><th>数据库类型</th><td>h2</td></tr><tr><th>数据库 JNDI 地址</th><td>h2 java:comp/env/jdbc/JiraDS</td></tr><tr><th>数据库URL</th><td>隐藏</td></tr><tr><th>数据库版本</th><td>1.4.185 (2015-01-16)</td></tr><tr><th>数据库驱动</th><td>H2 JDBC Driver 1.4.185 (2015-01-16)</td></tr><tr><th>数据库排序规则</th><td>Unknown</td></tr><tr><th>数据库延迟测量毫秒</th><td>0</td></tr><tr><th>外部用户管理</th><td>关</td></tr><tr><th>JVM 输入参数</th><td>-Xmx2g -Xms1g -Xdebug -Xrunjdwp:transport=dt_socket,address=5005,suspend=n,server=y -Dbaseurl=http://eve-22007526:2990/jira -Dplugin.root.directories=D:\CJW\Jira\localhost-jira -Dcargo.servlet.uriencoding=UTF-8 -Dcom.atlassian.jira.startup.LauncherContextListener.SYNCHRONOUS=true -Dplugin.resource.directories=D:\CJW\Jira\localhost-jira\src\main\resources,D:\CJW\Jira\localhost-jira\src\main\java -Datlassian.dev.mode=true -DjarsToSkip=xapool*.jar,${tomcat.util.scan.StandardJarScanFilter.jarsToSkip},jotm*.jar -Djava.awt.headless=true -Djira.home=D:/CJW/Jira/localhost-jira/target/jira/home -Dcargo.datasource.datasource=cargo.datasource.url=jdbc:h2:file:D:/CJW/Jira/localhost-jira/target/jira/home/<USER>/h2db|cargo.datasource.driver=org.h2.Driver|cargo.datasource.username=sa|cargo.datasource.password=|cargo.datasource.jndi=jdbc/JiraDS -Datlassian.sdk.version=8.0.0 -Dcatalina.home=D:\CJW\Jira\localhost-jira\target\container\tomcat8x\apache-tomcat-8.5.35 -Dcatalina.base=D:\CJW\Jira\localhost-jira\target\container\tomcat8x\cargo-jira-home -Djava.io.tmpdir=D:\CJW\Jira\localhost-jira\target\container\tomcat8x\cargo-jira-home\temp -Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager -Djava.util.logging.config.file=D:\CJW\Jira\localhost-jira\target\container\tomcat8x\cargo-jira-home\conf/logging.properties</td></tr><tr><th>集群</th><td>关</td></tr></tbody></table></div><div class="info-section" id="languageInfoPanel"><h2>语言信息</h2><table id="language-info" class="aui"><tbody><tr><th rowspan="23">已安装的语言</th><td>爱沙尼亚文 (爱沙尼亚)</td></tr><tr><td>冰岛文 (冰岛)</td></tr><tr><td>波兰文 (波兰)</td></tr><tr><td>朝鲜文 (韩国)</td></tr><tr><td>丹麦文 (丹麦)</td></tr><tr><td>德文 (德国)</td></tr><tr><td>俄文 (俄罗斯)</td></tr><tr><td>法文 (法国)</td></tr><tr><td>芬兰文 (芬兰)</td></tr><tr><td>荷兰文 (荷兰)</td></tr><tr><td>捷克文 (捷克共和国)</td></tr><tr><td>罗马尼亚文 (罗马尼亚)</td></tr><tr><td>挪威文 (挪威)</td></tr><tr><td>葡萄牙文 (巴西)</td></tr><tr><td>日文 (日本)</td></tr><tr><td>瑞典文 (瑞典)</td></tr><tr><td>斯洛伐克文 (斯洛伐克)</td></tr><tr><td>西班牙文 (西班牙)</td></tr><tr><td>匈牙利文 (匈牙利)</td></tr><tr><td>意大利文 (意大利)</td></tr><tr><td>英文 (UK)</td></tr><tr><td>英文 (美国)</td></tr><tr><td>中文 (中国)</td></tr><tr><th>默认语言</th><td>中文 (中国) - 系统默认</td></tr></tbody></table></div><div class="info-section" id="requestInfoPanel"><h2>请求信息</h2><table class="aui"><tbody><tr><th>查询串</th><td>_=1672117818580</td></tr><tr><th>Context 路径</th><td>/jira</td></tr><tr><th>方案</th><td>http</td></tr><tr><th>Servlet 路径</th><td>/internal-error</td></tr><tr><th>路径信息</th><td>null</td></tr><tr><th>服务器</th><td>localhost</td></tr><tr><th>端口</th><td>2990</td></tr><tr><th>URI</th><td>/jira/internal-error</td></tr><tr><th>URL请求</th><td>http://localhost:2990/jira/internal-error</td></tr></tbody></table></div><div class="info-section" id="requestAttributesPanel"><h2>请求的属性。</h2><table class="aui"><tbody><tr><th>javax.servlet.forward.context_path</th><td>/jira</td></tr><tr><th>javax.servlet.forward.servlet_path</th><td>/rest/oa2jira/1.0/field/copy/query/Transition/test1</td></tr><tr><th>javax.servlet.error.status_code</th><td>500</td></tr><tr><th>com.atlassian.jira.web.filters.accesslog.AccessLogFilter_already_filtered</th><td>true</td></tr><tr><th>jira.session.max.inactive.interval</th><td>18000</td></tr><tr><th>atlassian.core.seraph.original.url</th><td>/rest/oa2jira/1.0/field/copy/query/Transition/test1?_=1672117818580</td></tr><tr><th>os_authTypeDefault</th><td>any</td></tr><tr><th>com.atlassian.labs.botkiller.BotKillerFilter</th><td>true</td></tr><tr><th>com.atlassian.gzipfilter.GzipFilter_already_filtered</th><td>true</td></tr><tr><th>jira.request.assession.id</th><td>10falj</td></tr><tr><th>com.newrelic.agent.TRANSACTION_NAME</th><td>/rest/oa2jira/*</td></tr><tr><th>com.atlassian.jira.security.xsrf.XsrfTokenAdditionRequestFilter_already_filtered</th><td>true</td></tr><tr><th>jira.request.id</th><td>790x324334x1</td></tr><tr><th>com.atlassian.seraph.auth.LoginReason</th><td>OK</td></tr><tr><th>com.atlassian.jira.web.filters.accesslog.AccessLogRequestInfoexit.called</th><td>true</td></tr><tr><th>sanitized.query</th><td>?_=1672117818580</td></tr><tr><th>javax.servlet.forward.mapping</th><td>org.apache.catalina.core.ApplicationMapping$MappingImpl@2fe37f73</td></tr><tr><th>jira.request.time.micros</th><td>4</td></tr><tr><th>javax.servlet.forward.request_uri</th><td>/jira/rest/oa2jira/1.0/field/copy/query/Transition/test1</td></tr><tr><th>jira.request.start.millis</th><td>1672117819383</td></tr><tr><th>jira.request.username</th><td>admin</td></tr><tr><th>javax.servlet.error.servlet_name</th><td>default</td></tr><tr><th>javax.servlet.error.message</th><td></td></tr><tr><th>is_rest_request</th><td>true</td></tr><tr><th>jira.webwork.cleanup</th><td>false</td></tr><tr><th>com.atlassian.jira.web.filters.johnson.JiraJohnson503Filter_already_filtered</th><td>true</td></tr><tr><th>javax.servlet.forward.query_string</th><td>_=1672117818580</td></tr><tr><th>com.atlassian.jira.web.filters.JiraFirstFilter_alreadyfiltered</th><td>true</td></tr><tr><th>jira.session.last.accessed.time</th><td>1672117819382</td></tr><tr><th>B3-TraceId</th><td>9295a74adfe4f3</td></tr><tr><th>loginfilter.already.filtered</th><td>true</td></tr><tr><th>javax.servlet.error.request_uri</th><td>/jira/rest/oa2jira/1.0/field/copy/query/Transition/test1</td></tr><tr><th>com.atlassian.prettyurls.filter.PrettyUrlsSiteMeshFixupFilter</th><td>true</td></tr><tr><th>com.atlassian.core.filters.HeaderSanitisingFilter_already_filtered</th><td>true</td></tr><tr><th>sanitized.referer</th><td>http://localhost:2990/jira/secure/admin/workflows/AddWorkflowTransitionFunctionParams!default.jspa?workflowName=test1&amp;workflowMode=draft&amp;descriptorTab=postfunctions&amp;workflowTransition=21&amp;pluginModuleKey=com.eve.eve-jira%3ARunNextTransitionFunction&amp;atl_token=BWP3-NZB2-6EDY-6C7K_46311e3f9ddfa4945adc867e5ff19bed29c8f543_lin</td></tr><tr><th>os_securityfilter_already_filtered</th><td>true</td></tr><tr><th>javax.servlet.error.exception</th><td>org.codehaus.jackson.map.JsonMappingException: No serializer found for class com.eve.beans.TransitionBean and no properties discovered to create BeanSerializer (to avoid exception, disable SerializationConfig.Feature.FAIL_ON_EMPTY_BEANS) ) (through reference chain: com.eve.beans.ResultBean[&quot;value&quot;]-&gt;java.util.ArrayList[0])</td></tr><tr><th>com.atlassian.jira.web.filters.JiraPostEncodingFilter_alreadyfiltered</th><td>true</td></tr><tr><th>com.atlassian.prettyurls.filter.PrettyUrlsSiteMeshFilter</th><td>true</td></tr></tbody></table></div><div class="info-section" id="loggingPanel"><h2>需要登录</h2><p><b>0</b> log statements generated by this request.</p></div><div class="info-section" id="listenersPanel"><h2>监听器</h2><table class="aui"><tbody><tr><th>Issue Assignment Listener</th><td>com.atlassian.jira.event.listeners.history.IssueAssignHistoryListener</td></tr><tr><th>Issue Index Listener</th><td>com.atlassian.jira.event.listeners.search.IssueIndexListener</td></tr><tr><th>Mail Listener</th><td>com.atlassian.jira.event.listeners.mail.MailListener</td></tr></tbody></table></div><div class="info-section" id="servicesPanel"><h2>服务</h2><table class="aui"><tbody><tr><th>Audit log cleaning service</th><td>com.atlassian.jira.service.services.auditing.AuditLogCleaningService</td><td>0 0 10 * * ?</td></tr><tr><th>Backup Service</th><td>com.atlassian.jira.service.services.export.ExportService</td><td>0 5 2/12 * * ?</td></tr><tr><td colspan="3"><small>&mdash; <b>USE_DEFAULT_DIRECTORY:</b> true</small></td></tr><tr><th>Mail Queue Service</th><td>com.atlassian.jira.service.services.mail.MailQueueService</td><td>0 * * * * ?</td></tr></tbody></table></div><div class="info-section" id="pluginsPanel"><h2>插件</h2><table class="aui"><tbody><tr><th>ActiveObjects Plugin - OSGi Bundle</th><td>3.2.4</td><td>Atlassian</td><td>已开启</td></tr><tr><th>JIRA Active Objects SPI implementation</th><td>1.2.2</td><td>Atlassian Software Systems</td><td>已开启</td></tr><tr><th>Atlassian - Administration - Quick Search - JIRA</th><td>2.3.3</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Analytics Client Plugin</th><td>6.1.7</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Analytics Whitelist Plugin</th><td>3.86</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Applinks - Plugin - Basic Authentication</th><td>8.0.0</td><td>Atlassian</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>build:</b> 10</small></td></tr><tr><th>Applinks - Plugin - CORS</th><td>8.0.0</td><td>Atlassian</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>build:</b> 10</small></td></tr><tr><th>Applinks - Plugin - OAuth</th><td>8.0.0</td><td>Atlassian</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>build:</b> 10</small></td></tr><tr><th>Applinks - Plugin - Core</th><td>8.0.0</td><td>Atlassian</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>build:</b> 10</small></td></tr><tr><th>Applinks - Plugin - Trusted Apps</th><td>8.0.0</td><td>Atlassian</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>build:</b> 10</small></td></tr><tr><th>atlassian-failure-cache-plugin</th><td>2.0.0</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Atlassian Audit Plugin</th><td>1.13.0</td><td>Atlassian</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>plugin-icon:</b> images/pluginIcon.png</small></td></tr><tr><td colspan="4"><small>&mdash; <b>plugin-logo:</b> images/pluginLogo.png</small></td></tr><tr><th>Atlassian UI Plugin</th><td>9.1.4</td><td>Atlassian Pty Ltd.</td><td>已开启</td></tr><tr><th>ICU4J</th><td>*******</td><td>Atlassian Pty Ltd</td><td>已开启</td></tr><tr><th>JSON Library</th><td>20070829.0.0.1</td><td>Atlassian Pty Ltd</td><td>已开启</td></tr><tr><th>Neko HTML</th><td>********</td><td>Atlassian Pty Ltd</td><td>已开启</td></tr><tr><th>Atlassian Data Pipeline Core Plugin</th><td>1.1.5</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Atlassian Data Pipeline Jira Plugin</th><td>1.1.5</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Atlassian Cluster Monitoring Plugin</th><td>4.1.9</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Atlassian Embedded Crowd - Administration Plugin</th><td>4.0.1</td><td>Atlassian</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>build:</b> 1</small></td></tr><tr><th>Atlassian Developer Toolbox</th><td>2.0.17</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Atlassian Diagnostics - Plugin</th><td>1.1.13</td><td>Atlassian</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>atlassian-data-center-compatible:</b> true</small></td></tr><tr><td colspan="4"><small>&mdash; <b>atlassian-data-center-status:</b> compatible</small></td></tr><tr><th>Atlassian Jira - Plugins - Feedback Plugin</th><td>8.15.0</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Atlassian Front-End Runtime Plugin</th><td>0.4.0</td><td>Atlassian Pty Ltd.</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>plugin-icon:</b> images/JavaScript-logo.png</small></td></tr><tr><td colspan="4"><small>&mdash; <b>plugin-logo:</b> images/JavaScript-logo.png</small></td></tr><tr><th>Gadget Dashboard Plugin</th><td>6.0.1</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Gadget Directory Plugin</th><td>6.0.1</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Embedded Gadgets Plugin</th><td>6.0.1</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Atlassian Gadgets OAuth Service Provider Plugin</th><td>6.0.1</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Opensocial Plugin</th><td>6.0.1</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Gadget Spec Publisher Plugin</th><td>6.0.1</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Atlassian HealthCheck Common Module</th><td>6.0.0</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Atlassian HTTP Client, Apache HTTP components impl</th><td>2.0.0</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Jira Core Project Templates Plugin</th><td>7.0.5</td><td>Atlassian</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>plugin-icon:</b> images/pluginIcon.png</small></td></tr><tr><td colspan="4"><small>&mdash; <b>plugin-logo:</b> images/pluginLogo.png</small></td></tr><tr><th>Jira Issue Collector Plugin</th><td>4.1.1</td><td>Atlassian</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>atlassian-data-center-compatible:</b> true</small></td></tr><tr><th>Atlassian Jira - Plugins - Diagnostics Plugin</th><td>8.15.0</td><td>Atlassian</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>atlassian-data-center-compatible:</b> true</small></td></tr><tr><td colspan="4"><small>&mdash; <b>atlassian-data-center-status:</b> compatible</small></td></tr><tr><th>JIRA iCalendar Plugin</th><td>1.8.2</td><td>Atlassian</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>atlassian-data-center-compatible:</b> true</small></td></tr><tr><td colspan="4"><small>&mdash; <b>atlassian-data-center-status:</b> compatible</small></td></tr><tr><th>Atlassian Jira - Plugins - Gadgets Plugin</th><td>8.15.0</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Atlassian Jira - Plugins - ActiveObjects SPI</th><td>8.15.0</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Atlassian Jira - Plugins - Admin Navigation Component</th><td>8.15.0</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Atlassian Jira - Plugins - Admin Upgrades</th><td>8.15.0</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Atlassian Jira - Plugins - Analytics whitelist</th><td>8.15.0</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Atlassian Jira - Plugins - APDEX</th><td>8.15.0</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Atlassian Jira - Plugins - Application Properties</th><td>8.15.0</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Jira Base URL Plugin</th><td>3.3.0</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Atlassian Jira - Plugins - Cluster Monitoring</th><td>8.15.0</td><td>Atlassian</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>plugin-icon:</b> images/pluginIcon.png</small></td></tr><tr><td colspan="4"><small>&mdash; <b>plugin-logo:</b> images/pluginLogo.png</small></td></tr><tr><th>Atlassian Jira - Plugins - Core Reports</th><td>8.15.0</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Atlassian Jira - Plugins - Credits Plugin</th><td>8.15.0</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Atlassian JIRA - Plugins - File viewer plugin</th><td>8.0.1</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Atlassian Jira - Plugins - Frontend Features</th><td>8.15.0</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Atlassian Jira - Plugins - Header Plugin</th><td>8.15.0</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Atlassian Jira - Plugins - Invite User</th><td>2.3.2</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Atlassian Jira - Plugins - Common AppLinks Based Issue Link Plugin</th><td>8.15.0</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Atlassian Jira - Plugins - Confluence Link</th><td>8.15.0</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Atlassian Jira - Plugins - Remote Jira Link</th><td>8.15.0</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Atlassian Jira - Plugins - Issue Web Link</th><td>8.15.0</td><td>Atlassian</td><td>已开启</td></tr><tr><th>jira-issue-nav-components</th><td>11.0.10</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Atlassian Jira - Plugins - Global Issue Navigator</th><td>11.0.10</td><td>Atlassian</td><td>已开启</td></tr><tr><th>English (United Kingdom) Language Pack</th><td>8.15.0</td><td>Atlassian</td><td>已开启</td></tr><tr><th>English (United States) Language Pack</th><td>8.15.0</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Atlassian Jira - Plugins - LESS integration</th><td>8.15.0</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Atlassian Jira - Plugins - Mail Plugin</th><td>11.0.16</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Jira Monitoring Plugin</th><td>05.7.3</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Atlassian Jira - Plugins - My Jira Home</th><td>8.15.0</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Atlassian Jira - Plugins - Onboarding</th><td>8.15.0</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Atlassian Jira - Plugins - Post setup announcements plugin</th><td>8.15.0</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Atlassian Jira - Plugins - Project Config Plugin</th><td>8.15.0</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Atlassian Jira - Plugins - Project Centric Issue Navigator</th><td>11.0.10</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Jira Projects Plugin</th><td>6.0.10</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Atlassian JIRA - Plugins - Quick Edit Plugin</th><td>4.1.8</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Atlassian Jira - Plugins - Share Content Component</th><td>8.15.0</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Jira Software Application</th><td>8.15.0</td><td>Atlassian</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>atlassian-data-center-compatible:</b> true</small></td></tr><tr><td colspan="4"><small>&mdash; <b>plugin-icon:</b> images/pluginIcon.png</small></td></tr><tr><td colspan="4"><small>&mdash; <b>plugin-logo:</b> images/pluginLogo.png</small></td></tr><tr><th>Atlassian Jira - Plugins - Closure Template Renderer</th><td>8.15.0</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Atlassian Jira - Plugins - Statistics plugin</th><td>8.15.0</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Jira Time Zone Detection plugin</th><td>3.0.4</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Atlassian Jira - Plugins - User Profile Plugin</th><td>4.0.6</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Atlassian Jira - Plugins - View Issue Panels</th><td>8.15.0</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Atlassian Jira - Plugins - Atlassian Notifications Plugin</th><td>8.15.0</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Atlassian Jira - Plugins - Look And Feel Logo Upload Plugin</th><td>8.15.0</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Jira Cloud Migration Assistant</th><td>1.4.4</td><td>Atlassian</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>atlassian-data-center-status:</b> compatible</small></td></tr><tr><td colspan="4"><small>&mdash; <b>plugin-icon:</b> plugin-images/pluginIcon.png</small></td></tr><tr><td colspan="4"><small>&mdash; <b>atlassian-data-center-compatible:</b> true</small></td></tr><tr><td colspan="4"><small>&mdash; <b>plugin-logo:</b> plugin-images/pluginLogo.jpg</small></td></tr><tr><th>Jira Mobile</th><td>3.2.17</td><td>Atlassian</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>plugin-icon:</b> jira/images/JIRA_Mobile_logo_16.png</small></td></tr><tr><td colspan="4"><small>&mdash; <b>plugin-logo:</b> jira/images/JIRA_Mobile_logo_48.png</small></td></tr><tr><th>Mobile Plugin for Jira</th><td>3.2.0</td><td>Atlassian</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>atlassian-data-center-compatible:</b> true</small></td></tr><tr><td colspan="4"><small>&mdash; <b>atlassian-data-center-status:</b> compatible</small></td></tr><tr><th>Atlassian Jira - Plugins - OAuth Consumer SPI</th><td>8.15.0</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Atlassian Jira - Plugins - OAuth Service Provider SPI</th><td>8.15.0</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Jira Bamboo Plugin</th><td>8.15.0</td><td>Atlassian Software Systems Pty Ltd</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>atlassian-data-center-compatible:</b> true</small></td></tr><tr><td colspan="4"><small>&mdash; <b>configure.url:</b> /secure/admin/jira/ViewBambooApplicationLinks.jspa</small></td></tr><tr><th>Comment Panel Plugin</th><td>1.0</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Custom Field Types &amp; Searchers</th><td>1.0</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Issue Operations Plugin</th><td>1.0</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Issue Tab Panels Plugin</th><td>1.0</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Renderer Plugin</th><td>1.0</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Project Role Actors Plugin</th><td>1.0</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Wiki Renderer Macros Plugin</th><td>1.0</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Soy Function Plugin</th><td>1.0</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Workflow Plugin</th><td>1.0</td><td>Atlassian</td><td>已开启</td></tr><tr><th>JIRA Workflow Transition Tabs</th><td>1.0</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Content Link Resolvers Plugin</th><td>1.0</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Renderer Component Factories Plugin</th><td>1.0</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Jira inform - batchers</th><td>1.5.4</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Jira inform - batching plugin</th><td>1.5.4</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Jira inform - event plugin</th><td>1.5.4</td><td>Atlassian</td><td>已开启</td></tr><tr><th>jira-inline-issue-create-plugin</th><td>2.0.24</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Atlassian JIRA - Admin Helper Plugin</th><td>5.1.2</td><td>Atlassian Pty Ltd</td><td>已开启</td></tr><tr><th>Atlassian Jira - Plugins - Auditing Plugin [Audit Log]</th><td>8.15.0</td><td>Atlassian</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>plugin-icon:</b> images/pluginIcon.png</small></td></tr><tr><td colspan="4"><small>&mdash; <b>plugin-logo:</b> images/pluginLogo.png</small></td></tr><tr><th>Jira DVCS Connector Plugin</th><td>5.5.6</td><td>Atlassian</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>atlassian-data-center-compatible:</b> true</small></td></tr><tr><th>JIRA browser metrics integration plugin</th><td>2.0.14</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Atlassian Jira - Plugins - Development Integration Plugin</th><td>5.10.1</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Jira Drag and Drop Attachment Plugin</th><td>5.0.14</td><td>Atlassian</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>atlassian-data-center-compatible:</b> true</small></td></tr><tr><th>Rich Text Editor for JIRA</th><td>4.3.13</td><td>Atlassian</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>atlassian-data-center-compatible:</b> true</small></td></tr><tr><th>Asana Importers Plugin for JIM</th><td>2.1.0</td><td>Atlassian</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>plugin-icon:</b> images/pluginIcon.png</small></td></tr><tr><td colspan="4"><small>&mdash; <b>plugin-logo:</b> images/pluginLogo.png</small></td></tr><tr><th>Bitbucket Importer Plugin for JIM</th><td>3.1.1</td><td>Atlassian</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>atlassian-data-center-compatible:</b> true</small></td></tr><tr><td colspan="4"><small>&mdash; <b>plugin-icon:</b> images/pluginIcon.png</small></td></tr><tr><td colspan="4"><small>&mdash; <b>plugin-logo:</b> images/pluginLogo.png</small></td></tr><tr><th>Jira GitHub Issue Importer</th><td>3.1.0</td><td>Atlassian</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>atlassian-data-center-compatible:</b> true</small></td></tr><tr><th>jira-importers-plugin</th><td>9.2.2</td><td>Atlassian</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>atlassian-data-center-compatible:</b> true</small></td></tr><tr><th>Redmine Importers Plugin for JIM</th><td>2.2.0</td><td>Atlassian</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>atlassian-data-center-compatible:</b> true</small></td></tr><tr><th>jira-importers-trello-plugin</th><td>3.1.0</td><td>Atlassian</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>atlassian-data-center-compatible:</b> true</small></td></tr><tr><th>Jira Index Analyzer</th><td>8.15.0</td><td>Atlassian</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>atlassian-data-center-compatible:</b> true</small></td></tr><tr><th>jira-optimizer-plugin</th><td>3.0.0</td><td>Atlassian</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>plugin-icon:</b> images/pluginIcon.png</small></td></tr><tr><td colspan="4"><small>&mdash; <b>plugin-logo:</b> images/pluginLogo.png</small></td></tr><tr><th>JIRA Password Policy Plugin</th><td>2.1.0</td><td>Atlassian</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>plugin-icon:</b> images/pluginIcon.png</small></td></tr><tr><td colspan="4"><small>&mdash; <b>plugin-logo:</b> images/pluginLogo.png</small></td></tr><tr><th>Atlassian Jira - Plugins - Post-Upgrade Landing Page</th><td>8.15.0</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Atlassian Jira - Plugins - Quick Search</th><td>8.15.0</td><td>Atlassian</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>atlassian-data-center-compatible:</b> true</small></td></tr><tr><th>Jira Software Monitor Plugin</th><td>8.15.0</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Jira for Software Plugin</th><td>8.15.0</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Atlassian Jira - Plugins - Transition Trigger Plugin</th><td>8.15.0</td><td>Atlassian</td><td>已开启</td></tr><tr><th>jira-ui</th><td>0.3.0</td><td>Atlassian</td><td>已开启</td></tr><tr><th>wiki-editor</th><td>4.3.13</td><td>Atlassian</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>atlassian-data-center-compatible:</b> true</small></td></tr><tr><th>Jira Workflow Designer Plugin</th><td>8.0.7</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Atlassian Jira - Plugins - WebHooks Plugin</th><td>8.15.0</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Jira Workflow Sharing Plugin</th><td>2.2.8</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Project Templates Plugin</th><td>7.0.5</td><td>Atlassian</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>plugin-icon:</b> images/pluginIcon.png</small></td></tr><tr><td colspan="4"><small>&mdash; <b>plugin-logo:</b> images/pluginLogo.png</small></td></tr><tr><th>Atlassian Jira - Plugins - REST Plugin</th><td>8.15.0</td><td>Atlassian</td><td>已开启</td></tr><tr><th>User anonymization in Jira Core</th><td>1.0</td><td>Atlassian</td><td>已开启</td></tr><tr><th>FishEye Plugin</th><td>8.15.0</td><td>Atlassian Software Systems</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>atlassian-data-center-compatible:</b> true</small></td></tr><tr><th>Wallboard Plugin</th><td>4.0.1</td><td>Atlassian</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>atlassian-data-center-compatible:</b> true</small></td></tr><tr><th>Advanced Roadmaps plans</th><td>8.15.0</td><td>Atlassian</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>atlassian-data-center-status:</b> compatible</small></td></tr><tr><td colspan="4"><small>&mdash; <b>plugin-icon:</b> images-plugin/pluginIcon.png</small></td></tr><tr><td colspan="4"><small>&mdash; <b>atlassian-data-center-compatible:</b> true</small></td></tr><tr><td colspan="4"><small>&mdash; <b>plugin-logo:</b> images-plugin/pluginLogo.png</small></td></tr><tr><th>Atlassian Bot Session Killer</th><td>1.7.10</td><td>Atlassian</td><td>已开启</td></tr><tr><th>httpservice-bridge</th><td>0.6.2</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Quick Reload - Stealing time back from Maven since 2013. Just press up arrow!</th><td>3.0.0</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Atlassian REST API Browser</th><td>3.1.3</td><td>Atlassian</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>atlassian-data-center-compatible:</b> true</small></td></tr><tr><th>Workbox - Common Plugin</th><td>8.0.5</td><td>Atlassian</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>atlassian-data-center-compatible:</b> true</small></td></tr><tr><th>Workbox - JIRA Provider Plugin</th><td>8.0.5</td><td>Atlassian</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>atlassian-data-center-compatible:</b> true</small></td></tr><tr><th>Atlassian OAuth Admin Plugin</th><td>4.0.2</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Atlassian OAuth Consumer SPI</th><td>4.0.2</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Atlassian OAuth Service Provider SPI</th><td>4.0.2</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Atlassian OAuth Consumer Plugin</th><td>4.0.2</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Atlassian OAuth Service Provider Plugin</th><td>4.0.2</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Atlassian OAuth 2 Client - Plugin</th><td>1.0.3</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Personal Access Tokens plugin</th><td>1.1.1</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Atlassian PDK Install Plugin</th><td>0.6</td><td>Atlassian Software Systems Pty Ltd</td><td>已开启</td></tr><tr><th>Atlassian Spring Scanner Annotations</th><td>2.1.13</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Atlassian Spring Scanner Runtime</th><td>2.1.13</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Atlassian Plugins - JavaScript libraries</th><td>2.1.0</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Atlassian Awareness Capability</th><td>0.0.6</td><td>Atlassian</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>plugin-icon:</b> images/pluginIcon.png</small></td></tr><tr><td colspan="4"><small>&mdash; <b>plugin-logo:</b> images/pluginLogo.png</small></td></tr><tr><th>Atlassian Plugins - Client-side Extensions - Runtime</th><td>1.1.3</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Atlassian Navigation Links Plugin</th><td>6.0.2</td><td>Atlassian</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>build:</b> 1</small></td></tr><tr><th>Atlassian Plugins - Web Resources - Implementation Plugin</th><td>4.2.6</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Atlassian Plugins - Web Resources REST</th><td>4.2.6</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Project Creation Capability Product REST Plugin</th><td>4.0.0-74bdd7a6fa</td><td>Atlassian</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>plugin-icon:</b> images/pluginIcon.png</small></td></tr><tr><td colspan="4"><small>&mdash; <b>plugin-logo:</b> images/pluginLogo.png</small></td></tr><tr><th>Atlassian Remote Event Common Plugin</th><td>6.2.2</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Atlassian Remote Event Consumer Plugin</th><td>6.2.2</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Atlassian Whitelist API Plugin</th><td>5.0.2</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Atlassian Whitelist Core Plugin</th><td>5.0.2</td><td>Atlassian</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>build:</b> 4</small></td></tr><tr><th>Atlassian Whitelist UI Plugin</th><td>5.0.2</td><td>Atlassian</td><td>已开启</td></tr><tr><th>SSO for Atlassian Data Center</th><td>4.1.1</td><td>Atlassian</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>atlassian-data-center-status:</b> compatible</small></td></tr><tr><td colspan="4"><small>&mdash; <b>plugin-icon:</b> img/sso-plugin-icon.png</small></td></tr><tr><td colspan="4"><small>&mdash; <b>atlassian-data-center-compatible:</b> true</small></td></tr><tr><td colspan="4"><small>&mdash; <b>build:</b> 5</small></td></tr><tr><td colspan="4"><small>&mdash; <b>plugin-logo:</b> img/sso-plugin-logo.png</small></td></tr><tr><th>Atlassian browser metrics plugin</th><td>7.0.1</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Jira Help Tips plugin</th><td>8.15.0</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Issue Status Plugin</th><td>2.1.1</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Attach Image for JIRA</th><td>4.0.0</td><td>Atlassian</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>atlassian-data-center-compatible:</b> true</small></td></tr><tr><th>Project Creation Plugin SPI for JIRA</th><td>4.0.0-74bdd7a6fa</td><td>Atlassian</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>plugin-icon:</b> images/pluginIcon.png</small></td></tr><tr><td colspan="4"><small>&mdash; <b>plugin-logo:</b> images/pluginLogo.png</small></td></tr><tr><th>JIRA Remote Link Aggregator Plugin</th><td>3.0.0</td><td>Atlassian</td><td>已开启</td></tr><tr><th>jquery</th><td>********</td><td>(unknown)</td><td>已开启</td></tr><tr><th>Atlassian LESS Transformer Plugin</th><td>4.0.0</td><td>Atlassian</td><td>已开启</td></tr><tr><th>plugin-data-editor</th><td>1.2</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Remote Link Aggregator Plugin</th><td>3.0.0</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Atlassian REST - Module Types</th><td>6.0.7</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Static Assets (CDN) Plugin</th><td>1.0.5</td><td>Atlassian</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>atlassian-data-center-compatible:</b> true</small></td></tr><tr><td colspan="4"><small>&mdash; <b>atlassian-data-center-status:</b> compatible</small></td></tr><tr><th>Pocketknife Feature Flags Plugin</th><td>0.5.4</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Atlassian Pretty URLs Plugin</th><td>3.0.0</td><td>Atlassian</td><td>已开启</td></tr><tr><th>querydsl-4.0.7-provider-plugin</th><td>1.2</td><td>Atlassian</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>atlassian-data-center-compatible:</b> true</small></td></tr><tr><td colspan="4"><small>&mdash; <b>atlassian-data-center-status:</b> compatible</small></td></tr><tr><th>Atlassian Rate Limiting - Plugin</th><td>1.0.6</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Atlassian Jira - Plugins - SAL Plugin</th><td>8.15.0</td><td>Atlassian Software Systems</td><td>已开启</td></tr><tr><th>jackson-module-scala-2.10-provider</th><td>0.5</td><td>Atlassian</td><td>已开启</td></tr><tr><th>scala-2.10-provider-plugin</th><td>0.14</td><td>Atlassian</td><td>已开启</td></tr><tr><th>scala-2.11-provider-plugin</th><td>0.13</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Atlassian Soy - Plugin</th><td>5.0.0</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Streams Plugin</th><td>8.3.0</td><td>Atlassian Software Systems Pty Ltd</td><td>已开启</td></tr><tr><th>Streams Inline Actions Plugin</th><td>8.3.0</td><td>Atlassian Software Systems Pty Ltd</td><td>已开启</td></tr><tr><th>Streams Core Plugin</th><td>8.3.0</td><td>Atlassian Software Systems Pty Ltd</td><td>已开启</td></tr><tr><th>JIRA Streams Inline Actions Plugin</th><td>8.3.0</td><td>Atlassian Software Systems Pty Ltd</td><td>已开启</td></tr><tr><th>Streams API</th><td>8.3.0</td><td>Atlassian Software Systems Pty Ltd</td><td>已开启</td></tr><tr><th>JIRA Activity Stream Plugin</th><td>8.3.0</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Streams SPI</th><td>8.3.0</td><td>Atlassian Software Systems Pty Ltd</td><td>已开启</td></tr><tr><th>Streams Third Party Provider Plugin</th><td>8.3.0</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Advanced Roadmaps Team Management</th><td>8.15.0</td><td>Atlassian</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>atlassian-data-center-status:</b> compatible</small></td></tr><tr><td colspan="4"><small>&mdash; <b>plugin-icon:</b> images-plugin/pluginIcon.png</small></td></tr><tr><td colspan="4"><small>&mdash; <b>atlassian-data-center-compatible:</b> true</small></td></tr><tr><td colspan="4"><small>&mdash; <b>plugin-logo:</b> images-plugin/pluginLogo.png</small></td></tr><tr><th>Atlassian Template Renderer API</th><td>4.0.2</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Atlassian Template Renderer Velocity 1.6 Plugin</th><td>4.0.2</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Atlassian Troubleshooting and Support Tools</th><td>1.30.1</td><td>Atlassian</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>atlassian-data-center-status:</b> compatible</small></td></tr><tr><td colspan="4"><small>&mdash; <b>plugin-icon:</b> images/troubleshooting-app-icon-16px.png</small></td></tr><tr><td colspan="4"><small>&mdash; <b>atlassian-data-center-compatible:</b> true</small></td></tr><tr><td colspan="4"><small>&mdash; <b>build:</b> 1</small></td></tr><tr><td colspan="4"><small>&mdash; <b>plugin-logo:</b> images/troubleshooting-app-icon-72px.png</small></td></tr><tr><th>Atlassian Universal Plugin Manager Plugin</th><td>4.3.0</td><td>Atlassian</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>atlassian-data-center-status:</b> compatible</small></td></tr><tr><td colspan="4"><small>&mdash; <b>plugin-icon:</b> images/upm-logo.png</small></td></tr><tr><td colspan="4"><small>&mdash; <b>atlassian-data-center-compatible:</b> true</small></td></tr><tr><td colspan="4"><small>&mdash; <b>plugin-logo:</b> images/upm-logo.png</small></td></tr><tr><th>Universal Plugin Manager - Role-Based Licensing Implementation Plugin</th><td>4.3.0</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Atlassian Application Manager plugin</th><td>4.3.0</td><td>Atlassian</td><td>已开启</td></tr><tr><th>************************</th><td>5.2.0</td><td>Atlassian</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>plugin-icon:</b> images/servlet-icon.png</small></td></tr><tr><td colspan="4"><small>&mdash; <b>plugin-logo:</b> images/servlet-icon.png</small></td></tr><tr><th>Atlassian WebHooks Plugin</th><td>3.3.0</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Atlassian Notifications</th><td>3.0.0</td><td>Atlassian</td><td>已开启</td></tr><tr><th>LastLog for Jira</th><td>1.2.4</td><td>Decadis AG</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>atlassian-data-center-compatible:</b> true</small></td></tr><tr><td colspan="4"><small>&mdash; <b>vendor-icon:</b> images/vendorIcon.png</small></td></tr><tr><td colspan="4"><small>&mdash; <b>atlassian-data-center-status:</b> compatible</small></td></tr><tr><td colspan="4"><small>&mdash; <b>plugin-type:</b> both</small></td></tr><tr><td colspan="4"><small>&mdash; <b>vendor-logo:</b> images/vendorLogo.png</small></td></tr><tr><td colspan="4"><small>&mdash; <b>configure.url:</b> /secure/admin/ViewLastLog!default.jspa</small></td></tr><tr><td colspan="4"><small>&mdash; <b>atlassian-licensing-enabled:</b> false</small></td></tr><tr><td colspan="4"><small>&mdash; <b>plugin-icon:</b> images/pluginIcon.png</small></td></tr><tr><td colspan="4"><small>&mdash; <b>plugin-logo:</b> images/pluginLogo.png</small></td></tr><tr><th>eve-jira</th><td>1.0.5</td><td>Example Company</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>plugin-icon:</b> images/pluginIcon.png</small></td></tr><tr><td colspan="4"><small>&mdash; <b>plugin-logo:</b> images/pluginLogo.png</small></td></tr><tr><th>k3_material_manage</th><td>1.0.0</td><td>Example Company</td><td>已开启</td></tr><tr><th>Jira Agile</th><td>8.15.0</td><td>Atlassian</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>plugin-icon:</b> includes/images/upm/plugin-icon.png</small></td></tr><tr><td colspan="4"><small>&mdash; <b>atlassian-licensing-enabled:</b> true</small></td></tr><tr><td colspan="4"><small>&mdash; <b>atlassian-data-center-compatible:</b> true</small></td></tr><tr><td colspan="4"><small>&mdash; <b>post.update.url:</b> /plugins/servlet/greenhopper/whats-new</small></td></tr><tr><td colspan="4"><small>&mdash; <b>plugin-logo:</b> includes/images/upm/plugin-logo.png</small></td></tr><tr><th>Advanced Roadmaps for Jira</th><td>8.15.0</td><td>Atlassian</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>atlassian-data-center-compatible:</b> true</small></td></tr><tr><td colspan="4"><small>&mdash; <b>server-licensing-enabled:</b> true</small></td></tr><tr><td colspan="4"><small>&mdash; <b>vendor-icon:</b> images-plugin/vendor-icon.png</small></td></tr><tr><td colspan="4"><small>&mdash; <b>atlassian-data-center-status:</b> compatible</small></td></tr><tr><td colspan="4"><small>&mdash; <b>vendor-logo:</b> images-plugin/vendor-logo.png</small></td></tr><tr><td colspan="4"><small>&mdash; <b>plugin-icon:</b> images-plugin/plugin-icon.png</small></td></tr><tr><td colspan="4"><small>&mdash; <b>post.install.url:</b> /secure/PortfolioGettingStarted.jspa</small></td></tr><tr><td colspan="4"><small>&mdash; <b>post.update.url:</b> /secure/PortfolioGettingStarted.jspa</small></td></tr><tr><td colspan="4"><small>&mdash; <b>plugin-logo:</b> images-plugin/plugin-logo.png</small></td></tr><tr><th>ROME: RSS/Atom syndication and publishing tools</th><td>0.9.0</td><td>SpringSource</td><td>已开启</td></tr><tr><th>JDOM DOM Processor</th><td>1.0.0</td><td>SpringSource</td><td>已开启</td></tr><tr><th>Crowd REST API - Application Management</th><td>1.0</td><td>Atlassian Software Systems</td><td>已开启</td></tr><tr><th>Crowd REST API</th><td>1.0</td><td>Atlassian Software Systems</td><td>已开启</td></tr><tr><th>Crowd System Password Encoders</th><td>1.0</td><td>Atlassian Software Systems</td><td>已开启</td></tr><tr><th>Functional Extensions</th><td>4.7.2</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Functional Extensions Guava Inter-Ops</th><td>4.7.2</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Functional Optics Library</th><td>4.7.2</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Functional Extensions Retry Inter-Ops</th><td>4.7.2</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Functional Extensions Scala Inter-Ops</th><td>4.7.2</td><td>Atlassian</td><td>已开启</td></tr><tr><th>jira-capabilities-plugin</th><td>1.0</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Web Resources Plugin Jira Core</th><td>1.0</td><td>Atlassian</td><td>已开启</td></tr><tr><th>JIRA Feature Keys</th><td>1.0</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Filter Deletion Warning Plugin</th><td>1.0</td><td>Atlassian</td><td>已开启</td></tr><tr><th>JIRA Footer</th><td>1.0</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Help Paths Plugin</th><td>1.0</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Icon Types Plugin</th><td>1.0</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Issue Views Plugin</th><td>1.0</td><td>Atlassian</td><td>已开启</td></tr><tr><th>JQL Functions</th><td>1.0</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Keyboard Shortcuts Plugin</th><td>1.0</td><td>Atlassian</td><td>已开启</td></tr><tr><th>JIRA Attachment Archive File Processors</th><td>1.0</td><td>Atlassian</td><td>已开启</td></tr><tr><th>JIRA Global Permissions</th><td>1.0</td><td>Atlassian</td><td>已开启</td></tr><tr><th>JIRA Project Permissions</th><td>1.0</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Top Navigation Bar</th><td>1.0</td><td>Atlassian</td><td>已开启</td></tr><tr><th>JIRA Usage Hints</th><td>1.0</td><td>Atlassian</td><td>已开启</td></tr><tr><th>User Format</th><td>1.0</td><td>Atlassian</td><td>已开启</td></tr><tr><th>User Profile Panels</th><td>1.0</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Admin Menu Sections</th><td>1.0</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Browse Project Operations Sections</th><td>1.0</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Preset Filters Sections</th><td>1.0</td><td>Atlassian</td><td>已开启</td></tr><tr><th>User Navigation Bar Sections</th><td>1.0</td><td>Atlassian</td><td>已开启</td></tr><tr><th>User Profile Links</th><td>1.0</td><td>Atlassian</td><td>已开启</td></tr><tr><th>View Project Operations Sections</th><td>1.0</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Web Panel Plugin</th><td>1.0</td><td>Atlassian</td><td>已开启</td></tr><tr><th>jira-webresources-plugin</th><td>1.0</td><td>Atlassian</td><td>已开启</td></tr><tr><th>Apache Commons FileUpload Bundle</th><td>1.2.1</td><td>The Apache Software Foundation</td><td>已开启</td></tr><tr><th>Apache Felix Web Management Console</th><td>1.2.8</td><td>The Apache Software Foundation</td><td>已开启</td></tr><tr><th>Apache Apache HttpClient OSGi bundle</th><td>4.5.10</td><td>The Apache Software Foundation</td><td>已开启</td></tr><tr><th>Apache Apache HttpCore OSGi bundle</th><td>4.4.12</td><td>The Apache Software Foundation</td><td>已开启</td></tr><tr><th>Apache ServiceMix :: Bundles :: spring-tx</th><td>5.1.18.RELEASE_1</td><td>The Apache Software Foundation</td><td>已开启</td></tr><tr><th>OSGi R4 Compendium Bundle</th><td>4.1.0</td><td>OSGi Alliance</td><td>已开启</td></tr><tr><th>org.osgi:org.osgi.service.cm</th><td>1.5.0.201505202024</td><td>OSGi Alliance http://www.osgi.org/</td><td>已开启</td></tr><tr><th>ROME, RSS and atOM utilitiEs for Java</th><td>1.0</td><td>Sun Microsystems</td><td>已开启</td></tr><tr><th>Entity property conditions</th><td>1.0</td><td>Atlassian</td><td>已开启</td></tr><tr><th>JIRA Core Czech (Czech Republic) Language Pack</th><td>8.14.0.v20201110031400</td><td>Atlassian Community</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>atlassian-data-center-compatible:</b> true</small></td></tr><tr><th>JIRA Core Danish (Denmark) Language Pack</th><td>8.14.0.v20201110031400</td><td>Atlassian Community</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>atlassian-data-center-compatible:</b> true</small></td></tr><tr><th>JIRA Core German (Germany) Language Pack</th><td>8.14.0.v20201110031400</td><td>Atlassian Community</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>atlassian-data-center-compatible:</b> true</small></td></tr><tr><th>JIRA Core Spanish (Spain) Language Pack</th><td>8.14.0.v20201110031400</td><td>Atlassian Community</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>atlassian-data-center-compatible:</b> true</small></td></tr><tr><th>JIRA Core Estonian (Estonia) Language Pack</th><td>8.14.0.v20201110031400</td><td>Atlassian Community</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>atlassian-data-center-compatible:</b> true</small></td></tr><tr><th>JIRA Core Finnish (Finland) Language Pack</th><td>8.14.0.v20201110031400</td><td>Atlassian Community</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>atlassian-data-center-compatible:</b> true</small></td></tr><tr><th>JIRA Core French (France) Language Pack</th><td>8.14.0.v20201110031400</td><td>Atlassian Community</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>atlassian-data-center-compatible:</b> true</small></td></tr><tr><th>JIRA Core Hungarian (Hungary) Language Pack</th><td>8.14.0.v20201110031400</td><td>Atlassian Community</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>atlassian-data-center-compatible:</b> true</small></td></tr><tr><th>JIRA Core Icelandic (Iceland) Language Pack</th><td>8.14.0.v20201110031400</td><td>Atlassian Community</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>atlassian-data-center-compatible:</b> true</small></td></tr><tr><th>JIRA Core Italian (Italy) Language Pack</th><td>8.14.0.v20201110031400</td><td>Atlassian Community</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>atlassian-data-center-compatible:</b> true</small></td></tr><tr><th>JIRA Core Japanese (Japan) Language Pack</th><td>8.14.0.v20201110031400</td><td>Atlassian Community</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>atlassian-data-center-compatible:</b> true</small></td></tr><tr><th>JIRA Core Korean (South Korea) Language Pack</th><td>8.14.0.v20201110031400</td><td>Atlassian Community</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>atlassian-data-center-compatible:</b> true</small></td></tr><tr><th>JIRA Core Dutch (Netherlands) Language Pack</th><td>8.14.0.v20201110031400</td><td>Atlassian Community</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>atlassian-data-center-compatible:</b> true</small></td></tr><tr><th>JIRA Core Norwegian (Norway) Language Pack</th><td>8.14.0.v20201110031400</td><td>Atlassian Community</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>atlassian-data-center-compatible:</b> true</small></td></tr><tr><th>JIRA Core Polish (Poland) Language Pack</th><td>8.14.0.v20201110031400</td><td>Atlassian Community</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>atlassian-data-center-compatible:</b> true</small></td></tr><tr><th>JIRA Core Portuguese (Brazil) Language Pack</th><td>8.14.0.v20201110031400</td><td>Atlassian Community</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>atlassian-data-center-compatible:</b> true</small></td></tr><tr><th>JIRA Core Romanian (Romania) Language Pack</th><td>8.14.0.v20201110031400</td><td>Atlassian Community</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>atlassian-data-center-compatible:</b> true</small></td></tr><tr><th>JIRA Core Russian (Russia) Language Pack</th><td>8.14.0.v20201110031400</td><td>Atlassian Community</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>atlassian-data-center-compatible:</b> true</small></td></tr><tr><th>JIRA Core Slovak (Slovakia) Language Pack</th><td>8.14.0.v20201110031400</td><td>Atlassian Community</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>atlassian-data-center-compatible:</b> true</small></td></tr><tr><th>JIRA Core Swedish (Sweden) Language Pack</th><td>8.14.0.v20201110031400</td><td>Atlassian Community</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>atlassian-data-center-compatible:</b> true</small></td></tr><tr><th>JIRA Core Chinese (China) Language Pack</th><td>8.14.0.v20201110031400</td><td>Atlassian Community</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>atlassian-data-center-compatible:</b> true</small></td></tr><tr><th>JIRA Software Czech (Czech Republic) Language Pack</th><td>8.14.0.v20201110031350</td><td>Atlassian Community</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>atlassian-data-center-compatible:</b> true</small></td></tr><tr><th>JIRA Software Danish (Denmark) Language Pack</th><td>8.15.0.v20201203032600</td><td>Atlassian Community</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>atlassian-data-center-compatible:</b> true</small></td></tr><tr><th>JIRA Software German (Germany) Language Pack</th><td>8.14.0.v20201110031350</td><td>Atlassian Community</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>atlassian-data-center-compatible:</b> true</small></td></tr><tr><th>JIRA Software English (United States) Language Pack</th><td>8.15.0.v20201203032600</td><td>Atlassian Community</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>atlassian-data-center-compatible:</b> true</small></td></tr><tr><th>JIRA Software Spanish (Spain) Language Pack</th><td>8.14.0.v20201110031350</td><td>Atlassian Community</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>atlassian-data-center-compatible:</b> true</small></td></tr><tr><th>JIRA Software Estonian (Estonia) Language Pack</th><td>8.15.0.v20201203032600</td><td>Atlassian Community</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>atlassian-data-center-compatible:</b> true</small></td></tr><tr><th>JIRA Software Finnish (Finland) Language Pack</th><td>8.14.0.v20201110031350</td><td>Atlassian Community</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>atlassian-data-center-compatible:</b> true</small></td></tr><tr><th>JIRA Software French (France) Language Pack</th><td>8.14.0.v20201110031350</td><td>Atlassian Community</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>atlassian-data-center-compatible:</b> true</small></td></tr><tr><th>JIRA Software Hungarian (Hungary) Language Pack</th><td>8.15.0.v20201203032600</td><td>Atlassian Community</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>atlassian-data-center-compatible:</b> true</small></td></tr><tr><th>JIRA Software Icelandic (Iceland) Language Pack</th><td>8.15.0.v20201203032600</td><td>Atlassian Community</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>atlassian-data-center-compatible:</b> true</small></td></tr><tr><th>JIRA Software Italian (Italy) Language Pack</th><td>8.14.0.v20201110031350</td><td>Atlassian Community</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>atlassian-data-center-compatible:</b> true</small></td></tr><tr><th>JIRA Software Japanese (Japan) Language Pack</th><td>8.14.0.v20201110031350</td><td>Atlassian Community</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>atlassian-data-center-compatible:</b> true</small></td></tr><tr><th>JIRA Software Korean (South Korea) Language Pack</th><td>8.14.0.v20201110031350</td><td>Atlassian Community</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>atlassian-data-center-compatible:</b> true</small></td></tr><tr><th>JIRA Software Dutch (Netherlands) Language Pack</th><td>8.14.0.v20201110031350</td><td>Atlassian Community</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>atlassian-data-center-compatible:</b> true</small></td></tr><tr><th>JIRA Software Norwegian (Norway) Language Pack</th><td>8.15.0.v20201203032600</td><td>Atlassian Community</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>atlassian-data-center-compatible:</b> true</small></td></tr><tr><th>JIRA Software Polish (Poland) Language Pack</th><td>8.14.0.v20201110031350</td><td>Atlassian Community</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>atlassian-data-center-compatible:</b> true</small></td></tr><tr><th>JIRA Software Portuguese (Brazil) Language Pack</th><td>8.14.0.v20201110031350</td><td>Atlassian Community</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>atlassian-data-center-compatible:</b> true</small></td></tr><tr><th>JIRA Software Romanian (Romania) Language Pack</th><td>8.15.0.v20201203032600</td><td>Atlassian Community</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>atlassian-data-center-compatible:</b> true</small></td></tr><tr><th>JIRA Software Russian (Russia) Language Pack</th><td>8.14.0.v20201110031350</td><td>Atlassian Community</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>atlassian-data-center-compatible:</b> true</small></td></tr><tr><th>JIRA Software Slovak (Slovakia) Language Pack</th><td>8.15.0.v20201203032600</td><td>Atlassian Community</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>atlassian-data-center-compatible:</b> true</small></td></tr><tr><th>JIRA Software Swedish (Sweden) Language Pack</th><td>8.14.0.v20201110031350</td><td>Atlassian Community</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>atlassian-data-center-compatible:</b> true</small></td></tr><tr><th>JIRA Software Chinese (China) Language Pack</th><td>8.14.0.v20201110031350</td><td>Atlassian Community</td><td>已开启</td></tr><tr><td colspan="4"><small>&mdash; <b>atlassian-data-center-compatible:</b> true</small></td></tr><tr><th>Whisper Messages Plugin</th><td>1.0</td><td>Atlassian</td><td>已开启</td></tr></tbody></table></div></div></div><p>返回到 <a href="javascript:window.history.back()">上一页</a></p></div></main></div></div></div><footer id="footer" role="contentinfo"><section class="footer-body"><ul class="atlassian-footer">
    <li>
        Atlassian Jira <a class="seo-link" rel="nofollow" href="https://www.atlassian.com/software/jira">Project Management Software</a>
                                            <span id="footer-build-information">(v8.15.0#815001-<span title='9cd993cd5d44d1112926026f768b18ff3cb391a3' data-commit-id='9cd993cd5d44d1112926026f768b18ff3cb391a3}'>sha1:9cd993c</span>)</span>
    </li>
    <li>
        <a id="about-link" rel="nofollow" href="/jira/secure/AboutPage.jspa/secure/AboutPage.jspa">About Jira</a>
    </li>
    <li>
        <a id="footer-report-problem-link" rel="nofollow" href="/jira/secure/CreateIssue!default.jspa">Report a problem</a>
    </li>
</ul>
    <p class="atlassian-footer">
        <span class="licensemessage">

        </span>
    </p>
<div id="footer-logo"><a href="http://www.atlassian.com/" rel="nofollow">Atlassian</a></div></section></footer></div></body></html>
