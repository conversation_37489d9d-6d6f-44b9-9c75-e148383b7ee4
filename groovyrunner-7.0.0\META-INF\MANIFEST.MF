Manifest-Version: 1.0
Bundle-Description: <PERSON><PERSON><PERSON> for JIRA
Bundle-SymbolicName: com.onresolve.jira.groovy.groovyrunner
Implementation-Version: 7.0.0
Bundle-ManifestVersion: 2
Bnd-LastModified: 1663254887626
Bundle-Vendor: Adaptavist.com Ltd
Bundle-DocURL: https://docs.adaptavist.com/
Import-Package: org.springframework.context,javax.naming.directory,com
 .atlassian.jira.service.util.handler,org.apache.lucene.analysis,org.o
 sgi.framework;version="1.7.0",com.atlassian.core.ofbiz;version="8.1.0
 ",com.atlassian.core.ofbiz.util;version="8.1.0",com.atlassian.core.ut
 il;version="8.1.0",com.atlassian.seraph.service;version="8.1.0",com.o
 pensymphony.module.propertyset;version="8.1.0",com.opensymphony.util;
 version="8.1.0",com.opensymphony.workflow;version="8.1.0",com.opensym
 phony.workflow.config;version="8.1.0",com.opensymphony.workflow.loade
 r;version="8.1.0",com.opensymphony.workflow.spi;version="8.1.0",org.a
 pache.commons.lang3;version="3.8",com.atlassian.greenhopper.manager.r
 apidview;resolution:=optional;version="8.0",com.atlassian.greenhopper
 .model.query;resolution:=optional;version="8.0",com.atlassian.greenho
 pper.model.rapid;resolution:=optional;version="8.0",com.atlassian.gre
 enhopper.model.validation;resolution:=optional;version="8.0",com.atla
 ssian.greenhopper.service;resolution:=optional;version="8.0",com.atla
 ssian.greenhopper.service.issue;resolution:=optional;version="8.0",co
 m.atlassian.greenhopper.service.rapid;resolution:=optional;version="8
 .0",com.atlassian.greenhopper.service.rapid.view;resolution:=optional
 ;version="8.0",com.atlassian.greenhopper.service.sprint;resolution:=o
 ptional;version="8.0",com.atlassian.greenhopper.web.rapid.chart;resol
 ution:=optional;version="8.0",com.atlassian.greenhopper.web.rapid.vie
 w;resolution:=optional;version="8.0",com.ibm.icu.lang;resolution:=opt
 ional,com.ibm.icu.text;resolution:=optional,com.ibm.icu.util;resoluti
 on:=optional,com.thoughtworks.xstream;resolution:=optional;version="[
 1.4,2)",com.thoughtworks.xstream.io;resolution:=optional;version="[1.
 4,2)",com.thoughtworks.xstream.io.xml;resolution:=optional;version="[
 1.4,2)",groovy.cli;resolution:=optional;version="[3.0,4)",groovy.io;r
 esolution:=optional;version="[3.0,4)",groovy.lang;resolution:=optiona
 l;version="[3.0,4)",groovy.lang.groovydoc;resolution:=optional;versio
 n="[3.0,4)",groovy.namespace;resolution:=optional;version="[3.0,4)",g
 roovy.security;resolution:=optional;version="[3.0,4)",groovy.transfor
 m;resolution:=optional;version="[3.0,4)",groovy.transform.builder;res
 olution:=optional;version="[3.0,4)",groovy.transform.options;resoluti
 on:=optional;version="[3.0,4)",groovy.transform.stc;resolution:=optio
 nal;version="[3.0,4)",groovyjarjarantlr;resolution:=optional;version=
 "[3.0,4)",groovyjarjarantlr.ASdebug;resolution:=optional;version="[3.
 0,4)",groovyjarjarantlr.actions.cpp;resolution:=optional;version="[3.
 0,4)",groovyjarjarantlr.actions.csharp;resolution:=optional;version="
 [3.0,4)",groovyjarjarantlr.actions.java;resolution:=optional;version=
 "[3.0,4)",groovyjarjarantlr.actions.python;resolution:=optional;versi
 on="[3.0,4)",groovyjarjarantlr.collections;resolution:=optional;versi
 on="[3.0,4)",groovyjarjarantlr.collections.impl;resolution:=optional;
 version="[3.0,4)",groovyjarjarantlr.debug;resolution:=optional;versio
 n="[3.0,4)",groovyjarjarantlr.debug.misc;resolution:=optional;version
 ="[3.0,4)",groovyjarjarantlr.preprocessor;resolution:=optional;versio
 n="[3.0,4)",groovyjarjarantlr4.runtime;resolution:=optional;version="
 [3.0,4)",groovyjarjarantlr4.runtime.misc;resolution:=optional;version
 ="[3.0,4)",groovyjarjarantlr4.runtime.tree;resolution:=optional;versi
 on="[3.0,4)",groovyjarjarantlr4.stringtemplate;resolution:=optional,g
 roovyjarjarantlr4.v4;resolution:=optional;version="[3.0,4)",groovyjar
 jarantlr4.v4.analysis;resolution:=optional;version="[3.0,4)",groovyja
 rjarantlr4.v4.automata;resolution:=optional;version="[3.0,4)",groovyj
 arjarantlr4.v4.codegen;resolution:=optional;version="[3.0,4)",groovyj
 arjarantlr4.v4.codegen.model;resolution:=optional;version="[3.0,4)",g
 roovyjarjarantlr4.v4.codegen.model.chunk;resolution:=optional;version
 ="[3.0,4)",groovyjarjarantlr4.v4.codegen.model.decl;resolution:=optio
 nal;version="[3.0,4)",groovyjarjarantlr4.v4.gui;resolution:=optional;
 version="[3.0,4)",groovyjarjarantlr4.v4.misc;resolution:=optional;ver
 sion="[3.0,4)",groovyjarjarantlr4.v4.parse;resolution:=optional;versi
 on="[3.0,4)",groovyjarjarantlr4.v4.runtime;resolution:=optional;versi
 on="[3.0,4)",groovyjarjarantlr4.v4.runtime.atn;resolution:=optional;v
 ersion="[3.0,4)",groovyjarjarantlr4.v4.runtime.dfa;resolution:=option
 al;version="[3.0,4)",groovyjarjarantlr4.v4.runtime.misc;resolution:=o
 ptional;version="[3.0,4)",groovyjarjarantlr4.v4.runtime.tree;resoluti
 on:=optional;version="[3.0,4)",groovyjarjarantlr4.v4.runtime.tree.pat
 tern;resolution:=optional;version="[3.0,4)",groovyjarjarantlr4.v4.run
 time.tree.xpath;resolution:=optional;version="[3.0,4)",groovyjarjaran
 tlr4.v4.semantics;resolution:=optional;version="[3.0,4)",groovyjarjar
 antlr4.v4.tool;resolution:=optional;version="[3.0,4)",groovyjarjarant
 lr4.v4.tool.ast;resolution:=optional;version="[3.0,4)",groovyjarjaran
 tlr4.v4.unicode;resolution:=optional;version="[3.0,4)",groovyjarjaras
 m.asm;resolution:=optional;version="[3.0,4)",groovyjarjarasm.asm.sign
 ature;resolution:=optional;version="[3.0,4)",groovyjarjarasm.asm.tree
 ;resolution:=optional;version="[3.0,4)",groovyjarjarasm.asm.util;reso
 lution:=optional;version="[3.0,4)",groovyjarjarpicocli;resolution:=op
 tional;version="[3.0,4)",javax.annotation.processing;resolution:=opti
 onal,javax.imageio;resolution:=optional,javax.lang.model;resolution:=
 optional,javax.lang.model.element;resolution:=optional,javax.lang.mod
 el.type;resolution:=optional,javax.lang.model.util;resolution:=option
 al,javax.print;resolution:=optional,javax.print.attribute;resolution:
 =optional,javax.swing;resolution:=optional,javax.swing.border;resolut
 ion:=optional,javax.swing.event;resolution:=optional,javax.swing.file
 chooser;resolution:=optional,javax.swing.text;resolution:=optional,ja
 vax.swing.tree;resolution:=optional,javax.tools;resolution:=optional,
 javax.xml.parsers;resolution:=optional,org.abego.treelayout;resolutio
 n:=optional,org.abego.treelayout.util;resolution:=optional,org.apache
 .groovy.ast.tools;resolution:=optional;version="[3.0,4)",org.apache.g
 roovy.internal.metaclass;resolution:=optional;version="[3.0,4)",org.a
 pache.groovy.internal.util;resolution:=optional;version="[3.0,4)",org
 .apache.groovy.io;resolution:=optional;version="[3.0,4)",org.apache.g
 roovy.lang.annotation;resolution:=optional;version="[3.0,4)",org.apac
 he.groovy.parser.antlr4;resolution:=optional;version="[3.0,4)",org.ap
 ache.groovy.parser.antlr4.internal;resolution:=optional;version="[3.0
 ,4)",org.apache.groovy.parser.antlr4.internal.atnmanager;resolution:=
 optional;version="[3.0,4)",org.apache.groovy.parser.antlr4.util;resol
 ution:=optional;version="[3.0,4)",org.apache.groovy.plugin;resolution
 :=optional;version="[3.0,4)",org.apache.groovy.util;resolution:=optio
 nal;version="[3.0,4)",org.apache.groovy.util.concurrent;resolution:=o
 ptional;version="[3.0,4)",org.apache.groovy.util.concurrent.concurren
 tlinkedhashmap;resolution:=optional;version="[3.0,4)",org.codehaus.gr
 oovy;resolution:=optional;version="[3.0,4)",org.codehaus.groovy.antlr
 ;resolution:=optional;version="[3.0,4)",org.codehaus.groovy.antlr.jav
 a;resolution:=optional;version="[3.0,4)",org.codehaus.groovy.antlr.pa
 rser;resolution:=optional;version="[3.0,4)",org.codehaus.groovy.antlr
 .treewalker;resolution:=optional;version="[3.0,4)",org.codehaus.groov
 y.ast;resolution:=optional;version="[3.0,4)",org.codehaus.groovy.ast.
 builder;resolution:=optional;version="[3.0,4)",org.codehaus.groovy.as
 t.decompiled;resolution:=optional;version="[3.0,4)",org.codehaus.groo
 vy.ast.expr;resolution:=optional;version="[3.0,4)",org.codehaus.groov
 y.ast.stmt;resolution:=optional;version="[3.0,4)",org.codehaus.groovy
 .ast.tools;resolution:=optional;version="[3.0,4)",org.codehaus.groovy
 .classgen;resolution:=optional;version="[3.0,4)",org.codehaus.groovy.
 classgen.asm;resolution:=optional;version="[3.0,4)",org.codehaus.groo
 vy.classgen.asm.indy;resolution:=optional;version="[3.0,4)",org.codeh
 aus.groovy.classgen.asm.indy.sc;resolution:=optional;version="[3.0,4)
 ",org.codehaus.groovy.classgen.asm.sc;resolution:=optional;version="[
 3.0,4)",org.codehaus.groovy.classgen.asm.util;resolution:=optional;ve
 rsion="[3.0,4)",org.codehaus.groovy.control;resolution:=optional;vers
 ion="[3.0,4)",org.codehaus.groovy.control.customizers;resolution:=opt
 ional;version="[3.0,4)",org.codehaus.groovy.control.io;resolution:=op
 tional;version="[3.0,4)",org.codehaus.groovy.control.messages;resolut
 ion:=optional;version="[3.0,4)",org.codehaus.groovy.reflection;resolu
 tion:=optional;version="[3.0,4)",org.codehaus.groovy.reflection.andro
 id;resolution:=optional;version="[3.0,4)",org.codehaus.groovy.reflect
 ion.stdclasses;resolution:=optional;version="[3.0,4)",org.codehaus.gr
 oovy.reflection.v7;resolution:=optional;version="[3.0,4)",org.codehau
 s.groovy.runtime;resolution:=optional;version="[3.0,4)",org.codehaus.
 groovy.runtime.callsite;resolution:=optional;version="[3.0,4)",org.co
 dehaus.groovy.runtime.dgmimpl;resolution:=optional;version="[3.0,4)",
 org.codehaus.groovy.runtime.dgmimpl.arrays;resolution:=optional;versi
 on="[3.0,4)",org.codehaus.groovy.runtime.m12n;resolution:=optional;ve
 rsion="[3.0,4)",org.codehaus.groovy.runtime.memoize;resolution:=optio
 nal;version="[3.0,4)",org.codehaus.groovy.runtime.metaclass;resolutio
 n:=optional;version="[3.0,4)",org.codehaus.groovy.runtime.powerassert
 ;resolution:=optional;version="[3.0,4)",org.codehaus.groovy.runtime.t
 ypehandling;resolution:=optional;version="[3.0,4)",org.codehaus.groov
 y.runtime.wrappers;resolution:=optional;version="[3.0,4)",org.codehau
 s.groovy.syntax;resolution:=optional;version="[3.0,4)",org.codehaus.g
 roovy.tools.gse;resolution:=optional;version="[3.0,4)",org.codehaus.g
 roovy.tools.javac;resolution:=optional;version="[3.0,4)",org.codehaus
 .groovy.transform;resolution:=optional;version="[3.0,4)",org.codehaus
 .groovy.transform.sc;resolution:=optional;version="[3.0,4)",org.codeh
 aus.groovy.transform.sc.transformers;resolution:=optional;version="[3
 .0,4)",org.codehaus.groovy.transform.stc;resolution:=optional;version
 ="[3.0,4)",org.codehaus.groovy.transform.trait;resolution:=optional;v
 ersion="[3.0,4)",org.codehaus.groovy.util;resolution:=optional;versio
 n="[3.0,4)",org.codehaus.groovy.vmplugin;resolution:=optional;version
 ="[3.0,4)",org.codehaus.groovy.vmplugin.v5;resolution:=optional;versi
 on="[3.0,4)",org.codehaus.groovy.vmplugin.v6;resolution:=optional;ver
 sion="[3.0,4)",org.codehaus.groovy.vmplugin.v8;resolution:=optional;v
 ersion="[3.0,4)",org.stringtemplate.v4;resolution:=optional,org.strin
 gtemplate.v4.compiler;resolution:=optional,org.stringtemplate.v4.gui;
 resolution:=optional,org.stringtemplate.v4.misc;resolution:=optional,
 org.w3c.dom;resolution:=optional,com.atlassian.activeobjects.external
 ;version="3.0",com.atlassian.activeobjects.spi,com.atlassian.activeob
 jects.tx;version="3.0",com.atlassian.annotations,com.atlassian.annota
 tions.nonnull,com.atlassian.annotations.tenancy,com.atlassian.applica
 tion.api,com.atlassian.applinks.api,com.atlassian.applinks.api.applic
 ation.bamboo,com.atlassian.applinks.api.application.bitbucket,com.atl
 assian.applinks.api.application.confluence,com.atlassian.applinks.api
 .application.jira,com.atlassian.applinks.spi.link,com.atlassian.appli
 nks.spi.util,com.atlassian.cache,com.atlassian.collectors,com.atlassi
 an.configurable,com.atlassian.crowd.embedded.api,com.atlassian.crowd.
 event,com.atlassian.crowd.event.group,com.atlassian.crowd.event.user,
 com.atlassian.crowd.model.membership,com.atlassian.crowd.model.user,c
 om.atlassian.event.api,com.atlassian.event.internal,com.atlassian.eve
 nt.spi,com.atlassian.extras.api,com.atlassian.fugue;version="2.7",com
 .atlassian.instrumentation.operations,com.atlassian.jira,com.atlassia
 n.jira.admin,com.atlassian.jira.application,com.atlassian.jira.auditi
 ng,com.atlassian.jira.avatar,com.atlassian.jira.avatar.types.issuetyp
 e,com.atlassian.jira.bc,com.atlassian.jira.bc.config,com.atlassian.ji
 ra.bc.favourites,com.atlassian.jira.bc.filter,com.atlassian.jira.bc.g
 roup,com.atlassian.jira.bc.group.search,com.atlassian.jira.bc.issue,c
 om.atlassian.jira.bc.issue.comment,com.atlassian.jira.bc.issue.commen
 t.property,com.atlassian.jira.bc.issue.events,com.atlassian.jira.bc.i
 ssue.properties,com.atlassian.jira.bc.issue.search,com.atlassian.jira
 .bc.issue.visibility,com.atlassian.jira.bc.issue.worklog,com.atlassia
 n.jira.bc.license,com.atlassian.jira.bc.portal,com.atlassian.jira.bc.
 project,com.atlassian.jira.bc.project.component,com.atlassian.jira.bc
 .project.version,com.atlassian.jira.bc.projectroles,com.atlassian.jir
 a.bc.user,com.atlassian.jira.bc.user.search,com.atlassian.jira.bc.wor
 kflow,com.atlassian.jira.cluster,com.atlassian.jira.component,com.atl
 assian.jira.component.pico,com.atlassian.jira.config,com.atlassian.ji
 ra.config.feature,com.atlassian.jira.config.managedconfiguration,com.
 atlassian.jira.config.properties,com.atlassian.jira.config.util,com.a
 tlassian.jira.datetime,com.atlassian.jira.entity,com.atlassian.jira.e
 ntity.property,com.atlassian.jira.event,com.atlassian.jira.event.bc.p
 roject.component,com.atlassian.jira.event.comment,com.atlassian.jira.
 event.config,com.atlassian.jira.event.entity,com.atlassian.jira.event
 .issue,com.atlassian.jira.event.issue.field,com.atlassian.jira.event.
 issue.link,com.atlassian.jira.event.permission,com.atlassian.jira.eve
 nt.project,com.atlassian.jira.event.role,com.atlassian.jira.event.sch
 eme,com.atlassian.jira.event.type,com.atlassian.jira.event.workflow,c
 om.atlassian.jira.event.worklog,com.atlassian.jira.exception,com.atla
 ssian.jira.extension,com.atlassian.jira.favourites,com.atlassian.jira
 .imports.project.customfield,com.atlassian.jira.imports.project.mappe
 r,com.atlassian.jira.index,com.atlassian.jira.instrumentation,com.atl
 assian.jira.issue,com.atlassian.jira.issue.attachment,com.atlassian.j
 ira.issue.changehistory,com.atlassian.jira.issue.comments,com.atlassi
 an.jira.issue.comparator,com.atlassian.jira.issue.context,com.atlassi
 an.jira.issue.context.manager,com.atlassian.jira.issue.customfields,c
 om.atlassian.jira.issue.customfields.config.item,com.atlassian.jira.i
 ssue.customfields.converters,com.atlassian.jira.issue.customfields.im
 pl,com.atlassian.jira.issue.customfields.impl.rest,com.atlassian.jira
 .issue.customfields.manager,com.atlassian.jira.issue.customfields.opt
 ion,com.atlassian.jira.issue.customfields.persistence,com.atlassian.j
 ira.issue.customfields.searchers,com.atlassian.jira.issue.customfield
 s.searchers.information,com.atlassian.jira.issue.customfields.searche
 rs.renderer,com.atlassian.jira.issue.customfields.searchers.transform
 er,com.atlassian.jira.issue.customfields.statistics,com.atlassian.jir
 a.issue.customfields.view,com.atlassian.jira.issue.export,com.atlassi
 an.jira.issue.export.customfield,com.atlassian.jira.issue.fields,com.
 atlassian.jira.issue.fields.config,com.atlassian.jira.issue.fields.co
 nfig.manager,com.atlassian.jira.issue.fields.event,com.atlassian.jira
 .issue.fields.layout.column,com.atlassian.jira.issue.fields.layout.fi
 eld,com.atlassian.jira.issue.fields.renderer,com.atlassian.jira.issue
 .fields.renderer.wiki,com.atlassian.jira.issue.fields.rest,com.atlass
 ian.jira.issue.fields.rest.json,com.atlassian.jira.issue.fields.rest.
 json.beans,com.atlassian.jira.issue.fields.screen,com.atlassian.jira.
 issue.fields.screen.issuetype,com.atlassian.jira.issue.fields.util,co
 m.atlassian.jira.issue.history,com.atlassian.jira.issue.index,com.atl
 assian.jira.issue.index.indexers,com.atlassian.jira.issue.index.index
 ers.impl,com.atlassian.jira.issue.issuetype,com.atlassian.jira.issue.
 label,com.atlassian.jira.issue.link,com.atlassian.jira.issue.managers
 ,com.atlassian.jira.issue.operation,com.atlassian.jira.issue.priority
 ,com.atlassian.jira.issue.resolution,com.atlassian.jira.issue.search,
 com.atlassian.jira.issue.search.constants,com.atlassian.jira.issue.se
 arch.managers,com.atlassian.jira.issue.search.parameters.lucene,com.a
 tlassian.jira.issue.search.providers,com.atlassian.jira.issue.search.
 searchers.information,com.atlassian.jira.issue.search.searchers.rende
 rer,com.atlassian.jira.issue.search.searchers.transformer,com.atlassi
 an.jira.issue.search.searchers.util,com.atlassian.jira.issue.search.u
 til,com.atlassian.jira.issue.security,com.atlassian.jira.issue.statis
 tics,com.atlassian.jira.issue.statistics.util,com.atlassian.jira.issu
 e.status,com.atlassian.jira.issue.transport,com.atlassian.jira.issue.
 util,com.atlassian.jira.issue.watchers,com.atlassian.jira.issue.workl
 og,com.atlassian.jira.jql,com.atlassian.jira.jql.builder,com.atlassia
 n.jira.jql.context,com.atlassian.jira.jql.operand,com.atlassian.jira.
 jql.operand.registry,com.atlassian.jira.jql.operator,com.atlassian.ji
 ra.jql.parser,com.atlassian.jira.jql.query,com.atlassian.jira.jql.res
 olver,com.atlassian.jira.jql.util,com.atlassian.jira.jql.validator,co
 m.atlassian.jira.jql.values,com.atlassian.jira.license,com.atlassian.
 jira.mail,com.atlassian.jira.mail.util,com.atlassian.jira.model.query
 dsl,com.atlassian.jira.notification,com.atlassian.jira.notification.t
 ype,com.atlassian.jira.ofbiz,com.atlassian.jira.permission,com.atlass
 ian.jira.plugin,com.atlassian.jira.plugin.customfield,com.atlassian.j
 ira.plugin.jql.function,com.atlassian.jira.plugin.webfragment,com.atl
 assian.jira.plugin.webfragment.model,com.atlassian.jira.plugin.workfl
 ow,com.atlassian.jira.plugins.mail.handlers;version="11.0",com.atlass
 ian.jira.plugins.mail.webwork;version="11.0",com.atlassian.jira.porta
 l,com.atlassian.jira.project,com.atlassian.jira.project.archiving,com
 .atlassian.jira.project.type,com.atlassian.jira.project.version,com.a
 tlassian.jira.rest,com.atlassian.jira.rest.v2.issue;version="8.1",com
 .atlassian.jira.rest.v2.issue.project;version="8.1",com.atlassian.jir
 a.rest.v2.issue.version;version="8.1",com.atlassian.jira.scheme,com.a
 tlassian.jira.security,com.atlassian.jira.security.groups,com.atlassi
 an.jira.security.plugin,com.atlassian.jira.security.roles,com.atlassi
 an.jira.security.websudo,com.atlassian.jira.security.xsrf,com.atlassi
 an.jira.service,com.atlassian.jira.service.services.file,com.atlassia
 n.jira.service.services.mail,com.atlassian.jira.service.util,com.atla
 ssian.jira.sharing,com.atlassian.jira.sharing.search,com.atlassian.ji
 ra.template,com.atlassian.jira.timezone,com.atlassian.jira.user,com.a
 tlassian.jira.user.preferences,com.atlassian.jira.user.util,com.atlas
 sian.jira.util,com.atlassian.jira.util.collect,com.atlassian.jira.uti
 l.dbc,com.atlassian.jira.util.json,com.atlassian.jira.util.lucene,com
 .atlassian.jira.util.thread,com.atlassian.jira.util.velocity,com.atla
 ssian.jira.web,com.atlassian.jira.web.action.admin.translation,com.at
 lassian.jira.web.action.issue,com.atlassian.jira.web.action.util,com.
 atlassian.jira.web.action.util.workflow,com.atlassian.jira.web.bean,c
 om.atlassian.jira.web.component,com.atlassian.jira.web.util,com.atlas
 sian.jira.workflow,com.atlassian.jira.workflow.condition,com.atlassia
 n.jira.workflow.function.event,com.atlassian.jira.workflow.function.i
 ssue,com.atlassian.json.marshal,com.atlassian.mail,com.atlassian.mail
 .queue,com.atlassian.mail.server,com.atlassian.plugin,com.atlassian.p
 lugin.descriptors,com.atlassian.plugin.event,com.atlassian.plugin.eve
 nt.events,com.atlassian.plugin.hostcontainer,com.atlassian.plugin.mod
 ule,com.atlassian.plugin.osgi.container,com.atlassian.plugin.osgi.ext
 ernal,com.atlassian.plugin.osgi.module,com.atlassian.plugin.osgi.spri
 ng,com.atlassian.plugin.predicate,com.atlassian.plugin.schema.descrip
 tor,com.atlassian.plugin.servlet,com.atlassian.plugin.spring.scanner.
 annotation;version="2.1",com.atlassian.plugin.spring.scanner.annotati
 on.component;version="2.1",com.atlassian.plugin.spring.scanner.annota
 tion.export;version="2.1",com.atlassian.plugin.spring.scanner.annotat
 ion.imports;version="2.1",com.atlassian.plugin.util,com.atlassian.plu
 gin.util.resource,com.atlassian.plugin.web,com.atlassian.plugin.web.a
 pi,com.atlassian.plugin.web.api.provider,com.atlassian.plugin.web.con
 ditions,com.atlassian.plugin.web.descriptors,com.atlassian.plugin.web
 .model,com.atlassian.plugin.webresource,com.atlassian.plugin.webresou
 rce.transformer,com.atlassian.plugin.webresource.url,com.atlassian.pl
 ugins.rest.common.error.jersey,com.atlassian.plugins.rest.common.json
 ,com.atlassian.plugins.rest.common.security,com.atlassian.plugins.res
 t.common.security.jersey,com.atlassian.query,com.atlassian.query.clau
 se,com.atlassian.query.operand,com.atlassian.query.operator,com.atlas
 sian.query.order,com.atlassian.sal.api;version="4.0",com.atlassian.sa
 l.api.auth,com.atlassian.sal.api.component,com.atlassian.sal.api.exec
 utor,com.atlassian.sal.api.features,com.atlassian.sal.api.license,com
 .atlassian.sal.api.lifecycle,com.atlassian.sal.api.message,com.atlass
 ian.sal.api.net,com.atlassian.sal.api.pluginsettings,com.atlassian.sa
 l.api.rdbms,com.atlassian.sal.api.scheduling,com.atlassian.sal.api.tr
 ansaction,com.atlassian.sal.api.upgrade,com.atlassian.sal.api.user,co
 m.atlassian.sal.api.usersettings,com.atlassian.sal.api.web.context,co
 m.atlassian.sal.api.websudo,com.atlassian.scheduler,com.atlassian.sch
 eduler.caesium.cron.parser,com.atlassian.scheduler.config,com.atlassi
 an.scheduler.cron,com.atlassian.scheduler.status,com.atlassian.soy.re
 nderer,com.atlassian.upm.api.license,com.atlassian.upm.api.license.en
 tity,com.atlassian.upm.api.license.event,com.atlassian.upm.api.util,c
 om.atlassian.util.concurrent;version="3.0",com.atlassian.velocity,com
 .atlassian.webhooks.api.util,com.atlassian.webhooks.spi,com.atlassian
 .webresource.api.data,com.google.common.base;version="26.0",com.googl
 e.common.cache;version="26.0",com.google.common.collect;version="26.0
 ",com.google.common.escape;version="26.0",com.google.common.html;vers
 ion="26.0",com.google.common.io;version="26.0",com.google.common.net;
 version="26.0",com.google.common.primitives;version="26.0",com.google
 .common.reflect;version="26.0",com.sun.jersey.api;version="1.19",com.
 sun.jersey.api.uri;version="1.19",com.sun.jersey.spi.container;versio
 n="1.19",groovy.beans;version="3.0",groovy.cli.internal;version="3.0"
 ,io.atlassian.fugue;version="4.7",io.atlassian.util.concurrent;versio
 n="4.0",javax.activation,javax.annotation,javax.crypto,javax.crypto.s
 pec,javax.imageio.metadata,javax.imageio.spi,javax.imageio.stream,jav
 ax.inject,javax.mail;version="1.6",javax.mail.internet;version="1.6",
 javax.management,javax.management.modelmbean,javax.management.remote,
 javax.management.remote.rmi,javax.management.timer,javax.naming,javax
 .naming.ldap,javax.naming.spi,javax.net,javax.net.ssl,javax.print.att
 ribute.standard,javax.rmi.ssl,javax.script,javax.security.auth.x500,j
 avax.servlet;version="3.1",javax.servlet.http;version="3.1",javax.ser
 vlet.jsp,javax.sql,javax.swing.plaf,javax.swing.plaf.metal,javax.swin
 g.table,javax.swing.text.html,javax.swing.undo,javax.transaction,java
 x.transaction.xa,javax.ws.rs;version="1.1",javax.ws.rs.core;version="
 1.1",javax.ws.rs.ext;version="1.1",javax.xml.datatype,javax.xml.names
 pace,javax.xml.stream,javax.xml.stream.events,javax.xml.stream.util,j
 avax.xml.transform,javax.xml.transform.dom,javax.xml.transform.sax,ja
 vax.xml.transform.stream,javax.xml.validation,javax.xml.xpath,net.jav
 a.ao;version="3.0",net.java.ao.schema;version="3.0",org.aopalliance.a
 op,org.aopalliance.intercept,org.apache.commons.codec.binary;version=
 "1.9",org.apache.commons.codec.digest;version="1.9",org.apache.common
 s.collections;version="3.2",org.apache.commons.collections.comparator
 s;version="3.2",org.apache.commons.collections.iterators;version="3.2
 ",org.apache.commons.collections.keyvalue;version="3.2",org.apache.co
 mmons.collections.map;version="3.2",org.apache.commons.httpclient,org
 .apache.commons.httpclient.auth,org.apache.commons.httpclient.methods
 ,org.apache.commons.httpclient.params,org.apache.commons.lang3.concur
 rent;version="3.8",org.apache.commons.lang3.exception;version="3.8",o
 rg.apache.commons.lang3.math;version="3.8",org.apache.commons.lang3.t
 ext;version="3.8",org.apache.commons.lang3.tuple;version="3.8",org.ap
 ache.commons.logging,org.apache.commons.pool,org.apache.commons.pool.
 impl,org.apache.http,org.apache.http.auth,org.apache.http.client,org.
 apache.http.client.entity,org.apache.http.client.methods,org.apache.h
 ttp.client.utils,org.apache.http.conn,org.apache.http.conn.params,org
 .apache.http.conn.routing,org.apache.http.conn.scheme,org.apache.http
 .conn.ssl,org.apache.http.entity,org.apache.http.entity.mime,org.apac
 he.http.entity.mime.content,org.apache.http.impl.client,org.apache.ht
 tp.impl.conn.tsccm,org.apache.http.message,org.apache.http.params,org
 .apache.http.protocol,org.apache.http.util,org.apache.log4j;version="
 1.2",org.apache.log4j.helpers;version="1.2",org.apache.log4j.spi;vers
 ion="1.2",org.apache.log4j.varia;version="1.2",org.apache.lucene.anal
 ysis.reverse,org.apache.lucene.analysis.tokenattributes,org.apache.lu
 cene.document,org.apache.lucene.index,org.apache.lucene.search,org.ap
 ache.lucene.util,org.apache.oro.text,org.apache.oro.text.regex,org.co
 dehaus.groovy.transform.tailrec;version="3.0",org.codehaus.jackson.an
 notate;version="1.9",org.codehaus.jackson.map;version="1.9",org.cyber
 neko.html.parsers,org.dom4j,org.dom4j.io,org.eclipse.gemini.blueprint
 .context;version="3.0",org.eclipse.gemini.blueprint.context.support;v
 ersion="3.0",org.eclipse.gemini.blueprint.service.exporter;version="3
 .0",org.eclipse.gemini.blueprint.service.exporter.support;version="3.
 0",org.eclipse.gemini.blueprint.service.importer.support;version="3.0
 ",org.joda.time;version="2.8",org.joda.time.format;version="2.8",org.
 ofbiz.core.entity,org.osgi.util.tracker;version="1.5",org.slf4j;versi
 on="1.7",org.slf4j.spi;version="1.7",org.springframework.aop,org.spri
 ngframework.aop.config,org.springframework.aop.framework,org.springfr
 amework.aop.framework.autoproxy,org.springframework.aop.scope,org.spr
 ingframework.aop.support,org.springframework.aop.support.annotation,o
 rg.springframework.beans,org.springframework.beans.factory,org.spring
 framework.beans.factory.annotation,org.springframework.beans.factory.
 config,org.springframework.beans.factory.parsing,org.springframework.
 beans.factory.support,org.springframework.beans.factory.xml,org.sprin
 gframework.beans.propertyeditors,org.springframework.cglib.core,org.s
 pringframework.cglib.proxy,org.springframework.cglib.reflect,org.spri
 ngframework.context.annotation,org.springframework.context.event,org.
 springframework.context.support,org.springframework.core,org.springfr
 amework.core.annotation,org.springframework.core.convert.converter,or
 g.springframework.core.convert.support,org.springframework.core.env,o
 rg.springframework.core.io.support,org.springframework.core.task,org.
 springframework.core.type,org.springframework.core.type.filter,org.sp
 ringframework.jndi,org.springframework.lang,org.springframework.sched
 uling,org.springframework.stereotype,org.springframework.util,org.spr
 ingframework.util.concurrent,org.springframework.util.xml,org.w3c.dom
 .bootstrap,org.w3c.dom.ls,org.xml.sax,org.xml.sax.ext,org.xml.sax.hel
 pers,webwork.action,com.atlassian.jira.plugin.webfragment.conditions,
 com.atlassian.jira.plugin.webfragment.contextproviders,com.atlassian.
 plugin.osgi.bridge.external,org.apache.lucene.analysis.standard,com.a
 tlassian.jira.concurrent,com.atlassian.jira.i18n,com.atlassian.jira.p
 lugin.util,com.atlassian.jira.issue.customfields.vdi;resolution:=opti
 onal,groovy.grape;resolution:=optional;version="[3.0,4)",groovy.runti
 me.metaclass;resolution:=optional,groovy.ui;resolution:=optional;vers
 ion="[3.0,4)",groovy.util;resolution:=optional;version="[3.0,4)",groo
 vy.xml;resolution:=optional;version="[3.0,4)",org.apache.ivy;resoluti
 on:=optional;version="[2.0,3)",org.apache.ivy.core;resolution:=option
 al;version="[2.0,3)",org.apache.ivy.core.cache;resolution:=optional;v
 ersion="[2.0,3)",org.apache.ivy.core.event;resolution:=optional;versi
 on="[2.0,3)",org.apache.ivy.core.event.download;resolution:=optional;
 version="[2.0,3)",org.apache.ivy.core.event.resolve;resolution:=optio
 nal;version="[2.0,3)",org.apache.ivy.core.module.descriptor;resolutio
 n:=optional;version="[2.0,3)",org.apache.ivy.core.module.id;resolutio
 n:=optional;version="[2.0,3)",org.apache.ivy.core.report;resolution:=
 optional;version="[2.0,3)",org.apache.ivy.core.resolve;resolution:=op
 tional;version="[2.0,3)",org.apache.ivy.core.settings;resolution:=opt
 ional;version="[2.0,3)",org.apache.ivy.plugins.matcher;resolution:=op
 tional;version="[2.0,3)",org.apache.ivy.plugins.resolver;resolution:=
 optional;version="[2.0,3)",org.apache.ivy.util;resolution:=optional;v
 ersion="[2.0,3)",org.apache.ivy.util.extendable;resolution:=optional;
 version="[2.0,3)",org.codehaus.groovy.tools;resolution:=optional;vers
 ion="[3.0,4)",org.codehaus.groovy.tools.shell;resolution:=optional;ve
 rsion="[3.0,4)",org.codehaus.groovy.tools.shell.util;resolution:=opti
 onal;version="[3.0,4)",org.fusesource.jansi;resolution:=optional;vers
 ion="[2.4,3)"
Require-Capability: osgi.ee;filter:="(&(osgi.ee=JavaSE)(version=1.8))"
Atlassian-Plugin-Key: com.onresolve.jira.groovy.groovyrunner
DynamicImport-Package: com.botronsoft.cmj.spi.*;version="1.7";resoluti
 on:=optional,com.awnaba.projectconfigurator.extensionpoints.common,co
 m.awnaba.projectconfigurator.extensionpoints.customentities,com.awnab
 a.projectconfigurator.extensionpoints.customentities.references,com.a
 wnaba.projectconfigurator.extensionpoints.extensionservices,com.awnab
 a.projectconfigurator.operationsapi,com.awnaba.projectconfigurator.op
 erationsapi.importtree,com.atlassian.servicedesk.api;version="4.0",co
 m.atlassian.servicedesk.api.automation.configuration.ruleset,com.atla
 ssian.servicedesk.api.comment;version="4.0",com.atlassian.servicedesk
 .api.customer;version="4.0",com.atlassian.servicedesk.api.organizatio
 n;version="4.0",com.atlassian.servicedesk.api.permission;version="4.0
 ",com.atlassian.servicedesk.api.portal;version="4.0",com.atlassian.se
 rvicedesk.api.requesttype;version="4.0",com.atlassian.servicedesk.api
 .user;version="4.0",com.atlassian.servicedesk.api.util.paging;version
 ="4.0",com.atlassian.servicedesk.internal.api.requesttype;version="4.
 0",com.atlassian.servicedesk.internal.api.requesttype.group;version="
 4.0",com.atlassian.servicedesk.internal.feature.customer.request.requ
 esttype.field,com.atlassian.jira.plugins.workinghours.api.calendar,co
 m.atlassian.jira.plugins.workinghours.api.calendar.builder,com.atlass
 ian.jira.plugins.workinghours.api.calendar.util,com.atlassian.jira.pl
 ugins.workinghours.api.rest.DTO,com.atlassian.plugin.automation.core.
 action,com.atlassian.plugin.automation.core.auditlog,com.atlassian.pl
 ugin.automation.core.codebarrel,com.atlassian.plugin.automation.util,
 com.codebarrel.automation.api.thirdparty,com.codebarrel.automation.ap
 i.thirdparty.context,com.codebarrel.automation.api.thirdparty.context
 .result,com.codebarrel.automation.api.config
Tool: Bnd-4.0.0.201805111645
Spring-Context: *
Export-Package: com.onresolve.dataprovider;uses:="com.atlassian.json.m
 arshal,com.atlassian.plugin.spring.scanner.annotation.component,com.a
 tlassian.plugin.webresource,com.atlassian.sal.api,com.atlassian.sal.a
 pi.user,com.atlassian.upm.api.license,com.atlassian.webresource.api.d
 ata,com.onresolve.dataprovider.model,com.onresolve.dataprovider.model
 .admin,com.onresolve.scriptrunner.analytics,com.onresolve.scriptrunne
 r.canned,com.onresolve.scriptrunner.canned.docs,com.onresolve.scriptr
 unner.config,com.onresolve.scriptrunner.licensing,com.onresolve.scrip
 trunner.runner,com.onresolve.scriptrunner.runner.diag,com.onresolve.s
 criptrunner.settings,com.onresolve.scriptrunner.user.properties.servi
 ce,groovy.lang,groovy.transform,javax.inject,org.apache.log4j,org.cod
 ehaus.groovy.runtime",com.onresolve.dataprovider.model;uses:="groovy.
 lang,groovy.transform",com.onresolve.dataprovider.model.admin;uses:="
 com.onresolve.dataprovider.model,com.onresolve.scriptrunner.settings.
 model,groovy.lang,groovy.transform",com.onresolve.fixture.groovy;uses
 :="groovy.lang,groovy.transform",com.onresolve.jira.behaviours;uses:=
 "com.atlassian.cache,com.atlassian.event.api,com.atlassian.jira.confi
 g,com.atlassian.jira.event,com.atlassian.jira.event.config,com.atlass
 ian.jira.event.project,com.atlassian.jira.issue,com.atlassian.jira.is
 sue.context,com.atlassian.jira.issue.issuetype,com.atlassian.jira.pro
 ject,com.atlassian.jira.security,com.atlassian.json.marshal,com.atlas
 sian.plugin.spring.scanner.annotation.export,com.atlassian.sal.api,co
 m.atlassian.webresource.api.data,com.onresolve.jira.behaviours.types,
 com.onresolve.jira.groovy,com.onresolve.jira.spring,com.onresolve.scr
 iptrunner.analytics.tracking,com.onresolve.scriptrunner.audit,com.onr
 esolve.scriptrunner.canned,com.onresolve.scriptrunner.canned.jira.uti
 ls.servicedesk,com.onresolve.scriptrunner.querydsl,com.onresolve.scri
 ptrunner.runner,com.onresolve.scriptrunner.runner.stc,com.onresolve.s
 criptrunner.storageregistry,groovy.lang,groovy.transform,groovy.util,
 javax.annotation,javax.inject,javax.servlet.http,org.apache.log4j,org
 .codehaus.groovy.runtime",com.onresolve.jira.behaviours.customfields,
 com.onresolve.jira.behaviours.project;uses:="com.atlassian.jira.avata
 r,com.atlassian.jira.project,com.atlassian.jira.security,com.onresolv
 e.jira.behaviours.types,groovy.lang,groovy.transform,javax.inject",co
 m.onresolve.jira.behaviours.restservice;uses:="com.atlassian.jira.con
 fig,com.atlassian.jira.issue,com.atlassian.jira.issue.fields,com.atla
 ssian.jira.project,com.atlassian.jira.security,com.atlassian.plugins.
 rest.common.security,com.atlassian.plugins.rest.common.security.jerse
 y,com.atlassian.sal.api,com.onresolve.jira.behaviours,com.onresolve.j
 ira.behaviours.project,com.onresolve.jira.behaviours.types,com.onreso
 lve.jira.servicedesk,com.onresolve.jira.spring,com.onresolve.scriptru
 nner.canned,com.onresolve.scriptrunner.filters,com.onresolve.scriptru
 nner.runner.rest.common.providers.reader,com.opensymphony.workflow.lo
 ader,com.sun.jersey.spi.container,groovy.lang,groovy.transform,javax.
 servlet.http,javax.ws.rs,javax.ws.rs.core,javax.ws.rs.ext,org.apache.
 log4j,org.codehaus.groovy.runtime",com.onresolve.jira.behaviours.type
 s;uses:="com.onresolve.jira.behaviours,com.onresolve.scriptrunner.mod
 el,com.onresolve.scriptrunner.model.jackson,groovy.lang,groovy.transf
 orm,javax.annotation",com.onresolve.jira.behaviours.upgrades;uses:="c
 om.atlassian.plugin.spring.scanner.annotation.export,com.atlassian.sa
 l.api.message,com.atlassian.sal.api.upgrade,com.onresolve.jira.behavi
 ours,com.onresolve.jira.spring,groovy.lang,groovy.transform,javax.inj
 ect,org.codehaus.groovy.runtime",com.onresolve.jira.groovy;uses:="com
 .atlassian.jira.config,com.atlassian.jira.issue,com.atlassian.jira.is
 sue.fields,com.atlassian.jira.project,com.atlassian.jira.security,com
 .atlassian.jira.security.groups,com.atlassian.jira.security.roles,com
 .atlassian.jira.user,com.atlassian.jira.user.util,com.atlassian.jira.
 util,com.atlassian.jira.workflow,com.atlassian.sal.api.user,com.onres
 olve.jira.behaviours,com.onresolve.jira.behaviours.types,com.onresolv
 e.licensing,com.onresolve.scriptrunner.canned.jira.utils,com.onresolv
 e.scriptrunner.jira.workflow,com.onresolve.scriptrunner.runner,com.on
 resolve.scriptrunner.runner.diag,com.opensymphony.module.propertyset,
 com.opensymphony.workflow,groovy.lang,groovy.transform,javax.inject,o
 rg.apache.log4j,org.codehaus.groovy.runtime",com.onresolve.jira.groov
 y.jql;uses:="com.atlassian.cache,com.atlassian.core.util,com.atlassia
 n.event.api,com.atlassian.jira,com.atlassian.jira.bc.issue.comment.pr
 operty,com.atlassian.jira.bc.issue.properties,com.atlassian.jira.bc.i
 ssue.search,com.atlassian.jira.config.properties,com.atlassian.jira.e
 vent.comment,com.atlassian.jira.index,com.atlassian.jira.issue,com.at
 lassian.jira.issue.changehistory,com.atlassian.jira.issue.comments,co
 m.atlassian.jira.issue.customfields,com.atlassian.jira.issue.customfi
 elds.impl,com.atlassian.jira.issue.customfields.searchers,com.atlassi
 an.jira.issue.customfields.searchers.renderer,com.atlassian.jira.issu
 e.customfields.searchers.transformer,com.atlassian.jira.issue.customf
 ields.view,com.atlassian.jira.issue.fields,com.atlassian.jira.issue.i
 ndex,com.atlassian.jira.issue.index.indexers,com.atlassian.jira.issue
 .link,com.atlassian.jira.issue.search,com.atlassian.jira.issue.search
 .searchers.information,com.atlassian.jira.issue.search.searchers.rend
 erer,com.atlassian.jira.issue.search.searchers.transformer,com.atlass
 ian.jira.issue.search.searchers.util,com.atlassian.jira.issue.transpo
 rt,com.atlassian.jira.jql,com.atlassian.jira.jql.operand,com.atlassia
 n.jira.jql.parser,com.atlassian.jira.jql.query,com.atlassian.jira.jql
 .util,com.atlassian.jira.ofbiz,com.atlassian.jira.plugin.customfield,
 com.atlassian.jira.plugin.jql.function,com.atlassian.jira.project,com
 .atlassian.jira.security,com.atlassian.jira.user,com.atlassian.jira.u
 til,com.atlassian.jira.web,com.atlassian.plugin,com.atlassian.query,c
 om.atlassian.query.clause,com.atlassian.query.operand,com.atlassian.q
 uery.operator,com.onresolve.jira.groovy.jql.discovery,com.onresolve.j
 ira.groovy.jql.discovery.model,com.onresolve.jira.groovy.jql.relation
 s.portfolio,com.onresolve.licensing,com.onresolve.scriptrunner.analyt
 ics.tracking,com.onresolve.scriptrunner.canned.validators,com.onresol
 ve.scriptrunner.runner,com.onresolve.scriptrunner.runner.diag,groovy.
 lang,groovy.transform,javax.annotation,javax.inject,org.apache.log4j,
 org.apache.lucene.document,org.apache.lucene.index,org.apache.lucene.
 search,org.codehaus.groovy.ast.expr,org.codehaus.groovy.control.custo
 mizers,org.codehaus.groovy.runtime,webwork.action",com.onresolve.jira
 .groovy.jql.discovery;uses:="com.atlassian.jira.bc.issue.search,com.a
 tlassian.jira.issue.link,com.atlassian.jira.jql.parser,com.atlassian.
 jira.jql.util,com.atlassian.jira.security,com.atlassian.jira.security
 .roles,com.atlassian.jira.util,com.onresolve.jira.groovy.jql.discover
 y.model,com.onresolve.scriptrunner.runner,groovy.lang,groovy.transfor
 m,javax.inject,org.codehaus.groovy.runtime",com.onresolve.jira.groovy
 .jql.discovery.model;uses:="groovy.lang,groovy.transform",com.onresol
 ve.jira.groovy.jql.entities;uses:="com.atlassian.jira,com.atlassian.j
 ira.jql.operand,com.atlassian.jira.jql.operand.registry,com.atlassian
 .jira.jql.query,com.atlassian.jira.jql.resolver,com.atlassian.jira.pr
 oject.version,com.atlassian.jira.security,com.atlassian.jira.security
 .groups,com.atlassian.jira.security.roles,com.atlassian.jira.timezone
 ,com.atlassian.jira.user,com.atlassian.jira.user.util,com.atlassian.j
 ira.util,com.atlassian.query.clause,com.atlassian.query.operand,com.o
 nresolve.jira.groovy.jql,com.onresolve.jira.groovy.jql.discovery.mode
 l,com.onresolve.jira.groovy.jql.role,com.onresolve.scriptrunner.canne
 d.validators,groovy.lang,groovy.transform,org.apache.lucene.search,or
 g.codehaus.groovy.runtime",com.onresolve.jira.groovy.jql.entitymatch;
 uses:="com.atlassian.jira,com.atlassian.jira.bc.project.component,com
 .atlassian.jira.issue,com.atlassian.jira.issue.fields,com.atlassian.j
 ira.jql.operand,com.atlassian.jira.jql.query,com.atlassian.jira.proje
 ct,com.atlassian.jira.project.version,com.atlassian.jira.user,com.atl
 assian.jira.util,com.atlassian.query.clause,com.atlassian.query.opera
 nd,com.onresolve.jira.groovy.jql,com.onresolve.jira.groovy.jql.discov
 ery.model,groovy.lang,groovy.transform,org.apache.log4j,org.apache.lu
 cene.search,org.codehaus.groovy.runtime",com.onresolve.jira.groovy.jq
 l.expression;uses:="com.atlassian.jira,com.atlassian.jira.issue.searc
 h,com.atlassian.jira.issue.search.constants,com.atlassian.jira.issue.
 statistics.util,com.atlassian.jira.jql,com.atlassian.jira.util,com.at
 lassian.query.clause,com.atlassian.query.operand,com.atlassian.query.
 operator,groovy.lang,groovy.transform,org.apache.lucene.document,org.
 codehaus.groovy.runtime",com.onresolve.jira.groovy.jql.gshell;uses:="
 groovy.lang,groovy.transform,org.codehaus.groovy.ast,org.codehaus.gro
 ovy.ast.expr,org.codehaus.groovy.classgen,org.codehaus.groovy.control
 ",com.onresolve.jira.groovy.jql.indexing;uses:="com.atlassian.jira.bc
 .issue.properties,com.atlassian.jira.config.properties,com.atlassian.
 jira.issue,com.atlassian.jira.issue.changehistory,com.atlassian.jira.
 issue.fields,com.atlassian.jira.issue.index.indexers.impl,com.atlassi
 an.jira.issue.link,com.atlassian.jira.web,com.onresolve.jira.groovy.j
 ql,groovy.lang,groovy.transform,org.apache.log4j,org.apache.lucene.do
 cument,org.codehaus.groovy.runtime",com.onresolve.jira.groovy.jql.plu
 gins;uses:="com.atlassian.greenhopper.model.query,com.atlassian.green
 hopper.model.rapid,com.atlassian.greenhopper.service,com.atlassian.gr
 eenhopper.service.rapid,com.atlassian.greenhopper.service.rapid.view,
 com.atlassian.greenhopper.service.sprint,com.atlassian.greenhopper.we
 b.rapid.chart,com.atlassian.jira.config,com.atlassian.jira.issue,com.
 atlassian.jira.issue.changehistory,com.atlassian.jira.jql.query,com.a
 tlassian.jira.user,com.atlassian.jira.util,com.atlassian.query.clause
 ,com.atlassian.query.operand,com.onresolve.jira.groovy.jql,com.onreso
 lve.jira.groovy.jql.discovery.model,com.onresolve.scriptrunner.canned
 .jira.utils.plugins,groovy.lang,groovy.transform,org.apache.log4j,org
 .apache.lucene.search,org.codehaus.groovy.runtime",com.onresolve.jira
 .groovy.jql.relations;uses:="com.atlassian.jira.issue,com.atlassian.j
 ira.issue.fields,com.atlassian.jira.issue.link,com.atlassian.jira.iss
 ue.statistics.util,com.atlassian.jira.jql.query,com.atlassian.jira.us
 er,com.atlassian.jira.util,com.atlassian.query.clause,com.atlassian.q
 uery.operand,com.onresolve.jira.groovy.jql,com.onresolve.jira.groovy.
 jql.discovery,com.onresolve.jira.groovy.jql.discovery.model,groovy.la
 ng,groovy.transform,org.apache.log4j,org.apache.lucene.document,org.a
 pache.lucene.search,org.codehaus.groovy.runtime",com.onresolve.jira.g
 roovy.jql.relations.collectors;uses:="com.atlassian.jira.issue.statis
 tics.util,com.google.common.collect,groovy.lang,groovy.transform,org.
 apache.lucene.document,org.codehaus.groovy.runtime",com.onresolve.jir
 a.groovy.jql.relations.portfolio;uses:="com.atlassian.jira.issue.stat
 istics.util,com.atlassian.jira.jql.query,com.atlassian.jira.user,com.
 atlassian.jira.util,com.atlassian.query.clause,com.atlassian.query.op
 erand,com.onresolve.jira.groovy.jql,com.onresolve.jira.groovy.jql.dis
 covery.model,com.onresolve.scriptrunner.runner.customisers,groovy.lan
 g,groovy.transform,org.apache.log4j,org.apache.lucene.document,org.ap
 ache.lucene.search,org.codehaus.groovy.runtime",com.onresolve.jira.gr
 oovy.jql.role;uses:="com.atlassian.jira.jql.query,com.atlassian.jira.
 project,com.atlassian.jira.security.groups,com.atlassian.jira.securit
 y.roles,groovy.lang,groovy.transform,javax.inject,org.apache.lucene.s
 earch,org.codehaus.groovy.runtime",com.onresolve.jira.groovy.jql.user
 s;uses:="com.atlassian.crowd.embedded.api,com.atlassian.jira,com.atla
 ssian.jira.bc.user.search,com.atlassian.jira.issue.fields,com.atlassi
 an.jira.issue.search.managers,com.atlassian.jira.jql.operand,com.atla
 ssian.jira.jql.query,com.atlassian.jira.security.roles,com.atlassian.
 jira.user,com.atlassian.jira.user.util,com.atlassian.jira.util,com.at
 lassian.query.clause,com.atlassian.query.operand,com.onresolve.jira.g
 roovy.jql,com.onresolve.jira.groovy.jql.discovery,com.onresolve.jira.
 groovy.jql.discovery.model,com.onresolve.jira.groovy.jql.role,com.onr
 esolve.scriptrunner.fields,groovy.lang,groovy.transform,org.apache.lu
 cene.search,org.codehaus.groovy.runtime",com.onresolve.jira.groovy.jq
 l.util;uses:="groovy.lang,groovy.transform",com.onresolve.jira.groovy
 .regex;uses:="groovy.lang,groovy.transform",com.onresolve.jira.groovy
 .test.infra.validation;uses:="com.atlassian.plugin.spring.scanner.ann
 otation.export,groovy.lang,groovy.transform,javax.inject,org.springfr
 amework.beans.factory.config",com.onresolve.jira.groovy.user;uses:="c
 om.atlassian.jira.config.properties,com.atlassian.jira.issue,com.atla
 ssian.jira.issue.context,com.atlassian.jira.issue.fields,com.atlassia
 n.jira.issue.fields.screen,com.atlassian.jira.issue.fields.screen.iss
 uetype,com.atlassian.jira.workflow,com.onresolve.jira.behaviours.type
 s,com.onresolve.jira.groovy,com.opensymphony.workflow.loader,groovy.l
 ang,groovy.transform,javax.servlet.http,org.apache.log4j,org.codehaus
 .groovy.runtime",com.onresolve.jira.groovy.user.extractors;uses:="com
 .atlassian.crowd.embedded.api,com.atlassian.jira.bc.project.component
 ,com.atlassian.jira.issue.context,com.atlassian.jira.issue.customfiel
 ds.option,com.atlassian.jira.issue.fields,com.atlassian.jira.issue.is
 suetype,com.atlassian.jira.issue.priority,com.atlassian.jira.issue.re
 solution,com.atlassian.jira.project,com.atlassian.jira.project.versio
 n,com.atlassian.jira.security.roles,com.google.common.reflect,com.onr
 esolve.jira.behaviours.types,groovy.lang,groovy.transform,org.apache.
 log4j,org.codehaus.groovy.runtime",com.onresolve.jira.issue.customfie
 lds;uses:="com.atlassian.jira.issue.customfields.persistence,com.atla
 ssian.jira.issue.customfields.vdi,groovy.lang,groovy.transform",com.o
 nresolve.jira.issue.index;uses:="com.atlassian.jira.issue.index,groov
 y.lang,groovy.transform",com.onresolve.jira.scriptfields;uses:="com.a
 tlassian.jira.bc.issue,com.atlassian.jira.config.properties,com.atlas
 sian.jira.issue,com.atlassian.jira.issue.customfields,com.atlassian.j
 ira.issue.customfields.converters,com.atlassian.jira.issue.customfiel
 ds.searchers,com.atlassian.jira.issue.customfields.searchers.transfor
 mer,com.atlassian.jira.issue.customfields.statistics,com.atlassian.ji
 ra.issue.fields,com.atlassian.jira.issue.index.indexers.impl,com.atla
 ssian.jira.issue.search,com.atlassian.jira.issue.search.searchers.inf
 ormation,com.atlassian.jira.issue.search.searchers.renderer,com.atlas
 sian.jira.issue.search.searchers.transformer,com.atlassian.jira.issue
 .search.util,com.atlassian.jira.issue.statistics,com.atlassian.jira.j
 ql.builder,com.atlassian.jira.jql.operand,com.atlassian.jira.jql.quer
 y,com.atlassian.jira.jql.util,com.atlassian.jira.project,com.atlassia
 n.jira.project.version,com.atlassian.jira.security,com.atlassian.jira
 .security.groups,com.atlassian.jira.template,com.atlassian.jira.user,
 com.atlassian.jira.util,com.atlassian.jira.util.velocity,com.atlassia
 n.jira.web,com.atlassian.plugin.spring.scanner.annotation.export,com.
 atlassian.query.operator,groovy.lang,groovy.transform,javax.inject,or
 g.apache.log4j,org.apache.lucene.document,org.codehaus.groovy.runtime
 ",com.onresolve.jira.servicedesk;uses:="com.atlassian.jira.issue,com.
 atlassian.jira.issue.fields,com.atlassian.jira.issue.fields.layout.fi
 eld,com.atlassian.jira.user,com.onresolve.jira.servicedesk.model,com.
 onresolve.scriptrunner.runner.rest.jira,groovy.lang,groovy.transform,
 javax.inject,org.codehaus.groovy.runtime",com.onresolve.jira.serviced
 esk.model;uses:="groovy.lang,groovy.transform",com.onresolve.jira.spr
 ing;uses:="com.atlassian.jira.plugin.customfield,com.atlassian.jira.u
 til,com.onresolve.jira.issue.customfields,com.onresolve.jira.issue.in
 dex,com.onresolve.scriptrunner.spring,groovy.lang,groovy.transform,ja
 vax.inject,org.springframework.context.annotation",com.onresolve.lice
 nsing;uses:="com.atlassian.event.api,com.atlassian.extras.api,com.atl
 assian.jira.application,com.atlassian.jira.cluster,com.atlassian.plug
 in,com.atlassian.plugin.spring.scanner.annotation.export,com.atlassia
 n.sal.api,com.atlassian.sal.api.lifecycle,com.atlassian.sal.api.user,
 com.atlassian.scheduler,com.atlassian.soy.renderer,com.atlassian.upm.
 api.license,com.atlassian.upm.api.license.event,com.onresolve.scriptr
 unner,com.onresolve.scriptrunner.runner,groovy.lang,groovy.transform,
 javax.annotation,javax.inject,javax.servlet,javax.servlet.http,org.ap
 ache.log4j,org.codehaus.groovy.runtime,org.dom4j,org.springframework.
 beans.factory",com.onresolve.osgi;uses:="groovy.lang,groovy.transform
 ,javax.inject",com.onresolve.scriptrunner;uses:="com.atlassian.jira.s
 ecurity.groups,com.onresolve.scriptrunner.canned,com.onresolve.script
 runner.canned.common.rest.model,com.onresolve.scriptrunner.runner,com
 .onresolve.scriptrunner.runner.classloading,com.onresolve.scriptrunne
 r.stc.completions.request,com.onresolve.scriptrunner.stc.typecheck,gr
 oovy.lang,groovy.transform,javax.inject,org.codehaus.groovy.runtime",
 com.onresolve.scriptrunner.analytics;uses:="com.atlassian.activeobjec
 ts.external,com.atlassian.cache,com.atlassian.event.api,com.atlassian
 .sal.api.executor,com.onresolve.scriptrunner,com.onresolve.scriptrunn
 er.runner,groovy.lang,groovy.transform,javax.inject,org.apache.log4j,
 org.codehaus.groovy.runtime,org.springframework.stereotype",com.onres
 olve.scriptrunner.analytics.events;uses:="com.atlassian.plugin.event,
 com.atlassian.plugin.event.events,com.atlassian.sal.api.executor,com.
 onresolve.scriptrunner.analytics.tracking,com.onresolve.scriptrunner.
 runner,groovy.lang,groovy.transform,javax.annotation,javax.inject,org
 .apache.log4j,org.codehaus.groovy.runtime",com.onresolve.scriptrunner
 .analytics.tracking;uses:="com.atlassian.sal.api.license,com.atlassia
 n.sal.api.user,com.onresolve.scriptrunner.analytics,com.onresolve.scr
 iptrunner.runner,groovy.lang,groovy.transform,javax.annotation,javax.
 inject,org.codehaus.groovy.runtime",com.onresolve.scriptrunner.analyt
 ics.util;uses:="groovy.lang,groovy.transform",com.onresolve.scriptrun
 ner.ao;uses:="javax.inject",com.onresolve.scriptrunner.appfirecm.erro
 r;uses:="groovy.lang,groovy.transform",com.onresolve.scriptrunner.app
 firecm.handlers;uses:="com.onresolve.scriptrunner.analytics.tracking,
 com.onresolve.scriptrunner.appfirecm.json,com.onresolve.scriptrunner.
 appfirecm.objecttype,com.onresolve.spring,groovy.lang,groovy.transfor
 m,javax.inject,org.codehaus.groovy.runtime",com.onresolve.scriptrunne
 r.appfirecm.handlers.behaviours;uses:="com.atlassian.plugin.spring.sc
 anner.annotation,com.onresolve.jira.behaviours,com.onresolve.jira.beh
 aviours.types,com.onresolve.scriptrunner.appfirecm.handlers,com.onres
 olve.scriptrunner.appfirecm.json,com.onresolve.scriptrunner.appfirecm
 .objecttype,com.onresolve.scriptrunner.appfirecm.objecttype.behaviour
 ,com.onresolve.scriptrunner.appfirecm.reference,groovy.lang,groovy.tr
 ansform,javax.inject,org.apache.log4j,org.codehaus.groovy.runtime,org
 .springframework.context.annotation",com.onresolve.scriptrunner.appfi
 recm.handlers.emailaddress;uses:="com.atlassian.jira.issue,com.atlass
 ian.plugin.spring.scanner.annotation,com.onresolve.scriptrunner.appfi
 recm.handlers,com.onresolve.scriptrunner.appfirecm.reference,groovy.l
 ang,groovy.transform,javax.inject,org.codehaus.groovy.runtime",com.on
 resolve.scriptrunner.appfirecm.handlers.fragments;uses:="com.atlassia
 n.plugin.spring.scanner.annotation,com.onresolve.scriptrunner.appfire
 cm.handlers,com.onresolve.scriptrunner.appfirecm.json,com.onresolve.s
 criptrunner.appfirecm.objecttype,com.onresolve.scriptrunner.appfirecm
 .objecttype.fragment,com.onresolve.scriptrunner.appfirecm.reference,c
 om.onresolve.scriptrunner.fragments,com.onresolve.scriptrunner.fragme
 nts.model,groovy.lang,groovy.transform,javax.inject,org.codehaus.groo
 vy.runtime",com.onresolve.scriptrunner.appfirecm.handlers.jobs;uses:=
 "com.atlassian.plugin.spring.scanner.annotation,com.onresolve.scriptr
 unner.appfirecm.handlers,com.onresolve.scriptrunner.appfirecm.json,co
 m.onresolve.scriptrunner.appfirecm.objecttype,com.onresolve.scriptrun
 ner.appfirecm.objecttype.job,com.onresolve.scriptrunner.appfirecm.ref
 erence,com.onresolve.scriptrunner.scheduled,com.onresolve.scriptrunne
 r.scheduled.model,groovy.lang,groovy.transform,javax.inject,org.apach
 e.log4j,org.codehaus.groovy.runtime",com.onresolve.scriptrunner.appfi
 recm.handlers.linktypewithdirection;uses:="com.onresolve.scriptrunner
 .appfirecm.handlers,com.onresolve.scriptrunner.appfirecm.reference,gr
 oovy.lang,groovy.transform",com.onresolve.scriptrunner.appfirecm.hand
 lers.listeners;uses:="com.onresolve.scriptrunner.appfirecm.handlers,c
 om.onresolve.scriptrunner.appfirecm.handlers.emailaddress,com.onresol
 ve.scriptrunner.appfirecm.json,com.onresolve.scriptrunner.appfirecm.o
 bjecttype,com.onresolve.scriptrunner.appfirecm.objecttype.listener,co
 m.onresolve.scriptrunner.appfirecm.reference,com.onresolve.scriptrunn
 er.canned.jira.listener,groovy.lang,groovy.transform,javax.inject,org
 .codehaus.groovy.runtime,org.springframework.context.annotation",com.
 onresolve.scriptrunner.appfirecm.handlers.resources;uses:="com.atlass
 ian.plugin.spring.scanner.annotation,com.onresolve.scriptrunner.appfi
 recm.handlers,com.onresolve.scriptrunner.appfirecm.json,com.onresolve
 .scriptrunner.appfirecm.objecttype,com.onresolve.scriptrunner.appfire
 cm.objecttype.resource,com.onresolve.scriptrunner.db.configurations,c
 om.onresolve.scriptrunner.ldap.model,com.onresolve.scriptrunner.proje
 ctconfigurator.module.resources,com.onresolve.scriptrunner.resources,
 groovy.lang,groovy.transform,javax.inject,org.codehaus.groovy.runtime
 ",com.onresolve.scriptrunner.appfirecm.handlers.restendpoint;uses:="c
 om.atlassian.plugin.spring.scanner.annotation,com.onresolve.scriptrun
 ner.appfirecm.handlers,com.onresolve.scriptrunner.appfirecm.json,com.
 onresolve.scriptrunner.appfirecm.objecttype,com.onresolve.scriptrunne
 r.appfirecm.objecttype.restendpoint,com.onresolve.scriptrunner.canned
 .common.rest,com.onresolve.scriptrunner.runner,groovy.lang,groovy.tra
 nsform,javax.inject,org.codehaus.groovy.runtime",com.onresolve.script
 runner.appfirecm.handlers.scriptfields;uses:="com.atlassian.jira.issu
 e,com.atlassian.jira.issue.fields,com.atlassian.jira.issue.fields.con
 fig,com.atlassian.plugin.spring.scanner.annotation,com.onresolve.scri
 ptrunner.appfirecm.handlers.resources,com.onresolve.scriptrunner.appf
 irecm.json,com.onresolve.scriptrunner.appfirecm.reference,com.onresol
 ve.scriptrunner.canned.jira.fields.model,com.onresolve.scriptrunner.d
 b.configurations,com.onresolve.scriptrunner.fields,com.onresolve.scri
 ptrunner.resources,com.onresolve.spring,groovy.lang,groovy.transform,
 javax.inject,org.codehaus.groovy.runtime",com.onresolve.scriptrunner.
 appfirecm.handlers.workflow;uses:="com.atlassian.plugin.spring.scanne
 r.annotation,com.onresolve.scriptrunner.appfirecm.handlers,com.onreso
 lve.scriptrunner.appfirecm.handlers.emailaddress,com.onresolve.script
 runner.appfirecm.reference,com.onresolve.spring,groovy.lang,groovy.tr
 ansform,javax.inject,org.codehaus.groovy.runtime",com.onresolve.scrip
 trunner.appfirecm.json;uses:="com.onresolve.scriptrunner.canned,groov
 y.lang,groovy.transform,javax.inject,org.codehaus.groovy.runtime",com
 .onresolve.scriptrunner.appfirecm.objecttype;uses:="com.onresolve.spr
 ing,groovy.lang,groovy.transform,javax.inject,org.codehaus.groovy.run
 time",com.onresolve.scriptrunner.appfirecm.objecttype.behaviour;uses:
 ="com.atlassian.plugin.spring.scanner.annotation,com.onresolve.jira.b
 ehaviours.types,com.onresolve.scriptrunner.appfirecm.objecttype,groov
 y.lang,groovy.transform,javax.inject",com.onresolve.scriptrunner.appf
 irecm.objecttype.fragment;uses:="com.atlassian.plugin.spring.scanner.
 annotation,com.onresolve.scriptrunner.appfirecm.objecttype,com.onreso
 lve.scriptrunner.fragments.model,groovy.lang,groovy.transform,javax.i
 nject",com.onresolve.scriptrunner.appfirecm.objecttype.job;uses:="com
 .atlassian.plugin.spring.scanner.annotation,com.onresolve.scriptrunne
 r.scheduled.model,groovy.lang,groovy.transform,javax.inject",com.onre
 solve.scriptrunner.appfirecm.objecttype.listener;uses:="com.atlassian
 .plugin.spring.scanner.annotation,com.onresolve.scriptrunner.appfirec
 m.objecttype,com.onresolve.scriptrunner.canned.jira.listener,groovy.l
 ang,groovy.transform,javax.inject",com.onresolve.scriptrunner.appfire
 cm.objecttype.resource;uses:="com.atlassian.plugin.spring.scanner.ann
 otation,com.onresolve.scriptrunner.appfirecm.objecttype,com.onresolve
 .scriptrunner.db.configurations,groovy.lang,groovy.transform,javax.an
 notation,javax.inject",com.onresolve.scriptrunner.appfirecm.objecttyp
 e.restendpoint;uses:="com.atlassian.plugin.spring.scanner.annotation,
 com.onresolve.scriptrunner.appfirecm.objecttype,com.onresolve.scriptr
 unner.canned.common.rest,groovy.lang,groovy.transform,javax.annotatio
 n,javax.inject",com.onresolve.scriptrunner.appfirecm.reference;uses:=
 "com.atlassian.crowd.embedded.api,com.atlassian.jira.config,com.atlas
 sian.jira.event.type,com.atlassian.jira.issue,com.atlassian.jira.issu
 e.fields,com.atlassian.jira.issue.issuetype,com.atlassian.jira.issue.
 link,com.atlassian.jira.issue.status,com.atlassian.jira.jql.parser,co
 m.atlassian.jira.jql.util,com.atlassian.jira.project,com.atlassian.ji
 ra.security.groups,com.atlassian.jira.security.roles,com.atlassian.ji
 ra.user,com.atlassian.jira.user.util,com.atlassian.plugin.spring.scan
 ner.annotation,com.atlassian.sal.api,com.onresolve.jira.behaviours,gr
 oovy.lang,groovy.transform,javax.inject,org.codehaus.groovy.runtime",
 com.onresolve.scriptrunner.application;uses:="com.atlassian.sal.api,g
 roovy.lang,groovy.transform,javax.inject",com.onresolve.scriptrunner.
 audit;uses:="com.atlassian.jira.auditing,com.atlassian.plugin.spring.
 scanner.annotation.component,com.atlassian.sal.api.features,com.onres
 olve.scriptrunner.canned,groovy.lang,groovy.transform,javax.inject,or
 g.apache.log4j,org.codehaus.groovy.runtime",com.onresolve.scriptrunne
 r.audit.events;uses:="com.onresolve.scriptrunner.audit",com.onresolve
 .scriptrunner.audit.events.listener;uses:="com.atlassian.event.api,co
 m.atlassian.plugin.spring.scanner.annotation.component,com.onresolve.
 scriptrunner.audit,groovy.lang,groovy.transform,javax.annotation,java
 x.inject",com.onresolve.scriptrunner.automation;uses:="com.atlassian.
 jira.issue,com.atlassian.jira.security,com.atlassian.jira.user.util,c
 om.atlassian.plugin.automation.core.action,com.atlassian.plugin.autom
 ation.core.auditlog,com.atlassian.plugin.automation.core.codebarrel,c
 om.atlassian.plugin.automation.util,com.atlassian.sal.api.message,com
 .codebarrel.automation.api.config,com.codebarrel.automation.api.third
 party,com.codebarrel.automation.api.thirdparty.context,com.codebarrel
 .automation.api.thirdparty.context.result,com.onresolve.scriptrunner.
 canned,com.onresolve.scriptrunner.model,com.onresolve.scriptrunner.ru
 nner,com.onresolve.scriptrunner.runner.stc,groovy.lang,groovy.transfo
 rm,javax.inject,org.apache.log4j,org.codehaus.groovy.runtime",com.onr
 esolve.scriptrunner.beans;uses:="javax.inject,org.springframework.bea
 ns.factory",com.onresolve.scriptrunner.bitbucket;uses:="net.java.ao,n
 et.java.ao.schema",com.onresolve.scriptrunner.canned;uses:="com.atlas
 sian.plugin.spring.scanner.annotation.component,com.atlassian.plugin.
 spring.scanner.annotation.export,com.google.common.reflect,com.onreso
 lve.jira.groovy.test.infra.validation,com.onresolve.scriptrunner.cann
 ed.docs,com.onresolve.scriptrunner.canned.tags,com.onresolve.scriptru
 nner.canned.util,com.onresolve.scriptrunner.listener,groovy.lang,groo
 vy.transform,javax.annotation,javax.inject,org.codehaus.groovy.runtim
 e",com.onresolve.scriptrunner.canned.common;uses:="com.atlassian.appl
 inks.api,com.atlassian.sal.api,com.atlassian.sal.api.net,com.atlassia
 n.sal.api.user,com.onresolve.scriptrunner.canned,com.onresolve.script
 runner.canned.docs,com.onresolve.scriptrunner.canned.util,com.onresol
 ve.scriptrunner.runner.customisers,com.onresolve.scriptrunner.setting
 s,com.onresolve.scriptrunner.switchuser,groovy.lang,groovy.transform,
 io.atlassian.util.concurrent,javax.annotation,javax.inject,org.apache
 .log4j,org.codehaus.groovy.runtime",com.onresolve.scriptrunner.canned
 .common.admin;uses:="com.atlassian.sal.api,com.atlassian.scheduler.co
 nfig,com.atlassian.scheduler.status,com.onresolve.scriptrunner.canned
 ,com.onresolve.scriptrunner.canned.common.admin.model,com.onresolve.s
 criptrunner.canned.docs,com.onresolve.scriptrunner.canned.tags,com.on
 resolve.scriptrunner.canned.testrunner,com.onresolve.scriptrunner.can
 ned.util,com.onresolve.scriptrunner.runner,com.onresolve.scriptrunner
 .runner.customisers,com.onresolve.scriptrunner.runner.diag,com.onreso
 lve.scriptrunner.testrunner,groovy.lang,groovy.transform,javax.inject
 ,org.apache.log4j,org.codehaus.groovy.runtime",com.onresolve.scriptru
 nner.canned.common.admin.model;uses:="com.onresolve.scriptrunner.cann
 ed,groovy.lang,groovy.transform",com.onresolve.scriptrunner.canned.co
 mmon.fragments;uses:="com.atlassian.sal.api,com.onresolve.scriptrunne
 r.canned,com.onresolve.scriptrunner.canned.common.fragments.model,com
 .onresolve.scriptrunner.canned.docs,com.onresolve.scriptrunner.canned
 .tags,com.onresolve.scriptrunner.canned.util,com.onresolve.scriptrunn
 er.fragments,com.onresolve.scriptrunner.fragments.model,com.onresolve
 .scriptrunner.runner,com.onresolve.scriptrunner.runner.customisers,gr
 oovy.lang,groovy.transform,org.codehaus.groovy.runtime,org.dom4j",com
 .onresolve.scriptrunner.canned.common.fragments.model;uses:="com.onre
 solve.scriptrunner.fragments.model,groovy.lang,groovy.transform",com.
 onresolve.scriptrunner.canned.common.params;uses:="groovy.lang,groovy
 .transform,org.codehaus.groovy.runtime",com.onresolve.scriptrunner.ca
 nned.common.params.tree;uses:="groovy.lang,groovy.transform",com.onre
 solve.scriptrunner.canned.common.rest;uses:="com.onresolve.scriptrunn
 er.canned,com.onresolve.scriptrunner.canned.docs,com.onresolve.script
 runner.canned.jira.utils,com.onresolve.scriptrunner.canned.tags,com.o
 nresolve.scriptrunner.canned.util,com.onresolve.scriptrunner.model,co
 m.onresolve.scriptrunner.model.jackson,com.onresolve.scriptrunner.run
 ner,com.onresolve.scriptrunner.runner.customisers,groovy.lang,groovy.
 transform,javax.inject",com.onresolve.scriptrunner.canned.common.rest
 .model;uses:="groovy.lang,groovy.transform",com.onresolve.scriptrunne
 r.canned.common.testutils;uses:="groovy.lang,groovy.transform",com.on
 resolve.scriptrunner.canned.db;uses:="com.onresolve.scriptrunner.cann
 ed,com.onresolve.scriptrunner.canned.docs,com.onresolve.scriptrunner.
 canned.resources,com.onresolve.scriptrunner.canned.tags,com.onresolve
 .scriptrunner.canned.util,com.onresolve.scriptrunner.db,com.onresolve
 .scriptrunner.db.configurations,groovy.lang,groovy.transform,groovy.t
 ransform.stc,org.apache.log4j,org.codehaus.groovy.runtime",com.onreso
 lve.scriptrunner.canned.docs;uses:="com.onresolve.scriptrunner.runner
 ,groovy.lang,groovy.transform,javax.inject,org.apache.log4j",com.onre
 solve.scriptrunner.canned.jira.admin;uses:="com.atlassian.event.api,c
 om.atlassian.jira.bc,com.atlassian.jira.bc.filter,com.atlassian.jira.
 bc.issue.search,com.atlassian.jira.bc.portal,com.atlassian.jira.bc.pr
 oject,com.atlassian.jira.bc.project.component,com.atlassian.jira.bc.p
 rojectroles,com.atlassian.jira.config,com.atlassian.jira.issue,com.at
 lassian.jira.issue.context.manager,com.atlassian.jira.issue.customfie
 lds.option,com.atlassian.jira.issue.fields,com.atlassian.jira.issue.f
 ields.config,com.atlassian.jira.issue.fields.config.manager,com.atlas
 sian.jira.issue.fields.layout.field,com.atlassian.jira.issue.fields.s
 creen.issuetype,com.atlassian.jira.issue.index,com.atlassian.jira.iss
 ue.search,com.atlassian.jira.issue.security,com.atlassian.jira.jql.pa
 rser,com.atlassian.jira.notification,com.atlassian.jira.permission,co
 m.atlassian.jira.portal,com.atlassian.jira.project,com.atlassian.jira
 .project.version,com.atlassian.jira.security,com.atlassian.jira.secur
 ity.roles,com.atlassian.jira.security.websudo,com.atlassian.jira.shar
 ing,com.atlassian.jira.user,com.atlassian.jira.user.util,com.atlassia
 n.jira.util,com.atlassian.jira.workflow,com.atlassian.query,com.atlas
 sian.sal.api,com.atlassian.sal.api.features,com.atlassian.sal.api.use
 r,com.google.common.cache,com.onresolve.jira.behaviours,com.onresolve
 .jira.groovy.jql,com.onresolve.jira.spring,com.onresolve.scriptrunner
 .canned,com.onresolve.scriptrunner.canned.common,com.onresolve.script
 runner.canned.docs,com.onresolve.scriptrunner.canned.jira.admin.model
 ,com.onresolve.scriptrunner.canned.jira.admin.workflowManagement,com.
 onresolve.scriptrunner.canned.jira.fields.model,com.onresolve.scriptr
 unner.canned.jira.service,com.onresolve.scriptrunner.canned.jira.util
 s.agile,com.onresolve.scriptrunner.canned.tags,com.onresolve.scriptru
 nner.canned.util,com.onresolve.scriptrunner.fields,com.onresolve.scri
 ptrunner.jira.workflow,com.onresolve.scriptrunner.runner,com.onresolv
 e.scriptrunner.runner.customisers,com.onresolve.scriptrunner.schedule
 d,com.onresolve.scriptrunner.settings,com.onresolve.scriptrunner.swit
 chuser,com.onresolve.spring,com.opensymphony.workflow.loader,groovy.l
 ang,groovy.transform,javax.inject,javax.servlet.http,org.apache.log4j
 ,org.codehaus.groovy.runtime",com.onresolve.scriptrunner.canned.jira.
 admin.model;uses:="com.onresolve.scriptrunner.canned,com.onresolve.sc
 riptrunner.canned.jira.workflow.model,com.onresolve.scriptrunner.sche
 duled.model,groovy.lang,groovy.transform",com.onresolve.scriptrunner.
 canned.jira.admin.workflowManagement;uses:="com.atlassian.jira.workfl
 ow,com.onresolve.scriptrunner.canned.jira.utils,com.onresolve.scriptr
 unner.jira.workflow,com.onresolve.scriptrunner.jira.workflow.model,co
 m.opensymphony.workflow.loader,groovy.lang,groovy.transform,javax.inj
 ect,org.apache.log4j,org.codehaus.groovy.runtime",com.onresolve.scrip
 trunner.canned.jira.events.remote;uses:="com.atlassian.event.api,com.
 atlassian.plugin.hostcontainer,com.atlassian.plugin.spring.scanner.an
 notation.export,com.atlassian.plugins.rest.common.json,com.atlassian.
 sal.api.lifecycle,com.atlassian.sal.api.user,com.atlassian.webhooks.s
 pi,com.onresolve.scriptrunner.canned,com.onresolve.scriptrunner.event
 s.remote,com.onresolve.scriptrunner.jobs,com.onresolve.scriptrunner.r
 unner,groovy.lang,groovy.transform,javax.inject,org.apache.log4j,org.
 codehaus.groovy.runtime,org.springframework.core.type,org.springframe
 work.core.type.filter",com.onresolve.scriptrunner.canned.jira.fields;
 uses:="com.atlassian.applinks.api,com.atlassian.jira.bc.issue.propert
 ies,com.atlassian.jira.config,com.atlassian.jira.config.properties,co
 m.atlassian.jira.issue,com.atlassian.jira.issue.changehistory,com.atl
 assian.jira.issue.fields,com.atlassian.jira.issue.fields.config.manag
 er,com.atlassian.jira.issue.fields.layout.field,com.atlassian.jira.is
 sue.history,com.atlassian.jira.issue.link,com.atlassian.plugin,com.on
 resolve.scriptrunner.canned,com.onresolve.scriptrunner.canned.docs,co
 m.onresolve.scriptrunner.canned.jira.fields.editable,com.onresolve.sc
 riptrunner.canned.jira.fields.model,com.onresolve.scriptrunner.canned
 .jira.utils,com.onresolve.scriptrunner.canned.tags,com.onresolve.scri
 ptrunner.canned.util,com.onresolve.scriptrunner.events.remote,com.onr
 esolve.scriptrunner.fields,com.onresolve.scriptrunner.runner.customis
 ers,groovy.lang,groovy.transform,org.apache.log4j,org.codehaus.groovy
 .runtime",com.onresolve.scriptrunner.canned.jira.fields.editable;uses
 :="com.atlassian.jira.admin,com.atlassian.jira.bc.issue,com.atlassian
 .jira.issue,com.atlassian.jira.issue.context,com.atlassian.jira.issue
 .customfields,com.atlassian.jira.issue.customfields.impl,com.atlassia
 n.jira.issue.customfields.manager,com.atlassian.jira.issue.customfiel
 ds.option,com.atlassian.jira.issue.customfields.persistence,com.atlas
 sian.jira.issue.customfields.searchers.transformer,com.atlassian.jira
 .issue.customfields.vdi,com.atlassian.jira.issue.customfields.view,co
 m.atlassian.jira.issue.export,com.atlassian.jira.issue.export.customf
 ield,com.atlassian.jira.issue.fields,com.atlassian.jira.issue.fields.
 config,com.atlassian.jira.issue.fields.config.manager,com.atlassian.j
 ira.issue.fields.layout.field,com.atlassian.jira.issue.fields.rest,co
 m.atlassian.jira.issue.fields.rest.json,com.atlassian.jira.issue.fiel
 ds.screen,com.atlassian.jira.issue.fields.util,com.atlassian.jira.iss
 ue.issuetype,com.atlassian.jira.issue.search,com.atlassian.jira.issue
 .transport,com.atlassian.jira.issue.util,com.atlassian.jira.jql.opera
 nd,com.atlassian.jira.project,com.atlassian.jira.security,com.atlassi
 an.jira.user,com.atlassian.jira.util,com.atlassian.jira.web.action.is
 sue,com.atlassian.jira.web.bean,com.atlassian.plugin.hostcontainer,co
 m.atlassian.plugin.module,com.atlassian.plugin.spring.scanner.annotat
 ion.export,com.atlassian.query,com.atlassian.query.clause,com.atlassi
 an.query.operator,com.onresolve.jira.issue.customfields,com.onresolve
 .scriptrunner.analytics.tracking,com.onresolve.scriptrunner.canned,co
 m.onresolve.scriptrunner.canned.jira.fields.editable.picker,com.onres
 olve.scriptrunner.canned.jira.fields.model,com.onresolve.scriptrunner
 .canned.jira.utils,com.onresolve.scriptrunner.fields,com.onresolve.sc
 riptrunner.runner,com.onresolve.scriptrunner.runner.diag,com.opensymp
 hony.module.propertyset,com.opensymphony.workflow.loader,groovy.lang,
 groovy.transform,javax.annotation,javax.inject,org.apache.log4j,org.a
 pache.lucene.search,org.codehaus.groovy.runtime,org.ofbiz.core.entity
 ,org.springframework.beans.factory.config,webwork.action",com.onresol
 ve.scriptrunner.canned.jira.fields.editable.custom;uses:="com.atlassi
 an.jira.issue,com.atlassian.jira.issue.customfields.view,com.atlassia
 n.jira.issue.fields,com.atlassian.jira.issue.fields.config,com.atlass
 ian.jira.issue.fields.layout.field,com.atlassian.jira.util,com.onreso
 lve.scriptrunner.canned.jira.fields.editable.picker,com.onresolve.scr
 iptrunner.canned.jira.fields.model,com.onresolve.scriptrunner.runner,
 com.onresolve.scriptrunner.runner.rest.jira,com.onresolve.scriptrunne
 r.runner.stc,com.onresolve.scriptrunner.stc.typecheck,groovy.lang,gro
 ovy.transform,javax.annotation,javax.inject,org.apache.log4j,org.code
 haus.groovy.runtime",com.onresolve.scriptrunner.canned.jira.fields.ed
 itable.database;uses:="com.atlassian.jira.imports.project.customfield
 ,com.atlassian.jira.issue,com.atlassian.jira.issue.context,com.atlass
 ian.jira.issue.customfields.impl,com.atlassian.jira.issue.customfield
 s.impl.rest,com.atlassian.jira.issue.customfields.manager,com.atlassi
 an.jira.issue.customfields.persistence,com.atlassian.jira.issue.custo
 mfields.view,com.atlassian.jira.issue.export,com.atlassian.jira.issue
 .export.customfield,com.atlassian.jira.issue.fields,com.atlassian.jir
 a.issue.fields.config,com.atlassian.jira.issue.fields.config.manager,
 com.atlassian.jira.issue.fields.layout.field,com.atlassian.jira.issue
 .fields.rest,com.atlassian.jira.issue.fields.rest.json,com.atlassian.
 jira.issue.index.indexers.impl,com.atlassian.jira.util,com.atlassian.
 jira.web,com.onresolve.jira.issue.customfields,com.onresolve.scriptru
 nner.canned,com.onresolve.scriptrunner.canned.docs,com.onresolve.scri
 ptrunner.canned.jira.fields,com.onresolve.scriptrunner.canned.jira.fi
 elds.editable,com.onresolve.scriptrunner.canned.jira.fields.editable.
 picker,com.onresolve.scriptrunner.canned.jira.fields.editable.search,
 com.onresolve.scriptrunner.canned.jira.fields.model,com.onresolve.scr
 iptrunner.canned.tags,com.onresolve.scriptrunner.canned.util,com.onre
 solve.scriptrunner.db,com.onresolve.scriptrunner.fields,com.onresolve
 .scriptrunner.runner,com.onresolve.scriptrunner.runner.rest.jira,com.
 onresolve.scriptrunner.runner.stc,groovy.lang,groovy.transform,javax.
 annotation,javax.inject,org.apache.log4j,org.apache.lucene.document,o
 rg.codehaus.groovy.runtime,org.springframework.beans.factory.config",
 com.onresolve.scriptrunner.canned.jira.fields.editable.issue;uses:="c
 om.atlassian.jira.bc.issue,com.atlassian.jira.bc.issue.search,com.atl
 assian.jira.imports.project.customfield,com.atlassian.jira.imports.pr
 oject.mapper,com.atlassian.jira.issue,com.atlassian.jira.issue.contex
 t,com.atlassian.jira.issue.customfields,com.atlassian.jira.issue.cust
 omfields.impl,com.atlassian.jira.issue.customfields.impl.rest,com.atl
 assian.jira.issue.customfields.manager,com.atlassian.jira.issue.custo
 mfields.persistence,com.atlassian.jira.issue.customfields.searchers,c
 om.atlassian.jira.issue.customfields.searchers.transformer,com.atlass
 ian.jira.issue.customfields.statistics,com.atlassian.jira.issue.custo
 mfields.view,com.atlassian.jira.issue.export,com.atlassian.jira.issue
 .export.customfield,com.atlassian.jira.issue.fields,com.atlassian.jir
 a.issue.fields.config,com.atlassian.jira.issue.fields.config.manager,
 com.atlassian.jira.issue.fields.layout.field,com.atlassian.jira.issue
 .fields.rest,com.atlassian.jira.issue.fields.rest.json,com.atlassian.
 jira.issue.fields.rest.json.beans,com.atlassian.jira.issue.link,com.a
 tlassian.jira.issue.search,com.atlassian.jira.issue.search.searchers.
 information,com.atlassian.jira.issue.search.searchers.transformer,com
 .atlassian.jira.issue.statistics,com.atlassian.jira.issue.util,com.at
 lassian.jira.jql.builder,com.atlassian.jira.jql.operand,com.atlassian
 .jira.jql.parser,com.atlassian.jira.jql.util,com.atlassian.jira.jql.v
 alidator,com.atlassian.jira.plugin.customfield,com.atlassian.jira.sec
 urity,com.atlassian.jira.template,com.atlassian.jira.user,com.atlassi
 an.jira.util,com.atlassian.jira.web,com.atlassian.query,com.atlassian
 .query.clause,com.atlassian.query.operator,com.atlassian.sal.api,com.
 onresolve.jira.issue.customfields,com.onresolve.jira.issue.index,com.
 onresolve.scriptrunner.canned,com.onresolve.scriptrunner.canned.jira.
 fields.editable,com.onresolve.scriptrunner.canned.jira.fields.editabl
 e.issuepicker,com.onresolve.scriptrunner.canned.jira.fields.editable.
 picker,com.onresolve.scriptrunner.canned.jira.fields.editable.search,
 com.onresolve.scriptrunner.canned.jira.fields.model,com.onresolve.scr
 iptrunner.fields,com.onresolve.scriptrunner.runner,com.onresolve.scri
 ptrunner.runner.rest.jira,com.onresolve.scriptrunner.runner.stc,groov
 y.lang,groovy.transform,javax.annotation,javax.inject,org.apache.log4
 j,org.codehaus.groovy.runtime,org.springframework.beans.factory.confi
 g",com.onresolve.scriptrunner.canned.jira.fields.editable.issuepicker
 ;uses:="com.atlassian.jira.bc.issue.search,com.atlassian.jira.config,
 com.atlassian.jira.issue,com.atlassian.jira.issue.search,com.atlassia
 n.jira.jql.parser,com.atlassian.jira.project,com.atlassian.jira.secur
 ity,com.atlassian.jira.user,groovy.lang,groovy.transform,groovy.trans
 form.builder,javax.inject,org.apache.lucene.analysis,org.apache.lucen
 e.search,org.codehaus.groovy.runtime",com.onresolve.scriptrunner.cann
 ed.jira.fields.editable.ldap;uses:="com.atlassian.jira.issue,com.atla
 ssian.jira.issue.customfields.view,com.atlassian.jira.issue.fields,co
 m.atlassian.jira.issue.fields.config,com.atlassian.jira.issue.fields.
 layout.field,com.atlassian.jira.util,com.onresolve.scriptrunner.canne
 d,com.onresolve.scriptrunner.canned.docs,com.onresolve.scriptrunner.c
 anned.jira.fields,com.onresolve.scriptrunner.canned.jira.fields.edita
 ble,com.onresolve.scriptrunner.canned.jira.fields.editable.picker,com
 .onresolve.scriptrunner.canned.jira.fields.model,com.onresolve.script
 runner.canned.tags,com.onresolve.scriptrunner.canned.util,com.onresol
 ve.scriptrunner.ldap,com.onresolve.scriptrunner.runner,com.onresolve.
 scriptrunner.runner.rest.jira,com.onresolve.scriptrunner.runner.stc,g
 roovy.lang,groovy.transform,javax.inject,org.apache.log4j,org.codehau
 s.groovy.runtime",com.onresolve.scriptrunner.canned.jira.fields.edita
 ble.picker;uses:="com.atlassian.jira.issue,com.atlassian.jira.issue.c
 ustomfields.view,com.atlassian.jira.issue.fields,com.atlassian.jira.i
 ssue.fields.config,com.atlassian.jira.issue.fields.layout.field,com.a
 tlassian.jira.security,com.atlassian.jira.util,com.onresolve.scriptru
 nner.canned.jira.fields.model,com.onresolve.scriptrunner.runner,com.o
 nresolve.scriptrunner.runner.rest.jira,com.onresolve.scriptrunner.run
 ner.stc,groovy.lang,groovy.transform,javax.annotation,javax.inject,or
 g.codehaus.groovy.runtime",com.onresolve.scriptrunner.canned.jira.fie
 lds.editable.remoteissue;uses:="com.atlassian.applinks.api,com.atlass
 ian.jira.bc.issue.search,com.atlassian.jira.imports.project.customfie
 ld,com.atlassian.jira.issue,com.atlassian.jira.issue.customfields,com
 .atlassian.jira.issue.customfields.impl,com.atlassian.jira.issue.cust
 omfields.manager,com.atlassian.jira.issue.customfields.persistence,co
 m.atlassian.jira.issue.customfields.searchers,com.atlassian.jira.issu
 e.customfields.searchers.transformer,com.atlassian.jira.issue.customf
 ields.view,com.atlassian.jira.issue.fields,com.atlassian.jira.issue.f
 ields.config,com.atlassian.jira.issue.fields.config.manager,com.atlas
 sian.jira.issue.fields.layout.field,com.atlassian.jira.issue.index.in
 dexers.impl,com.atlassian.jira.issue.search,com.atlassian.jira.issue.
 search.searchers.information,com.atlassian.jira.issue.search.searcher
 s.renderer,com.atlassian.jira.issue.search.searchers.transformer,com.
 atlassian.jira.issue.transport,com.atlassian.jira.jql.operand,com.atl
 assian.jira.jql.parser,com.atlassian.jira.jql.util,com.atlassian.jira
 .jql.validator,com.atlassian.jira.plugin.customfield,com.atlassian.ji
 ra.user,com.atlassian.jira.util,com.atlassian.jira.web,com.atlassian.
 query,com.atlassian.query.clause,com.atlassian.query.operator,com.atl
 assian.sal.api.net,com.onresolve.jira.issue.customfields,com.onresolv
 e.jira.issue.index,com.onresolve.scriptrunner.canned,com.onresolve.sc
 riptrunner.canned.jira.fields.editable,com.onresolve.scriptrunner.can
 ned.jira.fields.model,com.onresolve.scriptrunner.fields,groovy.lang,g
 roovy.transform,javax.annotation,javax.inject,org.apache.log4j,org.ap
 ache.lucene.document,org.codehaus.groovy.runtime,org.codehaus.jackson
 .annotate,org.springframework.beans.factory.config",com.onresolve.scr
 iptrunner.canned.jira.fields.editable.search;uses:="com.atlassian.jir
 a.issue,com.atlassian.jira.issue.customfields,com.atlassian.jira.issu
 e.customfields.searchers,com.atlassian.jira.issue.customfields.search
 ers.renderer,com.atlassian.jira.issue.customfields.searchers.transfor
 mer,com.atlassian.jira.issue.customfields.statistics,com.atlassian.ji
 ra.issue.fields,com.atlassian.jira.issue.search,com.atlassian.jira.is
 sue.search.searchers.information,com.atlassian.jira.issue.search.sear
 chers.renderer,com.atlassian.jira.issue.search.searchers.transformer,
 com.atlassian.jira.issue.search.util,com.atlassian.jira.issue.statist
 ics,com.atlassian.jira.issue.transport,com.atlassian.jira.jql.builder
 ,com.atlassian.jira.jql.operand,com.atlassian.jira.jql.query,com.atla
 ssian.jira.jql.util,com.atlassian.jira.jql.validator,com.atlassian.ji
 ra.plugin.customfield,com.atlassian.jira.security,com.atlassian.jira.
 user,com.atlassian.jira.util,com.atlassian.jira.web,com.atlassian.que
 ry.clause,com.atlassian.query.operator,com.onresolve.jira.issue.index
 ,groovy.lang,groovy.transform,javax.annotation,org.apache.lucene.sear
 ch,webwork.action",com.onresolve.scriptrunner.canned.jira.fields.mode
 l;uses:="com.onresolve.scriptrunner.canned,com.onresolve.scriptrunner
 .canned.jira.fields.editable.picker,com.onresolve.scriptrunner.model,
 com.onresolve.scriptrunner.model.jackson,com.onresolve.scriptrunner.r
 unner.rest.jira.model,groovy.lang,groovy.transform,javax.annotation",
 com.onresolve.scriptrunner.canned.jira.fragments;uses:="com.atlassian
 .plugin,com.atlassian.sal.api,com.onresolve.scriptrunner.canned,com.o
 nresolve.scriptrunner.canned.docs,com.onresolve.scriptrunner.canned.j
 ira.fragments.model,com.onresolve.scriptrunner.canned.tags,com.onreso
 lve.scriptrunner.fragments,com.onresolve.scriptrunner.fragments.model
 ,com.onresolve.scriptrunner.runner,com.onresolve.scriptrunner.runner.
 customisers,com.onresolve.scriptrunner.runner.stc,groovy.lang,groovy.
 transform,org.codehaus.groovy.runtime",com.onresolve.scriptrunner.can
 ned.jira.fragments.model;uses:="com.onresolve.scriptrunner.fragments.
 model,com.onresolve.scriptrunner.model,com.onresolve.scriptrunner.mod
 el.jackson,groovy.lang,groovy.transform",com.onresolve.scriptrunner.c
 anned.jira.jobs;uses:="com.atlassian.jira.issue,com.atlassian.jira.pr
 oject.archiving,com.atlassian.jira.security,com.atlassian.jira.user.u
 til,com.atlassian.sal.api.transaction,com.onresolve.jira.groovy.jql,c
 om.onresolve.scriptrunner.canned,com.onresolve.scriptrunner.canned.do
 cs,com.onresolve.scriptrunner.canned.jira.admin.model,com.onresolve.s
 criptrunner.canned.jira.utils,com.onresolve.scriptrunner.canned.tags,
 com.onresolve.scriptrunner.canned.util,com.onresolve.scriptrunner.job
 s,com.onresolve.scriptrunner.runner,com.onresolve.scriptrunner.runner
 .customisers,com.onresolve.scriptrunner.runner.diag,com.onresolve.scr
 iptrunner.scheduled.model,groovy.lang,groovy.transform,org.apache.log
 4j,org.codehaus.groovy.runtime",com.onresolve.scriptrunner.canned.jir
 a.listener;uses:="com.atlassian.jira.workflow,com.onresolve.scriptrun
 ner.canned,com.onresolve.scriptrunner.mail,com.onresolve.scriptrunner
 .model,com.onresolve.scriptrunner.model.jackson,com.onresolve.scriptr
 unner.persistence,com.onresolve.scriptrunner.runner,com.onresolve.scr
 iptrunner.runner.rest.common,groovy.lang,groovy.transform,javax.injec
 t,org.codehaus.groovy.runtime",com.onresolve.scriptrunner.canned.jira
 .service;uses:="com.atlassian.jira.bc.issue,com.atlassian.jira.issue,
 com.atlassian.jira.issue.customfields.manager,com.atlassian.jira.issu
 e.fields,com.atlassian.jira.issue.link,com.atlassian.jira.security,co
 m.atlassian.jira.user.util,com.onresolve.scriptrunner.canned.jira.uti
 ls,com.onresolve.scriptrunner.canned.jira.workflow.model,com.onresolv
 e.scriptrunner.fields,groovy.lang,groovy.transform,javax.inject,org.a
 pache.log4j,org.codehaus.groovy.runtime",com.onresolve.scriptrunner.c
 anned.jira.utils;uses:="com.atlassian.crowd.embedded.api,com.atlassia
 n.event.api,com.atlassian.jira.bc.filter,com.atlassian.jira.bc.issue,
 com.atlassian.jira.bc.project.component,com.atlassian.jira.config,com
 .atlassian.jira.issue,com.atlassian.jira.issue.fields,com.atlassian.j
 ira.issue.fields.config.manager,com.atlassian.jira.issue.fields.scree
 n,com.atlassian.jira.issue.issuetype,com.atlassian.jira.issue.link,co
 m.atlassian.jira.issue.resolution,com.atlassian.jira.issue.search,com
 .atlassian.jira.issue.security,com.atlassian.jira.project,com.atlassi
 an.jira.project.version,com.atlassian.jira.security,com.atlassian.jir
 a.security.roles,com.atlassian.jira.user,com.atlassian.jira.user.util
 ,com.atlassian.jira.workflow,com.atlassian.plugin,com.atlassian.plugi
 n.schema.descriptor,com.atlassian.sal.api,com.onresolve.scriptrunner.
 canned,com.onresolve.scriptrunner.canned.jira.workflow,com.onresolve.
 scriptrunner.canned.jira.workflow.model,com.onresolve.scriptrunner.ca
 nned.util,com.onresolve.scriptrunner.jira.workflow,com.onresolve.scri
 ptrunner.jira.workflow.model,com.onresolve.scriptrunner.model,com.onr
 esolve.scriptrunner.runner,com.onresolve.scriptrunner.runner.stc,com.
 onresolve.scriptrunner.workflow,com.opensymphony.workflow,com.opensym
 phony.workflow.loader,com.opensymphony.workflow.spi,groovy.lang,groov
 y.transform,javax.inject,javax.script,org.apache.log4j,org.codehaus.g
 roovy.runtime",com.onresolve.scriptrunner.canned.jira.utils.agile;use
 s:="com.atlassian.greenhopper.manager.rapidview,com.atlassian.greenho
 pper.service.rapid,com.atlassian.greenhopper.service.sprint,com.atlas
 sian.jira.issue,com.atlassian.jira.security,com.atlassian.jira.user,c
 om.atlassian.jira.user.util,com.onresolve.scriptrunner.canned,com.onr
 esolve.scriptrunner.canned.docs,com.onresolve.scriptrunner.canned.jir
 a.workflow,com.onresolve.scriptrunner.canned.jira.workflow.model,com.
 onresolve.scriptrunner.canned.tags,com.onresolve.scriptrunner.canned.
 util,com.onresolve.scriptrunner.runner,com.onresolve.scriptrunner.run
 ner.events,com.onresolve.scriptrunner.runner.stc,com.onresolve.script
 runner.workflow,com.onresolve.spring,groovy.lang,groovy.transform,jav
 ax.inject,org.apache.log4j,org.codehaus.groovy.runtime",com.onresolve
 .scriptrunner.canned.jira.utils.plugins;uses:="com.atlassian.greenhop
 per.model.rapid,com.atlassian.greenhopper.model.validation,com.atlass
 ian.greenhopper.service,com.atlassian.greenhopper.service.rapid,com.a
 tlassian.greenhopper.service.rapid.view,com.atlassian.greenhopper.web
 .rapid.view,com.atlassian.jira.project,com.atlassian.jira.user,com.at
 lassian.query,groovy.lang,groovy.transform,org.apache.log4j,org.codeh
 aus.groovy.runtime",com.onresolve.scriptrunner.canned.jira.utils.serv
 icedesk;uses:="com.atlassian.application.api,com.atlassian.fugue,com.
 atlassian.jira.config,com.atlassian.jira.issue,com.atlassian.jira.iss
 ue.issuetype,com.atlassian.jira.plugins.workinghours.api.calendar,com
 .atlassian.jira.plugins.workinghours.api.calendar.builder,com.atlassi
 an.jira.plugins.workinghours.api.calendar.util,com.atlassian.jira.pro
 ject,com.atlassian.jira.user,com.atlassian.jira.util.json,com.atlassi
 an.plugin,com.atlassian.plugin.spring.scanner.annotation,com.atlassia
 n.servicedesk.api,com.atlassian.servicedesk.api.comment,com.atlassian
 .servicedesk.api.customer,com.atlassian.servicedesk.api.organization,
 com.atlassian.servicedesk.api.permission,com.atlassian.servicedesk.ap
 i.portal,com.atlassian.servicedesk.api.requesttype,com.atlassian.serv
 icedesk.api.user,com.atlassian.servicedesk.api.util.paging,com.atlass
 ian.servicedesk.internal.api.requesttype,com.atlassian.servicedesk.in
 ternal.api.requesttype.group,com.atlassian.servicedesk.internal.featu
 re.customer.request.requesttype.field,com.onresolve.osgi,groovy.lang,
 groovy.transform,io.atlassian.fugue,javax.annotation,javax.inject,org
 .apache.log4j,org.codehaus.groovy.runtime,org.springframework.beans,o
 rg.springframework.beans.factory,org.springframework.context",com.onr
 esolve.scriptrunner.canned.jira.workflow;uses:="com.atlassian.jira.is
 sue,com.atlassian.jira.issue.fields,com.atlassian.jira.issue.watchers
 ,com.atlassian.jira.user,com.atlassian.jira.user.util,com.onresolve.s
 criptrunner.canned,com.onresolve.scriptrunner.canned.jira.service,com
 .onresolve.scriptrunner.canned.jira.utils,com.onresolve.scriptrunner.
 canned.jira.workflow.model,com.onresolve.scriptrunner.canned.tags,com
 .onresolve.scriptrunner.canned.util,com.onresolve.scriptrunner.fields
 ,com.onresolve.scriptrunner.runner,com.onresolve.scriptrunner.runner.
 stc,com.onresolve.scriptrunner.workflow,com.opensymphony.workflow.loa
 der,groovy.lang,groovy.transform,org.apache.log4j,org.codehaus.groovy
 .runtime",com.onresolve.scriptrunner.canned.jira.workflow.conditions;
 uses:="com.atlassian.jira.bc.issue.search,com.atlassian.jira.config,c
 om.atlassian.jira.issue,com.atlassian.jira.issue.changehistory,com.at
 lassian.jira.issue.fields,com.atlassian.jira.issue.history,com.atlass
 ian.jira.issue.watchers,com.atlassian.jira.jql.parser,com.atlassian.j
 ira.security,com.atlassian.jira.security.roles,com.atlassian.jira.use
 r.util,com.atlassian.sal.api.user,com.onresolve.scriptrunner.canned,c
 om.onresolve.scriptrunner.canned.docs,com.onresolve.scriptrunner.cann
 ed.jira.service,com.onresolve.scriptrunner.canned.jira.utils,com.onre
 solve.scriptrunner.canned.jira.workflow,com.onresolve.scriptrunner.ca
 nned.jira.workflow.conditions.model,com.onresolve.scriptrunner.canned
 .jira.workflow.model,com.onresolve.scriptrunner.canned.tags,com.onres
 olve.scriptrunner.canned.util,com.onresolve.scriptrunner.fields,com.o
 nresolve.scriptrunner.runner,com.onresolve.scriptrunner.runner.custom
 isers,com.onresolve.scriptrunner.runner.stc,com.onresolve.scriptrunne
 r.workflow,groovy.lang,groovy.transform,javax.inject,org.apache.log4j
 ,org.codehaus.groovy.runtime",com.onresolve.scriptrunner.canned.jira.
 workflow.conditions.model;uses:="com.onresolve.scriptrunner.canned.ji
 ra.workflow.model,groovy.lang,groovy.transform",com.onresolve.scriptr
 unner.canned.jira.workflow.listeners;uses:="com.atlassian.jira.bc.pro
 ject.version,com.atlassian.jira.config.properties,com.atlassian.jira.
 event.project,com.atlassian.jira.mail,com.atlassian.jira.project,com.
 atlassian.jira.project.version,com.atlassian.jira.security,com.atlass
 ian.jira.security.groups,com.atlassian.mail,com.atlassian.mail.server
 ,com.atlassian.sal.api.features,com.onresolve.scriptrunner.canned,com
 .onresolve.scriptrunner.canned.docs,com.onresolve.scriptrunner.canned
 .jira.utils,com.onresolve.scriptrunner.canned.jira.workflow,com.onres
 olve.scriptrunner.canned.jira.workflow.listeners.model,com.onresolve.
 scriptrunner.canned.jira.workflow.model,com.onresolve.scriptrunner.ca
 nned.jira.workflow.postfunctions.mail,com.onresolve.scriptrunner.cann
 ed.tags,com.onresolve.scriptrunner.canned.util,com.onresolve.scriptru
 nner.listener,com.onresolve.scriptrunner.runner,com.onresolve.scriptr
 unner.runner.customisers,com.onresolve.scriptrunner.runner.stc,com.on
 resolve.scriptrunner.workflow,groovy.lang,groovy.transform,javax.inje
 ct,org.apache.log4j,org.codehaus.groovy.runtime",com.onresolve.script
 runner.canned.jira.workflow.listeners.model;uses:="com.onresolve.scri
 ptrunner.canned.jira.workflow.model,com.onresolve.scriptrunner.model,
 com.onresolve.scriptrunner.model.jackson,groovy.lang,groovy.transform
 ",com.onresolve.scriptrunner.canned.jira.workflow.model;uses:="com.at
 lassian.jira.workflow,com.onresolve.scriptrunner.canned,com.onresolve
 .scriptrunner.canned.jira.utils.agile,com.onresolve.scriptrunner.mode
 l,com.onresolve.scriptrunner.model.jackson,groovy.lang,groovy.transfo
 rm,org.codehaus.groovy.runtime",com.onresolve.scriptrunner.canned.jir
 a.workflow.module;uses:="com.atlassian.jira.plugin.workflow,com.atlas
 sian.jira.security,com.atlassian.plugin.module,groovy.lang,groovy.tra
 nsform,org.springframework.beans.factory.annotation",com.onresolve.sc
 riptrunner.canned.jira.workflow.postfunctions;uses:="com.atlassian.ji
 ra.bc.issue.comment,com.atlassian.jira.config,com.atlassian.jira.conf
 ig.properties,com.atlassian.jira.event.issue,com.atlassian.jira.event
 .type,com.atlassian.jira.issue,com.atlassian.jira.issue.changehistory
 ,com.atlassian.jira.issue.comments,com.atlassian.jira.issue.fields,co
 m.atlassian.jira.issue.history,com.atlassian.jira.issue.issuetype,com
 .atlassian.jira.issue.link,com.atlassian.jira.issue.security,com.atla
 ssian.jira.issue.watchers,com.atlassian.jira.mail,com.atlassian.jira.
 project,com.atlassian.jira.project.archiving,com.atlassian.jira.secur
 ity,com.atlassian.jira.security.groups,com.atlassian.jira.security.ro
 les,com.atlassian.jira.user,com.atlassian.jira.user.util,com.atlassia
 n.mail,com.atlassian.mail.server,com.atlassian.plugin,com.onresolve.s
 criptrunner.canned,com.onresolve.scriptrunner.canned.docs,com.onresol
 ve.scriptrunner.canned.jira.service,com.onresolve.scriptrunner.canned
 .jira.utils,com.onresolve.scriptrunner.canned.jira.workflow,com.onres
 olve.scriptrunner.canned.jira.workflow.model,com.onresolve.scriptrunn
 er.canned.jira.workflow.postfunctions.mail,com.onresolve.scriptrunner
 .canned.jira.workflow.postfunctions.model,com.onresolve.scriptrunner.
 canned.tags,com.onresolve.scriptrunner.canned.util,com.onresolve.scri
 ptrunner.runner,com.onresolve.scriptrunner.runner.customisers,com.onr
 esolve.scriptrunner.runner.stc,com.onresolve.scriptrunner.slack,com.o
 nresolve.scriptrunner.workflow,com.opensymphony.workflow.loader,groov
 y.lang,groovy.transform,org.apache.log4j,org.codehaus.groovy.runtime,
 org.ofbiz.core.entity",com.onresolve.scriptrunner.canned.jira.workflo
 w.postfunctions.mail;uses:="com.atlassian.jira.config.properties,com.
 atlassian.jira.issue,com.atlassian.jira.issue.attachment,com.atlassia
 n.jira.issue.fields.layout.column,com.atlassian.jira.mail,com.atlassi
 an.jira.ofbiz,com.atlassian.jira.security.groups,com.atlassian.jira.t
 emplate,com.atlassian.jira.user,com.atlassian.jira.web.component,com.
 atlassian.mail,com.atlassian.mail.queue,com.atlassian.mail.server,com
 .google.common.base,com.onresolve.scriptrunner.canned,com.onresolve.s
 criptrunner.canned.jira.utils,com.onresolve.scriptrunner.canned.jira.
 workflow,com.onresolve.scriptrunner.canned.jira.workflow.model,com.on
 resolve.scriptrunner.canned.util,com.onresolve.scriptrunner.mail,com.
 onresolve.scriptrunner.model,com.onresolve.scriptrunner.model.jackson
 ,com.onresolve.scriptrunner.workflow,com.opensymphony.module.property
 set,groovy.lang,groovy.transform,org.apache.log4j,org.codehaus.groovy
 .runtime,org.ofbiz.core.entity",com.onresolve.scriptrunner.canned.jir
 a.workflow.postfunctions.model;uses:="com.onresolve.scriptrunner.cann
 ed.jira.workflow.model,com.onresolve.scriptrunner.model,com.onresolve
 .scriptrunner.model.jackson,groovy.lang,groovy.transform",com.onresol
 ve.scriptrunner.canned.jira.workflow.validators;uses:="com.atlassian.
 jira.issue.fields,com.atlassian.jira.issue.watchers,com.atlassian.jir
 a.user.util,com.onresolve.scriptrunner.canned,com.onresolve.scriptrun
 ner.canned.docs,com.onresolve.scriptrunner.canned.jira.service,com.on
 resolve.scriptrunner.canned.jira.utils,com.onresolve.scriptrunner.can
 ned.jira.workflow,com.onresolve.scriptrunner.canned.jira.workflow.mod
 el,com.onresolve.scriptrunner.canned.tags,com.onresolve.scriptrunner.
 canned.util,com.onresolve.scriptrunner.fields,com.onresolve.scriptrun
 ner.runner,com.onresolve.scriptrunner.runner.customisers,com.onresolv
 e.scriptrunner.runner.stc,com.onresolve.scriptrunner.workflow,groovy.
 lang,groovy.transform,javax.inject,org.codehaus.groovy.runtime",com.o
 nresolve.scriptrunner.canned.ldap;uses:="com.onresolve.scriptrunner.c
 anned,com.onresolve.scriptrunner.canned.docs,com.onresolve.scriptrunn
 er.canned.resources,com.onresolve.scriptrunner.canned.tags,com.onreso
 lve.scriptrunner.canned.util,com.onresolve.scriptrunner.ldap,com.onre
 solve.scriptrunner.ldap.model,com.onresolve.scriptrunner.runner,groov
 y.lang,groovy.transform,javax.inject,org.apache.log4j,org.codehaus.gr
 oovy.runtime",com.onresolve.scriptrunner.canned.resources;uses:="com.
 onresolve.scriptrunner.canned,com.onresolve.scriptrunner.canned.util,
 com.onresolve.scriptrunner.db.configurations,groovy.lang,groovy.trans
 form,org.codehaus.groovy.runtime",com.onresolve.scriptrunner.canned.s
 lack;uses:="com.onresolve.scriptrunner.canned,com.onresolve.scriptrun
 ner.canned.docs,com.onresolve.scriptrunner.canned.resources,com.onres
 olve.scriptrunner.canned.tags,com.onresolve.scriptrunner.canned.util,
 com.onresolve.scriptrunner.db.configurations,com.onresolve.scriptrunn
 er.slack,groovy.lang,groovy.transform,javax.inject",com.onresolve.scr
 iptrunner.canned.tags;uses:="com.onresolve.scriptrunner.runner",com.o
 nresolve.scriptrunner.canned.testrunner;uses:="com.atlassian.sal.api.
 pluginsettings,com.onresolve.scriptrunner.runner,groovy.lang,groovy.t
 ransform,javax.inject,org.codehaus.groovy.runtime",com.onresolve.scri
 ptrunner.canned.testutils;uses:="com.atlassian.jira.util.thread,groov
 y.lang,groovy.transform,javax.inject,org.apache.log4j",com.onresolve.
 scriptrunner.canned.util;uses:="com.onresolve.scriptrunner.runner.dia
 g,com.onresolve.scriptrunner.runner.rest.common.error.model,groovy.la
 ng,groovy.transform,groovy.xml,org.apache.log4j,org.codehaus.groovy.r
 untime",com.onresolve.scriptrunner.canned.util.serializers;uses:="com
 .atlassian.jira.workflow,groovy.lang,groovy.transform",com.onresolve.
 scriptrunner.canned.validators;uses:="com.atlassian.jira.bc.filter,co
 m.atlassian.jira.bc.issue.search,com.atlassian.jira.config,com.atlass
 ian.jira.issue,com.atlassian.jira.issue.fields,com.atlassian.jira.iss
 ue.link,com.atlassian.jira.issue.search,com.atlassian.jira.issue.sear
 ch.searchers.util,com.atlassian.jira.jql.operand,com.atlassian.jira.j
 ql.parser,com.atlassian.jira.jql.query,com.atlassian.jira.jql.resolve
 r,com.atlassian.jira.project,com.atlassian.jira.security,com.atlassia
 n.jira.security.roles,com.atlassian.jira.user,com.atlassian.jira.util
 ,com.atlassian.jira.workflow,com.atlassian.query,com.atlassian.query.
 clause,com.atlassian.sal.api.user,com.onresolve.scriptrunner.canned,c
 om.onresolve.scriptrunner.fields,com.onresolve.scriptrunner.scheduled
 ,com.onresolve.scriptrunner.scheduled.model,groovy.lang,groovy.transf
 orm,javax.inject,org.codehaus.groovy.runtime",com.onresolve.scriptrun
 ner.cloudmigration;uses:="com.atlassian.plugin.spring.scanner.annotat
 ion.export,com.onresolve.scriptrunner.cloudmigration.export,groovy.la
 ng,groovy.transform,javax.inject,org.apache.log4j",com.onresolve.scri
 ptrunner.cloudmigration.export;uses:="com.atlassian.jira.event.type,c
 om.atlassian.jira.issue,com.atlassian.jira.project,com.atlassian.jira
 .workflow,com.atlassian.upm.api.license,com.onresolve.scriptrunner.ca
 nned,com.onresolve.scriptrunner.canned.jira.listener,com.onresolve.sc
 riptrunner.cloudmigration.mapping,com.onresolve.scriptrunner.cloudmig
 ration.model,com.onresolve.scriptrunner.cloudmigration.workflow,com.o
 nresolve.scriptrunner.fields,com.onresolve.scriptrunner.runner,com.on
 resolve.scriptrunner.scheduled,com.onresolve.scriptrunner.scheduled.m
 odel,groovy.lang,groovy.transform,javax.inject,org.apache.log4j,org.c
 odehaus.groovy.runtime",com.onresolve.scriptrunner.cloudmigration.map
 ping;uses:="com.atlassian.sal.api.user,groovy.lang,groovy.transform",
 com.onresolve.scriptrunner.cloudmigration.model;uses:="com.atlassian.
 jira.workflow,com.atlassian.scheduler.config,com.onresolve.scriptrunn
 er.canned.jira.fields.model,groovy.lang,groovy.transform",com.onresol
 ve.scriptrunner.cloudmigration.workflow;uses:="com.atlassian.jira.wor
 kflow,com.onresolve.scriptrunner.model,com.onresolve.scriptrunner.run
 ner,groovy.lang,groovy.transform,javax.inject",com.onresolve.scriptru
 nner.cluster;uses:="com.atlassian.jira.cluster,com.atlassian.plugin.s
 pring.scanner.annotation.component,com.onresolve.scriptrunner.fragmen
 ts,groovy.lang,groovy.transform,javax.annotation,javax.inject",com.on
 resolve.scriptrunner.concurrent.util;uses:="groovy.lang,groovy.transf
 orm,groovy.transform.stc",com.onresolve.scriptrunner.config;uses:="gr
 oovy.lang,groovy.transform,javax.inject",com.onresolve.scriptrunner.c
 ustomfield;uses:="com.atlassian.jira.config.properties,com.atlassian.
 jira.issue,com.atlassian.jira.issue.customfields,com.atlassian.jira.i
 ssue.customfields.converters,com.atlassian.jira.issue.customfields.im
 pl,com.atlassian.jira.issue.fields,com.atlassian.jira.issue.fields.co
 nfig,com.atlassian.jira.issue.fields.layout.field,com.atlassian.jira.
 issue.fields.rest,com.atlassian.jira.issue.fields.rest.json,com.atlas
 sian.jira.issue.fields.rest.json.beans,com.atlassian.jira.issue.index
 .indexers,com.atlassian.jira.notification.type,com.atlassian.jira.sec
 urity,com.atlassian.jira.web,com.google.common.cache,com.onresolve.sc
 riptrunner.canned.jira.fields.editable,com.onresolve.scriptrunner.can
 ned.jira.fields.editable.issue,com.onresolve.scriptrunner.canned.jira
 .fields.model,com.onresolve.scriptrunner.fields,com.onresolve.scriptr
 unner.runner,com.onresolve.scriptrunner.runner.diag,com.onresolve.scr
 iptrunner.runner.stc,groovy.lang,groovy.transform,javax.annotation,or
 g.apache.log4j,org.codehaus.groovy.runtime",com.onresolve.scriptrunne
 r.db;uses:="com.atlassian.activeobjects.spi,com.atlassian.event.api,c
 om.atlassian.plugin.spring.scanner.annotation.export,com.atlassian.sa
 l.api.lifecycle,com.onresolve.scriptrunner.analytics.tracking,com.onr
 esolve.scriptrunner.cluster,com.onresolve.scriptrunner.db.configurati
 ons,com.onresolve.scriptrunner.fragments,com.onresolve.scriptrunner.r
 esources,com.onresolve.scriptrunner.runner,groovy.lang,groovy.transfo
 rm,groovy.transform.stc,javax.annotation,javax.inject,javax.sql,org.a
 pache.log4j,org.codehaus.groovy.runtime",com.onresolve.scriptrunner.d
 b.configurations;uses:="com.onresolve.scriptrunner.canned,groovy.lang
 ,groovy.transform,org.apache.log4j,org.codehaus.groovy.runtime",com.o
 nresolve.scriptrunner.events.remote;uses:="com.atlassian.event.api,co
 m.atlassian.sal.api.lifecycle,com.atlassian.sal.api.user,com.onresolv
 e.scriptrunner.canned,com.onresolve.scriptrunner.runner,groovy.lang,g
 roovy.transform,org.codehaus.groovy.runtime",com.onresolve.scriptrunn
 er.exception;uses:="com.onresolve.scriptrunner.canned.util,groovy.lan
 g,groovy.transform",com.onresolve.scriptrunner.features;uses:="com.on
 resolve.scriptrunner.runner,groovy.lang,groovy.transform,javax.inject
 ,org.codehaus.groovy.runtime",com.onresolve.scriptrunner.features.res
 t;uses:="com.onresolve.scriptrunner.features,groovy.lang,groovy.trans
 form,javax.ws.rs,javax.ws.rs.core,org.codehaus.groovy.runtime",com.on
 resolve.scriptrunner.fields;uses:="com.atlassian.cache,com.atlassian.
 crowd.embedded.api,com.atlassian.event.api,com.atlassian.jira.event.i
 ssue.field,com.atlassian.jira.issue,com.atlassian.jira.issue.fields,c
 om.atlassian.jira.issue.fields.config.manager,com.atlassian.jira.user
 ,com.atlassian.jira.user.util,com.onresolve.scriptrunner,com.onresolv
 e.scriptrunner.audit,com.onresolve.scriptrunner.canned,com.onresolve.
 scriptrunner.canned.common.rest.model,com.onresolve.scriptrunner.cann
 ed.jira.fields.model,com.onresolve.scriptrunner.persistence,com.onres
 olve.scriptrunner.runner,com.onresolve.scriptrunner.runner.rest,com.o
 nresolve.scriptrunner.storageregistry,groovy.lang,groovy.transform,ja
 vax.annotation,javax.inject,org.codehaus.groovy.runtime",com.onresolv
 e.scriptrunner.fields.stc;uses:="com.atlassian.crowd.embedded.api,com
 .atlassian.jira.project,com.atlassian.jira.project.version,com.atlass
 ian.jira.user,com.onresolve.scriptrunner.runner,groovy.lang,groovy.tr
 ansform",com.onresolve.scriptrunner.fields.upgrade;uses:="com.atlassi
 an.jira.issue,com.atlassian.jira.issue.fields.config.manager,com.atla
 ssian.jira.ofbiz,com.atlassian.plugin.spring.scanner.annotation.expor
 t,com.atlassian.sal.api.message,com.atlassian.sal.api.upgrade,com.onr
 esolve.scriptrunner.canned,com.onresolve.scriptrunner.canned.jira.fie
 lds.model,com.onresolve.scriptrunner.fields,com.onresolve.scriptrunne
 r.persistence,com.onresolve.scriptrunner.upgrade,groovy.lang,groovy.t
 ransform,javax.inject,org.apache.log4j,org.codehaus.groovy.runtime,or
 g.ofbiz.core.entity",com.onresolve.scriptrunner.filters;uses:="com.at
 lassian.plugins.rest.common.security.jersey,com.atlassian.sal.api.use
 r,com.onresolve.scriptrunner.canned,com.onresolve.scriptrunner.runner
 ,com.onresolve.scriptrunner.runner.rest.common.permissions,com.onreso
 lve.scriptrunner.settings,com.sun.jersey.spi.container,groovy.lang,gr
 oovy.transform,javax.ws.rs.core,javax.ws.rs.ext",com.onresolve.script
 runner.fragments;uses:="com.atlassian.event.api,com.atlassian.json.ma
 rshal,com.atlassian.plugin,com.atlassian.plugin.event,com.atlassian.p
 lugin.event.events,com.atlassian.plugin.module,com.atlassian.plugin.s
 chema.descriptor,com.atlassian.plugin.spring.scanner.annotation.compo
 nent,com.atlassian.plugin.spring.scanner.annotation.export,com.atlass
 ian.plugin.web,com.atlassian.plugin.web.api,com.atlassian.plugin.web.
 api.provider,com.atlassian.plugin.web.conditions,com.atlassian.plugin
 .web.descriptors,com.atlassian.plugin.web.model,com.atlassian.sal.api
 ,com.atlassian.sal.api.lifecycle,com.atlassian.sal.api.user,com.atlas
 sian.webresource.api.data,com.onresolve.licensing,com.onresolve.scrip
 trunner,com.onresolve.scriptrunner.analytics.tracking,com.onresolve.s
 criptrunner.audit,com.onresolve.scriptrunner.canned,com.onresolve.scr
 iptrunner.canned.common.rest.model,com.onresolve.scriptrunner.canned.
 docs,com.onresolve.scriptrunner.canned.util,com.onresolve.scriptrunne
 r.cluster,com.onresolve.scriptrunner.fragments.model,com.onresolve.sc
 riptrunner.persistence,com.onresolve.scriptrunner.runner,com.onresolv
 e.scriptrunner.runner.customisers,com.onresolve.scriptrunner.runner.r
 est,com.onresolve.scriptrunner.runner.stc,com.onresolve.scriptrunner.
 storageregistry,com.onresolve.scriptrunner.switchuser,groovy.lang,gro
 ovy.transform,javax.annotation,javax.inject,org.apache.log4j,org.code
 haus.groovy.runtime,org.dom4j",com.onresolve.scriptrunner.fragments.l
 ocator;uses:="com.atlassian.plugin.web.descriptors,com.atlassian.plug
 in.web.model,groovy.lang,groovy.transform",com.onresolve.scriptrunner
 .fragments.model;uses:="com.onresolve.scriptrunner.canned,com.onresol
 ve.scriptrunner.fragments,com.onresolve.scriptrunner.model,com.onreso
 lve.scriptrunner.model.jackson,groovy.lang,groovy.transform",com.onre
 solve.scriptrunner.fragments.samples;uses:="groovy.lang,groovy.transf
 orm,org.codehaus.groovy.runtime",com.onresolve.scriptrunner.fragments
 .upgrade;uses:="com.atlassian.plugin.spring.scanner.annotation.export
 ,com.atlassian.sal.api.message,com.atlassian.sal.api.upgrade,com.onre
 solve.scriptrunner.canned,com.onresolve.scriptrunner.fragments,com.on
 resolve.scriptrunner.persistence,com.onresolve.scriptrunner.runner,co
 m.onresolve.scriptrunner.upgrade,groovy.lang,groovy.transform,javax.i
 nject,org.apache.log4j,org.codehaus.groovy.runtime",com.onresolve.scr
 iptrunner.fragments.xml;uses:="com.onresolve.scriptrunner.model,groov
 y.lang,groovy.transform,groovy.xml,org.codehaus.groovy.runtime",com.o
 nresolve.scriptrunner.items,com.onresolve.scriptrunner.jira;uses:="co
 m.atlassian.plugin.spring.scanner.annotation",com.onresolve.scriptrun
 ner.jira.workflow;uses:="com.atlassian.annotations,com.atlassian.cach
 e,com.atlassian.event.api,com.atlassian.jira.event,com.atlassian.jira
 .event.workflow,com.atlassian.jira.issue,com.atlassian.jira.plugin.wo
 rkflow,com.atlassian.jira.project,com.atlassian.jira.security,com.atl
 assian.jira.workflow,com.atlassian.plugin,com.atlassian.plugin.spring
 .scanner.annotation.export,com.atlassian.plugin.webresource,com.atlas
 sian.sal.api,com.atlassian.sal.api.lifecycle,com.atlassian.sal.api.us
 er,com.google.common.base,com.onresolve.jira.spring,com.onresolve.lic
 ensing,com.onresolve.scriptrunner.canned,com.onresolve.scriptrunner.c
 anned.jira.admin,com.onresolve.scriptrunner.canned.jira.utils,com.onr
 esolve.scriptrunner.jira.workflow.model,com.onresolve.scriptrunner.ru
 nner,com.onresolve.scriptrunner.runner.diag,com.onresolve.scriptrunne
 r.runner.rest,com.onresolve.scriptrunner.runner.rest.jira.model,com.o
 pensymphony.workflow.loader,groovy.lang,groovy.transform,javax.annota
 tion,javax.inject,org.apache.log4j,org.codehaus.groovy.runtime",com.o
 nresolve.scriptrunner.jira.workflow.model;uses:="com.onresolve.script
 runner.runner.rest.jira.model,groovy.lang,groovy.transform",com.onres
 olve.scriptrunner.jira.workflow.util;uses:="com.onresolve.scriptrunne
 r.jira.workflow.model,groovy.lang,groovy.transform,org.codehaus.groov
 y.runtime",com.onresolve.scriptrunner.jobs;uses:="com.atlassian.jira.
 security,com.atlassian.jira.user.util,com.atlassian.plugin.spring.sca
 nner.annotation.component,com.atlassian.sal.api.transaction,com.atlas
 sian.scheduler,com.atlassian.scheduler.status,com.onresolve.scriptrun
 ner.canned,com.onresolve.scriptrunner.canned.docs,com.onresolve.scrip
 trunner.canned.util,com.onresolve.scriptrunner.runner,com.onresolve.s
 criptrunner.runner.customisers,com.onresolve.scriptrunner.runner.diag
 ,com.onresolve.scriptrunner.runner.stc,com.onresolve.scriptrunner.sch
 eduled.model,groovy.lang,groovy.transform,javax.annotation,javax.inje
 ct,org.apache.log4j,org.codehaus.groovy.runtime",com.onresolve.script
 runner.ldap;uses:="com.atlassian.cache,com.atlassian.plugin,com.onres
 olve.scriptrunner.analytics.tracking,com.onresolve.scriptrunner.ldap.
 model,com.onresolve.scriptrunner.resources,groovy.lang,groovy.transfo
 rm,javax.inject,org.codehaus.groovy.runtime",com.onresolve.scriptrunn
 er.ldap.model;uses:="com.onresolve.scriptrunner.db.configurations,gro
 ovy.lang,groovy.transform",com.onresolve.scriptrunner.licensing;uses:
 ="com.atlassian.upm.api.license,groovy.lang,groovy.transform,javax.in
 ject,org.codehaus.groovy.runtime",com.onresolve.scriptrunner.listener
 ;uses:="com.atlassian.plugin,com.atlassian.sal.api.features,com.googl
 e.common.reflect,com.onresolve.scriptrunner.canned,com.onresolve.scri
 ptrunner.canned.docs,com.onresolve.scriptrunner.canned.tags,com.onres
 olve.scriptrunner.canned.util,com.onresolve.scriptrunner.runner,com.o
 nresolve.scriptrunner.runner.customisers,com.onresolve.scriptrunner.r
 unner.events,groovy.lang,groovy.transform,javax.inject,org.apache.log
 4j,org.codehaus.groovy.runtime",com.onresolve.scriptrunner.mail;uses:
 ="com.atlassian.configurable,com.atlassian.jira.application,com.atlas
 sian.jira.bc.user,com.atlassian.jira.config.properties,com.atlassian.
 jira.issue,com.atlassian.jira.issue.comments,com.atlassian.jira.issue
 .fields,com.atlassian.jira.plugins.mail.handlers,com.atlassian.jira.p
 lugins.mail.webwork,com.atlassian.jira.security,com.atlassian.jira.se
 rvice,com.atlassian.jira.service.util.handler,com.atlassian.mail.serv
 er,com.atlassian.plugin,com.onresolve.licensing,com.onresolve.scriptr
 unner.audit,com.onresolve.scriptrunner.canned.jira.utils,com.onresolv
 e.scriptrunner.canned.util,com.onresolve.scriptrunner.runner,com.onre
 solve.scriptrunner.runner.stc,com.onresolve.scriptrunner.settings,gro
 ovy.lang,groovy.transform,javax.mail,org.codehaus.groovy.runtime",com
 .onresolve.scriptrunner.merge;uses:="groovy.lang,groovy.transform",co
 m.onresolve.scriptrunner.model;uses:="com.onresolve.scriptrunner.cann
 ed,com.onresolve.scriptrunner.model.validation,groovy.lang,groovy.tra
 nsform",com.onresolve.scriptrunner.model.jackson;uses:="com.onresolve
 .scriptrunner.model,groovy.lang,groovy.transform,org.codehaus.groovy.
 runtime",com.onresolve.scriptrunner.model.legacy,com.onresolve.script
 runner.model.validation;uses:="com.onresolve.scriptrunner.listener,co
 m.onresolve.scriptrunner.model,com.onresolve.scriptrunner.runner,groo
 vy.lang,groovy.transform,javax.inject,org.codehaus.groovy.runtime",co
 m.onresolve.scriptrunner.onboarding.example;uses:="com.atlassian.plug
 in.spring.scanner.annotation.component,com.onresolve.scriptrunner.can
 ned,groovy.lang,groovy.transform,javax.inject,org.codehaus.groovy.run
 time",com.onresolve.scriptrunner.parameters;uses:="com.onresolve.scri
 ptrunner.canned,com.onresolve.scriptrunner.parameters.converter,com.o
 nresolve.scriptrunner.parameters.metadata,com.onresolve.scriptrunner.
 parameters.validation,com.onresolve.scriptrunner.runner,groovy.lang,g
 roovy.transform,javax.inject,org.codehaus.groovy.ast,org.codehaus.gro
 ovy.ast.expr,org.codehaus.groovy.classgen,org.codehaus.groovy.control
 ,org.codehaus.groovy.control.customizers,org.codehaus.groovy.runtime,
 org.springframework.beans.factory",com.onresolve.scriptrunner.paramet
 ers.annotation;uses:="com.atlassian.crowd.embedded.api,com.atlassian.
 jira.bc.project.component,com.atlassian.jira.issue.fields,com.atlassi
 an.jira.issue.issuetype,com.atlassian.jira.issue.link,com.atlassian.j
 ira.issue.priority,com.atlassian.jira.issue.resolution,com.atlassian.
 jira.issue.search,com.atlassian.jira.issue.status,com.atlassian.jira.
 project,com.atlassian.jira.project.version,com.atlassian.jira.securit
 y.roles,com.atlassian.jira.user,com.onresolve.scriptrunner.parameters
 ,com.onresolve.scriptrunner.parameters.annotation.meta,com.onresolve.
 scriptrunner.parameters.converter,com.onresolve.scriptrunner.paramete
 rs.metadata,com.onresolve.scriptrunner.parameters.validation",com.onr
 esolve.scriptrunner.parameters.annotation.meta,com.onresolve.scriptru
 nner.parameters.converter;uses:="com.atlassian.crowd.embedded.api,com
 .atlassian.jira.bc.project.component,com.atlassian.jira.config,com.at
 lassian.jira.issue,com.atlassian.jira.issue.fields,com.atlassian.jira
 .issue.issuetype,com.atlassian.jira.issue.link,com.atlassian.jira.iss
 ue.priority,com.atlassian.jira.issue.resolution,com.atlassian.jira.is
 sue.search,com.atlassian.jira.issue.status,com.atlassian.jira.project
 ,com.atlassian.jira.project.version,com.atlassian.jira.security.roles
 ,com.atlassian.jira.user,com.atlassian.jira.user.util,groovy.lang,gro
 ovy.transform,javax.inject",com.onresolve.scriptrunner.parameters.met
 adata;uses:="groovy.lang,groovy.transform,org.codehaus.groovy.ast,org
 .codehaus.groovy.ast.expr,org.codehaus.groovy.runtime",com.onresolve.
 scriptrunner.parameters.validation;uses:="groovy.lang,groovy.transfor
 m,org.codehaus.groovy.ast,org.codehaus.groovy.runtime",com.onresolve.
 scriptrunner.permissions;uses:="com.atlassian.plugin,com.atlassian.pl
 ugin.web,com.atlassian.sal.api.user,com.onresolve.scriptrunner.settin
 gs,groovy.lang,groovy.transform",com.onresolve.scriptrunner.persisten
 ce;uses:="com.onresolve.scriptrunner.canned,com.onresolve.scriptrunne
 r.runner,com.onresolve.scriptrunner.runner.rest.common,groovy.lang,gr
 oovy.transform,org.codehaus.groovy.runtime",com.onresolve.scriptrunne
 r.projectconfigurator;uses:="com.awnaba.projectconfigurator.extension
 points.common,com.awnaba.projectconfigurator.extensionpoints.customen
 tities,com.awnaba.projectconfigurator.operationsapi.importtree,groovy
 .lang,groovy.transform,org.codehaus.groovy.runtime",com.onresolve.scr
 iptrunner.projectconfigurator.module.behaviours;uses:="com.atlassian.
 jira.project,com.atlassian.plugin.spring.scanner.annotation,com.atlas
 sian.servicedesk.api,com.awnaba.projectconfigurator.extensionpoints.c
 ommon,com.awnaba.projectconfigurator.extensionpoints.customentities,c
 om.awnaba.projectconfigurator.extensionpoints.customentities.referenc
 es,com.awnaba.projectconfigurator.extensionpoints.extensionservices,c
 om.onresolve.jira.behaviours,com.onresolve.jira.behaviours.types,com.
 onresolve.scriptrunner.canned,com.onresolve.scriptrunner.projectconfi
 gurator.module.model,com.onresolve.scriptrunner.projectconfigurator.m
 odule.property,groovy.lang,groovy.transform,javax.annotation,javax.in
 ject,org.apache.commons.lang3.tuple,org.codehaus.groovy.runtime,org.s
 pringframework.beans,org.springframework.beans.factory,org.springfram
 ework.context",com.onresolve.scriptrunner.projectconfigurator.module.
 fragments;uses:="com.atlassian.plugin.spring.scanner.annotation,com.a
 wnaba.projectconfigurator.extensionpoints.common,com.awnaba.projectco
 nfigurator.extensionpoints.customentities,com.awnaba.projectconfigura
 tor.extensionpoints.customentities.references,com.awnaba.projectconfi
 gurator.extensionpoints.extensionservices,com.onresolve.scriptrunner.
 canned.common.fragments.model,com.onresolve.scriptrunner.canned.jira.
 fragments.model,com.onresolve.scriptrunner.fragments,com.onresolve.sc
 riptrunner.fragments.model,com.onresolve.scriptrunner.projectconfigur
 ator.module.property,groovy.lang,groovy.transform,javax.inject,org.co
 dehaus.groovy.runtime",com.onresolve.scriptrunner.projectconfigurator
 .module.jobs;uses:="com.atlassian.jira.workflow,com.atlassian.plugin.
 spring.scanner.annotation,com.awnaba.projectconfigurator.extensionpoi
 nts.customentities,com.awnaba.projectconfigurator.extensionpoints.cus
 tomentities.references,com.awnaba.projectconfigurator.extensionpoints
 .extensionservices,com.onresolve.scriptrunner.canned,com.onresolve.sc
 riptrunner.canned.jira.admin.model,com.onresolve.scriptrunner.project
 configurator.module.property,com.onresolve.scriptrunner.scheduled,com
 .onresolve.scriptrunner.scheduled.model,groovy.lang,groovy.transform,
 javax.inject,org.codehaus.groovy.runtime",com.onresolve.scriptrunner.
 projectconfigurator.module.listener;uses:="com.atlassian.jira.issue,c
 om.atlassian.jira.project,com.atlassian.plugin.spring.scanner.annotat
 ion,com.awnaba.projectconfigurator.extensionpoints.common,com.awnaba.
 projectconfigurator.extensionpoints.customentities,com.awnaba.project
 configurator.extensionpoints.customentities.references,com.awnaba.pro
 jectconfigurator.extensionpoints.extensionservices,com.awnaba.project
 configurator.operationsapi.importtree,com.onresolve.scriptrunner.cann
 ed.jira.listener,com.onresolve.scriptrunner.mail,com.onresolve.script
 runner.model,com.onresolve.scriptrunner.projectconfigurator.module.pr
 operty,com.onresolve.scriptrunner.runner.events,groovy.lang,groovy.tr
 ansform,javax.inject,org.codehaus.groovy.runtime",com.onresolve.scrip
 trunner.projectconfigurator.module.model;uses:="com.atlassian.plugin.
 spring.scanner.annotation,com.onresolve.jira.behaviours,com.onresolve
 .jira.behaviours.types,com.onresolve.scriptrunner.projectconfigurator
 .module.behaviours,groovy.lang,groovy.transform,javax.inject,org.code
 haus.groovy.runtime",com.onresolve.scriptrunner.projectconfigurator.m
 odule.property;uses:="com.atlassian.jira.project,com.atlassian.plugin
 .spring.scanner.annotation,com.awnaba.projectconfigurator.extensionpo
 ints.common,com.awnaba.projectconfigurator.extensionpoints.customenti
 ties,com.awnaba.projectconfigurator.extensionpoints.customentities.re
 ferences,com.awnaba.projectconfigurator.extensionpoints.extensionserv
 ices,com.awnaba.projectconfigurator.operationsapi.importtree,com.onre
 solve.scriptrunner.canned,com.onresolve.scriptrunner.canned.jira.list
 ener,com.onresolve.scriptrunner.model,groovy.lang,groovy.transform,ja
 vax.inject,org.codehaus.groovy.runtime",com.onresolve.scriptrunner.pr
 ojectconfigurator.module.resources;uses:="com.atlassian.plugin.spring
 .scanner.annotation,com.awnaba.projectconfigurator.extensionpoints.co
 mmon,com.awnaba.projectconfigurator.extensionpoints.customentities,co
 m.awnaba.projectconfigurator.extensionpoints.extensionservices,com.aw
 naba.projectconfigurator.operationsapi.importtree,com.onresolve.scrip
 trunner.db,com.onresolve.scriptrunner.db.configurations,com.onresolve
 .scriptrunner.ldap,com.onresolve.scriptrunner.ldap.model,com.onresolv
 e.scriptrunner.projectconfigurator.module.property,com.onresolve.scri
 ptrunner.resources,groovy.lang,groovy.transform,javax.inject,org.code
 haus.groovy.runtime",com.onresolve.scriptrunner.projectconfigurator.m
 odule.restendpoints;uses:="com.atlassian.plugin.spring.scanner.annota
 tion,com.awnaba.projectconfigurator.extensionpoints.customentities,co
 m.onresolve.scriptrunner.canned.common.rest,com.onresolve.scriptrunne
 r.projectconfigurator.module.property,com.onresolve.scriptrunner.runn
 er,groovy.lang,groovy.transform,javax.inject,org.codehaus.groovy.runt
 ime",com.onresolve.scriptrunner.projectconfigurator.module.scriptedfi
 elds;uses:="com.atlassian.jira.issue.fields.config.manager,com.atlass
 ian.plugin.spring.scanner.annotation,com.awnaba.projectconfigurator.e
 xtensionpoints.customentities,com.onresolve.scriptrunner.canned.jira.
 fields.model,com.onresolve.scriptrunner.fields,com.onresolve.scriptru
 nner.projectconfigurator.module.property,com.onresolve.scriptrunner.p
 rojectconfigurator.module.resources,com.onresolve.scriptrunner.projec
 tconfigurator.module.scriptfields,groovy.lang,groovy.transform,javax.
 inject,org.codehaus.groovy.runtime",com.onresolve.scriptrunner.projec
 tconfigurator.module.scriptfields;uses:="com.atlassian.jira.issue.fie
 lds.config,com.atlassian.jira.issue.fields.config.manager,com.atlassi
 an.plugin.spring.scanner.annotation,com.awnaba.projectconfigurator.ex
 tensionpoints.common,com.awnaba.projectconfigurator.extensionpoints.c
 ustomentities,com.awnaba.projectconfigurator.extensionpoints.customen
 tities.references,com.awnaba.projectconfigurator.extensionpoints.exte
 nsionservices,com.awnaba.projectconfigurator.operationsapi.importtree
 ,com.onresolve.scriptrunner.canned.jira.fields.model,com.onresolve.sc
 riptrunner.fields,com.onresolve.scriptrunner.projectconfigurator.modu
 le.property,com.onresolve.scriptrunner.projectconfigurator.module.res
 ources,groovy.lang,groovy.transform,javax.inject,org.codehaus.groovy.
 runtime",com.onresolve.scriptrunner.querydsl;uses:="com.atlassian.act
 iveobjects.external,com.atlassian.activeobjects.spi,com.atlassian.plu
 gin.spring.scanner.annotation.component,javax.inject",com.onresolve.s
 criptrunner.remote;uses:="com.atlassian.applinks.api,com.atlassian.sa
 l.api.net,groovy.lang,groovy.transform,org.apache.log4j,org.codehaus.
 groovy.runtime",com.onresolve.scriptrunner.resources;uses:="com.onres
 olve.scriptrunner.canned,com.onresolve.scriptrunner.db,com.onresolve.
 scriptrunner.db.configurations,com.onresolve.scriptrunner.ldap,com.on
 resolve.scriptrunner.ldap.model,com.onresolve.scriptrunner.persistenc
 e,com.onresolve.scriptrunner.storageregistry,groovy.lang,groovy.trans
 form,javax.inject,org.codehaus.groovy.runtime",com.onresolve.scriptru
 nner.runner;uses:="com.atlassian.cache,com.atlassian.event.api,com.at
 lassian.event.spi,com.atlassian.jira,com.atlassian.jira.bc.issue.sear
 ch,com.atlassian.jira.cluster,com.atlassian.jira.config.managedconfig
 uration,com.atlassian.jira.config.util,com.atlassian.jira.event,com.a
 tlassian.jira.event.issue,com.atlassian.jira.event.type,com.atlassian
 .jira.instrumentation,com.atlassian.jira.issue,com.atlassian.jira.jql
 .operand,com.atlassian.jira.jql.parser,com.atlassian.jira.project,com
 .atlassian.jira.user,com.atlassian.plugin,com.atlassian.plugin.event,
 com.atlassian.plugin.event.events,com.atlassian.plugin.module,com.atl
 assian.plugin.spring.scanner.annotation.export,com.atlassian.plugin.w
 ebresource,com.atlassian.query,com.atlassian.sal.api,com.atlassian.sa
 l.api.auth,com.atlassian.sal.api.lifecycle,com.atlassian.sal.api.user
 ,com.atlassian.sal.api.usersettings,com.atlassian.sal.api.websudo,com
 .atlassian.soy.renderer,com.atlassian.util.concurrent,com.google.comm
 on.base,com.onresolve.jira.groovy.jql,com.onresolve.licensing,com.onr
 esolve.scriptrunner.audit,com.onresolve.scriptrunner.beans,com.onreso
 lve.scriptrunner.canned,com.onresolve.scriptrunner.canned.common.rest
 ,com.onresolve.scriptrunner.canned.docs,com.onresolve.scriptrunner.ca
 nned.util,com.onresolve.scriptrunner.cluster,com.onresolve.scriptrunn
 er.concurrent.util,com.onresolve.scriptrunner.fields,com.onresolve.sc
 riptrunner.fragments,com.onresolve.scriptrunner.listener,com.onresolv
 e.scriptrunner.model,com.onresolve.scriptrunner.persistence,com.onres
 olve.scriptrunner.runner.classloading,com.onresolve.scriptrunner.runn
 er.diag,com.onresolve.scriptrunner.runner.event,com.onresolve.scriptr
 unner.runner.events,com.onresolve.scriptrunner.runner.field,com.onres
 olve.scriptrunner.runner.file,com.onresolve.scriptrunner.runner.scrip
 tPlugins,com.onresolve.scriptrunner.runner.util,com.onresolve.scriptr
 unner.storageregistry,groovy.lang,groovy.transform,groovy.util,javax.
 annotation,javax.inject,javax.script,javax.servlet,javax.servlet.http
 ,org.apache.log4j,org.apache.lucene.search,org.codehaus.groovy.contro
 l,org.codehaus.groovy.runtime,org.osgi.framework,org.springframework.
 beans.factory",com.onresolve.scriptrunner.runner.classloading;uses:="
 com.atlassian.cache,com.atlassian.plugin,com.atlassian.plugin.spring.
 scanner.annotation.component,com.onresolve.scriptrunner.runner,com.on
 resolve.scriptrunner.runner.customisers,groovy.lang,groovy.transform,
 javax.annotation,javax.inject,org.apache.log4j,org.codehaus.groovy.co
 ntrol,org.codehaus.groovy.runtime,org.springframework.context.annotat
 ion",com.onresolve.scriptrunner.runner.customisers;uses:="com.atlassi
 an.jira.issue,groovy.lang,groovy.transform,org.codehaus.groovy.ast,or
 g.codehaus.groovy.classgen,org.codehaus.groovy.control,org.codehaus.g
 roovy.control.customizers,org.codehaus.groovy.runtime.m12n",com.onres
 olve.scriptrunner.runner.customisers.ast;uses:="com.onresolve.scriptr
 unner.runner.customisers,groovy.lang,groovy.transform,org.apache.log4
 j,org.codehaus.groovy.ast,org.codehaus.groovy.ast.expr,org.codehaus.g
 roovy.control,org.codehaus.groovy.runtime",com.onresolve.scriptrunner
 .runner.diag;uses:="com.atlassian.activeobjects.external,com.atlassia
 n.activeobjects.tx,com.atlassian.event.api,com.atlassian.instrumentat
 ion.operations,com.atlassian.plugin.spring.scanner.annotation.compone
 nt,com.atlassian.plugin.spring.scanner.annotation.export,com.atlassia
 n.sal.api.transaction,com.atlassian.sal.api.user,com.google.common.ca
 che,com.onresolve.scriptrunner.analytics.tracking,com.onresolve.scrip
 trunner.runner,com.onresolve.scriptrunner.runner.diag.rrd,com.onresol
 ve.scriptrunner.runner.events,groovy.lang,groovy.transform,javax.anno
 tation,javax.inject,net.java.ao,net.java.ao.schema,org.apache.log4j,o
 rg.apache.log4j.spi,org.codehaus.groovy.runtime",com.onresolve.script
 runner.runner.diag.java;uses:="org.apache.log4j,org.apache.log4j.spi"
 ,com.onresolve.scriptrunner.runner.diag.rrd;uses:="com.atlassian.plug
 in.spring.scanner.annotation.component,com.atlassian.plugin.spring.sc
 anner.annotation.export,com.onresolve.scriptrunner.fields,com.onresol
 ve.scriptrunner.jira.workflow,com.onresolve.scriptrunner.runner,com.o
 nresolve.scriptrunner.runner.diag,com.onresolve.scriptrunner.schedule
 d,groovy.lang,groovy.transform,javax.annotation,javax.inject,org.apac
 he.log4j,org.codehaus.groovy.runtime",com.onresolve.scriptrunner.runn
 er.event;uses:="com.atlassian.event.api,com.atlassian.plugin.event.ev
 ents,com.atlassian.plugin.spring.scanner.annotation.component,com.onr
 esolve.scriptrunner.runner.util,groovy.lang,groovy.transform,javax.in
 ject,org.apache.log4j,org.codehaus.groovy.runtime",com.onresolve.scri
 ptrunner.runner.events;uses:="com.atlassian.event.api,com.atlassian.e
 vent.spi,com.atlassian.plugin,com.atlassian.plugin.event,com.atlassia
 n.plugin.event.events,com.atlassian.plugin.spring.scanner.annotation.
 component,com.atlassian.plugin.spring.scanner.annotation.export,com.a
 tlassian.util.concurrent,com.onresolve.scriptrunner.runner.stc,groovy
 .lang,groovy.transform,javax.annotation,javax.inject,javax.ws.rs.core
 ,org.apache.log4j,org.codehaus.groovy.runtime,org.springframework.cor
 e.annotation",com.onresolve.scriptrunner.runner.field;uses:="com.atla
 ssian.jira.admin,com.atlassian.jira.config,com.atlassian.jira.excepti
 on,com.atlassian.jira.issue,com.atlassian.jira.issue.context,com.atla
 ssian.jira.issue.customfields,com.atlassian.jira.issue.customfields.i
 mpl,com.atlassian.jira.issue.customfields.option,com.atlassian.jira.i
 ssue.customfields.view,com.atlassian.jira.issue.fields,com.atlassian.
 jira.issue.fields.config,com.atlassian.jira.issue.fields.config.manag
 er,com.atlassian.jira.issue.fields.layout.field,com.atlassian.jira.is
 sue.fields.renderer,com.atlassian.jira.issue.fields.rest,com.atlassia
 n.jira.issue.fields.rest.json,com.atlassian.jira.issue.fields.screen,
 com.atlassian.jira.issue.fields.util,com.atlassian.jira.issue.issuety
 pe,com.atlassian.jira.issue.search,com.atlassian.jira.issue.util,com.
 atlassian.jira.jql.context,com.atlassian.jira.model.querydsl,com.atla
 ssian.jira.ofbiz,com.atlassian.jira.plugin.customfield,com.atlassian.
 jira.project,com.atlassian.jira.security,com.atlassian.jira.util,com.
 atlassian.jira.web.action.admin.translation,com.atlassian.jira.web.be
 an,com.onresolve.scriptrunner.canned,com.onresolve.scriptrunner.canne
 d.jira.fields.editable,com.opensymphony.module.propertyset,groovy.lan
 g,groovy.transform,javax.inject,org.apache.lucene.search,org.ofbiz.co
 re.entity,webwork.action",com.onresolve.scriptrunner.runner.file;uses
 :="com.onresolve.scriptrunner.runner.diag,groovy.lang,groovy.transfor
 m,javax.inject,javax.ws.rs.core,org.apache.log4j,org.codehaus.groovy.
 runtime",com.onresolve.scriptrunner.runner.rest;uses:="com.atlassian.
 plugins.rest.common.security.jersey,com.onresolve.scriptrunner,com.on
 resolve.scriptrunner.audit,com.onresolve.scriptrunner.canned,com.onre
 solve.scriptrunner.canned.common.rest.model,com.onresolve.scriptrunne
 r.canned.docs,com.onresolve.scriptrunner.canned.util,com.onresolve.sc
 riptrunner.runner,com.sun.jersey.spi.container,groovy.lang,groovy.tra
 nsform,javax.annotation,javax.inject,javax.servlet.http,javax.ws.rs,j
 avax.ws.rs.core,org.apache.log4j,org.codehaus.groovy.runtime",com.onr
 esolve.scriptrunner.runner.rest.common;uses:="com.atlassian.plugin.sp
 ring.scanner.annotation.component,com.atlassian.plugins.rest.common.s
 ecurity,com.atlassian.plugins.rest.common.security.jersey,com.atlassi
 an.sal.api,com.atlassian.sal.api.user,com.atlassian.sal.api.web.conte
 xt,com.onresolve.dataprovider,com.onresolve.dataprovider.model.admin,
 com.onresolve.scriptrunner.audit,com.onresolve.scriptrunner.canned,co
 m.onresolve.scriptrunner.canned.common.rest,com.onresolve.scriptrunne
 r.canned.common.rest.model,com.onresolve.scriptrunner.canned.testrunn
 er,com.onresolve.scriptrunner.db.configurations,com.onresolve.scriptr
 unner.events.remote,com.onresolve.scriptrunner.filters,com.onresolve.
 scriptrunner.fragments,com.onresolve.scriptrunner.fragments.model,com
 .onresolve.scriptrunner.jobs,com.onresolve.scriptrunner.model,com.onr
 esolve.scriptrunner.onboarding.example,com.onresolve.scriptrunner.per
 sistence,com.onresolve.scriptrunner.resources,com.onresolve.scriptrun
 ner.runner,com.onresolve.scriptrunner.runner.diag,com.onresolve.scrip
 trunner.runner.diag.rrd,com.onresolve.scriptrunner.runner.events,com.
 onresolve.scriptrunner.runner.file,com.onresolve.scriptrunner.runner.
 rest,com.onresolve.scriptrunner.runner.rest.common.model,com.onresolv
 e.scriptrunner.runner.rest.common.permissions,com.onresolve.scriptrun
 ner.runner.rest.common.providers.reader,com.onresolve.scriptrunner.ru
 nner.rest.common.providers.writer,com.onresolve.scriptrunner.runner.s
 tc,com.onresolve.scriptrunner.runner.util,com.onresolve.scriptrunner.
 scheduled,com.onresolve.scriptrunner.scheduled.model,com.onresolve.sc
 riptrunner.stc.completions,com.onresolve.scriptrunner.stc.completions
 .response,com.onresolve.scriptrunner.stc.completions.webrequest,com.o
 nresolve.scriptrunner.stc.typecheck,com.onresolve.scriptrunner.storag
 eregistry,com.onresolve.scriptrunner.switchuser,com.sun.jersey.spi.co
 ntainer,groovy.lang,groovy.transform,groovy.transform.stc,javax.crypt
 o,javax.inject,javax.servlet,javax.servlet.http,javax.ws.rs,javax.ws.
 rs.core,javax.ws.rs.ext,org.apache.log4j,org.codehaus.groovy.runtime"
 ,com.onresolve.scriptrunner.runner.rest.common.ast;uses:="groovy.insp
 ect,groovy.lang,groovy.transform,org.codehaus.groovy.runtime",com.onr
 esolve.scriptrunner.runner.rest.common.error;uses:="com.atlassian.sal
 .api,com.atlassian.sal.api.web.context,com.onresolve.scriptrunner.can
 ned,com.onresolve.scriptrunner.exception,com.onresolve.scriptrunner.r
 unner.rest.common.error.model,com.onresolve.scriptrunner.switchuser,g
 roovy.lang,groovy.transform,javax.inject,javax.ws.rs.core,javax.ws.rs
 .ext",com.onresolve.scriptrunner.runner.rest.common.error.model;uses:
 ="com.onresolve.scriptrunner.canned.util,com.onresolve.scriptrunner.r
 unner.rest.common.error,groovy.lang,groovy.transform",com.onresolve.s
 criptrunner.runner.rest.common.model;uses:="com.onresolve.scriptrunne
 r.runner.diag,groovy.lang,groovy.transform",com.onresolve.scriptrunne
 r.runner.rest.common.permissions;uses:="com.atlassian.plugin.spring.s
 canner.annotation.component,com.atlassian.plugin.spring.scanner.annot
 ation.export,com.atlassian.plugins.rest.common.security.jersey,com.at
 lassian.sal.api.user,com.sun.jersey.spi.container,groovy.lang,groovy.
 transform,javax.inject,javax.ws.rs.ext",com.onresolve.scriptrunner.ru
 nner.rest.common.providers.reader;uses:="com.google.common.reflect,co
 m.onresolve.scriptrunner.canned,groovy.lang,groovy.transform,javax.ws
 .rs,javax.ws.rs.core,javax.ws.rs.ext",com.onresolve.scriptrunner.runn
 er.rest.common.providers.writer;uses:="com.google.common.reflect,com.
 onresolve.scriptrunner.canned,com.onresolve.scriptrunner.settings.mod
 el,groovy.lang,groovy.transform,javax.ws.rs,javax.ws.rs.core,javax.ws
 .rs.ext,org.codehaus.groovy.runtime",com.onresolve.scriptrunner.runne
 r.rest.common.resource;uses:="groovy.lang,groovy.transform",com.onres
 olve.scriptrunner.runner.rest.common.scriptsearch;uses:="groovy.lang,
 groovy.transform,org.codehaus.groovy.runtime",com.onresolve.scriptrun
 ner.runner.rest.common.settings;uses:="com.atlassian.event.api,com.at
 lassian.plugins.rest.common.security,com.atlassian.plugins.rest.commo
 n.security.jersey,com.atlassian.sal.api,com.atlassian.sal.api.license
 ,com.atlassian.sal.api.user,com.atlassian.soy.renderer,com.onresolve.
 scriptrunner.analytics,com.onresolve.scriptrunner.analytics.tracking,
 com.onresolve.scriptrunner.runner,com.onresolve.scriptrunner.settings
 ,com.onresolve.scriptrunner.settings.model,com.sun.jersey.spi.contain
 er,groovy.lang,groovy.transform,javax.inject,javax.ws.rs,javax.ws.rs.
 core,javax.ws.rs.ext,org.codehaus.groovy.runtime",com.onresolve.scrip
 trunner.runner.rest.jira;uses:="com.atlassian.applinks.api,com.atlass
 ian.jira.bc.issue.search,com.atlassian.jira.event.type,com.atlassian.
 jira.issue,com.atlassian.jira.issue.fields,com.atlassian.jira.issue.f
 ields.config.manager,com.atlassian.jira.issue.link,com.atlassian.jira
 .issue.search,com.atlassian.jira.jql.operand,com.atlassian.jira.proje
 ct,com.atlassian.jira.security,com.atlassian.jira.security.groups,com
 .atlassian.jira.security.roles,com.atlassian.jira.user,com.atlassian.
 plugin.web.api,com.atlassian.plugins.rest.common.security,com.atlassi
 an.plugins.rest.common.security.jersey,com.atlassian.sal.api,com.atla
 ssian.sal.api.net,com.atlassian.sal.api.websudo,com.onresolve.jira.gr
 oovy.jql,com.onresolve.jira.groovy.jql.discovery,com.onresolve.jira.g
 roovy.jql.discovery.model,com.onresolve.scriptrunner.audit,com.onreso
 lve.scriptrunner.canned,com.onresolve.scriptrunner.canned.common.rest
 .model,com.onresolve.scriptrunner.canned.jira.fields.editable,com.onr
 esolve.scriptrunner.canned.jira.fields.editable.issuepicker,com.onres
 olve.scriptrunner.canned.jira.fields.model,com.onresolve.scriptrunner
 .canned.jira.utils.servicedesk,com.onresolve.scriptrunner.fields,com.
 onresolve.scriptrunner.filters,com.onresolve.scriptrunner.jira.workfl
 ow,com.onresolve.scriptrunner.runner,com.onresolve.scriptrunner.runne
 r.diag,com.onresolve.scriptrunner.runner.rest,com.onresolve.scriptrun
 ner.runner.rest.common,com.onresolve.scriptrunner.runner.rest.common.
 error,com.onresolve.scriptrunner.runner.rest.common.providers.reader,
 com.onresolve.scriptrunner.runner.rest.common.providers.writer,com.on
 resolve.scriptrunner.runner.rest.jira.model,com.onresolve.scriptrunne
 r.runner.stc,com.onresolve.scriptrunner.stc.completions,com.sun.jerse
 y.spi.container,groovy.lang,groovy.transform,javax.inject,javax.servl
 et.http,javax.ws.rs,javax.ws.rs.core,javax.ws.rs.ext,org.apache.log4j
 ,org.codehaus.groovy.runtime,org.springframework.beans.factory.config
 ",com.onresolve.scriptrunner.runner.rest.jira.error;uses:="com.onreso
 lve.scriptrunner.canned,com.onresolve.scriptrunner.canned.jira.fields
 .editable.picker,groovy.lang,groovy.transform,javax.ws.rs.core,javax.
 ws.rs.ext",com.onresolve.scriptrunner.runner.rest.jira.model;uses:="c
 om.onresolve.scriptrunner.canned.jira.fields.model,groovy.lang,groovy
 .transform",com.onresolve.scriptrunner.runner.rest.jira.software;uses
 :="com.atlassian.jira.bc.issue.search,com.atlassian.jira.security,com
 .atlassian.plugins.rest.common.security,com.atlassian.sal.api,groovy.
 lang,groovy.transform,javax.ws.rs,javax.ws.rs.core",com.onresolve.scr
 iptrunner.runner.scriptPlugins;uses:="com.atlassian.event.api,com.atl
 assian.plugin,com.atlassian.plugin.spring.scanner.annotation.export,c
 om.onresolve.scriptrunner.fragments,com.onresolve.scriptrunner.runner
 ,groovy.lang,groovy.transform,javax.annotation,javax.inject,org.apach
 e.log4j,org.codehaus.groovy.runtime",com.onresolve.scriptrunner.runne
 r.servlets;uses:="com.atlassian.sal.api,groovy.lang,groovy.transform,
 javax.servlet,javax.servlet.http,org.apache.log4j",com.onresolve.scri
 ptrunner.runner.startup;uses:="com.atlassian.jira.extension,com.atlas
 sian.plugin.event,com.atlassian.plugin.spring.scanner.annotation.expo
 rt,com.atlassian.sal.api.lifecycle,com.onresolve.scriptrunner.runner,
 groovy.lang,groovy.transform,javax.inject,org.apache.log4j,org.codeha
 us.groovy.runtime,org.springframework.beans.factory",com.onresolve.sc
 riptrunner.runner.stc;uses:="com.atlassian.plugin,com.atlassian.plugi
 n.spring.scanner.annotation.export,com.onresolve.scriptrunner.stc.com
 pletions,groovy.lang,groovy.transform,javax.inject,org.codehaus.groov
 y.ast,org.codehaus.groovy.runtime",com.onresolve.scriptrunner.runner.
 upgrade;uses:="com.atlassian.plugin.spring.scanner.annotation.export,
 com.atlassian.sal.api.message,com.atlassian.sal.api.upgrade,com.onres
 olve.scriptrunner.canned,com.onresolve.scriptrunner.runner,com.onreso
 lve.scriptrunner.upgrade,groovy.lang,groovy.transform,javax.inject,or
 g.codehaus.groovy.runtime",com.onresolve.scriptrunner.runner.util;use
 s:="com.atlassian.activeobjects.external,com.atlassian.plugin,com.atl
 assian.plugin.servlet,com.atlassian.plugin.webresource,com.atlassian.
 plugin.webresource.transformer,com.atlassian.plugin.webresource.url,c
 om.atlassian.sal.api,com.atlassian.sal.api.pluginsettings,com.atlassi
 an.sal.api.transaction,com.onresolve.scriptrunner.analytics.tracking,
 com.onresolve.scriptrunner.bitbucket,com.onresolve.scriptrunner.persi
 stence,com.onresolve.scriptrunner.runner,com.onresolve.scriptrunner.r
 unner.stc,com.onresolve.scriptrunner.stc.completions,groovy.lang,groo
 vy.transform,javax.inject,javax.servlet.http,org.apache.log4j,org.cod
 ehaus.groovy.ast,org.codehaus.groovy.runtime,org.osgi.framework",com.
 onresolve.scriptrunner.scheduled;uses:="com.atlassian.event.api,com.a
 tlassian.plugin.spring.scanner.annotation.component,com.atlassian.plu
 gin.spring.scanner.annotation.export,com.atlassian.scheduler,com.atla
 ssian.scheduler.config,com.onresolve.licensing,com.onresolve.scriptru
 nner.canned,com.onresolve.scriptrunner.persistence,com.onresolve.scri
 ptrunner.runner,com.onresolve.scriptrunner.scheduled.model,com.onreso
 lve.scriptrunner.storageregistry,groovy.lang,groovy.transform,javax.a
 nnotation,javax.inject,org.apache.log4j,org.codehaus.groovy.runtime",
 com.onresolve.scriptrunner.scheduled.model;uses:="com.atlassian.jira.
 workflow,com.atlassian.scheduler.config,com.onresolve.scriptrunner.ca
 nned,com.onresolve.scriptrunner.canned.validators,com.onresolve.scrip
 trunner.model,com.onresolve.scriptrunner.model.jackson,groovy.lang,gr
 oovy.transform",com.onresolve.scriptrunner.scheduled.upgrade;uses:="c
 om.atlassian.jira.ofbiz,com.atlassian.jira.service,com.atlassian.plug
 in.spring.scanner.annotation.export,com.atlassian.sal.api.message,com
 .atlassian.sal.api.upgrade,com.onresolve.scriptrunner.canned,com.onre
 solve.scriptrunner.persistence,com.onresolve.scriptrunner.runner,com.
 onresolve.scriptrunner.scheduled,groovy.lang,groovy.transform,javax.i
 nject,org.apache.log4j,org.codehaus.groovy.runtime",com.onresolve.scr
 iptrunner.settings;uses:="com.atlassian.cache,com.atlassian.crowd.eve
 nt.group,com.atlassian.event.api,com.atlassian.jira.event.permission,
 com.atlassian.jira.security,com.atlassian.plugin.spring.scanner.annot
 ation.component,com.atlassian.sal.api.user,com.onresolve.scriptrunner
 .runner,com.onresolve.scriptrunner.runner.util,com.onresolve.scriptru
 nner.settings.cache,com.onresolve.scriptrunner.settings.db,com.onreso
 lve.scriptrunner.settings.event,com.onresolve.scriptrunner.settings.m
 odel,groovy.lang,groovy.transform,javax.annotation,javax.inject,org.a
 pache.log4j,org.codehaus.groovy.runtime",com.onresolve.scriptrunner.s
 ettings.cache;uses:="groovy.lang,groovy.transform",com.onresolve.scri
 ptrunner.settings.db;uses:="com.atlassian.plugin.spring.scanner.annot
 ation.component,com.google.common.reflect,com.onresolve.scriptrunner.
 canned,com.onresolve.scriptrunner.persistence,com.onresolve.scriptrun
 ner.settings.model,groovy.lang,groovy.transform,javax.inject,org.code
 haus.groovy.runtime",com.onresolve.scriptrunner.settings.event;uses:=
 "com.atlassian.event.api,com.onresolve.scriptrunner.audit.events,com.
 onresolve.scriptrunner.settings.event.audit.converter,groovy.lang,gro
 ovy.transform",com.onresolve.scriptrunner.settings.event.audit.conver
 ter;uses:="com.onresolve.scriptrunner.audit,com.onresolve.scriptrunne
 r.audit.events,com.onresolve.scriptrunner.settings.event,groovy.lang,
 groovy.transform",com.onresolve.scriptrunner.settings.model;uses:="gr
 oovy.lang,groovy.transform",com.onresolve.scriptrunner.setuser;uses:=
 "com.atlassian.plugin.spring.scanner.annotation.component,com.atlassi
 an.sal.api.user,com.atlassian.sal.api.web.context,groovy.lang,groovy.
 transform,javax.inject",com.onresolve.scriptrunner.shared,com.onresol
 ve.scriptrunner.slack;uses:="com.onresolve.scriptrunner.analytics.tra
 cking,com.onresolve.scriptrunner.resources,groovy.lang,groovy.transfo
 rm,javax.inject,org.apache.http.client.methods,org.codehaus.groovy.ru
 ntime",com.onresolve.scriptrunner.spring;uses:="com.atlassian.jira.jq
 l.builder,com.atlassian.jira.jql.resolver,groovy.lang,groovy.transfor
 m,javax.inject,org.springframework.beans.factory",com.onresolve.scrip
 trunner.stc;uses:="com.atlassian.jira.issue,com.atlassian.jira.issue.
 fields,com.atlassian.plugin.spring.scanner.annotation.component,com.a
 tlassian.plugin.spring.scanner.annotation.export,com.onresolve.script
 runner.runner.classloading,com.onresolve.scriptrunner.runner.stc,com.
 onresolve.scriptrunner.stc.completions,com.onresolve.scriptrunner.stc
 .completions.pluggable,com.onresolve.scriptrunner.stc.completions.req
 uest,com.onresolve.scriptrunner.stc.typecheck,com.onresolve.scriptrun
 ner.stc.typecheck.deprecations,groovy.lang,groovy.transform,javax.inj
 ect,org.apache.log4j,org.codehaus.groovy.ast,org.codehaus.groovy.ast.
 expr,org.codehaus.groovy.control,org.codehaus.groovy.control.customiz
 ers,org.codehaus.groovy.runtime",com.onresolve.scriptrunner.stc.compi
 lation;uses:="com.onresolve.scriptrunner.stc,groovy.lang,groovy.trans
 form,org.codehaus.groovy.control,org.codehaus.groovy.runtime",com.onr
 esolve.scriptrunner.stc.completions;uses:="com.onresolve.scriptrunner
 ,com.onresolve.scriptrunner.beans,com.onresolve.scriptrunner.runner.c
 lassloading,com.onresolve.scriptrunner.runner.stc,com.onresolve.scrip
 trunner.stc.completions.classindex,com.onresolve.scriptrunner.stc.com
 pletions.doc,com.onresolve.scriptrunner.stc.completions.request,com.o
 nresolve.scriptrunner.stc.completions.response,com.onresolve.scriptru
 nner.stc.completions.webrequest,com.onresolve.scriptrunner.stc.typech
 eck,com.onresolve.scriptrunner.stc.util,groovy.lang,groovy.transform,
 javax.inject,org.apache.log4j,org.codehaus.groovy.ast,org.codehaus.gr
 oovy.ast.expr,org.codehaus.groovy.ast.stmt,org.codehaus.groovy.classg
 en,org.codehaus.groovy.control,org.codehaus.groovy.control.customizer
 s,org.codehaus.groovy.control.messages,org.codehaus.groovy.runtime,or
 g.codehaus.groovy.syntax,org.codehaus.groovy.transform.stc",com.onres
 olve.scriptrunner.stc.completions.classindex;uses:="groovy.lang,groov
 y.transform,org.codehaus.groovy.ast,org.codehaus.groovy.runtime",com.
 onresolve.scriptrunner.stc.completions.doc;uses:="com.onresolve.scrip
 trunner.application,com.onresolve.scriptrunner.stc.completions.reques
 t,com.onresolve.scriptrunner.stc.completions.response,groovy.lang,gro
 ovy.transform,javax.inject,org.codehaus.groovy.ast,org.codehaus.groov
 y.runtime",com.onresolve.scriptrunner.stc.completions.pluggable;uses:
 ="com.onresolve.scriptrunner.stc.completions,com.onresolve.scriptrunn
 er.stc.completions.request,com.onresolve.scriptrunner.stc.typecheck,g
 roovy.lang,groovy.transform,org.codehaus.groovy.ast,org.codehaus.groo
 vy.ast.expr,org.codehaus.groovy.runtime",com.onresolve.scriptrunner.s
 tc.completions.request;uses:="com.onresolve.scriptrunner.runner.stc,c
 om.onresolve.scriptrunner.stc.completions,com.onresolve.scriptrunner.
 stc.typecheck,com.onresolve.scriptrunner.stc.util,groovy.lang,groovy.
 transform,javax.inject,org.codehaus.groovy.ast,org.codehaus.groovy.co
 ntrol,org.codehaus.groovy.runtime,org.codehaus.groovy.transform.stc",
 com.onresolve.scriptrunner.stc.completions.response;uses:="com.onreso
 lve.scriptrunner.stc.completions,groovy.lang,groovy.transform",com.on
 resolve.scriptrunner.stc.completions.webrequest;uses:="com.onresolve.
 scriptrunner.stc.completions,com.onresolve.scriptrunner.stc.completio
 ns.request,groovy.lang,groovy.transform",com.onresolve.scriptrunner.s
 tc.gstring;uses:="groovy.lang,groovy.transform,org.codehaus.groovy.ru
 ntime",com.onresolve.scriptrunner.stc.typecheck;uses:="com.atlassian.
 util.concurrent,com.google.common.collect,com.onresolve.scriptrunner,
 com.onresolve.scriptrunner.parameters,com.onresolve.scriptrunner.runn
 er.classloading,com.onresolve.scriptrunner.runner.stc,com.onresolve.s
 criptrunner.stc.completions,com.onresolve.scriptrunner.stc.completion
 s.classindex,com.onresolve.scriptrunner.stc.completions.pluggable,com
 .onresolve.scriptrunner.stc.completions.request,com.onresolve.scriptr
 unner.stc.gstring,com.onresolve.scriptrunner.stc.typecheck.deprecatio
 ns,groovy.lang,groovy.transform,javax.inject,org.apache.log4j,org.cod
 ehaus.groovy.ast,org.codehaus.groovy.ast.expr,org.codehaus.groovy.cla
 ssgen,org.codehaus.groovy.control,org.codehaus.groovy.control.customi
 zers,org.codehaus.groovy.runtime",com.onresolve.scriptrunner.stc.type
 check.deprecations;uses:="com.onresolve.scriptrunner.application,com.
 onresolve.scriptrunner.canned,com.onresolve.scriptrunner.stc.completi
 ons,groovy.lang,groovy.transform,javax.inject,org.codehaus.groovy.ast
 ,org.codehaus.groovy.ast.expr,org.codehaus.groovy.control,org.codehau
 s.groovy.runtime,org.codehaus.groovy.transform.stc",com.onresolve.scr
 iptrunner.stc.typecheck.imports;uses:="com.onresolve.scriptrunner.stc
 .completions,groovy.lang,groovy.transform,org.codehaus.groovy.ast,org
 .codehaus.groovy.control,org.codehaus.groovy.runtime",com.onresolve.s
 criptrunner.stc.util;uses:="groovy.lang,groovy.transform",com.onresol
 ve.scriptrunner.storageregistry;uses:="com.onresolve.scriptrunner.can
 ned,com.onresolve.scriptrunner.persistence,com.onresolve.scriptrunner
 .runner,groovy.lang,groovy.transform,javax.inject,javax.ws.rs.core,or
 g.codehaus.groovy.runtime",com.onresolve.scriptrunner.switchuser;uses
 :="com.atlassian.plugin,com.atlassian.plugin.spring.scanner.annotatio
 n.component,com.atlassian.plugin.web,com.atlassian.sal.api.user,com.a
 tlassian.sal.api.web.context,com.onresolve.scriptrunner.settings,com.
 onresolve.scriptrunner.setuser,groovy.lang,groovy.transform,javax.inj
 ect,javax.servlet.http",com.onresolve.scriptrunner.tags;uses:="com.on
 resolve.scriptrunner.canned.tags,groovy.lang,groovy.transform",com.on
 resolve.scriptrunner.test;uses:="com.atlassian.jira.bc.issue,com.atla
 ssian.jira.bc.project,com.atlassian.jira.config,com.atlassian.jira.is
 sue,com.atlassian.jira.issue.comments,com.atlassian.jira.issue.contex
 t,com.atlassian.jira.issue.customfields,com.atlassian.jira.issue.fiel
 ds,com.atlassian.jira.issue.issuetype,com.atlassian.jira.issue.link,c
 om.atlassian.jira.issue.resolution,com.atlassian.jira.issue.security,
 com.atlassian.jira.issue.watchers,com.atlassian.jira.project,com.atla
 ssian.jira.project.version,com.atlassian.jira.scheme,com.atlassian.ji
 ra.user,com.atlassian.jira.workflow,com.atlassian.plugin.webresource,
 com.atlassian.soy.renderer,com.onresolve.scriptrunner.fields,com.onre
 solve.scriptrunner.model,com.onresolve.scriptrunner.runner,com.opensy
 mphony.workflow.loader,groovy.lang,groovy.transform,javax.servlet,jav
 ax.servlet.http,org.apache.log4j,org.codehaus.groovy.runtime",com.onr
 esolve.scriptrunner.testrunner;uses:="com.onresolve.scriptrunner.test
 runner.event,com.onresolve.scriptrunner.testrunner.reporting,groovy.l
 ang,groovy.transform,javax.inject,org.codehaus.groovy.runtime",com.on
 resolve.scriptrunner.testrunner.engine;uses:="com.onresolve.scriptrun
 ner.testrunner.event,groovy.lang,groovy.transform,org.apache.http,org
 .apache.http.protocol,org.codehaus.groovy.runtime",com.onresolve.scri
 ptrunner.testrunner.event;uses:="groovy.lang,groovy.transform",com.on
 resolve.scriptrunner.testrunner.jackson;uses:="groovy.lang,groovy.tra
 nsform,org.codehaus.groovy.runtime",com.onresolve.scriptrunner.testru
 nner.reporting;uses:="groovy.lang,groovy.transform,javax.inject",com.
 onresolve.scriptrunner.upgrade;uses:="com.onresolve.scriptrunner.ao,c
 om.onresolve.scriptrunner.canned,com.onresolve.scriptrunner.querydsl,
 groovy.lang,groovy.transform,javax.inject,net.java.ao,net.java.ao.sch
 ema,org.apache.log4j,org.codehaus.groovy.runtime",com.onresolve.scrip
 trunner.user.properties.db;uses:="com.onresolve.scriptrunner.ao,com.o
 nresolve.scriptrunner.querydsl,groovy.lang,groovy.transform,javax.inj
 ect,net.java.ao,net.java.ao.schema,org.codehaus.groovy.runtime",com.o
 nresolve.scriptrunner.user.properties.rest;uses:="com.atlassian.sal.a
 pi.user,com.onresolve.scriptrunner.user.properties.service,groovy.lan
 g,groovy.transform,javax.ws.rs,javax.ws.rs.core",com.onresolve.script
 runner.user.properties.service;uses:="com.atlassian.crowd.event.user,
 com.atlassian.event.api,com.atlassian.sal.api.user,com.onresolve.scri
 ptrunner.user.properties.db,groovy.lang,groovy.transform,javax.annota
 tion,javax.inject,org.apache.log4j",com.onresolve.scriptrunner.workfl
 ow;uses:="com.atlassian.jira.issue,com.onresolve.scriptrunner.model,c
 om.onresolve.scriptrunner.runner,groovy.lang,groovy.transform,javax.i
 nject",com.onresolve.scriptrunner.xml.util;uses:="groovy.lang,groovy.
 transform,org.dom4j.io",com.onresolve.spring;uses:="com.atlassian.plu
 gin.spring.scanner.annotation.export,groovy.lang,groovy.transform,jav
 ax.inject,org.apache.log4j,org.springframework.beans,org.springframew
 ork.beans.factory,org.springframework.context",com.onresolve.upgrade;
 uses:="com.atlassian.sal.api.message,groovy.lang,groovy.transform",gr
 oovyx.net.http;uses:="groovy.lang,javax.xml.parsers,org.apache.common
 s.logging,org.apache.http,org.apache.http.client,org.apache.http.clie
 nt.entity,org.apache.http.client.methods,org.apache.http.entity,org.a
 pache.http.params,org.apache.http.protocol,org.xml.sax",groovyx.net.h
 ttp.thirdparty;uses:="org.apache.http.conn,org.apache.http.conn.routi
 ng,org.apache.http.conn.scheme",groovy.beans;version="3.0.12";uses:="
 groovy.lang,groovy.transform,groovyjarjarasm.asm,org.codehaus.groovy.
 ast,org.codehaus.groovy.ast.expr,org.codehaus.groovy.ast.stmt,org.cod
 ehaus.groovy.control,org.codehaus.groovy.runtime,org.codehaus.groovy.
 transform",groovy.cli;version="3.0.12";uses:="groovy.lang,groovy.tran
 sform",groovy.cli.internal;version="3.0.12";uses:="groovy.cli,groovy.
 lang,groovy.transform,groovyjarjarpicocli,org.codehaus.groovy.runtime
 ",groovy.grape;version="3.0.12";uses:="groovy.lang,groovy.transform,o
 rg.apache.groovy.plugin,org.apache.ivy,org.apache.ivy.core.module.des
 criptor,org.apache.ivy.core.module.id,org.apache.ivy.core.report,org.
 apache.ivy.core.settings,org.apache.ivy.util.extendable,org.codehaus.
 groovy.ast,org.codehaus.groovy.control,org.codehaus.groovy.reflection
 ,org.codehaus.groovy.runtime,org.codehaus.groovy.transform,org.w3c.do
 m",groovy.inspect;version="3.0.12";uses:="groovy.lang,groovy.transfor
 m",groovy.io;version="3.0.12",groovy.lang;version="3.0.12";uses:="gro
 ovy.transform,groovy.transform.stc,groovyjarjarasm.asm,org.apache.gro
 ovy.plugin,org.codehaus.groovy.ast,org.codehaus.groovy.control,org.co
 dehaus.groovy.reflection,org.codehaus.groovy.runtime,org.codehaus.gro
 ovy.runtime.callsite,org.codehaus.groovy.runtime.memoize,org.codehaus
 .groovy.runtime.metaclass,org.codehaus.groovy.transform,org.codehaus.
 groovy.util",groovy.lang.groovydoc;version="3.0.12",groovy.namespace;
 version="3.0.12",groovy.security;version="3.0.12",groovy.time;version
 ="3.0.12",groovy.transform;version="3.0.12";uses:="groovy.lang,groovy
 .transform.options,org.apache.groovy.lang.annotation,org.codehaus.gro
 ovy.ast,org.codehaus.groovy.control,org.codehaus.groovy.transform",gr
 oovy.transform.builder;version="3.0.12";uses:="groovy.transform,org.c
 odehaus.groovy.ast,org.codehaus.groovy.transform",groovy.transform.op
 tions;version="3.0.12";uses:="groovy.lang,org.apache.groovy.lang.anno
 tation,org.codehaus.groovy.ast,org.codehaus.groovy.ast.expr,org.codeh
 aus.groovy.ast.stmt,org.codehaus.groovy.transform",groovy.transform.s
 tc;version="3.0.12";uses:="org.codehaus.groovy.ast,org.codehaus.groov
 y.ast.expr,org.codehaus.groovy.control,org.codehaus.groovy.syntax",gr
 oovy.ui;version="3.0.12";uses:="groovy.lang,groovy.transform,groovyja
 rjarpicocli,javax.swing,javax.swing.event,javax.swing.filechooser,jav
 ax.swing.text,org.codehaus.groovy.control,org.codehaus.groovy.runtime
 ,org.codehaus.groovy.tools.shell.util",groovy.util;version="3.0.12";u
 ses:="groovy.lang,groovy.namespace,groovy.transform,groovy.transform.
 stc,groovy.xml,javax.management,javax.xml.parsers,org.codehaus.groovy
 .control,org.codehaus.groovy.runtime,org.xml.sax,org.xml.sax.helpers"
 ,groovy.util.logging;version="3.0.12";uses:="groovy.lang,org.codehaus
 .groovy.ast,org.codehaus.groovy.ast.expr,org.codehaus.groovy.transfor
 m",groovy.xml;version="3.0.12";uses:="groovy.lang,groovy.namespace,gr
 oovy.transform,groovy.util,javax.xml.parsers,javax.xml.transform,org.
 codehaus.groovy.runtime,org.w3c.dom,org.xml.sax,org.xml.sax.helpers",
 groovyjarjarantlr;version="3.0.12";uses:="groovyjarjarantlr.ASdebug,g
 roovyjarjarantlr.collections,groovyjarjarantlr.collections.impl,groov
 yjarjarantlr.debug",groovyjarjarantlr.ASdebug;version="3.0.12";uses:=
 groovyjarjarantlr,groovyjarjarantlr.actions.cpp;version="3.0.12";uses
 :="groovyjarjarantlr,groovyjarjarantlr.collections.impl",groovyjarjar
 antlr.actions.csharp;version="3.0.12";uses:="groovyjarjarantlr,groovy
 jarjarantlr.collections.impl",groovyjarjarantlr.actions.java;version=
 "3.0.12";uses:="groovyjarjarantlr,groovyjarjarantlr.collections.impl"
 ,groovyjarjarantlr.actions.python;version="3.0.12";uses:="groovyjarja
 rantlr,groovyjarjarantlr.collections.impl",groovyjarjarantlr.build;ve
 rsion="3.0.12",groovyjarjarantlr.collections;version="3.0.12";uses:=g
 roovyjarjarantlr,groovyjarjarantlr.collections.impl;version="3.0.12";
 uses:="groovyjarjarantlr,groovyjarjarantlr.collections",groovyjarjara
 ntlr.debug;version="3.0.12";uses:="groovyjarjarantlr,groovyjarjarantl
 r.collections.impl",groovyjarjarantlr.debug.misc;version="3.0.12";use
 s:="groovyjarjarantlr.collections,javax.swing,javax.swing.event,javax
 .swing.tree",groovyjarjarantlr.preprocessor;version="3.0.12";uses:="g
 roovyjarjarantlr,groovyjarjarantlr.collections.impl",groovyjarjarantl
 r4.runtime;version="3.0.12";uses:="groovyjarjarantlr4.runtime.misc,gr
 oovyjarjarantlr4.runtime.tree",groovyjarjarantlr4.runtime.debug;versi
 on="3.0.12";uses:="groovyjarjarantlr4.runtime,groovyjarjarantlr4.runt
 ime.misc,groovyjarjarantlr4.runtime.tree",groovyjarjarantlr4.runtime.
 misc;version="3.0.12",groovyjarjarantlr4.runtime.tree;version="3.0.12
 ";uses:="groovyjarjarantlr4.runtime,groovyjarjarantlr4.runtime.misc,g
 roovyjarjarantlr4.stringtemplate",groovyjarjarantlr4.v4;version="3.0.
 12";uses:="groovyjarjarantlr4.runtime,groovyjarjarantlr4.v4.runtime.m
 isc,groovyjarjarantlr4.v4.tool,groovyjarjarantlr4.v4.tool.ast",groovy
 jarjarantlr4.v4.analysis;version="3.0.12";uses:="groovyjarjarantlr4.r
 untime,groovyjarjarantlr4.v4,groovyjarjarantlr4.v4.parse,groovyjarjar
 antlr4.v4.runtime.atn,groovyjarjarantlr4.v4.runtime.misc,groovyjarjar
 antlr4.v4.tool,groovyjarjarantlr4.v4.tool.ast,org.stringtemplate.v4",
 groovyjarjarantlr4.v4.automata;version="3.0.12";uses:="groovyjarjaran
 tlr4.v4.runtime.atn,groovyjarjarantlr4.v4.runtime.misc,groovyjarjaran
 tlr4.v4.tool,groovyjarjarantlr4.v4.tool.ast,org.stringtemplate.v4",gr
 oovyjarjarantlr4.v4.codegen;version="3.0.12";uses:="groovyjarjarantlr
 4.runtime,groovyjarjarantlr4.runtime.tree,groovyjarjarantlr4.v4,groov
 yjarjarantlr4.v4.codegen.model,groovyjarjarantlr4.v4.codegen.model.ch
 unk,groovyjarjarantlr4.v4.codegen.model.decl,groovyjarjarantlr4.v4.pa
 rse,groovyjarjarantlr4.v4.runtime.misc,groovyjarjarantlr4.v4.tool,gro
 ovyjarjarantlr4.v4.tool.ast,org.stringtemplate.v4",groovyjarjarantlr4
 .v4.codegen.model;version="3.0.12";uses:="groovyjarjarantlr4.runtime.
 tree,groovyjarjarantlr4.v4.codegen,groovyjarjarantlr4.v4.codegen.mode
 l.chunk,groovyjarjarantlr4.v4.codegen.model.decl,groovyjarjarantlr4.v
 4.misc,groovyjarjarantlr4.v4.parse,groovyjarjarantlr4.v4.runtime.atn,
 groovyjarjarantlr4.v4.runtime.misc,groovyjarjarantlr4.v4.tool,groovyj
 arjarantlr4.v4.tool.ast,org.stringtemplate.v4",groovyjarjarantlr4.v4.
 codegen.model.chunk;version="3.0.12";uses:="groovyjarjarantlr4.v4.cod
 egen.model,groovyjarjarantlr4.v4.codegen.model.decl,org.stringtemplat
 e.v4",groovyjarjarantlr4.v4.codegen.model.decl;version="3.0.12";uses:
 ="groovyjarjarantlr4.v4.codegen,groovyjarjarantlr4.v4.codegen.model,g
 roovyjarjarantlr4.v4.runtime.misc,groovyjarjarantlr4.v4.tool",groovyj
 arjarantlr4.v4.codegen.target;version="3.0.12";uses:="groovyjarjarant
 lr4.v4.codegen,groovyjarjarantlr4.v4.tool.ast,org.stringtemplate.v4",
 groovyjarjarantlr4.v4.gui;version="3.0.12";uses:="groovyjarjarantlr4.
 v4.runtime,groovyjarjarantlr4.v4.runtime.tree,javax.print,javax.swing
 ,org.abego.treelayout",groovyjarjarantlr4.v4.misc;version="3.0.12";us
 es:="groovyjarjarantlr4.v4.runtime.misc,groovyjarjarantlr4.v4.tool.as
 t",groovyjarjarantlr4.v4.parse;version="3.0.12";uses:="groovyjarjaran
 tlr4.runtime,groovyjarjarantlr4.runtime.tree,groovyjarjarantlr4.v4,gr
 oovyjarjarantlr4.v4.automata,groovyjarjarantlr4.v4.runtime.misc,groov
 yjarjarantlr4.v4.tool,groovyjarjarantlr4.v4.tool.ast",groovyjarjarant
 lr4.v4.runtime;version="3.0.12";uses:="groovyjarjarantlr4.v4.runtime.
 atn,groovyjarjarantlr4.v4.runtime.dfa,groovyjarjarantlr4.v4.runtime.m
 isc,groovyjarjarantlr4.v4.runtime.tree,groovyjarjarantlr4.v4.runtime.
 tree.pattern",groovyjarjarantlr4.v4.runtime.atn;version="3.0.12";uses
 :="groovyjarjarantlr4.v4.runtime,groovyjarjarantlr4.v4.runtime.dfa,gr
 oovyjarjarantlr4.v4.runtime.misc",groovyjarjarantlr4.v4.runtime.dfa;v
 ersion="3.0.12";uses:="groovyjarjarantlr4.v4.runtime,groovyjarjarantl
 r4.v4.runtime.atn",groovyjarjarantlr4.v4.runtime.misc;version="3.0.12
 ";uses:="groovyjarjarantlr4.v4.runtime,javax.annotation.processing,ja
 vax.lang.model,javax.lang.model.element",groovyjarjarantlr4.v4.runtim
 e.tree;version="3.0.12";uses:="groovyjarjarantlr4.v4.runtime,groovyja
 rjarantlr4.v4.runtime.misc",groovyjarjarantlr4.v4.runtime.tree.patter
 n;version="3.0.12";uses:="groovyjarjarantlr4.v4.runtime,groovyjarjara
 ntlr4.v4.runtime.misc,groovyjarjarantlr4.v4.runtime.tree",groovyjarja
 rantlr4.v4.runtime.tree.xpath;version="3.0.12";uses:="groovyjarjarant
 lr4.v4.runtime,groovyjarjarantlr4.v4.runtime.atn,groovyjarjarantlr4.v
 4.runtime.tree",groovyjarjarantlr4.v4.semantics;version="3.0.12";uses
 :="groovyjarjarantlr4.runtime,groovyjarjarantlr4.v4.misc,groovyjarjar
 antlr4.v4.parse,groovyjarjarantlr4.v4.tool,groovyjarjarantlr4.v4.tool
 .ast,org.stringtemplate.v4.misc",groovyjarjarantlr4.v4.tool;version="
 3.0.12";uses:="groovyjarjarantlr4.runtime,groovyjarjarantlr4.runtime.
 tree,groovyjarjarantlr4.v4,groovyjarjarantlr4.v4.analysis,groovyjarja
 rantlr4.v4.codegen,groovyjarjarantlr4.v4.misc,groovyjarjarantlr4.v4.p
 arse,groovyjarjarantlr4.v4.runtime,groovyjarjarantlr4.v4.runtime.atn,
 groovyjarjarantlr4.v4.runtime.dfa,groovyjarjarantlr4.v4.runtime.misc,
 groovyjarjarantlr4.v4.tool.ast,org.stringtemplate.v4",groovyjarjarant
 lr4.v4.tool.ast;version="3.0.12";uses:="groovyjarjarantlr4.runtime,gr
 oovyjarjarantlr4.runtime.tree,groovyjarjarantlr4.v4.analysis,groovyja
 rjarantlr4.v4.runtime.atn,groovyjarjarantlr4.v4.runtime.misc,groovyja
 rjarantlr4.v4.tool",groovyjarjarantlr4.v4.tool.templates;version="3.0
 .12",groovyjarjarantlr4.v4.tool.templates.codegen.Java;version="3.0.1
 2",groovyjarjarantlr4.v4.tool.templates.dot;version="3.0.12",groovyja
 rjarantlr4.v4.tool.templates.messages.formats;version="3.0.12",groovy
 jarjarantlr4.v4.unicode;version="3.0.12";uses:="groovyjarjarantlr4.v4
 .runtime.misc",groovyjarjarasm.asm;version="3.0.12",groovyjarjarasm.a
 sm.commons;version="3.0.12";uses:="groovyjarjarasm.asm,groovyjarjaras
 m.asm.signature,groovyjarjarasm.asm.tree",groovyjarjarasm.asm.signatu
 re;version="3.0.12",groovyjarjarasm.asm.tree;version="3.0.12";uses:="
 groovyjarjarasm.asm",groovyjarjarasm.asm.util;version="3.0.12";uses:=
 "groovyjarjarasm.asm,groovyjarjarasm.asm.signature",groovyjarjarpicoc
 li;version="3.0.12",org.apache.groovy.antlr;version="3.0.12";uses:="j
 avax.swing,org.codehaus.groovy.ast,org.codehaus.groovy.ast.expr,org.c
 odehaus.groovy.control",org.apache.groovy.ast.tools;version="3.0.12";
 uses:="org.codehaus.groovy.ast,org.codehaus.groovy.ast.expr,org.codeh
 aus.groovy.ast.stmt,org.codehaus.groovy.transform",org.apache.groovy.
 internal.metaclass;version="3.0.12";uses:="groovy.lang,org.apache.gro
 ovy.lang.annotation",org.apache.groovy.internal.util;version="3.0.12"
 ;uses:="org.apache.groovy.lang.annotation",org.apache.groovy.io;versi
 on="3.0.12",org.apache.groovy.lang.annotation;version="3.0.12",org.ap
 ache.groovy.metaclass;version="3.0.12";uses:="groovy.lang,org.apache.
 groovy.lang.annotation",org.apache.groovy.parser.antlr4;version="3.0.
 12";uses:="groovy.lang,groovyjarjarantlr4.v4.runtime,groovyjarjarantl
 r4.v4.runtime.atn,groovyjarjarantlr4.v4.runtime.tree,org.codehaus.gro
 ovy.ast,org.codehaus.groovy.ast.expr,org.codehaus.groovy.ast.stmt,org
 .codehaus.groovy.control,org.codehaus.groovy.syntax",org.apache.groov
 y.parser.antlr4.internal;version="3.0.12";uses:="groovyjarjarantlr4.v
 4.runtime",org.apache.groovy.parser.antlr4.internal.atnmanager;versio
 n="3.0.12";uses:="groovyjarjarantlr4.v4.runtime.atn",org.apache.groov
 y.parser.antlr4.util;version="3.0.12";uses:="groovy.lang,groovyjarjar
 antlr4.v4.runtime,groovyjarjarantlr4.v4.runtime.tree,org.apache.groov
 y.parser.antlr4,org.codehaus.groovy.ast",org.apache.groovy.plugin;ver
 sion="3.0.12";uses:="groovy.lang",org.apache.groovy.util;version="3.0
 .12",org.apache.groovy.util.concurrent;version="3.0.12",org.apache.gr
 oovy.util.concurrent.concurrentlinkedhashmap;version="3.0.12",org.cod
 ehaus.groovy;version="3.0.12",org.codehaus.groovy.antlr;version="3.0.
 12";uses:="groovyjarjarantlr,groovyjarjarantlr.collections,javax.swin
 g,org.codehaus.groovy.antlr.parser,org.codehaus.groovy.ast,org.codeha
 us.groovy.ast.expr,org.codehaus.groovy.ast.stmt,org.codehaus.groovy.c
 ontrol,org.codehaus.groovy.syntax",org.codehaus.groovy.antlr.java;ver
 sion="3.0.12";uses:="groovyjarjarantlr,groovyjarjarantlr.collections,
 groovyjarjarantlr.collections.impl,org.codehaus.groovy.antlr,org.code
 haus.groovy.antlr.parser,org.codehaus.groovy.antlr.treewalker",org.co
 dehaus.groovy.antlr.parser;version="3.0.12";uses:="groovyjarjarantlr,
 groovyjarjarantlr.collections,groovyjarjarantlr.collections.impl,org.
 codehaus.groovy.antlr",org.codehaus.groovy.antlr.treewalker;version="
 3.0.12";uses:="groovyjarjarantlr.collections,org.codehaus.groovy.antl
 r",org.codehaus.groovy.ast;version="3.0.12";uses:="groovy.lang,groovy
 .lang.groovydoc,groovy.transform,groovyjarjarasm.asm,org.codehaus.gro
 ovy.ast.expr,org.codehaus.groovy.ast.stmt,org.codehaus.groovy.classge
 n,org.codehaus.groovy.control,org.codehaus.groovy.control.io,org.code
 haus.groovy.transform",org.codehaus.groovy.ast.builder;version="3.0.1
 2";uses:="groovy.lang,groovy.transform,org.codehaus.groovy.ast,org.co
 dehaus.groovy.control,org.codehaus.groovy.runtime,org.codehaus.groovy
 .transform",org.codehaus.groovy.ast.decompiled;version="3.0.12";uses:
 ="groovyjarjarasm.asm,org.codehaus.groovy.ast,org.codehaus.groovy.con
 trol",org.codehaus.groovy.ast.expr;version="3.0.12";uses:="groovy.lan
 g,org.codehaus.groovy.ast,org.codehaus.groovy.ast.stmt,org.codehaus.g
 roovy.syntax",org.codehaus.groovy.ast.stmt;version="3.0.12";uses:="or
 g.codehaus.groovy.ast,org.codehaus.groovy.ast.expr",org.codehaus.groo
 vy.ast.tools;version="3.0.12";uses:="groovy.lang,groovyjarjarasm.asm,
 org.codehaus.groovy.ast,org.codehaus.groovy.ast.expr,org.codehaus.gro
 ovy.ast.stmt,org.codehaus.groovy.classgen,org.codehaus.groovy.control
 ,org.codehaus.groovy.control.io,org.codehaus.groovy.syntax",org.codeh
 aus.groovy.classgen;version="3.0.12";uses:="groovy.lang,groovy.transf
 orm,groovyjarjarasm.asm,org.codehaus.groovy.ast,org.codehaus.groovy.a
 st.expr,org.codehaus.groovy.ast.stmt,org.codehaus.groovy.classgen.asm
 ,org.codehaus.groovy.control,org.codehaus.groovy.runtime",org.codehau
 s.groovy.classgen.asm;version="3.0.12";uses:="groovyjarjarasm.asm,org
 .codehaus.groovy.ast,org.codehaus.groovy.ast.expr,org.codehaus.groovy
 .ast.stmt,org.codehaus.groovy.classgen,org.codehaus.groovy.control,or
 g.codehaus.groovy.syntax",org.codehaus.groovy.classgen.asm.indy;versi
 on="3.0.12";uses:="org.codehaus.groovy.ast,org.codehaus.groovy.ast.ex
 pr,org.codehaus.groovy.classgen.asm",org.codehaus.groovy.classgen.asm
 .indy.sc;version="3.0.12";uses:="org.codehaus.groovy.classgen.asm,org
 .codehaus.groovy.classgen.asm.sc",org.codehaus.groovy.classgen.asm.sc
 ;version="3.0.12";uses:="groovyjarjarasm.asm,org.codehaus.groovy.ast,
 org.codehaus.groovy.ast.expr,org.codehaus.groovy.ast.stmt,org.codehau
 s.groovy.classgen,org.codehaus.groovy.classgen.asm",org.codehaus.groo
 vy.classgen.asm.util;version="3.0.12";uses:="groovyjarjarasm.asm,groo
 vyjarjarasm.asm.util,org.codehaus.groovy.ast",org.codehaus.groovy.con
 trol;version="3.0.12";uses:="groovy.lang,groovyjarjarantlr,groovyjarj
 arasm.asm,javax.tools,org.codehaus.groovy,org.codehaus.groovy.ast,org
 .codehaus.groovy.ast.expr,org.codehaus.groovy.ast.stmt,org.codehaus.g
 roovy.classgen,org.codehaus.groovy.control.customizers,org.codehaus.g
 roovy.control.io,org.codehaus.groovy.control.messages,org.codehaus.gr
 oovy.syntax,org.codehaus.groovy.tools",org.codehaus.groovy.control.cu
 stomizers;version="3.0.12";uses:="groovy.lang,groovy.transform,org.co
 dehaus.groovy.ast,org.codehaus.groovy.ast.expr,org.codehaus.groovy.as
 t.stmt,org.codehaus.groovy.classgen,org.codehaus.groovy.control,org.c
 odehaus.groovy.runtime,org.codehaus.groovy.syntax,org.codehaus.groovy
 .transform",org.codehaus.groovy.control.customizers.builder;version="
 3.0.12";uses:="groovy.lang,groovy.transform,groovy.util,org.codehaus.
 groovy.control,org.codehaus.groovy.control.customizers",org.codehaus.
 groovy.control.io;version="3.0.12";uses:="org.codehaus.groovy.control
 ",org.codehaus.groovy.control.messages;version="3.0.12";uses:="org.co
 dehaus.groovy.control,org.codehaus.groovy.syntax",org.codehaus.groovy
 .plugin;version="3.0.12";uses:="org.apache.groovy.plugin",org.codehau
 s.groovy.reflection;version="3.0.12";uses:="groovy.lang,groovyjarjara
 sm.asm,org.codehaus.groovy.runtime.callsite,org.codehaus.groovy.util"
 ,org.codehaus.groovy.reflection.android;version="3.0.12",org.codehaus
 .groovy.reflection.stdclasses;version="3.0.12";uses:="groovy.lang,org
 .codehaus.groovy.reflection",org.codehaus.groovy.reflection.v7;versio
 n="3.0.12";uses:="org.codehaus.groovy.reflection",org.codehaus.groovy
 .runtime;version="3.0.12";uses:="groovy.io,groovy.lang,groovy.lang.gr
 oovydoc,groovy.transform,groovy.transform.stc,groovy.util,groovyjarja
 rasm.asm,javax.swing,javax.swing.table,javax.swing.tree,org.apache.gr
 oovy.lang.annotation,org.codehaus.groovy.reflection,org.codehaus.groo
 vy.runtime.metaclass,org.codehaus.groovy.runtime.wrappers,org.codehau
 s.groovy.util,org.w3c.dom",org.codehaus.groovy.runtime.callsite;versi
 on="3.0.12";uses:="groovy.lang,groovyjarjarasm.asm,org.codehaus.groov
 y.reflection",org.codehaus.groovy.runtime.dgmimpl;version="3.0.12";us
 es:="groovy.lang,org.codehaus.groovy.reflection,org.codehaus.groovy.r
 untime.callsite",org.codehaus.groovy.runtime.dgmimpl.arrays;version="
 3.0.12";uses:="groovy.lang,org.codehaus.groovy.reflection,org.codehau
 s.groovy.runtime.callsite",org.codehaus.groovy.runtime.m12n;version="
 3.0.12";uses:="groovy.lang",org.codehaus.groovy.runtime.memoize;versi
 on="3.0.12";uses:="groovy.lang",org.codehaus.groovy.runtime.metaclass
 ;version="3.0.12";uses:="groovy.lang,org.codehaus.groovy.ast,org.code
 haus.groovy.reflection,org.codehaus.groovy.runtime,org.codehaus.groov
 y.runtime.callsite,org.codehaus.groovy.runtime.m12n,org.codehaus.groo
 vy.util",org.codehaus.groovy.runtime.powerassert;version="3.0.12";use
 s:="org.codehaus.groovy.ast.stmt,org.codehaus.groovy.control",org.cod
 ehaus.groovy.runtime.typehandling;version="3.0.12";uses:="groovy.lang
 ",org.codehaus.groovy.runtime.wrappers;version="3.0.12";uses:="groovy
 .lang",org.codehaus.groovy.syntax;version="3.0.12";uses:="groovy.lang
 ,groovyjarjarantlr.collections,org.codehaus.groovy,org.codehaus.groov
 y.ast,org.codehaus.groovy.control",org.codehaus.groovy.tools;version=
 "3.0.12";uses:="groovy.lang,groovy.transform,groovyjarjarasm.asm,groo
 vyjarjarpicocli,org.codehaus.groovy,org.codehaus.groovy.control,org.c
 odehaus.groovy.reflection,org.codehaus.groovy.runtime",org.codehaus.g
 roovy.tools.ast;version="3.0.12";uses:="groovy.lang,groovy.transform,
 org.codehaus.groovy.control,org.codehaus.groovy.transform",org.codeha
 us.groovy.tools.gse;version="3.0.12";uses:="org.codehaus.groovy.ast,o
 rg.codehaus.groovy.ast.expr,org.codehaus.groovy.ast.stmt,org.codehaus
 .groovy.control",org.codehaus.groovy.tools.javac;version="3.0.12";use
 s:="groovy.lang,javax.tools,org.codehaus.groovy.ast,org.codehaus.groo
 vy.ast.stmt,org.codehaus.groovy.control",org.codehaus.groovy.tools.sh
 ell;version="3.0.12";uses:="groovy.lang,groovy.transform,groovyjarjar
 antlr.collections,org.codehaus.groovy.antlr,org.codehaus.groovy.contr
 ol,org.codehaus.groovy.runtime,org.codehaus.groovy.tools.shell.util",
 org.codehaus.groovy.tools.shell.util;version="3.0.12";uses:="groovy.l
 ang,groovy.transform,groovy.util,org.codehaus.groovy.antlr,org.codeha
 us.groovy.antlr.parser,org.codehaus.groovy.ast,org.codehaus.groovy.as
 t.expr,org.codehaus.groovy.classgen,org.codehaus.groovy.control,org.c
 odehaus.groovy.runtime,org.codehaus.groovy.tools.shell",org.codehaus.
 groovy.transform;version="3.0.12";uses:="groovy.lang,groovy.transform
 ,groovyjarjarasm.asm,org.codehaus.groovy.ast,org.codehaus.groovy.ast.
 expr,org.codehaus.groovy.ast.stmt,org.codehaus.groovy.control,org.cod
 ehaus.groovy.runtime,org.codehaus.groovy.transform.stc",org.codehaus.
 groovy.transform.sc;version="3.0.12";uses:="org.codehaus.groovy.ast,o
 rg.codehaus.groovy.ast.expr,org.codehaus.groovy.ast.stmt,org.codehaus
 .groovy.classgen.asm,org.codehaus.groovy.control,org.codehaus.groovy.
 transform,org.codehaus.groovy.transform.stc",org.codehaus.groovy.tran
 sform.sc.transformers;version="3.0.12";uses:="groovyjarjarasm.asm,org
 .codehaus.groovy.ast,org.codehaus.groovy.ast.expr,org.codehaus.groovy
 .ast.stmt,org.codehaus.groovy.classgen.asm.sc,org.codehaus.groovy.con
 trol,org.codehaus.groovy.transform.stc",org.codehaus.groovy.transform
 .stc;version="3.0.12";uses:="groovy.lang,org.codehaus.groovy.ast,org.
 codehaus.groovy.ast.expr,org.codehaus.groovy.ast.stmt,org.codehaus.gr
 oovy.classgen,org.codehaus.groovy.control",org.codehaus.groovy.transf
 orm.tailrec;version="3.0.12";uses:="groovy.lang,groovy.transform,org.
 codehaus.groovy.ast,org.codehaus.groovy.ast.expr,org.codehaus.groovy.
 ast.stmt,org.codehaus.groovy.control,org.codehaus.groovy.runtime,org.
 codehaus.groovy.transform",org.codehaus.groovy.transform.trait;versio
 n="3.0.12";uses:="groovy.transform,org.codehaus.groovy.ast,org.codeha
 us.groovy.control,org.codehaus.groovy.transform",org.codehaus.groovy.
 util;version="3.0.12";uses:="groovy.lang,groovy.transform,groovy.util
 ,org.codehaus.groovy.runtime",org.codehaus.groovy.vmplugin;version="3
 .0.12";uses:="groovy.lang,org.codehaus.groovy.ast",org.codehaus.groov
 y.vmplugin.v5;version="3.0.12";uses:="groovy.lang,org.codehaus.groovy
 .ast,org.codehaus.groovy.runtime,org.codehaus.groovy.vmplugin",org.co
 dehaus.groovy.vmplugin.v6;version="3.0.12";uses:="org.codehaus.groovy
 .vmplugin.v5",org.codehaus.groovy.vmplugin.v7;version="3.0.12";uses:=
 "groovy.lang,org.codehaus.groovy.runtime.memoize,org.codehaus.groovy.
 vmplugin.v6",org.codehaus.groovy.vmplugin.v8;version="3.0.12";uses:="
 groovy.lang,groovy.transform.stc,org.codehaus.groovy.ast,org.codehaus
 .groovy.runtime,org.codehaus.groovy.runtime.memoize,org.codehaus.groo
 vy.vmplugin",org.codehaus.groovy.vmplugin.v9;version="3.0.12";uses:="
 groovy.lang,org.codehaus.groovy.vmplugin.v8"
Bundle-Version: 7.0.0
Bundle-Name: groovyrunner
Bundle-ClassPath: .,META-INF/lib/shared-7.0.0.jar,META-INF/lib/jira-8-
 16-features-7.0.0.jar,META-INF/lib/jira-infra-7.0.0.jar,META-INF/lib/
 code-insight-7.0.0.jar,META-INF/lib/unit-test-runner-7.0.0.jar,META-I
 NF/lib/infra-7.0.0.jar,META-INF/lib/jira-8-12-features-7.0.0.jar,META
 -INF/lib/jira-rest-model-7.0.0.jar,META-INF/lib/jfr-jira-7.0.0.jar,ME
 TA-INF/lib/rest-model-7.0.0.jar,META-INF/lib/closure-util-7.0.0.jar,M
 ETA-INF/lib/jfr-infra-7.0.0.jar,META-INF/lib/ast-extensions-7.0.0.jar
 ,META-INF/lib/ast-overrides-7.0.0.jar,META-INF/lib/groovy-dateutil-3.
 0.12.jar,META-INF/lib/ivy-2.3.0.jar,META-INF/lib/jsoup-1.15.3.jar,MET
 A-INF/lib/commons-beanutils-1.9.4.jar,META-INF/lib/atlassian-spring-s
 canner-dynamic-contexts-2.1.7.jar,META-INF/lib/groovy-ant-3.0.12.jar,
 META-INF/lib/groovy-astbuilder-3.0.12.jar,META-INF/lib/groovy-cli-pic
 ocli-3.0.12.jar,META-INF/lib/groovy-groovysh-3.0.12.jar,META-INF/lib/
 groovy-console-3.0.12.jar,META-INF/lib/groovy-datetime-3.0.12.jar,MET
 A-INF/lib/groovy-groovydoc-3.0.12.jar,META-INF/lib/groovy-docgenerato
 r-3.0.12.jar,META-INF/lib/groovy-jmx-3.0.12.jar,META-INF/lib/http-bui
 lder-0.7.3.jar,META-INF/lib/groovy-json-3.0.12.jar,META-INF/lib/groov
 y-jsr223-3.0.12.jar,META-INF/lib/groovy-nio-3.0.12.jar,META-INF/lib/g
 roovy-servlet-3.0.12.jar,META-INF/lib/groovy-sql-3.0.12.jar,META-INF/
 lib/groovy-swing-3.0.12.jar,META-INF/lib/groovy-templates-3.0.12.jar,
 META-INF/lib/groovy-xml-3.0.12.jar,META-INF/lib/spock-core-2.0-groovy
 -3.0.jar,META-INF/lib/groovy-3.0.12.jar,META-INF/lib/remote-transport
 -http-0.8-avst.jar,META-INF/lib/remote-core-0.8-avst.jar,META-INF/lib
 /snakeyaml-1.31.jar,META-INF/lib/commons-io-2.7.jar,META-INF/lib/atla
 ssian-app-cloud-migration-listener-1.0.2.jar,META-INF/lib/configurati
 on-manager-spi-tools-1.3.1.jar,META-INF/lib/atlassian-pocketknife-que
 rydsl-5.0.5.jar,META-INF/lib/querydsl-sql-4.1.4.jar,META-INF/lib/hibe
 rnate-validator-6.0.22.Final.jar,META-INF/lib/validation-api-2.0.1.Fi
 nal.jar,META-INF/lib/spring-tx-5.0.10.RELEASE.jar,META-INF/lib/ant-ju
 nit-1.10.12.jar,META-INF/lib/ant-1.10.12.jar,META-INF/lib/ant-launche
 r-1.10.12.jar,META-INF/lib/ant-antlr-1.10.12.jar,META-INF/lib/picocli
 -4.6.3.jar,META-INF/lib/qdox-1.12.1.jar,META-INF/lib/javaparser-core-
 3.24.2.jar,META-INF/lib/jline-2.14.6.jar,META-INF/lib/spring-ldap-cor
 e-2.3.2.RELEASE.jar,META-INF/lib/jjwt-0.9.1.jar,META-INF/lib/classgra
 ph-4.8.143.jar,META-INF/lib/jackson-datatype-jsr310-2.12.6.jar,META-I
 NF/lib/jackson-dataformat-xml-2.12.6.jar,META-INF/lib/jackson-module-
 jaxb-annotations-2.12.6.jar,META-INF/lib/jackson-databind-2.12.6.1.ja
 r,META-INF/lib/jackson-core-2.12.6.jar,META-INF/lib/jackson-annotatio
 ns-2.12.6.jar,META-INF/lib/jackson-datatype-jdk8-2.12.6.jar,META-INF/
 lib/javax.el-3.0.1-b10.jar,META-INF/lib/HikariCP-3.1.0.jar,META-INF/l
 ib/typetools-0.6.1.jar,META-INF/lib/cron-parser-core-3.5.jar,META-INF
 /lib/rrd4j-3.8.jar,META-INF/lib/commons-collections4-4.3.jar,META-INF
 /lib/atlassian-pocketknife-dynamic-modules-0.60.jar,META-INF/lib/scri
 ptrunner-spi-7.0.0.jar,META-INF/lib/junit-platform-engine-1.8.2.jar,M
 ETA-INF/lib/junit-platform-testkit-1.8.2.jar,META-INF/lib/junit-platf
 orm-commons-1.8.2.jar,META-INF/lib/junit-platform-reporting-1.8.2.jar
 ,META-INF/lib/junit-platform-launcher-1.8.2.jar,META-INF/lib/adaptavi
 st-analytics-core-2.2.7.jar,META-INF/lib/atlassian-plugins-osgi-javac
 onfig-0.2.0.jar,META-INF/lib/json-lib-2.3-jdk15.jar,META-INF/lib/xml-
 resolver-1.2.jar,META-INF/lib/scriptrunner-api-7.0.0.jar,META-INF/lib
 /analytics-2.1.1.jar,META-INF/lib/analytics-core-2.1.1.jar,META-INF/l
 ib/retrofit1-okhttp3-client-1.1.0.jar,META-INF/lib/retrofit-1.9.0.jar
 ,META-INF/lib/gson-2.8.9.jar,META-INF/lib/joor-0.9.5.jar,META-INF/lib
 /hamcrest-2.2.jar,META-INF/lib/annotations-20.1.0.jar,META-INF/lib/as
 m-9.1.jar,META-INF/lib/byte-buddy-1.11.0.jar,META-INF/lib/cglib-nodep
 -3.3.0.jar,META-INF/lib/objenesis-3.2.jar,META-INF/lib/woodstox-core-
 6.2.4.jar,META-INF/lib/stax2-api-4.2.1.jar,META-INF/lib/ezmorph-1.0.6
 .jar,META-INF/lib/commons-lang-2.4.jar,META-INF/lib/opentest4j-1.2.0.
 jar,META-INF/lib/okhttp-3.9.0.jar,META-INF/lib/backo-1.0.0.jar,META-I
 NF/lib/querydsl-core-4.1.4.jar,META-INF/lib/bridge-method-annotation-
 1.13.jar,META-INF/lib/assertj-core-3.20.2.jar,META-INF/lib/jakarta.xm
 l.bind-api-2.3.2.jar,META-INF/lib/jboss-logging-3.3.2.Final.jar,META-
 INF/lib/classmate-1.3.4.jar,META-INF/lib/okio-1.13.0.jar,META-INF/lib
 /mysema-commons-lang-0.2.4.jar
Atlassian-Build-Date: 2022-09-15T00:00:00+0000
Private-Package: com.acme.scriptrunner.scripts,com.acme.scriptrunner.t
 est,com.adaptavist.jsdcc,com.adaptavist.jsdcc.rest,com.adaptavist.jsd
 cc.upgrade,com.adaptavist.osgi,com.atlassian.jira.issue.customfields.
 vdi,com.sun.jndi.ldap,groovy.text,org.gradle.cli,org.gradle.wrapper,c
 om.adaptavist.scriptrunner.jfr.events.feature.fields,com.adaptavist.s
 criptrunner.jfr.events.feature.jql,com.adaptavist.scriptrunner.jfr.ev
 ents.feature.listeners,com.adaptavist.scriptrunner.jfr.events.feature
 .workflows,com.adaptavist.scriptrunner.jfr.events.indexing,com.adapta
 vist.scriptrunner.jfr,com.adaptavist.scriptrunner.jfr.events,com.adap
 tavist.scriptrunner.jfr.events.api,com.adaptavist.scriptrunner.jfr.ev
 ents.feature.builtins,com.adaptavist.scriptrunner.jfr.events.feature.
 code.insight,com.adaptavist.scriptrunner.jfr.events.feature.console,c
 om.adaptavist.scriptrunner.jfr.events.feature.fragments,com.adaptavis
 t.scriptrunner.jfr.events.feature.jobs,com.adaptavist.scriptrunner.jf
 r.events.feature.rest,com.adaptavist.scriptrunner.jfr.servlet,com.ada
 ptavist.groovy,org.apache.groovy.dateutil.extensions,fr.jayasoft.ivy.
 ant,org.apache.ivy,org.apache.ivy.ant,org.apache.ivy.core,org.apache.
 ivy.core.cache,org.apache.ivy.core.check,org.apache.ivy.core.deliver,
 org.apache.ivy.core.event,org.apache.ivy.core.event.download,org.apac
 he.ivy.core.event.publish,org.apache.ivy.core.event.resolve,org.apach
 e.ivy.core.event.retrieve,org.apache.ivy.core.install,org.apache.ivy.
 core.module.descriptor,org.apache.ivy.core.module.id,org.apache.ivy.c
 ore.module.status,org.apache.ivy.core.publish,org.apache.ivy.core.rep
 ort,org.apache.ivy.core.repository,org.apache.ivy.core.resolve,org.ap
 ache.ivy.core.retrieve,org.apache.ivy.core.search,org.apache.ivy.core
 .settings,org.apache.ivy.core.sort,org.apache.ivy.osgi.core,org.apach
 e.ivy.osgi.obr,org.apache.ivy.osgi.obr.filter,org.apache.ivy.osgi.obr
 .xml,org.apache.ivy.osgi.p2,org.apache.ivy.osgi.repo,org.apache.ivy.o
 sgi.updatesite,org.apache.ivy.osgi.updatesite.xml,org.apache.ivy.osgi
 .util,org.apache.ivy.plugins,org.apache.ivy.plugins.circular,org.apac
 he.ivy.plugins.conflict,org.apache.ivy.plugins.latest,org.apache.ivy.
 plugins.lock,org.apache.ivy.plugins.matcher,org.apache.ivy.plugins.na
 mespace,org.apache.ivy.plugins.parser,org.apache.ivy.plugins.parser.m
 2,org.apache.ivy.plugins.parser.xml,org.apache.ivy.plugins.report,org
 .apache.ivy.plugins.repository,org.apache.ivy.plugins.repository.file
 ,org.apache.ivy.plugins.repository.jar,org.apache.ivy.plugins.reposit
 ory.sftp,org.apache.ivy.plugins.repository.ssh,org.apache.ivy.plugins
 .repository.url,org.apache.ivy.plugins.repository.vfs,org.apache.ivy.
 plugins.repository.vsftp,org.apache.ivy.plugins.resolver,org.apache.i
 vy.plugins.resolver.packager,org.apache.ivy.plugins.resolver.util,org
 .apache.ivy.plugins.signer,org.apache.ivy.plugins.signer.bouncycastle
 ,org.apache.ivy.plugins.trigger,org.apache.ivy.plugins.version,org.ap
 ache.ivy.tools.analyser,org.apache.ivy.util,org.apache.ivy.util.cli,o
 rg.apache.ivy.util.extendable,org.apache.ivy.util.filter,org.apache.i
 vy.util.url,org.jsoup,org.jsoup.helper,org.jsoup.internal,org.jsoup.n
 odes,org.jsoup.parser,org.jsoup.safety,org.jsoup.select,org.apache.co
 mmons.beanutils,org.apache.commons.beanutils.converters,org.apache.co
 mmons.beanutils.expression,org.apache.commons.beanutils.locale,org.ap
 ache.commons.beanutils.locale.converters,com.atlassian.plugin.spring.
 scanner.dynamic.contexts,groovy.ant,org.codehaus.groovy.ant,org.apach
 e.groovy.ast.builder,groovy.cli.picocli,org.apache.groovy.groovysh,or
 g.apache.groovy.groovysh.antlr4,org.apache.groovy.groovysh.commands,o
 rg.apache.groovy.groovysh.completion,org.apache.groovy.groovysh.compl
 etion.antlr4,org.apache.groovy.groovysh.util,org.apache.groovy.groovy
 sh.util.antlr4,org.codehaus.groovy.tools.shell.commands,org.codehaus.
 groovy.tools.shell.completion,groovy.console,groovy.console.ui,groovy
 .console.ui.icons,groovy.console.ui.text,groovy.console.ui.view,groov
 y.inspect.swingui,groovy.ui.icons,groovy.ui.text,groovy.ui.view,org.a
 pache.groovy.datetime.extensions,org.apache.groovy.groovydoc.tools,or
 g.codehaus.groovy.groovydoc,org.codehaus.groovy.tools.groovydoc,org.c
 odehaus.groovy.tools.groovydoc.antlr4,org.codehaus.groovy.tools.groov
 ydoc.gstringTemplates,org.codehaus.groovy.tools.groovydoc.gstringTemp
 lates.classLevel,org.codehaus.groovy.tools.groovydoc.gstringTemplates
 .packageLevel,org.codehaus.groovy.tools.groovydoc.gstringTemplates.to
 pLevel,org.apache.groovy.docgenerator,groovy.jmx,groovy.jmx.builder,c
 atalog,groovy.json,org.apache.groovy.json,org.apache.groovy.json.inte
 rnal,org.codehaus.groovy.jsr223,org.apache.groovy.nio.extensions,org.
 apache.groovy.nio.runtime,groovy.servlet,groovy.sql,org.apache.groovy
 .sql.extensions,groovy.model,groovy.swing,groovy.swing.binding,groovy
 .swing.factory,groovy.swing.impl,groovy.swing.model,groovy.swing.tabl
 e,org.apache.groovy.swing.binding,org.apache.groovy.swing.extensions,
 org.codehaus.groovy.binding,groovy.text.markup,groovy.util.slurpersup
 port,groovy.xml.dom,groovy.xml.markupsupport,groovy.xml.slurpersuppor
 t,groovy.xml.streamingmarkupsupport,org.apache.groovy.xml.extensions,
 org.apache.groovy.xml.tools,org.codehaus.groovy.tools.xml,dsld,org.sp
 ockframework.builder,org.spockframework.buildsupport,org.spockframewo
 rk.compat.groovy2,org.spockframework.compiler,org.spockframework.comp
 iler.model,org.spockframework.gentyref,org.spockframework.idea,org.sp
 ockframework.lang,org.spockframework.mock,org.spockframework.mock.cod
 egen,org.spockframework.mock.constraint,org.spockframework.mock.respo
 nse,org.spockframework.mock.runtime,org.spockframework.report.log,org
 .spockframework.runtime,org.spockframework.runtime.condition,org.spoc
 kframework.runtime.extension,org.spockframework.runtime.extension.bui
 ltin,org.spockframework.runtime.model,org.spockframework.runtime.mode
 l.parallel,org.spockframework.tempdir,org.spockframework.util,org.spo
 ckframework.util.inspector,spock.config,spock.lang,spock.mock,spock.u
 til,spock.util.concurrent,spock.util.environment,spock.util.matcher,s
 pock.util.mop,spock.util.time,io.remotecontrol.transport.http,io.remo
 tecontrol,io.remotecontrol.client,io.remotecontrol.groovy,io.remoteco
 ntrol.groovy.client,io.remotecontrol.groovy.server,io.remotecontrol.r
 esult,io.remotecontrol.result.impl,io.remotecontrol.server,io.remotec
 ontrol.util,org.yaml.snakeyaml,org.yaml.snakeyaml.comments,org.yaml.s
 nakeyaml.composer,org.yaml.snakeyaml.constructor,org.yaml.snakeyaml.e
 mitter,org.yaml.snakeyaml.env,org.yaml.snakeyaml.error,org.yaml.snake
 yaml.events,org.yaml.snakeyaml.extensions.compactnotation,org.yaml.sn
 akeyaml.external.biz.base64Coder,org.yaml.snakeyaml.external.com.goog
 le.gdata.util.common.base,org.yaml.snakeyaml.introspector,org.yaml.sn
 akeyaml.nodes,org.yaml.snakeyaml.parser,org.yaml.snakeyaml.reader,org
 .yaml.snakeyaml.representer,org.yaml.snakeyaml.resolver,org.yaml.snak
 eyaml.scanner,org.yaml.snakeyaml.serializer,org.yaml.snakeyaml.tokens
 ,org.yaml.snakeyaml.util,org.apache.commons.io,org.apache.commons.io.
 comparator,org.apache.commons.io.file,org.apache.commons.io.filefilte
 r,org.apache.commons.io.function,org.apache.commons.io.input,org.apac
 he.commons.io.input.buffer,org.apache.commons.io.monitor,org.apache.c
 ommons.io.output,org.apache.commons.io.serialization,com.atlassian.mi
 gration.app,com.atlassian.migration.app.gateway,com.atlassian.migrati
 on.app.jira,com.atlassian.migration.app.listener,com.botronsoft.cmj.s
 pitools.dsl,com.botronsoft.cmj.spitools.gadget,com.botronsoft.cmj.spi
 tools.impl,com.botronsoft.cmj.spitools.impl.dsl,com.botronsoft.cmj.sp
 itools.impl.transformation,com.botronsoft.cmj.spitools.impl.transform
 ation.configuration,com.botronsoft.cmj.spitools.impl.transformation.p
 arsers,com.botronsoft.cmj.spitools.impl.transformation.producers,com.
 botronsoft.cmj.spitools.impl.transformation.transformers,com.botronso
 ft.cmj.spitools.workflow,com.atlassian.pocketknife.api.querydsl,com.a
 tlassian.pocketknife.api.querydsl.configuration,com.atlassian.pocketk
 nife.api.querydsl.schema,com.atlassian.pocketknife.api.querydsl.strea
 m,com.atlassian.pocketknife.api.querydsl.tuple,com.atlassian.pocketkn
 ife.api.querydsl.util,com.atlassian.pocketknife.internal.querydsl,com
 .atlassian.pocketknife.internal.querydsl.cache,com.atlassian.pocketkn
 ife.internal.querydsl.configuration,com.atlassian.pocketknife.interna
 l.querydsl.dialect,com.atlassian.pocketknife.internal.querydsl.listen
 er,com.atlassian.pocketknife.internal.querydsl.schema,com.atlassian.p
 ocketknife.internal.querydsl.stream,com.atlassian.pocketknife.interna
 l.querydsl.util,com.atlassian.pocketknife.internal.querydsl.util.fp,c
 om.atlassian.pocketknife.spi.querydsl,com.atlassian.pocketknife.spi.q
 uerydsl.configuration,com.querydsl.sql,com.querydsl.sql.dml,com.query
 dsl.sql.mssql,com.querydsl.sql.mysql,com.querydsl.sql.oracle,com.quer
 ydsl.sql.postgresql,com.querydsl.sql.support,com.querydsl.sql.teradat
 a,com.querydsl.sql.types,keywords,org.hibernate.validator,org.hiberna
 te.validator.cfg,org.hibernate.validator.cfg.context,org.hibernate.va
 lidator.cfg.defs,org.hibernate.validator.cfg.defs.br,org.hibernate.va
 lidator.cfg.defs.pl,org.hibernate.validator.constraints,org.hibernate
 .validator.constraints.br,org.hibernate.validator.constraints.pl,org.
 hibernate.validator.constraints.time,org.hibernate.validator.constrai
 ntvalidation,org.hibernate.validator.constraintvalidators,org.hiberna
 te.validator.engine,org.hibernate.validator.group,org.hibernate.valid
 ator.internal,org.hibernate.validator.internal.cfg.context,org.hibern
 ate.validator.internal.constraintvalidators,org.hibernate.validator.i
 nternal.constraintvalidators.bv,org.hibernate.validator.internal.cons
 traintvalidators.bv.money,org.hibernate.validator.internal.constraint
 validators.bv.notempty,org.hibernate.validator.internal.constraintval
 idators.bv.number,org.hibernate.validator.internal.constraintvalidato
 rs.bv.number.bound,org.hibernate.validator.internal.constraintvalidat
 ors.bv.number.bound.decimal,org.hibernate.validator.internal.constrai
 ntvalidators.bv.number.sign,org.hibernate.validator.internal.constrai
 ntvalidators.bv.size,org.hibernate.validator.internal.constraintvalid
 ators.bv.time,org.hibernate.validator.internal.constraintvalidators.b
 v.time.future,org.hibernate.validator.internal.constraintvalidators.b
 v.time.futureorpresent,org.hibernate.validator.internal.constraintval
 idators.bv.time.past,org.hibernate.validator.internal.constraintvalid
 ators.bv.time.pastorpresent,org.hibernate.validator.internal.constrai
 ntvalidators.hv,org.hibernate.validator.internal.constraintvalidators
 .hv.br,org.hibernate.validator.internal.constraintvalidators.hv.pl,or
 g.hibernate.validator.internal.constraintvalidators.hv.time,org.hiber
 nate.validator.internal.engine,org.hibernate.validator.internal.engin
 e.constraintdefinition,org.hibernate.validator.internal.engine.constr
 aintvalidation,org.hibernate.validator.internal.engine.groups,org.hib
 ernate.validator.internal.engine.messageinterpolation,org.hibernate.v
 alidator.internal.engine.messageinterpolation.el,org.hibernate.valida
 tor.internal.engine.messageinterpolation.parser,org.hibernate.validat
 or.internal.engine.messageinterpolation.util,org.hibernate.validator.
 internal.engine.path,org.hibernate.validator.internal.engine.resolver
 ,org.hibernate.validator.internal.engine.scripting,org.hibernate.vali
 dator.internal.engine.valueextraction,org.hibernate.validator.interna
 l.metadata,org.hibernate.validator.internal.metadata.aggregated,org.h
 ibernate.validator.internal.metadata.aggregated.rule,org.hibernate.va
 lidator.internal.metadata.core,org.hibernate.validator.internal.metad
 ata.descriptor,org.hibernate.validator.internal.metadata.facets,org.h
 ibernate.validator.internal.metadata.location,org.hibernate.validator
 .internal.metadata.provider,org.hibernate.validator.internal.metadata
 .raw,org.hibernate.validator.internal.util,org.hibernate.validator.in
 ternal.util.annotation,org.hibernate.validator.internal.util.classhie
 rarchy,org.hibernate.validator.internal.util.logging,org.hibernate.va
 lidator.internal.util.logging.formatter,org.hibernate.validator.inter
 nal.util.privilegedactions,org.hibernate.validator.internal.util.ster
 eotypes,org.hibernate.validator.internal.xml,org.hibernate.validator.
 internal.xml.config,org.hibernate.validator.internal.xml.mapping,org.
 hibernate.validator.messageinterpolation,org.hibernate.validator.meta
 data,org.hibernate.validator.parameternameprovider,org.hibernate.vali
 dator.path,org.hibernate.validator.resourceloading,org.hibernate.vali
 dator.spi.cfg,org.hibernate.validator.spi.group,org.hibernate.validat
 or.spi.resourceloading,org.hibernate.validator.spi.scripting,javax.va
 lidation,javax.validation.bootstrap,javax.validation.constraints,java
 x.validation.constraintvalidation,javax.validation.executable,javax.v
 alidation.groups,javax.validation.metadata,javax.validation.spi,javax
 .validation.valueextraction,org.springframework.dao,org.springframewo
 rk.dao.annotation,org.springframework.dao.support,org.springframework
 .jca.cci,org.springframework.jca.cci.connection,org.springframework.j
 ca.cci.core,org.springframework.jca.cci.core.support,org.springframew
 ork.jca.cci.object,org.springframework.jca.context,org.springframewor
 k.jca.endpoint,org.springframework.jca.support,org.springframework.jc
 a.work,org.springframework.transaction,org.springframework.transactio
 n.annotation,org.springframework.transaction.config,org.springframewo
 rk.transaction.event,org.springframework.transaction.interceptor,org.
 springframework.transaction.jta,org.springframework.transaction.suppo
 rt,org.apache.tools.ant.taskdefs.optional.junit,org.apache.tools.ant.
 taskdefs.optional.junit.xsl,images,org.apache.tools.ant,org.apache.to
 ols.ant.attribute,org.apache.tools.ant.dispatch,org.apache.tools.ant.
 filters,org.apache.tools.ant.filters.util,org.apache.tools.ant.helper
 ,org.apache.tools.ant.input,org.apache.tools.ant.listener,org.apache.
 tools.ant.loader,org.apache.tools.ant.property,org.apache.tools.ant.t
 askdefs,org.apache.tools.ant.taskdefs.compilers,org.apache.tools.ant.
 taskdefs.condition,org.apache.tools.ant.taskdefs.cvslib,org.apache.to
 ols.ant.taskdefs.email,org.apache.tools.ant.taskdefs.launcher,org.apa
 che.tools.ant.taskdefs.modules,org.apache.tools.ant.taskdefs.optional
 ,org.apache.tools.ant.taskdefs.optional.ccm,org.apache.tools.ant.task
 defs.optional.clearcase,org.apache.tools.ant.taskdefs.optional.depend
 ,org.apache.tools.ant.taskdefs.optional.depend.constantpool,org.apach
 e.tools.ant.taskdefs.optional.ejb,org.apache.tools.ant.taskdefs.optio
 nal.extension,org.apache.tools.ant.taskdefs.optional.extension.resolv
 ers,org.apache.tools.ant.taskdefs.optional.i18n,org.apache.tools.ant.
 taskdefs.optional.j2ee,org.apache.tools.ant.taskdefs.optional.javacc,
 org.apache.tools.ant.taskdefs.optional.javah,org.apache.tools.ant.tas
 kdefs.optional.jlink,org.apache.tools.ant.taskdefs.optional.jsp,org.a
 pache.tools.ant.taskdefs.optional.jsp.compilers,org.apache.tools.ant.
 taskdefs.optional.native2ascii,org.apache.tools.ant.taskdefs.optional
 .net,org.apache.tools.ant.taskdefs.optional.pvcs,org.apache.tools.ant
 .taskdefs.optional.script,org.apache.tools.ant.taskdefs.optional.sos,
 org.apache.tools.ant.taskdefs.optional.testing,org.apache.tools.ant.t
 askdefs.optional.unix,org.apache.tools.ant.taskdefs.optional.vss,org.
 apache.tools.ant.taskdefs.optional.windows,org.apache.tools.ant.taskd
 efs.rmic,org.apache.tools.ant.types,org.apache.tools.ant.types.condit
 ions,org.apache.tools.ant.types.mappers,org.apache.tools.ant.types.op
 tional,org.apache.tools.ant.types.optional.depend,org.apache.tools.an
 t.types.resources,org.apache.tools.ant.types.resources.comparators,or
 g.apache.tools.ant.types.resources.selectors,org.apache.tools.ant.typ
 es.selectors,org.apache.tools.ant.types.selectors.modifiedselector,or
 g.apache.tools.ant.types.spi,org.apache.tools.ant.util,org.apache.too
 ls.ant.util.depend,org.apache.tools.ant.util.facade,org.apache.tools.
 ant.util.java15,org.apache.tools.ant.util.optional,org.apache.tools.a
 nt.util.regexp,org.apache.tools.bzip2,org.apache.tools.mail,org.apach
 e.tools.tar,org.apache.tools.zip,org.apache.tools.ant.launch,picocli,
 com.thoughtworks.qdox,com.thoughtworks.qdox.ant,com.thoughtworks.qdox
 .directorywalker,com.thoughtworks.qdox.junit,com.thoughtworks.qdox.mo
 del,com.thoughtworks.qdox.model.annotation,com.thoughtworks.qdox.mode
 l.util,com.thoughtworks.qdox.parser,com.thoughtworks.qdox.parser.impl
 ,com.thoughtworks.qdox.parser.structs,com.thoughtworks.qdox.tools,com
 .github.javaparser,com.github.javaparser.ast,com.github.javaparser.as
 t.body,com.github.javaparser.ast.comments,com.github.javaparser.ast.e
 xpr,com.github.javaparser.ast.modules,com.github.javaparser.ast.nodeT
 ypes,com.github.javaparser.ast.nodeTypes.modifiers,com.github.javapar
 ser.ast.observer,com.github.javaparser.ast.stmt,com.github.javaparser
 .ast.type,com.github.javaparser.ast.validator,com.github.javaparser.a
 st.validator.language_level_validations,com.github.javaparser.ast.val
 idator.language_level_validations.chunks,com.github.javaparser.ast.va
 lidator.postprocessors,com.github.javaparser.ast.visitor,com.github.j
 avaparser.javadoc,com.github.javaparser.javadoc.description,com.githu
 b.javaparser.metamodel,com.github.javaparser.printer,com.github.javap
 arser.printer.concretesyntaxmodel,com.github.javaparser.printer.confi
 guration,com.github.javaparser.printer.lexicalpreservation,com.github
 .javaparser.printer.lexicalpreservation.changes,com.github.javaparser
 .resolution,com.github.javaparser.resolution.declarations,com.github.
 javaparser.resolution.types,com.github.javaparser.resolution.types.pa
 rametrization,com.github.javaparser.utils,jline,jline.console,jline.c
 onsole.completer,jline.console.history,jline.console.internal,jline.i
 nternal,org.fusesource.hawtjni.runtime,org.fusesource.jansi,org.fuses
 ource.jansi.internal,org.springframework,org.springframework.ldap,org
 .springframework.ldap.authentication,org.springframework.ldap.config,
 org.springframework.ldap.control,org.springframework.ldap.core,org.sp
 ringframework.ldap.core.support,org.springframework.ldap.filter,org.s
 pringframework.ldap.odm.annotations,org.springframework.ldap.odm.core
 ,org.springframework.ldap.odm.core.impl,org.springframework.ldap.odm.
 typeconversion,org.springframework.ldap.odm.typeconversion.impl,org.s
 pringframework.ldap.odm.typeconversion.impl.converters,org.springfram
 ework.ldap.pool,org.springframework.ldap.pool.factory,org.springframe
 work.ldap.pool.validation,org.springframework.ldap.pool2,org.springfr
 amework.ldap.pool2.factory,org.springframework.ldap.pool2.validation,
 org.springframework.ldap.query,org.springframework.ldap.support,org.s
 pringframework.ldap.transaction.compensating,org.springframework.ldap
 .transaction.compensating.manager,org.springframework.ldap.transactio
 n.compensating.support,org.springframework.transaction.compensating,o
 rg.springframework.transaction.compensating.support,groverconfig84910
 16507689653801,io.jsonwebtoken,io.jsonwebtoken.impl,io.jsonwebtoken.i
 mpl.compression,io.jsonwebtoken.impl.crypto,io.jsonwebtoken.lang,io.g
 ithub.classgraph,nonapi.io.github.classgraph.classloaderhandler,nonap
 i.io.github.classgraph.classpath,nonapi.io.github.classgraph.concurre
 ncy,nonapi.io.github.classgraph.fastzipfilereader,nonapi.io.github.cl
 assgraph.fileslice,nonapi.io.github.classgraph.fileslice.reader,nonap
 i.io.github.classgraph.json,nonapi.io.github.classgraph.recycler,nona
 pi.io.github.classgraph.reflection,nonapi.io.github.classgraph.scansp
 ec,nonapi.io.github.classgraph.types,nonapi.io.github.classgraph.util
 s,com.fasterxml.jackson.datatype.jsr310,com.fasterxml.jackson.datatyp
 e.jsr310.deser,com.fasterxml.jackson.datatype.jsr310.deser.key,com.fa
 sterxml.jackson.datatype.jsr310.ser,com.fasterxml.jackson.datatype.js
 r310.ser.key,com.fasterxml.jackson.datatype.jsr310.util,com.fasterxml
 .jackson.dataformat.xml,com.fasterxml.jackson.dataformat.xml.annotati
 on,com.fasterxml.jackson.dataformat.xml.deser,com.fasterxml.jackson.d
 ataformat.xml.jaxb,com.fasterxml.jackson.dataformat.xml.ser,com.faste
 rxml.jackson.dataformat.xml.util,com.fasterxml.jackson.module.jaxb,co
 m.fasterxml.jackson.module.jaxb.deser,com.fasterxml.jackson.module.ja
 xb.ser,com.fasterxml.jackson.databind,com.fasterxml.jackson.databind.
 annotation,com.fasterxml.jackson.databind.cfg,com.fasterxml.jackson.d
 atabind.deser,com.fasterxml.jackson.databind.deser.impl,com.fasterxml
 .jackson.databind.deser.std,com.fasterxml.jackson.databind.exc,com.fa
 sterxml.jackson.databind.ext,com.fasterxml.jackson.databind.introspec
 t,com.fasterxml.jackson.databind.jdk14,com.fasterxml.jackson.databind
 .json,com.fasterxml.jackson.databind.jsonFormatVisitors,com.fasterxml
 .jackson.databind.jsonschema,com.fasterxml.jackson.databind.jsontype,
 com.fasterxml.jackson.databind.jsontype.impl,com.fasterxml.jackson.da
 tabind.module,com.fasterxml.jackson.databind.node,com.fasterxml.jacks
 on.databind.ser,com.fasterxml.jackson.databind.ser.impl,com.fasterxml
 .jackson.databind.ser.std,com.fasterxml.jackson.databind.type,com.fas
 terxml.jackson.databind.util,com.fasterxml.jackson.core,com.fasterxml
 .jackson.core.async,com.fasterxml.jackson.core.base,com.fasterxml.jac
 kson.core.exc,com.fasterxml.jackson.core.filter,com.fasterxml.jackson
 .core.format,com.fasterxml.jackson.core.io,com.fasterxml.jackson.core
 .json,com.fasterxml.jackson.core.json.async,com.fasterxml.jackson.cor
 e.sym,com.fasterxml.jackson.core.type,com.fasterxml.jackson.core.util
 ,com.fasterxml.jackson.annotation,com.fasterxml.jackson.datatype.jdk8
 ,com.sun.el,com.sun.el.lang,com.sun.el.parser,com.sun.el.stream,com.s
 un.el.util,javax.el,com.zaxxer.hikari,com.zaxxer.hikari.hibernate,com
 .zaxxer.hikari.metrics,com.zaxxer.hikari.metrics.dropwizard,com.zaxxe
 r.hikari.metrics.micrometer,com.zaxxer.hikari.metrics.prometheus,com.
 zaxxer.hikari.pool,com.zaxxer.hikari.util,net.jodah.typetools,net.red
 hogs.cronparser,net.redhogs.cronparser.builder,com.tomgibara.crinch.h
 ashing,eu.bengreen.data.utility,org.rrd4j,org.rrd4j.core,org.rrd4j.co
 re.jrrd,org.rrd4j.core.timespec,org.rrd4j.data,org.rrd4j.graph,org.ap
 ache.commons.collections4,org.apache.commons.collections4.bag,org.apa
 che.commons.collections4.bidimap,org.apache.commons.collections4.coll
 ection,org.apache.commons.collections4.comparators,org.apache.commons
 .collections4.functors,org.apache.commons.collections4.iterators,org.
 apache.commons.collections4.keyvalue,org.apache.commons.collections4.
 list,org.apache.commons.collections4.map,org.apache.commons.collectio
 ns4.multimap,org.apache.commons.collections4.multiset,org.apache.comm
 ons.collections4.properties,org.apache.commons.collections4.queue,org
 .apache.commons.collections4.sequence,org.apache.commons.collections4
 .set,org.apache.commons.collections4.splitmap,org.apache.commons.coll
 ections4.trie,org.apache.commons.collections4.trie.analyzer,com.atlas
 sian.pocketknife.api.lifecycle.modules,com.atlassian.pocketknife.inte
 rnal.lifecycle.modules,com.atlassian.pocketknife.internal.lifecycle.m
 odules.utils,org.junit.platform.engine,org.junit.platform.engine.disc
 overy,org.junit.platform.engine.reporting,org.junit.platform.engine.s
 upport.config,org.junit.platform.engine.support.descriptor,org.junit.
 platform.engine.support.discovery,org.junit.platform.engine.support.f
 ilter,org.junit.platform.engine.support.hierarchical,org.junit.platfo
 rm.testkit.engine,org.junit.platform.commons,org.junit.platform.commo
 ns.annotation,org.junit.platform.commons.function,org.junit.platform.
 commons.logging,org.junit.platform.commons.support,org.junit.platform
 .commons.util,org.junit.platform.reporting.legacy,org.junit.platform.
 reporting.legacy.xml,org.junit.platform.launcher,org.junit.platform.l
 auncher.core,org.junit.platform.launcher.listeners,org.junit.platform
 .launcher.listeners.discovery,org.junit.platform.launcher.listeners.s
 ession,org.junit.platform.launcher.tagexpression,com.adaptavist.analy
 tic,com.adaptavist.analytic.concurrent,com.adaptavist.analytic.dispat
 cher,com.adaptavist.analytic.dispatcher.config,com.adaptavist.analyti
 c.dispatcher.key,com.adaptavist.analytic.event,com.adaptavist.analyti
 c.event.customisation,com.adaptavist.analytic.event.listener,com.adap
 tavist.analytic.metadata,com.adaptavist.analytic.model,com.adaptavist
 .analytic.service,com.adaptavist.analytic.service.action,com.adaptavi
 st.analytic.service.setting,com.adaptavist.analytic.setting,com.adapt
 avist.analytic.setting.adaptavist,com.adaptavist.analytic.setting.atl
 assian,com.adaptavist.analytic.setting.persistence,com.adaptavist.ana
 lytic.setting.persistence.timeout,com.adaptavist.analytic.setting.per
 sistence.validation,com.segment.analytics,com.segment.analytics.inter
 nal,com.atlassian.plugins.osgi.javaconfig,com.atlassian.plugins.osgi.
 javaconfig.conditions,com.atlassian.plugins.osgi.javaconfig.condition
 s.product,com.atlassian.plugins.osgi.javaconfig.configs.beans,com.atl
 assian.plugins.osgi.javaconfig.configs,com.atlassian.plugins.osgi.jav
 aconfig.moduletypes,net.sf.json,net.sf.json.filters,net.sf.json.groov
 y,net.sf.json.processors,net.sf.json.regexp,net.sf.json.test,net.sf.j
 son.util,net.sf.json.xml,org.apache.env,org.apache.xml.resolver,org.a
 pache.xml.resolver.apps,org.apache.xml.resolver.etc,org.apache.xml.re
 solver.helpers,org.apache.xml.resolver.readers,org.apache.xml.resolve
 r.tools,com.segment.analytics.gson,com.segment.analytics.http,com.seg
 ment.analytics.messages,com.jakewharton.retrofit,retrofit,retrofit.an
 droid,retrofit.appengine,retrofit.client,retrofit.converter,retrofit.
 http,retrofit.mime,com.google.gson,com.google.gson.annotations,com.go
 ogle.gson.internal,com.google.gson.internal.bind,com.google.gson.inte
 rnal.bind.util,com.google.gson.internal.reflect,com.google.gson.inter
 nal.sql,com.google.gson.reflect,com.google.gson.stream,org.joor,org.h
 amcrest,org.hamcrest.beans,org.hamcrest.collection,org.hamcrest.compa
 rator,org.hamcrest.core,org.hamcrest.internal,org.hamcrest.io,org.ham
 crest.number,org.hamcrest.object,org.hamcrest.text,org.hamcrest.xml,o
 rg.intellij.lang.annotations,org.jetbrains.annotations,org.objectweb.
 asm,org.objectweb.asm.signature,net.bytebuddy,net.bytebuddy.agent.bui
 lder,net.bytebuddy.asm,net.bytebuddy.build,net.bytebuddy.description,
 net.bytebuddy.description.annotation,net.bytebuddy.description.enumer
 ation,net.bytebuddy.description.field,net.bytebuddy.description.metho
 d,net.bytebuddy.description.modifier,net.bytebuddy.description.type,n
 et.bytebuddy.dynamic,net.bytebuddy.dynamic.loading,net.bytebuddy.dyna
 mic.scaffold,net.bytebuddy.dynamic.scaffold.inline,net.bytebuddy.dyna
 mic.scaffold.subclass,net.bytebuddy.implementation,net.bytebuddy.impl
 ementation.attribute,net.bytebuddy.implementation.auxiliary,net.byteb
 uddy.implementation.bind,net.bytebuddy.implementation.bind.annotation
 ,net.bytebuddy.implementation.bytecode,net.bytebuddy.implementation.b
 ytecode.assign,net.bytebuddy.implementation.bytecode.assign.primitive
 ,net.bytebuddy.implementation.bytecode.assign.reference,net.bytebuddy
 .implementation.bytecode.collection,net.bytebuddy.implementation.byte
 code.constant,net.bytebuddy.implementation.bytecode.member,net.bytebu
 ddy.jar.asm,net.bytebuddy.jar.asm.commons,net.bytebuddy.jar.asm.signa
 ture,net.bytebuddy.matcher,net.bytebuddy.pool,net.bytebuddy.utility,n
 et.bytebuddy.utility.privilege,net.bytebuddy.utility.visitor,net.sf.c
 glib.asm,net.sf.cglib.beans,net.sf.cglib.core,net.sf.cglib.core.inter
 nal,net.sf.cglib.proxy,net.sf.cglib.reflect,net.sf.cglib.transform,ne
 t.sf.cglib.transform.impl,net.sf.cglib.util,org.objenesis,org.objenes
 is.instantiator,org.objenesis.instantiator.android,org.objenesis.inst
 antiator.annotations,org.objenesis.instantiator.basic,org.objenesis.i
 nstantiator.gcj,org.objenesis.instantiator.perc,org.objenesis.instant
 iator.sun,org.objenesis.instantiator.util,org.objenesis.strategy,com.
 ctc.wstx.api,com.ctc.wstx.cfg,com.ctc.wstx.compat,com.ctc.wstx.dom,co
 m.ctc.wstx.dtd,com.ctc.wstx.ent,com.ctc.wstx.evt,com.ctc.wstx.exc,com
 .ctc.wstx.io,com.ctc.wstx.msv,com.ctc.wstx.osgi,com.ctc.wstx.sax,com.
 ctc.wstx.shaded.msv.org_isorelax.catalog,com.ctc.wstx.shaded.msv.org_
 isorelax.dispatcher,com.ctc.wstx.shaded.msv.org_isorelax.dispatcher.i
 mpl,com.ctc.wstx.shaded.msv.org_isorelax.jaxp,com.ctc.wstx.shaded.msv
 .org_isorelax.verifier,com.ctc.wstx.shaded.msv.org_isorelax.verifier.
 impl,com.ctc.wstx.shaded.msv.org_jp_gr_xml.dom,com.ctc.wstx.shaded.ms
 v.org_jp_gr_xml.sax,com.ctc.wstx.shaded.msv.org_jp_gr_xml.xml,com.ctc
 .wstx.shaded.msv.relaxng_datatype,com.ctc.wstx.shaded.msv.relaxng_dat
 atype.helpers,com.ctc.wstx.shaded.msv.xsd_util,com.ctc.wstx.shaded.ms
 v_core.datatype,com.ctc.wstx.shaded.msv_core.datatype.regexp,com.ctc.
 wstx.shaded.msv_core.datatype.xsd,com.ctc.wstx.shaded.msv_core.dataty
 pe.xsd.datetime,com.ctc.wstx.shaded.msv_core.datatype.xsd.ngimpl,com.
 ctc.wstx.shaded.msv_core.datatype.xsd.regex,com.ctc.wstx.shaded.msv_c
 ore.driver.textui,com.ctc.wstx.shaded.msv_core.grammar,com.ctc.wstx.s
 haded.msv_core.grammar.dtd,com.ctc.wstx.shaded.msv_core.grammar.relax
 ,com.ctc.wstx.shaded.msv_core.grammar.relaxng,com.ctc.wstx.shaded.msv
 _core.grammar.relaxng.datatype,com.ctc.wstx.shaded.msv_core.grammar.t
 rex,com.ctc.wstx.shaded.msv_core.grammar.trex.typed,com.ctc.wstx.shad
 ed.msv_core.grammar.util,com.ctc.wstx.shaded.msv_core.grammar.xmlsche
 ma,com.ctc.wstx.shaded.msv_core.reader,com.ctc.wstx.shaded.msv_core.r
 eader.datatype,com.ctc.wstx.shaded.msv_core.reader.datatype.xsd,com.c
 tc.wstx.shaded.msv_core.reader.dtd,com.ctc.wstx.shaded.msv_core.reade
 r.relax,com.ctc.wstx.shaded.msv_core.reader.relax.core,com.ctc.wstx.s
 haded.msv_core.reader.relax.core.checker,com.ctc.wstx.shaded.msv_core
 .reader.trex,com.ctc.wstx.shaded.msv_core.reader.trex.classic,com.ctc
 .wstx.shaded.msv_core.reader.trex.ng,com.ctc.wstx.shaded.msv_core.rea
 der.trex.ng.comp,com.ctc.wstx.shaded.msv_core.reader.trex.typed,com.c
 tc.wstx.shaded.msv_core.reader.util,com.ctc.wstx.shaded.msv_core.read
 er.xmlschema,com.ctc.wstx.shaded.msv_core.relaxns.grammar,com.ctc.wst
 x.shaded.msv_core.relaxns.grammar.relax,com.ctc.wstx.shaded.msv_core.
 relaxns.grammar.trex,com.ctc.wstx.shaded.msv_core.relaxns.reader,com.
 ctc.wstx.shaded.msv_core.relaxns.reader.relax,com.ctc.wstx.shaded.msv
 _core.relaxns.reader.trex,com.ctc.wstx.shaded.msv_core.relaxns.verifi
 er,com.ctc.wstx.shaded.msv_core.scanner.dtd,com.ctc.wstx.shaded.msv_c
 ore.scanner.dtd.resources,com.ctc.wstx.shaded.msv_core.util,com.ctc.w
 stx.shaded.msv_core.util.xml,com.ctc.wstx.shaded.msv_core.verifier,co
 m.ctc.wstx.shaded.msv_core.verifier.identity,com.ctc.wstx.shaded.msv_
 core.verifier.jarv,com.ctc.wstx.shaded.msv_core.verifier.jaxp,com.ctc
 .wstx.shaded.msv_core.verifier.psvi,com.ctc.wstx.shaded.msv_core.veri
 fier.regexp,com.ctc.wstx.shaded.msv_core.verifier.regexp.xmlschema,co
 m.ctc.wstx.shaded.msv_core.verifier.util,com.ctc.wstx.shaded.msv_core
 .writer,com.ctc.wstx.shaded.msv_core.writer.relaxng,com.ctc.wstx.sr,c
 om.ctc.wstx.stax,com.ctc.wstx.sw,com.ctc.wstx.util,org.codehaus.stax2
 ,org.codehaus.stax2.evt,org.codehaus.stax2.io,org.codehaus.stax2.osgi
 ,org.codehaus.stax2.ri,org.codehaus.stax2.ri.dom,org.codehaus.stax2.r
 i.evt,org.codehaus.stax2.ri.typed,org.codehaus.stax2.typed,org.codeha
 us.stax2.util,org.codehaus.stax2.validation,net.sf.ezmorph,net.sf.ezm
 orph.array,net.sf.ezmorph.bean,net.sf.ezmorph.object,net.sf.ezmorph.p
 rimitive,net.sf.ezmorph.test,org.apache.commons.lang,org.apache.commo
 ns.lang.builder,org.apache.commons.lang.enum,org.apache.commons.lang.
 enums,org.apache.commons.lang.exception,org.apache.commons.lang.math,
 org.apache.commons.lang.mutable,org.apache.commons.lang.text,org.apac
 he.commons.lang.time,org.opentest4j,okhttp3,okhttp3.internal,okhttp3.
 internal.cache,okhttp3.internal.cache2,okhttp3.internal.connection,ok
 http3.internal.http,okhttp3.internal.http1,okhttp3.internal.http2,okh
 ttp3.internal.io,okhttp3.internal.platform,okhttp3.internal.publicsuf
 fix,okhttp3.internal.tls,okhttp3.internal.ws,com.segment.backo,com.qu
 erydsl.core,com.querydsl.core.alias,com.querydsl.core.annotations,com
 .querydsl.core.dml,com.querydsl.core.group,com.querydsl.core.support,
 com.querydsl.core.types,com.querydsl.core.types.dsl,com.querydsl.core
 .util,com.infradna.tool.bridge_method_injector,org.assertj.core.annot
 ations,org.assertj.core.api,org.assertj.core.api.exception,org.assert
 j.core.api.filter,org.assertj.core.api.iterable,org.assertj.core.api.
 junit.jupiter,org.assertj.core.api.recursive.comparison,org.assertj.c
 ore.condition,org.assertj.core.configuration,org.assertj.core.data,or
 g.assertj.core.description,org.assertj.core.error,org.assertj.core.er
 ror.array2d,org.assertj.core.error.future,org.assertj.core.error.uri,
 org.assertj.core.extractor,org.assertj.core.groups,org.assertj.core.i
 nternal,org.assertj.core.internal.bytebuddy,org.assertj.core.internal
 .bytebuddy.agent.builder,org.assertj.core.internal.bytebuddy.asm,org.
 assertj.core.internal.bytebuddy.build,org.assertj.core.internal.byteb
 uddy.description,org.assertj.core.internal.bytebuddy.description.anno
 tation,org.assertj.core.internal.bytebuddy.description.enumeration,or
 g.assertj.core.internal.bytebuddy.description.field,org.assertj.core.
 internal.bytebuddy.description.method,org.assertj.core.internal.byteb
 uddy.description.modifier,org.assertj.core.internal.bytebuddy.descrip
 tion.type,org.assertj.core.internal.bytebuddy.dynamic,org.assertj.cor
 e.internal.bytebuddy.dynamic.loading,org.assertj.core.internal.bytebu
 ddy.dynamic.scaffold,org.assertj.core.internal.bytebuddy.dynamic.scaf
 fold.inline,org.assertj.core.internal.bytebuddy.dynamic.scaffold.subc
 lass,org.assertj.core.internal.bytebuddy.implementation,org.assertj.c
 ore.internal.bytebuddy.implementation.attribute,org.assertj.core.inte
 rnal.bytebuddy.implementation.auxiliary,org.assertj.core.internal.byt
 ebuddy.implementation.bind,org.assertj.core.internal.bytebuddy.implem
 entation.bind.annotation,org.assertj.core.internal.bytebuddy.implemen
 tation.bytecode,org.assertj.core.internal.bytebuddy.implementation.by
 tecode.assign,org.assertj.core.internal.bytebuddy.implementation.byte
 code.assign.primitive,org.assertj.core.internal.bytebuddy.implementat
 ion.bytecode.assign.reference,org.assertj.core.internal.bytebuddy.imp
 lementation.bytecode.collection,org.assertj.core.internal.bytebuddy.i
 mplementation.bytecode.constant,org.assertj.core.internal.bytebuddy.i
 mplementation.bytecode.member,org.assertj.core.internal.bytebuddy.jar
 .asm,org.assertj.core.internal.bytebuddy.jar.asm.commons,org.assertj.
 core.internal.bytebuddy.jar.asm.signature,org.assertj.core.internal.b
 ytebuddy.matcher,org.assertj.core.internal.bytebuddy.pool,org.assertj
 .core.internal.bytebuddy.utility,org.assertj.core.internal.bytebuddy.
 utility.privilege,org.assertj.core.internal.bytebuddy.utility.visitor
 ,org.assertj.core.matcher,org.assertj.core.presentation,org.assertj.c
 ore.util,org.assertj.core.util.diff,org.assertj.core.util.diff.myers,
 org.assertj.core.util.introspection,org.assertj.core.util.xml,javax.x
 ml.bind,javax.xml.bind.annotation,javax.xml.bind.annotation.adapters,
 javax.xml.bind.attachment,javax.xml.bind.helpers,javax.xml.bind.util,
 org.jboss.logging,com.fasterxml.classmate,com.fasterxml.classmate.mem
 bers,com.fasterxml.classmate.types,com.fasterxml.classmate.util,okio,
 com.mysema.commons.lang
Created-By: 1.8.0_322 (Temurin)

