com.adaptavist.jsdcc.CannedCommentsService#com.adaptavist.jsdcc.CannedCommentsService,com.atlassian.sal.api.lifecycle.LifecycleAware
com.adaptavist.jsdcc.upgrade.AddSamplesUpgradeTask01
com.atlassian.pocketknife.internal.querydsl.schema.SchemaStateProviderImpl
com.onresolve.jira.behaviours.BehaviourManagerImpl#com.onresolve.jira.behaviours.BehaviourManager
com.onresolve.jira.behaviours.BehavioursCategoryMappingServiceImpl#com.onresolve.jira.behaviours.BehavioursCategoryMappingService
com.onresolve.jira.behaviours.upgrades.NullStringRemovingUpgradeTask#com.atlassian.sal.api.upgrade.PluginUpgradeTask
com.onresolve.jira.groovy.test.infra.validation.OsgiConstraintValidatorFactory#com.onresolve.jira.groovy.test.infra.validation.OsgiConstraintValidatorFactory
com.onresolve.jira.scriptfields.ProjectPickerSearcher
com.onresolve.licensing.DynamicModulesComponentImpl
com.onresolve.licensing.InternalDynamicModulesComponentImpl#com.onresolve.licensing.InternalDynamicModulesComponent
com.onresolve.licensing.JiraLicenceChecker#com.onresolve.licensing.LicenceChecker,com.atlassian.sal.api.lifecycle.LifecycleAware
com.onresolve.scriptrunner.canned.ConfiguredObjectMapper#com.onresolve.scriptrunner.canned.ConfiguredObjectMapper
com.onresolve.scriptrunner.canned.ConfiguredValidatorFactory#com.onresolve.scriptrunner.canned.ConfiguredValidatorFactory
com.onresolve.scriptrunner.canned.jira.events.remote.JiraSerialisationManager#com.onresolve.scriptrunner.events.remote.ISerialisationManager,com.atlassian.sal.api.lifecycle.LifecycleAware
com.onresolve.scriptrunner.canned.jira.fields.editable.ScriptRunnerModuleFactory#com.onresolve.scriptrunner.canned.jira.fields.editable.ScriptRunnerModuleFactory
com.onresolve.scriptrunner.cloudmigration.CloudMigrationManager#com.atlassian.migration.app.listener.DiscoverableListener
com.onresolve.scriptrunner.db.JiraDbConnectionManager#com.onresolve.scriptrunner.db.DbConnectionManager,com.atlassian.sal.api.lifecycle.LifecycleAware
com.onresolve.scriptrunner.fields.upgrade.AddMissingScriptFieldConfigurationsUpgradeTask#com.atlassian.sal.api.upgrade.PluginUpgradeTask
com.onresolve.scriptrunner.fields.upgrade.AddMissingScriptFieldConfigurationsUpgradeTaskV2#com.atlassian.sal.api.upgrade.PluginUpgradeTask
com.onresolve.scriptrunner.fields.upgrade.NormaliseScriptFieldIdsUpgradeTask#com.atlassian.sal.api.upgrade.PluginUpgradeTask
com.onresolve.scriptrunner.fields.upgrade.ScriptFieldNaturalSearcherUpgradeTask#com.atlassian.sal.api.upgrade.PluginUpgradeTask
com.onresolve.scriptrunner.fields.upgrade.ScriptFieldNaturalSearcherUpgradeTaskV2#com.atlassian.sal.api.upgrade.PluginUpgradeTask
com.onresolve.scriptrunner.fields.upgrade.ScriptFieldsUpgradeTask#com.atlassian.sal.api.upgrade.PluginUpgradeTask
com.onresolve.scriptrunner.fragments.DefaultFragmentsLifecycle#com.atlassian.sal.api.lifecycle.LifecycleAware
com.onresolve.scriptrunner.fragments.FragmentsManager#com.onresolve.scriptrunner.fragments.IFragmentsManager
com.onresolve.scriptrunner.fragments.JiraFragmentLocationsProvider#com.onresolve.scriptrunner.fragments.AbstractFragmentLocationsProvider
com.onresolve.scriptrunner.fragments.JiraFragmentsLifecycle#com.atlassian.sal.api.lifecycle.LifecycleAware
com.onresolve.scriptrunner.fragments.upgrade.JiraFragmentsSerializationUpgradeTask#com.atlassian.sal.api.upgrade.PluginUpgradeTask
com.onresolve.scriptrunner.jira.workflow.WorkflowFunctionModuleRegistrar#com.atlassian.sal.api.lifecycle.LifecycleAware
com.onresolve.scriptrunner.runner.JiraClusterHomeLocatorServiceImpl#com.onresolve.scriptrunner.runner.diag.ClusterHomeLocatorService
com.onresolve.scriptrunner.runner.JiraEventListManager#com.onresolve.scriptrunner.runner.events.EventListManager
com.onresolve.scriptrunner.runner.JqlFunctionsManagerImpl#com.onresolve.scriptrunner.runner.JqlFunctionsManager,com.atlassian.sal.api.lifecycle.LifecycleAware
com.onresolve.scriptrunner.runner.ListenerManagerImpl#com.onresolve.scriptrunner.runner.ListenerManager,com.atlassian.sal.api.lifecycle.LifecycleAware
com.onresolve.scriptrunner.runner.RestEndpointManagerImpl#com.atlassian.sal.api.lifecycle.LifecycleAware
com.onresolve.scriptrunner.runner.ScriptRunnerImpl#com.onresolve.scriptrunner.runner.ScriptRunner,com.atlassian.sal.api.lifecycle.LifecycleAware
com.onresolve.scriptrunner.runner.diag.DiagnosticsManagerImpl#com.onresolve.scriptrunner.runner.diag.DiagnosticsManager
com.onresolve.scriptrunner.runner.diag.rrd.RrdManagerImpl
com.onresolve.scriptrunner.runner.events.DefaultEventsCompileContextProvider#com.onresolve.scriptrunner.runner.events.EventsCompileContextProvider
com.onresolve.scriptrunner.runner.events.JiraEventsCompileContextProvider#com.onresolve.scriptrunner.runner.events.EventsCompileContextProvider
com.onresolve.scriptrunner.runner.rest.common.permissions.DefaultAdminOnlyResourceFilter#com.onresolve.scriptrunner.runner.rest.common.permissions.DefaultAdminOnlyResourceFilter
com.onresolve.scriptrunner.runner.rest.common.permissions.DefaultTypeCheckingPermissions#com.onresolve.scriptrunner.runner.rest.common.permissions.TypeCheckingPermissions
com.onresolve.scriptrunner.runner.rest.common.permissions.TypeCheckingResourceFilter#com.onresolve.scriptrunner.runner.rest.common.permissions.TypeCheckingResourceFilter
com.onresolve.scriptrunner.runner.scriptPlugins.JiraDependentPluginListener#com.onresolve.scriptrunner.runner.scriptPlugins.DependentPluginListener
com.onresolve.scriptrunner.runner.startup.JiraStartupManager#com.atlassian.sal.api.lifecycle.LifecycleAware
com.onresolve.scriptrunner.runner.stc.DefaultPluginClassLoaderAccessor#com.onresolve.scriptrunner.runner.stc.PluginClassLoaderAccessor
com.onresolve.scriptrunner.runner.stc.ScriptCompileContextProvider#com.onresolve.scriptrunner.runner.stc.ScriptCompileContextProvider
com.onresolve.scriptrunner.runner.upgrade.ListenerAndEscalationServiceIdMigrationUpgradeTask
com.onresolve.scriptrunner.runner.upgrade.RemoveJiraRemoteEventListenersUpgradeTask
com.onresolve.scriptrunner.runner.upgrade.RewriteCustomScriptsUpgradeTask
com.onresolve.scriptrunner.scheduled.ScheduledScriptJobManagerImpl#com.onresolve.scriptrunner.scheduled.ScheduledScriptJobManager,com.atlassian.scheduler.JobRunner
com.onresolve.scriptrunner.scheduled.upgrade.EscalationServiceMigrationTask#com.atlassian.sal.api.upgrade.PluginUpgradeTask
com.onresolve.scriptrunner.scheduled.upgrade.JiraScriptJobManagerUpgradeTask#com.atlassian.sal.api.upgrade.PluginUpgradeTask
com.onresolve.scriptrunner.scheduled.upgrade.JiraServiceMigrationTask#com.atlassian.sal.api.upgrade.PluginUpgradeTask
com.onresolve.scriptrunner.stc.DefaultCodeInsightEnvironmentProvider#com.onresolve.scriptrunner.stc.typecheck.CodeInsightEnvironmentProvider
com.onresolve.scriptrunner.stc.JiraCodeInsightEnvironmentProvider#com.onresolve.scriptrunner.stc.typecheck.CodeInsightEnvironmentProvider
com.onresolve.spring.DefaultDynamicApplicationContextManager#com.onresolve.spring.DynamicApplicationContextManager
