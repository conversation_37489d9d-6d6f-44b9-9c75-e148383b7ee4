com.atlassian.activeobjects.spi.DataSourceProvider
com.atlassian.activeobjects.spi.TenantAwareDataSourceProvider
com.atlassian.applinks.spi.link.MutatingApplicationLinkService
com.atlassian.applinks.spi.util.TypeAccessor
com.atlassian.cache.CacheManager
com.atlassian.crowd.embedded.api.CrowdService
com.atlassian.event.api.EventPublisher
com.atlassian.jira.application.ApplicationRoleManager
com.atlassian.jira.auditing.AuditingManager
com.atlassian.jira.avatar.AvatarService
com.atlassian.jira.bc.config.ConstantsService
com.atlassian.jira.bc.favourites.FavouritesService
com.atlassian.jira.bc.filter.SearchRequestService
com.atlassian.jira.bc.group.GroupService
com.atlassian.jira.bc.issue.IssueService
com.atlassian.jira.bc.issue.comment.CommentService
com.atlassian.jira.bc.issue.comment.property.CommentPropertyService
com.atlassian.jira.bc.issue.properties.IssuePropertyService
com.atlassian.jira.bc.issue.search.SearchService
com.atlassian.jira.bc.issue.worklog.WorklogService
com.atlassian.jira.bc.portal.PortalPageService
com.atlassian.jira.bc.project.ProjectService
com.atlassian.jira.bc.project.component.ProjectComponentManager
com.atlassian.jira.bc.project.component.ProjectComponentService
com.atlassian.jira.bc.project.version.VersionService
com.atlassian.jira.bc.projectroles.ProjectRoleService
com.atlassian.jira.bc.user.UserService
com.atlassian.jira.bc.user.search.UserSearchService
com.atlassian.jira.bc.workflow.WorkflowSchemeService
com.atlassian.jira.cluster.ClusterManager
com.atlassian.jira.cluster.ClusterMessagingService
com.atlassian.jira.config.ConstantsManager
com.atlassian.jira.config.IssueTypeManager
com.atlassian.jira.config.PriorityManager
com.atlassian.jira.config.ResolutionManager
com.atlassian.jira.config.StatusCategoryManager
com.atlassian.jira.config.StatusManager
com.atlassian.jira.config.SubTaskManager
com.atlassian.jira.config.managedconfiguration.ManagedConfigurationItemService
com.atlassian.jira.config.properties.ApplicationProperties#com.atlassian.jira.config.properties.ApplicationProperties
com.atlassian.jira.config.properties.JiraProperties
com.atlassian.jira.config.util.JiraHome
com.atlassian.jira.datetime.DateTimeFormatter
com.atlassian.jira.datetime.DateTimeFormatterFactory
com.atlassian.jira.event.issue.IssueEventBundleFactory
com.atlassian.jira.event.issue.IssueEventManager
com.atlassian.jira.event.type.EventTypeManager
com.atlassian.jira.favourites.FavouritesManager
com.atlassian.jira.issue.AttachmentManager
com.atlassian.jira.issue.CustomFieldManager
com.atlassian.jira.issue.IssueFactory
com.atlassian.jira.issue.IssueManager
com.atlassian.jira.issue.RendererManager
com.atlassian.jira.issue.attachment.TemporaryWebAttachmentManager
com.atlassian.jira.issue.changehistory.ChangeHistoryManager
com.atlassian.jira.issue.comments.CommentManager
com.atlassian.jira.issue.customfields.converters.DoubleConverter
com.atlassian.jira.issue.customfields.converters.MultiGroupConverter
com.atlassian.jira.issue.customfields.converters.ProjectConverter
com.atlassian.jira.issue.customfields.converters.StringConverter
com.atlassian.jira.issue.customfields.converters.UserConverter
com.atlassian.jira.issue.customfields.manager.GenericConfigManager
com.atlassian.jira.issue.customfields.manager.OptionsManager
com.atlassian.jira.issue.customfields.persistence.CustomFieldValuePersister
com.atlassian.jira.issue.customfields.searchers.transformer.CustomFieldInputHelper
com.atlassian.jira.issue.fields.FieldManager
com.atlassian.jira.issue.fields.TextFieldCharacterLengthValidator
com.atlassian.jira.issue.fields.config.manager.FieldConfigManager
com.atlassian.jira.issue.fields.config.manager.FieldConfigSchemeManager
com.atlassian.jira.issue.fields.config.manager.IssueTypeSchemeManager
com.atlassian.jira.issue.fields.config.manager.PrioritySchemeManager
com.atlassian.jira.issue.fields.layout.field.FieldLayoutManager
com.atlassian.jira.issue.fields.rest.json.beans.JiraBaseUrls
com.atlassian.jira.issue.fields.screen.FieldScreenFactory
com.atlassian.jira.issue.fields.screen.FieldScreenManager
com.atlassian.jira.issue.fields.screen.FieldScreenRendererFactory
com.atlassian.jira.issue.fields.screen.FieldScreenSchemeManager
com.atlassian.jira.issue.fields.screen.issuetype.IssueTypeScreenSchemeManager
com.atlassian.jira.issue.index.IssueIndexManager
com.atlassian.jira.issue.label.LabelManager
com.atlassian.jira.issue.link.IssueLinkManager
com.atlassian.jira.issue.link.IssueLinkTypeManager
com.atlassian.jira.issue.link.RemoteIssueLinkManager
com.atlassian.jira.issue.search.SearchProvider
com.atlassian.jira.issue.search.SearchProviderFactory
com.atlassian.jira.issue.search.SearchRequestManager
com.atlassian.jira.issue.search.managers.SearchHandlerManager
com.atlassian.jira.issue.search.parameters.lucene.PermissionsFilterGenerator
com.atlassian.jira.issue.search.util.SearchSortUtil
com.atlassian.jira.issue.security.IssueSecurityLevelManager
com.atlassian.jira.issue.security.IssueSecuritySchemeManager
com.atlassian.jira.issue.watchers.WatcherManager
com.atlassian.jira.issue.worklog.WorklogManager
com.atlassian.jira.jql.operand.JqlOperandResolver
com.atlassian.jira.jql.parser.JqlQueryParser
com.atlassian.jira.jql.resolver.UserResolver
com.atlassian.jira.jql.util.JqlDateSupport
com.atlassian.jira.jql.util.JqlIssueKeySupport
com.atlassian.jira.jql.util.JqlIssueSupport
com.atlassian.jira.jql.util.JqlStringSupport
com.atlassian.jira.mail.CssInliner
com.atlassian.jira.notification.NotificationSchemeManager
com.atlassian.jira.ofbiz.OfBizDelegator
com.atlassian.jira.permission.PermissionSchemeManager
com.atlassian.jira.portal.PortalPageManager
com.atlassian.jira.portal.PortletConfigurationManager
com.atlassian.jira.project.ProjectManager
com.atlassian.jira.project.archiving.ArchivingLicenseCheck
com.atlassian.jira.project.version.VersionManager
com.atlassian.jira.security.GlobalPermissionManager
com.atlassian.jira.security.JiraAuthenticationContext
com.atlassian.jira.security.PermissionManager
com.atlassian.jira.security.groups.GroupManager
com.atlassian.jira.security.roles.ProjectRoleManager
com.atlassian.jira.security.websudo.InternalWebSudoManager
com.atlassian.jira.service.ServiceManager
com.atlassian.jira.sharing.ShareManager
com.atlassian.jira.template.VelocityTemplatingEngine
com.atlassian.jira.timezone.TimeZoneManager
com.atlassian.jira.timezone.TimeZoneService
com.atlassian.jira.user.UserIssueHistoryManager
com.atlassian.jira.user.UserProjectHistoryManager
com.atlassian.jira.user.UserPropertyManager
com.atlassian.jira.user.preferences.UserPreferencesManager
com.atlassian.jira.user.util.UserManager#userManager
com.atlassian.jira.user.util.UserUtil
com.atlassian.jira.util.BuildUtilsInfo
com.atlassian.jira.util.DateFieldFormat
com.atlassian.jira.util.EmailFormatter
com.atlassian.jira.util.I18nHelper
com.atlassian.jira.util.I18nHelper$BeanFactory
com.atlassian.jira.util.thread.JiraThreadLocalUtil
com.atlassian.jira.util.velocity.VelocityRequestContextFactory
com.atlassian.jira.web.FieldVisibilityManager
com.atlassian.jira.web.action.admin.translation.TranslationManager
com.atlassian.jira.web.action.issue.IssueCreationHelperBean
com.atlassian.jira.web.action.util.CalendarLanguageUtil
com.atlassian.jira.workflow.IssueWorkflowManager
com.atlassian.jira.workflow.WorkflowManager
com.atlassian.jira.workflow.WorkflowSchemeManager
com.atlassian.mail.queue.MailQueue
com.atlassian.mail.server.MailServerManager
com.atlassian.plugin.ModuleDescriptorFactory
com.atlassian.plugin.PluginAccessor
com.atlassian.plugin.PluginController
com.atlassian.plugin.event.PluginEventManager
com.atlassian.plugin.module.ModuleFactory
com.atlassian.plugin.schema.descriptor.DescribedModuleDescriptorFactory
com.atlassian.plugin.web.WebFragmentHelper
com.atlassian.plugin.web.api.DynamicWebInterfaceManager
com.atlassian.plugin.webresource.WebResourceIntegration
com.atlassian.plugin.webresource.WebResourceManager
com.atlassian.plugin.webresource.WebResourceUrlProvider
com.atlassian.sal.api.ApplicationProperties
com.atlassian.sal.api.auth.LoginUriProvider
com.atlassian.sal.api.executor.ThreadLocalDelegateExecutorFactory
com.atlassian.sal.api.features.DarkFeatureManager
com.atlassian.sal.api.license.LicenseHandler
com.atlassian.sal.api.pluginsettings.PluginSettingsFactory
com.atlassian.sal.api.rdbms.TransactionalExecutorFactory
com.atlassian.sal.api.scheduling.PluginScheduler
com.atlassian.sal.api.transaction.TransactionTemplate
com.atlassian.sal.api.user.UserManager#salUserManager
com.atlassian.sal.api.usersettings.UserSettingsService
com.atlassian.sal.api.web.context.HttpContext
com.atlassian.sal.api.websudo.WebSudoManager
com.atlassian.scheduler.SchedulerService
com.atlassian.soy.renderer.SoyTemplateRenderer
com.atlassian.upm.api.license.PluginLicenseEventRegistry
com.atlassian.upm.api.license.PluginLicenseManager
com.atlassian.velocity.VelocityManager
