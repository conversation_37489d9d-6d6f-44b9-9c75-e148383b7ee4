<?xml version="1.0" encoding="UTF-8"?>

<beans:beans xmlns:beans="http://www.springframework.org/schema/beans" xmlns:osgi="http://www.eclipse.org/gemini/blueprint/schema/blueprint" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-2.5.xsd
http://www.eclipse.org/gemini/blueprint/schema/blueprint http://www.eclipse.org/gemini/blueprint/schema/blueprint/gemini-blueprint.xsd" default-autowire="autodetect" osgi:default-timeout="30000">
  <osgi:reference id="ao">
    <osgi:interfaces>
      <beans:value>com.atlassian.activeobjects.external.ActiveObjects</beans:value>
    </osgi:interfaces>
  </osgi:reference>
</beans:beans>
