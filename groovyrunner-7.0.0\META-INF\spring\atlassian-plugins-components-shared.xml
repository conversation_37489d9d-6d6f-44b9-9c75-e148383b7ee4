<?xml version="1.0" encoding="UTF-8"?>

<beans:beans xmlns:beans="http://www.springframework.org/schema/beans" xmlns:osgi="http://www.eclipse.org/gemini/blueprint/schema/blueprint" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-2.5.xsd
http://www.eclipse.org/gemini/blueprint/schema/blueprint http://www.eclipse.org/gemini/blueprint/schema/blueprint/gemini-blueprint.xsd" default-autowire="autodetect" osgi:default-timeout="30000">

  <beans:bean id="dynamic-module-factory" autowire="default" class="com.atlassian.pocketknife.internal.lifecycle.modules.DynamicModuleDescriptorFactoryImpl"/>
  <beans:bean id="combined-module-descriptor-factory-provider" autowire="default" class="com.atlassian.pocketknife.internal.lifecycle.modules.CombinedModuleDescriptorFactoryProvider"/>
  <beans:bean id="dynamic-module-registration" autowire="default" class="com.atlassian.pocketknife.internal.lifecycle.modules.DynamicModuleRegistration"/>

  <beans:bean id="tx-processor" autowire="default" class="com.atlassian.activeobjects.external.TransactionalAnnotationProcessor"/>

</beans:beans>
