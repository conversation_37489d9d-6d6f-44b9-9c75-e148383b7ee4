<?xml version="1.0" encoding="UTF-8"?>

<beans:beans xmlns:beans="http://www.springframework.org/schema/beans" xmlns:osgi="http://www.eclipse.org/gemini/blueprint/schema/blueprint" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-2.5.xsd
http://www.eclipse.org/gemini/blueprint/schema/blueprint http://www.eclipse.org/gemini/blueprint/schema/blueprint/gemini-blueprint.xsd" default-autowire="autodetect" osgi:default-timeout="30000">
  <beans:bean id="springHostContainer" class="com.atlassian.plugin.osgi.bridge.external.SpringHostContainer"/>

  <beans:bean id="moduleType-srworkflow-function" class="com.atlassian.plugin.osgi.external.SingleModuleDescriptorFactory">
    <beans:constructor-arg index="0" ref="springHostContainer"/>
    <beans:constructor-arg index="1">
      <beans:value>srworkflow-condition</beans:value>
    </beans:constructor-arg>
    <beans:constructor-arg index="2">
      <beans:value>com.onresolve.scriptrunner.canned.jira.workflow.module.SrWorkflowConditionModuleDescriptor</beans:value>
    </beans:constructor-arg>
  </beans:bean>
  <osgi:service id="moduleType-srworkflow-function_osgiService" ref="moduleType-srworkflow-function" auto-export="interfaces"/>

  <beans:bean id="moduleType-srworkflow-validator" class="com.atlassian.plugin.osgi.external.SingleModuleDescriptorFactory">
    <beans:constructor-arg index="0" ref="springHostContainer"/>
    <beans:constructor-arg index="1">
      <beans:value>srworkflow-validator</beans:value>
    </beans:constructor-arg>
    <beans:constructor-arg index="2">
      <beans:value>com.onresolve.scriptrunner.canned.jira.workflow.module.SrWorkflowValidatorModuleDescriptor</beans:value>
    </beans:constructor-arg>
  </beans:bean>
  <osgi:service id="moduleType-srworkflow-validator_osgiService" ref="moduleType-srworkflow-validator" auto-export="interfaces"/>
</beans:beans>
