<beans:beans
        xmlns:beans="http://www.springframework.org/schema/beans"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-2.5.xsd"
        default-autowire="constructor">
    <beans:bean
            id="fieldIdValidator"
            class="com.onresolve.scriptrunner.canned.validators.FieldIdValidator"
            scope="prototype"/>
    <beans:bean
            id="missingPluginWorkflowVisitor"
            class="com.onresolve.scriptrunner.canned.jira.admin.workflowManagement.MissingPluginWorkflowVisitor"
            scope="prototype"/>
</beans:beans>