<atlassian-plugin key="com.onresolve.jira.groovy.groovyrunner" name="Adaptavist Sc<PERSON>tRunner for JIRA" plugins-version="2">
    <plugin-info>
        <description>Adaptavist ScriptRunner for JIRA</description>
        <version>7.0.0</version>
        <vendor name="Adaptavist.com Ltd" url="http://www.adaptavist.com"/>
        <param name="plugin-icon">images/adaptavistLogo.png</param>
        <param name="plugin-logo">images/SR-Enterprise.png</param>
        <!-- Start of section removed if product not Jira -->
        <param name="plugin-banner">images/SR-JiraBanner.png</param>
        <param name="min-pc-version-supported">3.6.0</param>
        <!-- End of section removed if product not Jira -->
        <param name="atlassian-licensing-enabled">true</param>
        <param name="atlassian-data-center-status">compatible</param>
        <param name="atlassian-data-center-compatible">true</param>
        <param name="read-only-access-mode-compatible">true</param>

        <!--<param name="configure.url">/plugins/servlet/scriptrunner/admin/settings</param>-->
        <param name="post.install.url">/plugins/servlet/scriptrunner/unlicensed</param>
        <!--<param name="post.update.url">/plugins/servlet/scriptrunner/admin/postUpdate</param>-->
    </plugin-info>

    <!-- add our i18n resource -->
    <resource type="i18n" name="i18n" location="scriptrunner"/>
    <resource type="i18n" name="i18n-confluence" location="scriptrunner-confluence"/>
    <resource type="i18n" name="cpage-i18n" location="cpage-i18n"/>
    <resource type="i18n" name="pageinfo-i18n" location="pageinfo-i18n"/>
    <resource type="i18n" name="labeltools-i18n" location="labeltools-i18n"/>

    <ao key="ao-stash-module" application="bitbucket">
        <entity>com.onresolve.scriptrunner.runner.diag.ScriptRunResultAO</entity>
        <entity>com.onresolve.scriptrunner.bitbucket.StashSettings</entity>
        <entity>com.adaptavist.analytic.service.setting.AnalyticSetting</entity>
        <entity>com.onresolve.scriptrunner.user.properties.db.UserPropertiesAO</entity>
        <entity>com.onresolve.scriptrunner.upgrade.UpgradeBackupAO</entity>
    </ao>

    <ao key="ao-history-module" application="jira">
        <entity>com.onresolve.scriptrunner.runner.diag.ScriptRunResultAO</entity>
        <entity>com.onresolve.scriptrunner.bitbucket.StashSettings</entity>
        <entity>com.adaptavist.analytic.service.setting.AnalyticSetting</entity>
        <entity>com.onresolve.scriptrunner.user.properties.db.UserPropertiesAO</entity>
        <entity>com.onresolve.scriptrunner.upgrade.UpgradeBackupAO</entity>
    </ao>

    <ao key="ao-confluence-module" application="confluence">
        <entity>com.onresolve.scriptrunner.runner.diag.ScriptRunResultAO</entity>
        <entity>com.onresolve.scriptrunner.bitbucket.StashSettings</entity>
        <entity>com.adaptavist.analytic.service.setting.AnalyticSetting</entity>
        <entity>com.onresolve.scriptrunner.user.properties.db.UserPropertiesAO</entity>
        <entity>com.onresolve.scriptrunner.upgrade.UpgradeBackupAO</entity>
        <entity>com.onresolve.scriptrunner.confluence.descendants.db.DescendantsAO</entity>
    </ao>

    <ao key="ao-bamboo-module" application="bamboo">
        <entity>com.onresolve.scriptrunner.runner.diag.ScriptRunResultAO</entity>
        <entity>com.onresolve.scriptrunner.bitbucket.StashSettings</entity>
        <entity>com.adaptavist.analytic.service.setting.AnalyticSetting</entity>
        <entity>com.onresolve.scriptrunner.user.properties.db.UserPropertiesAO</entity>
        <entity>com.onresolve.scriptrunner.upgrade.UpgradeBackupAO</entity>
    </ao>

    <!-- Common Section -->

    <!--this remains present because the UPM doesn't refresh the page after a licence is installed.
    The web item pointing to it is removed though.-->
    <servlet name="Unlicensed Servlet" key="unlicensed-servlet" class="com.onresolve.licensing.UnlicensedServlet">
        <description key="unlicensed-servlet.description">Servlet for the unlicensed product</description>
        <url-pattern>/scriptrunner/unlicensed</url-pattern>
    </servlet>

    <web-resource key="unlicensed-soy" name="Unlicensed ScriptRunner Soy Templates">
        <resource type="soy" name="unlicensed" location="/com/onresolve/scriptrunner/templates/soy/unlicensed.soy"/>
    </web-resource>

    <!--
        WEB RESOURCES
        These are all compiled and rendered server-side, so must be in the old soy syntax. Some may also be pre-compiled by webpack.
    -->
    <web-resource key="project-soy" name="Project Soy Templates">
        <resource type="soy" name="templates" location="/com/onresolve/scriptrunner/templates/soy/templates.soy"/>
    </web-resource>

    <web-resource key="codemirror-server" name="CodeMirror server-side templates">
        <dependency>com.onresolve.jira.groovy.groovyrunner:project-soy</dependency>
    </web-resource>

    <web-resource key="analytics" name="Segment.io iframe content">
        <resource type="soy" name="templates" location="/com/onresolve/scriptrunner/templates/soy/analytics.soy"/>
    </web-resource>

    <!--These items should be safe to include as a dependency across all products-->
    <web-resource key="web-item-response-renderer" name="Formats web-item REST responses">
        <data key="web-item-actions-data-provider" class="com.onresolve.scriptrunner.fragments.WebItemActionsDataProvider" />
    </web-resource>

    <web-resource key="adminSettingsWebResources" name="Web resources for admin pages">
        <data key="admin-settings-data-provider" class="com.onresolve.dataprovider.AdminSettingsDataProvider"/>
    </web-resource>

    <web-resource key="pluginInfoSettingsWebResources" name="Web resources for non-admin settings">
        <data key="settings-data-provider" class="com.onresolve.dataprovider.PluginInfoSettingsDataProvider"/>
    </web-resource>

    <web-resource key="vs" name="Monaco Editor">
        <resource type="download" name="vs/" location="vs/" />
    </web-resource>

    <web-resource key="vscode-oniguruma" name="Vscode Oniguruma">
        <resource type="download" name="vscode-oniguruma/" location="vscode-oniguruma/" />
    </web-resource>

    <url-reading-web-resource-transformer key="tracking-transformer" class="com.onresolve.scriptrunner.runner.util.TrackingUrlReadingTransformerFactory" />

    <!-- WORKFLOW FUNCTIONS
        Can't be in jira-specific stuff because it looks them up by name and causes very ugly errors if not present
    -->
    <workflow-function key="rungroovy-function" name="Script Post-Function [ScriptRunner]"
                       class="com.onresolve.scriptrunner.jira.workflow.ScriptWorkflowPostFunctionFactory" application="jira">
        <description>Runs a script in a post-function, or a built-in script.</description>
        <function-class>com.onresolve.jira.groovy.GroovyFunctionPlugin</function-class>
        <orderable>true</orderable>
        <unique>false</unique>
        <deletable>true</deletable>
        <default>false</default>
        <addable/>
        <resource type="velocity" name="view" location="/com/onresolve/scriptrunner/templates/vm/script-function-view.vm"/>
        <resource type="velocity" name="input-parameters" location="/com/onresolve/scriptrunner/templates/vm/workflow-function-list.vm"/>
        <resource type="velocity" name="edit-parameters" location="/com/onresolve/scriptrunner/templates/vm/workflow-function-list.vm"/>
    </workflow-function>

    <srworkflow-condition key="script-condition" name="Script Condition [ScriptRunner]"
                        class="com.onresolve.scriptrunner.jira.workflow.ScriptConditionFactory" application="jira">
        <description>Runs a script to evaluate whether to allow this action, or a built-in script.</description>
        <!--this class name mustn't change for backwards compatibility-->
        <condition-class>com.onresolve.jira.groovy.GroovyCondition</condition-class>
        <resource type="velocity" name="view" location="/com/onresolve/scriptrunner/templates/vm/script-function-view.vm"/>
        <resource type="velocity" name="input-parameters" location="/com/onresolve/scriptrunner/templates/vm/workflow-function-list.vm"/>
        <resource type="velocity" name="edit-parameters" location="/com/onresolve/scriptrunner/templates/vm/workflow-function-list.vm"/>
    </srworkflow-condition>

    <srworkflow-validator key="script-validator" name="Script Validator [ScriptRunner]"
                        class="com.onresolve.scriptrunner.jira.workflow.ScriptValidatorFactory" application="jira">
        <description>Runs a script to check validation, or a built-in script.</description>
        <!--this class name mustn't change for backwards compatibility-->
        <validator-class>com.onresolve.jira.groovy.GroovyValidator</validator-class>
        <resource type="velocity" name="view" location="/com/onresolve/scriptrunner/templates/vm/script-function-view.vm"/>
        <resource type="velocity" name="input-parameters" location="/com/onresolve/scriptrunner/templates/vm/workflow-function-list.vm"/>
        <resource type="velocity" name="edit-parameters" location="/com/onresolve/scriptrunner/templates/vm/workflow-function-list.vm"/>
    </srworkflow-validator>

    <!--
        SEARCHERS FOR THE SCRIPT FIELD
        These need to be here because of Spring init problems when enabled with dynamic modules
    -->
    <customfield-searcher key="textsearcher" name="Free Text Searcher"
                          i18n-name-key="admin.customfield.searcher.textsearcher.name"
                          class="com.atlassian.jira.issue.customfields.searchers.TextSearcher"
                          application="jira">
        <description key="admin.customfield.searcher.textsearcher.desc">Search for values using a free text search.
        </description>

        <resource type="velocity" name="search" location="templates/plugins/fields/edit-searcher/search-basictext.vm"/>
        <resource type="velocity" name="view"
                  location="templates/plugins/fields/view-searcher/view-searcher-basictext.vm"/>
        <resource type="velocity" name="label" location="templates/plugins/fields/view-searcher/label-searcher-basictext.vm"/>
        <valid-customfield-type package="com.onresolve.jira.groovy.groovyrunner" key="scripted-field"/>
    </customfield-searcher>

    <customfield-searcher key="stattabletextsearcher" name="Free Text Searcher (Stattable)"
                          class="com.onresolve.jira.scriptfields.StattableTextSearcher"
                          application="jira">
        <description key="admin.customfield.searcher.textsearcher.desc">Search for values using a free text search. Can be used in 2D gadgets.
        </description>

        <resource type="velocity" name="search" location="templates/plugins/fields/edit-searcher/search-basictext.vm"/>
        <resource type="velocity" name="view"
                  location="templates/plugins/fields/view-searcher/view-searcher-basictext.vm"/>
        <resource type="velocity" name="label" location="templates/plugins/fields/view-searcher/label-searcher-basictext.vm"/>
        <valid-customfield-type package="com.onresolve.jira.groovy.groovyrunner" key="scripted-field"/>
    </customfield-searcher>

    <customfield-searcher key="exacttextsearcher" name="Exact Text Searcher"
                          i18n-name-key="admin.customfield.searcher.exacttextsearcher.name"
                          class="com.atlassian.jira.issue.customfields.searchers.ExactTextSearcher"
                          application="jira">
        <description key="admin.customfield.searcher.exacttextsearcher.desc">Search for values exactly matching the input</description>
        <resource type="velocity" name="search" location="templates/plugins/fields/edit-searcher/search-basictext.vm"/>
        <resource type="velocity" name="view" location="templates/plugins/fields/view-searcher/view-searcher-basictext.vm"/>
        <valid-customfield-type package="com.onresolve.jira.groovy.groovyrunner" key="scripted-field"/>
    </customfield-searcher>

    <customfield-searcher key="stattableexacttextsearcher" name="Exact Text Searcher (Stattable)"
                          class="com.onresolve.jira.scriptfields.StattableExactTextSearcher"
                          application="jira">
        <description key="admin.customfield.searcher.exacttextsearcher.desc">Search for values exactly matching the input. Can be used in 2D gadgets.</description>
        <resource type="velocity" name="search" location="templates/plugins/fields/edit-searcher/search-basictext.vm"/>
        <resource type="velocity" name="view" location="templates/plugins/fields/view-searcher/view-searcher-basictext.vm"/>
        <resource type="velocity" name="label" location="templates/plugins/fields/view-searcher/label-searcher-basictext.vm"/>
        <valid-customfield-type package="com.onresolve.jira.groovy.groovyrunner" key="scripted-field"/>
    </customfield-searcher>

    <customfield-searcher key="exactnumber" name="Number Searcher"
                          i18n-name-key="admin.customfield.searcher.exactnumber.name"
                          class="com.atlassian.jira.issue.customfields.searchers.ExactNumberSearcher"
                          application="jira">
        <description key="admin.customfield.searcher.exactnumber.desc">Allow searching for a number which exactly
            matches.
        </description>

        <resource type="velocity" name="search" location="templates/plugins/fields/edit-searcher/search-basictext.vm"/>
        <resource type="velocity" name="view"
                  location="templates/plugins/fields/view-searcher/view-searcher-basictext.vm"/>
        <valid-customfield-type package="com.onresolve.jira.groovy.groovyrunner" key="scripted-field"/>
    </customfield-searcher>

    <customfield-searcher key="numberrange" name="Number range searcher"
                          i18n-name-key="admin.customfield.searcher.numberrange.name"
                          class="com.atlassian.jira.issue.customfields.searchers.NumberRangeSearcher"
                          application="jira">
        <description key="admin.customfield.searcher.numberrange.desc">Allow searching for a number that is in a given
            range
        </description>

        <resource type="velocity" name="search"
                  location="templates/plugins/fields/edit-searcher/search-number-range.vm"/>
        <resource type="velocity" name="view"
                  location="templates/plugins/fields/view-searcher/view-searcher-number-range.vm"/>
        <valid-customfield-type package="com.onresolve.jira.groovy.groovyrunner" key="scripted-field"/>
    </customfield-searcher>

    <customfield-searcher key="userpickersearcher" name="User Picker Searcher"
                          i18n-name-key="admin.customfield.searcher.userpickersearcher.name"
                          class="com.atlassian.jira.issue.customfields.searchers.UserPickerSearcher"
                          application="jira">
        <description key="admin.customfield.searcher.userpickersearcher.desc">Allow to search for a user using a
            userpicker.
        </description>
        <resource type="velocity" name="label"
                  location="templates/plugins/fields/view-searcher/label-searcher-user.vm"/>
        <resource type="velocity" name="search" location="templates/plugins/fields/edit-searcher/search-userpicker.vm"/>
        <resource type="velocity" name="view"
                  location="templates/plugins/fields/view-searcher/view-searcher-basictext.vm"/>
        <valid-customfield-type package="com.onresolve.jira.groovy.groovyrunner" key="scripted-field"/>
    </customfield-searcher>

    <!--This is duplicated to allow looking up the relevant searcher, and munging in the multi cf type indexer-->
    <customfield-searcher key="multiuserpickersearcher" name="Multi User Picker Searcher"
                          class="com.atlassian.jira.issue.customfields.searchers.UserPickerSearcher"
                          application="jira">
        <description key="admin.customfield.searcher.userpickersearcher.desc">Allow to search for a user using a
            userpicker.
        </description>
        <resource type="velocity" name="label"
                  location="templates/plugins/fields/view-searcher/label-searcher-user.vm"/>
        <resource type="velocity" name="search" location="templates/plugins/fields/edit-searcher/search-userpicker.vm"/>
        <resource type="velocity" name="view"
                  location="templates/plugins/fields/view-searcher/view-searcher-basictext.vm"/>
        <valid-customfield-type package="com.onresolve.jira.groovy.groovyrunner" key="scripted-field"/>
    </customfield-searcher>

    <customfield-searcher key="grouppickersearcher" name="Group Picker Searcher"
                          class="com.onresolve.jira.scriptfields.GroupSearcher"
                          application="jira">
        <description key="admin.customfield.searcher.grouppickersearcher.desc">Allow to search for a group using a group picker.</description>
        <resource type="velocity" name="label" location="templates/plugins/fields/view-searcher/label-searcher-group.vm"/>
        <resource type="velocity" name="search" location="templates/plugins/fields/edit-searcher/search-grouppicker.vm"/>
        <resource type="velocity" name="view" location="templates/plugins/fields/view-searcher/view-searcher-grouppicker.vm"/>
        <valid-customfield-type package="com.onresolve.jira.groovy.groovyrunner" key="scripted-field"/>
    </customfield-searcher>

    <!--This is duplicated to allow looking up the relevant searcher, and munging in the multi cf type indexer-->
    <customfield-searcher key="multigrouppickersearcher" name="Multi Group Picker Searcher"
                          class="com.onresolve.jira.scriptfields.GroupSearcher"
                          application="jira">
        <description key="admin.customfield.searcher.multigrouppickersearcher.desc">Allow to search for a group using a group picker.</description>
        <resource type="velocity" name="label" location="templates/plugins/fields/view-searcher/label-searcher-group.vm"/>
        <resource type="velocity" name="search" location="templates/plugins/fields/edit-searcher/search-grouppicker.vm"/>
        <resource type="velocity" name="view" location="templates/plugins/fields/view-searcher/view-searcher-grouppicker.vm"/>
        <valid-customfield-type package="com.onresolve.jira.groovy.groovyrunner" key="scripted-field"/>
    </customfield-searcher>

    <customfield-searcher key="datetimerange" name="Date Time Range picker"
                          i18n-name-key="admin.customfield.searcher.datetimerange.name"
                          class="com.atlassian.jira.issue.customfields.searchers.DateTimeRangeSearcher"
                          application="jira">
        <description key="admin.customfield.searcher.datetimerange.desc">Allow searching for a date and time that is
            between two other dates and times
        </description>
        <valid-customfield-type package="com.onresolve.jira.groovy.groovyrunner" key="scripted-field"/>
    </customfield-searcher>

    <customfield-searcher key="durationsearcher" name="Duration Searcher"
                          class="com.onresolve.jira.scriptfields.DurationSearcher"
                          application="jira">
        <description key="admin.customfield.searcher.textsearcher.desc">Search for date periods.
        </description>

        <resource type="velocity" name="search" location="templates/plugins/fields/edit-searcher/search-basictext.vm"/>
        <resource type="velocity" name="view"
                  location="templates/plugins/fields/view-searcher/view-searcher-basictext.vm"/>
        <valid-customfield-type package="com.onresolve.jira.groovy.groovyrunner" key="scripted-field"/>
    </customfield-searcher>

    <customfield-searcher key="issuekeysearcher" name="Issue Key Searcher"
                          class="com.onresolve.jira.scriptfields.IssueKeySearcher"
                          application="jira">
        <description key="admin.customfield.searcher.textsearcher.desc">Search a custom field by issue key.
        </description>

        <resource type="velocity" name="search" location="templates/plugins/fields/edit-searcher/search-basictext.vm"/>
        <resource type="velocity" name="view"
                  location="templates/plugins/fields/view-searcher/view-searcher-basictext.vm"/>
        <valid-customfield-type package="com.onresolve.jira.groovy.groovyrunner" key="scripted-field"/>
    </customfield-searcher>

    <customfield-searcher key="versionsearcher" name="Version Searcher"
                          class="com.onresolve.jira.scriptfields.VersionSearcher"
                          application="jira">
        <description key="admin.customfield.searcher.versionsearcher.desc">
            Allow to search for versions using a dropdown list.
        </description>

        <resource type="velocity" name="search" location="templates/plugins/fields/edit/edit-version.vm"/>
        <resource type="velocity" name="view"
                  location="templates/plugins/fields/view-searcher/view-searcher-version.vm"/>
        <resource type="velocity" name="label"
                  location="templates/plugins/fields/view-searcher/label-searcher-version.vm"/>
        <valid-customfield-type package="com.onresolve.jira.groovy.groovyrunner" key="scripted-field"/>
    </customfield-searcher>

    <customfield-searcher key="projectsearcher" name="Project Searcher"
                          i18n-name-key="admin.customfield.searcher.projectsearcher.name"
                          class="com.onresolve.jira.scriptfields.ProjectPickerSearcher"
                          application="jira">
        <description key="admin.customfield.searcher.projectsearcher.desc">Allow to search for a projects.</description>
        <resource type="velocity" name="search" location="templates/plugins/fields/edit/edit-project.vm"/>
        <resource type="velocity" name="view"
                  location="templates/plugins/fields/view-searcher/view-searcher-project.vm"/>
        <resource type="velocity" name="label"
                  location="templates/plugins/fields/view-searcher/label-searcher-project.vm"/>
        <valid-customfield-type package="com.onresolve.jira.groovy.groovyrunner" key="scripted-field"/>
    </customfield-searcher>

    <entity-search-extractor key="commentVisibilitySearchExtractor"
                             class="com.onresolve.jira.groovy.jql.CommentVisibilitySearchExtractor"
                             application="jira">
        <description>Search for comment visibility property.</description>
    </entity-search-extractor>
    <entity-search-extractor key="commentCreationSortabilitySearchExtractor"
                             class="com.onresolve.jira.groovy.jql.CommentCreationSortabilitySearchExtractor"
                             application="jira">
        <description>Index comment creation for sorting.</description>
    </entity-search-extractor>

    <entity-search-extractor key="lastCommentSearchExtractor"
                             class="com.onresolve.jira.groovy.jql.LastCommentSearchExtractor"
                             application="jira">
        <description>Search for last comments.</description>
    </entity-search-extractor>

    <!--JQL FUNCTIONS-->
    <customfield-type key="jqlFunctionsCustomFieldType"
                      name="JQL Functions Customfield Type"
                      class="com.onresolve.jira.groovy.jql.IndexingCustomField"
                      managed-access-level="locked"
                      managed-description-key="item.script.runner.jql.field.description"
                      application="jira">
        <description>This custom field is added only to support JQL functions - do not add.</description>
        <!--<resource type="velocity" name="column-view" location="templates/col-attachment.vm" />-->
    </customfield-type>

    <customfield-searcher key="jqlFunctionsSearcher" name="JQL Functions Searcher"
                          class="com.onresolve.jira.groovy.jql.ScriptFunctionSearcher" application="jira">
        <description>Search for values using a free text search.</description>
        <resource type="velocity" name="search" location="templates/customfield/search-issue-function.vm"/>
        <valid-customfield-type package="com.onresolve.jira.groovy.groovyrunner" key="jqlFunctionsCustomFieldType"/>
    </customfield-searcher>

    <macro-metadata-provider key="scriptMacroMetadataProvider" class="com.onresolve.scriptrunner.confluence.macro.ScriptMacroMetadataProvider" application="confluence" />

    <!-- Bitbucket hooks and merge checks need to be registered even unlicensed to reject operations when unlicensed -->
    <repository-hook key="script-receive-hook" name="Scriptable pre-receive global hook" configurable="false"
                     class="com.onresolve.scriptrunner.bitbucket.ScriptRunnerPreHook"
                     application="bitbucket">
        <description>ScriptRunner pre-receive hook</description>
    </repository-hook>

    <repository-merge-check key="scriptedMergeCheck" configurable="false"
                            class="com.onresolve.scriptrunner.bitbucket.ScriptRunnerMergeCheck"
                            application="bitbucket"/>

    <repository-merge-check key="mandatoryReviewersMergeCheck" configurable="false"
                            class="com.onresolve.scriptrunner.bitbucket.merge.MandatoryReviewersMergeCheck"
                            application="bitbucket" />

    <repository-hook key="mirrored-repos-script-receive-hook" name="Mirrored Repos Push Blocker" configurable="false"
                     class="com.onresolve.scriptrunner.bitbucket.MirroredRepoPreReceiveBlocker"
                     application="bitbucket">
        <description>Mirrored Repos Push Blocker</description>
    </repository-hook>

    <!-- Configuration Manager SPI Handler Packages -->
    <configurationManagerSpiHandler
            application="jira"
            key="configuration-manager-spi-handler-packages">
        <package>com.onresolve.scriptrunner.appfirecm.handlers</package>
    </configurationManagerSpiHandler>

    <servlet-filter name="JFR Servlet Filter" key="jfrFilter" class="com.adaptavist.scriptrunner.jfr.servlet.FlightRecordingFilter" location="before-login" weight="100">
        <description>Generates JFR events for HTTP requests to ScriptRunner endpoints</description>
        <url-pattern>/rest/scriptrunner/*</url-pattern>

        <dispatcher>REQUEST</dispatcher>
        <dispatcher>FORWARD</dispatcher>
    </servlet-filter>

</atlassian-plugin>
