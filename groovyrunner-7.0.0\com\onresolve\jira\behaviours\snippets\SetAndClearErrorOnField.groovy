package com.onresolve.jira.behaviours.snippets

// tag::ex1[]
// Use this snippet if setting a value in one field always requires the user to enter a value in another field
// Change the 'Other Field Name' to the name of the field that should be required if this one has a value.

def currentField = getFieldById(getFieldChanged()) // field this behaviour script is defined on
def otherField = getFieldByName('Other Field Name')
def errorMessage = 'You must also populate this field!'

// set errors to ensure dependant fields are populated
if (currentField.value && !otherField.value) {
    currentField.clearError()
    otherField.setError(errorMessage)
} else if (!currentField.value && otherField.value) {
    otherField.clearError()
    currentField.setError(errorMessage)
} else {
    otherField.clearError()
    currentField.clearError()
}
// end::ex1[]
