package com.onresolve.jira.behaviours.snippets

// tag::ex1[]
if (underlyingIssue) {
    return // do not set a default value if the issue already exists
}

def field = getFieldByName('Description')
def defaultValue = """\
        h2. Renewal Information
        * Confirm Company Name:
        * Confirm Existing License:
        * Confirm Number of Users:
        * Confirm Type of License:
         
        h3. Notes
        Provide any notes on renewal. Copy/pate from proposals and email correspondence as needed.
 
        h3. Final Actions
        * Update Jira Issue with appropriate information.
        * Assign issue to Licensing lead for approval.
    """.stripIndent()

if (!field.formValue) { // set the description if the form field value is empty to avoid overwriting user input
    field.setFormValue(defaultValue)
}
// end::ex1[]
