package com.onresolve.jira.console.snippets

import com.atlassian.jira.component.ComponentAccessor

def issueManager = ComponentAccessor.issueManager
def commentManager = ComponentAccessor.commentManager

def issueKey = 'TEST-1'

def issue = issueManager.getIssueObject(issueKey)
assert issue: "Could not find issue with key $issueKey"

def comment = commentManager.getLastComment(issue)

if (comment) {
    comment.body
} else {
    'No comments'
}