package com.onresolve.jira.console.snippets

import com.atlassian.jira.component.ComponentAccessor
import com.atlassian.jira.event.type.EventDispatchOption

def issueService = ComponentAccessor.issueService
def issueManager = ComponentAccessor.issueManager

def user = ComponentAccessor.jiraAuthenticationContext.loggedInUser

def issueKey = 'TEST-1'

def issue = issueManager.getIssueObject(issueKey)
assert issue: "Could not find issue with key $issueKey"

def inputParams = issueService.newIssueInputParameters().tap {
    it.setSummary('Hello ScriptRunner!')
}

def validation = issueService.validateUpdate(user, issue.id, inputParams)

if (validation.isValid()) {
    def issueResult = issueService.update(user, validation, EventDispatchOption.ISSUE_UPDATED, false)
    if (issueResult.isValid()) {
        "${issue.key} updated"
    } else {
        log.error(issueResult.errorCollection.errors)
        'Failed updating the issue'
    }
} else {
    log.error(validation.errorCollection.errors)
    'Validation failed.'
}