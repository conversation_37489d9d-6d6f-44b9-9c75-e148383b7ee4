package com.onresolve.scriptrunner.canned.jira.fields.editable.custom.snippets

// tag::ex1[]
import com.atlassian.jira.issue.Issue

search = { String inputValue, Issue issue -> }

getItemFromId = { String id -> }

toOption = { Map<String, String> map, Closure<String> highlight, Issue issue ->
    // return a com.onresolve.scriptrunner.canned.jira.fields.model.PickerOption
}

/*
    The following are used for rendering the view in different scenarios.
    For multiple pickers, they will called for each selected item.

    In scripts, you can replace Object with whatever type you return from getItemFromId
*/
renderItemViewHtml = { Object object -> }
renderItemColumnViewHtml = { Object object -> }
renderItemTextOnlyValue = { Object object -> }

/*
 * The following closures can be called to render all selected items, which is useful if you want to present a table for instance.
 *
 * In scripts, you can replace List<Object> with whatever type you return from getItemFromId, for example List<Version>
 *
 * If you implement the closures below, the ones above will not be called. Eg. if you implement `renderViewHtml`,
 * you do not need to implement `renderItemViewHtml`
 */
renderViewHtml = { List<Object> objects -> }
renderColumnHtml = { List<Object> objects -> }
renderTextOnlyValue = { List<Object> objects -> }

// set the maximum number of records to be shown in the drop-down
maxRecordsForSearch = 30

// String used to join multiple values for multiple pickers.
// Not used if you choose to render all items together.
multiValueDelimiter = ', '
// end::ex1[]
