package com.onresolve.scriptrunner.canned.jira.fields.editable.custom.snippets

// tag::ex1[]
import com.onresolve.scriptrunner.canned.jira.fields.model.PickerOption
import groovyx.net.http.ContentType
import groovyx.net.http.HTTPBuilder
import groovyx.net.http.Method

// Create an HTTPBuilder instance - all URLs below are relative to this base URL
HTTPBuilder getHttpBuilder() {
    new HTTPBuilder("https://restcountries.com/")
}

/*
    Search is called when the user opens the drop-down, and when they start typing.

    `inputValue` will contain what they type to search through the list. It may be an empty string.

    This should return a Collection of "objects", e.g. Version, ApplicationUser - in this case we will return a Map.
 */
search = { String inputValue ->
    httpBuilder.request(Method.GET, ContentType.JSON) {

        // If inputValue is not empty then use the search URL, otherwise get `/all`
        uri.path = inputValue ?
            "v2/name/${inputValue}" :
            'v2/all'

        // This resource allows us to choose to return only the fields that we are interested in.
        // For the drop-down we only need the name and the unique ID
        uri.query = [fields: 'name,alpha3Code']

        response.failure = { null }
    }
}

/*
    `toOption` will be called for each item returned by `search`.

    The first parameter will have the type of whatever object we are representing, in this case Map.
    You are also passed a Closure that can be used to highlight search matches in the display value.

    You must return a com.onresolve.scriptrunner.canned.jira.fields.model.PickerOption, with at least `value` and `label` keys.

    The `value` should be a String, and is the unique identifier that is stored in the database.
    We will call `getItemFromId` with this ID to convert back to the "object".

    The `label` value is used for sorting and uniqueness, and will be displayed if the `html` key is not used.

    The `html` value should be used if you want to highlight the matching search term(s) within the result, or include other HTML in the option.

    Optionally, you can include an `icon` key which should be the URL to an icon representing this option.
 */
toOption = { Map<String, String> map, Closure<String> highlight ->
    new PickerOption(
        value: map.alpha3Code,
        label: map.name,

        // The second argument `false` means to highlight the search term even in the middle of the result.
        // Without this, or if this is `true`, only matches at the beginning of words are highlighted.
        html: highlight(map.name, false),
    )
}

/*
    `getItemFromId` is called to convert from the ID back to the object you are representing, in this case a Map.

    If the object no longer exists then you should return null.
 */
getItemFromId = { String id ->
    httpBuilder.request(Method.GET, ContentType.JSON) {
        uri.path = "v2/alpha/$id"
        uri.query = [fields: 'name,region,alpha3Code']

        // In the event that a country is "deleted", this will return 404 and throw an exception - in our case we
        // want to handle that and just return `null`
        response.failure = { null }
    }
}

/*
    `renderItemViewHtml` is called to get the view HTML when viewing an issue.

    In our case we are showing the country name and, in parentheses its region
    (note that we also requested to retrieve the `region` field in `getItemFromId`),
    for example: "Algeria (Africa)".
    You can use HTML in the value to be displayed, and include other information from the object.
 */
renderItemViewHtml = { Map country ->
    "${country.name} (${country.region})"
}

/*
    `renderTextOnlyValue` is called when doing a CSV export or View -> Printable.
    You should not include HTML in output from this closure.
 */
renderItemTextOnlyValue = { Map country ->
    country.name
}
// end::ex1[]
