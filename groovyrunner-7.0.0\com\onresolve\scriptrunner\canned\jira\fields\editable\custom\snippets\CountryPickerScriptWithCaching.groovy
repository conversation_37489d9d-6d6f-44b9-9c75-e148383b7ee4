//file:noinspection GroovyUnusedAssignment
package com.onresolve.scriptrunner.canned.jira.fields.editable.custom.snippets

// tag::ex1[]
import com.google.common.cache.Cache
import com.google.common.cache.CacheBuilder
import com.google.common.cache.CacheLoader
import com.onresolve.scriptrunner.canned.jira.fields.model.PickerOption
import groovyx.net.http.ContentType
import groovyx.net.http.HTTPBuilder
import groovyx.net.http.Method

def httpBuilder = new HTTPBuilder("https://restcountries.com/")

/*
If we wished to expire entries from the cache we could add `.expireAfterWrite(14, TimeUnit.DAYS)` after .newBuilder below
 */
Cache<String, Optional<Map>> cache = CacheBuilder.newBuilder().maximumSize(250).build(new CacheLoader<String, Optional<Map>>() {
    @Override
    Optional<Map> load(String id) throws Exception {
        def country = httpBuilder.request(Method.GET, ContentType.JSON) {
            uri.path = "v2/alpha/$id"
            uri.query = [fields: 'name,region,alpha3Code']

            response.failure = { null }
        } as Map

        Optional.ofNullable(country)
    }
})

search = { String inputValue ->
    def queryParameters = [fields: 'name,region,alpha3Code']

    httpBuilder.request(Method.GET, ContentType.JSON) {
        uri.path = inputValue ?
            "v2/name/${inputValue}" :
            'v2/all'

        uri.query = queryParameters
    }
}

toOption = { Map<String, String> map, Closure highlight ->
    new PickerOption(
        value: map.alpha3Code,
        label: map.name,
        html: "${highlight(map.name, false)} <i>${map.region}</i>",
    )
}

getItemFromId = { String id ->
    // Use the cache here rather than fetch the item every time
    cache.get(id).orElse(null)
}

renderItemViewHtml = { Map country ->
    "${country.name} <i>${country.region}</i>"
}

renderItemTextOnlyValue = { Map country ->
    country.name
}
// end::ex1[]
