package com.onresolve.scriptrunner.canned.jira.fields.editable.custom.snippets

import com.onresolve.scriptrunner.canned.jira.fields.model.PickerOption
import groovyx.net.http.ContentType
import groovyx.net.http.HTTPBuilder
import groovyx.net.http.Method

HTTPBuilder getHttpBuilder() {
    new HTTPBuilder("https://api.github.com/")
}

search = { String inputValue ->
    def reposQuery = 'org:Adaptavist '
    if (inputValue) {
        reposQuery += "$inputValue in:name"
    }

    def response = httpBuilder.request(Method.GET, ContentType.JSON) {
        headers."User-Agent" = "My JIRA"
        uri.path = 'search/repositories'
        uri.query = [q: reposQuery, per_page: 30]
    } as Map

    response.items
}

getItemFromId = { String id ->
    httpBuilder.request(Method.GET, ContentType.JSON) {
        headers."User-Agent" = "My JIRA"
        uri.path = "repos/Adaptavist/$id"
    }
}

toOption = { Map<String, String> map, Closure highlight ->
    new PickerOption(
        value: map.name,
        label: map.name,
        html: "${highlight(map.name, false)}",
    )
}

renderItemViewHtml = { Map repo ->
    repo.full_name
}

renderItemTextOnlyValue = renderItemViewHtml
