package com.onresolve.scriptrunner.canned.jira.fields.editable.custom.snippets

// tag::ex1[]
import com.atlassian.jira.avatar.Avatar
import com.atlassian.jira.bc.user.search.UserSearchParams
import com.atlassian.jira.bc.user.search.UserSearchService
import com.atlassian.jira.component.ComponentAccessor
import com.atlassian.jira.user.ApplicationUser
import com.atlassian.jira.user.UserFilter
import com.onresolve.scriptrunner.canned.jira.fields.model.PickerOption

def userSearchService = ComponentAccessor.getComponent(UserSearchService)
def userManager = ComponentAccessor.userManager
def avatarService = ComponentAccessor.avatarService
def authenticationContext = ComponentAccessor.jiraAuthenticationContext
def groupManager = ComponentAccessor.groupManager

def userFilter = new UserFilter(true, null, ['jira-administrators', 'jira-system-administrators'])
def userSearchParams = new UserSearchParams.Builder().allowEmptyQuery(true).filter(userFilter).maxResults(30).build()

search = { String inputValue ->
    userSearchService.findUsers(inputValue, userSearchParams)
}

/*
 * Retrieve the user from internal storage (we store the key). Return them if they still exist, regardless of
 * their group membership.
 */
getItemFromId = { String id ->
    userManager.getUserByKey(id)
}

/*
 * The user is still "valid" if they are a member of either of the below-mentioned groups, otherwise they become "disabled".
 */
validate = { ApplicationUser user ->
    groupManager.getGroupNamesForUser(user).intersect(['jira-administrators', 'jira-system-administrators'])
}

renderItemViewHtml = { ApplicationUser user ->
    user.displayName
}

renderItemTextOnlyValue = renderItemViewHtml

toOption = { ApplicationUser user, Closure<String> highlight ->
    def remoteUser = authenticationContext.loggedInUser

    new PickerOption(
        value: user.key,
        label: user.displayName,
        html: highlight(user.displayName, false),
        icon: avatarService.getAvatarURL(remoteUser, user, Avatar.Size.SMALL)?.toString()
    )
}
// end::ex1[]
