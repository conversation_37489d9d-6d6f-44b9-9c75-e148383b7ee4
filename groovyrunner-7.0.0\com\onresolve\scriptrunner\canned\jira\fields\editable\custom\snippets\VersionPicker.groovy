package com.onresolve.scriptrunner.canned.jira.fields.editable.custom.snippets

// tag::ex1[]
import com.atlassian.jira.component.ComponentAccessor
import com.atlassian.jira.project.version.Version
import com.atlassian.jira.project.version.VersionManager
import com.onresolve.scriptrunner.canned.jira.fields.model.PickerOption
import org.apache.commons.lang3.StringUtils

def versionManager = ComponentAccessor.getComponent(VersionManager)

validate = { Version version ->
    !version.archived
}

search = { String inputValue ->
    versionManager.allVersions.findAll {
        !it.archived && StringUtils.containsIgnoreCase(it.name, inputValue)
    }
}

getItemFromId = { String id ->
    versionManager.getVersion(id.toLong())
}

toOption = { Version version, Closure highlight ->
    new PickerOption(
        value: version.id.toString(),
        label: version.name,
        html: "${highlight(version.name, false)} ${getLozenge(version)} (${version.project.key})",
    )
}

renderItemViewHtml = { Version version ->
    "$version.name ${getLozenge(version)} ($version.project.key)"
}

renderItemTextOnlyValue = { Version version ->
    version.name
}

String getLozenge(Version version) {
    if (version.isArchived()) {
        '<span class="aui-lozenge aui-lozenge-subtle">Archived</span>'
    } else if (version.released) {
        '<span class="aui-lozenge aui-lozenge-success aui-lozenge-subtle">Released</span>'
    } else {
        '<span class="aui-lozenge aui-lozenge-current aui-lozenge-subtle">Unreleased</span>'
    }
}
// end::ex1[]
