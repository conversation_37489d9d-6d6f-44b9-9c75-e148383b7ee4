package com.onresolve.scriptrunner.canned.jira.fields.editable.database.snippets

// tag::ex1[]
import com.atlassian.jira.issue.Issue
import com.onresolve.scriptrunner.canned.jira.fields.editable.database.SqlWithParameters

getSearchSql = { String inputValue, Issue issue, String originalValue ->
    // return SqlWithParameters, that will control the search SQL..., eg:
    new SqlWithParameters("select * from X where name like ? || '%'", [inputValue])
}
// end::ex1[]
