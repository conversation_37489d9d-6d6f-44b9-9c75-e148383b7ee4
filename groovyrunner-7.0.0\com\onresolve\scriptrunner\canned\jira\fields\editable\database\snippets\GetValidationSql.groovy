package com.onresolve.scriptrunner.canned.jira.fields.editable.database.snippets

// tag::ex1[]
import com.onresolve.scriptrunner.canned.jira.fields.editable.database.SqlWithParameters
import com.atlassian.jira.issue.Issue

getValidationSql = { String id, Issue issue, String originalValue ->
    // return SqlWithParameters, that will control the validation SQL..., eg:
    new SqlWithParameters("select id, name from X where name like ? || '%' and id = ?", [issue.summary ?: '', id])
}
// end::ex1[]
