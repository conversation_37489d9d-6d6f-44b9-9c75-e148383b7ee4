package com.onresolve.scriptrunner.canned.jira.fields.editable.issue.snippets

//tag::ex1[]
import com.atlassian.jira.component.ComponentAccessor
import com.atlassian.jira.issue.Issue
import com.atlassian.jira.permission.GlobalPermissionKey

def authenticationContext = ComponentAccessor.jiraAuthenticationContext
def globalPermissionManager = ComponentAccessor.globalPermissionManager

getJql = { Issue issue, String configuredJql ->

    def currentUser = authenticationContext?.loggedInUser
    def isAdmin = globalPermissionManager.hasPermission(GlobalPermissionKey.ADMINISTER, currentUser)

    if (isAdmin) {
        return ''
    } else {
        return 'reporter = currentUser()'
    }
}
//end::ex1[]
