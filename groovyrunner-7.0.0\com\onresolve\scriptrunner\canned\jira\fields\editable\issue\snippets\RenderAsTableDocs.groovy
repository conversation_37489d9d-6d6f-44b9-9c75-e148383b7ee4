package com.onresolve.scriptrunner.canned.jira.fields.editable.issue.snippets

// tag::ex1[]
import com.atlassian.jira.issue.Issue
import com.onresolve.scriptrunner.canned.util.OutputFormatter

renderViewHtml = { List<Issue> issues, Closure<Issue> hasPermission, String baseUrl ->
    OutputFormatter.markupBuilder {
        table(class: 'aui') {
            thead {
                tr {
                    td('Key')
                    td('Summary')
                    td('Description')
                }
            }
            issues.each { issue ->
                tbody {
                    tr {
                        if (hasPermission(issue)) {
                            td {
                                a(href: "${baseUrl}/browse/${issue.key}", issue.key)
                            }
                            td(issue.summary)
                            td(issue.description)
                        } else {
                            td(colspan: 3, issue.key + ' (Permission Denied)')
                        }
                    }
                }
            }
        }
    }
}
// end::ex1[]