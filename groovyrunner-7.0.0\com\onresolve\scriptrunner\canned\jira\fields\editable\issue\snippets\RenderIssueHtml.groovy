package com.onresolve.scriptrunner.canned.jira.fields.editable.issue.snippets

//tag::ex1[]
import com.atlassian.jira.component.ComponentAccessor
import com.atlassian.jira.issue.Issue
import com.onresolve.scriptrunner.canned.util.OutputFormatter

def customFieldManager = ComponentAccessor.customFieldManager

renderIssueViewHtml = { Issue issue, String baseUrl ->
    def customField = customFieldManager.getCustomFieldObjectsByName('ShortText1').first()

    OutputFormatter.markupBuilder {
        div {
            a(href: "${baseUrl}/browse/${issue.key}", issue.key)
            mkp.yield(': ')
            span(issue.getCustomFieldValue(customField))
        }
    }
}
//end::ex1[]

renderIssueColumnHtml = renderIssueViewHtml