package com.onresolve.scriptrunner.canned.jira.fields.editable.issue.snippets

//tag::ex1[]
import com.atlassian.jira.issue.Issue
import com.onresolve.scriptrunner.canned.util.OutputFormatter

getSearchFields = {
    ['issuekey', 'summary', 'priority']
}

renderOptionHtml = { String displayValue, Issue issue, Closure<String> highlight ->
    OutputFormatter.markupBuilder {
        i('Priority: ' + issue.priority?.name)
        mkp.yield(' ')
        mkp.yieldUnescaped(displayValue)
    }
}
//end::ex1[]
