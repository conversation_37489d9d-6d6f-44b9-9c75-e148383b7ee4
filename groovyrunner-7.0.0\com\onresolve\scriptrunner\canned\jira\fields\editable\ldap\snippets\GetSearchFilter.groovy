package com.onresolve.scriptrunner.canned.jira.fields.editable.ldap.snippets

// tag::ex1[]
import com.atlassian.jira.issue.Issue
import org.springframework.ldap.filter.AndFilter

getSearchFilter = { AndFilter currentFilter, Issue issue, String searchValue ->
    // return org.springframework.ldap.filter.Filter. Typically you will add to the supplied AndFilter.
    // Alternately, to search on multiple attributes, return a new filter
}
// end::ex1[]
