package com.onresolve.scriptrunner.canned.listeners.snippets

import com.atlassian.mail.queue.SingleMailQueueItem
import com.atlassian.jira.component.ComponentAccessor
import com.atlassian.jira.mail.Email

def email = new Email('<EMAIL>')
email.setSubject("Something has gone wrong in ${event.featureName}")
email.setBody("""
    Notes: ${event.notes}.
    Something has gone wrong in ${event.featureName}.
    To edit this click here: ${event.url}.
    
    Error: ${event.stackTrace}
""")

SingleMailQueueItem item = new SingleMailQueueItem(email)
ComponentAccessor.getMailQueue().addItem(item)