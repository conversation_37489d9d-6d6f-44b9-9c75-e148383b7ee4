{namespace plugin.com.onresolve.scriptrunner }

/**
* @param writeKey Segment.io write key
* @param platform name of the Atlassian platform
* @param productKeys installed product keys
* @param pluginInfo JSON string with plugin information
* @param userId unique user identification string
* @param isAnonymous is current user an anonymous user
*/
{template .analytics}
<!DOCTYPE html>
<html>
<head>
<script type="text/javascript">
  var isEnabled = true;
  var myOrigin = location.origin;
  var pageHistory = {lb} current: document.referrer, previous: document.location.href {rb};
  var referrerBase = document.referrer.substring(0, document.referrer.lastIndexOf('/'));
  var params = new URLSearchParams(document.location.search.substring(1));
  var origReferrer = params.get('origReferrer');
  var parentLocation = params.get('parentLocation');
  if (origReferrer) {lb}
    pageHistory = {lb} current: parentLocation, previous: origReferrer {rb};
  {rb}
  function receiveMessage(event) {lb}
    if (event.origin !== myOrigin) {lb}
      return;
    {rb}
    var data = event.data;
    if (typeof event.data !== 'object' || event.data == null) {lb}
      return;
    {rb}

    if (data.type === 'toggleEnabled') {lb}
      isEnabled = !isEnabled;
      return;
    {rb}
    if (!isEnabled) {lb}
      return;
    {rb}
    if (!data.properties) {lb}
      data.properties = {lb}{rb}
    {rb}
    data.properties.platform = '{$platform}';
    data.properties.isAnonymous = {$isAnonymous};
    data.properties.pluginInfo = {$pluginInfo|noAutoescape};
    if ({$productKeys|noAutoescape}.length > 0) {lb}
      data.properties.productKeys = {$productKeys|noAutoescape};
    {rb}
    if (data.type === 'page') {lb}
      updatePageHistory(data.url);
      window.analytics.page(data.url, data.properties);
    {rb} else if (data.type === 'track') {lb}
      window.analytics.track(data.action, data.properties);
    {rb}
  {rb}
  window.addEventListener("message", receiveMessage, false);

  function updatePageHistory(path) {lb}
    pageHistory = {lb} previous: pageHistory.current, current: referrerBase + path {rb};
  {rb}

  function sourceMiddleware(event) {lb}
    var currentUrl = new URL(pageHistory.current);
    var page = event.payload.obj.context.page;
    if (pageHistory.previous.includes('/scriptrunner/admin/')) {lb}
        page.referrer = pageHistory.previous;
    {rb} else if (pageHistory.previous.includes('/space_admin/')) {lb}
        page.referrer = pageHistory.previous.split('&key')[0];
    {rb} else {lb}
        page.referrer = new URL(pageHistory.previous).origin;
    {rb}
    page.url = pageHistory.current;
    page.path = currentUrl.pathname;
    page.search = currentUrl.search;
    event.next(event.payload);
  {rb}

  !function() {lb}
    var analytics=window.analytics=window.analytics||[];if(!analytics.initialize)if(analytics.invoked)window.console&&console.error&&console.error("Segment snippet included twice.");else
    {lb}
      analytics.invoked=!0;analytics.methods=["trackSubmit","trackClick","trackLink","trackForm","pageview","identify","reset","group","track","ready","alias","debug","page","once","off","on","addSourceMiddleware","addIntegrationMiddleware","setAnonymousId","addDestinationMiddleware"];analytics.factory=function(e){lb}return function(){lb}var t=Array.prototype.slice.call(arguments);t.unshift(e);analytics.push(t);return analytics{rb}{rb};for(var e=0;e<analytics.methods.length;e++){lb}var key=analytics.methods[e];analytics[key]=analytics.factory(key){rb}analytics.load=function(key,e){lb}var t=document.createElement("script");t.type="text/javascript";t.async=!0;t.src="https://cdn.segment.com/analytics.js/v1/" + key + "/analytics.min.js";var n=document.getElementsByTagName("script")[0];n.parentNode.insertBefore(t,n);analytics._loadOptions=e{rb};analytics._writeKey="{$writeKey}";analytics.SNIPPET_VERSION="4.13.2";
      analytics.addSourceMiddleware(sourceMiddleware);
      analytics.load('{$writeKey}');
      analytics.identify('{$userId}');
      parent.postMessage('sr-segment-fully-loaded', myOrigin);
    {rb}
  {rb}();
</script>
</head>
<body></body>
</html>
{/template}
