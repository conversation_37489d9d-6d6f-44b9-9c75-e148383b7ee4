{namespace plugin.com.onresolve.scriptrunner }

/**
* @param baseUrl Base URL as not provided by stash
* @param section Active section in the admin menu
* @param title Page title
* @param sectionFirstUse User's first time using section
* @param srFirstUse User's first time using SR
*/
{template .combinedAdminSection}
<html>
<head>
    <meta name="decorator" content="atl.admin">
    <meta name="admin.active.section" content="scriptrunner_section">
    <meta name="admin.active.tab" content="{$section}">
    <meta name="activeTab" content="{$section}">
    <meta name="srBaseUrl" content="{$baseUrl}">
    <meta name="sectionFirstUse" content="{$sectionFirstUse}">
    <meta name="srFirstUse" content="{$srFirstUse}">

    <title>{$title}</title>
</head>
<body>
<content tag="selectedWebItem" style="display:none">{$section}_conf</content>

<div id="admin-screens" class="adaptavist-sr"></div>

</body>
</html>
{/template}

/**
* @param baseUrl Base URL as not provided by stash
* @param section Active section in the admin menu
* @param repositorySlug Repository key
* @param projectKey Project key
* @param title Page title
*/
{template .stashRepoHooks}
<html>
<head>
    <meta name="decorator" content="bitbucket.repository.settings">
    <meta name="projectKey" content="{$projectKey}">
    <meta name="repositorySlug" content="{$repositorySlug}">
    <meta name="activeTab" content="{$section}">
    <meta name="srBaseUrl" content="{$baseUrl}">
    <title>{$title}</title>
</head>
<body>

<div id="admin-screens" class="adaptavist-sr" data-context='{lb}"project": "{$projectKey}", "repo": "{$repositorySlug}" {rb}'></div>
</body>
</html>
{/template}


/**
* @param baseUrl Base URL as not provided by stash
* @param section Active section in the admin menu
* @param projectKey Project key
* @param title Page title
*/
{template .projectLevel}
<html>
<head>
    <meta name="decorator" content="bitbucket.project.settings">
    <meta name="projectKey" content="{$projectKey}">
    <meta name="admin.active.tab" content="{$section}">
    <meta name="activeTab" content="{$section}">
    <meta name="srBaseUrl" content="{$baseUrl}">
    <title>{$title}</title>
</head>
<body>
<div id="admin-screens" class="adaptavist-sr" data-context='{lb}"project": "{$projectKey}" {rb}'></div>
</body>
</html>
{/template}



