{namespace plugin.com.onresolve.scriptrunner}

/**
* Template to be shown when the add-on is not licensed
* Can be found here: http://localhost:7990/stash/plugins/servlet/scriptrunner/unlicensed
* @param baseUrl
* @param pluginKey
* @param pluginName
*/
{template .unlicensed}
<html>
<head>
    <meta name="decorator" content="atl.admin">
    <title>ScriptRunner</title>
</head>
<body>
<h2>License Information</h2>
    <div class="aui-message aui-message-warning">
        <p class="title">
            <strong>{$pluginName} is not licensed</strong>
        </p>
        <p>The plugin will remain disabled until a valid license is entered.</p>
    </div>

    <div style="padding-top: 20px">
        <p>{$pluginName} requires a valid license to work. </p>
        <p>
            <a href="{$baseUrl}/plugins/servlet/upm?fragment=manage/{$pluginKey}">
                <button  class="aui-button aui-button-link">I want to get a license for {$pluginName}</button>
            </a>
        </p>
    </div>
</body>
</html>
{/template}

