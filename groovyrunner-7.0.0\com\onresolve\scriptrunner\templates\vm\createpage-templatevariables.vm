<html>
<head>
    #requireResource("confluence.web.resources:page-templates")
    <title>$action.getText("page.template.wizard")</title>
</head>

<body>
    #parse ( "/template/includes/actionerrors.vm" )
    #applyDecorator("root")
    #decoratorParam("helper" $action.helper)
    #decoratorParam("context" "space-pages")
    #decoratorParam("mode" "create-page")

<div class="padded">
    <div class="steptitle" style="margin-top: 10px">$action.getText('pagevariables.step2')</div>
    <p>$action.getText('text.pagevariables.step2.instructions')</p>

    <div class="smallfont view-template">
        <div class="wiki-content">$action.renderedTemplateContent</div>
    </div>

    <form name="filltemplateform" method="POST" action="createpageentervariables.action">
        #tag ("Hidden" "name='templateId'" "value='$pageTemplate.id'")
        #tag ("Hidden" "name='title'" "value=title")
        #tag ("Hidden" "name='parentId'" "value=parentId")
        #tag ("Hidden" "name='fromPageId'" "value=fromPageId")
        #tag ("Hidden" "name='spaceKey'" "value=spaceKey")
        #tag ("Hidden" "name='labels'" "value=labels")
        #tag ("Hidden" "name='target'" "value=target")
        #tag ("Hidden" "name='identIndex'" "value=identIndex")
        #tag ("Hidden" "name='createPageId'" "value=createPageId")
        #tag ("Hidden" "name='pageId'" "value=pageId")
        #tag ("Hidden" "name='notificationSuppressed'" "value=notificationSuppressed")

        <div class="aui-toolbar2" role="toolbar">
            <div class="aui-toolbar2-inner">
                <input class="aui-button" type="button" value="$action.getText('back.witharrows.name')" onclick="javascript:history.go(-1)">
                #tag( "Submit" "name='confirm'" "id=confirm" "value='next.name'" "theme='notable'" "cssClass='aui-button'")
            </div>
        </div>
    </form>

    #parse ( "/pages/page-breadcrumbs.vm" )
</div>

#end
</body>
</html>


