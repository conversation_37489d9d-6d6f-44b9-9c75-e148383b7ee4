#disable_html_escaping()
<div class="adaptavist-sr">
    #if (! $licensed)

        <strong>ScriptRunner for JIRA is not licensed</strong>
        <p>The workflow function will be ignored until a valid license is entered.</p>
    #else
        #if ($!canned-script)

            #if ($!errorCollection.hasAnyErrors())

                <div class="aui-message aui-message-error adaptavist-sr" style="margin-top: 30px">
                    <p class="title">
                        <strong>Script function errors!</strong>
                    </p>
                    <p>
                        <ul>
                            #foreach ($error in $errorCollection.getErrorMessages())
                                <li>$error</li>
                            #end
                            #foreach ($error in $errorCollection.errors)
                                <li>$!error</li>
                            #end
                        </ul>
                    </p>
                </div>
            #end

            ## this div is overwritten by react
            <div data-function-info='$jsonArgs' class="sr-view-workflow-function">
                <div>
                    ScriptRunner workflow function - $!cannedScriptPreview
                </div>
            </div>
        #else
            <div>
                ScriptRunner Script <b>$scriptFileName</b> will be run.
            </div>
        #end
    #end
</div>
