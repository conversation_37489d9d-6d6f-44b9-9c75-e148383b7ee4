#requireResource("com.onresolve.confluence.groovy.groovyrunner:codemirror")
#requireResource("com.onresolve.confluence.groovy.groovyrunner:adminPluginResources")
<html>
<head>
    <title>$action.getText("scriptrunner.confluence.space.tool.section")</title>
    <meta name="decorator" content="main"/>
</head>

    #applyDecorator("root")
    #decoratorParam("helper" $action.helper)
    #decoratorParam("context" "space-administration")
    #applyDecorator ("root")
    #decoratorParam ("context" "spaceadminpanel")
    #decoratorParam ("selection" "space_script_endpoints_conf")
    #decoratorParam ("selectedSpaceToolsWebItem" "space_script_endpoints_conf")
    #decoratorParam ("helper" $action.helper)
<body>
<div id="sr-info-disable-div" class="sr-info-message">
    <p class="sr-message-title">
        <strong>ScriptRunner for Confluence</strong>
    </p>
    <p class="sr-message-body"><b>Built-in Scripts</b> have been disabled! This page has advanced space administration
        functionality such as Copy Page Tree and Bulk Delete Trash. If you need to have access to these features please
        contact your Confluence Administrator.</p>
</div>
</body>
#end
#end
</html>