#requireResource("com.onresolve.confluence.groovy.groovyrunner:codemirror")
#requireResource("com.onresolve.confluence.groovy.groovyrunner:space-tools")
<html>
<head>
    <title>$action.getText("scriptrunner.confluence.space.tool.section")</title>
    <meta name="decorator" content="main"/>
</head>

    #applyDecorator("root")
    #decoratorParam("helper" $action.helper)
    #decoratorParam("context" "space-administration")
    #applyDecorator ("root")
    #decoratorParam ("context" "spaceadminpanel")
    #decoratorParam ("selection" "space_script_endpoints_conf")
    #decoratorParam ("selectedSpaceToolsWebItem" "space_script_endpoints_conf")
    #decoratorParam ("helper" $action.helper)
<body>

<div id="admin-screens" class="adaptavist-sr"></div>


</body>
#end
#end
</html>