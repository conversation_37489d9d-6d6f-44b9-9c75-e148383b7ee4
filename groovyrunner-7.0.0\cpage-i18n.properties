# com.onresolve.scriptrunner.canned.confluence.macros.CreatePageMacro
com.adaptavist.confluence.createpage.create-page.label=Create Page
com.adaptavist.confluence.createpage.create-page.desc=Helps users to create pages in the right place, with the right template and correctly formatted page title.
com.adaptavist.confluence.createpage.create-page.param.parent.label=Parent
com.adaptavist.confluence.createpage.create-page.param.parent.desc=By default, the created page will use the current page as its parent ($self). However, you can specify a page title or use $parent, $username, $fullname, $year, $month, $day
com.adaptavist.confluence.createpage.create-page.param.title.label=Title
com.adaptavist.confluence.createpage.create-page.param.title.desc=Specify the title of the new page. Most useful when injecting values into the new pages title using $parenttitle, $ident, $username, $fullname, $year, $month, $day
com.adaptavist.confluence.createpage.create-page.param.labels.label=Labels
com.adaptavist.confluence.createpage.create-page.param.labels.desc=Add labels to the new page. It accepts the injected values $parenttitle, $pagetitle, $username, $fullname, $year, $month, $day separated by commas. Illegal characters ( , !, #, &, (, ), *, ,, ., :, ;, <, >, ?, @, [, ], ^) will be removed.
com.adaptavist.confluence.createpage.create-page.param.prefix.label=Title Prefix
com.adaptavist.confluence.createpage.create-page.param.prefix.desc=Add text to the start of the new page title. It accepts the following variables $parenttitle, $ident, $username, $fullname, $year, $month, $day
com.adaptavist.confluence.createpage.create-page.param.postfix.label=Title Suffix
com.adaptavist.confluence.createpage.create-page.param.postfix.desc=Add text to the end of the new page title. It accepts the following variables $parenttitle, $ident, $username, $fullname, $year, $month, $day
com.adaptavist.confluence.createpage.create-page.param.target.label=Target Mode
com.adaptavist.confluence.createpage.create-page.param.target.desc=Whether to go to view or edit mode (defaults to view mode).
com.adaptavist.confluence.createpage.create-page.param.template.label=Template
com.adaptavist.confluence.createpage.create-page.param.template.desc=The name of the template whose contents will be copied to the new page.

com.adaptavist.confluence.createpage.create-page.param.auiClass.label=AUI Class
com.adaptavist.confluence.createpage.create-page.param.auiClass.desc=Optional AUI Class to help with styling. Use "aui-button" to represent a button or "aui-message" for an alert style message
com.adaptavist.confluence.createpage.create-page.param.css.label=CSS
com.adaptavist.confluence.createpage.create-page.param.css.desc=Optional basic CSS to help with styling. Please provide css in a comma separated list, for example: "font-size: 20px, background-color: red"

com.adaptavist.confluence.createpage.create-page.param.identIndex.label=Ident index
com.adaptavist.confluence.createpage.create-page.param.identIndex.desc=Allow $ident parameter to start from a specific number
com.adaptavist.confluence.createpage.create-page.param.addSpace.label=Add Space
com.adaptavist.confluence.createpage.create-page.param.addSpace.desc=Add a space in between prefix and postfix variables
com.adaptavist.confluence.createpage.create-page.param.prompt.label=Prompt Title
com.adaptavist.confluence.createpage.create-page.param.prompt.desc=Customise the title used in the prompt dialog
com.adaptavist.confluence.createpage.create-page.param.from-page.label=From Page
com.adaptavist.confluence.createpage.create-page.param.from-page.desc=Create page based on the specified page
com.adaptavist.confluence.createpage.create-page.param.suppressNotification.label=Suppress Notification
com.adaptavist.confluence.createpage.create-page.param.suppressNotification.desc=Suppress notification while creating page
com.adaptavist.confluence.createpage.create-page.param.openInNewTab.label=Open in a new tab
com.adaptavist.confluence.createpage.create-page.param.openInNewTab.desc=Open the created page in a new tab
com.onresolve.confluence.groovy.groovyrunner.create-page.param.target.View.desc=View
com.onresolve.confluence.groovy.groovyrunner.create-page.param.target.Edit.desc=Edit

# Error messages
com.adaptavist.confluence.createpage.create-page.error.msgBoxtitle=Create Page for ScriptRunner Confluence error
com.adaptavist.confluence.createpage.create-page.error.license=Create Page requires a license for <a href="https://marketplace.atlassian.com/plugins/com.onresolve.confluence.groovy.groovyrunner/server/overview">ScriptRunner for Confluence</a>
com.adaptavist.confluence.createpage.create-page.error.fromPageAndTemplate=Both 'From Page' and 'Template' found. Only one can be defined.
com.adaptavist.confluence.createpage.create-page.error.fromPageNotExist=The specified 'From Page' does not exist
com.adaptavist.confluence.createpage.create-page.error.templateNotExist=The specified Template does not exist
com.adaptavist.confluence.createpage.create-page.error.parentPageNotFound=Unable to retrieve parent page. Parent page not found
com.adaptavist.confluence.createpage.create-page.error.blogPostParentPage=Is not possible to create a page child of a blog post
com.adaptavist.confluence.createpage.create-page.error.identNotNumeric=The specified ident index is not a number
# Page Utils error messages
com.adaptavist.confluence.createpage.create-page.utils.error.parent.removed=Unable to create page. The parent page has been removed recently
com.adaptavist.confluence.createpage.create-page.utils.error.page.already.exists=Unable to create page. A page with the given name already exists
com.adaptavist.confluence.createpage.create-page.utils.error.page.openedinanewtab=Unable to create page. Opening Create Page in a new tab is not supported
com.adaptavist.confluence.createpage.create-page.utils.error.page.invalid.title=The page title you specified contains invalid characters or is too long
com.adaptavist.confluence.createpage.create-page.utils.error.no.Permissions.On.From.Page=Unable to create page. Unfortunately, you do not have the appropriate permission level to create this page.
