#script-runner-form {
    margin-top: 10px;
}

.sr-scripts-description {
    margin-bottom: 15px;
}

#sr-show-hide-add-new-items {
    margin-top: 20px;
    margin-bottom: 15px;
}

#configured-items {
    margin-bottom: 15px;
}

#script-list {
    list-style-image: url(../images/bullets.png);
    display: grid;
    grid-template-columns: 1fr 1fr;
}

#script-list > li {
    padding: 5px;
    min-height: 60px;
}

#script-list > li.highlighOnHover:hover {
    background-color: #EBF2F9;
    border: 1px solid #CDDCEC;
    border-radius: 3px;
    cursor: pointer;
}

.sr-script-transparent-border {
    border: 1px solid transparent;
}

.sr-script-selected {
    background-color: #EBF2F9;
    border: 1px solid #CDDCEC;
    border-radius: 3px;
    cursor: pointer;
}

.view-ast, #enable-location-finder, #disable-location-finder, .sr-table-edit-action, .sr-table-delete-action,
#configured-item-create, #disableFeatureDiscovery, .configured-item-delete, .configured-item-edit, .edit,
.select-all.check-status, .select-all.check-status-all, .select-all.select-column-all,
.sr-table-edit-action-escl-services {
    cursor: pointer;
}

a.sr-disable-link {
    pointer-events: none;
    cursor: default;
}

.scriptlink {
    padding-right: 7px;
}

#script-list .sr-script-fragment-left {
    grid-column-start: 1;
    grid-column-end: 2;
    min-width: 90%;
    max-width: 90%;
}

#script-list .sr-script-fragment-right {
    grid-column-start: 2;
    min-width: 90%;
    max-width: 90%;
}

#behaviour-fields .field-group[id*="field-group-"]:nth-child(even) {
    background-color: #eaeaea;
}

.aui.sr-configured-items-table td {
    vertical-align: middle;
}

.aui.sr-configured-items-table .table-size-name {
    width: 25%
}

.aui.sr-configured-items-table .table-size-actions {
    width: 5%
}

#show-all-code {
    float: right;
    margin-right: 10px;
}

#hide-all-code {
    float: right;
}

.show-hide-code {
    margin-bottom: 10px;
}

#macro-key-error {
    color: #d04437;
}

.sr-table-edit-action-escl-services, .cannedscript-table-edit-action {
    margin-right: 20px;
}

.sr-table-edit-action,
.sr-table-disable-action,
.sr-table-delete-action {
    padding: 0px !important;
}

.configured-item-confirm {
    margin-top: 20px;
}

@media only screen and (max-width: 1024px) {
    #script-list {
        display: block;
    }

    #script-list .sr-script-fragment-left, #script-list .sr-script-fragment-right {
        display: block;
        width: 100%;
        min-width: 90%;
    }
}

.qtip-aui-inline-dialog-clone {
    background: #ffffff;
    border: 1px solid #cccccc;
    border-radius: 3px;
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.2);
    padding: 20px;
    color: #333333;
    font-family: Arial, sans-serif;
    font-size: 14px;
    line-height: 1.42857142857143;
}

/*Clear a `float: left` set by Atlassian in Bamboo (SRBAM-71) */
.aui-dropdown2 .select2-results .select2-result .select2-result-label .select2-match {
    float: none;
}

.project-lozenge {
    background: #DFE1E6;
    border: 1px solid #DFE1E6;
    color: #253858;
}
