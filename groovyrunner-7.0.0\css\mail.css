/*This file is not really used anymore after the changes performed in 8404a3f (Gmail not showing styles properly)...*/
/*I'm just keeping it for a clearer reference*/

body {
    font-family: Arial,sans-serif;
    font-size: 14px;
    line-height: 1.42857142857143;
    padding: 20px;
}

pre {
    font-family: Arial,sans-serif;
    font-size: 14px;
    line-height: 1.42857142857143;
    white-space: pre-wrap;       /* CSS 3 */
    white-space: -moz-pre-wrap;  /* Mozilla, since 1999 */
    white-space: -pre-wrap;      /* Opera 4-6 */
    white-space: -o-pre-wrap;    /* Opera 7 */
    word-wrap: break-word;       /* Internet Explorer 5.5+ */
}

div.mail-section {
    background: #F5F5F5;
    border: 1px solid #ccc;
    border-radius: 3px;
    margin: 10px 0 20px 0;
    padding: 20px;
    max-width: 1024px;
}

ul.ref-changes {
    padding-left: 0px;
}

div ul.ref-changes li {
    list-style-type: none;
    padding-bottom: 20px;
}

div ul.ref-changes li div.ref-change{
    padding-bottom: 10px;
}

table.commits {
    font-size: 14px;
    width: 100%;
    padding-bottom: 10px;
}

table.commits tr {
    background: #ffffff;
    border-bottom: 1px solid #cccccc;
    color: #333333;
}

table.commits td {
    border: 1px solid #cccccc;
    padding: 7px 10px;
    text-align: left;
    vertical-align: top;
}

table.pathChanges {
    font-size: 14px;
    width: 100%;
}

table.pathChanges th {
    text-align: left;
    padding: 0px 10px;
}

table.pathChanges tr {
    background: #ffffff;
    color: #333333;
}

table.pathChanges td {
    border: 0px none;
    padding: 2px 10px;
    text-align: left;
    vertical-align: top;
}

.typeColumn {
    width: 60px;
}

.linkColumn {
    width: 110px;
}
