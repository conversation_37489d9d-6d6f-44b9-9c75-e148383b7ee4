.CodeMirror {
    height: 100%;
    background-color: #ffffff;
    /*max-height: 270px;*/
    min-height: 57px!important;
    line-height: 140%;
    font-family:Consolas,Monaco,Lucida <PERSON>,Liberation Mono,Deja<PERSON>u Sans Mono,Bitstream Vera Sans Mono,Courier New, monospace;

    border: 1px solid #ccc;
    border-radius: 3.01px;
    box-shadow: inset 0 1px 3px #ccc;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    color: #333;
    font-size: inherit;
    margin: 0;
    vertical-align: baseline;
    width: 100%;
}

.CodeMirror:not(.CodeMirror-fullscreen) > .CodeMirror-scroll {
    padding-bottom: 50px;
}

.CodeMirror-gutters, .CodeMirror-lint-markers, .CodeMirror-linenumbers {
    padding-bottom: 0 !important;
    margin-bottom: 0 !important;
}

textarea.CodeMirror.CodeMirrorSmall + div.CodeMirror {
    width: 500px;
}

div.CodeMirror-fullscreen{
    width: 100% !important;
    z-index: 30000;
}

.CodeEditorField {
    max-width: 750px;
}

/* This class makes CodeMirror textareas compatible with JIRA */
.maxWidth {
    width: 100%;
}

.etabs {
    margin: 0;
    padding: 0;
    float: right;
}
.tab { display: inline-block; zoom:1; *display:inline; background: #eee; border: solid 1px #cccccc; border-bottom: none; -moz-border-radius: 4px 4px 0 0; border-radius: 4px 4px 0 0; }
.tab a { font-size: 10px; line-height: 2em; display: block; padding: 0 10px; outline: none;
    color: black;}
.tab a:hover { text-decoration: none; }
.tab.active { background: #fff; position: relative; top: 0px; border-color: #cccccc; }
.tab a.active { text-decoration: none; }
.tab-container .panel-container { background: #fff; border: solid #666 1px; padding: 10px; -moz-border-radius: 0 4px 4px 4px; border-radius: 0 4px 4px 4px; }
/*

ul.script-errors {
    list-style-type: none;
    padding: 0px;
    margin: 0px;
}
*/

span.sr-error {
    width: 16px;
    height: 16px;
    background: url(../images/compiler/error.png) no-repeat top left;
}

span.sr-warning {
    width: 16px;
    height: 16px;
    background: url(../images/compiler/warning.png) no-repeat top left;
}

span.sr-ok {
    width: 16px;
    height: 16px;
    background: url(../images/compiler/ok.png) no-repeat;
    background-size: 14px 14px;
}

ul.script-errors li.sr-error {
    background-image: url(../images/compiler/error.png);
}

ul.script-errors li.sr-warning {
    background-image: url(../images/compiler/warning.png);
}

ul.script-errors li {

    white-space: pre-wrap;
    font-family: monospace;
    background: no-repeat top left;
    list-style-type: none;
    padding: 0 20px 0 20px;
}

.function-history {
    -webkit-border-radius: 3.01px;
    -moz-border-radius: 3.01px;
    border-radius: 3.01px;
    -moz-background-clip: padding;
    -webkit-background-clip: padding-box;
    background-clip: padding-box;
    background: #fff;
    border: 1px solid #ccc;
    display: inline-block;
    margin-right: 10px;
    padding: 1px 5px;
    position: relative;
    vertical-align: middle
}

.function-history > ol {
    display: table;
    float: left;
    list-style: none;
    margin: 0 5px;
    padding: 0
}

.function-history > ol > li {
    border-right: 1px solid #ddd;
    display: table-cell
}

.function-history > ol > li:first-child {
    border-left: 1px solid #ddd
}

.function-history .sequence-break > a {
    float: left
}

.function-history li > a {
    -webkit-transition: all .2s ease;
    -moz-transition: all .2s ease;
    -ms-transition: all .2s ease;
    -o-transition: all .2s ease;
    transition: all .2s ease;
    display: block;
    padding: 5px
}

.function-history li > a > span {
    vertical-align: middle;
}

.function-history li > a:hover {
    background: #f0f0f0;
    cursor: pointer;
}

.function-history .current > a {
    background: #ebf2f9
}

.function-history .icon {
    display: block
}

.function-history a.previous:hover > span, .function-history a.next:hover > span {
    border-color: transparent #505050
}

.function-history span.previous > span, .function-history span.next > span {
    border-color: transparent #f0f0f0
}

.function-history > p {
    color: #707070;
    font-size: 12px;
    line-height: 1;
    margin: 0;
    padding: 7px 8px
}

pre.pre-json {outline: 1px solid #ccc; padding: 5px; margin: 5px; }
pre.pre-json.string { color: green; }
pre.pre-json.number { color: darkorange; }
pre.pre-json.boolean { color: blue; }
pre.pre-json.null { color: magenta; }
pre.pre-json.key { color: red; }

.pre-wrap {
    white-space: -moz-pre-wrap; /* Mozilla, supported since 1999 */
    white-space: -pre-wrap; /* Opera */
    white-space: -o-pre-wrap; /* Opera */
    white-space: pre-wrap; /* CSS3 - Text module (Candidate Recommendation) http://www.w3.org/TR/css3-text/#white-space */
    word-wrap: break-word; /* IE 5.5+ */
}

.truncate {
    max-width: 350px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.switch .aui-button.switch-mode-on[aria-pressed=true] {
    box-shadow: inset 0 3px 6px #0f6a22;
    border-color: #0c541b;
    text-shadow: 0 1px 0 #0f6a22;
    background: #14892c;
    color: #fff
}

/* Start of CSS for expandable/collapsible inline script */

.toggle-box-for-inline-script:checked + pre {
    display: none;
}

.toggle-box-for-inline-script + pre {
    display: inline;
}

.toggle-box-for-inline-script {
    visibility: hidden;
    cursor: pointer;
    display: block;
    font-weight: bold;
    line-height: 21px;
    margin-bottom: 11px;
}

.toggle-box-for-inline-script:after {
    display: block;
    content: "Hide script";
    position: inherit;
    visibility: visible;
    height: 20px;
    line-height: 1.46;
    width: 90px;
    text-align: left;
    color: #3b73af;
    font-size: 14px;
    cursor: pointer;
    font-weight: normal;
}

.toggle-box-for-inline-script:checked:after {
    content: "Show script";
}

/* End of CSS for expandable/collapsible inline script */

a.action {
    cursor: pointer;
}

a.disableClick{
    pointer-events: none;
    color: grey;
}

.sr-quicksearch-highlight {
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    border-radius: 5px;
    -moz-background-clip: padding;
    -webkit-background-clip: padding-box;
    background-clip: padding-box;
    -webkit-box-shadow: 0 2px 2px #ccc;
    -moz-box-shadow: 0 2px 2px #ccc;
    box-shadow: 0 2px 2px #ccc;
    background-color: #ffde00;
    margin: -2px;
    padding: 2px;
    position: relative
}
