{"class": ["com.atlassian.jira.action.JiraActionSupport#", "com.atlassian.jira.cluster.dbr.analytics.DBRStatsEvent#", "com.atlassian.jira.config.database.LegacyHsqlDatasourceInfo#", "com.atlassian.jira.config.webwork.JiraActionFactory$NonWebActionFactory#", "com.atlassian.jira.index.BackgroundIndexSnapshotOperator#", "com.atlassian.jira.index.WriterStatsEvent#", "com.atlassian.jira.issue.comparator.UserBestNameComparator#", "com.atlassian.jira.issue.fields.CustomFieldImpl#", "com.atlassian.jira.issue.transitions.TransitionLinkFactory#", "com.atlassian.jira.plugin.bigpipe.BigPipeService#", "com.atlassian.jira.plugin.bigpipe.BigPipeWebPanelModuleDescriptor#", "com.atlassian.jira.plugin.bigpipe.CollectingExecutor#", "com.atlassian.jira.plugin.bigpipe.GlobalPriorityConfigurer#", "com.atlassian.jira.plugin.bigpipe.PrioritizedRunnable#", "com.atlassian.jira.plugin.webfragment.conditions.IsRolesEnabled#", "com.atlassian.jira.security.request.DefaultActionTracker#", "com.atlassian.jira.security.request.TrackedAction#", "com.atlassian.jira.security.request.TrackedActionEvent#", "com.atlassian.jira.security.xsrf.XsrfRecoverableFailureException#", "com.atlassian.jira.upgrade.ConnectionKeeper#", "com.atlassian.jira.util.collect.LRUMap#", "com.atlassian.jira.versioning.analytics.EntityVersioningStatsEvent#", "com.atlassian.jira.web.pagebuilder.AbstractJspDecorator#", "com.atlassian.jira.web.pagebuilder.GeneralJspDecorator#", "com.atlassian.jira.web.pagebuilder.JspDecoratorUtils#"], "interface": ["com.atlassian.jira.plugin.bigpipe.BigPipePriorityConfigurer#", "com.atlassian.jira.security.request.ActionTracker#", "com.atlassian.jira.web.pagebuilder.DecoratorListener#", "com.atlassian.jira.web.pagebuilder.JspDecorator#", "com.atlassian.jira.web.pagebuilder.PageBuilder#"], "method": ["com.atlassian.jira.action.JiraActionSupport#addErrorMessages(java.util.Collection)", "com.atlassian.jira.action.JiraActionSupport#addErrorMessages(webwork.dispatcher.ActionResult)", "com.atlassian.jira.action.JiraActionSupport#addErrors(java.util.Map)", "com.atlassian.jira.action.JiraActionSupport#addIllegalArgumentException(java.lang.String,java.lang.IllegalArgumentException)", "com.atlassian.jira.action.JiraActionSupport#execute()", "com.atlassian.jira.action.JiraActionSupport#getActionName()", "com.atlassian.jira.action.JiraActionSupport#getApplicationProperties()", "com.atlassian.jira.action.JiraActionSupport#getComponentInstanceOfType(java.lang.Class)", "com.atlassian.jira.action.JiraActionSupport#getDefaultResourceBundle()", "com.atlassian.jira.action.JiraActionSupport#getI18nHelper()", "com.atlassian.jira.action.JiraActionSupport#getKeysForPrefix(java.lang.String)", "com.atlassian.jira.action.JiraActionSupport#getLocale()", "com.atlassian.jira.action.JiraActionSupport#getOfBizDelegator()", "com.atlassian.jira.action.JiraActionSupport#getResourceBundle()", "com.atlassian.jira.action.JiraActionSupport#getResult()", "com.atlassian.jira.action.JiraActionSupport#getText(java.lang.String)", "com.atlassian.jira.action.JiraActionSupport#getText(java.lang.String,java.lang.String)", "com.atlassian.jira.action.JiraActionSupport#getText(java.lang.String,java.lang.String,java.lang.String)", "com.atlassian.jira.action.JiraActionSupport#getText(java.lang.String,java.lang.String,java.lang.String,java.lang.String)", "com.atlassian.jira.action.JiraActionSupport#getText(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String)", "com.atlassian.jira.action.JiraActionSupport#getText(java.lang.String,java.lang.Object,java.lang.Object,java.lang.Object)", "com.atlassian.jira.action.JiraActionSupport#getText(java.lang.String,java.lang.Object,java.lang.Object,java.lang.Object,java.lang.Object)", "com.atlassian.jira.action.JiraActionSupport#getText(java.lang.String,java.lang.Object,java.lang.Object,java.lang.Object,java.lang.Object,java.lang.Object)", "com.atlassian.jira.action.JiraActionSupport#getText(java.lang.String,java.lang.Object,java.lang.Object,java.lang.Object,java.lang.Object,java.lang.Object,java.lang.Object)", "com.atlassian.jira.action.JiraActionSupport#getText(java.lang.String,java.lang.Object,java.lang.Object,java.lang.Object,java.lang.Object,java.lang.Object,java.lang.Object,java.lang.Object)", "com.atlassian.jira.action.JiraActionSupport#getText(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String)", "com.atlassian.jira.action.JiraActionSupport#getText(java.lang.String,java.lang.Object,java.lang.Object,java.lang.Object,java.lang.Object,java.lang.Object,java.lang.Object,java.lang.Object,java.lang.Object)", "com.atlassian.jira.action.JiraActionSupport#getText(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String)", "com.atlassian.jira.action.JiraActionSupport#getText(java.lang.String,java.lang.Object)", "com.atlassian.jira.action.JiraActionSupport#getUnescapedText(java.lang.String)", "com.atlassian.jira.action.JiraActionSupport#getUntransformedRawText(java.lang.String)", "com.atlassian.jira.action.JiraActionSupport#getUserPreferences()", "com.atlassian.jira.action.JiraActionSupport#hasAnyErrors()", "com.atlassian.jira.action.JiraActionSupport#isIndexing()", "com.atlassian.jira.action.JiraActionSupport#isKeyDefined(java.lang.String)", "com.atlassian.jira.action.JiraActionSupport#removeKeyOrAddError(java.util.Map,java.lang.String,java.lang.String)", "com.atlassian.jira.application.ApplicationAuthorizationService#rolesEnabled()", "com.atlassian.jira.application.ApplicationRoleManager#getDefaultRoles()", "com.atlassian.jira.application.DefaultApplicationRoleManager#getDefaultRoles()", "com.atlassian.jira.application.DefaultApplicationRoleManager#rolesEnabled()", "com.atlassian.jira.bc.JiraServiceContext#getLoggedInUser()", "com.atlassian.jira.cluster.dbr.analytics.DBRStatsEvent#createReceiverEvent(java.util.Map,java.util.Map)", "com.atlassian.jira.cluster.dbr.analytics.DBRStatsEvent#createReplicatorEvent(java.util.Map,java.util.Map)", "com.atlassian.jira.cluster.dbr.analytics.DBRStatsEvent#getEventName()", "com.atlassian.jira.cluster.dbr.analytics.DBRStatsEvent#getSnapshot()", "com.atlassian.jira.cluster.dbr.analytics.DBRStatsEvent#getTotal()", "com.atlassian.jira.cluster.NodeStateManager#getAllNodes()", "com.atlassian.jira.cluster.NodeStateManager#getNode()", "com.atlassian.jira.cluster.NodeStateManager#getNodeWithRefresh()", "com.atlassian.jira.config.ConstantsManager#convertToConstantObjects(java.lang.String,java.util.Collection)", "com.atlassian.jira.config.ConstantsManager#getDefaultPriorityObject()", "com.atlassian.jira.config.ConstantsManager#getIssueTypeObject(java.lang.String)", "com.atlassian.jira.config.ConstantsManager#getPriorityObjects()", "com.atlassian.jira.config.ConstantsManager#getResolutionObject(java.lang.String)", "com.atlassian.jira.config.ConstantsManager#getResolutionObjects()", "com.atlassian.jira.config.ConstantsManager#getStatusObjects()", "com.atlassian.jira.config.ConstantsManager#refresh()", "com.atlassian.jira.config.database.DatabaseConfig#isHSql()", "com.atlassian.jira.config.FeatureManager#isOnDemand()", "com.atlassian.jira.config.properties.JiraSystemProperties#isOnDemand()", "com.atlassian.jira.help.HelpUrlsApplicationKeyProvider#shouldParseLegacyHelpProperties()", "com.atlassian.jira.index.ha.DefaultIndexSnapshotOperator#getDestinationDirectory()", "com.atlassian.jira.index.ha.IndexCopyService#backupIndex(java.lang.String,com.atlassian.jira.index.ha.TemporaryFilesProvider)", "com.atlassian.jira.index.ha.IndexSnapshotOperator#performIndexSnapshotBackupAndCleanup(int)", "com.atlassian.jira.index.ha.IndexUtils#performBackupOperations(java.lang.String,java.lang.String,int,com.atlassian.jira.util.compression.ArchiveUtils$Type,com.atlassian.jira.index.ha.TemporaryFilesProvider,java.lang.String)", "com.atlassian.jira.index.WriterStatsEvent#getIndex()", "com.atlassian.jira.index.WriterStatsEvent#getSnapshot()", "com.atlassian.jira.index.WriterStatsEvent#getTotal()", "com.atlassian.jira.issue.changehistory.ChangeHistoryItem#getFrom()", "com.atlassian.jira.issue.changehistory.ChangeHistoryItem#getFromValue()", "com.atlassian.jira.issue.changehistory.ChangeHistoryItem#getTo()", "com.atlassian.jira.issue.changehistory.ChangeHistoryItem#getToValue()", "com.atlassian.jira.issue.changehistory.ChangeHistoryItem#getUser()", "com.atlassian.jira.issue.changehistory.ChangeHistoryItem$Builder#forIssue(long,java.lang.String)", "com.atlassian.jira.issue.changehistory.ChangeHistoryItem$Builder#inChangeGroup(long)", "com.atlassian.jira.issue.changehistory.ChangeHistoryItem$Builder#inProject(long)", "com.atlassian.jira.issue.changehistory.ChangeHistoryItem$Builder#withId(long)", "com.atlassian.jira.issue.changehistory.ChangeHistoryManager#findAllPossibleValues(java.lang.String)", "com.atlassian.jira.issue.changehistory.ChangeHistoryManager#findMovedIssue(java.lang.String)", "com.atlassian.jira.issue.changehistory.ChangeHistoryManager#getPreviousIssueKeys(java.lang.String)", "com.atlassian.jira.issue.changehistory.ChangeHistoryManager#getPreviousIssueKeys(java.lang.Long)", "com.atlassian.jira.issue.comparator.UserBestNameComparator#compare(com.atlassian.jira.user.ApplicationUser,com.atlassian.jira.user.ApplicationUser)", "com.atlassian.jira.issue.customfields.CustomFieldUtils#getDateFormat()", "com.atlassian.jira.issue.customfields.CustomFieldUtils#getDateTimeFormat()", "com.atlassian.jira.issue.customfields.CustomFieldUtils#getTimeFormat()", "com.atlassian.jira.issue.fields.CustomFieldImpl#getParamKeyIssueId()", "com.atlassian.jira.issue.fields.CustomFieldImpl#getParamKeyProjectId()", "com.atlassian.jira.issue.index.DefaultIndexManager#isIndexingEnabled()", "com.atlassian.jira.issue.index.DefaultIndexManager#reIndexAllIssuesInBackground(com.atlassian.jira.task.context.Context,boolean,boolean)", "com.atlassian.jira.issue.index.IndexingLimitsHelper#parseLimit(java.lang.String)", "com.atlassian.jira.issue.index.IssueIndexer#deindexIssues(com.atlassian.jira.util.collect.EnclosedIterable,com.atlassian.jira.task.context.Context)", "com.atlassian.jira.issue.index.IssueIndexer#indexIssues(com.atlassian.jira.util.collect.EnclosedIterable,com.atlassian.jira.task.context.Context)", "com.atlassian.jira.issue.tabpanels.AllTabPanel#getActions(com.atlassian.jira.plugin.issuetabpanel.GetActionsRequest)", "com.atlassian.jira.issue.tabpanels.AllTabPanel#showPanel(com.atlassian.jira.plugin.issuetabpanel.ShowPanelRequest)", "com.atlassian.jira.issue.tabpanels.CommentTabPanel#getActions(com.atlassian.jira.plugin.issuetabpanel.GetActionsRequest)", "com.atlassian.jira.issue.transitions.TransitionLinkFactory#getItems(java.util.Map)", "com.atlassian.jira.plugin.bigpipe.BigPipePriorityConfigurer#calculatePriority(com.atlassian.plugin.elements.ResourceDescriptor,java.util.Map)", "com.atlassian.jira.plugin.bigpipe.BigPipeService#closeExecutor()", "com.atlassian.jira.plugin.bigpipe.BigPipeService#executeSingleTask()", "com.atlassian.jira.plugin.bigpipe.BigPipeService#isBigPipeEnabled()", "com.atlassian.jira.plugin.bigpipe.BigPipeService#pipeContent(java.lang.String,java.lang.Integer,com.atlassian.jira.util.Supplier)", "com.atlassian.jira.plugin.bigpipe.BigPipeWebPanelModuleDescriptor#getModule()", "com.atlassian.jira.plugin.bigpipe.BigPipeWebPanelModuleDescriptor#init(com.atlassian.plugin.Plugin,org.dom4j.Element)", "com.atlassian.jira.plugin.bigpipe.CollectingExecutor#close()", "com.atlassian.jira.plugin.bigpipe.CollectingExecutor#execute(java.lang.Runnable)", "com.atlassian.jira.plugin.bigpipe.CollectingExecutor#pop()", "com.atlassian.jira.plugin.bigpipe.CollectingExecutor#prioritized(java.lang.Integer)", "com.atlassian.jira.plugin.bigpipe.PrioritizedRunnable#compareTo(com.atlassian.jira.plugin.bigpipe.PrioritizedRunnable)", "com.atlassian.jira.plugin.bigpipe.PrioritizedRunnable#getPriority()", "com.atlassian.jira.plugin.bigpipe.PrioritizedRunnable#run()", "com.atlassian.jira.plugin.issuetabpanel.IssueTabPanelInvoker#invokeGetActions(com.atlassian.jira.plugin.issuetabpanel.GetActionsRequest,com.atlassian.jira.plugin.issuetabpanel.IssueTabPanelModuleDescriptor)", "com.atlassian.jira.plugin.issuetabpanel.************************#invokeGetActions(com.atlassian.jira.plugin.issuetabpanel.GetActionsRequest,com.atlassian.jira.plugin.issuetabpanel.IssueTabPanelModuleDescriptor)", "com.atlassian.jira.plugin.issuetabpanel.IssueTabPanelModuleDescriptorImpl#createModule()", "com.atlassian.jira.plugin.navigation.HeaderFooterRendering#flushBigPipe(javax.servlet.jsp.JspWriter)", "com.atlassian.jira.plugin.navigation.HeaderFooterRendering#includeResources(javax.servlet.jsp.JspWriter)", "com.atlassian.jira.plugin.navigation.HeaderFooterRendering#includeTopNavigation(javax.servlet.jsp.JspWriter,javax.servlet.http.HttpServletRequest,com.atlassian.jira.web.pagebuilder.DecoratablePage$ParsedBody)", "com.atlassian.jira.plugin.navigation.HeaderFooterRendering#includeTopNavigation(javax.servlet.jsp.JspWriter,javax.servlet.http.HttpServletRequest,java.lang.String,java.util.Map)", "com.atlassian.jira.plugin.navigation.HeaderFooterRendering#requireCommonResources()", "com.atlassian.jira.plugin.navigation.HeaderFooterRendering#requireLookAndFeelResources()", "com.atlassian.jira.plugin.webfragment.conditions.IsRolesEnabled#init(java.util.Map)", "com.atlassian.jira.plugin.webfragment.conditions.IsRolesEnabled#shouldDisplay(java.util.Map)", "com.atlassian.jira.security.request.ActionTracker#track(webwork.action.Action,java.lang.String)", "com.atlassian.jira.security.request.TrackedAction#equals(java.lang.Object)", "com.atlassian.jira.security.request.TrackedAction#hashCode()", "com.atlassian.jira.security.request.TrackedActionEvent#equals(java.lang.Object)", "com.atlassian.jira.security.request.TrackedActionEvent#getClassName()", "com.atlassian.jira.security.request.TrackedActionEvent#getMethodName()", "com.atlassian.jira.security.request.TrackedActionEvent#getRequestMethod()", "com.atlassian.jira.security.request.TrackedActionEvent#hashCode()", "com.atlassian.jira.task.TaskManager#shutdownAndWait(long)", "com.atlassian.jira.upgrade.ConnectionKeeper#shutdown()", "com.atlassian.jira.upgrade.ConnectionKeeper#start()", "com.atlassian.jira.user.a11y.A11yPersonalSettingsManager#getA11yCssClasses()", "com.atlassian.jira.user.a11y.A11yPersonalSettingsManagerImpl#getA11yCssClasses()", "com.atlassian.jira.user.UserUtils#getAllUsers()", "com.atlassian.jira.user.util.UserManager#getAllGroups()", "com.atlassian.jira.user.util.UserManager#getAllUsers()", "com.atlassian.jira.user.util.UserManager#getGroups()", "com.atlassian.jira.user.util.UserManager#getUser(java.lang.String)", "com.atlassian.jira.user.util.UserManager#getUserEvenWhenUnknown(java.lang.String)", "com.atlassian.jira.user.util.UserManager#getUserObject(java.lang.String)", "com.atlassian.jira.user.util.UserManager#getUsers()", "com.atlassian.jira.user.util.UserUtil#addToJiraUsePermission(com.atlassian.jira.user.ApplicationUser)", "com.atlassian.jira.user.util.UserUtil#canActivateNumberOfUsers(int)", "com.atlassian.jira.user.util.UserUtil#clearActiveUserCount()", "com.atlassian.jira.user.util.UserUtil#createUser(com.atlassian.jira.user.UserDetails,boolean,int,java.util.Set)", "com.atlassian.jira.user.util.UserUtil#createUserNoNotification(java.lang.String,java.lang.String,java.lang.String,java.lang.String)", "com.atlassian.jira.user.util.UserUtil#createUserNoNotification(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.Long)", "com.atlassian.jira.user.util.UserUtil#createUserWithNotification(java.lang.String,java.lang.String,java.lang.String,java.lang.String,int)", "com.atlassian.jira.user.util.UserUtil#createUserWithNotification(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.Long,int)", "com.atlassian.jira.user.util.UserUtil#getActiveUserCount()", "com.atlassian.jira.user.util.UserUtil#getAdministrators()", "com.atlassian.jira.user.util.UserUtil#getAllApplicationUsers()", "com.atlassian.jira.user.util.UserUtil#getGroup(java.lang.String)", "com.atlassian.jira.user.util.UserUtil#getGroupObject(java.lang.String)", "com.atlassian.jira.user.util.UserUtil#getSystemAdministrators()", "com.atlassian.jira.user.util.UserUtil#getTotalUserCount()", "com.atlassian.jira.user.util.UserUtil#getUser(java.lang.String)", "com.atlassian.jira.user.util.UserUtil#getUserByKey(java.lang.String)", "com.atlassian.jira.user.util.UserUtil#getUserByName(java.lang.String)", "com.atlassian.jira.user.util.UserUtil#getUserObject(java.lang.String)", "com.atlassian.jira.user.util.UserUtil#getUsers()", "com.atlassian.jira.user.util.UserUtil#getUsersInGroupNames(java.util.Collection)", "com.atlassian.jira.user.util.UserUtil#getUsersInGroups(java.util.Collection)", "com.atlassian.jira.util.collect.LRUMap#newLRUMap(int)", "com.atlassian.jira.util.collect.LRUMap#removeEldestEntry(java.util.Map$Entry)", "com.atlassian.jira.util.collect.LRUMap#synchronizedLRUMap(int)", "com.atlassian.jira.util.index.IndexLifecycleManager#isIndexingEnabled()", "com.atlassian.jira.util.index.IndexLifecycleManager#reIndexAllIssuesInBackground(com.atlassian.jira.task.context.Context,boolean,boolean)", "com.atlassian.jira.util.JiraUtils#loadComponent(java.lang.String,java.lang.Class)", "com.atlassian.jira.util.JiraUtils#loadComponent(java.lang.String,java.lang.ClassLoader)", "com.atlassian.jira.util.JiraUtils#loadComponent(java.lang.Class)", "com.atlassian.jira.util.JiraUtils#loadComponent(java.lang.Class,java.util.Collection)", "com.atlassian.jira.versioning.analytics.EntityVersioningStatsEvent#getSnapshot()", "com.atlassian.jira.versioning.analytics.EntityVersioningStatsEvent#getTotal()", "com.atlassian.jira.web.pagebuilder.DecoratorListener#onDecoratorSet()", "com.atlassian.jira.web.pagebuilder.DefaultJiraPageBuilderService#initForRequest(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse,com.atlassian.jira.web.pagebuilder.DecoratorListener,javax.servlet.ServletContext)", "com.atlassian.jira.web.pagebuilder.JiraPageBuilderService#get()", "com.atlassian.jira.web.pagebuilder.JspDecorator#setContext(javax.servlet.ServletContext,javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)", "com.atlassian.jira.web.pagebuilder.JspDecoratorUtils#clearParsedBody()", "com.atlassian.jira.web.pagebuilder.JspDecoratorUtils#clearParsedHead()", "com.atlassian.jira.web.pagebuilder.JspDecoratorUtils#getBody()", "com.atlassian.jira.web.pagebuilder.JspDecoratorUtils#getHead()", "com.atlassian.jira.web.pagebuilder.JspDecoratorUtils#setParsedBody(com.atlassian.jira.web.pagebuilder.DecoratablePage$ParsedBody)", "com.atlassian.jira.web.pagebuilder.JspDecoratorUtils#setParsedHead(com.atlassian.jira.web.pagebuilder.DecoratablePage$ParsedHead)", "com.atlassian.jira.web.pagebuilder.PageBuilder#flush()", "com.atlassian.jira.web.pagebuilder.PageBuilder#setDecorator(com.atlassian.jira.web.pagebuilder.Decorator)", "com.atlassian.jira.web.pagebuilder.PageBuilderServiceSpi#initForRequest(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse,com.atlassian.jira.web.pagebuilder.DecoratorListener,javax.servlet.ServletContext)", "com.atlassian.servicedesk.spi.sla.condition.TimeMetricHitCondition#getHistory(com.atlassian.jira.issue.Issue)", "com.atlassian.servicedesk.spi.sla.condition.TimeMetricMatchCondition#getHistory(com.atlassian.jira.issue.Issue)"], "field": ["com.atlassian.jira.action.JiraActionSupport#log", "com.atlassian.jira.config.CoreFeatures#LICENSE_ROLES_ENABLED", "com.atlassian.jira.index.ha.DefaultIndexSnapshotOperator#BACKUP_COUNT", "com.atlassian.jira.index.ha.IndexSnapshotService#BACKUP_COUNT", "com.atlassian.jira.index.ha.IndexSnapshotService#DEFAULT_COUNT", "com.atlassian.jira.issue.action.IssueActionComparator#COMPARATOR", "com.atlassian.jira.issue.fields.util.VersionHelperBean#NEW_VERSION_RREFIX", "com.atlassian.jira.issue.index.IndexingLimitsHelper#DEFAULT_INDEXING_LIMIT", "com.atlassian.jira.************************#WEBACTIONS_USAGE_ANALYTICS", "com.atlassian.jira.plugin.bigpipe.CollectingExecutor#DEFAULT_PRIORITY", "com.atlassian.jira.plugin.jql.function.AbstractUserBasedFunction#userUtil", "com.atlassian.jira.user.preferences.PreferenceKeys#USER_JQL_AUTOCOMPLETE_DISABLED", "com.atlassian.jira.web.action.admin.translation.ViewTranslations#ISSUECONSTANT_PRIORITY", "com.atlassian.jira.web.action.admin.translation.ViewTranslations#ISSUECONSTANT_ISSUETYPE", "com.atlassian.jira.web.action.admin.translation.ViewTranslations#ISSUECONSTANT_STATUS", "com.atlassian.jira.web.action.admin.translation.ViewTranslations#ISSUECONSTANT_RESOLUTION", "com.atlassian.jira.web.pagebuilder.AbstractJspDecorator#bodyPrePath", "com.atlassian.jira.web.pagebuilder.AbstractJspDecorator#headPostPath", "com.atlassian.jira.web.pagebuilder.AbstractJspDecorator#headPrePath", "com.atlassian.jira.web.pagebuilder.AbstractJspDecorator#bodyPostPath", "com.atlassian.jira.web.pagebuilder.AbstractJspDecorator#webResourceAssembler"], "versionRemovedInShortName": "Jira 9"}