div.admin-spinner {
    background-image: url(./spinner.svg);

    width: 100%;
    height: 400px;

    background-color: lightgray;
    background-position: center;
    background-size: 100px 100px;
    background-repeat: no-repeat;
}

div.admin-blank {

    width: 100%;
    height: 400px;

    color: white;
    fill: white;
}

/* This classname is added to the document.body when the home page modal is opened, and removed when it's closed */
.ReactModal__Body--open {
    overflow: hidden;
}

/* This classname .atlaskit-portal-container is added to the document.body when the dropdown opens, this is a work around to get rid of blue border. */
div.atlaskit-portal-container .atlaskit-portal > div:focus-visible:focus { 
    box-shadow: 0 4px 8px -2px rgb(9 30 66 / 25%), 0 0 1px rgb(9 30 66 / 31%);
 }