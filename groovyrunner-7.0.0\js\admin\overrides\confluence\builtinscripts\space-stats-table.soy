{namespace ScriptRunner.Templates.SpaceStatistics}

/**
 * @param creationDate
 * @param spaceName
 * @param statisticsData
 */
{template .tableIndividual}
<div class="chart-table-wrapper" >
    <table id="space-stats-table" class="aui">
        <thead>
            <tr>
                <th>Space name</th>
            {foreach $column in $statisticsData.labels}
                <th class="clickable-sorter">{$column}<span class=""></span>
                </th>
            {/foreach}
                <th class="clickable-sorter">Creation date<span class=""></span>
                </th>
            </tr>
        </thead>
        <tbody>
            {for $i in range(length($spaceName))}
                <tr class="table-zebra">
                    <td class="table-bold-first-column">{$spaceName[$i]}</td>
                    {foreach $row in $statisticsData.datasets[$i].data}
                    <td>{$row}</td>
                    {/foreach}
                    <td>{$creationDate[$i]}</td>
                </tr>
            {/for}
{/template}

/**
 * @param statisticsData
 */
{template .tableTotal}
    <tr class="table-zebra">
        <td class="table-bold-first-column stat-table-total">Totals</td>
    {for $i in range(length($statisticsData.labels))}
        <td class="stat-table-total">{$statisticsData.datasets[0].data[$i]}</td>
    {/for}
        <td class="stat-table-total">N/A</td>
    </tr>
        </tbody>
    </table>
</div>
{/template}