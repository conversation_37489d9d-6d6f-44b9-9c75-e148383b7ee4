# /params


Here you will find all of the user interaction components for script parameters (e.g., checkboxes, lists, text area).
most of these  interaction components are  wrapped using the **FieldWrapper**  component which provides a label and error text.

### ParamField

All of these interaction components will eventually be used to dynamically generate a form based from an array of param field types (for example 'checkbox', 'jqlQuery', etc). Because of this, an additional mapping layer has been provided which will be consumed as the  **ParamField**  component.
You can now pass a param field object that has a 'type' , and any additional properties into the **ParamField** component and you can expect that it will return the highest matching component (see ParamField.tsx for more details).

