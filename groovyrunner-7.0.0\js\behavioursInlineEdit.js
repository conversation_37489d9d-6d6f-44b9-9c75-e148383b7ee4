/**
 * <PERSON>les inline edit on the view screen
 *
 * Work required here:
 * - Move to behaviours dir
 * - Use common code from that directory
 * - Should use a parameter to indicate that this is behaviours for the VIEW_ISSUE screen
 */

import {serializeQueryString} from "./behaviours/serialiseQueryString";
import {trackedFetch} from "./behaviours/fetch-utils";

require('./jquery.livequery');

(function ($) {
    "use strict";

    let behaviours = {};
    window.addEventListener('load', function () {
        initialiseEnableEditForm();
        JIRA.bind(JIRA.Events.ISSUE_REFRESHED, initialiseEnableEditForm);
        JIRA.bind(JIRA.Events.INLINE_EDIT_FOCUSED, inlineEditBehaviour);
        JIRA.bind(JIRA.Events.INLINE_EDIT_SAVE_COMPLETE, initialiseEnableEditForm);
        // hack for JA, which emits no events - https://jira.atlassian.com/browse/JSW-13527
        // When only supporting IE11+ switch to MutationObserver, or expire and re-add the livequery
        if (window.location.href.indexOf("RapidBoard.jspa?") > -1) {
            $("#ghx-detail-issue").livequery(function () {
                initialiseEnableEditForm();
            });
        }
    }, false);

    const initialiseEnableEditForm = function () {
        behaviours = {};

        /*
        Wondering why it's so complicated to get the Issue ID? Lots of different fallbacks are required
        for different Jira versions and contexts. Coupled with that, the JIRA.Issues.Api.getSelectedIssueId method
        intermittently fails to return anything.

        See https://stash.adaptavist.com/projects/SR/repos/scriptrunner/pull-requests/1069/diff
        */
        const jiraAgileBoardPrimarySelectedIssue = document.querySelector('.ghx-selected-primary');
        let issueId = JIRA.Issues.Api.getSelectedIssueId() ||
            (jiraAgileBoardPrimarySelectedIssue ? jiraAgileBoardPrimarySelectedIssue.dataset.issueId : null) ||
            $("input[name=id]").val() ||
            $("#key-val").attr("rel");

        if (issueId) {
            // get validators for new issue
            doGetValidatorsForEnable(issueId);
        }
    };

    const doGetValidatorsForEnable = function (issueId) {
        trackedFetch(
            `${AJS.contextPath()}/rest/scriptrunner/behaviours/latest/validators.json?${serializeQueryString(
                {
                    issueId,
                }
            )}`,
            {
                method: 'POST',
                body  : JSON.stringify({
                    form: {},
                }),
            }
        )
            .then(({result, error}) => {
                if (error) {
                    console.error('Error retrieving validators', error)
                }
                if (result != null) {
                    behaviours = result;
                    enableEditFormInViewScreen();
                }
            })
    };

    function inlineEditBehaviour() {
        const $active = $(':focus')
        const activeId = getFieldIdForField($active)
        if (activeId && activeId in behaviours && !behaviours[activeId].allowInlineEdit) {
            JIRA.trigger(JIRA.Events.INLINE_EDIT_BLURRED, [activeId]);
            popEditScreen($active)
        }
    }

    function enableEditFormInViewScreen() {
        // we can't handle tabs in inline mode at the moment
        delete behaviours.__TABS__;

        // if we require a behaviour then we enable the edit popup form
        setTimeout(enableEditForm, 300);
    }

    function enableEditForm() {
        let fieldsInBehaviour = [];
        $.each(behaviours, function (k, val) {
            if (!val["allowInlineEdit"]) {
                fieldsInBehaviour.push(k);
            }
        });

        console.log("Enable the edit popup for fields: ", fieldsInBehaviour);

        // comes from JIRA.Issues.FocusShifter - ctrl+shift+f in debugger to find the file
        try {
            JIRA.Issues.FocusShifter.getSuggestions = JIRA.Issues.FocusShifter.getSuggestions = function () {
                let fields = JIRA.Issues.Api.getFieldsOnSelectedIssue();
                return _
                    .chain(fields.models)
                    .filter(function (field) {
                        return !($.inArray(field.id, fieldsInBehaviour) > -1);
                    })
                    .filter(JIRA.Components.IssueEditor.Models.Field.IS_EDITABLE)
                    .filter(function (field) {
                        return field.id !== 'comment'
                    })
                    .map(function (field) {
                        return {
                            label: field.getLabel(),
                            value: field.id
                        };
                    })
                    .value();
            };
        } catch (e) {
            console.log("FocusShifter wasn't ready");
        }

        $(".editable-field").each(function () {
            let fieldId = getFieldIdForField($(this));
            if (typeof fieldId !== "undefined") {
                if ($.inArray("labels", fieldsInBehaviour) > -1 && fieldId.includes("labels")) {
                    enableEditFormForField($(this));
                }
                if ($.inArray(fieldId, fieldsInBehaviour) > -1) {
                    enableEditFormForField($(this));
                }
            }
        });
    }

    function getFieldIdForField(field) {

        let fieldId = field.attr("id");

        if (!fieldId) {
            //case for labels, add to it if you find more special fields.
            if (field.hasClass("labels-wrap")) {
                fieldId = field.parent().attr("id")
            }
        } else {
            fieldId = fieldId.replace("-val", "");
        }

        // converting to fixVersion and due date so that behaviours can work with this field
        // the Field name must have changed before jira 6.0
        if (fieldId === "fixfor" || fieldId === "fixVersions-textarea") {
            return "fixVersions"
        }
        if (fieldId === "components-textarea") {
            return "components"
        }
        if (fieldId === "due-date") {
            return "duedate"
        }
        if (fieldId === "type" || fieldId === "issuetype-field") {
            return "issuetype"
        }

        return fieldId
    }

    //Function to enable the click handler attached to fields with behaviours
    function enableEditFormForField(view) {
        if ($(view).hasClass("inactive")) {
            if ($(view).is('.editable-field') || ($(view).is('.editable-field > *'))) {
                view.attr('title', "Edit in dialog");
                view.removeClass("inactive");

                //This is done to avoid id being null and hence breaking the selector at the else if something goes wrong.
                const id = (view.context["id"]) ? view.context["id"] : "null";
                let button;

                // If this selector has the edit button, display only when clicking the edit, instead of every element.
                if (id === "null" && view.hasClass("labels-wrap")) {
                    button = $(`#${$(view).parent().attr("id")} > div > span`);
                } else {
                    button = $(`#${id} > span.overlay-icon.aui-icon.aui-icon-small.aui-iconfont-edit`);
                }

                if (button.length) {
                    button.click(function () {
                        popEditScreen(view);
                    });
                } else {
                    view.click(function () {
                        popEditScreen(view);
                    });
                }
            }
        }
    }


    function popEditScreen(view) {
        $(".issueaction-edit-issue").click();

        // Create Deferred object to be passed into jquery.when() method
        let dialogLoadedEventDfd = $.Deferred();

        // Resolve Deferred object when Edit issue dialog is ready
        $(document).on('dialogContentReady', function (e) {
            dialogLoadedEventDfd.resolve()
        });

        if ($(view).prop("id") === "type-val") {
            return
        }
        // Ensure that no code is ran before the Edit dialog is open
        $.when(dialogLoadedEventDfd).done(function () {
            //Use a timeout to ensure that all form elements are present
            setTimeout(function () {
                let target = formFieldSelector(view.attr("id"));

                //Remove focus that is automatically set to the Summary field
                //Only one field can have focus at a time
                $("#summary").blur();
                if (target != null) {
                    if (target.closest(".group")[0]) {
                        //Scroll to target field-set element
                        $(".form-body").animate({
                            scrollTop: target.closest(".group").position().top
                        }, 1000);
                    } else {
                        //Scroll to and focus in on target element
                        $(".form-body").animate({
                            scrollTop: target.closest(".field-group").position().top
                        }, 1000);
                        target.focus();
                    }
                } else {
                    console.log("ERROR: Element was not found and the behaviour cannot be applied!");
                }
            }, 100);
        });
    }

    //Function used to match the View screen fields with the Form fields
    function formFieldSelector(viewFieldID) {
        //Remove -val from the viewFieldID
        viewFieldID = viewFieldID.replace("-val", "");

        // converting to fixVersion and due date so that behaviours can work with this field
        // the Field name must have changed before jira 6.0
        if (viewFieldID === "fixfor") {
            viewFieldID = "fixVersions"
        }
        if (viewFieldID === "due-date") {
            viewFieldID = "duedate"
        }

        let targetElement;
        //Get the elements that contain the viewFieldID from the form
        $(".form-body").find("[id*=" + viewFieldID + "]").each(function () {
            //Grab tag name and ensure that they are input-type tags
            let tagName = $(this).prop("tagName");
            if (tagName === "TEXTAREA" || tagName === "INPUT")// || tagName == "SELECT")
            {
                targetElement = $(this);
            } else if (tagName === "SELECT") {
                //This style attribute will be present if the Select tag is the parent of a hidden select list
                //We want to ignore those hidden lists.
                if ($(this).attr("style") === "display: none;") {
                    //Continue to next iteration
                    return true;
                }
                targetElement = $(this);
            }

        });
        return targetElement
    }

})(AJS.$);

