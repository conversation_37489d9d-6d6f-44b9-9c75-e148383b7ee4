<!-- HTM<PERSON> from Jira 8.0, for an assignee select -->
<select id='assignee' multiple='multiple' class="single-user-picker js-assignee-picker aui-ss-select" style='display: none'>
    <optgroup id='assignee-group-suggested' label='Suggestions' data-weight='0'>
        <option value='admin'>Mr Admin</option>
        <option value=''>Unassigned</option>
        <option selected='selected' value='-1'>
            Automatic
        </option>
    </optgroup>
</select>

<select class="select cf-select" name="single" id="single">
    <option value="-1">None</option>
    <option value="10003" selected="selected">AAA</option>
    <option value="10004">BBB</option>
    <option value="10005">CCC</option>
</select>

<input class="text long-field" id="summary" name="summary" type="text" value="the summary">

<input class="text long-field" id="empty" name="summary" type="text" value="">

<textarea
        class="textarea long-field wiki-textfield long-field wiki-editor-initialised wiki-edit-wrapped richeditor-cover"
        id="environment" name="environment"
>the environment</textarea>