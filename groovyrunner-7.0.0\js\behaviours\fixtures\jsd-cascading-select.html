<!-- HTML from Jira 8.16 JSM, for a cascading select field group -->
<div class='field-group'>
    <label class='field-label' for='customfield_00000' id='customfield_00000-label'>
        CascadingSelect
    </label>
    <div class='field-container'>
        <div class='cp-cascading-select aui-field-cascadingselect trigger-cascading-select'>
            <div
                class='select2-container cascadingselect-parent full-width-field aui-select2-container'
                id='s2id_customfield_00000'
            ></div>
            <select id='customfield_00000' name='customfield_00000'></select>
            <div
                class='select2-container cascadingselect-child full-width-field aui-select2-container'
                id='s2id_customfield_00000:1'
            ></div>
            <select
                class='cascadingselect-child full-width-field select2-offscreen'
                name='customfield_00000:1'
                id='customfield_00000:1'
                aria-labelledby='customfield_00000-label'
                tabindex='-1'
            >
                <option class='default-option' value=''>
                    None
                </option>
            </select>
        </div>
    </div>
    <div class="description" id="customfield_00000-helper"><p>field description</p></div>
</div>
