import {trackedFetch} from "../fetch-utils";
import {clearAuiSingleSelect} from "../fields/fieldutils/select-utils";

export const fetchSprint = async (sprintId) => {
    const sprintReq = await trackedFetch(`${AJS.contextPath()}/rest/agile/1.0/sprint/${sprintId}`);

    if (sprintReq.error) {
        console.log("Erroring, can't set sprint, status:", sprintReq.error);
        throw new Error("Sprint not found");
    }

    return sprintReq.result
};

export const setSprintField = ($field, {id, name}) => {
    const sprintIdStr = id.toString();
    const $option = $("<option/>", {text: name, value: sprintIdStr, title: name});
    $option.data("descriptor", new AJS.ItemDescriptor({
        value: sprintIdStr,
        label: name,
    }));

    $field.append($option);
    $field.trigger("set-selection-value", sprintIdStr);
};

export const fetchEpic = async (epicId, $field) => {
    // find ID of epic name
    const fieldsReq = await trackedFetch(`${AJS.contextPath()}/rest/api/2/field`);

    const fields = fieldsReq.result

    let epicNameField = _.find(fields, function (item) {
        return item.schema && item.schema.custom && item.schema.custom === "com.pyxis.greenhopper.jira:gh-epic-label"
    });

    if (!epicNameField) {
        throw("Failed to find ID of Epic Link field");
    }

    if (!epicId) {
        clearAuiSingleSelect($field)
    }

    const epicFieldReq = await trackedFetch(`${AJS.contextPath()}/rest/api/2/issue/${epicId}?fields=${epicNameField.id}`)

    const data = epicFieldReq.result
    const epicKey = "key:" + data.key;

    if (data.fields && data.fields[epicNameField.id]) {
        const $option = $("<option/>", {text: data.fields[epicNameField.id], value: epicKey});
        $option.data("descriptor", new AJS.ItemDescriptor({
            value: epicKey,
            label: data.fields[epicNameField.id]
        }));

        $field.append($option);
        $field.trigger("set-selection-value", epicKey);
    } else {
        throw new Error("Failed to read epic name field from issue: " + epicId + " - perhaps it wasn't an epic");
    }
};