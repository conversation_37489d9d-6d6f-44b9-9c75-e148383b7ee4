/*! For license information please see bhInlineEditPluginResources.ee4d7089babd8223fb74.js.LICENSE.txt */
"use strict";(self.webpackJsonpScriptRunner=self.webpackJsonpScriptRunner||[]).push([["bhInlineEditPluginResources"],{74729:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(o,i){function u(e){try{s(r.next(e))}catch(e){i(e)}}function a(e){try{s(r.throw(e))}catch(e){i(e)}}function s(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(u,a)}s((r=r.apply(e,t||[])).next())}))},o=this&&this.__generator||function(e,t){var n,r,o,i,u={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function a(i){return function(a){return function(i){if(n)throw new TypeError("Generator is already executing.");for(;u;)try{if(n=1,r&&(o=2&i[0]?r.return:i[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,i[1])).done)return o;switch(r=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return u.label++,{value:i[1],done:!1};case 5:u.label++,r=i[1],i=[0];continue;case 7:i=u.ops.pop(),u.trys.pop();continue;default:if(!(o=u.trys,(o=o.length>0&&o[o.length-1])||6!==i[0]&&2!==i[0])){u=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){u.label=i[1];break}if(6===i[0]&&u.label<o[1]){u.label=o[1],o=i;break}if(o&&u.label<o[2]){u.label=o[2],u.ops.push(i);break}o[2]&&u.ops.pop(),u.trys.pop();continue}i=t.call(e,u)}catch(e){i=[6,e],r=0}finally{n=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,a])}}},i=this&&this.__values||function(e){var t="function"==typeof Symbol&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},u=Object.create,a=Object.defineProperty,s=Object.defineProperties,c=Object.getOwnPropertyDescriptor,l=Object.getOwnPropertyDescriptors,f=Object.getOwnPropertyNames,d=Object.getOwnPropertySymbols,p=Object.getPrototypeOf,h=Object.prototype.hasOwnProperty,v=Object.prototype.propertyIsEnumerable,y=function(e,t,n){return t in e?a(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n},g=function(e,t){var n,r;for(var o in t||(t={}))h.call(t,o)&&y(e,o,t[o]);if(d)try{for(var u=i(d(t)),a=u.next();!a.done;a=u.next()){o=a.value;v.call(t,o)&&y(e,o,t[o])}}catch(e){n={error:e}}finally{try{a&&!a.done&&(r=u.return)&&r.call(u)}finally{if(n)throw n.error}}return e},m=function(e,t){return s(e,l(t))},b=function(e){return a(e,"__esModule",{value:!0})};!function(e,t){for(var n in b(e),t)a(e,n,{get:t[n],enumerable:!0})}(t,{fetchJson:function(){return S},getRequestCountValue:function(){return _},trackedFetchFactory:function(){return q},wrappedFetch:function(){return O}});var x,w=(x=n(60208),function(e,t,n){var r,o;if(t&&"object"==typeof t||"function"==typeof t){var u=function(r){h.call(e,r)||"default"===r||a(e,r,{get:function(){return t[r]},enumerable:!(n=c(t,r))||n.enumerable})};try{for(var s=i(f(t)),l=s.next();!l.done;l=s.next())u(l.value)}catch(e){r={error:e}}finally{try{l&&!l.done&&(o=s.return)&&o.call(s)}finally{if(r)throw r.error}}}return e}(b(a(null!=x?u(p(x)):{},"default",x&&x.__esModule&&"default"in x?{get:function(){return x.default},enumerable:!0}:{value:x,enumerable:!0})),x)),I="Content-Type",E="application/json",q=function(e){return function(t,n){return R(e),O(t,n).finally((function(){return P(e)}))}};function A(e){var t=e.headers.get(I);return t&&-1===t.indexOf("text/html")&&-1===t.indexOf("text/plain")?-1!==t.indexOf("application/json")||t.startsWith("application/")&&-1!==t.indexOf("+json;")?e.text().then((function(e){return e.length>0?JSON.parse(e):null})):t.startsWith("image/")?e.blob():Promise.resolve(null):e.text()}var O=function(e,t){return r(void 0,void 0,void 0,(function(){var n;return o(this,(function(r){return n=(0,w.deepmerge)(function(){var e;return{credentials:"same-origin",headers:(e={"Cache-Control":"no-cache"},e[I]=E,e["X-Atlassian-token"]="no-check",e)}}(),t||{}),[2,fetch(e,n).then((function(e){if(!e.ok){var t={error:e.statusText||"request failed",response:e};return A(e).then((function(e){return Promise.resolve(m(g({},t),{errorResult:e}))})).catch((function(e){return Promise.resolve(t)}))}return A(e).then((function(t){return Promise.resolve({result:t,response:e})})).catch((function(t){return n.method&&["delete","post"].includes(n.method.toLowerCase())?Promise.resolve({result:{},response:e}):(console.warn("Could not parse: ".concat(t)),Promise.resolve({error:"Could not parse: ".concat(t)}))}))})).catch((function(e){return console.warn("Error fetching",e),Promise.resolve({error:"Network ".concat(e)})}))]}))}))},S=function(e,t){return r(void 0,void 0,void 0,(function(){var n;return o(this,(function(r){return[2,O(e,m(g({},t),{headers:m(g({},null!=(n=null==t?void 0:t.headers)?n:{}),{Accept:E})}))]}))}))},R=function(e){e&&e.length&&C(e,_(e)+1)},P=function(e){e&&e.length&&C(e,_(e)-1)},_=function(e){return Number.parseInt(document.body.dataset[e]||"0",10)},C=function(e,t){document.body.dataset[e]=t.toString()}},40270:(e,t,n)=>{n.d(t,{e:()=>i,k:()=>u});var r=n(74729),o="behavioursServerCallCount",i=function(){return(0,r.getRequestCountValue)(o)},u=(0,r.trackedFetchFactory)(o)},39507:(e,t,n)=>{n.d(t,{F:()=>u});var r=n(17619),o=n(29577),i=n(16897),u=function(e){return r.stringify(o.Z((function(e){return!i.Z(e)}),e))}},26268:(e,t,n)=>{var r=n(39507),o=n(40270);n(46086),function(e){var t={};window.addEventListener("load",(function(){n(),JIRA.bind(JIRA.Events.ISSUE_REFRESHED,n),JIRA.bind(JIRA.Events.INLINE_EDIT_FOCUSED,u),JIRA.bind(JIRA.Events.INLINE_EDIT_SAVE_COMPLETE,n),window.location.href.indexOf("RapidBoard.jspa?")>-1&&e("#ghx-detail-issue").livequery((function(){n()}))}),!1);var n=function(){t={};var n=document.querySelector(".ghx-selected-primary"),r=JIRA.Issues.Api.getSelectedIssueId()||(n?n.dataset.issueId:null)||e("input[name=id]").val()||e("#key-val").attr("rel");r&&i(r)},i=function(e){(0,o.k)("".concat(AJS.contextPath(),"/rest/scriptrunner/behaviours/latest/validators.json?").concat((0,r.F)({issueId:e})),{method:"POST",body:JSON.stringify({form:{}})}).then((function(e){var n=e.result,r=e.error;r&&console.error("Error retrieving validators",r),null!=n&&(delete(t=n).__TABS__,setTimeout(a,300))}))};function u(){var n=e(":focus"),r=s(n);r&&r in t&&!t[r].allowInlineEdit&&(JIRA.trigger(JIRA.Events.INLINE_EDIT_BLURRED,[r]),l(n))}function a(){var n=[];e.each(t,(function(e,t){t.allowInlineEdit||n.push(e)})),console.log("Enable the edit popup for fields: ",n);try{JIRA.Issues.FocusShifter.getSuggestions=JIRA.Issues.FocusShifter.getSuggestions=function(){var t=JIRA.Issues.Api.getFieldsOnSelectedIssue();return _.chain(t.models).filter((function(t){return!(e.inArray(t.id,n)>-1)})).filter(JIRA.Components.IssueEditor.Models.Field.IS_EDITABLE).filter((function(e){return"comment"!==e.id})).map((function(e){return{label:e.getLabel(),value:e.id}})).value()}}catch(e){console.log("FocusShifter wasn't ready")}e(".editable-field").each((function(){var t=s(e(this));void 0!==t&&(e.inArray("labels",n)>-1&&t.includes("labels")&&c(e(this)),e.inArray(t,n)>-1&&c(e(this)))}))}function s(e){var t=e.attr("id");return t?t=t.replace("-val",""):e.hasClass("labels-wrap")&&(t=e.parent().attr("id")),"fixfor"===t||"fixVersions-textarea"===t?"fixVersions":"components-textarea"===t?"components":"due-date"===t?"duedate":"type"===t||"issuetype-field"===t?"issuetype":t}function c(t){if(e(t).hasClass("inactive")&&(e(t).is(".editable-field")||e(t).is(".editable-field > *"))){t.attr("title","Edit in dialog"),t.removeClass("inactive");var n=t.context.id?t.context.id:"null",r=void 0;(r="null"===n&&t.hasClass("labels-wrap")?e("#".concat(e(t).parent().attr("id")," > div > span")):e("#".concat(n," > span.overlay-icon.aui-icon.aui-icon-small.aui-iconfont-edit"))).length?r.click((function(){l(t)})):t.click((function(){l(t)}))}}function l(t){e(".issueaction-edit-issue").click();var n=e.Deferred();e(document).on("dialogContentReady",(function(e){n.resolve()})),"type-val"!==e(t).prop("id")&&e.when(n).done((function(){setTimeout((function(){var n=function(t){"fixfor"===(t=t.replace("-val",""))&&(t="fixVersions");"due-date"===t&&(t="duedate");var n;return e(".form-body").find("[id*="+t+"]").each((function(){var t=e(this).prop("tagName");if("TEXTAREA"===t||"INPUT"===t)n=e(this);else if("SELECT"===t){if("display: none;"===e(this).attr("style"))return!0;n=e(this)}})),n}(t.attr("id"));e("#summary").blur(),null!=n?n.closest(".group")[0]?e(".form-body").animate({scrollTop:n.closest(".group").position().top},1e3):(e(".form-body").animate({scrollTop:n.closest(".field-group").position().top},1e3),n.focus()):console.log("ERROR: Element was not found and the behaviour cannot be applied!")}),100)}))}}(AJS.$)},46086:(e,t,n)=>{var r,o,i;o=[n(65311)],r=function(e,t){function n(e,t,n,r){return!(e.selector!=t.selector||e.context!=t.context||n&&n.$lqguid!=t.fn.$lqguid||r&&r.$lqguid!=t.fn2.$lqguid)}e.extend(e.fn,{livequery:function(t,o){var i,u=this;return e.each(r.queries,(function(e,r){if(n(u,r,t,o))return(i=r)&&!1})),(i=i||new r(u.selector,u.context,t,o)).stopped=!1,i.run(),u},expire:function(t,o){var i=this;return e.each(r.queries,(function(e,u){n(i,u,t,o)&&!i.stopped&&r.stop(u.id)})),i}});var r=e.livequery=function(t,n,o,i){var u=this;return u.selector=t,u.context=n,u.fn=o,u.fn2=i,u.elements=e([]),u.stopped=!1,u.id=r.queries.push(u)-1,o.$lqguid=o.$lqguid||r.guid++,i&&(i.$lqguid=i.$lqguid||r.guid++),u};r.prototype={stop:function(){var t=this;t.stopped||(t.fn2&&t.elements.each(t.fn2),t.elements=e([]),t.stopped=!0)},run:function(){var t=this;if(!t.stopped){var n=t.elements,r=e(t.selector,t.context),o=r.not(n),i=n.not(r);t.elements=r,o.each(t.fn),t.fn2&&i.each(t.fn2)}}},e.extend(r,{guid:0,queries:[],queue:[],running:!1,timeout:null,registered:[],checkQueue:function(){if(r.running&&r.queue.length)for(var e=r.queue.length;e--;)r.queries[r.queue.shift()].run()},pause:function(){r.running=!1},play:function(){r.running=!0,r.run()},registerPlugin:function(){e.each(arguments,(function(t,n){if(e.fn[n]&&!(e.inArray(n,r.registered)>0)){var o=e.fn[n];e.fn[n]=function(){var e=o.apply(this,arguments);return r.run(),e},r.registered.push(n)}}))},run:function(n){n!==t?e.inArray(n,r.queue)<0&&r.queue.push(n):e.each(r.queries,(function(t){e.inArray(t,r.queue)<0&&r.queue.push(t)})),r.timeout&&clearTimeout(r.timeout),r.timeout=setTimeout(r.checkQueue,20)},stop:function(n){n!==t?r.queries[n].stop():e.each(r.queries,r.prototype.stop)}}),r.registerPlugin("append","prepend","after","before","wrap","attr","removeAttr","addClass","removeClass","toggleClass","empty","remove","html","prop","removeProp"),e((function(){r.play()}))},void 0===(i="function"==typeof r?r.apply(t,o):r)||(e.exports=i)},65311:e=>{e.exports=jQuery}},e=>{e.O(0,["bhResources"],(()=>{return t=26268,e(e.s=t);var t}));e.O()}]);