"use strict";(self.webpackJsonpScriptRunner=self.webpackJsonpScriptRunner||[]).push([["bhIssuePluginResources"],{23869:(e,r,s)=>{var i=s(35484),n=s(60247),t=s(53281),a=s(75583),o=(s(5667),jQuery),u=(0,a.j)("sr-behaviours"),c=window.location.href.includes("CreateIssue!default.jspa"),l=window.location.href.includes("CreateIssueDetails!init.jspa"),_=new t.kc,d=function(e){return _.initialiseBehaviours(null,o(document),null,e)},f=function(){return d(!1)};c&&o((function(){o("#project").on("change",f),o("#issuetype").on("change",f)})),(0,i.nM)()||o((function(){var e=!l&&o("form").map((function(e,r){return o(r).find("div").first().find(".aui-message-error").length})).get().some((function(e){return!!e}));d(e),JIRA.bind(JIRA.Events.NEW_CONTENT_ADDED,(function(e,r,s){_.initialiseBehaviours(e,r,s,!1),c&&o("#create-issue-dialog .form-footer .cancel").on("click",f)})),o(document).ajaxError((function(e,r){if(204!==r.status&&!o.isEmptyObject(_.getBehaviours())&&400===r.status)try{var s=JSON.parse(r.responseText);"errors"in s&&n.uj(_.getTabOperations,s,_.getBehaviours());var i=o(e.target).find("div.jira-dialog-content, div.jira-dialog-core-content");_.initialiseBehaviours(null,i,null,!0)}catch(e){u.error("startup failed",e)}}))})),window.JBHV=_},38860:e=>{e.exports=require("aui/flag")},38871:e=>{e.exports=require("jira/editor/registry")}},e=>{var r=r=>e(e.s=r);e.O(0,["bhResources","default-src_main_resources_js_behaviours_serialiseQueryString_ts-src_main_resources_js_compon-2a076c","default-src_main_resources_js_behaviours_index_ts","default-src_main_resources_js_admin_util_generalUtils_ts-src_main_resources_js_behaviours_deb-a911b4"],(()=>(r(23869),r(28790))));e.O()}]);