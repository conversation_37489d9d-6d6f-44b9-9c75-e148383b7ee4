(self.webpackJsonpScriptRunner=self.webpackJsonpScriptRunner||[]).push([["bhPortal"],{55484:()=>{},60674:(e,t,r)=>{"use strict";r(6602);var n=r(39507),i=r(50777),a=r(35484),s=(r(19024),r(84806)),o=r(85321),c=(r(5667),r(74729)),u=function(e,t,r,n){return new(r||(r=Promise))((function(i,a){function s(e){try{c(n.next(e))}catch(e){a(e)}}function o(e){try{c(n.throw(e))}catch(e){a(e)}}function c(e){var t;e.done?i(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(s,o)}c((n=n.apply(e,t||[])).next())}))},l=function(e,t){var r,n,i,a,s={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return a={next:o(0),throw:o(1),return:o(2)},"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function o(a){return function(o){return function(a){if(r)throw new TypeError("Generator is already executing.");for(;s;)try{if(r=1,n&&(i=2&a[0]?n.return:a[0]?n.throw||((i=n.return)&&i.call(n),0):n.next)&&!(i=i.call(n,a[1])).done)return i;switch(n=0,i&&(a=[2&a[0],i.value]),a[0]){case 0:case 1:i=a;break;case 4:return s.label++,{value:a[1],done:!1};case 5:s.label++,n=a[1],a=[0];continue;case 7:a=s.ops.pop(),s.trys.pop();continue;default:if(!(i=s.trys,(i=i.length>0&&i[i.length-1])||6!==a[0]&&2!==a[0])){s=0;continue}if(3===a[0]&&(!i||a[1]>i[0]&&a[1]<i[3])){s.label=a[1];break}if(6===a[0]&&s.label<i[1]){s.label=i[1],i=a;break}if(i&&s.label<i[2]){s.label=i[2],s.ops.push(a);break}i[2]&&s.ops.pop(),s.trys.pop();continue}a=t.call(e,s)}catch(e){a=[6,e],n=0}finally{r=i=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,o])}}},f=function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,i,a=r.call(e),s=[];try{for(;(void 0===t||t-- >0)&&!(n=a.next()).done;)s.push(n.value)}catch(e){i={error:e}}finally{try{n&&!n.done&&(r=a.return)&&r.call(a)}finally{if(i)throw i.error}}return s},d=function(e,t,r){if(r||2===arguments.length)for(var n,i=0,a=t.length;i<a;i++)!n&&i in t||(n||(n=Array.prototype.slice.call(t,0,i)),n[i]=t[i]);return e.concat(n||Array.prototype.slice.call(t))};r(6602);var p=jQuery;p((function(){r(34166)().on("change",(function(e){return u(void 0,void 0,void 0,(function(){var e,t,r,v,h,b,m,y;return l(this,(function(_){switch(_.label){case 0:return(0,a.kK)()?(e=(0,a.EA)(),[4,(0,c.wrappedFetch)("".concat(AJS.contextPath(),"/rest/scriptrunner/behaviours/latest/portal/fields/edit?").concat((0,n.F)(e)))]):[3,4];case 1:return(t=_.sent()).error?(console.error("Failed to retrieve script fields details"),[2]):(r=t.result,[4,(0,s.bK)("form.vp-form,form.cp-request-form",{subtree:!0})]);case 2:return _.sent(),r.map((function(e){return u(void 0,void 0,void 0,(function(){var t,r,n,a,o;return l(this,(function(c){switch(c.label){case 0:return t=e.customfieldid,r=e.fieldconfigid,n=e.jsonEncodedParameters,[4,(0,s.bK)("input#".concat(t))];case 1:return a=c.sent(),o=p("<div/>",{class:"adaptavist-sr sr-custompicker","data-customfieldid":t,"data-fieldconfigid":r,"data-fieldparams":n}),p(a).after(o),p(a).replaceWith(p("<select/>",{multiple:"multiple",id:t,name:t}).hide()),(0,i.renderPickers)(null,o.parent(),!0),[2]}}))}))})),[4,(0,s.bK)("form.vp-form,form.cp-request-form",{subtree:!0})];case 3:_.sent();try{p(".aui-dropdown2").css("max-width","unset"),p(".aui-layer").css("position","absolute"),(v=new o.A).initialiseBehaviours(null,p(document),null,!1),p(document).ajaxError((function(e,t){p.isEmptyObject(v.getBehaviours())||400!==t.status||v.initialiseBehaviours(null,p(e.target),null,!0)}))}catch(e){console.error("Error initialising behaviours",e)}return[2];case 4:return(h=(0,a.vo)())?[4,(0,c.wrappedFetch)("".concat(AJS.contextPath(),"/rest/scriptrunner/behaviours/latest/portal/fields/view?").concat((0,n.F)(h)))]:[2];case 5:return(b=_.sent()).error?(console.error("Failed to retrieve script fields details"),[2]):[4,(0,s.bK)("ul.vp-activity-list")];case 6:return _.sent(),0===(m=b.result).length?(p("div.request-fields").attr("data-sr-values-populated","true"),[2]):(y=function(){m.forEach((function(e){var t=e.name,r=e.html,n=e.sequence,i=p("ul.vp-activity-list").find("dt").filter((function(e,r){return jQuery(r).text()===t}));if(i.length)i.next("dd").empty().html(r);else{var a=p("div.request-fields dl"),s=p("<dl>".concat(t,"</dl>")).append("<dt>".concat(r,"</dt>"));0===n?a.parent().find("header").after(s):n>=a.length?a.last().after(s):a.eq(n).before(s)}}))},new MutationObserver((function(e){e.some((function(e){return d([],f(e.addedNodes),!1).some((function(e){return e instanceof HTMLUListElement&&e.classList.contains("vp-activity-list")}))}))&&y()})).observe(p(".cv-request-details")[0],{childList:!0,subtree:!0}),y(),p("div.request-fields").attr("data-sr-values-populated","true"),[2])}}))}))}))}))},6602:(e,t,r)=>{e.exports=r},38860:e=>{"use strict";e.exports=require("aui/flag")},38871:e=>{"use strict";e.exports=require("jira/editor/registry")}},e=>{var t=t=>e(e.s=t);e.O(0,["bhResources","default-src_main_resources_js_behaviours_serialiseQueryString_ts-src_main_resources_js_compon-2a076c","default-src_main_resources_js_behaviours_index_ts","default-src_main_resources_js_admin_util_generalUtils_ts-src_main_resources_js_behaviours_deb-a911b4"],(()=>(t(55484),t(60674),t(28790))));e.O()}]);