{"size": 7, "limit": 25, "isLastPage": true, "values": [{"slug": "rep_1", "id": 1, "name": "rep_1", "hierarchyId": "f99c94774b6a4a65b003", "scmId": "git", "state": "AVAILABLE", "statusMessage": "Available", "forkable": true, "project": {"key": "PROJECT_1", "id": 1, "name": "Project 1", "description": "Default configuration project #1", "public": false, "type": "NORMAL", "links": {"self": [{"href": "http://localhost:8080/bitbucket/projects/PROJECT_1"}]}}, "public": false, "links": {"clone": [{"href": "ssh://git@localhost:7999/project_1/rep_1.git", "name": "ssh"}, {"href": "http://localhost:8080/bitbucket/scm/project_1/rep_1.git", "name": "http"}], "self": [{"href": "http://localhost:8080/bitbucket/projects/PROJECT_1/repos/rep_1/browse"}]}}, {"slug": "rep_1", "id": 57, "name": "rep_1", "hierarchyId": "f5334ea214c9ae4322c2", "scmId": "git", "state": "AVAILABLE", "statusMessage": "Available", "forkable": true, "project": {"key": "PROJECT_2", "id": 22, "name": "Project 2", "public": false, "type": "NORMAL", "links": {"self": [{"href": "http://localhost:8080/bitbucket/projects/PROJECT_2"}]}}, "public": false, "links": {"clone": [{"href": "http://localhost:8080/bitbucket/scm/project_2/rep_1.git", "name": "http"}, {"href": "ssh://git@localhost:7999/project_2/rep_1.git", "name": "ssh"}], "self": [{"href": "http://localhost:8080/bitbucket/projects/PROJECT_2/repos/rep_1/browse"}]}}, {"slug": "rep_10", "id": 14, "name": "rep_10", "hierarchyId": "fc807b5e6ec5c1668728", "scmId": "git", "state": "AVAILABLE", "statusMessage": "Available", "forkable": true, "project": {"key": "PROJECT_2", "id": 22, "name": "Project 2", "public": false, "type": "NORMAL", "links": {"self": [{"href": "http://localhost:8080/bitbucket/projects/PROJECT_2"}]}}, "public": false, "links": {"clone": [{"href": "ssh://git@localhost:7999/project_2/rep_10.git", "name": "ssh"}, {"href": "http://localhost:8080/bitbucket/scm/project_2/rep_10.git", "name": "http"}], "self": [{"href": "http://localhost:8080/bitbucket/projects/PROJECT_2/repos/rep_10/browse"}]}}, {"slug": "rep_11", "id": 15, "name": "rep_11", "hierarchyId": "d7a4c2e4fa1fdcfd67c3", "scmId": "git", "state": "AVAILABLE", "statusMessage": "Available", "forkable": true, "project": {"key": "PROJECT_2", "id": 22, "name": "Project 2", "public": false, "type": "NORMAL", "links": {"self": [{"href": "http://localhost:8080/bitbucket/projects/PROJECT_2"}]}}, "public": false, "links": {"clone": [{"href": "http://localhost:8080/bitbucket/scm/project_2/rep_11.git", "name": "http"}, {"href": "ssh://git@localhost:7999/project_2/rep_11.git", "name": "ssh"}], "self": [{"href": "http://localhost:8080/bitbucket/projects/PROJECT_2/repos/rep_11/browse"}]}}, {"slug": "rep_2", "id": 12, "name": "rep_2", "hierarchyId": "3f5828b0fe1c09a2c575", "scmId": "git", "state": "AVAILABLE", "statusMessage": "Available", "forkable": true, "project": {"key": "PROJECT_1", "id": 1, "name": "Project 1", "description": "Default configuration project #1", "public": false, "type": "NORMAL", "links": {"self": [{"href": "http://localhost:8080/bitbucket/projects/PROJECT_1"}]}}, "public": false, "links": {"clone": [{"href": "ssh://git@localhost:7999/project_1/rep_2.git", "name": "ssh"}, {"href": "http://localhost:8080/bitbucket/scm/project_1/rep_2.git", "name": "http"}], "self": [{"href": "http://localhost:8080/bitbucket/projects/PROJECT_1/repos/rep_2/browse"}]}}, {"slug": "rep_3", "id": 13, "name": "rep_3", "hierarchyId": "703be5482b8bf1417c0e", "scmId": "git", "state": "AVAILABLE", "statusMessage": "Available", "forkable": true, "project": {"key": "PROJECT_1", "id": 1, "name": "Project 1", "description": "Default configuration project #1", "public": false, "type": "NORMAL", "links": {"self": [{"href": "http://localhost:8080/bitbucket/projects/PROJECT_1"}]}}, "public": false, "links": {"clone": [{"href": "ssh://git@localhost:7999/project_1/rep_3.git", "name": "ssh"}, {"href": "http://localhost:8080/bitbucket/scm/project_1/rep_3.git", "name": "http"}], "self": [{"href": "http://localhost:8080/bitbucket/projects/PROJECT_1/repos/rep_3/browse"}]}}, {"slug": "rep_clone", "id": 16, "name": "rep_clone", "hierarchyId": "0b77af91bcf06213faa4", "scmId": "git", "state": "AVAILABLE", "statusMessage": "Available", "forkable": true, "project": {"key": "PROJECT_2", "id": 22, "name": "Project 2", "public": false, "type": "NORMAL", "links": {"self": [{"href": "http://localhost:8080/bitbucket/projects/PROJECT_2"}]}}, "public": false, "links": {"clone": [{"href": "http://localhost:8080/bitbucket/scm/project_2/rep_clone.git", "name": "http"}, {"href": "ssh://git@localhost:7999/project_2/rep_clone.git", "name": "ssh"}], "self": [{"href": "http://localhost:8080/bitbucket/projects/PROJECT_2/repos/rep_clone/browse"}]}}], "start": 0}