[["Issue Events", [["", "All Issue Events"], [1, "Issue Created"], [2, "Issue Updated"], [3, "Issue Assigned"], [4, "Issue Resolved"], [5, "Issue Closed"], [6, "Issue Commented"], [14, "Issue Comment Edited"], [17, "Issue Comment Deleted"], [7, "Issue Reopened"], [8, "Issue Deleted"], [9, "Issue Moved"], [10, "Work Logged On Issue"], [11, "Work Started On Issue"], [12, "Work Stopped On Issue"], [15, "Issue Worklog Updated"], [16, "Issue Worklog Deleted"], [13, "Generic Event"]]], ["Component Events", [["com.atlassian.jira.event.bc.project.component.AbstractProjectComponentEvent", "AbstractProjectComponentEvent"], ["com.atlassian.jira.issue.fields.event.ComponentCreatedInlineEvent", "ComponentCreatedInlineEvent"], ["com.atlassian.jira.event.bc.project.component.ProjectComponentCreatedEvent", "ProjectComponentCreatedEvent"], ["com.atlassian.jira.event.bc.project.component.ProjectComponentCreatedViaRestEvent", "ProjectComponentCreatedViaRestEvent"], ["com.atlassian.jira.event.bc.project.component.ProjectComponentDeletedEvent", "ProjectComponentDeletedEvent"], ["com.atlassian.jira.event.bc.project.component.ProjectComponentMergedEvent", "ProjectComponentMergedEvent"], ["com.atlassian.jira.event.bc.project.component.ProjectComponentUpdatedEvent", "ProjectComponentUpdatedEvent"]]], ["Custom Field Events", [["com.atlassian.jira.event.issue.field.AbstractCustomFieldEvent", "AbstractCustomFieldEvent"], ["com.atlassian.jira.event.issue.field.CustomFieldCreatedEvent", "CustomFieldCreatedEvent"], ["com.atlassian.jira.event.issue.field.CustomFieldDeletedEvent", "CustomFieldDeletedEvent"], ["com.atlassian.jira.event.issue.field.CustomFieldUpdatedEvent", "CustomFieldUpdatedEvent"]]], ["Directory Events", [["com.atlassian.crowd.event.login.AllPasswordsExpiredEvent", "AllPasswordsExpiredEvent"], ["com.atlassian.crowd.event.group.AutoGroupCreatedEvent", "AutoGroupCreatedEvent"], ["com.atlassian.crowd.event.group.AutoGroupMembershipCreatedEvent", "AutoGroupMembershipCreatedEvent"], ["com.atlassian.crowd.event.group.AutoGroupMembershipDeletedEvent", "AutoGroupMembershipDeletedEvent"], ["com.atlassian.crowd.event.user.AutoUserCreatedEvent", "AutoUserCreatedEvent"], ["com.atlassian.crowd.event.user.AutoUserUpdatedEvent", "AutoUserUpdatedEvent"], ["com.atlassian.crowd.event.directory.DirectoryCreatedEvent", "DirectoryCreatedEvent"], ["com.atlassian.crowd.event.directory.DirectoryDeletedEvent", "DirectoryDeletedEvent"], ["com.atlassian.crowd.event.directory.DirectoryUpdatedEvent", "DirectoryUpdatedEvent"], ["com.atlassian.crowd.event.group.GroupAttributeDeletedEvent", "GroupAttributeDeletedEvent"], ["com.atlassian.crowd.event.group.GroupAttributeStoredEvent", "GroupAttributeStoredEvent"], ["com.atlassian.crowd.event.group.GroupCreatedEvent", "GroupCreatedEvent"], ["com.atlassian.crowd.event.group.GroupDeletedEvent", "GroupDeletedEvent"], ["com.atlassian.crowd.event.group.GroupMembershipCreatedEvent", "GroupMembershipCreatedEvent"], ["com.atlassian.crowd.event.group.GroupMembershipDeletedEvent", "GroupMembershipDeletedEvent"], ["com.atlassian.crowd.event.group.GroupMembershipsCreatedEvent", "GroupMembershipsCreatedEvent"], ["com.atlassian.crowd.event.group.GroupUpdatedEvent", "GroupUpdatedEvent"], ["com.atlassian.crowd.event.user.ResetPasswordEvent", "ResetPasswordEvent"], ["com.atlassian.crowd.event.role.RoleCreatedEvent", "RoleCreatedEvent"], ["com.atlassian.crowd.event.role.RoleDeletedEvent", "RoleDeletedEvent"], ["com.atlassian.crowd.event.role.RoleMembershipCreatedEvent", "RoleMembershipCreatedEvent"], ["com.atlassian.crowd.event.role.RoleMembershipDeletedEvent", "RoleMembershipDeletedEvent"], ["com.atlassian.crowd.event.role.RoleUpdatedEvent", "RoleUpdatedEvent"], ["com.atlassian.crowd.event.user.UserAttributeDeletedEvent", "UserAttributeDeletedEvent"], ["com.atlassian.crowd.event.user.UserAttributeStoredEvent", "UserAttributeStoredEvent"], ["com.atlassian.crowd.event.user.UserAuthenticatedEvent", "UserAuthenticatedEvent"], ["com.atlassian.crowd.event.user.UserAuthenticationFailedInvalidAuthenticationEvent", "UserAuthenticationFailedInvalidAuthenticationEvent"], ["com.atlassian.crowd.event.user.UserCreatedEvent", "UserCreatedEvent"], ["com.atlassian.crowd.event.user.UserCreatedFromDirectorySynchronisationEvent", "UserCreatedFromDirectorySynchronisationEvent"], ["com.atlassian.crowd.event.user.UserCredentialUpdatedEvent", "UserCredentialUpdatedEvent"], ["com.atlassian.crowd.event.user.UserCredentialValidationFailed", "UserCredentialValidationFailed"], ["com.atlassian.crowd.event.user.UserDeletedEvent", "UserDeletedEvent"], ["com.atlassian.crowd.event.user.UserEditedEvent", "UserEditedEvent"], ["com.atlassian.crowd.event.user.UserEmailChangedEvent", "UserEmailChangedEvent"], ["com.atlassian.crowd.event.user.UserRenamedEvent", "UserRenamedEvent"]]], ["Indexing Events", [["com.atlassian.jira.issue.index.ReindexAllCancelledEvent", "ReindexAllCancelledEvent"], ["com.atlassian.jira.issue.index.ReindexAllCompletedEvent", "ReindexAllCompletedEvent"], ["com.atlassian.jira.issue.index.ReindexAllStartedEvent", "ReindexAllStartedEvent"]]], ["Issue Link Events", [["com.atlassian.jira.event.issue.link.IssueLinkCreatedEvent", "IssueLinkCreatedEvent"], ["com.atlassian.jira.event.issue.link.IssueLinkDeletedEvent", "IssueLinkDeletedEvent"]]], ["Issue Watch Events", [["com.atlassian.jira.event.issue.IssueWatcherAddedEvent", "IssueWatcherAddedEvent"], ["com.atlassian.jira.event.issue.IssueWatcherDeletedEvent", "IssueWatcherDeletedEvent"]]], ["Mention Events", [["com.atlassian.jira.event.issue.MentionIssueCommentEvent", "MentionIssueCommentEvent"], ["com.atlassian.jira.event.issue.MentionIssueEvent", "MentionIssueEvent"]]], ["Others", [["com.atlassian.jira.event.config.ApplicationPropertyChangeEvent", "ApplicationPropertyChangeEvent"], ["com.atlassian.jira.event.property.BooleanApplicationPropertySetEvent", "BooleanApplicationPropertySetEvent"], ["com.atlassian.jira.event.DashboardViewEvent", "DashboardViewEvent"], ["com.atlassian.jira.event.DraftWorkflowCreatedEvent", "DraftWorkflowCreatedEvent"], ["com.atlassian.jira.event.DraftWorkflowDeletedEvent", "DraftWorkflowDeletedEvent"], ["com.atlassian.jira.event.DraftWorkflowPublishedEvent", "DraftWorkflowPublishedEvent"], ["com.atlassian.jira.issue.search.providers.EmptyJqlSearchEvent", "EmptyJqlSearchEvent"], ["com.atlassian.jira.event.ExportEvent", "ExportEvent"], ["com.atlassian.jira.event.FilterCreatedEvent", "FilterCreatedEvent"], ["com.atlassian.jira.event.IndexRecoveryEnabledEvent", "IndexRecoveryEnabledEvent"], ["com.atlassian.jira.event.issue.IssueChangedEventImpl", "IssueChangedEventImpl"], ["com.atlassian.jira.event.issue.IssuePreDeleteEvent", "IssuePreDeleteEvent"], ["com.atlassian.jira.event.issue.IssueSearchEvent", "IssueSearchEvent"], ["com.atlassian.jira.event.issue.IssueViewEvent", "IssueViewEvent"], ["com.atlassian.jira.event.issue.JqlSearchVersionEvent", "JqlSearchVersionEvent"], ["com.atlassian.jira.event.user.LoginEvent", "LoginEvent"], ["com.atlassian.jira.event.user.LogoutEvent", "LogoutEvent"], ["com.atlassian.jira.issue.priority.************************", "************************"], ["com.atlassian.jira.event.project.ProjectImportedEvent", "ProjectImportedEvent"], ["com.atlassian.jira.event.ProjectUpdatedCategoryChangedEvent", "ProjectUpdatedCategoryChangedEvent"], ["com.atlassian.jira.event.ProjectUpdatedDetailedChangesEvent", "ProjectUpdatedDetailedChangesEvent"], ["com.atlassian.jira.event.ProjectUpdatedKeyChangedEvent", "ProjectUpdatedKeyChangedEvent"], ["com.atlassian.jira.event.ProjectUpdatedTypeChangedEvent", "ProjectUpdatedTypeChangedEvent"], ["com.atlassian.jira.event.issue.QuickBrowseEvent", "QuickBrowseEvent"], ["com.atlassian.jira.event.issue.QuickSearchEvent", "QuickSearchEvent"], ["com.atlassian.jira.event.issue.RefreshIssueSearchEvent", "RefreshIssueSearchEvent"], ["com.atlassian.jira.event.ReplicationSettingsConfiguredEvent", "ReplicationSettingsConfiguredEvent"], ["com.atlassian.jira.event.SharedEntityUpdatedMetricsEvent", "SharedEntityUpdatedMetricsEvent"], ["com.atlassian.jira.event.property.StringApplicationPropertySetEvent", "StringApplicationPropertySetEvent"], ["com.atlassian.jira.event.issue.SwitchIssueSearchEvent", "SwitchIssueSearchEvent"], ["com.atlassian.jira.event.user.UserEvent", "UserEvent"], ["com.atlassian.jira.event.WorkflowCreatedEvent", "WorkflowCreatedEvent"], ["com.atlassian.jira.event.WorkflowDeletedEvent", "WorkflowDeletedEvent"], ["com.atlassian.jira.event.WorkflowImportedFromXmlEvent", "WorkflowImportedFromXmlEvent"], ["com.atlassian.jira.event.WorkflowRenamedEvent", "WorkflowRenamedEvent"], ["com.atlassian.jira.event.WorkflowUpdatedEvent", "WorkflowUpdatedEvent"]]], ["Project Category Events", [["com.atlassian.jira.event.project.ProjectCategoryChangeEvent", "ProjectCategoryChangeEvent"]]], ["Project Events", [["com.atlassian.jira.event.ProjectCreatedEvent", "ProjectCreatedEvent"], ["com.atlassian.jira.event.ProjectDeletedEvent", "ProjectDeletedEvent"], ["com.atlassian.jira.event.ProjectUpdatedEvent", "ProjectUpdatedEvent"]]], ["Project Role Events", [["com.atlassian.jira.event.role.AbstractProjectRoleEvent", "AbstractProjectRoleEvent"], ["com.atlassian.jira.event.role.ProjectRoleDeletedEvent", "ProjectRoleDeletedEvent"], ["com.atlassian.jira.event.role.ProjectRoleUpdatedEvent", "ProjectRoleUpdatedEvent"]]], ["Remote Link Events", [["com.atlassian.jira.event.issue.link.AbstractRemoteIssueLinkEvent", "AbstractRemoteIssueLinkEvent"], ["com.atlassian.jira.event.issue.link.RemoteIssueLinkCreateEvent", "RemoteIssueLinkCreateEvent"], ["com.atlassian.jira.event.issue.link.RemoteIssueLinkDeleteEvent", "RemoteIssueLinkDeleteEvent"], ["com.atlassian.jira.event.issue.link.RemoteIssueLinkUICreateEvent", "RemoteIssueLinkUICreateEvent"], ["com.atlassian.jira.event.issue.link.RemoteIssueLinkUIDeleteEvent", "RemoteIssueLinkUIDeleteEvent"], ["com.atlassian.jira.event.issue.link.RemoteIssueLinkUpdateEvent", "RemoteIssueLinkUpdateEvent"]]], ["Version Events", [["com.atlassian.jira.event.project.AbstractVersionEvent", "AbstractVersionEvent"], ["com.atlassian.jira.issue.fields.event.AffectedVersionCreatedInlineEvent", "AffectedVersionCreatedInlineEvent"], ["com.atlassian.jira.issue.fields.event.FixVersionCreatedInline", "FixVersionCreatedInline"], ["com.atlassian.jira.event.project.RemoteVersionLinkDeleteEvent", "RemoteVersionLinkDeleteEvent"], ["com.atlassian.jira.event.project.RemoteVersionLinkPutEvent", "RemoteVersionLinkPutEvent"], ["com.atlassian.jira.event.project.VersionArchiveEvent", "VersionArchiveEvent"], ["com.atlassian.jira.event.project.VersionCreateEvent", "VersionCreateEvent"], ["com.atlassian.jira.event.project.VersionCreatedViaRestEvent", "VersionCreatedViaRestEvent"], ["com.atlassian.jira.event.project.VersionDeleteEvent", "VersionDeleteEvent"], ["com.atlassian.jira.event.project.VersionMergeEvent", "VersionMergeEvent"], ["com.atlassian.jira.event.project.VersionMoveEvent", "VersionMoveEvent"], ["com.atlassian.jira.event.project.VersionReleaseEvent", "VersionReleaseEvent"], ["com.atlassian.jira.event.project.VersionUnarchiveEvent", "VersionUnarchiveEvent"], ["com.atlassian.jira.event.project.VersionUnreleaseEvent", "VersionUnreleaseEvent"], ["com.atlassian.jira.event.project.VersionUpdatedEvent", "VersionUpdatedEvent"]]], ["Worklog Events", [["com.atlassian.jira.event.worklog.WorklogCreatedEvent", "WorklogCreatedEvent"], ["com.atlassian.jira.event.worklog.WorklogDeletedEvent", "WorklogDeletedEvent"], ["com.atlassian.jira.event.worklog.WorklogUpdatedEvent", "WorklogUpdatedEvent"]]], ["Jira Software", [["com.atlassian.greenhopper.api.events.RemoteSprintLinkEvent", "RemoteSprintLinkEvent"], ["com.atlassian.greenhopper.api.events.board.AbstractBoardEvent", "AbstractBoardEvent"], ["com.atlassian.greenhopper.api.events.board.BoardConfigurationChangedEvent", "BoardConfigurationChangedEvent"], ["com.atlassian.greenhopper.api.events.board.BoardCreatedEvent", "BoardCreatedEvent"], ["com.atlassian.greenhopper.api.events.board.BoardDeletedEvent", "BoardDeletedEvent"], ["com.atlassian.greenhopper.api.events.board.BoardEvent", "BoardEvent"], ["com.atlassian.greenhopper.api.events.board.BoardUpdatedEvent", "BoardUpdatedEvent"], ["com.atlassian.greenhopper.api.events.board.KanbanBacklogToggledEvent", "KanbanBacklogToggledEvent"], ["com.atlassian.greenhopper.api.events.feature.FeatureToggledEvent", "FeatureToggledEvent"], ["com.atlassian.greenhopper.api.events.sprint.AbstractSprintEvent", "AbstractSprintEvent"], ["com.atlassian.greenhopper.api.events.sprint.SprintClosedEvent", "SprintClosedEvent"], ["com.atlassian.greenhopper.api.events.sprint.SprintCreatedEvent", "SprintCreatedEvent"], ["com.atlassian.greenhopper.api.events.sprint.SprintDeletedEvent", "SprintDeletedEvent"], ["com.atlassian.greenhopper.api.events.sprint.SprintEvent", "SprintEvent"], ["com.atlassian.greenhopper.api.events.sprint.SprintStartedEvent", "SprintStartedEvent"], ["com.atlassian.greenhopper.api.events.sprint.SprintUpdatedEvent", "SprintUpdatedEvent"], ["com.atlassian.greenhopper.events.RemoteSprintLinkEvent", "RemoteSprintLinkEvent"], ["com.atlassian.greenhopper.imports.ImportAnalyticEvent", "ImportAnalyticEvent"], ["com.atlassian.greenhopper.manager.issue.issuetypes.EpicIssueTypeChangedEvent", "EpicIssueTypeChangedEvent"], ["com.atlassian.greenhopper.service.lexorank.balance.LexoRankChangeEvent", "LexoRankChangeEvent"], ["com.atlassian.greenhopper.service.rapid.view.event.BoardPropertyDeletedEvent", "BoardPropertyDeletedEvent"], ["com.atlassian.greenhopper.service.rapid.view.event.BoardPropertySetEvent", "BoardPropertySetEvent"], ["com.atlassian.greenhopper.service.sprint.event.SprintPropertyDeletedEvent", "SprintPropertyDeletedEvent"], ["com.atlassian.greenhopper.service.sprint.event.SprintPropertySetEvent", "SprintPropertySetEvent"], ["com.atlassian.greenhopper.web.rapid.event.UnlicensedAnalyticsEvent", "UnlicensedAnalyticsEvent"], ["com.atlassian.greenhopper.web.rapid.list.CollectIssuesEvent", "CollectIssuesEvent"]]], ["Service Desk", [["com.atlassian.servicedesk.api.event.ServiceDeskCommentEvent", "ServiceDeskCommentEvent"], ["com.atlassian.servicedesk.api.sla.event.SLAChangeEvent", "SLAChangeEvent"], ["com.atlassian.servicedesk.bootstrap.lifecycle.server.ServiceDeskReRaisedLicenseChangedEvent", "ServiceDeskReRaisedLicenseChangedEvent"], ["com.atlassian.servicedesk.internal.api.events.OrganisationsAddedToIssueEvent", "OrganisationsAddedToIssueEvent"], ["com.atlassian.servicedesk.internal.confluenceknowledgebase.permissions.analytics.ConfluencePermissionFixAnalyticEvent", "ConfluencePermissionFixAnalyticEvent"], ["com.atlassian.servicedesk.internal.email.replystripping.analytics.QuotedEmailDetectionEvent", "QuotedEmailDetectionEvent"], ["com.atlassian.servicedesk.internal.feature.customer.request.analytics.RequestPortalProjectAnalyticsEvent", "RequestPortalProjectAnalyticsEvent"], ["com.atlassian.servicedesk.internal.feature.customer.request.analytics.RequestProjectAnalyticsEvent", "RequestProjectAnalyticsEvent"], ["com.atlassian.servicedesk.internal.feature.emailchannel.analyticsevents.NonServiceDeskEmailRequestCommentedEvent", "NonServiceDeskEmailRequestCommentedEvent"], ["com.atlassian.servicedesk.internal.feature.emailchannel.analyticsevents.OutsiderCommentedEvent", "OutsiderCommentedEvent"], ["com.atlassian.servicedesk.internal.feature.emailchannel.analyticsevents.RequestCreatedSharedWithOrganizationViaEmailAnalyticsEvent", "RequestCreatedSharedWithOrganizationViaEmailAnalyticsEvent"], ["com.atlassian.servicedesk.internal.feature.emailchannel.analyticsevents.ServiceDeskEmailRequestCommentedEvent", "ServiceDeskEmailRequestCommentedEvent"], ["com.atlassian.servicedesk.internal.feature.shareparticipants.RequestSharedWithOrganizationAnalyticsEvent", "RequestSharedWithOrganizationAnalyticsEvent"], ["com.atlassian.servicedesk.internal.sla.configuration.copier.CalendarNamePrefixedEvent", "CalendarNamePrefixedEvent"], ["com.atlassian.servicedesk.internal.sla.model.TimelineEvent", "TimelineEvent"], ["com.atlassian.servicedesk.workinprogressapi.sla.threshold.SlaThresholdsExceededEvent", "SlaThresholdsExceededEvent"]]]]