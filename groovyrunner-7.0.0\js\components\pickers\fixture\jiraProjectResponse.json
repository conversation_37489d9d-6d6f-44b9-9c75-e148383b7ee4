[{"expand": "description,lead,url,projectKeys", "self": "http://localhost:8080/jira/rest/api/2/project/10000", "id": "10000", "key": "JRA", "name": "Jira Testing Project", "avatarUrls": {"48x48": "http://localhost:8080/jira/secure/projectavatar?avatarId=10324", "24x24": "http://localhost:8080/jira/secure/projectavatar?size=small&avatarId=10324", "16x16": "http://localhost:8080/jira/secure/projectavatar?size=xsmall&avatarId=10324", "32x32": "http://localhost:8080/jira/secure/projectavatar?size=medium&avatarId=10324"}, "projectTypeKey": "software"}, {"expand": "description,lead,url,projectKeys", "self": "http://localhost:8080/jira/rest/api/2/project/10001", "id": "10001", "key": "JRTWO", "name": "Jira Two Project", "avatarUrls": {"48x48": "http://localhost:8080/jira/secure/projectavatar?avatarId=10324", "24x24": "http://localhost:8080/jira/secure/projectavatar?size=small&avatarId=10324", "16x16": "http://localhost:8080/jira/secure/projectavatar?size=xsmall&avatarId=10324", "32x32": "http://localhost:8080/jira/secure/projectavatar?size=medium&avatarId=10324"}, "projectTypeKey": "software"}, {"expand": "description,lead,url,projectKeys", "self": "http://localhost:8080/jira/rest/api/2/project/10002", "id": "10002", "key": "SSPA", "name": "Sample Scrum Project A", "avatarUrls": {"48x48": "http://localhost:8080/jira/secure/projectavatar?avatarId=10324", "24x24": "http://localhost:8080/jira/secure/projectavatar?size=small&avatarId=10324", "16x16": "http://localhost:8080/jira/secure/projectavatar?size=xsmall&avatarId=10324", "32x32": "http://localhost:8080/jira/secure/projectavatar?size=medium&avatarId=10324"}, "projectTypeKey": "software"}]