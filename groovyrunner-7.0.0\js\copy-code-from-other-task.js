require("wr-dependency!com.atlassian.auiplugin:dialog2");
const {findChildElementAsync, findElementAsync} = require("./admin/util/DomUtils");

(function ($) {
    var paramsShown = "/start/com.onresolve.scriptrunner.canned.bamboo.tasks.scriptable.ScriptableTask";

    $(document).off(paramsShown).on(paramsShown, function () {
        var $copyTaskLink = $("#copy-from-other-task"),
            selectTaskDialogSelector = "#select-task-dialog",
            dialog;

        $copyTaskLink.off("click").on("click", function (e) {
            // open dialog
            dialog = AJS.dialog2(selectTaskDialogSelector);
            dialog.show();
        });

        AJS.$("#dialog-close-button").off("click").click(function(e) {
            e.preventDefault();
            dialog.hide();
        });

        AJS.$("#dialog-submit-button").off("click").click(function(e) {
            var taskIdx = $(selectTaskDialogSelector).find("#select-task").val(),
                planKey = $('#admin-screens')
                    .closest('form')
                    .find('[name="planKey"]')
                    .val()

            $.ajax({
                type: "GET",
                url: AJS.contextPath() + "/rest/scriptrunner-bamboo/latest/tasks/" + planKey + "/" + taskIdx + "/code"
            }).fail(function (XMLHttpRequest, textStatus, errorThrown) {
                AJS.messages.error("#error-context", {
                    title: 'Could not retrieve code.',
                    body: '<p>Failure to generate code, perhaps the plan, job or task has been deleted</p>'
                });

            }).then(async function (data) {
                // copy response to code editor
                // wait for Monaco being fully initialized
                const editorEl = await findElementAsync('.EditBuiltinScript div.sr-monaco.initialized')
                const editor = window.divToEditorMap.get(editorEl)

                if (editorEl && editor) {
                    editor.getModel().setValue(data)
                } else {
                    alert("Code generate from the selected task is:\n\n" + data)
                }

                e.preventDefault();
                dialog.hide();
            });
        });
    });
})(AJS.$);
