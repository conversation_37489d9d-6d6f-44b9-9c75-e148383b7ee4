(self.webpackJsonpScriptRunner=self.webpackJsonpScriptRunner||[]).push([["default-frontend-components_node_modules_vscode-textmate_release_sync_recursive-src_main_reso-d6f95a"],{42761:e=>{function t(e){var t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=42761,e.exports=t},66573:(e,t,n)=>{"use strict";n.r(t)},58297:(e,t,n)=>{"use strict";n.r(t)},66661:function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(o,i){function a(e){try{s(r.next(e))}catch(e){i(e)}}function c(e){try{s(r.throw(e))}catch(e){i(e)}}function s(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(a,c)}s((r=r.apply(e,t||[])).next())}))},o=this&&this.__generator||function(e,t){var n,r,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:c(0),throw:c(1),return:c(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function c(i){return function(c){return function(i){if(n)throw new TypeError("Generator is already executing.");for(;a;)try{if(n=1,r&&(o=2&i[0]?r.return:i[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,i[1])).done)return o;switch(r=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return a.label++,{value:i[1],done:!1};case 5:a.label++,r=i[1],i=[0];continue;case 7:i=a.ops.pop(),a.trys.pop();continue;default:if(!(o=a.trys,(o=o.length>0&&o[o.length-1])||6!==i[0]&&2!==i[0])){a=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){a.label=i[1];break}if(6===i[0]&&a.label<o[1]){a.label=o[1],o=i;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(i);break}o[2]&&a.ops.pop(),a.trys.pop();continue}i=t.call(e,a)}catch(e){i=[6,e],r=0}finally{n=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,c])}}},i=this&&this.__values||function(e){var t="function"==typeof Symbol&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},a=this&&this.__read||function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,i=n.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(r=i.next()).done;)a.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return a},c=this&&this.__spreadArray||function(e,t,n){if(n||2===arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))},s=Object.create,u=Object.defineProperty,l=Object.defineProperties,d=Object.getOwnPropertyDescriptor,p=Object.getOwnPropertyDescriptors,f=Object.getOwnPropertyNames,m=Object.getOwnPropertySymbols,h=Object.getPrototypeOf,g=Object.prototype.hasOwnProperty,y=Object.prototype.propertyIsEnumerable,v=function(e,t,n){return t in e?u(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n},b=function(e,t){var n,r;for(var o in t||(t={}))g.call(t,o)&&v(e,o,t[o]);if(m)try{for(var a=i(m(t)),c=a.next();!c.done;c=a.next()){o=c.value;y.call(t,o)&&v(e,o,t[o])}}catch(e){n={error:e}}finally{try{c&&!c.done&&(r=a.return)&&r.call(a)}finally{if(n)throw n.error}}return e},w=function(e,t){return l(e,p(t))},E=function(e){return u(e,"__esModule",{value:!0})},S=function(e,t){return function(){return t||(0,e[Object.keys(e)[0]])((t={exports:{}}).exports,t),t.exports}},x=function(e){return function(e,t,n){var r,o;if(t&&"object"==typeof t||"function"==typeof t){var a=function(r){g.call(e,r)||"default"===r||u(e,r,{get:function(){return t[r]},enumerable:!(n=d(t,r))||n.enumerable})};try{for(var c=i(f(t)),s=c.next();!s.done;s=c.next())a(s.value)}catch(e){r={error:e}}finally{try{s&&!s.done&&(o=c.return)&&o.call(c)}finally{if(r)throw r.error}}}return e}(E(u(null!=e?s(h(e)):{},"default",e&&e.__esModule&&"default"in e?{get:function(){return e.default},enumerable:!0}:{value:e,enumerable:!0})),e)},P=S({"src/grammars/groovy.tmLanguage.json":function(e,t){t.exports={information_for_contributors:["This file has been converted from https://github.com/textmate/groovy.tmbundle/blob/master/Syntaxes/Groovy.tmLanguage","If you want to provide a fix or improvement, please create a pull request against the original repository.","Once accepted there, we are happy to receive an update request."],version:"https://github.com/textmate/groovy.tmbundle/commit/85d8f7c97ae473ccb9473f6c8d27e4ec957f4be1",name:"Groovy",scopeName:"source.groovy",patterns:[{captures:{1:{name:"punctuation.definition.comment.groovy"}},match:"^(#!).+$\\n",name:"comment.line.hashbang.groovy"},{captures:{1:{name:"keyword.other.package.groovy"},2:{name:"storage.modifier.package.groovy"},3:{name:"punctuation.terminator.groovy"}},match:"^\\s*(package)\\b(?:\\s*([^ ;$]+)\\s*(;)?)?",name:"meta.package.groovy"},{begin:"(import static)\\b\\s*",beginCaptures:{1:{name:"keyword.other.import.static.groovy"}},captures:{1:{name:"keyword.other.import.groovy"},2:{name:"storage.modifier.import.groovy"},3:{name:"punctuation.terminator.groovy"}},contentName:"storage.modifier.import.groovy",end:"\\s*(?:$|(?=%>)(;))",endCaptures:{1:{name:"punctuation.terminator.groovy"}},name:"meta.import.groovy",patterns:[{match:"\\.",name:"punctuation.separator.groovy"},{match:"\\s",name:"invalid.illegal.character_not_allowed_here.groovy"}]},{begin:"(import)\\b\\s*",beginCaptures:{1:{name:"keyword.other.import.groovy"}},captures:{1:{name:"keyword.other.import.groovy"},2:{name:"storage.modifier.import.groovy"},3:{name:"punctuation.terminator.groovy"}},contentName:"storage.modifier.import.groovy",end:"\\s*(?:$|(?=%>)|(;))",endCaptures:{1:{name:"punctuation.terminator.groovy"}},name:"meta.import.groovy",patterns:[{match:"\\.",name:"punctuation.separator.groovy"},{match:"\\s",name:"invalid.illegal.character_not_allowed_here.groovy"}]},{captures:{1:{name:"keyword.other.import.groovy"},2:{name:"keyword.other.import.static.groovy"},3:{name:"storage.modifier.import.groovy"},4:{name:"punctuation.terminator.groovy"}},match:"^\\s*(import)(?:\\s+(static)\\s+)\\b(?:\\s*([^ ;$]+)\\s*(;)?)?",name:"meta.import.groovy"},{include:"#groovy"}],repository:{annotations:{patterns:[{begin:"(?<!\\.)(@[^ (]+)(\\()",beginCaptures:{1:{name:"storage.type.annotation.groovy"},2:{name:"punctuation.definition.annotation-arguments.begin.groovy"}},end:"(\\))",endCaptures:{1:{name:"punctuation.definition.annotation-arguments.end.groovy"}},name:"meta.declaration.annotation.groovy",patterns:[{captures:{1:{name:"constant.other.key.groovy"},2:{name:"keyword.operator.assignment.groovy"}},match:"(\\w*)\\s*(=)"},{include:"#values"},{match:",",name:"punctuation.definition.seperator.groovy"}]},{match:"(?<!\\.)@\\S+",name:"storage.type.annotation.groovy"}]},"anonymous-classes-and-new":{begin:"\\bnew\\b",beginCaptures:{0:{name:"keyword.control.new.groovy"}},end:"(?<=\\)|\\])(?!\\s*{)|(?<=})|(?=[;])|$",patterns:[{begin:"(\\w+)\\s*(?=\\[)",beginCaptures:{1:{name:"storage.type.groovy"}},end:"}|(?=\\s*(?:,|;|\\)))|$",patterns:[{begin:"\\[",end:"\\]",patterns:[{include:"#groovy"}]},{begin:"{",end:"(?=})",patterns:[{include:"#groovy"}]}]},{begin:"(?=\\w.*\\(?)",end:"(?<=\\))|$",patterns:[{include:"#object-types"},{begin:"\\(",beginCaptures:{1:{name:"storage.type.groovy"}},end:"\\)",patterns:[{include:"#groovy"}]}]},{begin:"{",end:"}",name:"meta.inner-class.groovy",patterns:[{include:"#class-body"}]}]},braces:{begin:"\\{",end:"\\}",patterns:[{include:"#groovy-code"}]},class:{begin:"(?=\\w?[\\w\\s]*(?:class|(?:@)?interface|enum)\\s+\\w+)",end:"}",endCaptures:{0:{name:"punctuation.section.class.end.groovy"}},name:"meta.definition.class.groovy",patterns:[{include:"#storage-modifiers"},{include:"#comments"},{captures:{1:{name:"storage.modifier.groovy"},2:{name:"entity.name.type.class.groovy"}},match:"(class|(?:@)?interface|enum)\\s+(\\w+)",name:"meta.class.identifier.groovy"},{begin:"extends",beginCaptures:{0:{name:"storage.modifier.extends.groovy"}},end:"(?={|implements)",name:"meta.definition.class.inherited.classes.groovy",patterns:[{include:"#object-types-inherited"},{include:"#comments"}]},{begin:"(implements)\\s",beginCaptures:{1:{name:"storage.modifier.implements.groovy"}},end:"(?=\\s*extends|\\{)",name:"meta.definition.class.implemented.interfaces.groovy",patterns:[{include:"#object-types-inherited"},{include:"#comments"}]},{begin:"{",end:"(?=})",name:"meta.class.body.groovy",patterns:[{include:"#class-body"}]}]},"class-body":{patterns:[{include:"#enum-values"},{include:"#constructors"},{include:"#groovy"}]},closures:{begin:"\\{(?=.*?->)",end:"\\}",patterns:[{begin:"(?<=\\{)(?=[^\\}]*?->)",end:"->",endCaptures:{0:{name:"keyword.operator.groovy"}},patterns:[{begin:"(?!->)",end:"(?=->)",name:"meta.closure.parameters.groovy",patterns:[{begin:"(?!,|->)",end:"(?=,|->)",name:"meta.closure.parameter.groovy",patterns:[{begin:"=",beginCaptures:{0:{name:"keyword.operator.assignment.groovy"}},end:"(?=,|->)",name:"meta.parameter.default.groovy",patterns:[{include:"#groovy-code"}]},{include:"#parameters"}]}]}]},{begin:"(?=[^}])",end:"(?=\\})",patterns:[{include:"#groovy-code"}]}]},"comment-block":{begin:"/\\*",captures:{0:{name:"punctuation.definition.comment.groovy"}},end:"\\*/",name:"comment.block.groovy"},comments:{patterns:[{captures:{0:{name:"punctuation.definition.comment.groovy"}},match:"/\\*\\*/",name:"comment.block.empty.groovy"},{include:"text.html.javadoc"},{include:"#comment-block"},{captures:{1:{name:"punctuation.definition.comment.groovy"}},match:"(//).*$\\n?",name:"comment.line.double-slash.groovy"}]},constants:{patterns:[{match:"\\b([A-Z][A-Z0-9_]+)\\b",name:"constant.other.groovy"},{match:"\\b(true|false|null)\\b",name:"constant.language.groovy"}]},constructors:{applyEndPatternLast:1,begin:"(?<=;|^)(?=\\s*(?:(?:private|protected|public|native|synchronized|abstract|threadsafe|transient|static|final)\\s+)*[A-Z]\\w*\\()",end:"}",patterns:[{include:"#method-content"}]},"enum-values":{patterns:[{begin:"(?<=;|^)\\s*\\b([A-Z0-9_]+)(?=\\s*(?:,|;|}|\\(|$))",beginCaptures:{1:{name:"constant.enum.name.groovy"}},end:",|;|(?=})|^(?!\\s*\\w+\\s*(?:,|$))",patterns:[{begin:"\\(",end:"\\)",name:"meta.enum.value.groovy",patterns:[{match:",",name:"punctuation.definition.seperator.parameter.groovy"},{include:"#groovy-code"}]}]}]},groovy:{patterns:[{include:"#comments"},{include:"#class"},{include:"#variables"},{include:"#methods"},{include:"#annotations"},{include:"#groovy-code"}]},"groovy-code":{patterns:[{include:"#groovy-code-minus-map-keys"},{include:"#map-keys"}]},"groovy-code-minus-map-keys":{comment:"In some situations, maps can't be declared without enclosing []'s, \n\t\t\t\ttherefore we create a collection of everything but that",patterns:[{include:"#comments"},{include:"#annotations"},{include:"#support-functions"},{include:"#keyword-language"},{include:"#values"},{include:"#anonymous-classes-and-new"},{include:"#keyword-operator"},{include:"#types"},{include:"#storage-modifiers"},{include:"#parens"},{include:"#closures"},{include:"#braces"}]},keyword:{patterns:[{include:"#keyword-operator"},{include:"#keyword-language"}]},"keyword-language":{patterns:[{match:"\\b(try|catch|finally|throw)\\b",name:"keyword.control.exception.groovy"},{match:"\\b((?<!\\.)(?:return|break|continue|default|do|while|for|switch|if|else))\\b",name:"keyword.control.groovy"},{begin:"\\bcase\\b",beginCaptures:{0:{name:"keyword.control.groovy"}},end:":",endCaptures:{0:{name:"punctuation.definition.case-terminator.groovy"}},name:"meta.case.groovy",patterns:[{include:"#groovy-code-minus-map-keys"}]},{begin:"\\b(assert)\\s",beginCaptures:{1:{name:"keyword.control.assert.groovy"}},end:"$|;|}",name:"meta.declaration.assertion.groovy",patterns:[{match:":",name:"keyword.operator.assert.expression-seperator.groovy"},{include:"#groovy-code-minus-map-keys"}]},{match:"\\b(throws)\\b",name:"keyword.other.throws.groovy"}]},"keyword-operator":{patterns:[{match:"\\b(as)\\b",name:"keyword.operator.as.groovy"},{match:"\\b(in)\\b",name:"keyword.operator.in.groovy"},{match:"\\?\\:",name:"keyword.operator.elvis.groovy"},{match:"\\*\\:",name:"keyword.operator.spreadmap.groovy"},{match:"\\.\\.",name:"keyword.operator.range.groovy"},{match:"\\->",name:"keyword.operator.arrow.groovy"},{match:"<<",name:"keyword.operator.leftshift.groovy"},{match:"(?<=\\S)\\.(?=\\S)",name:"keyword.operator.navigation.groovy"},{match:"(?<=\\S)\\?\\.(?=\\S)",name:"keyword.operator.safe-navigation.groovy"},{begin:"\\?",beginCaptures:{0:{name:"keyword.operator.ternary.groovy"}},end:"(?=$|\\)|}|])",name:"meta.evaluation.ternary.groovy",patterns:[{match:":",name:"keyword.operator.ternary.expression-seperator.groovy"},{include:"#groovy-code-minus-map-keys"}]},{match:"==~",name:"keyword.operator.match.groovy"},{match:"=~",name:"keyword.operator.find.groovy"},{match:"\\b(instanceof)\\b",name:"keyword.operator.instanceof.groovy"},{match:"(===|==|!=|<=|>=|<=>|<>|<|>|<<)",name:"keyword.operator.comparison.groovy"},{match:"=",name:"keyword.operator.assignment.groovy"},{match:"(\\-\\-|\\+\\+)",name:"keyword.operator.increment-decrement.groovy"},{match:"(\\-|\\+|\\*|\\/|%)",name:"keyword.operator.arithmetic.groovy"},{match:"(!|&&|\\|\\|)",name:"keyword.operator.logical.groovy"}]},"language-variables":{patterns:[{match:"\\b(this|super)\\b",name:"variable.language.groovy"}]},"map-keys":{patterns:[{captures:{1:{name:"constant.other.key.groovy"},2:{name:"punctuation.definition.seperator.key-value.groovy"}},match:"(\\w+)\\s*(:)"}]},"method-call":{begin:"([\\w$]+)(\\()",beginCaptures:{1:{name:"meta.method.groovy"},2:{name:"punctuation.definition.method-parameters.begin.groovy"}},end:"\\)",endCaptures:{0:{name:"punctuation.definition.method-parameters.end.groovy"}},name:"meta.method-call.groovy",patterns:[{match:",",name:"punctuation.definition.seperator.parameter.groovy"},{include:"#groovy-code"}]},"method-content":{patterns:[{match:"\\s"},{include:"#annotations"},{begin:"(?=(?:\\w|<)[^\\(]*\\s+(?:[\\w$]|<)+\\s*\\()",end:"(?=[\\w$]+\\s*\\()",name:"meta.method.return-type.java",patterns:[{include:"#storage-modifiers"},{include:"#types"}]},{begin:"([\\w$]+)\\s*\\(",beginCaptures:{1:{name:"entity.name.function.java"}},end:"\\)",name:"meta.definition.method.signature.java",patterns:[{begin:"(?=[^)])",end:"(?=\\))",name:"meta.method.parameters.groovy",patterns:[{begin:"(?=[^,)])",end:"(?=,|\\))",name:"meta.method.parameter.groovy",patterns:[{match:",",name:"punctuation.definition.separator.groovy"},{begin:"=",beginCaptures:{0:{name:"keyword.operator.assignment.groovy"}},end:"(?=,|\\))",name:"meta.parameter.default.groovy",patterns:[{include:"#groovy-code"}]},{include:"#parameters"}]}]}]},{begin:"(?=<)",end:"(?=\\s)",name:"meta.method.paramerised-type.groovy",patterns:[{begin:"<",end:">",name:"storage.type.parameters.groovy",patterns:[{include:"#types"},{match:",",name:"punctuation.definition.seperator.groovy"}]}]},{begin:"throws",beginCaptures:{0:{name:"storage.modifier.groovy"}},end:"(?={|;)|^(?=\\s*(?:[^{\\s]|$))",name:"meta.throwables.groovy",patterns:[{include:"#object-types"}]},{begin:"{",end:"(?=})",name:"meta.method.body.java",patterns:[{include:"#groovy-code"}]}]},methods:{applyEndPatternLast:1,begin:"(?x:(?<=;|^|{)(?=\\s*\n                (?:\n                    (?:private|protected|public|native|synchronized|abstract|threadsafe|transient|static|final) # visibility/modifier\n                        |\n                    (?:def)\n                        |\n                    (?:\n                        (?:\n                            (?:void|boolean|byte|char|short|int|float|long|double)\n                                |\n                            (?:@?(?:[a-zA-Z]\\w*\\.)*[A-Z]+\\w*) # object type\n                        )\n                        [\\[\\]]*\n                        (?:<.*>)?\n                    ) \n                    \n                )\n                \\s+\n                ([^=]+\\s+)?\\w+\\s*\\(\n\t\t\t))",end:"}|(?=[^{])",name:"meta.definition.method.groovy",patterns:[{include:"#method-content"}]},nest_curly:{begin:"\\{",captures:{0:{name:"punctuation.section.scope.groovy"}},end:"\\}",patterns:[{include:"#nest_curly"}]},numbers:{patterns:[{match:"((0(x|X)[0-9a-fA-F]*)|(\\+|-)?\\b(([0-9]+\\.?[0-9]*)|(\\.[0-9]+))((e|E)(\\+|-)?[0-9]+)?)([LlFfUuDdg]|UL|ul)?\\b",name:"constant.numeric.groovy"}]},"object-types":{patterns:[{begin:"\\b((?:[a-z]\\w*\\.)*(?:[A-Z]+\\w*[a-z]+\\w*|UR[LI]))<",end:">|[^\\w\\s,\\?<\\[\\]]",name:"storage.type.generic.groovy",patterns:[{include:"#object-types"},{begin:"<",comment:"This is just to support <>'s with no actual type prefix",end:">|[^\\w\\s,\\[\\]<]",name:"storage.type.generic.groovy"}]},{begin:"\\b((?:[a-z]\\w*\\.)*[A-Z]+\\w*[a-z]+\\w*)(?=\\[)",end:"(?=[^\\]\\s])",name:"storage.type.object.array.groovy",patterns:[{begin:"\\[",end:"\\]",patterns:[{include:"#groovy"}]}]},{match:"\\b(?:[a-zA-Z]\\w*\\.)*(?:[A-Z]+\\w*[a-z]+\\w*|UR[LI])\\b",name:"storage.type.groovy"}]},"object-types-inherited":{patterns:[{begin:"\\b((?:[a-zA-Z]\\w*\\.)*[A-Z]+\\w*[a-z]+\\w*)<",end:">|[^\\w\\s,\\?<\\[\\]]",name:"entity.other.inherited-class.groovy",patterns:[{include:"#object-types-inherited"},{begin:"<",comment:"This is just to support <>'s with no actual type prefix",end:">|[^\\w\\s,\\[\\]<]",name:"storage.type.generic.groovy"}]},{captures:{1:{name:"keyword.operator.dereference.groovy"}},match:"\\b(?:[a-zA-Z]\\w*(\\.))*[A-Z]+\\w*[a-z]+\\w*\\b",name:"entity.other.inherited-class.groovy"}]},parameters:{patterns:[{include:"#annotations"},{include:"#storage-modifiers"},{include:"#types"},{match:"\\w+",name:"variable.parameter.method.groovy"}]},parens:{begin:"\\(",end:"\\)",patterns:[{include:"#groovy-code"}]},"primitive-arrays":{patterns:[{match:"\\b(?:void|boolean|byte|char|short|int|float|long|double)(\\[\\])*\\b",name:"storage.type.primitive.array.groovy"}]},"primitive-types":{patterns:[{match:"\\b(?:void|boolean|byte|char|short|int|float|long|double)\\b",name:"storage.type.primitive.groovy"}]},regexp:{patterns:[{begin:"/(?=[^/]+/([^>]|$))",beginCaptures:{0:{name:"punctuation.definition.string.regexp.begin.groovy"}},end:"/",endCaptures:{0:{name:"punctuation.definition.string.regexp.end.groovy"}},name:"string.regexp.groovy",patterns:[{match:"\\\\.",name:"constant.character.escape.groovy"}]},{begin:'~"',beginCaptures:{0:{name:"punctuation.definition.string.regexp.begin.groovy"}},end:'"',endCaptures:{0:{name:"punctuation.definition.string.regexp.end.groovy"}},name:"string.regexp.compiled.groovy",patterns:[{match:"\\\\.",name:"constant.character.escape.groovy"}]}]},"storage-modifiers":{patterns:[{match:"\\b(private|protected|public)\\b",name:"storage.modifier.access-control.groovy"},{match:"\\b(static)\\b",name:"storage.modifier.static.groovy"},{match:"\\b(final)\\b",name:"storage.modifier.final.groovy"},{match:"\\b(native|synchronized|abstract|threadsafe|transient)\\b",name:"storage.modifier.other.groovy"}]},"string-quoted-double":{begin:'"',beginCaptures:{0:{name:"punctuation.definition.string.begin.groovy"}},end:'"',endCaptures:{0:{name:"punctuation.definition.string.end.groovy"}},name:"string.quoted.double.groovy",patterns:[{include:"#string-quoted-double-contents"}]},"string-quoted-double-contents":{patterns:[{match:"\\\\.",name:"constant.character.escape.groovy"},{applyEndPatternLast:1,begin:"\\$\\w",end:"(?=\\W)",name:"variable.other.interpolated.groovy",patterns:[{match:"\\w",name:"variable.other.interpolated.groovy"},{match:"\\.",name:"keyword.other.dereference.groovy"}]},{begin:"\\$\\{",captures:{0:{name:"punctuation.section.embedded.groovy"}},end:"\\}",name:"source.groovy.embedded.source",patterns:[{include:"#nest_curly"}]}]},"string-quoted-double-multiline":{begin:'"""',beginCaptures:{0:{name:"punctuation.definition.string.begin.groovy"}},end:'"""',endCaptures:{0:{name:"punctuation.definition.string.end.groovy"}},name:"string.quoted.double.multiline.groovy",patterns:[{include:"#string-quoted-double-contents"}]},"string-quoted-single":{begin:"'",beginCaptures:{0:{name:"punctuation.definition.string.begin.groovy"}},end:"'",endCaptures:{0:{name:"punctuation.definition.string.end.groovy"}},name:"string.quoted.single.groovy",patterns:[{include:"#string-quoted-single-contents"}]},"string-quoted-single-contents":{patterns:[{match:"\\\\.",name:"constant.character.escape.groovy"}]},"string-quoted-single-multiline":{begin:"'''",beginCaptures:{0:{name:"punctuation.definition.string.begin.groovy"}},end:"'''",endCaptures:{0:{name:"punctuation.definition.string.end.groovy"}},name:"string.quoted.single.multiline.groovy",patterns:[{include:"#string-quoted-single-contents"}]},strings:{patterns:[{include:"#string-quoted-double-multiline"},{include:"#string-quoted-single-multiline"},{include:"#string-quoted-double"},{include:"#string-quoted-single"},{include:"#regexp"}]},structures:{begin:"\\[",beginCaptures:{0:{name:"punctuation.definition.structure.begin.groovy"}},end:"\\]",endCaptures:{0:{name:"punctuation.definition.structure.end.groovy"}},name:"meta.structure.groovy",patterns:[{include:"#groovy-code"},{match:",",name:"punctuation.definition.separator.groovy"}]},"support-functions":{patterns:[{match:"(?x)\\b(?:sprintf|print(?:f|ln)?)\\b",name:"support.function.print.groovy"},{match:"(?x)\\b(?:shouldFail|fail(?:NotEquals)?|ass(?:ume|ert(?:S(?:cript|ame)|N(?:ot(?:Same|\n\t\t\t\t\tNull)|ull)|Contains|T(?:hat|oString|rue)|Inspect|Equals|False|Length|\n\t\t\t\t\tArrayEquals)))\\b",name:"support.function.testing.groovy"}]},types:{patterns:[{match:"\\b(def)\\b",name:"storage.type.def.groovy"},{include:"#primitive-types"},{include:"#primitive-arrays"},{include:"#object-types"}]},values:{patterns:[{include:"#language-variables"},{include:"#strings"},{include:"#numbers"},{include:"#constants"},{include:"#types"},{include:"#structures"},{include:"#method-call"}]},variables:{applyEndPatternLast:1,patterns:[{begin:"(?x:(?=\n                        (?:\n                            (?:private|protected|public|native|synchronized|abstract|threadsafe|transient|static|final) # visibility/modifier\n                                |\n                            (?:def)\n                                |\n                            (?:void|boolean|byte|char|short|int|float|long|double)\n                                |\n                            (?:(?:[a-z]\\w*\\.)*[A-Z]+\\w*) # object type\n                        )\n                        \\s+\n                        [\\w\\d_<>\\[\\],\\s]+\n                        (?:=|$)\n                        \n        \t\t\t))",end:";|$",name:"meta.definition.variable.groovy",patterns:[{match:"\\s"},{captures:{1:{name:"constant.variable.groovy"}},match:"([A-Z_0-9]+)\\s+(?=\\=)"},{captures:{1:{name:"meta.definition.variable.name.groovy"}},match:"(\\w[^\\s,]*)\\s+(?=\\=)"},{begin:"=",beginCaptures:{0:{name:"keyword.operator.assignment.groovy"}},end:"$",patterns:[{include:"#groovy-code"}]},{captures:{1:{name:"meta.definition.variable.name.groovy"}},match:"(\\w[^\\s=]*)(?=\\s*($|;))"},{include:"#groovy-code"}]}]}}}}}),I=S({"src/grammars/GStringTemplate.tmLanguage.json":function(e,t){t.exports={$schema:"https://raw.githubusercontent.com/martinring/tmlanguage/master/tmlanguage.json",name:"GStringTemplate",patterns:[{include:"#groovyCode"}],repository:{groovyCode:{contentName:"source.groovy",begin:"\\$\\{|<%=?",end:"\\}|%>",patterns:[{include:"source.groovy"}]}},scopeName:"source.gstringtemplate"}}}),C=S({"src/configurations/gstringtemplate.json":function(e,t){t.exports={autoClosingPairs:[{open:"${",close:"}"},{open:"<%",close:"%>"},{open:"<%=",close:"%>"}]}}}),T=S({"src/configurations/groovy.json":function(e,t){t.exports={comments:{lineComment:"//",blockComment:["/*","*/"]},brackets:[["{","}"],["[","]"],["(",")"]],autoClosingPairs:[{open:"[",close:"]",notIn:["string"]},{open:"(",close:")",notIn:["string"]},{open:"{",close:"}",notIn:["string"]},{open:'"',close:'"',notIn:["string"]},{open:"'",close:"'",notIn:["string"]}],surroundingPairs:[["{","}"],["[","]"],["(",")"],['"','"'],["'","'"]]}}});!function(e,t){for(var n in E(e),t)u(e,n,{get:t[n],enumerable:!0})}(t,{AwsHostedMonacoEditor:function(){return mt},CompletionItemKind:function(){return ie},MarkerSeverity:function(){return z},MonacoEditor:function(){return lt},ScriptEditorContext:function(){return ot},editorInfoService:function(){return qe},mapError:function(){return de},mapWarning:function(){return pe},stripHtml:function(){return he},supportsCodeInsight:function(){return ze}});var O=x(n(63844)),k=x(n(22087)),A=x(n(29648)),_=x(n(67357)),D=function(){function e(e){this.config=e;var t=e.grammars,n=e.fetchGrammar,i=e.theme,a=e.onigLib,c=e.monaco;this.monaco=c,this.registry=new k.Registry({onigLib:a,loadGrammar:function(e){return r(this,void 0,void 0,(function(){var r,i,a;return o(this,(function(o){switch(o.label){case 0:return null==t[e]?[2,null]:[4,n(e)];case 1:return r=o.sent(),i=r.type,a=r.grammar,[2,(0,k.parseRawGrammar)(a,"example.".concat(i))]}}))}))},getInjections:function(e){var n=t[e];return n?n.injections:void 0},theme:i}),this.tokensProviderCache=new N(this.registry)}return e.prototype.injectCSS=function(){var e=this.registry.getColorMap().map(_.Color.Format.CSS.parseHex),t=(0,A.generateTokensCSSForColorMap)(e);(function(){var e,t=document.createElement("style"),n=document.getElementsByClassName("monaco-colors")[0];if(n)null==(e=n.parentElement)||e.insertBefore(t,n.nextSibling);else{var r=document.head;null==r&&(r=document.getElementsByTagName("head")[0]),null==r||r.appendChild(t)}return t}()).innerHTML=t},e.prototype.fetchLanguageInfo=function(e){return r(this,void 0,void 0,(function(){var t,n,r;return o(this,(function(o){switch(o.label){case 0:return[4,Promise.all([this.getTokensProviderForLanguage(e),this.config.fetchConfiguration(e)])];case 1:return t=a.apply(void 0,[o.sent(),2]),n=t[0],r=t[1],[2,{tokensProvider:n,configuration:r}]}}))}))},e.prototype.getTokensProviderForLanguage=function(e){var t=this.getScopeNameForLanguage(e);if(null==t)return Promise.resolve(null);var n=this.monaco.languages.getEncodedLanguageId(e);return this.tokensProviderCache.createEncodedTokensProvider(t,n)},e.prototype.getScopeNameForLanguage=function(e){var t,n;try{for(var r=i(Object.entries(this.config.grammars)),o=r.next();!o.done;o=r.next()){var c=a(o.value,2),s=c[0];if(c[1].language===e)return s}}catch(e){t={error:e}}finally{try{o&&!o.done&&(n=r.return)&&n.call(r)}finally{if(t)throw t.error}}return null},e}(),N=function(){function e(e){this.registry=e,this.scopeNameToGrammar=new Map}return e.prototype.createEncodedTokensProvider=function(e,t){return r(this,void 0,void 0,(function(){var n;return o(this,(function(r){switch(r.label){case 0:return[4,this.getGrammar(e,t)];case 1:return n=r.sent(),[2,{getInitialState:function(){return k.INITIAL},tokenizeEncoded:function(e,t){var r=n.tokenizeLine2(e,t);return{tokens:r.tokens,endState:r.ruleStack}}}]}}))}))},e.prototype.getGrammar=function(e,t){var n=this.scopeNameToGrammar.get(e);if(null!=n)return n;var r=this.registry.loadGrammarWithConfiguration(e,t,{}).then((function(t){if(t)return t;throw Error("failed to load grammar for ".concat(e))}));return this.scopeNameToGrammar.set(e,r),r},e}();var F=["indentationRules.decreaseIndentPattern","indentationRules.increaseIndentPattern","indentationRules.indentNextLinePattern","indentationRules.unIndentedLinePattern","folding.markers.start","folding.markers.end","wordPattern"];function L(e){var t,n,r;try{for(var o=i(F),a=o.next();!a.done;a=o.next()){var c=a.value,s=(r=e,void 0,c.split(".").reduce((function(e,t){return null!=e?e[t]:null}),r));"string"==typeof s&&R(e,c,new RegExp(s))}}catch(e){t={error:e}}finally{try{a&&!a.done&&(n=o.return)&&n.call(o)}finally{if(t)throw t.error}}return e}function R(e,t,n){var r=t.split("."),o=r.length-1;r.reduce((function(e,t,r){return null==e?e:r===o?(e[t]=n,null):e[t]}),e)}var j=x(n(42099)),M=!1;function U(e){return r(this,void 0,void 0,(function(){var t;return o(this,(function(n){switch(n.label){case 0:return[4,fetch(null!=e?e:"https://cdn.jsdelivr.net/npm/vscode-oniguruma@1.3.0/release/onig.wasm")];case 1:return t=n.sent(),"application/wasm"===t.headers.get("content-type")?[2,t]:[2,t.arrayBuffer()]}}))}))}function Z(e){return r(this,void 0,void 0,(function(){var t;return o(this,(function(n){switch(n.label){case 0:return M?[3,3]:(M=!0,[4,U(e)]);case 1:return t=n.sent(),[4,(0,j.loadWASM)(t)];case 2:n.sent(),n.label=3;case 3:return[2]}}))}))}var W=x(n(42099)),q={name:"Dark+ (default dark)",settings:[{settings:{foreground:"#D4D4D4",background:"#1E1E1E"}},{name:"Function declarations",scope:["entity.name.function","support.function","support.constant.handlebars","source.powershell variable.other.member","entity.name.operator.custom-literal"],settings:{foreground:"#DCDCAA"}},{name:"Types declaration and references",scope:["meta.return-type","support.class","support.type","entity.name.type","entity.name.namespace","entity.other.attribute","entity.name.scope-resolution","entity.name.class","storage.type.numeric.go","storage.type.byte.go","storage.type.boolean.go","storage.type.string.go","storage.type.uintptr.go","storage.type.error.go","storage.type.rune.go","storage.type.cs","storage.type.generic.cs","storage.type.modifier.cs","storage.type.variable.cs","storage.type.annotation.java","storage.type.generic.java","storage.type.java","storage.type.object.array.java","storage.type.primitive.array.java","storage.type.primitive.java","storage.type.token.java","storage.type.groovy","storage.type.annotation.groovy","storage.type.parameters.groovy","storage.type.generic.groovy","storage.type.object.array.groovy","storage.type.primitive.array.groovy","storage.type.primitive.groovy"],settings:{foreground:"#4EC9B0"}},{name:"Types declaration and references, TS grammar specific",scope:["meta.type.cast.expr","meta.type.new.expr","support.constant.math","support.constant.dom","support.constant.json","entity.other.inherited-class"],settings:{foreground:"#4EC9B0"}},{name:"Control flow / Special keywords",scope:["keyword.control","source.cpp keyword.operator.new","keyword.operator.delete","keyword.other.using","keyword.other.operator","entity.name.operator"],settings:{foreground:"#C586C0"}},{name:"Variable and parameter name",scope:["variable","meta.definition.variable.name","support.variable","entity.name.variable"],settings:{foreground:"#9CDCFE"}},{name:"Constants and enums",scope:["variable.other.constant","variable.other.enummember"],settings:{foreground:"#51B6C4"}},{name:"Object keys, TS grammar specific",scope:"meta.object-literal.key",settings:{foreground:"#9CDCFE"}},{name:"CSS property value",scope:["support.constant.property-value","support.constant.font-name","support.constant.media-type","support.constant.media","constant.other.color.rgb-value","constant.other.rgb-value","support.constant.color"],settings:{foreground:"#CE9178"}},{name:"Regular expression groups",scope:["punctuation.definition.group.regexp","punctuation.definition.group.assertion.regexp","punctuation.definition.character-class.regexp","punctuation.character.set.begin.regexp","punctuation.character.set.end.regexp","keyword.operator.negation.regexp","support.other.parenthesis.regexp"],settings:{foreground:"#CE9178"}},{scope:["constant.character.character-class.regexp","constant.other.character-class.set.regexp","constant.other.character-class.regexp","constant.character.set.regexp"],settings:{foreground:"#d16969"}},{scope:["keyword.operator.or.regexp","keyword.control.anchor.regexp"],settings:{foreground:"#DCDCAA"}},{scope:"keyword.operator.quantifier.regexp",settings:{foreground:"#d7ba7d"}},{scope:"constant.character",settings:{foreground:"#569cd6"}},{scope:"constant.character.escape",settings:{foreground:"#d7ba7d"}},{scope:"entity.name.label",settings:{foreground:"#C8C8C8"}},{scope:["meta.embedded","source.groovy.embedded"],settings:{foreground:"#D4D4D4"}},{scope:"emphasis",settings:{fontStyle:"italic"}},{scope:"strong",settings:{fontStyle:"bold"}},{scope:"header",settings:{foreground:"#000080"}},{scope:"comment",settings:{foreground:"#6A9955"}},{scope:"constant.language",settings:{foreground:"#569cd6"}},{scope:["constant.numeric","entity.name.operator.custom-literal.number","keyword.operator.plus.exponent","keyword.operator.minus.exponent"],settings:{foreground:"#b5cea8"}},{scope:"constant.regexp",settings:{foreground:"#646695"}},{scope:"entity.name.tag",settings:{foreground:"#569cd6"}},{scope:"entity.name.tag.css",settings:{foreground:"#d7ba7d"}},{scope:"entity.other.attribute-name",settings:{foreground:"#9cdcfe"}},{scope:["entity.other.attribute-name.class.css","entity.other.attribute-name.class.mixin.css","entity.other.attribute-name.id.css","entity.other.attribute-name.parent-selector.css","entity.other.attribute-name.pseudo-class.css","entity.other.attribute-name.pseudo-element.css","source.css.less entity.other.attribute-name.id","entity.other.attribute-name.attribute.scss","entity.other.attribute-name.scss"],settings:{foreground:"#d7ba7d"}},{scope:"invalid",settings:{foreground:"#f44747"}},{scope:"markup.underline",settings:{fontStyle:"underline"}},{scope:"markup.bold",settings:{fontStyle:"bold",foreground:"#569cd6"}},{scope:"markup.heading",settings:{fontStyle:"bold",foreground:"#569cd6"}},{scope:"markup.italic",settings:{fontStyle:"italic"}},{scope:"markup.inserted",settings:{foreground:"#b5cea8"}},{scope:"markup.deleted",settings:{foreground:"#ce9178"}},{scope:"markup.changed",settings:{foreground:"#569cd6"}},{scope:"punctuation.definition.quote.begin.markdown",settings:{foreground:"#6A9955"}},{scope:"punctuation.definition.list.begin.markdown",settings:{foreground:"#6796e6"}},{scope:"markup.inline.raw",settings:{foreground:"#ce9178"}},{name:"brackets of XML/HTML tags",scope:"punctuation.definition.tag",settings:{foreground:"#808080"}},{scope:["meta.preprocessor","entity.name.function.preprocessor"],settings:{foreground:"#569cd6"}},{scope:"meta.preprocessor.string",settings:{foreground:"#ce9178"}},{scope:"meta.preprocessor.numeric",settings:{foreground:"#b5cea8"}},{scope:"meta.structure.dictionary.key.python",settings:{foreground:"#9cdcfe"}},{scope:"meta.diff.header",settings:{foreground:"#569cd6"}},{scope:"storage",settings:{foreground:"#569cd6"}},{scope:"storage.type",settings:{foreground:"#569cd6"}},{scope:["storage.modifier","keyword.operator.noexcept"],settings:{foreground:"#569cd6"}},{scope:["string","entity.name.operator.custom-literal.string","meta.embedded.assembly"],settings:{foreground:"#ce9178"}},{scope:"string.tag",settings:{foreground:"#ce9178"}},{scope:"string.value",settings:{foreground:"#ce9178"}},{scope:"string.regexp",settings:{foreground:"#d16969"}},{name:"String interpolation",scope:["punctuation.definition.template-expression.begin","punctuation.definition.template-expression.end","punctuation.section.embedded"],settings:{foreground:"#569cd6"}},{name:"Reset JavaScript string interpolation expression",scope:"meta.template.expression",settings:{foreground:"#d4d4d4"}},{scope:["support.type.vendored.property-name","support.type.property-name","variable.css","variable.scss","variable.other.less","source.coffee.embedded"],settings:{foreground:"#9cdcfe"}},{scope:"keyword",settings:{foreground:"#569cd6"}},{scope:"keyword.operator",settings:{foreground:"#d4d4d4"}},{scope:["keyword.operator.new","keyword.operator.expression","keyword.operator.cast","keyword.operator.sizeof","keyword.operator.alignof","keyword.operator.typeid","keyword.operator.alignas","keyword.operator.instanceof","keyword.operator.logical.python","keyword.operator.wordlike"],settings:{foreground:"#569cd6"}},{scope:"keyword.other.unit",settings:{foreground:"#b5cea8"}},{scope:["punctuation.section.embedded.begin.php","punctuation.section.embedded.end.php"],settings:{foreground:"#569cd6"}},{scope:"support.function.git-rebase",settings:{foreground:"#9cdcfe"}},{scope:"constant.sha.git-rebase",settings:{foreground:"#b5cea8"}},{name:"coloring of the Java import and package identifiers",scope:["storage.modifier.import.java","variable.language.wildcard.java","storage.modifier.package.java"],settings:{foreground:"#d4d4d4"}},{name:"this.self",scope:"variable.language",settings:{foreground:"#569cd6"}}]},G="groovy",V="GStringTemplate",H=[G,V],J=[{id:G,extensions:[".groovy"],aliases:["Groovy"],filenames:["groovy"]},{id:V,extensions:[".gtpl"],aliases:[],filenames:["gtpl"]}];function B(e){return r(this,void 0,void 0,(function(){return o(this,(function(t){switch(t.label){case 0:return[4,Z(e)];case 1:return t.sent(),[2,new D({grammars:$,fetchGrammar:Y,configurations:["groovy","GStringTemplate"],fetchConfiguration:Q,theme:q,onigLib:X,monaco:window.monaco})]}}))}))}var z,K,$={"source.groovy":{language:G,path:"groovy.tmLanguage.json"},"source.gstringtemplate":{language:V,path:"GStringTemplate.tmLanguage.json"}},Y=function(e){return r(void 0,void 0,void 0,(function(){var t;return o(this,(function(n){return"source.groovy"===e?(t=P(),[2,{type:"json",grammar:JSON.stringify(t)}]):"source.gstringtemplate"===e?(t=I(),[2,{type:"json",grammar:JSON.stringify(t)}]):[2,{type:"json",grammar:""}]}))}))},Q=function(e){return r(void 0,void 0,void 0,(function(){var t,n;return o(this,(function(r){return e===V?[2,L(C())]:(t=T(),(n=L(t)).onEnterRules=[{beforeText:/^.*?{.*?->[^}]*$/,action:{indentAction:2}}],[2,n])}))}))},X=Promise.resolve({createOnigScanner:W.createOnigScanner,createOnigString:W.createOnigString}),ee=x(n(50559)),te=x(n(15653)),ne=function(){function e(){this._queue=[]}return e.prototype.enqueue=function(e){this._queue.push(e)},e.prototype.dequeue=function(){return this._queue=this._queue.slice(-1),this._queue.pop()},Object.defineProperty(e.prototype,"size",{get:function(){return this._queue.length},enumerable:!1,configurable:!0}),e}(),re={suggestions:{width:600,height:256}},oe=function(e,t){void 0===e&&(e="scriptrunner.editor"),void 0===t&&(t=localStorage);var n=this;if(this.key=e,this.storage=t,this._persistSettings=function(){n.storage.setItem(n.key,JSON.stringify(n._settings))},this.persistSuggestionsConfig=function(e){if(e.value._persistedSize){var t=e.value.element.size.width||n._getSuggestionsWidth();e.value._persistedSize.store({width:t,height:re.suggestions.height}),n._setSuggestionsWidth(t)}},this._getSuggestionsWidth=function(){var e;return(null==(e=n._settings.suggestions)?void 0:e.width)||re.suggestions.width},this._setSuggestionsWidth=function(e){n._settings.suggestions&&(n._settings.suggestions.width=e,n._persistSettings())},this.storage.getItem(this.key))try{this._settings=JSON.parse(this.storage.getItem(this.key))}catch(e){console.warn("Unable to parse editor settings from local storage. Using default settings as fallback."),this._settings=re}else this._settings=re};(K=z||(z={}))[K.Hint=1]="Hint",K[K.Info=2]="Info",K[K.Warning=4]="Warning",K[K.Error=8]="Error";var ie,ae,ce,se,ue,le,de=function(e){return me(e,8)},pe=function(e){return me(e,4)},fe=function(e){return me(e,2)},me=function(e,t){return{startLineNumber:e.startLine,endLineNumber:e.endLine,startColumn:e.startColumn,endColumn:e.endColumn,message:he(e.originalMessage),messageHTML:e.originalMessage,severity:t}},he=function(e){var t=document.createElement("DIV");return t.innerHTML=e,t.textContent||t.innerText||""};(ae=ie||(ie={}))[ae.Method=0]="Method",ae[ae.Function=1]="Function",ae[ae.Constructor=2]="Constructor",ae[ae.Field=3]="Field",ae[ae.Variable=4]="Variable",ae[ae.Class=5]="Class",ae[ae.Struct=6]="Struct",ae[ae.Interface=7]="Interface",ae[ae.Module=8]="Module",ae[ae.Property=9]="Property",ae[ae.Event=10]="Event",ae[ae.Operator=11]="Operator",ae[ae.Unit=12]="Unit",ae[ae.Value=13]="Value",ae[ae.Constant=14]="Constant",ae[ae.Enum=15]="Enum",ae[ae.EnumMember=16]="EnumMember",ae[ae.Keyword=17]="Keyword",ae[ae.Text=18]="Text",ae[ae.Color=19]="Color",ae[ae.File=20]="File",ae[ae.Reference=21]="Reference",ae[ae.Customcolor=22]="Customcolor",ae[ae.Folder=23]="Folder",ae[ae.TypeParameter=24]="TypeParameter",ae[ae.User=25]="User",ae[ae.Issue=26]="Issue",ae[ae.Snippet=27]="Snippet",(se=ce||(ce={}))[se.Invoke=0]="Invoke",se[se.TriggerCharacter=1]="TriggerCharacter",se[se.TriggerForIncompleteCompletions=2]="TriggerForIncompleteCompletions",(le=ue||(ue={}))[le.KeepWhitespace=1]="KeepWhitespace",le[le.InsertAsSnippet=4]="InsertAsSnippet";var ge=function(e){return new Promise((function(t){var n=e.value.onDidShow((function(){t(!0),n.dispose()})),r=e.value.onDidHide((function(){t(!1),r.dispose()}))}))},ye=function(e){return r(void 0,void 0,void 0,(function(){var t,n,r,i,a;return o(this,(function(o){switch(o.label){case 0:return[4,ve(e)];case 1:return t=o.sent(),n=t.html,r=t.fragment,i=t.url,n?[2,{value:null!=(a=xe(n,r,i))?a:"No documentation found",supportHtml:!0}]:[2,{supportHtml:!1,value:"Cannot fetch documentation, please check your internet connection."}]}}))}))},ve=function(e){return r(void 0,void 0,void 0,(function(){var t,n,r,i,a;return o(this,(function(o){switch(o.label){case 0:t=new URL(e).hash.substr(1),n=e.indexOf("#"),r=e.substr(0,n<0?e.length:n),o.label=1;case 1:return o.trys.push([1,4,,5]),[4,fetch(r)];case 2:return i=o.sent(),a={fragment:t},[4,i.text()];case 3:return[2,(a.html=o.sent(),a.url=r,a)];case 4:return o.sent(),[2,{fragment:t,html:null,url:r}];case 5:return[2]}}))}))},be=function(e,t){var n=(new DOMParser).parseFromString(e,"text/html").body;if(!t)return n.querySelector("div.contentContainer > div.description > ul > li");var r=n.querySelector('a[name="'.concat(t,'"], a[id="').concat(t,'"]'));return r&&r.nextElementSibling?r.nextElementSibling.querySelector("li"):null},we=function(e){var t=e.arguments.map((function(e,t){return"".concat(e," ").concat(function(e,t){var n,r=e.endsWith("[]"),o=["[","<"].map((function(t){return e.indexOf(t)})).filter((function(e){return e>0})),i=o.length>0?e.substr(0,Math.min.apply(Math,c([],a(o),!1))):e;return"".concat((n=i,n.charAt(0).toLowerCase()+n.slice(1))).concat(r?"s":"").concat(t)}(e,t))}));return{label:t.join(", "),parameters:t.map((function(e){return{label:e}}))}},Ee=function(e){return r(void 0,void 0,void 0,(function(){var t,n,i,a,c;return o(this,(function(s){switch(s.label){case 0:return n=e.suggestions.map((function(e){return r(void 0,void 0,void 0,(function(){var t;return o(this,(function(n){switch(n.label){case 0:return 0===e.arguments.length?[2,{label:"(no parameters)",parameters:[]}]:e.docUrl?[4,(i=e.docUrl,r(void 0,void 0,void 0,(function(){var e,t,n;return o(this,(function(r){switch(r.label){case 0:return[4,ve(i)];case 1:return e=r.sent(),t=e.html,n=e.fragment,[2,Se(t,n)]}}))})))]:[3,2];case 1:return[2,null!=(t=n.sent())?t:we(e)];case 2:return[2,we(e)]}var i}))}))})),i=e.suggestions.findIndex((function(e){return e.enabled})),c={activeSignature:a=i>-1?i:0,activeParameter:null==(t=e.suggestions[a])?void 0:t.parameterIndex},[4,Promise.all(n)];case 1:return c.signatures=s.sent(),[2,{dispose:function(){},value:c}]}}))}))},Se=function(e,t){var n,r=be(e,decodeURIComponent(t));if(!r)return null;var o,i=null==(n=r.querySelector("h4"))?void 0:n.textContent,a=i.includes("(")?i:r.querySelector("pre").textContent,c=r.querySelector(".paramLabel, dt > b");if(c){var s=c.closest("dt");o=function(e,t){var n=[];for(e=e.nextElementSibling;e&&e.matches(t);)n.push(e),e=e.nextElementSibling;return n}(s,"dd").map((function(e){return e.innerHTML}))}var u=a.match(/\((.*)\)/s)[1],l=u.split(/,\s*/s);return{label:u,parameters:l.map((function(e,t){var n={label:e};return o&&(n.documentation={value:o[t],supportHtml:!0}),n}))}},xe=function(e,t,n){var r,o=be(e,decodeURIComponent(t));return o?(null==(r=o.querySelector("h4"))||r.remove(),Ie(o),function(e,t){Array.from(e.querySelectorAll("a")).forEach((function(e){var n=e.getAttribute("href");if(n){var r=0===n.indexOf("http://")||0===n.indexOf("https://")?n:t.substr(0,t.lastIndexOf("/")+1)+n;e.setAttribute("data-href",r),e.setAttribute("href",r),e.setAttribute("target","_blank")}}))}(o,n),Pe(o.innerHTML)):null},Pe=function(e){return(t=e,t.split(/\r\n|\n|\r/)).map((function(e){return e.trim()})).join("\n");var t},Ie=function(e){Ce(e,"dl","ul"),Ce(e,"dt","li"),Ce(e,"dd","li")},Ce=function(e,t,n){Array.from(e.querySelectorAll(t)).forEach((function(e){var t=document.createElement(n);t.innerHTML=e.innerHTML,e.replaceWith(t)}))},Te=x(n(15653)),Oe=function(){function e(){this._exactMatchMode=!1,this._smartCompletions=!1,this._hasCompletions=!1}return Object.defineProperty(e.prototype,"exactMatchMode",{get:function(){return this._exactMatchMode},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"smartCompletions",{get:function(){return this._smartCompletions},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"hasCompletions",{get:function(){return this._hasCompletions},set:function(e){this._hasCompletions=e},enumerable:!1,configurable:!0}),e.prototype.enableExactMode=function(){this._exactMatchMode=!0},e.prototype.enableSmartCompletions=function(){this._smartCompletions=!0},e.prototype.reset=function(){this._exactMatchMode=!1,this._smartCompletions=!1},e}(),ke=function(){function e(e,t,n,r){this.completionState=e,this.editorInfoService=t,this.fetchSuggestions=n,this.track=r}return Object.defineProperty(e.prototype,"completionItemProvider",{get:function(){var e=this;return{triggerCharacters:[".",'"',"'"],resolveCompletionItem:function(t){return e.resolveCompletionItem(t)},provideCompletionItems:function(t,n,i){return r(e,void 0,void 0,(function(){return o(this,(function(e){return[2,this.provideCompletionItems(t,n,i)]}))}))}}},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"hasCompletions",{get:function(){return this.completionState.hasCompletions},enumerable:!1,configurable:!0}),e.prototype.resolveCompletionItem=function(e){return r(this,void 0,void 0,(function(){var t,n;return o(this,(function(r){switch(r.label){case 0:return(t=e.documentationUrl)?[4,this.editorInfoService.getInfoByModelId(e.modelId).shouldShowDocumentation()]:[3,2];case 1:t=r.sent(),r.label=2;case 2:return t?[4,_e(e.documentationUrl)]:[3,4];case 3:return n=r.sent(),[2,w(b({},e),{documentation:n})];case 4:return[2]}}))}))},e.prototype.provideCompletionItems=function(e,t,n){return r(this,void 0,void 0,(function(){var r,i,a,c,s,u,l,d,p,f;return o(this,(function(o){switch(o.label){case 0:return s=null!=(r=e.getWordAtPosition(t))?r:{word:"",startColumn:t.column,endColumn:t.column},[4,this.fetchSuggestions({model:e,position:t,exactMatchMode:this.completionState.exactMatchMode,smartCompletions:this.completionState.smartCompletions})];case 1:return u=o.sent(),l=u.result,this.completionState.hasCompletions=l.completions.length>0,d=l.filterText&&"dummyVariableUsedByScriptRunnerCodeInsight"!==l.filterText?l.filterText.length:0,p={startLineNumber:t.lineNumber,endLineNumber:t.lineNumber,startColumn:t.column-d,endColumn:null==s?void 0:s.endColumn},f={manual:n.triggerKind===ce.Invoke,smartCompletions:this.completionState.smartCompletions,suggestions:null!=(a=null==(i=l.completions)?void 0:i.length)?a:0},null==(c=this.track)||c.completionsOpen(this.completionState.smartCompletions?"smartCompletions":"completions",f),this.completionState.reset(),[2,{incomplete:l.incomplete,suggestions:l.completions.map(this.toSuggestion(e,l,p,f))}]}}))}))},e.prototype.toSuggestion=function(e,t,n,r){var o=this;return function(i,s){var u,l,d=[],p={modelId:e.id,label:{label:i.text,detail:i.detail,description:i.description},kind:i.kind,filterText:i.text,range:n,sortText:String(s).padStart(4,"0"),insertText:null!=(u=i.insertText)?u:i.text+(i.bracketsStr?i.bracketsStr.replace("|","$0"):""),insertTextRules:ue.InsertAsSnippet};if(i.docUrl&&(p.documentation="Loading...",p.documentationUrl=i.docUrl),[ie.Method,ie.Constructor].includes(p.kind)&&!(null==(l=i.detail)?void 0:l.startsWith("(Closure"))){var f={type:"showParams",editor:o.editorInfoService.getInfoByModelId(e.id).editor};d.push(f)}var m={type:"appendImport",model:e,insertImportString:i.insertImportString,importInsertPosition:t.importInsertPosition},h={type:"trackCompletion",action:"completionSelected",trackingPayloadCompletions:r,completion:i,idx:s};return d.push(m,h),p.command={title:"",id:ke.registerCommandId,arguments:c([],a(d),!1)},p}},e}(),Ae=ke;Ae.registerCommandId="AFTER_SUGGESTION";var _e=Te.memoizeWith(Te.identity,(function(e){return r(void 0,void 0,void 0,(function(){return o(this,(function(t){return[2,ye(e)]}))}))})),De=function(e){return e.trigger("","editor.action.triggerParameterHints",{})},Ne=function(e,t,n){return function(){if(t){var r=n.from,o=n.to;e.pushEditOperations(null,[{text:"".concat(t,"\n"),range:{startLineNumber:r.line+1,startColumn:r.ch+1,endLineNumber:o.line+1,endColumn:o.ch+1}}],null)}}},Fe=function(e){return"showParams"===e.type},Le=function(e){return"trackCompletion"===e.type},Re=function(e){return"appendImport"===e.type},je=function(){function e(e){void 0===e&&(e={}),this._editorInfoMap=e}return Object.defineProperty(e.prototype,"editorInfoMap",{get:function(){return this._editorInfoMap},enumerable:!1,configurable:!0}),e.prototype.getInfoByModelId=function(e){return this._editorInfoMap[e]},e.prototype.getInfoByEditorId=function(e){return Object.values(this._editorInfoMap).find((function(t){return t.props.editorId===e}))},e.prototype.addEditorInfo=function(e,t){this._editorInfoMap[e]||(this._editorInfoMap[e]=t)},e.prototype.deleteInfoByEditorId=function(e){var t=Object.entries(this._editorInfoMap).find((function(t){var n=a(t,2);n[0];return n[1].props.editorId===e}));if(t){var n=a(t,1)[0];delete this._editorInfoMap[n]}},e}(),Me=x(n(74729)),Ue=x(n(82345)),Ze=x(n(74729)),We=function(){function e(){this.inlayHintsSubject=new Ue.Subject}return e.prototype.lint=function(e,t,n){return r(this,void 0,void 0,(function(){var r;return o(this,(function(o){switch(o.label){case 0:return[4,(0,Ze.wrappedFetch)("".concat(t,"/check"),{method:"POST",body:JSON.stringify(n)})];case 1:return r=o.sent(),this.inlayHintsSubject.next({modelId:e,inlayHints:r.result.compilationResult.inlayHints}),[2,r.result]}}))}))},e.prototype.inlayHints=function(e){return(0,Ue.firstValueFrom)(this.inlayHintsSubject.pipe((0,Ue.filter)((function(t){return t.modelId==e})),(0,Ue.map)((function(e,t){return e.inlayHints}))))},e}(),qe=new je,Ge=new Oe,Ve=null,He=!1,Je=null,Be=["groovy","GStringTemplate"],ze=function(e){return Be.includes(e)},Ke=new ee.default({concurrency:1,queueClass:ne}),$e=function(e,t,n,r){Ke.add((function(){return Ye(e,t,n,r)}))},Ye=function(e,t,n,i){return r(void 0,void 0,void 0,(function(){var r,a,c,s,u,l,d,p;return o(this,(function(o){switch(o.label){case 0:return He?[2]:(l=t.getModel()).getValue()?(null==(c=i.onRagStatusDidChange)||c.call(i,"waiting"),[4,Je.lint(l.id,e,{script:l.getValue(),compileContextDescriptor:qe.getInfoByModelId(l.id).compileContextDescriptor,language:i.language})]):(n.editor.setModelMarkers(l,"owner",[]),null==(r=i.onRagStatusDidChange)||r.call(i,null),null==(a=i.onParametersDidChange)||a.call(i,{known:!0,metadata:[]}),[2]);case 1:return d=o.sent(),p=d.compilationResult.errors.map(de).concat(d.compilationResult.warnings.map(pe)).concat(d.compilationResult.infos.map(fe)),n.editor.setModelMarkers(l,"owner",p),null==(s=i.onRagStatusDidChange)||s.call(i,function(e){var t,n;return(null==(t=e.compilationResult.errors)?void 0:t.length)?"red":(null==(n=e.compilationResult.warnings)?void 0:n.length)?"amber":"green"}(d)),null==(u=i.onParametersDidChange)||u.call(i,d.compilationResult.parameters),[2]}}))}))};var Qe=function(e,t,n,i){var a;null==Je&&(Je=new We);var c,s=t.getModel(),u=s.id,l=new oe,d=null==(a=t.getContribution("editor.contrib.suggestController"))?void 0:a.widget;d&&(l.persistSuggestionsConfig(d),c=function(e,t){var n=null,i=ge(t),a=e.getContainerDomNode();return t.value.onDidShow((function(){n=new Promise((function(e){var n=new MutationObserver((function(t,n){a.querySelector(".suggest-details")&&(n.disconnect(),e(!0))}));t.value.onDidHide((function(){n.disconnect(),e(!1)})),n.observe(a,{childList:!0,subtree:!0,attributes:!1,characterData:!1})}))})),t.value.onDidHide((function(){i=ge(t)})),function(){return r(void 0,void 0,void 0,(function(){return o(this,(function(e){switch(e.label){case 0:return[4,i];case 1:return[2,e.sent()&&n]}}))}))}}(t,d),d.value.onDidShow((function(){He=!0})),d.value.onDidHide((function(){He=!1,Ve.hasCompletions&&l.persistSuggestionsConfig(d)}))),qe.addEditorInfo(u,{compileContextDescriptor:i.compileContextDescriptor,validate:function(){return $e(e,t,n,i)},editor:t,props:i,shouldShowDocumentation:c});var p,f=n.KeyMod,m=n.KeyCode;(t.addAction({keybindings:[f.Alt|f.Shift|m.Space],id:"groovySmartCompletions",label:"",run:function(e){Ge.enableSmartCompletions(),e.trigger("","editor.action.triggerSuggest",{})}}),ze(i.language))&&(Ye(e,t,n,i),s.onDidChangeContent((function(){i.onChange(s.getValue()),clearTimeout(p),!s.getValue()||i.disableStaticCompilationDebounce?$e(e,t,n,i):p=setTimeout((function(){return $e(e,t,n,i)}),1e3)})))},Xe=function(e,t,n,c){null==Ve&&(Ve=function(e,t){return new Ae(Ge,qe,(function(t){var n=t.model,r=t.position,o=t.exactMatchMode,i=t.smartCompletions,a="".concat(e,"/").concat(i?"smartcompletions":"completions");return(0,Me.wrappedFetch)(a,{method:"POST",body:JSON.stringify({source:n.getValue(),line:r.lineNumber-1,ch:r.column-1,exactMatch:o,compileContextDescriptor:qe.getInfoByModelId(n.id).compileContextDescriptor,language:n.getLanguageId()})})}),t)}(n,c));var s=function(t){return e.Range.fromPositions({lineNumber:t.from.line+1,column:t.from.ch+1},{lineNumber:t.to.line+1,column:t.to.ch+1})};e.editor.registerCommand("RUN_ANYTHING",(function(){for(var e,t,n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];var o=a(n),c=o.slice(1);try{for(var s=i(c),u=s.next();!u.done;u=s.next()){var l=u.value;l()}}catch(t){e={error:t}}finally{try{u&&!u.done&&(t=s.return)&&t.call(s)}finally{if(e)throw e.error}}})),e.editor.registerCommand(Ae.registerCommandId,(function(){for(var e,t,n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];var o=a(n),s=o.slice(1);try{for(var u=i(s),l=u.next();!l.done;l=u.next()){var d=l.value;if(Fe(d))De(d.editor);else if(Le(d)){var p=d.trackingPayloadCompletions,f=d.completion,m=d.idx;null==c||c.completionSelected(p,f,m)}else if(Re(d)){var h=d.model,g=d.insertImportString,y=d.importInsertPosition;Ne(h,g,y)()}}}catch(t){e={error:t}}finally{try{l&&!l.done&&(t=u.return)&&t.call(u)}finally{if(e)throw e.error}}})),e.languages.registerDeclarationProvider(t,{provideDeclaration:function(e,i){return r(this,void 0,void 0,(function(){var a=this;return o(this,(function(c){return[2,Ke.add((function(){return r(a,void 0,void 0,(function(){var r,a;return o(this,(function(o){switch(o.label){case 0:return[4,(0,Me.wrappedFetch)("".concat(n,"/gotodefinition"),{method:"POST",body:JSON.stringify({source:e.getValue(),line:i.lineNumber-1,ch:i.column-1,compileContextDescriptor:qe.getInfoByModelId(e.id).compileContextDescriptor,language:t})})];case 1:return r=o.sent(),(a=r.result.definition)?[2,{uri:e.uri,range:s(a)}]:[2,null]}}))}))}))]}))}))}}),e.languages.registerInlayHintsProvider(t,{provideInlayHints:function(e){return r(this,void 0,void 0,(function(){var t;return o(this,(function(n){switch(n.label){case 0:return[4,Je.inlayHints(e.id)];case 1:return(t=n.sent())?[2,{hints:t.map((function(e){return{label:e.label,position:{lineNumber:e.position.from.line+1,column:e.position.from.ch+1},paddingRight:!0}})),dispose:function(){}}]:[2,null]}}))}))}}),e.languages.registerReferenceProvider(t,{provideReferences:function(e,i){return r(this,void 0,void 0,(function(){var a=this;return o(this,(function(c){return[2,Ke.add((function(){return r(a,void 0,void 0,(function(){return o(this,(function(r){switch(r.label){case 0:return[4,(0,Me.wrappedFetch)("".concat(n,"/gotoreferences"),{method:"POST",body:JSON.stringify({source:e.getValue(),line:i.lineNumber-1,ch:i.column-1,compileContextDescriptor:qe.getInfoByModelId(e.id).compileContextDescriptor,language:t})})];case 1:return[2,r.sent().result.usages.map((function(t){return{uri:e.uri,range:s(t)}}))]}}))}))}))]}))}))}}),e.languages.registerHoverProvider(t,{provideHover:function(i,a){return r(this,void 0,void 0,(function(){var c=this;return o(this,(function(s){return e.editor.getModelMarkers({}).filter((function(t){return t.severity===e.MarkerSeverity.Error})).map((function(t){return e.Range.fromPositions({lineNumber:t.startLineNumber,column:t.startColumn},{lineNumber:t.endLineNumber,column:t.endColumn})})).some((function(e){return e.containsPosition(a)}))?[2,null]:[2,Ke.add((function(){return r(c,void 0,void 0,(function(){var e,r,c;return o(this,(function(o){switch(o.label){case 0:return[4,(0,Me.wrappedFetch)("".concat(n,"/documentation"),{method:"POST",body:JSON.stringify({source:i.getValue(),line:a.lineNumber-1,ch:a.column-1,compileContextDescriptor:qe.getInfoByModelId(i.id).compileContextDescriptor,language:t})})];case 1:return e=o.sent(),(r=e.result).hasNoDocumentation?[2,null]:[4,ye(r.javadocUrl)];case 2:return c=o.sent(),[2,{contents:[c]}]}}))}))}))]}))}))}}),e.languages.registerSignatureHelpProvider(t,{signatureHelpTriggerCharacters:["(",","],signatureHelpRetriggerCharacters:[","],provideSignatureHelp:function(i,a,c,s){return r(this,void 0,void 0,(function(){var c,u,l,d,p=this;return o(this,(function(f){if(s.isRetrigger&&(u=null==(c=s.activeSignatureHelp)?void 0:c.position)){if(l=e.Range.fromPositions(a,u),d=i.getValueInRange(l),["(",")","{","}"].some((function(e){return d.includes(e)})))return[2,null];if(![","," "].some((function(e){return d.includes(e)})))return[2,{value:s.activeSignatureHelp,dispose:function(){}}]}return[2,Ke.add((function(){return r(p,void 0,void 0,(function(){var e,r,c,s,u,l;return o(this,(function(o){switch(o.label){case 0:return[4,(0,Me.wrappedFetch)("".concat(n,"/parameters"),{method:"POST",body:JSON.stringify({source:i.getValue(),line:a.lineNumber-1,ch:a.column-1,compileContextDescriptor:qe.getInfoByModelId(i.id).compileContextDescriptor,language:t})})];case 1:return e=o.sent(),r=e.result,[4,Ee(r)];case 2:return c=o.sent(),s=c.dispose,u=c.value,l=w(b({},u),{position:a}),[2,{dispose:s,value:l}]}}))}))}))]}))}))}}),e.languages.registerCodeActionProvider(t,{provideCodeActions:function(t,n,r){var o=r.markers.filter((function(e){return e.message.match(/import is never referenced/)})),i=e.editor.getModelMarkers({}).filter((function(e){return e.message.match(/import is never referenced/)})).map((function(e){return{resource:t.uri,edit:{range:{startLineNumber:e.startLineNumber,endLineNumber:e.endLineNumber+1},text:""}}}));return{actions:o.map((function(e){return{title:"Optimise Imports",diagnostics:[e],kind:"quickfix.optimise-imports",edit:{edits:i},isPreferred:!0}})),dispose:function(){}}}},{providedCodeActionKinds:["quickfix.optimise-imports"]}),e.languages.registerCodeActionProvider(t,{provideCodeActions:function(e,t,n){return{actions:n.markers.filter((function(e){return e.message.match(/The variable \[[A-Z].*?] is undeclared/)||e.message.match(/unable to resolve class [A-Z].*?/)})).map((function(t){return{title:"Import class",diagnostics:[t],kind:"quickfix",command:{id:"RUN_ANYTHING",title:"Suggest",arguments:[function(){var n=qe.getInfoByModelId(e.id).editor;Ge.enableExactMode(),n.setPosition({lineNumber:t.endLineNumber,column:t.endColumn}),n.trigger("","editor.action.triggerSuggest",{})}]},isPreferred:!0}})),dispose:function(){}}}}),e.languages.registerCompletionItemProvider(t,Ve.completionItemProvider)},et=x(n(21351)),tt=x(n(15653)),nt=x(n(63844)),rt=x(n(21351)),ot=x(n(63844)).createContext({minHeight:57,style:null}),it=new WeakMap;window.divToEditorMap=it;var at=tt.once((function(e,t,n,a){return r(void 0,void 0,void 0,(function(){var r,c,s,u,l,d;return o(this,(function(o){switch(o.label){case 0:return rt.loader.init().then((function(e){t(e)})).finally((function(){null==a||a()})),[4,B(n)];case 1:r=o.sent();try{for(c=i(J),s=c.next();!s.done;s=c.next())u=s.value,e.languages.register(u)}catch(e){l={error:e}}finally{try{s&&!s.done&&(d=c.return)&&d.call(c)}finally{if(l)throw l.error}}return r.injectCSS(),[2]}}))}))})),ct=function(e,t){void 0===t&&(t=57);var n=19*(((null==e?void 0:e.match(/\n/g))||[]).length+1+1);return Math.min(700,Math.max(n,t))},st=function(e){var t,n=(0,nt.useRef)(null),r=nt.default.useContext(ot),o=r.minHeight,i=r.style,c=a((0,nt.useState)(ct(e.defaultValue,o)),2),s=c[0],u=c[1],l=e.fullScreen,d=(0,nt.useRef)(!1);nt.default.useEffect((function(){var e;l&&(null==(e=it.get(n.current))||e.focus())}),[l]);return nt.default.createElement("div",{onKeyUp:function(e){var t,r;if("Escape"===e.key){if(d.current){if(!l)return;null==(r=null==(t=it.get(n.current))?void 0:t.getAction("TOGGLE_FULL_SCREEN"))||r.run()}d.current=!0,setTimeout((function(){d.current=!1}),500),setTimeout((function(){var e;null==(e=it.get(n.current))||e.focus()}),1)}},className:"sr-monaco","data-qa":e.editorId,ref:n,style:l?{zIndex:1e4,position:"fixed",left:0,right:0,top:0,bottom:0}:i},nt.default.createElement(rt.default,{onMount:function(t,r){e.onMount(t,r),t.getModel().onDidChangeContent((function(){u(ct(t.getModel().getValue(),o)),t.layout()})),t.addAction({id:"TOGGLE_FULL_SCREEN",label:"Toggle Full Screen",keybindings:[r.KeyCode.F11],run:function(){var n;null==(n=e.onToggleFullScreen)||n.call(e,e.editorId),l||t.layout({width:1,height:1})}}),t.addAction({id:"SAVE",label:"Save editor content",keybindings:[r.KeyMod.CtrlCmd|r.KeyCode.KeyS],run:function(){var n;return null==(n=e.onSave)?void 0:n.call(e,t.getValue())}}),n.current&&(it.set(n.current,t),n.current.className="".concat(n.current.className," initialized"))},beforeMount:function(t){at(t,e.beforeMount,e.onigPath,e.afterMount)},height:l?"100vh":null!=(t=null==i?void 0:i.height)?t:"".concat(s,"px"),defaultLanguage:e.defaultLanguage,defaultValue:e.defaultValue,theme:"vs-dark",onChange:function(t){return e.onChange(t)},options:b({fixedOverflowWidgets:!0,scrollBeyondLastLine:!1,wordBasedSuggestions:!1,renderValidationDecorations:"on"},e.options),loading:e.loading}))},ut=x(n(76416)),lt=function(e){et.loader.config({paths:{vs:e.loaderUrl}});var t=(0,O.useRef)(!0);return(0,O.useEffect)((function(){return function(){return qe.deleteInfoByEditorId(e.editorId)}}),[]),(0,O.useEffect)((function(){var n,r,o;t.current?t.current=!1:"groovy"===e.language&&(n=e.editorId,r=e.compileContextDescriptor,(o=qe.getInfoByEditorId(n))?te.equals(o.compileContextDescriptor,r)||(o.compileContextDescriptor=r,o.validate()):console.warn("Attempt to update compile context of an editor that does not exist",n))}),[e.compileContextDescriptor]),O.default.createElement(st,{editorId:e.editorId,defaultValue:e.value,defaultLanguage:e.language,options:{readOnly:e.readonly},fullScreen:e.fullScreen,beforeMount:function(t){return function(e,t,n,a){return r(void 0,void 0,void 0,(function(){var c,s,u,l,d,p,f;return o(this,(function(m){switch(m.label){case 0:return[4,B(a)];case 1:c=m.sent(),s=function(i){e.languages.onLanguage(i,(function(){return r(void 0,void 0,void 0,(function(){var r,a,s;return o(this,(function(o){switch(o.label){case 0:return[4,c.fetchLanguageInfo(i)];case 1:return r=o.sent(),a=r.tokensProvider,s=r.configuration,null!=a&&e.languages.setTokensProvider(i,a),null!=s&&e.languages.setLanguageConfiguration(i,s),Xe(e,i,t,n),[2]}}))}))}))};try{for(u=i(H),l=u.next();!l.done;l=u.next())d=l.value,s(d)}catch(e){p={error:e}}finally{try{l&&!l.done&&(f=u.return)&&f.call(u)}finally{if(p)throw p.error}}return[2]}}))}))}(t,e.serviceUrl,e.tracking,e.onigPath)},onigPath:e.onigPath,onToggleFullScreen:e.onToggleFullScreen,onMount:function(t,n){var r;null==(r=e.onMount)||r.call(e,t,n),Qe(e.serviceUrl,t,n,e)},afterMount:e.afterMount,onChange:function(t){return e.onChange(t)},onSave:e.onSave,loading:O.default.createElement(dt,null)})},dt=O.default.memo((function(){return O.default.createElement(ut.LoadingSpinner,{size:"large"})})),pt=x(n(63844)),ft={prod:"https://qhtj7s9cqi.execute-api.us-east-1.amazonaws.com/default",dev:"https://rakb2o7pcc.execute-api.us-east-1.amazonaws.com/default",local:"http://localhost:5680/codeinsight"},mt=function(e){var t;return pt.default.createElement(lt,b({loaderUrl:"https://cdn.jsdelivr.net/npm/monaco-editor@0.33.0/min/vs",serviceUrl:"".concat(ft[null!=(t=e.lambdaEnvironment)?t:"prod"],"/").concat(e.host)},e))}},50770:(e,t,n)=>{"use strict";n.d(t,{Z:()=>i});var r=function(){return location.href.indexOf("admin/workflows/EditWorkflowTransition")>0},o=function(){return location.href.indexOf("admin/workflows/AddWorkflowTransition")>0};const i={isEditWorkflowFunction:r,isAddWorkflowFunction:o,isWorkflows:function(){return r()||o()},isTests:function(){return location.href.indexOf("plugins/servlet/scriptrunner/tests")>0}}},77937:(e,t,n)=>{"use strict";n.d(t,{$9:()=>F,Dq:()=>V,ER:()=>C,Ge:()=>N,H2:()=>K,HP:()=>q,IK:()=>j,Id:()=>G,Jy:()=>b,KE:()=>E,Ko:()=>_,M0:()=>R,OH:()=>H,PQ:()=>D,PT:()=>P,R0:()=>te,Sj:()=>re,TO:()=>w,XS:()=>U,aB:()=>W,aL:()=>B,bB:()=>S,d3:()=>Q,fP:()=>z,fY:()=>k,kq:()=>A,kz:()=>x,mB:()=>ee,pD:()=>J,qE:()=>L,qS:()=>$,tr:()=>Z,uc:()=>ne,vG:()=>I,x$:()=>Y});var r=n(86530),o=n(50770),i=n(7011),a=n(71530),c=n(39507),s=n(64772),u=n(96355),l=n(66638),d=n(84331),p=n(50382),f=n(74729),m=function(){return m=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},m.apply(this,arguments)},h=function(e,t,n,r){return new(n||(n=Promise))((function(o,i){function a(e){try{s(r.next(e))}catch(e){i(e)}}function c(e){try{s(r.throw(e))}catch(e){i(e)}}function s(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(a,c)}s((r=r.apply(e,t||[])).next())}))},g=function(e,t){var n,r,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:c(0),throw:c(1),return:c(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function c(i){return function(c){return function(i){if(n)throw new TypeError("Generator is already executing.");for(;a;)try{if(n=1,r&&(o=2&i[0]?r.return:i[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,i[1])).done)return o;switch(r=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return a.label++,{value:i[1],done:!1};case 5:a.label++,r=i[1],i=[0];continue;case 7:i=a.ops.pop(),a.trys.pop();continue;default:if(!(o=a.trys,(o=o.length>0&&o[o.length-1])||6!==i[0]&&2!==i[0])){a=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){a.label=i[1];break}if(6===i[0]&&a.label<o[1]){a.label=o[1],o=i;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(i);break}o[2]&&a.ops.pop(),a.trys.pop();continue}i=t.call(e,a)}catch(e){i=[6,e],r=0}finally{n=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,c])}}},y=(0,r.ZP)(),v=(0,u.ZP)(y),b=y("CLEAR_VALIDATION_AND_EDIT"),w=y("UPDATE_VALIDATION"),E=y("UPDATE_VALIDATION_FOR_FIELD"),S=y("UPDATE_BUILTIN_SCRIPT_PARAM"),x=y("COPY_CONFIG_TO_EDIT"),P=v("DELETE_ITEM",(function(e,t,n){var r=e.config,o=e.id;return h(void 0,void 0,void 0,(function(){var e,i;return g(this,(function(a){switch(a.label){case 0:return e=n().context,i=(0,c.F)(m({},e)),[4,(0,f.fetchJson)("".concat(r.restUrl,"/").concat(o).concat(i?"?".concat(i):""),{method:"DELETE"})];case 1:return void 0===a.sent().error&&t(p.j.configured.initiate(r,e,{forceRefetch:!0})),[2]}}))}))})),I=v("VALIDATE",(function(e,t,n){var r=e.config,o=e.extraParams;return h(void 0,void 0,void 0,(function(){var e,t,i,a;return g(this,(function(s){switch(s.label){case 0:return e=m(m({},n().edit),o),t=e["canned-script"],i=(0,c.F)(n().context),[4,(0,f.fetchJson)("".concat(r.restUrl,"/").concat(t,"/validate?").concat(i),{method:"POST",body:JSON.stringify(e)})];case 1:if(!(a=s.sent()).response.ok)throw{success:!1,validation:a.errorResult};return[2,{}]}}))}))})),C=function(e,t,n,r){return h(void 0,void 0,void 0,(function(){return g(this,(function(o){return[2,(0,f.fetchJson)("".concat(e.restUrl,"/").concat(t).concat(r?"?".concat(r):""),{method:"POST",body:JSON.stringify(n)})]}))}))},T=function(e,t,n){return h(void 0,void 0,Promise,(function(){var r,o,i,a;return g(this,(function(s){return r=e().context,o=(0,l.S)({bitbucket:m(m(m({},t),r.repositoryId&&{FIELD_PER_REPO_ID:r.repositoryId}),!t.FIELD_PER_REPO_ID&&r.projectId&&!r.repositoryId&&{perProjectId:r.projectId}),default:t}),i=(0,c.F)(m(m({},n.urlParams),r)),a=t["canned-script"],[2,C(n,a,o,i)]}))}))},O=function(e,t){return void 0===t&&(t=function(){return null}),v(e,(function(e,n,r){var o=e.config;return h(void 0,void 0,void 0,(function(){var e,i,c;return g(this,(function(s){switch(s.label){case 0:return e=r().edit,[4,T(r,e,o)];case 1:return(i=s.sent()).response.ok?(n(p.j.configured.invalidate(o)),n(b()),n((0,a.VF)(o.rootHash)),[4,t(i)]):[3,3];case 2:return c=s.sent(),[2,m(m({},e),c)];case 3:throw{success:!1,validation:i.errorResult}}}))}))}))},k=O("POST_UPDATE"),A=O("POST_SCRIPT_FIELD_UPDATE",(function(e){return h(void 0,void 0,void 0,(function(){var t;return g(this,(function(n){return[2,{customFieldId:(t=e.result).customFieldId,fieldConfigurationSchemeId:t.fieldConfigurationSchemeId}]}))}))})),_=v("EXECUTE_BUILTIN",(function(e,t,n){var r=e.builtinScript,o=e.preview,i=e.config;return h(void 0,void 0,void 0,(function(){var e,t;return g(this,(function(a){switch(a.label){case 0:return[4,(0,f.fetchJson)("".concat(i.restUrl,"/").concat(r).concat(o?"/preview":""),{method:"POST",body:JSON.stringify(m(m({},n().edit),n().context))})];case 1:if(!(e=a.sent()).response.ok)throw{success:!1,validation:e.errorResult};return(t=e.result).hasOwnProperty("output")?[2,t]:[2,{output:t}]}}))}))})),D=function(e,t){return setTimeout((function(){return e(R({section:t}))}),500)},N=y("UPDATE_CONSOLE"),F=v("EXECUTE_SCRIPT_CONSOLE",(function(e,t,n){return h(void 0,void 0,void 0,(function(){var e,r,o,i;return g(this,(function(a){switch(a.label){case 0:void 0===(e=n().edit.SCRIPT_CONSOLE)&&(r=n().console||{scriptText:null,scriptFile:null,parameters:{}},e={script:r.scriptText,scriptPath:r.scriptFile,parameters:r.parameters}),o=function(e,t){return setTimeout((function(){return e(j({button:t}))}),1e3)}(t,"submit"),a.label=1;case 1:return a.trys.push([1,,3,4]),[4,(0,f.fetchJson)("".concat(AJS.contextPath(),"/rest/scriptrunner/latest/user/exec/"),{method:"POST",body:JSON.stringify(e)})];case 2:if(500===(i=a.sent()).response.status){if(Object.keys(i.errorResult).includes("errorMessages"))throw{success:!1,validation:i.errorResult};return[2,i.errorResult]}return[2,i.result];case 3:return clearTimeout(o),[7];case 4:return[2]}}))}))})),L=v("EXECUTE_SCRIPT_FIELD_PREVIEW_VALUE_UPDATE",(function(e){var t=e.parameters,n=e.form;return h(void 0,void 0,void 0,(function(){var e;return g(this,(function(r){switch(r.label){case 0:return[4,(0,f.fetchJson)("".concat(AJS.contextPath(),"/rest/scriptrunner-jira/latest/scriptfields/previewHtml"),{method:"POST",body:JSON.stringify({parameters:t,form:n})})];case 1:return(e=r.sent()).error&&console.warn("Failure to get field preview",e.error),[2,e.result]}}))}))})),R=y("SLOW_LOADING_INDICATOR"),j=y("SLOW_EXECUTION_INDICATOR"),M=function(e,t){return 200===e.response.status?e.result:404===e.response.status?(t(ee({body:"An error occurred, try refreshing the page.",type:"error",title:"Not found",close:"manual"})),[]):401===e.response.status?(t(ee({body:"You are not logged in as an administrator. Try refreshing the page.",type:"error",title:"Permission denied",close:"manual"})),[]):(t(ee({body:"An error occurred. Please review server logs.",type:"error",title:"Server Error",close:"manual"})),[])},U=v("LOAD_PARAMS",(function(e,t,n){var r=e.config,i=e.builtinScript,a=e.existingItem;return h(void 0,void 0,void 0,(function(){var e,s,u,d,p,h,y,v,b,w,E,S;return g(this,(function(g){switch(g.label){case 0:e=n().edit&&Object.keys(n().edit).length,s=new URLSearchParams(window.location.search),u=o.Z.isWorkflows()?{workflowName:s.get("workflowName"),workflowMode:s.get("workflowMode"),workflowTransition:s.get("workflowTransition")}:{},d=e?n().edit:a||{},p=D(t,"params"),g.label=1;case 1:return g.trys.push([1,,4,5]),h=n().context,y=(0,c.F)(m(m({},r.urlParams),h)),v=(0,l.S)({bitbucket:m(m(m(m({},d),u),h.repositoryId&&{FIELD_PER_REPO_ID:h.repositoryId}),h.projectId&&!h.repositoryId&&!d.FIELD_PER_REPO_ID&&{perProjectId:h.projectId}),bamboo:m(m({},d),h),default:m(m({},d),u)}),[4,(0,f.fetchJson)("".concat(r.restUrl,"/").concat(i,"/params").concat(y?"?".concat(y):""),{method:"POST",body:JSON.stringify(v)})];case 2:if(500===(b=g.sent()).response.status)throw{success:!1,validation:b.errorResult};return[4,M(b,t)];case 3:return w=g.sent(),a?(E=w.reduce((function(e,t){var n;return null===a[t.name]||t.hidden?m(m({},e),((n={})[t.name]=t.value,n)):e}),a),t(x(E))):(S=w.reduce((function(e,t){var n;return m(m({},e),((n={})[t.name]=t.value,n))}),{}),t(x(m(m({},S),{"canned-script":i})))),[2,w];case 4:return clearTimeout(p),[7];case 5:return[2]}}))}))})),Z=y("UPDATE_PARAMS"),W=y("RESET_BUTTON_STATE"),q=y("UPDATE_FIELD_VISIBILITY"),G=y("UPDATE_FIELD_ENABLED"),V=y("UPDATE_FIELD_WAITING"),H=y("UPDATE_FIELD_PROPERTIES"),J=v("SCAN_FOR_NEW_ITEMS",(function(e,t,n){return h(void 0,void 0,void 0,(function(){var r;return g(this,(function(o){switch(o.label){case 0:return[4,(0,f.fetchJson)("".concat(e.restUrl,"/scan"))];case 1:return r=o.sent(),(0,i.$7)(r.result.msg),t(p.j.configured.initiate(e,n().context)),[2]}}))}))})),B=y("UPDATE_BUTTON"),z=y("DISABLE_ALL_BUTTONS"),K=y("SET_PROJECT_CONTEXT"),$=v("DISABLE_ITEM",(function(e,t,n){var r=e.config,o=e.id;return h(void 0,void 0,void 0,(function(){return g(this,(function(e){return[2,X(!1,{config:r,id:o},t,n)]}))}))})),Y=v("ENABLE_ITEM",(function(e,t,n){var r=e.config,o=e.id;return h(void 0,void 0,void 0,(function(){return g(this,(function(e){return[2,X(!0,{config:r,id:o},t,n)]}))}))})),Q=y("TOGGLE_DISABLED"),X=function(e,t,n,r){var o=t.config,i=t.id;return h(void 0,void 0,void 0,(function(){var t,a,c;return g(this,(function(u){switch(u.label){case 0:return t=r().context,o.constructor===s.Z&&i.startsWith("com.onresolve.scriptrunner.canned.confluence.macros")?[4,(0,f.fetchJson)("".concat(o.restUrl,"/").concat(e?"enable":"disable","/").concat(i),{method:"POST"})]:[3,2];case 1:return 204===(c=u.sent()).response.status?n(p.j.configured.initiate(o,t)):M(c,n),[3,4];case 2:return n(Q({config:o,id:i})),a=r().items.find((function(e){return e[o.uniqueIdKey]===i})),[4,T(r,a,o)];case 3:(c=u.sent()).response.status>=300&&n(ee({body:"An error occurred disabling/enabling. Trying editing the item and saving - there may be a configuration problem",type:"error",title:"Error",close:"manual"})),u.label=4;case 4:return n(p.j.configured.initiate(o,t,{forceRefetch:!0})),[2]}}))}))},ee=y("SET_FLAG"),te=y("CLEAR_FLAG"),ne=v("UPDATE_WORKFLOW_FUNCTION_ITEMS",(function(e,t,n){var r=e.updatedWorkflowFunctions,o=e.modifiedWorkflowName;return h(void 0,void 0,void 0,(function(){var e,t,i;return g(this,(function(a){return e=n().items,t=e.filter((function(e){return e.name!==o})),i=e.indexOf(e.find((function(e){return e.name===o}))),[2,(0,d.QO)(t,i,r)]}))}))})),re=function(e){window.require(["aui/flag"],(function(t){t({type:"error",title:"Error",persistent:!1,body:e})}))}},97509:(e,t,n)=>{"use strict";n.d(t,{Z:()=>o});var r=AJS.$;const o={isBuildConfigAction:function(){return!1},isEditBuildConfigAction:function(){return Boolean(AJS.$("#currentArgs").val())},collectExtraParams:function(){var e=r("#admin-screens").closest("form").find('[name="planKey"]').val(),t=r("#admin-screens").closest("form").find('[name="taskId"]').val();return e||t?{planKey:e,taskId:t}:{}}}},21996:(e,t,n)=>{"use strict";n.d(t,{Z:()=>g});var r,o=n(63844),i=n(149),a=n.n(i),c=n(14849),s=n(78417),u=n(18390),l=n(65202),d=n(91594),p=(r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},r(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),f=function(e,t){return Object.defineProperty?Object.defineProperty(e,"raw",{value:t}):e.raw=t,e},m=function(e,t,n,r){return new(n||(n=Promise))((function(o,i){function a(e){try{s(r.next(e))}catch(e){i(e)}}function c(e){try{s(r.throw(e))}catch(e){i(e)}}function s(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(a,c)}s((r=r.apply(e,t||[])).next())}))},h=function(e,t){var n,r,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:c(0),throw:c(1),return:c(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function c(i){return function(c){return function(i){if(n)throw new TypeError("Generator is already executing.");for(;a;)try{if(n=1,r&&(o=2&i[0]?r.return:i[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,i[1])).done)return o;switch(r=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return a.label++,{value:i[1],done:!1};case 5:a.label++,r=i[1],i=[0];continue;case 7:i=a.ops.pop(),a.trys.pop();continue;default:if(!(o=a.trys,(o=o.length>0&&o[o.length-1])||6!==i[0]&&2!==i[0])){a=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){a.label=i[1];break}if(6===i[0]&&a.label<o[1]){a.label=o[1],o=i;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(i);break}o[2]&&a.ops.pop(),a.trys.pop();continue}i=t.call(e,a)}catch(e){i=[6,e],r=0}finally{n=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,c])}}};const g=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.state={searchString:t.props.value,searchStringChangeAccepting:!1},t.debouncedSearch=function(e){return m(t,void 0,void 0,(function(){return h(this,(function(t){switch(t.label){case 0:return[4,this.props.updateSearchString(e)];case 1:return t.sent(),this.debouncedSearchQuery(e),[2]}}))}))},t.debouncedSearchQuery=a()(t.triggerSearchQueryAccepted,500),t.handleOnChange=function(e){return m(t,void 0,void 0,(function(){var t;return h(this,(function(n){switch(n.label){case 0:return t=e.target.value,[4,this.setSearchString(t)];case 1:return n.sent(),[2]}}))}))},t.handleKeyUp=function(e){"Enter"===e.key&&t.triggerSearchQueryAccepted(e.currentTarget.value)},t}return p(t,e),t.prototype.triggerSearchQueryAccepted=function(e){this.props.onSearchQueryAccepted(),this.setState({searchStringChangeAccepting:e.trim()!==this.state.searchString.trim()})},t.prototype.setSearchString=function(e){return m(this,void 0,void 0,(function(){return h(this,(function(t){switch(t.label){case 0:return this.setState({searchString:e,searchStringChangeAccepting:!0}),[4,this.debouncedSearch(e)];case 1:return t.sent(),[2]}}))}))},t.prototype.render=function(){var e=this,t=this.props,n=t.placeholder,r=t.value,i=t.isCompact,a=t.autoFocus,u=i?"small":"medium";return o.createElement(w,{className:"sr-search-words-filter",shouldFitContainer:this.props.shouldFitContainer},o.createElement(c.Z,{autoComplete:"off",autoFocus:a,placeholder:n,value:r,onChange:this.handleOnChange,onKeyUp:this.handleKeyUp,id:"searchScripts",isCompact:i,elemAfterInput:o.createElement("div",{style:{display:"flex"}},o.createElement("a",{style:{color:"inherit"},className:r?"action":"",onClick:function(){return e.setSearchString("")}},o.createElement(b,{style:{visibility:r?"visible":"hidden"}},o.createElement(d.Z,{size:u,label:"clear",primaryColor:"#A5ADBA"}))),this.state.searchStringChangeAccepting?o.createElement(b,{className:"sr-progress-indicator"},o.createElement(s.Z,{size:u,appearance:"inherit"})):o.createElement(b,null,o.createElement(l.Z,{size:u,label:"search"})))}))},t.defaultProps={placeholder:"Search ScriptRunner functionality",shouldFitContainer:!0,isCompact:!1},t}(o.Component);var y,v,b=u.Z.span(y||(y=f(["\n    display: flex;\n    flex-direction: column;\n    justify-content: center;\n    margin-right: 8px;\n"],["\n    display: flex;\n    flex-direction: column;\n    justify-content: center;\n    margin-right: 8px;\n"]))),w=u.Z.div(v||(v=f(["\n    display: flex;\n    flex-direction: row;\n    align-items: center;\n    ","\n"],["\n    display: flex;\n    flex-direction: row;\n    align-items: center;\n    ","\n"])),(function(e){return!e.shouldFitContainer&&"& > div {\n        flex: 0 0 auto;\n        margin-right: 5px;\n    }"}))},65171:(e,t,n)=>{"use strict";n.d(t,{AP:()=>F,Ab:()=>y,DB:()=>R,Mo:()=>w,N9:()=>O,RR:()=>S,W5:()=>P,a0:()=>x,c0:()=>k,ev:()=>E,rK:()=>v,rv:()=>b,wh:()=>j,wm:()=>T});var r,o=n(18769),i=n(26052),a=n(17005),c=n(14500),s=n(7120),u=n(1559),l=n(7688),d=n(79949),p=n(37591),f=n(51372),m=n(58376),h=function(){return h=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},h.apply(this,arguments)},g={selectedFirstLevelTag:"",selectedSecondLevelTag:"",searchString:"",taggedScripts:[]},y=(0,d.oM)({name:"builtInScriptLibrary",initialState:g,reducers:{loadScriptLibrary:function(e,t){e.taggedScripts=t.payload},selectFirstLevelTag:function(e,t){var n=t.payload.tag!==e.selectedFirstLevelTag?t.payload.tag:"";e.selectedFirstLevelTag=n},selectSecondLevelTag:function(e,t){var n=t.payload.tag!==e.selectedSecondLevelTag?t.payload.tag:"";e.selectedSecondLevelTag=n},updateSearchString:function(e,t){var n=!!e.selectedSecondLevelTag&&!!e.selectedFirstLevelTag;e.searchString=t.payload.searchString,e.selectedSecondLevelTag=n?"":e.selectedSecondLevelTag,e.selectedFirstLevelTag=n?"":e.selectedFirstLevelTag},selectTags:function(e,t){e.selectedFirstLevelTag=t.payload.firstLevelTag,e.selectedSecondLevelTag=t.payload.secondLevelTag},resetSearchState:function(e){e.searchString=g.searchString,e.selectedFirstLevelTag=g.selectedFirstLevelTag,e.selectedSecondLevelTag=g.selectedSecondLevelTag}}}),v=(0,d.PH)("SEARCH_QUERY_ACCEPTED"),b=(r=y.actions).loadScriptLibrary,w=r.selectFirstLevelTag,E=r.selectSecondLevelTag,S=r.updateSearchString,x=r.selectTags,P=r.resetSearchState,I=(0,p.P1)(f.D,(function(e){return e.builtInScriptLibrary})),C=(0,p.P1)([I],(function(e){return e.taggedScripts})),T=(0,p.P1)([I],(function(e){return e.selectedFirstLevelTag})),O=(0,p.P1)([I],(function(e){return e.selectedSecondLevelTag})),k=(0,p.P1)([I],(function(e){return e.searchString})),A=(0,p.P1)([f.D],(function(e){return e.firstLevelTagsInfo.firstLevelTagsInfo.map((function(e){return e.tag}))})),_=(0,p.P1)([f.D],(function(e){return e.secondLevelTagsInfo.secondLevelTagsInfo.map((function(e){return e.tag}))})),D=function(e){return function(t){return!e||t.tags.includes(e)}},N=function(e){return function(t){if(!e)return!0;var n=t.name?t.name.toLowerCase():"",r=t.description?t.description.toLowerCase():"",o=e.trim().toLowerCase().split(" "),i=[n,r,t.tags.join(" ").toLowerCase()].join(" ");return o.every((function(e){return i.includes(e)}))}},F=(0,p.P1)(C,T,O,k,m.jK,(function(e,t,n,r,a){var c=D(t),s=D(n);return e.filter(N(r)).filter(c).filter(s).map((function(e){return h(h({},e),{isNew:!!a.scripts[e.class]})})).sort(o.Z(i.Z("isNew")))})),L=function(e,t){return(0,p.P1)(C,e,t,k,(function(e,t,n,r){var o,d=D(n);o=e.filter(d).filter(N(r));var p=a.Z(c.Z(i.Z("tags")),s.Z,u.Z)(o);return l.Z(t,p)}))},R=L(A,O),j=L(_,T)},79820:(e,t,n)=>{"use strict";n.d(t,{ac:()=>u,kY:()=>l,wk:()=>p});var r=n(21500),o=n(1491),i=n(51372),a=n(65171),c=n(79949),s=n(37591),u=(0,c.oM)({name:"firstLevelTagsInfo",initialState:{firstLevelTagsInfo:[]},reducers:{loadFirstLevelTagsInfo:function(e,t){e.firstLevelTagsInfo=t.payload}}}),l=u.actions.loadFirstLevelTagsInfo,d=(0,s.P1)(i.D,(function(e){return e.firstLevelTagsInfo})),p=(0,s.P1)([a.wm,d],(function(e,t){var n=e||"";return r.Z(o.Z("tag",n))(t.firstLevelTagsInfo)}))},51372:(e,t,n)=>{"use strict";n.d(t,{D:()=>o,e:()=>r});var r="home",o=function(e){return e.home}},96119:(e,t,n)=>{"use strict";n.d(t,{de:()=>o,jb:()=>r});var r=(0,n(79949).oM)({name:"secondLevelTagsInfo",initialState:{secondLevelTagsInfo:[]},reducers:{loadSecondLevelTagsInfo:function(e,t){e.secondLevelTagsInfo=t.payload}}}),o=r.actions.loadSecondLevelTagsInfo},13061:(e,t,n)=>{"use strict";n.d(t,{Z:()=>p});var r=n(63844),o=n(80515),i=n(40528),a=n(70117),c=n(79193),s=n(98356),u=n(65076),l=n(98229),d=function(){return d=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},d.apply(this,arguments)};const p=function(){function e(){var e=this;this.docUrl=null,this.docLabel=null,this.canAddNew=!0,this.canPreview=!0,this.urlParams={},this.omitExecutionHistoryColumns=!1,this.omitActionsColumn=!1,this.hasConfiguredItems=!0,this.canDisable=!1,this.hasNew=function(t){return t.createButtons.includes(e.scriptType)},this.renderShortDescription=function(e){return r.createElement("div",null,"Short desc")},this.canEdit=function(e){return!0},this.canDelete=function(e){return!0},this.onSuccess=function(e){return{}},this.onFormLoad=function(e){return{}},this.processConfiguredItems=function(e){return e},this.getSearchFiltersContainer=function(){return r.createElement(a.s,null,e.getSearchFiltersComponents())},this.getSearchFiltersComponents=function(){return[r.createElement(i.r,{key:"wordsSearchFilter",getItemName:function(e){return e.name},getItemNotes:function(e){return e.FIELD_NOTES}})]},this.renderLeftPanelDescription=function(e,t){return r.createElement(o.z,d({},e,{searchWords:t}),r.createElement(o.o,{value:e.FIELD_NOTES,searchWords:t}))}}return Object.defineProperty(e.prototype,"scriptType",{get:function(){return this.restUrl.split("/").pop()},enumerable:!1,configurable:!0}),e.prototype.shouldDisplayConfigOptions=function(e){return!0},e.prototype.renderConfigMenuItems=function(e,t){var n=this,o=e[this.uniqueIdKey];return[r.createElement(c.e,{key:"edit-action",label:"Edit",icon:r.createElement(s.Z,{label:"Edit",size:"small"}),isVisible:this.canEdit(e),onClick:function(){return t.navigateTo("".concat(n.rootHash,"/edit/").concat(o))},testId:"edit"}),r.createElement(c.e,{key:"enable-action",label:"Enable",icon:r.createElement(u.Z,{label:"Enable",size:"small"}),isVisible:this.canDisable&&!0===e.disabled,onClick:function(){return t.enableItem(o)},testId:"enable"}),r.createElement(c.e,{key:"disable-action",label:"Disable",icon:r.createElement("span",{className:"aui-icon aui-icon-small aui-iconfont-devtools-task-disabled"}),isVisible:this.canDisable&&!e.disabled,onClick:function(){return t.disableItem(o)},testId:"disable"}),r.createElement(c.e,{key:"delete-action",label:"Delete",icon:r.createElement(l.Z,{label:"Delete",size:"small"}),isVisible:this.canDelete(e),onClick:function(){return t.deleteItem(!0)},testId:"delete"})]},e}()},87333:(e,t,n)=>{"use strict";n.d(t,{GV:()=>g,ir:()=>w,mu:()=>y,rY:()=>d});var r=n(63844),o=n(2610),i=n(24478),a=n(45837),c=function(e){var t=e.children;return r.createElement("kbd",{style:{verticalAlign:"middle"}},t)},s=function(e,t){return{title:"Documentation",body:r.createElement("p",null,"Not sure how and when to use this feature? Need more information on what it can do for you? Check out our"," ",r.createElement("a",{href:(0,i.w7)(e||""),onClick:t,target:"_blank"},"documentation"),"."),analyticsId:"documentation"}},u={title:"Using Apache Groovy in ScriptRunner",body:r.createElement("p",null,"Use Apache Groovy to unlock the full potential of ScriptRunner. Check out some helpful"," ",r.createElement("a",{href:(0,i.w7)("best-practices/write-code/introduction-to-groovy"),target:"_blank"},"resources")," ","and become a Groovy pro."),analyticsId:"intro-to-groovy"},l={title:"Library",body:r.createElement("p",null,"To access a collection of tailor-made scripts to use in your ScriptRunner instance, visit the"," ",r.createElement("a",{href:"https://library.adaptavist.com/search?utm_source=product-help&apps=scriptrunner&platforms=data-center&products=".concat("jira"),target:"_blank"},"Adaptavist Library"),"."),analyticsId:"library"},d=function(e){return{title:"Code Insight",body:r.createElement(r.Fragment,null,r.createElement("p",null,"Use Code Insight features to simplify the process of writing scripts in ScriptRunner."),r.createElement("p",null,r.createElement("strong",null,"Completions")," - Press ",r.createElement(c,null,"Ctrl"),"+",r.createElement(c,null,"space")," to complete the current class, method, property, or variable."),"bamboo"!==e&&r.createElement(r.Fragment,null,r.createElement("p",null,r.createElement("strong",null,"Documentation")," - Press ",r.createElement(c,null,"Ctrl"),"+",r.createElement(c,null,"space")," again to show documentation where available. The documentation popup open automatically from that point, press"," ",r.createElement(c,null,"Ctrl"),"+",r.createElement(c,null,"space")," once more to close it."),r.createElement("p",null,"Hover over a method, property, or class, to open the Javadoc for this symbol.")),r.createElement("p",null,r.createElement("strong",null,"Smart completions")," - Press ",r.createElement(c,null,"Alt"),"+",r.createElement(c,null,"shift"),"+",r.createElement(c,null,"space")," to complete the method, property, or variable, respecting the expected type."),r.createElement("p",null,r.createElement("strong",null,"Parameter hints")," - Press ",r.createElement(c,null,"Ctrl"),"+",r.createElement(c,null,"shift"),"+",r.createElement(c,null,"space")," when inside method parameters to show the possible parameter types for a method."),r.createElement("p",null,r.createElement("strong",null,"Full screen")," - To open the editor in full screen, either use the"," ",r.createElement(o.Z,{size:"small",label:"Toggle Full Screen"})," icon, or press ",r.createElement(c,null,"F11")," when the focus is in the editor. To exit full screen, press ",r.createElement(c,null,"F11")," again or ",r.createElement("kbd",null,"Esc")," twice."),r.createElement("p",null,r.createElement("strong",null,"Other shortcuts")," - Press ",r.createElement(c,null,"F1")," with the focus in the editor to list all commands and keyboard shortcuts.")),analyticsId:"codeInsights"}},p={title:"Dynamic Forms",body:r.createElement("p",null,"Use Dynamic forms to create complex scripts with flexible variables that can be shared with multiple users, allowing one script to be used for various use cases. For more information, see our"," ",r.createElement("a",{href:(0,i.w7)("best-practices/write-code/dynamic-forms"),target:"_blank"},"Dynamic Forms documentation"),"."),analyticsId:"dynamicForms"},f={title:"Atlassian Community help",body:r.createElement("p",null,"If you have a question, the"," ",r.createElement("a",{href:"https://community.atlassian.com/",target:"_blank"},"Atlassian Community")," ","may also have answers for you!"),analyticsId:"atlassianCommunity"},m={title:"Custom JQL functions",body:r.createElement("p",null,"Do you have a very specific use case our built-in functions don't cover? If you're experienced with Groovy, you can create your own"," ",r.createElement("a",{href:(0,i.w7)("features/jql-functions/custom-jql-functions")},"Custom JQL Function"),"."),analyticsId:"customJqlFunctions"},h={title:"Universal Plugin Manager",body:r.createElement("p",null,"You can ",r.createElement("b",null,"disable")," or ",r.createElement("b",null,"enable")," functions from the Universal Plugin Manager. Open the"," ",r.createElement("a",{href:"".concat(AJS.contextPath(),"/plugins/servlet/upm?fragment=manage/com.onresolve.jira.groovy.groovyrunner")},"ScriptRunner plugin section"),", expand the modules, and select which ones to disable."),analyticsId:"universalPluginManager"},g=function(e,t){var n=[s(e,t),f],r=[s(e,t),m,h,f];return e===a.m?r:e?n:[]},y=function(e,t,n){var r=[s(t,n),u,l];return"jira"===e&&r.push(p),r.push(d(e)),r.push(f),r},v="parameterisedCheckedScriptTextOrFile",b=[v,"checkedScriptFile","checkedScriptTextOrFile"],w=function(e,t,n,r){var o=t.filter((function(e){return b.includes(e)}));if(0===o.length&&!n)return[];var i=n?[s(n,r)]:[];return o.length>0&&(i.push(u),i.push(l),o.includes(v)&&i.push(p),i.push(d(e))),n&&i.push(f),i}},46290:(e,t,n)=>{"use strict";n.d(t,{IT:()=>s,Tx:()=>a,fz:()=>c});var r=n(86530),o=n(53904),i=(0,r.ZP)(o.e),a=i("FLIP_EXPANDED"),c=i("NEXT_PAGE"),s=i("PREVIOUS_PAGE")},53904:(e,t,n)=>{"use strict";n.d(t,{e:()=>r});var r="hintsAndTips"},45837:(e,t,n)=>{"use strict";n.d(t,{Z:()=>d,m:()=>l});var r,o=n(63844),i=n(13061),a=n(77937),c=n(80515),s=n(40528),u=(r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},r(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),l="?contentKey=jql-functions";const d=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.restUrl="".concat(AJS.contextPath(),"/rest/scriptrunner-jira/latest/jqlfunctions"),t.canAddNew=!1,t.heading="JQL Functions",t.docUrl=l,t.functionIdKey=function(e){return e.id},t.preamble=function(e){var n=e.dispatch;return o.createElement("div",null,o.createElement("p",null,"These are ScriptRunner JQL Functions available in Basic and Advanced view in your instance. Here you can see how much each one of them is being used."),o.createElement("p",null,"If you have just added or removed a class for a script function, you can"," ",o.createElement("a",{style:{cursor:"pointer"},onClick:function(){return n((0,a.pD)(t))}},"scan")," ","for them, otherwise they will be automatically loaded at startup."))},t.uniqueIdKey="id",t.executeButtonValue="Update",t.rootHash="/jqlfunctions",t.omitExecutionHistoryColumns=!1,t.omitActionsColumn=!0,t.getSearchFiltersComponents=function(){return[o.createElement(s.r,{key:"wordsSearchFilter",getItemName:function(e){return e.id},getItemNotes:function(e){return e.description}})]},t.renderLeftPanelDescription=function(e,t){return o.createElement(c.z,{name:e.id,searchWords:t},o.createElement(c.o,{value:e.description,searchWords:t}))},t}return u(t,e),t}(i.Z)},80515:(e,t,n)=>{"use strict";n.d(t,{o:()=>i,z:()=>a});var r=n(63844),o=n(22106),i=function(e){return e.value?r.createElement(o,{searchWords:e.searchWords||[],autoEscape:!0,textToHighlight:e.value||""}):r.createElement("p",null,"No note")},a=function(e){return r.createElement("form",{className:"aui"},r.createElement("span",{className:"item-name"},r.createElement(o,{searchWords:e.searchWords||[],autoEscape:!0,textToHighlight:e.name||""}))," ",e.disabled&&r.createElement("i",{className:"item-disabled"},"(Disabled)"),r.createElement("br",null),r.createElement("div",{className:"description"},e.children))}},98583:(e,t,n)=>{"use strict";n.d(t,{T:()=>h,U:()=>g});var r=n(63844),o=n(49159),i=n(94194),a=n(3835),c=n(57700),s=n(87483),u=n(77510),l=n(76416),d=n(32248),p=n(58844),f=function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,i=n.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(r=i.next()).done;)a.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return a},m=function(e){var t,n=e.bindingInformation,o=Object.entries(null!==(t=n.bindingVariables)&&void 0!==t?t:[]),i=Object.entries(n.other);return r.createElement(r.Fragment,null,r.createElement("table",{className:"aui aui-table-list",style:{marginTop:15}},r.createElement("thead",null,r.createElement("tr",null,r.createElement("th",null,"Name"),r.createElement("th",null,"Type"),r.createElement("th",null,"Description"))),r.createElement("tbody",null,0===o.length?r.createElement("tr",null,r.createElement("td",{colSpan:3},"No binding variables available")):o.map((function(e){var t=f(e,2),n=t[0],o=t[1];return r.createElement("tr",{key:n},r.createElement("td",null,n),r.createElement("td",{dangerouslySetInnerHTML:{__html:(0,p.sanitize)(o[0])}}),r.createElement("td",{dangerouslySetInnerHTML:{__html:(0,p.sanitize)(o[1])}}))})))),i.length>0&&r.createElement(r.Fragment,null,r.createElement("h3",null,"Additional information"),r.createElement("table",{className:"aui aui-table-list"},r.createElement("tbody",null,i.map((function(e){var t=f(e,2),n=t[0],o=t[1];return r.createElement("tr",{key:n},r.createElement("td",null,n),r.createElement("td",null,o))}))))))},h=function(e){var t=e.isLoading,n=e.isError,o=e.bindingInformation;return t?r.createElement(l.LoadingSpinner,{size:"large"}):n?r.createElement(d.Z,{appearance:"error",title:"Error loading binding information"},"Please reload this page and try again."):o?r.createElement(r.Fragment,null,r.createElement("p",null,"The table below lists the variables that are automatically accessible within your script."),r.createElement(m,{bindingInformation:o})):null},g=function(e){var t=e.heading,n=e.isLoading,l=e.isError,d=e.bindingInformation,p=e.blanketHidden,f=e.onClose;return r.createElement(i.Z,{width:"x-large",autoFocus:!1,onClose:f,isBlanketHidden:p},r.createElement(a.Z,null,r.createElement(c.Z,null,t)),r.createElement(s.Z,null,r.createElement("div",{style:{marginTop:5,marginBottom:10}},r.createElement(h,{isLoading:n,isError:l,bindingInformation:d}))),r.createElement(u.Z,null,r.createElement(o.Z,{autoFocus:!0,appearance:"primary",onClick:f},"Close")))}},36754:(e,t,n)=>{"use strict";n.d(t,{T:()=>o,e:()=>i});var r=n(40653).B.injectEndpoints({endpoints:function(e){return{getBindingInformation:e.mutation({query:function(e){var t=e.compileContextDescriptor;return{url:"".concat(AJS.contextPath(),"/rest/scriptrunner/latest/diagnostics/binding"),method:"POST",body:t}}}),getElementBindingInformation:e.query({query:function(e){return{url:"".concat(AJS.contextPath(),"/rest/scriptrunner/latest/diagnostics/element"),params:{element:e}}}})}}}),o=r.useGetBindingInformationMutation,i=r.useGetElementBindingInformationQuery},70117:(e,t,n)=>{"use strict";n.d(t,{X4:()=>y,pj:()=>h,s:()=>g});var r,o=n(63844),i=n(72142),a=n(55882),c=n(49159),s=n(18390),u=n(34968),l=n(75337),d=function(e,t){return Object.defineProperty?Object.defineProperty(e,"raw",{value:t}):e.raw=t,e},p=function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,i=n.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(r=i.next()).done;)a.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return a},f=function(e,t,n){if(n||2===arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))},m=function(e,t,n){var r=this;this.execute=e,this.clearFilters=t,this.setFiltersAreApplied=n,this.filters=new Map,this.register=function(e,t,n,o){r.filters.set(e,{filter:t,clearFilter:n,isApplied:o}),r.setFiltersAreApplied(f([],p(r.filters.values()),!1).some((function(e){return e.isApplied()})))},this.doFilter=function(){return r.execute(f([],p(r.filters.values()),!1).map((function(e){return e.filter})))},this.clearFiltersHandler=function(){f([],p(r.filters.values()),!1).forEach((function(e){return e.clearFilter()})),r.clearFilters()}},h=o.createContext(null),g=(0,i.$j)((function(e){return{filtersStateId:e.itemsSearch.filtersStateId}}),(function(e){return{applyFilters:function(t){return e((0,a.O)(t))},clearFilters:function(){return e((0,a.K5)())}}}))((function(e){var t=p(o.useState(!1),2),n=t[0],r=t[1],i=p(o.useState(e.filtersStateId),2),a=i[0],s=i[1],d=p(o.useState(new m(e.applyFilters,e.clearFilters,r)),1)[0];return o.useEffect((function(){s(e.filtersStateId),d.doFilter()}),[a!==e.filtersStateId]),o.createElement("div",{style:{display:"flex",alignItems:"center"}},o.createElement(u.SV,{FallbackComponent:function(e){var t=e.error;return o.createElement(l.B,{error:t,featureName:"filtering",compact:!0})}},o.createElement(h.Provider,{value:d},e.children),Array.isArray(e.children)&&e.children.length>1&&o.createElement(c.Z,{style:{marginLeft:"10px",marginRight:"10px",marginTop:"4px",alignSelf:"flex-start",visibility:n?"visible":"hidden"},onClick:function(){return d.clearFiltersHandler()},appearance:"subtle",spacing:"compact"},"Clear all")))})),y=s.Z.div(r||(r=d(["\n    margin-left: 10px;\n    align-self: flex-start;\n    display: inline-block;\n    min-width: 220px;\n"],["\n    margin-left: 10px;\n    align-self: flex-start;\n    display: inline-block;\n    min-width: 220px;\n"])))},40528:(e,t,n)=>{"use strict";n.d(t,{r:()=>f});var r=n(63844),o=n(72142),i=n(55882),a=n(21996),c=n(70117),s=n(74703),u=n(3503),l=function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,i=n.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(r=i.next()).done;)a.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return a},d=function(e,t,n){if(n||2===arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))},p={searchString:"",searchWords:[]},f=(0,o.$j)((function(e){return{location:e.router.location.pathname}}),(function(e){return{updateSearchWords:function(t){return e((0,i.Nk)(t))}}}))((function(e){var t=e.getItemName,n=e.getItemNotes,o=e.updateSearchWords,i=e.location,s=r.useContext(c.pj),d=l(r.useState(p),2),f=d[0],h=d[1],y=g(f.searchString),v=function(){h(p),o([])};(0,r.useEffect)((function(){return s.register("wordsSearchFilter",m({words:y,getItemName:t,getItemNotes:n,location:i}),v,(function(){return!u.Z(y)}))}),[y]);return r.createElement(c.X4,null,r.createElement(a.Z,{value:f.searchString,placeholder:"Search",updateSearchString:function(e){return h({searchString:e,searchWords:f.searchWords})},onSearchQueryAccepted:function(){var e=g(f.searchString);f.searchWords.length===e.length&&e.every((function(e){return f.searchWords.includes(e)}))||(h({searchWords:e,searchString:f.searchString}),o(e),s.doFilter())},shouldFitContainer:!1,isCompact:!0}))})),m=function(e){return function(t){if(t){if(!e.words.length)return t;var n=e.words,r=e.getItemName,o=e.getItemNotes,i=e.location,a=h(n);return u.Z(a)||(0,s.w)("configItemsSearch",{params:{action:"wordsSearchFilter",totalConfigItemsNumber:t.length,location:i}}),t.filter((function(e){var t,n;return a("".concat(null!==(t=r(e))&&void 0!==t?t:""," ").concat(null!==(n=o(e))&&void 0!==n?n:""))}))}}},h=function(e){return function(t){return e.every((function(e){return t.toLowerCase().includes(e)}))}},g=function(e){return e.toLowerCase().split(" ").reduce((function(e,t){return""===t||e.includes(t)?e:d(d([],l(e),!1),[t],!1)}),[]).sort((function(e,t){return t.length-e.length}))}},55882:(e,t,n)=>{"use strict";n.d(t,{K5:()=>c,Nk:()=>s,O:()=>a});var r=n(86530),o=n(17249),i=(0,r.ZP)(o.e),a=i("APPLY_FILTERS"),c=i("CLEAR_FILTERS"),s=i("UPDATE_SEARCH_WORDS")},17249:(e,t,n)=>{"use strict";n.d(t,{e:()=>r});var r="itemsSearch"},4125:(e,t,n)=>{"use strict";n.d(t,{z:()=>u});var r,o,i=n(74729),a=(r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},r(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),c=function(e,t,n,r){return new(n||(n=Promise))((function(o,i){function a(e){try{s(r.next(e))}catch(e){i(e)}}function c(e){try{s(r.throw(e))}catch(e){i(e)}}function s(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(a,c)}s((r=r.apply(e,t||[])).next())}))},s=function(e,t){var n,r,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:c(0),throw:c(1),return:c(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function c(i){return function(c){return function(i){if(n)throw new TypeError("Generator is already executing.");for(;a;)try{if(n=1,r&&(o=2&i[0]?r.return:i[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,i[1])).done)return o;switch(r=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return a.label++,{value:i[1],done:!1};case 5:a.label++,r=i[1],i=[0];continue;case 7:i=a.ops.pop(),a.trys.pop();continue;default:if(!(o=a.trys,(o=o.length>0&&o[o.length-1])||6!==i[0]&&2!==i[0])){a=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){a.label=i[1];break}if(6===i[0]&&a.label<o[1]){a.label=o[1],o=i;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(i);break}o[2]&&a.ops.pop(),a.trys.pop();continue}i=t.call(e,a)}catch(e){i=[6,e],r=0}finally{n=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,c])}}};!function(e){e.success="success",e.fail="failure"}(o||(o={}));var u=function(){function e(){}return e.prototype.getPropertiesKeys=function(){return c(this,void 0,Promise,(function(){return s(this,(function(e){switch(e.label){case 0:return[4,(0,i.wrappedFetch)("".concat(AJS.contextPath(),"/rest/scriptrunner/latest/self/properties"))];case 1:return[2,e.sent().result||[]]}}))}))},e.prototype.setProperty=function(e,t){return c(this,void 0,Promise,(function(){var n;return s(this,(function(r){switch(r.label){case 0:return[4,(0,i.wrappedFetch)("".concat(AJS.contextPath(),"/rest/scriptrunner/latest/self/properties/").concat(e),{method:"PUT",body:JSON.stringify(t),headers:{"Content-Type":"text/plain"}})];case 1:return n=r.sent(),204===n.response.status?[2,t]:[2]}}))}))},e.prototype.mergeProperty=function(e,t){return c(this,void 0,Promise,(function(){var n,r;return s(this,(function(o){switch(o.label){case 0:return[4,(0,i.wrappedFetch)("".concat(AJS.contextPath(),"/rest/scriptrunner/latest/self/properties/merge/").concat(e),{method:"PUT",body:JSON.stringify(t),headers:{"Content-Type":"application/json"}})];case 1:return n=o.sent(),204!==(r=n.response.status)&&200!==r&&console.log("Failed to merge property",e,t),[2]}}))}))},e.prototype.getProperty=function(e){return c(this,void 0,Promise,(function(){var t;return s(this,(function(n){switch(n.label){case 0:return[4,(0,i.wrappedFetch)("".concat(AJS.contextPath(),"/rest/scriptrunner/latest/self/properties/").concat(e),{method:"GET"})];case 1:return t=n.sent(),200!==t.response.status?[2,void 0]:[2,JSON.parse(t.result)]}}))}))},e.prototype.deleteProperty=function(e){return c(this,void 0,Promise,(function(){var t;return s(this,(function(n){switch(n.label){case 0:return[4,(0,i.wrappedFetch)("".concat(AJS.contextPath(),"/rest/scriptrunner/latest/self/properties/").concat(e),{method:"DELETE"})];case 1:return t=n.sent(),204!==t.response.status?[2,o.fail]:[2,o.success]}}))}))},e}();!function(e){function t(t){var n=e.call(this)||this;return n.usersProperties=t,n}a(t,e),t.prototype.getPropertiesKeys=function(){var e=this;return new Promise((function(t){t(Object.keys(e.usersProperties).map((function(e){return e})))}))},t.prototype.setProperty=function(e,t){var n=this;return this.usersProperties[e]=t,new Promise((function(t){t(n.usersProperties[e])}))},t.prototype.mergeProperty=function(e,t){var n=this;return this.usersProperties[e]=t,new Promise((function(t){t(n.usersProperties[e])}))},t.prototype.getProperty=function(e){var t=this;return new Promise((function(n){n(t.usersProperties[e])}))},t.prototype.deleteProperty=function(e){var t=this;return new Promise((function(n,r){try{delete t.usersProperties[e],n(o.success)}catch(e){console.warn(e),r(o.fail)}}))}}(u)},62437:(e,t,n)=>{"use strict";n.d(t,{x:()=>l});var r,o=n(63844),i=n(74703),a=n(56420),c=(r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},r(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),s=function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,i=n.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(r=i.next()).done;)a.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return a},u=function(e,t,n){if(n||2===arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))},l=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.state={selectedNodeId:t.props.clusterNodeId,selectedDuration:"TWO_DAYS"},t}return c(t,e),t.prototype.render=function(){var e=this,t=this.props,n=t.clustered,r=t.clusterNodeId,c=t.clusterNodeIds,l=t.loading;return o.createElement("div",{style:{display:"flex",justifyContent:"space-around",marginTop:"20px"}},o.createElement("div",null,o.createElement("b",null,"Duration"),Object.entries(a.hE).map((function(t){var n=s(t,2),r=n[0],a=n[1],c=l;return o.createElement("div",{className:"radio",key:r,onClick:function(){c||((0,i.w)("performanceAction",{params:{action:"durationEdit"}}),e.setState({selectedDuration:r},(function(){return e.props.onStateChange(e.state)})))}},o.createElement("input",{className:"radio",type:"radio",name:r,disabled:c,onChange:function(){},checked:e.state.selectedDuration===r}),o.createElement("label",{htmlFor:r},a))}))),o.createElement("div",null,n&&o.createElement(o.Fragment,null,o.createElement("b",null,"Node"),u(u([],s(c),!1),["All nodes"],!1).map((function(t){return o.createElement("div",{className:"radio",key:t,"aria-disabled":l,onClick:function(){!l&&e.setState({selectedNodeId:t},(function(){return e.props.onStateChange(e.state)}))}},o.createElement("input",{className:"radio",type:"radio",name:t,disabled:l,onChange:function(){},checked:e.state.selectedNodeId===t}),o.createElement("label",{htmlFor:t},t," ",r===t?" (this node)":""))})))))},t}(o.Component)},29284:(e,t,n)=>{"use strict";n.d(t,{S:()=>g});var r=n(63844),o=n(7230),i=n(49159),a=n(81314),c=n(94194),s=n(3835),u=n(57700),l=n(87483),d=n(77510),p=n(44150),f=n(56420),m=n(74703),h=function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,i=n.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(r=i.next()).done;)a.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return a},g=function(e){var t=e.functionId,n=e.itemName,g=h(r.useState(!1),2),y=g[0],v=g[1],b=n?"'".concat(n,"' performance"):"Performance";return r.useEffect((function(){y&&(0,m.w)("performanceAction",{params:{action:"open"}})}),[y]),r.createElement(r.Fragment,null,r.createElement(o.Z,{content:"Performance",position:"right"},r.createElement(i.Z,{type:"button",iconBefore:r.createElement(p.Z,{label:"Performance"}),appearance:"subtle",onClick:function(){return v(!0)}})),r.createElement(a.Z,null,y&&r.createElement(c.Z,{width:800,onClose:function(){return v(!1)}},r.createElement(s.Z,null,r.createElement(u.Z,null,b)),r.createElement(l.Z,null,r.createElement(f.lC,{chartDuration:"TWO_DAYS",functionId:t})),r.createElement(d.Z,null,r.createElement(i.Z,{autoFocus:!0,appearance:"primary",onClick:function(){return v(!1)}},"Close")))))}},56420:(e,t,n)=>{"use strict";n.d(t,{hE:()=>m,lC:()=>y});var r,o=n(63844),i=n(39507),a=n(20371),c=n(62437),s=n(18390),u=n(31269),l=n(20678),d=function(e,t){return Object.defineProperty?Object.defineProperty(e,"raw",{value:t}):e.raw=t,e},p=function(){return p=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},p.apply(this,arguments)},f=function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,i=n.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(r=i.next()).done;)a.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return a},m={TWO_DAYS:"2 days",TWO_WEEKS:"2 weeks",TWO_MONTHS:"2 months",TWO_YEARS:"2 years"},h=function(e,t){switch(t.type){case"increment":return e+1;case"decrement":return e-1;default:throw new Error}},g=function(e){var t=e.clustered,n=e.clusterNodeIds,r=e.clusterNodeId,a=e.functionId,s=f((0,o.useState)("TWO_DAYS"),2),d=s[0],p=s[1],m=f((0,o.useState)(r),2),g=m[0],y=m[1],b=f((0,o.useReducer)(h,0),2),w=b[0],E=b[1],S=w>0,x={functionId:a,duration:d,nodeId:"All nodes"===g?null:g};return o.createElement("div",null,o.createElement(v,null,o.createElement(o.Fragment,null,["performance","success"].map((function(e){return o.createElement(u.L,{key:e,alt:"".concat((0,l.f)(e)," chart"),src:"".concat(AJS.contextPath(),"/rest/scriptrunner/latest/diagnostics/chart/").concat(e,"?").concat((0,i.F)(x)),onLoadStart:function(){return E({type:"increment"})},onLoadComplete:function(){return E({type:"decrement"})}})})))),o.createElement(c.x,{clustered:t,clusterNodeId:r,clusterNodeIds:n,loading:S,onStateChange:function(e){y(e.selectedNodeId),p(e.selectedDuration)}}))},y=function(e){return o.createElement(g,p({},e,{clusterNodeIds:a.E.clusterNodeIds,clustered:a.E.clustered,clusterNodeId:a.E.clusterNodeId}))},v=s.Z.div(r||(r=d(["\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n"],["\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n"])))},90917:(e,t,n)=>{"use strict";n.d(t,{P:()=>ne});var r,o,i,a,c=n(63844),s=n(31722),u=function(e){var t=e.length;if(0===t)return{iconClassName:"aui-iconfont-time",message:"Has not run yet"};var n=e.filter((function(e){return e.exception})).length;return n>0?n===t?{iconClassName:"aui-iconfont-time",iconColour:"#BF2600",message:"All of the last ".concat(t," executions have failed")}:t>0&&e[0].exception?{iconClassName:"aui-iconfont-time",iconColour:"#FF5630",message:"".concat(n," of ").concat(t," executions have failed, including the last one")}:{iconClassName:"aui-iconfont-time",iconColour:"orange",message:"".concat(n," of ").concat(t," executions have failed")}:{iconClassName:"aui-iconfont-successful-build",iconColour:"#00875A",message:"No failures in the last ".concat(t," execution(s)")}},l=n(74729),d=function(e,t,n,r){return new(n||(n=Promise))((function(o,i){function a(e){try{s(r.next(e))}catch(e){i(e)}}function c(e){try{s(r.throw(e))}catch(e){i(e)}}function s(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(a,c)}s((r=r.apply(e,t||[])).next())}))},p=function(e,t){var n,r,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:c(0),throw:c(1),return:c(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function c(i){return function(c){return function(i){if(n)throw new TypeError("Generator is already executing.");for(;a;)try{if(n=1,r&&(o=2&i[0]?r.return:i[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,i[1])).done)return o;switch(r=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return a.label++,{value:i[1],done:!1};case 5:a.label++,r=i[1],i=[0];continue;case 7:i=a.ops.pop(),a.trys.pop();continue;default:if(!(o=a.trys,(o=o.length>0&&o[o.length-1])||6!==i[0]&&2!==i[0])){a=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){a.label=i[1];break}if(6===i[0]&&a.label<o[1]){a.label=o[1],o=i;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(i);break}o[2]&&a.ops.pop(),a.trys.pop();continue}i=t.call(e,a)}catch(e){i=[6,e],r=0}finally{n=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,c])}}},f=function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,i=n.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(r=i.next()).done;)a.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return a},m=function(e){return d(void 0,void 0,Promise,(function(){var t,n,r;return p(this,(function(o){switch(o.label){case 0:return[4,(0,l.wrappedFetch)("".concat(AJS.contextPath(),"/rest/scriptrunner/latest/diagnostics/results?functionId=").concat(e))];case 1:if(t=o.sent(),n=t.result,r=t.error,n)return[2,n.reverse()];throw new Error(r)}}))}))},h=function(e){var t=f((0,c.useState)(!0),2),n=t[0],r=t[1],o=f((0,c.useState)(),2),i=o[0],a=o[1],s=f((0,c.useState)([]),2),l=s[0],h=s[1];return(0,c.useEffect)((function(){(function(e){return d(void 0,void 0,void 0,(function(){var t,n;return p(this,(function(o){switch(o.label){case 0:return o.trys.push([0,2,3,4]),t=h,[4,m(e)];case 1:return t.apply(void 0,[o.sent()]),[3,4];case 2:return n=o.sent(),a(n),[3,4];case 3:return r(!1),[7];case 4:return[2]}}))}))})(e).catch(console.warn)}),[e]),i?{error:i,loading:n}:{loading:n,error:i,executionResults:l,executionSummary:u(l)}},g=n(78417),y=n(49159),v=function(e){var t=e.executionSummary,n=e.onClick,r=e.disabled,o=t.message,i=t.iconClassName,a=t.iconColour;return c.createElement("div",null,c.createElement(y.Z,{onClick:n,appearance:"link",iconBefore:c.createElement("span",{style:{color:a,marginRight:5},className:"aui-icon aui-icon-small ".concat(i)}),isDisabled:r,spacing:"none"},o))},b=n(81314),w=n(94194),E=n(87483),S=n(77510),x=n(57700),P=n(78941),I=n(79822),C=n(26841),T=n(35541),O=function(){return O=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},O.apply(this,arguments)},k=function(e){var t=e.data,n=e.elementProps,r=e.isSelected,o=e.innerRef,i=t;return c.createElement(I.LY,O({},n,{innerRef:o,status:r?"selected":"normal"}),i.exception?c.createElement(C.Z,{label:"Error",primaryColor:"#DE350B"}):c.createElement(T.Z,{label:"Success",primaryColor:"#00875A"}),r&&c.createElement(I.Uj,{status:"selected"}))},A=n(18390),_=function(e,t){return Object.defineProperty?Object.defineProperty(e,"raw",{value:t}):e.raw=t,e},D=function(e){var t=e.titleText,n=e.results,r=e.selectedResult,o=e.setSelectedResult;return c.createElement(N,null,c.createElement(x.Z,null,t),c.createElement(F,null,c.createElement(P.Z,{tabs:n,selected:r,components:{Item:k},onSelect:function(e){return o(e)}})))},N=A.Z.div(r||(r=_(["\n    margin: 24px 24px 0 24px;\n"],["\n    margin: 24px 24px 0 24px;\n"]))),F=A.Z.div(o||(o=_(["\n    margin-top: 24px;\n"],["\n    margin-top: 24px;\n"]))),L=n(47424),R=n(79289),j=n(35244),M=function(e){var t=e.result.log||"No logs were found for this execution.";return c.createElement(L.f,{heading:"Logs",headingButton:c.createElement(j.q,{textToCopy:t}),text:t,language:"text",description:c.createElement("p",null,"The following log entries were produced by this script execution. Use statements like"," ",c.createElement(R.Z,{language:"groovy",text:"log.warn('...')"})," in your script (depending on your logging configuration) to record logging information.")})},U=n(82633),Z=n.n(U),W=function(e){var t=e.result;return c.createElement(L.f,{heading:"Payload",text:Z()(t.payload,{quoteProperties:!0}),language:"json",description:c.createElement("p",null,"Payload represents the runtime binding variables provided to the executed script.")})},q=function(e){var t=e.result;return c.createElement(L.f,{heading:"Timing",text:"Elapsed: ".concat(t.millisecondsTaken," ms\nCPU time: ").concat(Math.round(t.cpuTime/1e6)," ms"),language:"text",description:c.createElement("p",null,"Timing information related to this script execution. ",c.createElement("strong",null,"Elapsed"),' measures the real world or "wall clock" duration of the execution whereas ',c.createElement("strong",null,"CPU time")," measures the amount of time the CPU was busy.")})},G=n(74046),V=function(e){var t=e.result,n="".concat(new Date(t.created));return c.createElement(G.Q,{heading:"Time",description:c.createElement("p",null,"The time this was executed (server timezone).")},n)},H=n(74703),J=function(e,t){return Object.defineProperty?Object.defineProperty(e,"raw",{value:t}):e.raw=t,e},B=function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,i=n.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(r=i.next()).done;)a.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return a},z=function(e){var t=e.executionResults,n=e.isOpen,r=e.setOpen,o=e.itemName,i=o?"'".concat(o,"' execution history"):"Execution history",a=B((0,c.useState)(t[0]),2),s=a[0],u=a[1],l=function(){r(!1),u(t[0])},d=function(e){(0,H.w)("executionHistoryAction",{params:{action:"tabSelection"}}),u(e)};return c.createElement(b.Z,null,n&&c.createElement(w.Z,{autoFocus:!1,width:"60%",onClose:l},c.createElement(D,{titleText:i,results:t,selectedResult:s,setSelectedResult:d}),c.createElement(E.Z,null,c.createElement(K,null,c.createElement(V,{result:s}),c.createElement(M,{result:s}),c.createElement(W,{result:s}),c.createElement(q,{result:s}))),c.createElement(S.Z,null,c.createElement(y.Z,{appearance:"subtle",onClick:l},"Close"))))},K=A.Z.div(i||(i=J(["\n    margin-bottom: 10px;\n"],["\n    margin-bottom: 10px;\n"]))),$=n(21705),Y=function(e,t){return Object.defineProperty?Object.defineProperty(e,"raw",{value:t}):e.raw=t,e},Q=A.Z.div(a||(a=Y(["\n    display: flex;\n    align-items: center;\n"],["\n    display: flex;\n    align-items: center;\n"]))),X=function(){return c.createElement(Q,null,c.createElement($.Z,{label:"Error"}),"Error loading execution history")},ee=function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,i=n.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(r=i.next()).done;)a.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return a},te=function(e){var t=e.functionId,n=e.itemName,r=h(t),o=r.loading,i=r.error,a=r.executionResults,s=r.executionSummary,u=ee((0,c.useState)(!1),2),l=u[0],d=u[1];return(0,c.useEffect)((function(){l&&(0,H.w)("executionHistoryAction",{params:{action:"open"}})}),[l]),c.createElement("div",{className:"execution-history"},i&&c.createElement(X,null),o&&c.createElement("div",{className:"sr-progress-indicator"},c.createElement(g.Z,null)),!o&&!i&&c.createElement(c.Fragment,null,c.createElement(v,{executionSummary:s,onClick:function(){return d(!0)},disabled:0===a.length}),c.createElement(z,{isOpen:l,setOpen:d,executionResults:a,itemName:n})))},ne=function(e){var t=e.functionId,n=e.itemName;return c.createElement("div",{style:{minWidth:160}},c.createElement(s.df,{triggerOnce:!0},(function(e){var r=e.inView,o=e.ref;return c.createElement("div",{ref:o},r&&c.createElement(te,{functionId:t,itemName:n}))})))}},3845:(e,t,n)=>{"use strict";n.d(t,{f:()=>l,i:()=>d});var r,o=n(64929),i=n(50770),a=n(97509),c=n(66638),s=function(){try{var e=function(){var e=n(93257),t={};try{t.projectState=e.getProject()}catch(e){}try{t.pullRequestState=e.getPullRequest()}catch(e){}try{t.repositoryState=e.getRepository()}catch(e){}return t}(),t=e.repositoryState,r=e.projectState;return{repo:t&&t.slug,repositoryId:t&&t.id,project:r&&r.key,projectId:r&&r.id,allLevels:void 0===(null==r?void 0:r.id)&&void 0===(null==t?void 0:t.id)||void 0,allInProject:void 0!==(null==r?void 0:r.id)&&void 0===(null==t?void 0:t.id)||void 0}}catch(e){return{}}},u=AJS.$,l=function(){var e=new URLSearchParams(window.location.search).get("key");return(0,c.S)({confluence:e?{project:e}:{},bamboo:a.Z.collectExtraParams(),bitbucket:s(),default:u("#admin-screens").data("context")})},d=function(){if(r)return r;if(a.Z.isBuildConfigAction())return r=(0,o.PP)();var e,t=i.Z.isWorkflows()||i.Z.isTests()?(0,o.q_)():(0,o.lX)({basename:(["(".concat(AJS.contextPath(),"/plugins/servlet/scriptrunner/admin)"),"(".concat(AJS.contextPath(),"/plugins/servlet/scriptrunner/projectadmin/.*?)/"),"(".concat(AJS.contextPath(),"/plugins/servlet/scriptrunner/repoadmin/.*?/.*?)/")].find((function(t){var n=document.location.pathname.match(t);return!(!n||!n.length||(e=n[1],0))})),e)});return r=t}},57196:(e,t,n)=>{"use strict";n.d(t,{z:()=>g,g:()=>h});var r=n(86530),o=n(96355),i=n(25880),a=n(4125),c=n(74729),s=function(e,t,n,r){return new(n||(n=Promise))((function(o,i){function a(e){try{s(r.next(e))}catch(e){i(e)}}function c(e){try{s(r.throw(e))}catch(e){i(e)}}function s(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(a,c)}s((r=r.apply(e,t||[])).next())}))},u=function(e,t){var n,r,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:c(0),throw:c(1),return:c(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function c(i){return function(c){return function(i){if(n)throw new TypeError("Generator is already executing.");for(;a;)try{if(n=1,r&&(o=2&i[0]?r.return:i[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,i[1])).done)return o;switch(r=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return a.label++,{value:i[1],done:!1};case 5:a.label++,r=i[1],i=[0];continue;case 7:i=a.ops.pop(),a.trys.pop();continue;default:if(!(o=a.trys,(o=o.length>0&&o[o.length-1])||6!==i[0]&&2!==i[0])){a=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){a.label=i[1];break}if(6===i[0]&&a.label<o[1]){a.label=o[1],o=i;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(i);break}o[2]&&a.ops.pop(),a.trys.pop();continue}i=t.call(e,a)}catch(e){i=[6,e],r=0}finally{n=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,c])}}},l="newFeatureDiscovery",d=function(){function e(){this.propertiesRepository=new a.z,this.seenFeatures=[]}return e.prototype.getNewFeatures=function(){return s(this,void 0,Promise,(function(){var e,t;return u(this,(function(n){switch(n.label){case 0:return e=this,[4,this.propertiesRepository.getProperty(l)];case 1:return e.seenFeatures=n.sent(),this.seenFeatures||(this.seenFeatures=[]),[4,this.getInstanceNewFeatures()];case 2:return t=n.sent(),[2,i.Z(this.seenFeatures,t)]}}))}))},e.prototype.setFeatureSeen=function(e){return s(this,void 0,void 0,(function(){return u(this,(function(t){return this.seenFeatures.push(e),[2,this.propertiesRepository.setProperty(l,this.seenFeatures)]}))}))},e.prototype.getInstanceNewFeatures=function(){return s(this,void 0,Promise,(function(){var e;return u(this,(function(t){switch(t.label){case 0:return[4,(0,c.wrappedFetch)("".concat(AJS.contextPath(),"/rest/scriptrunner/1.0/features/new"))];case 1:return(e=t.sent()).error?[2,{}]:[2,e.result]}}))}))},e}(),p=(0,r.ZP)("featureDiscovery"),f=(0,o.ZP)(p),m=new d,h=f("INIT_FEATURE_DISCOVERY",(function(){return m.getNewFeatures()})),g=f("HIDE_SCRIPT_DISCOVERY_BADGE",(function(e){return m.setFeatureSeen(e.class).then((function(e){}))}))},58376:(e,t,n)=>{"use strict";n.d(t,{jK:()=>v,qA:()=>y});var r=n(87890),o=n(25880),i=n(17005),a=n(7120),c=n(14500),s=n(6826),u=n(66494),l=n(57196),d=function(){return d=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},d.apply(this,arguments)},p=function(e,t){var n=e.indexOf(t);return n>-1?r.Z(n,n+1,e):e},f=function(e,t){return void 0===t&&(t=[]),t.reduce(p,e)},m=function(e){switch(e){case"BUILT_IN_SCRIPT":return"builtin";case"LISTENER":return"listeners";case"SCRIPT_FILED":return"scriptfields";case"REST_ENDPOINT":return"restendpoints";case"FRAGMENT":return"fragments";case"JOB":return"jobs";case"JQL_FUNCTION":return"jqlfunctions";case"RESOURCE":return"resources";case"BROWSE_PAGE":return"browse"}},h=i.Z(a.Z,c.Z(m)),g={sections:[],createButtons:[],scripts:{}},y={moduleName:"featureDiscovery",reducer:(0,u.reducerWithInitialState)(g).case(l.g.async.done,(function(e,t){var n=t.result,r=Object.values(n),o=h(r),i=s.Z(Object.keys(n),r.map((function(e){return e.map(m)})));return d(d({},e),{scripts:i,sections:o.filter((function(e){return"browse"===e})),createButtons:o})})).case(l.z.async.started,(function(e,t){var n=e.scripts[t.class];return{scripts:o.Z([t.class],e.scripts),sections:f(e.sections,n),createButtons:f(e.createButtons,n)}}))},v=function(e){return e.featureDiscovery||g}},1026:(e,t,n)=>{"use strict";n.d(t,{eG:()=>u,eU:()=>l,tw:()=>s});var r=n(66638),o=function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,i=n.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(r=i.next()).done;)a.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return a},i=function(e,t,n){if(n||2===arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))},a=["LISTENERS_MODAL","CONSOLE_MODAL","JOBS_MODAL","RESOURCES_MODAL","FRAGMENTS_MODAL","BUILTIN_SCRIPTS_MODAL","REST_ENDPOINTS_MODAL","REST_ENDPOINTS_CONFIG","NAVIGATION_SPOTLIGHT","LISTENERS_CONFIG","JOBS_CONFIG","FRAGMENTS_CONFIG","HOME_PAGE"],c=i(i([],o(a),!1),["MACROS_MODAL","MACRO_SPOTLIGHT","MACROS_CONFIG"],!1),s=function(){return(0,r.S)({default:a,confluence:c})},u=function(){return d("DISMISSED")},l=function(){return d("UNVISITED")},d=function(e){var t=s(),n=t.filter((function(e){return e.endsWith("_CONFIG")})),a=(0,r.S)({default:n,confluence:i(i([],o(n),!1),["MACRO_SPOTLIGHT"],!1)});return Object.fromEntries(t.map((function(t){return function(e,t){return void 0===t&&(t=[]),i(["NAVIGATION_SPOTLIGHT","HOME_PAGE"],o(t),!1).includes(e)}(t,a)?[t,"UNVISITED"]:[t,e]})))}},3114:(e,t,n)=>{"use strict";n.d(t,{$f:()=>c,Pq:()=>s});var r=n(4125),o=function(){return o=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},o.apply(this,arguments)},i=function(e,t,n,r){return new(n||(n=Promise))((function(o,i){function a(e){try{s(r.next(e))}catch(e){i(e)}}function c(e){try{s(r.throw(e))}catch(e){i(e)}}function s(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(a,c)}s((r=r.apply(e,t||[])).next())}))},a=function(e,t){var n,r,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:c(0),throw:c(1),return:c(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function c(i){return function(c){return function(i){if(n)throw new TypeError("Generator is already executing.");for(;a;)try{if(n=1,r&&(o=2&i[0]?r.return:i[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,i[1])).done)return o;switch(r=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return a.label++,{value:i[1],done:!1};case 5:a.label++,r=i[1],i=[0];continue;case 7:i=a.ops.pop(),a.trys.pop();continue;default:if(!(o=a.trys,(o=o.length>0&&o[o.length-1])||6!==i[0]&&2!==i[0])){a=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){a.label=i[1];break}if(6===i[0]&&a.label<o[1]){a.label=o[1],o=i;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(i);break}o[2]&&a.ops.pop(),a.trys.pop();continue}i=t.call(e,a)}catch(e){i=[6,e],r=0}finally{n=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,c])}}},c="scriptrunner_onboarding",s=function(){function e(e){void 0===e&&(e=new r.z),this.userPropertiesRepository=e}return e.prototype.getStoredModules=function(){return i(this,void 0,Promise,(function(){return a(this,(function(e){return[2,this.userPropertiesRepository.getProperty(c)]}))}))},e.prototype.updateModule=function(e,t){return i(this,void 0,void 0,(function(){var n,r;return a(this,(function(i){switch(i.label){case 0:return(r={})[e]=t,n=r,[4,this.userPropertiesRepository.mergeProperty(c,o({},n))];case 1:return i.sent(),[2,n]}}))}))},e}()},89180:(e,t,n)=>{"use strict";n.d(t,{w:()=>E});var r,o,i,a,c,s,u,l,d=n(63844),p=n(58844),f=n(18390),m=n(49159),h=n(81314),g=n(94194),y=n(87483),v=n(52369),b=function(e,t){return Object.defineProperty?Object.defineProperty(e,"raw",{value:t}):e.raw=t,e},w=function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,i=n.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(r=i.next()).done;)a.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return a},E=function(e){var t=e.pages,n=e.shouldCloseOnEscapePress,r=void 0!==n&&n,o=e.shouldCloseOnOverlayClick,i=void 0!==o&&o,a=e.testId,c=e.setModuleDismissed,s=e.setModuleCompleted,u=e.trackModalAction,l=e.onClose,f=e.onNextPage,b=w((0,d.useState)(0),2),E=b[0],A=b[1],_=t[E],D=0===E,N=E===t.length-1;return d.createElement(h.Z,null,d.createElement(g.Z,{width:"large",shouldScrollInViewport:!0,shouldCloseOnEscapePress:r,shouldCloseOnOverlayClick:i,onClose:function(){return l(E)},testId:a},d.createElement(y.Z,null,d.createElement("img",{src:_.image,width:"100%",alt:"Step ".concat(E+1)}),d.createElement(S,null,d.createElement(x,null,_.headerText),d.createElement(P,{dangerouslySetInnerHTML:{__html:(0,p.sanitize)(_.bodyText)}}),d.createElement(I,null,1!==t.length&&d.createElement(d.Fragment,null,d.createElement(O,null,d.createElement(k,{shouldRender:D},d.createElement(m.Z,{appearance:"subtle",onClick:function(){return c(E)}},"Dismiss"))),d.createElement(T,null,d.createElement(v.Z,{selectedIndex:E,values:t.map((function(e){return e.headerText})),appearance:"primary",onSelect:function(e){A(e.index),null==u||u("progress_dot",e.index)}}))),N?d.createElement(C,null,d.createElement(m.Z,{autoFocus:!0,appearance:"primary",onClick:function(){return s(E)}},"Done")):d.createElement(C,null,d.createElement(m.Z,{autoFocus:!0,appearance:"primary",onClick:function(){null==f||f(E),A(E+1),null==u||u("next",E)}},"Next")))))))},S=f.Z.div(r||(r=b(["\n    padding: 0px 44px 36px;\n    text-align: center;\n"],["\n    padding: 0px 44px 36px;\n    text-align: center;\n"]))),x=f.Z.h4(o||(o=b(["\n    margin-bottom: 30px;\n    color: inherit;\n    font-size: 20px;\n    font-style: inherit;\n    font-weight: 500;\n    letter-spacing: -0.008em;\n    line-height: 1.2px;\n"],["\n    margin-bottom: 30px;\n    color: inherit;\n    font-size: 20px;\n    font-style: inherit;\n    font-weight: 500;\n    letter-spacing: -0.008em;\n    line-height: 1.2px;\n"]))),P=f.Z.p(i||(i=b(["\n    margin-bottom: 40px;\n"],["\n    margin-bottom: 40px;\n"]))),I=f.Z.div(a||(a=b(["\n    display: grid;\n    grid-template-columns: repeat(3, 1fr);\n    justify-content: space-between;\n    align-items: center;\n"],["\n    display: grid;\n    grid-template-columns: repeat(3, 1fr);\n    justify-content: space-between;\n    align-items: center;\n"]))),C=f.Z.div(c||(c=b(["\n    justify-self: end;\n    grid-column: 3;\n"],["\n    justify-self: end;\n    grid-column: 3;\n"]))),T=f.Z.div(s||(s=b(["\n    grid-column: 2;\n"],["\n    grid-column: 2;\n"]))),O=f.Z.div(u||(u=b(["\n    justify-self: start;\n    grid-column: 1;\n"],["\n    justify-self: start;\n    grid-column: 1;\n"]))),k=f.Z.div(l||(l=b(["\n    visibility: ",";\n"],["\n    visibility: ",";\n"])),(function(e){return e.shouldRender?"visible":"collapse"}))},49268:(e,t,n)=>{"use strict";n.d(t,{XF:()=>v,at:()=>w,gX:()=>b,i2:()=>E,o4:()=>y});var r=n(86530),o=n(96355),i=n(3114),a=n(77937),c=n(42213),s=n(50382),u=n(71530),l=function(){return l=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},l.apply(this,arguments)},d=function(e,t,n,r){return new(n||(n=Promise))((function(o,i){function a(e){try{s(r.next(e))}catch(e){i(e)}}function c(e){try{s(r.throw(e))}catch(e){i(e)}}function s(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(a,c)}s((r=r.apply(e,t||[])).next())}))},p=function(e,t){var n,r,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:c(0),throw:c(1),return:c(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function c(i){return function(c){return function(i){if(n)throw new TypeError("Generator is already executing.");for(;a;)try{if(n=1,r&&(o=2&i[0]?r.return:i[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,i[1])).done)return o;switch(r=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return a.label++,{value:i[1],done:!1};case 5:a.label++,r=i[1],i=[0];continue;case 7:i=a.ops.pop(),a.trys.pop();continue;default:if(!(o=a.trys,(o=o.length>0&&o[o.length-1])||6!==i[0]&&2!==i[0])){a=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){a.label=i[1];break}if(6===i[0]&&a.label<o[1]){a.label=o[1],o=i;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(i);break}o[2]&&a.ops.pop(),a.trys.pop();continue}i=t.call(e,a)}catch(e){i=[6,e],r=0}finally{n=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,c])}}},f=function(e){var t="function"==typeof Symbol&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},m=(0,r.ZP)(i.$f),h=(0,o.ZP)(m),g=new i.Pq,y=h("SET_MODULE_COMPLETED",(function(e,t,n){var r=e.moduleType;return d(void 0,void 0,void 0,(function(){return p(this,(function(e){return""!==n().router.location.search&&t((0,u.VF)({search:""})),[2,g.updateModule(r,"COMPLETED")]}))}))})),v=h("SET_MODULE_DISMISSED",(function(e,t){var n=e.moduleType;return d(void 0,void 0,void 0,(function(){return p(this,(function(e){return t((0,u.VF)({search:""})),[2,g.updateModule(n,"DISMISSED")]}))}))})),b=h("SET_MODULE_UNVISITED",(function(e){return g.updateModule(e,"UNVISITED")})),w=h("SHOULD_RENDER_ONBOARDING",(function(e){return e})),E=h("POST_CONFIGURE_EXAMPLES",(function(e,t,n){return d(void 0,void 0,void 0,(function(){var r,o,i,u,d,m,h,g,v,b,w;return p(this,(function(p){switch(p.label){case 0:p.trys.push([0,5,6,7]),r=f(e.examples),o=r.next(),p.label=1;case 1:return o.done?[3,4]:(i=o.value,u=l(l({},i.params),{"canned-script":i.scriptName,FIELD_NOTES:"Edit before use - ".concat(i.description),disabled:!0}),[4,(0,a.ER)(e.config,i.scriptName,u,null)]);case 2:if(!(d=p.sent()).response.ok)throw m=null===(w=d.errorResult)||void 0===w?void 0:w.errorMessages,h=void 0===m||m.empty?"":m[0],(0,a.Sj)('There was a problem configuring "'.concat(i.description,'." <i>').concat(h,"</i>\n                    If the problem persists, please contact ")+"<a href='".concat(c.M,"' target='_blank'>support</a>.")),new Error("Server error: ".concat(d.response.status," ").concat(h));p.label=3;case 3:return o=r.next(),[3,1];case 4:return[3,7];case 5:return g=p.sent(),v={error:g},[3,7];case 6:try{o&&!o.done&&(b=r.return)&&b.call(r)}finally{if(v)throw v.error}return[7];case 7:return[4,t(y({moduleType:e.moduleType}))];case 8:return p.sent(),[2,t(s.j.configured.initiate(e.config,n().context,{forceRefetch:!0}))]}}))}))}))},83635:(e,t,n)=>{"use strict";n.d(t,{Bl:()=>y,E4:()=>u,Hj:()=>f,XO:()=>d,a4:()=>g,cf:()=>m,kt:()=>h});var r=n(67609),o=n(86530),i=n(66494),a=n(79949),c=function(){return c=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},c.apply(this,arguments)},s=function(e,t){return l.indexOf(e)>l.indexOf(t)},u=function(e){return"".concat(e.name,"_active:").concat(e.active,"_draft:").concat(e.draft)},l=["green","amber","red"],d=(0,o.ZP)("REGISTRY")("UPATE_RAG"),p=(0,i.reducerWithInitialState)({workflows:"green",listeners:"green",fields:"green",endpoints:"green",behaviours:"green",fragments:"green",jobs:"green"}).case(d,(function(e,t){var n,r=l.indexOf(e[t.tab]);return l.indexOf(t.rag)>r?c(c({},e),((n={})[t.tab]=t.rag,n)):c({},e)})),f=(0,a.oM)({name:"scriptRegistry",initialState:!1,reducers:{setRegistryFilter:function(e,t){var n;return null!==(n=t.payload)&&void 0!==n&&n}}}),m=(0,a.oM)({name:"scriptRegistry",initialState:!1,reducers:{setInitialCheckCompleted:function(e,t){var n;return null!==(n=t.payload)&&void 0!==n&&n}}}),h=(0,a.oM)({name:"scriptRegistry",initialState:{workflows:{},listeners:{},fields:{},endpoints:{},behaviours:{},fragments:{},jobs:{}},reducers:{updateConfiguredItemRag:function(e,t){var n=t.payload,r=n.rag,o=n.configuredItemId,i=n.tab;e[i][o]?"red"!==e[i][o].rag&&s(r,e[i][o].rag)&&(e[i][o].rag=r):e[i][o]={rag:r}}}}),g=(0,a.oM)({name:"scriptRegistry",initialState:{},reducers:{updateWorkflowStatus:function(e,t){var n,r=t.payload,o=r.workflow,i=r.rag,a=r.transition,c=u(o);e[c]=null!==(n=e[c])&&void 0!==n?n:{},e[c][a.name]?"red"!==e[c][a.name].rag&&s(i,e[c][a.name].rag)&&(e[c][a.name].rag=i):e[c][a.name]={rag:i}}}}),y=(0,r.UY)({rag:p,isFiltered:f.reducer,initialCheckCompleted:m.reducer,ragByObject:h.reducer,workflowStatus:g.reducer})},22909:(e,t,n)=>{"use strict";n.d(t,{I:()=>o,Q:()=>r});var r=500,o=165},8163:(e,t,n)=>{"use strict";n.d(t,{G:()=>i,Z:()=>o});var r=(0,n(86530).ZP)("annotatedScriptParameters"),o=r("UPDATE_ANNOTATIONS"),i=r("UPDATE_EDITOR_MODE")},13033:(e,t,n)=>{"use strict";n.d(t,{I6:()=>p,e1:()=>l,eE:()=>h,oD:()=>m});var r=n(66494),o=n(8163),i=n(59482),a=n(79949),c=n(37591),s=n(50382),u=function(){return u=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},u.apply(this,arguments)},l="annotatedScriptParameters",d=(0,a.Lq)({},(function(e){e.addMatcher(s.j.configured.matchFulfilled,(function(){return{}}))})),p=(0,r.reducerWithInitialState)({}).case(o.Z,(function(e,t){var n,r=t.editorId,o=t.annotations;return u(u({},e),((n={})[r]=u(u({},e[r]),{enabled:o.known,parameters:o.metadata}),n))})).case(i.VN.async.done,(function(e,t){var n,r=t.params,o=t.result,i=r.editorId,a=o.compilationResult.parameters;return u(u({},e),((n={})[i]=u(u({},e[i]),{enabled:a.known,parameters:a.metadata}),n))})).case(o.G,(function(e,t){var n;return u(u({},e),((n={})[t.fieldName]=u(u({},e[t.fieldName]),{editorMode:t.editorMode}),n))})).default(d),f=function(e){return e[l]},m=function(e){return(0,c.P1)(f,(function(t){return t[e]||{}}))},h=function(e){return(0,c.P1)(f,(function(t){var n,r,o;return(null!==(o=null===(r=null===(n=t[e])||void 0===n?void 0:n.parameters)||void 0===r?void 0:r.length)&&void 0!==o?o:0)>0}))}},87094:(e,t,n)=>{"use strict";n.d(t,{ZP:()=>Ce,eP:()=>fe,r_:()=>Ie,Cq:()=>xe});var r,o,i=n(79949),a=n(37591),c=n(67609),s=n(66494),u=n(91479),l=n(77937),d=n(73167),p=n.n(d),f=n(4248),m=n(65171),h=n(79820),g=n(96119),y=n(51372),v=(0,c.UY)(((r={})[m.Ab.name]=m.Ab.reducer,r[h.ac.name]=h.ac.reducer,r[g.jb.name]=g.jb.reducer,r)),b=n(66638),w=n(83051),E=n(50382),S=((0,b.S)({jira:function(e){}}),(0,w.GV)((function(e){e.addMatcher(E.j.configured.matchRejected,(function(e,t){if(t.payload){var n=t.payload.status;404===n?e.flags.push({body:"An error occurred, try refreshing the page.",type:"error",options:{title:"Not found",close:"manual"}}):401===n?e.flags.push({body:"You are not logged in as an administrator. Try refreshing the page.",type:"error",options:{title:"Permission denied",close:"manual"}}):e.flags.push({body:"An error occurred. Please review server logs.",type:"error",options:{title:"Server Error",close:"manual"}})}}))}))),x=n(13033),P=n(59482),I=n(3845),C=n(58376),T=n(60562),O=n(83635),k=n(76529),A=n(47680),_=n(53904),D=n(46290),N=n(7948),F=(0,s.reducerWithInitialState)(!1).case(D.Tx,(function(e){return!e})).case(N.cT,(function(){return!1})),L=(0,s.reducerWithInitialState)(0).case(D.fz,(function(e){return e+1})).case(D.IT,(function(e){return e-1})).case(N.cT,(function(){return 0})),R=(0,c.UY)({expanded:F,activePage:L}),j=n(17249),M=n(10746),U=n(55882),Z=n(91845),W=function(){return W=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},W.apply(this,arguments)},q=function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,i=n.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(r=i.next()).done;)a.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return a},G=function(e,t,n){if(n||2===arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))},V=function(e){return{location:"",searchWords:[],itemsOrig:{},itemsFiltered:[],filtersStateId:e}},H=(0,i.Lq)(V(0),(function(e){e.addCase(N.cT,(function(e,t){var n=t.payload.location.pathname;e.location=n,e.itemsFiltered=(0,M.Vk)(e).itemsOrig[n],e.filtersStateId=e.filtersStateId+1})),e.addMatcher(E.j.configured.matchFulfilled,(function(e,t){e.itemsOrig[t.meta.arg.originalArgs.rootHash]=t.payload,e.itemsFiltered=t.payload,e.filtersStateId=e.filtersStateId+1}))})),J=(0,s.reducerWithInitialState)(V(0)).case(U.O,(function(e,t){var n=t.reduce((function(e,t){return t(e)}),e.itemsOrig[e.location]);return W(W({},e),{itemsFiltered:n})})).case(U.K5,(function(e){return W(W({},e),{itemsFiltered:e.itemsOrig[e.location]})})).case(U.Nk,(function(e,t){return W(W({},e),{searchWords:t})})).case(Z.b9.async.done,(function(e,t){var n;return W(W({},e),{filtersStateId:e.filtersStateId+1,itemsOrig:W(W({},e.itemsOrig),(n={},n[e.location]=t.result,n))})})).case(Z.CS.async.done,(function(e,t){var n;return W(W({},e),{itemsOrig:W(W({},e.itemsOrig),(n={},n[e.location]=e.itemsOrig[e.location].filter((function(e){return e.id!==t.result.id})),n)),itemsFiltered:e.itemsFiltered.filter((function(e){return e.id!==t.result.id}))})})).case(Z.O7.async.done,(function(e,t){var n;if(!e.itemsFiltered)return e;var r=t.result.behaviours.find((function(e){return e.id===t.result.behaviourId}));return W(W({},e),{itemsOrig:W(W({},e.itemsOrig),(n={},n[e.location]=t.result.behaviours,n)),itemsFiltered:e.itemsFiltered.map((function(e){return e.id===t.result.behaviourId?r:e}))})})).case(Z.oY.async.done,(function(e,t){var n,r,o=null===(r=e.itemsOrig[e.location])||void 0===r?void 0:r.find((function(e){return e.id===t.result.id}));if(!o)return e;var i=W(W({},o),{mappings:o.mappings.filter((function(e){return!(0,k.mp)(e,t.result.mapping)}))});return W(W({},e),{itemsOrig:W(W({},e.itemsOrig),(n={},n[e.location]=e.itemsOrig[e.location].map((function(e){return e.id===t.result.id?i:e})),n)),itemsFiltered:e.itemsFiltered.map((function(e){return e.id===t.result.id?i:e}))})})).case(Z.BR.async.done,(function(e,t){var n,r=function(e){return e.id===t.result.behaviourId?W(W({},e),{disabled:t.result.disabled}):e};return W(W({},e),{itemsOrig:W(W({},e.itemsOrig),(n={},n[e.location]=e.itemsOrig[e.location].map(r),n)),itemsFiltered:e.itemsFiltered.map(r)})})).case(l.uc.async.done,(function(e,t){var n,r,o=null===(r=t.result)||void 0===r?void 0:r.reduce((function(e,t){return e[t.functionAssignmentId]=t,e}),{});return W(W({},e),{itemsOrig:W(W({},e.itemsOrig),(n={},n[e.location]=e.itemsOrig[e.location].filter((function(e){return o[e.functionAssignmentId]})).map((function(e){return o[e.functionAssignmentId]||e})),n)),itemsFiltered:e.itemsFiltered.filter((function(e){return o[e.functionAssignmentId]})).map((function(e){return o[e.functionAssignmentId]||e}))})})).case(Z.DK,(function(e,t){var n;return W(W({},e),{itemsOrig:W(W({},e.itemsOrig),(n={},n[e.location]=G(G([],q(e.itemsOrig[e.location]),!1),[t],!1),n)),itemsFiltered:G(G([],q(e.itemsOrig[e.location]),!1),[t],!1)})})).default(H),B=J,z=n(96226),K=n(3114),$=n(49268),Y=n(20371),Q=n(1026),X=function(){return X=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},X.apply(this,arguments)},ee={moduleName:K.$f,reducer:(0,s.reducerWithInitialState)(Y.E.isEvaluationLicense?X(X({},(0,Q.eU)()),Y.E.onboarding||{}):X(X({},(0,Q.eG)()),Y.E.onboarding||{})).cases([$.o4.async.done,$.XF.async.done,$.gX.async.done],(function(e,t){var n=t.result;return n?X(X({},e),n):e})).case($.o4.async.started,(function(e,t){var n,r=((n={})[t.moduleType]="COMPLETED",n);return X(X({},e),r)})).case($.XF.async.started,(function(e,t){var n,r=((n={})[t.moduleType]="DISMISSED",n);return X(X({},e),r)})).case($.gX.async.started,(function(e,t){var n,r=((n={})[t]="UNVISITED",n);return X(X({},e),r)}))},te=(0,s.reducerWithInitialState)(!1).case($.at.async.done,(function(e,t){return t.result})),ne=n(40653),re=n(33323),oe=function(){return oe=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},oe.apply(this,arguments)},ie={cancel:{title:"Cancel"},submit:{title:"Run"},preview:{title:"Preview"}},ae=function(e,t){return["cancel","submit","preview"].reduce((function(n,r){return n[r]=oe(oe({},e[r]),{disabled:t,waiting:!1}),n}),{})},ce=(0,s.reducerWithInitialState)(ie).case(l.aL,(function(e,t){var n,r,o=t.button,i=t.attr,a=t.val;return oe(oe({},e),((n={})[o]=oe(oe({},e[o]),((r={})[i]=a,r)),n))})).case(l.aB,(function(){return ie})).case(l.fP,(function(e){return ae(e,!0)})).cases([l.Ko.async.started,l.fY.async.started,l.kq.async.started,l.$9.async.started],(function(e){return ae(e,!0)})).cases([l.Ko.async.done,l.fY.async.done,l.kq.async.done,l.$9.async.done],(function(e){return ae(e,!1)})).cases([l.Ko.async.failed,l.fY.async.failed,l.kq.async.failed,l.$9.async.failed],(function(e){return ae(e,!1)})).case(l.IK,(function(e,t){var n,r=t.button;return oe(oe({},e),((n={})[r]=oe(oe({},e[r]),{waiting:!0}),n))})),se=(0,i.Lq)(null,(function(e){e.addMatcher(E.j.configured.matchFulfilled,(function(e,t){return t.payload}))})),ue=(0,s.reducerWithInitialState)(null).case(l.uc.async.done,(function(e,t){return t.result})).case(l.d3,(function(e,t){var n=t.id,r=t.config,o=e.findIndex((function(e){return e[r.uniqueIdKey]===n})),i=e[o].disabled;return e.map((function(t,n){return o!==n?t:p()(e[o],{disabled:{$set:!i}})}))})).default(se),le=function(e,t){return t.error.validation?t.error.validation:{errorMessages:["Failed to post update"]}},de=(0,s.reducerWithInitialState)({}).case(l.fY.async.failed,le).case(l.kq.async.failed,le).case(l.Ko.async.failed,le).case(l.vG.async.failed,le).case(l.$9.async.failed,le).case(l.XS.async.failed,le).case(l.TO,(function(e,t){return t})).case(l.KE,(function(e,t){return oe(oe({},e),{errors:oe(oe({},e.errors),t)})})).cases([l.Ko.async.done,l.Jy,l.vG.async.done,l.$9.async.done],(function(){return{}})).cases([l.fY.async.done,l.kq.async.done],(function(){return{}})),pe=(0,i.oM)({name:"search",initialState:{},reducers:{},extraReducers:function(e){e.addMatcher((0,i.Q)(E.j.available.matchFulfilled,E.j.configured.matchFulfilled),(function(e,t){var n=t.meta.arg.originalArgs.rootHash,r=e[n];r?r.configured=t.payload:e[n]={configured:t.payload}}))}}),fe=(0,s.reducerWithInitialState)({}).case(l.bB,(function(e,t){var n,r=t.key,o=t.value;return oe(oe({},e),((n={})[r]=o,n))})).cases([l.Jy,z.R6],(function(){return{}})).case(l.kz,(function(e,t){return t})),me=(0,s.reducerWithInitialState)(function(){var e=localStorage.getItem(f.jQ),t={scriptFile:null,scriptText:null,parameters:{}};if(e)try{return JSON.parse(e)}catch(e){return t}return t}()).case(l.Ge,(function(e,t){return oe(oe({},e),t)})),he=(0,s.reducerWithInitialState)(null).cases([l.Ko.async.done,l.$9.async.done],(function(e,t){var n=t.result;return oe(oe({},n),{isLoading:!1})})).cases([l.Ko.async.started,l.$9.async.started],(function(e){return e?oe(oe({},e),{isLoading:!0}):null})).cases([l.Ko.async.failed,l.$9.async.failed,l.Jy,N.cT],(function(){return null})).case(l.qE.async.done,(function(e,t){var n=t.result;return oe(oe({},e),{output:oe(oe({},e.output),n)})})),ge=(0,s.reducerWithInitialState)(null).case(l.XS.async.done,(function(e,t){return t.result.map((function(e){return(t=e)&&(t.examples||t.schema||t.placeholder)&&e.examples?oe(oe({},e),{examples:(e.examples||[]).map((function(e){return{name:e[0],code:e[1]}}))}):e;var t}))})).case(l.tr,(function(e,t){return t})).case(l.Jy,(function(){return null})).case(l.Id,(function(e,t){var n=t.fieldId,r=t.disabled;return e.map((function(e){return e.name===n?oe(oe({},e),{disabled:r}):e}))})).case(l.HP,(function(e,t){var n=t.fieldId,r=t.hidden;return e.map((function(e){return e.name===n?oe(oe({},e),{hidden:r}):e}))})).case(l.Dq,(function(e,t){var n=t.fieldId,r=t.waiting;return e.map((function(e){return e.name===n?oe(oe({},e),{waiting:r}):e}))})).case(l.OH,(function(e,t){var n=t.fieldId,r=t.properties;return e.map((function(e){return e.name===n?oe(oe({},e),r):e}))})),ye={builtins:null,params:null,configured:null},ve=(0,i.Lq)(ye,(function(e){e.addMatcher(E.j.available.matchPending,(function(e){e.builtins="short"})).addMatcher(E.j.configured.matchPending,(function(e){e.configured="short"})).addMatcher((0,i.Q)(E.j.configured.matchFulfilled,E.j.configured.matchRejected),(function(e){e.configured=null})).addMatcher((0,i.Q)(E.j.available.matchFulfilled,E.j.available.matchRejected),(function(e){e.builtins=null}))})),be=(0,s.reducerWithInitialState)(ye).case(l.XS.async.started,(function(e){return oe(oe({},e),{params:"short"})})).cases([l.XS.async.done,l.XS.async.failed],(function(e){return oe(oe({},e),{params:null})})).case(l.M0,(function(e,t){var n,r=t.section;return oe(oe({},e),((n={})[r]="long",n))})).default(ve),we=(0,s.reducerWithInitialState)({}).case(l.H2,(function(e,t){return t})),Ee=(0,s.reducerWithInitialState)(null).case(l.mB,(function(e,t){return oe(oe({},t),{counter:(null==e?void 0:e.counter)?e.counter+1:1})})).case(l.R0,(function(){return null})),Se=function(e){return e.validation||{}},xe=function(e){return(0,a.P1)(Se,(function(t){return t.errors?t.errors[e]:null}))},Pe=function(e){return e.edit},Ie=function(e){return(0,a.P1)(Pe,(function(t){return t[e]||{}}))};const Ce=(0,c.UY)(((o={ui:ce,items:ue,validation:de,console:me,edit:fe,result:he,editParams:ge,loading:be,context:we,flag:Ee})[pe.name]=pe.reducer,o.registryState=O.Bl,o.router=(0,u.iz)((0,I.i)()),o[y.e]=v,o[P.e1]=P.I6,o[T.e1]=T.I6,o[x.e1]=x.I6,o[C.qA.moduleName]=C.qA.reducer,o.onboarding=ee.reducer,o[S.name]=S.reducer,o.renderOnboarding=te,o.requests=function(e,t){var n=e||{active:0};if(navigator.webdriver){var r=t.type;if(r.endsWith("_STARTED"))return{active:n.active+1};if(r.endsWith("_DONE")||r.endsWith("_FAILED"))return{active:n.active-1}}return n},o.behavioursAdmin=k.ZP,o.behaviourEdit=A.ZP,o[_.e]=R,o[j.e]=B,o[ne.B.reducerPath]=ne.B.reducer,o[re.lC.name]=re.lC.reducer,o))},64772:(e,t,n)=>{"use strict";n.d(t,{Q:()=>l,Z:()=>d});var r,o=n(63844),i=n(13061),a=n(80515),c=n(40528),s=(r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},r(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),u=n(22106),l=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.restUrl="".concat(AJS.contextPath(),"/rest/scriptrunner-confluence/latest/macros"),t.heading="Macros",t.docUrl="?contentKey=macros",t.preamble=function(){return o.createElement("div",null,o.createElement("p",null,"Script macros allow you to include dynamic content through executing your own scripts."))},t.uniqueIdKey="id",t.executeButtonValue="Update",t.rootHash="/macros",t.functionIdKey=function(e){return e.id},t.omitExecutionHistoryColumns=!1,t.canPreview=!1,t.canDisable=!0,t.canEdit=function(e){return!e.builtin},t.canDelete=function(e){return!e.builtin},t.getSearchFiltersComponents=function(){return[o.createElement(c.r,{key:"wordsSearchFilter",getItemName:function(e){return e.FIELD_MACRO_NAME},getItemNotes:function(e){return e.FIELD_MACRO_DESC}})]},t.renderLeftPanelDescription=function(e,t){return o.createElement("form",{className:"aui"},o.createElement(u,{searchWords:t||[],autoEscape:!0,textToHighlight:e.FIELD_MACRO_NAME||""}),e.helpUrl&&o.createElement(o.Fragment,null," ",o.createElement("a",{href:e.helpUrl,target:"_blank"},o.createElement("div",{className:"aui-icon aui-icon-small aui-iconfont-help"}))),o.createElement("br",null),o.createElement("div",{className:"description"},o.createElement(a.o,{value:e.FIELD_MACRO_DESC,searchWords:t})),e.builtin&&o.createElement("div",{className:"description",style:{color:"#707070",fontSize:12,lineHeight:"1.66666666666667",margin:"5px 0 0 0"}},"This is a built-in macro and can't be edited, but you can disable it"))},t}return s(t,e),t.prototype.shouldDisplayConfigOptions=function(e){return!0},t}(i.Z);const d=l},4248:(e,t,n)=>{"use strict";n.d(t,{_9:()=>o,jQ:()=>r});n(33323);var r="scriptrunner.".concat("jira",".admin.console.state"),o={background:"repeating-linear-gradient(45deg, transparent, transparent 10px, rgb(246, 246, 246) 10px, rgb(246, 246, 246) 20px)"}},83051:(e,t,n)=>{"use strict";n.d(t,{GV:()=>i,Wz:()=>o,YH:()=>a});var r=n(79949),o={flags:[]},i=function(e){return(0,r.oM)({name:"uiFlag",initialState:o,reducers:{removeLastFlag:function(e){e.flags.shift()}},extraReducers:e})},a=i().actions.removeLastFlag},65475:(e,t,n)=>{"use strict";function r(){if(void 0!==AJS.contextPath)return AJS.contextPath();var e=$("meta[name='srBaseUrl']");if(e.length)return e.attr("content");var t=$("#confluence-base-url");return t.length?t.attr("content"):window.CodeBarrel?window.CodeBarrel.Automation.getContext().baseUrl:AJS.params.baseURL}n.d(t,{S:()=>r})},7011:(e,t,n)=>{"use strict";n.d(t,{$7:()=>o,Cc:()=>i,aT:()=>s,k1:()=>c});var r=function(){return r=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},r.apply(this,arguments)},o=function(e,t){void 0===t&&(t={}),window.require(["aui/flag"],(function(n){return n(r({type:"success",title:"Success",persistent:!1,close:"auto",body:e},t))}))},i=function(e){return void 0===e&&(e={}),o(null,e)},a=function(e,t,n){return function(r,o){var a=void 0===o?{}:o,c=a.title,s=void 0===c?t:c,u=a.close;return i({type:e,title:s,close:void 0===u?n:u,body:r,persistent:!1}),r}},c=a("error","Error","auto"),s=a("success","Success","auto")},20678:(e,t,n)=>{"use strict";n.d(t,{f:()=>r});var r=function(e){return e?e.charAt(0).toUpperCase()+e.slice(1):""}},84331:(e,t,n)=>{"use strict";n.d(t,{QO:()=>c,e5:()=>a});var r=n(3514),o=function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,i=n.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(r=i.next()).done;)a.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return a},i=function(e,t,n){if(n||2===arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))},a=function(e,t){var n=Array.isArray(e)?e:[],o=Array.isArray(t)?t:[];return r.Z(n,o)},c=function(e,t,n){return e.length>=t?i(i(i([],o(e.slice(0,t)),!1),o(n),!1),o(e.slice(t)),!1):i(i([],o(e),!1),o(n),!1)}},49278:(e,t,n)=>{"use strict";n.d(t,{Xl:()=>u,_3:()=>s});var r=n(63844),o=n(72142),i=n(89180),a=n(33323),c=function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,i=n.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(r=i.next()).done;)a.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return a},s=function(e){var t=e.context,n=(0,o.I0)();return r.useEffect((function(){n((0,a.vG)(t))}),[t]),null},u=function(){var e=(0,o.I0)(),t=(0,o.v9)((function(e){return e.whatsNew.pages}));return r.createElement(l,{config:t,markAsSeen:function(t,n){return e((0,a.PZ)({page:t,index:n}))},dismissAll:function(t,n){return e((0,a.NM)({page:t,index:n}))}})},l=function(e){var t=e.config,n=e.dismissAll,o=e.markAsSeen,a=c(r.useState(t.length>0),2),s=a[0],u=a[1];if(r.useEffect((function(){return u(t.length>0)}),[t]),!s)return null;var l=t.flatMap((function(e){return e.pages})),d=function(e){return t.find((function(t){return t.pages.includes(l[e])}))},p=function(e){n(d(e),e),u(!1)},f=function(e){return o(d(e),e)};return r.createElement(i.w,{onClose:p,shouldCloseOnEscapePress:!0,shouldCloseOnOverlayClick:!0,pages:l,setModuleDismissed:p,setModuleCompleted:function(e){f(e),u(!1)},onNextPage:function(e){return f(e)}})}},33323:(e,t,n)=>{"use strict";n.d(t,{vG:()=>O,NM:()=>_,PZ:()=>A,lC:()=>k});var r=n(79949),o=n(4125),i=function(){return i=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},i.apply(this,arguments)},a=function(e,t,n,r){return new(n||(n=Promise))((function(o,i){function a(e){try{s(r.next(e))}catch(e){i(e)}}function c(e){try{s(r.throw(e))}catch(e){i(e)}}function s(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(a,c)}s((r=r.apply(e,t||[])).next())}))},c=function(e,t){var n,r,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:c(0),throw:c(1),return:c(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function c(i){return function(c){return function(i){if(n)throw new TypeError("Generator is already executing.");for(;a;)try{if(n=1,r&&(o=2&i[0]?r.return:i[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,i[1])).done)return o;switch(r=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return a.label++,{value:i[1],done:!1};case 5:a.label++,r=i[1],i=[0];continue;case 7:i=a.ops.pop(),a.trys.pop();continue;default:if(!(o=a.trys,(o=o.length>0&&o[o.length-1])||6!==i[0]&&2!==i[0])){a=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){a.label=i[1];break}if(6===i[0]&&a.label<o[1]){a.label=o[1],o=i;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(i);break}o[2]&&a.ops.pop(),a.trys.pop();continue}i=t.call(e,a)}catch(e){i=[6,e],r=0}finally{n=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,c])}}},s=function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,i=n.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(r=i.next()).done;)a.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return a},u=function(e,t,n){if(n||2===arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))},l="whatsNew",d=function(){function e(e){void 0===e&&(e=new o.z),this.userPropertiesRepository=e,this.state=null}return e.prototype.getState=function(){var e;return a(this,void 0,Promise,(function(){var t;return c(this,(function(n){switch(n.label){case 0:if(null!=this.state)return[3,4];n.label=1;case 1:return n.trys.push([1,3,,4]),t=this,[4,this.userPropertiesRepository.getProperty(l)];case 2:return t.state=null!==(e=n.sent())&&void 0!==e?e:{},[3,4];case 3:return n.sent(),this.state={},[3,4];case 4:return[2,this.state]}}))}))},e.prototype.markStepAsSeen=function(e,t){var n;return a(this,void 0,void 0,(function(){var r;return c(this,(function(o){switch(o.label){case 0:return null!=this.state?[3,2]:[4,this.getState()];case 1:o.sent(),o.label=2;case 2:return this.state=i(i({},this.state),((r={})[e]=u(u([],s(null!==(n=this.state[e])&&void 0!==n?n:[]),!1),[{name:t.name,releasedOn:t.releasedOn}],!1),r)),[4,this.userPropertiesRepository.mergeProperty(l,this.state)];case 3:return o.sent(),[2,this.state]}}))}))},e.prototype.markAllAsSeen=function(e,t){return a(this,void 0,void 0,(function(){var n;return c(this,(function(r){switch(r.label){case 0:return null!=this.state?[3,2]:[4,this.getState()];case 1:r.sent(),r.label=2;case 2:return this.state=i(i({},this.state),((n={})[e]=t.map((function(e){return{name:e.name,releasedOn:e.releasedOn}})),n)),[2,this.userPropertiesRepository.mergeProperty(l,this.state)]}}))}))},e}();const p=n.p+"intelligentCodeEditor1-f34fd25a..svg",f=n.p+"intelligentCodeEditor2-d3208dbb..png",m=n.p+"intelligentCodeEditor3-f7986567..svg";var h,g=function(e,t,n,r){return new(n||(n=Promise))((function(o,i){function a(e){try{s(r.next(e))}catch(e){i(e)}}function c(e){try{s(r.throw(e))}catch(e){i(e)}}function s(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(a,c)}s((r=r.apply(e,t||[])).next())}))},y=function(e,t){var n,r,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:c(0),throw:c(1),return:c(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function c(i){return function(c){return function(i){if(n)throw new TypeError("Generator is already executing.");for(;a;)try{if(n=1,r&&(o=2&i[0]?r.return:i[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,i[1])).done)return o;switch(r=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return a.label++,{value:i[1],done:!1};case 5:a.label++,r=i[1],i=[0];continue;case 7:i=a.ops.pop(),a.trys.pop();continue;default:if(!(o=a.trys,(o=o.length>0&&o[o.length-1])||6!==i[0]&&2!==i[0])){a=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){a.label=i[1];break}if(6===i[0]&&a.label<o[1]){a.label=o[1],o=i;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(i);break}o[2]&&a.ops.pop(),a.trys.pop();continue}i=t.call(e,a)}catch(e){i=[6,e],r=0}finally{n=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,c])}}},v={codeEditor:[{name:"intelligentCodeEditor",context:"codeEditor",releasedOn:new Date("2022-09-07T00:00:00.000Z"),pages:[{headerText:"Introducing: Intelligent Code Editor",bodyText:"The Code Editor collaborates with you to make coding faster. See completions automatically as you type, find and replace, plus much more!",image:p},{headerText:"",bodyText:"Documentation is also closer than ever, right when you need it: simply hover over methods and classes or press Control+Space when completions are open to get insight and tips.",image:f},{headerText:"",bodyText:"ScriptRunner is designed to save Atlassian administrators time and the scripting experience is no exception. We hope you enjoy using the new Code Editor!",image:m}]}]},b=function(){function e(e,t){void 0===e&&(e=new d),void 0===t&&(t=v),this.repository=e,this.config=t;var n=new Date;this.cutOffDate=new Date(Date.UTC(n.getUTCFullYear(),n.getUTCMonth()-6))}return e.prototype.getPagesForContext=function(e){var t,n,r,o;return g(this,void 0,Promise,(function(){var i,a,c,s=this;return y(this,(function(u){switch(u.label){case 0:return i=null!==(n=null===(t=this.config[e])||void 0===t?void 0:t.filter((function(e){return e.releasedOn.getTime()>s.cutOffDate.getTime()})))&&void 0!==n?n:[],[4,this.repository.getState()];case 1:return a=u.sent(),c=null!==(o=null===(r=a[e])||void 0===r?void 0:r.map(this.toSeenKey))&&void 0!==o?o:[],[2,i.filter((function(e){return!c.includes(s.toSeenKey(e))}))]}}))}))},e.prototype.markAsSeen=function(e,t){return this.repository.markStepAsSeen(e,t)},e.prototype.markAllAsSeen=function(e){return this.repository.markAllAsSeen(e,this.config[e])},e.prototype.toSeenKey=function(e){return"".concat(e.name,"-").concat(new Date(e.releasedOn))},e}(),w=n(74703),E=function(e){var t="function"==typeof Symbol&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},S=function(){function e(e,t,n){void 0===n&&(n=w.w),this.path=e,this.pages=t,this.track=n,this.overallPages=t.flatMap((function(e){return e.pages})).length-1}return e.prototype.trackNextPage=function(e,t){if(this.pages.length){var n={path:this.path,featureName:e.name,featurePage:this.pagePosition(t),page:t,action:t===this.overallPages?"done":"next"};this.track("whatsNew",n)}},e.prototype.trackDismiss=function(e,t){if(this.pages.length){var n={path:this.path,featureName:e.name,featurePage:this.pagePosition(t),page:t,action:"dismiss"};this.track("whatsNew",n)}},e.prototype.pagePosition=function(e){var t,n,r=e;try{for(var o=E(this.pages),i=o.next();!i.done;i=o.next()){var a=i.value;if(!(r-a.pages.length>=0))return r;r-=a.pages.length}}catch(e){t={error:e}}finally{try{i&&!i.done&&(n=o.return)&&n.call(o)}finally{if(t)throw t.error}}return 0},e}(),x=function(e,t,n,r){return new(n||(n=Promise))((function(o,i){function a(e){try{s(r.next(e))}catch(e){i(e)}}function c(e){try{s(r.throw(e))}catch(e){i(e)}}function s(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(a,c)}s((r=r.apply(e,t||[])).next())}))},P=function(e,t){var n,r,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:c(0),throw:c(1),return:c(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function c(i){return function(c){return function(i){if(n)throw new TypeError("Generator is already executing.");for(;a;)try{if(n=1,r&&(o=2&i[0]?r.return:i[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,i[1])).done)return o;switch(r=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return a.label++,{value:i[1],done:!1};case 5:a.label++,r=i[1],i=[0];continue;case 7:i=a.ops.pop(),a.trys.pop();continue;default:if(!(o=a.trys,(o=o.length>0&&o[o.length-1])||6!==i[0]&&2!==i[0])){a=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){a.label=i[1];break}if(6===i[0]&&a.label<o[1]){a.label=o[1],o=i;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(i);break}o[2]&&a.ops.pop(),a.trys.pop();continue}i=t.call(e,a)}catch(e){i=[6,e],r=0}finally{n=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,c])}}},I=function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,i=n.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(r=i.next()).done;)a.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return a},C=function(e,t,n){if(n||2===arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))},T=new b,O=(0,r.hg)("whatsNew/pagesForContext",(function(e){return x(void 0,void 0,void 0,(function(){return P(this,(function(t){return[2,T.getPagesForContext(e)]}))}))}),{condition:function(e,t){var n=t.getState();return!D(!1,n.onboarding)&&!n.whatsNew.visibleContext.includes(e)}}),k=(0,r.oM)({name:"whatsNew",initialState:{visibleContext:[],pages:[]},reducers:{markPageAsSeen:function(e,t){var n=t.payload,r=n.page,o=n.index;return T.markAsSeen(r.context,r),new S(r.context,e.pages).trackNextPage(r,o),e},markAllAsSeen:function(e,t){var n=t.payload,r=n.page,o=n.index;return T.markAllAsSeen(r.context),new S(r.context,e.pages).trackDismiss(r,o),e}},extraReducers:function(e){e.addCase(O.pending,(function(e,t){e.visibleContext.push(t.meta.arg)})).addCase(O.fulfilled,(function(e,t){var n;(n=e.pages).push.apply(n,C([],I(t.payload),!1))}))}}),A=(h=k.actions).markPageAsSeen,_=h.markAllAsSeen,D=function(e,t){return!1}},50382:(e,t,n)=>{"use strict";n.d(t,{j:()=>s});var r=n(40653),o=n(77937),i=function(){return i=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},i.apply(this,arguments)},a=function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,i=n.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(r=i.next()).done;)a.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return a},c=r.B.injectEndpoints({endpoints:function(e){return{available:e.query({queryFn:function(e,t,n,r){var o=e.canAddNew,i=e.restUrl,a=e.params;return o?r({url:"".concat(i,"/available/"),params:a}):{data:[]}},onQueryStarted:function(e,t){var n=(0,o.PQ)(t.dispatch,"builtins");t.queryFulfilled.finally((function(){return clearTimeout(n)}))}}),configured:e.query({query:function(e){return{url:e.restUrl,params:e.params}},onQueryStarted:function(e,t){var n=(0,o.PQ)(t.dispatch,"configured");t.queryFulfilled.finally((function(){return clearTimeout(n)}))},providesTags:function(e,t,n){return e&&u(n)},keepUnusedDataFor:1})}}}),s={available:i(i({},c.endpoints.available),{initiate:function(e,t,n){return c.endpoints.available.initiate(d(e,t),n)},select:function(e,t){return c.endpoints.available.select(d(e,t.context))(t)},scriptWithId:function(e,t){return Object.entries(e[r.B.reducerPath].queries).filter((function(e){var t=a(e,2),n=t[0],r=t[1];return n.startsWith("available")&&r.data})).flatMap((function(e){var t=a(e,2);t[0];return t[1].data})).find((function(e){return e.class===t}))}}),configured:i(i({},c.endpoints.configured),{initiate:function(e,t,n){return c.endpoints.configured.initiate(l(e,t),n)},invalidate:function(e){return c.util.invalidateTags(u(e))},select:function(e,t){return c.endpoints.configured.select(l(e,t.context))(t)},isLoadingConfiguredEndpoint:function(e){var t,n=e[r.B.reducerPath].queries;return"fulfilled"!==(null===(t=Object.values(n).find((function(t){var n;return t&&(null===(n=t.originalArgs)||void 0===n?void 0:n.rootHash)===e.itemsSearch.location})))||void 0===t?void 0:t.status)}}),useAvailableItemsQuery:function(e,t){return c.useAvailableQuery(d(e,t))},useConfiguredScriptsQuery:function(e,t){return c.useConfiguredQuery(l(e,t))}},u=function(e){return[{type:"Configured",id:e.rootHash}]},l=function(e,t){return{restUrl:e.restUrl,rootHash:e.rootHash,params:i(i({},e.urlParams),t)}},d=function(e,t){return i(i({},l(e,t)),{canAddNew:e.canAddNew})}},79193:(e,t,n)=>{"use strict";n.d(t,{e:()=>o});var r=n(63844),o=function(e){var t=e.label,n=e.icon,o=e.onClick,i=e.isVisible,a=e.testId,c=e.redirectUrl;return!1===i?null:r.createElement("li",null,r.createElement("span",{className:"sr-table-action sr-table-".concat(a,"-action")},r.createElement("a",{onClick:o,href:c,"data-qa":a},r.createElement("span",null,n," ",t))))}},42213:(e,t,n)=>{"use strict";n.d(t,{M:()=>o,m:()=>i});var r=n(24478),o="https://productsupport.adaptavist.com/servicedesk/customer/portal/13",i=[{url:"https://www.adaptavist.com/case-studies/payback",text:"Case study",resourceType:"caseStudy"},{url:"https://docs.adaptavist.com/sr4c/latest",text:"Product documentation",resourceType:"documentation"},{url:(0,r.w7)("?contentKey=training"),text:"Training hub",resourceType:"trainingHub"},{url:(0,r.w7)("?contentKey=scriptrunner-migration"),text:"Migration guide",resourceType:"migrationGuide"},{url:"https://library.adaptavist.com/",text:"Script library",resourceType:"scriptLibrary"},{url:o,text:"Give feedback",resourceType:"supportPortal"}]},59482:(e,t,n)=>{"use strict";n.d(t,{VN:()=>P,on:()=>L,Ie:()=>_,pz:()=>N,up:()=>F,eK:()=>D,ri:()=>w,e1:()=>f,I6:()=>j,lH:()=>E,Nv:()=>b,jn:()=>S});var r=n(7009),o=n(66494),i=n(86530),a=n(96355),c=n(59889),s=n(26052),u=n(66661),l=function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,i=n.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(r=i.next()).done;)a.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return a},d=function(e,t,n){if(n||2===arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))},p=function(e){var t,n;return!(null===(t=e.script)||void 0===t?void 0:t.trim())&&!(null===(n=e.scriptFile)||void 0===n?void 0:n.trim())},f="monacoState",m=n(74729),h=function(e,t,n,r){return new(n||(n=Promise))((function(o,i){function a(e){try{s(r.next(e))}catch(e){i(e)}}function c(e){try{s(r.throw(e))}catch(e){i(e)}}function s(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(a,c)}s((r=r.apply(e,t||[])).next())}))},g=function(e,t){var n,r,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:c(0),throw:c(1),return:c(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function c(i){return function(c){return function(i){if(n)throw new TypeError("Generator is already executing.");for(;a;)try{if(n=1,r&&(o=2&i[0]?r.return:i[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,i[1])).done)return o;switch(r=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return a.label++,{value:i[1],done:!1};case 5:a.label++,r=i[1],i=[0];continue;case 7:i=a.ops.pop(),a.trys.pop();continue;default:if(!(o=a.trys,(o=o.length>0&&o[o.length-1])||6!==i[0]&&2!==i[0])){a=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){a.label=i[1];break}if(6===i[0]&&a.label<o[1]){a.label=o[1],o=i;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(i);break}o[2]&&a.ops.pop(),a.trys.pop();continue}i=t.call(e,a)}catch(e){i=[6,e],r=0}finally{n=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,c])}}},y=(0,i.ZP)(f),v=(0,a.ZP)(y),b=y("SET_MONACO_STATE"),w=y("TOGGLE_FULL_SCREEN"),E=y("SET_MONACO_RESET_RAG_STATE"),S=y("SET_RAG_STATUS"),x={compilationResult:{errors:[],warnings:[],infos:[],parameters:{known:!1,metadata:[]}}},P=v("STATIC_COMPILE",(function(e){return h(void 0,void 0,void 0,(function(){return g(this,(function(t){switch(t.label){case 0:return p(e)?[2,x]:[4,(0,m.wrappedFetch)("".concat(AJS.contextPath(),"/rest/scriptrunner/latest/codeinsight/check"),{method:"POST",body:JSON.stringify(e)})];case 1:return[2,t.sent().result]}}))}))})),I=n(77937),C=n(96226),T=n(1399),O=n(37591),k=function(e){return(0,O.P1)([function(e){return e[f]}],(function(t){var n;return null!==(n=t[e])&&void 0!==n?n:{id:e,fullScreen:!1,snippetsVisible:!1}}))},A=function(e){return function(t){return(0,O.P1)([k(t)],e)}},_=A((function(e){var t;return null!==(t=e.fullScreen)&&void 0!==t&&t})),D=A((function(e){return e.snippetsVisible})),N=A((function(e){return e.markers})),F=A((function(e){return e.ragStatus})),L=A((function(e){return e.aiCodeAssistStatus||"done"})),R=function(){return R=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},R.apply(this,arguments)},j=(0,o.reducerWithInitialState)({}).case(b,(function(e,t){var n;return R(R({},e),((n={})[t.id]=R(R({},e[t.id]),t),n))})).case(w,(function(e,t){var n,r;return R(R({},e),((n={})[t]=R(R({},e[t]),{fullScreen:!(null===(r=e[t])||void 0===r?void 0:r.fullScreen)}),n))})).case(E,(function(e,t){var n;return R(R({},e),((n={})[t.id]=R(R({},e[t.id]),{ragStatus:null}),n))})).case(I.Jy,(function(){return{}})).case(S,(function(e,t){var n;return R(R({},e),((n={})[t.id]=R(R({},e[t.id]),{ragStatus:t.value}),n))})).case(P.async.started,(function(e,t){var n;return p(t)?e:R(R({},e),((n={})[t.editorId]=R(R({},e[t.editorId]),{ragStatus:"waiting"}),n))})).case(P.async.done,(function(e,t){var n;if(p(t.params))return e;var r=t.params.editorId,o=function(e){var t,n;return(null===(t=e.compilationResult.errors)||void 0===t?void 0:t.length)?"red":(null===(n=e.compilationResult.warnings)||void 0===n?void 0:n.length)?"amber":"green"}(t.result),i=function(e){var t,n;if(null===e.compilationResult)return[];var r=null===(t=e.compilationResult.errors)||void 0===t?void 0:t.map(u.mapError),o=null===(n=e.compilationResult.warnings)||void 0===n?void 0:n.map(u.mapWarning);return c.Z(s.Z("message"),d(d([],l(r),!1),l(o),!1))}(t.result);return R(R({},e),((n={})[r]=R(R({},e[r]),{ragStatus:o,markers:i}),n))})).case(C.x3,(function(e){return r.Z("initializer",e)})).case(C.gV,(function(e,t){var n=t.fieldId;return r.Z(n,e)})).default(T.I)},48432:(e,t,n)=>{"use strict";n.d(t,{$8:()=>I,FF:()=>j,I6:()=>D,IW:()=>x,Tp:()=>M,U_:()=>O,aC:()=>C,e1:()=>S,fm:()=>F,hG:()=>A,oT:()=>R,pm:()=>T});var r=n(66494),o=n(37591),i=n(86530),a=n(96355),c=n(43332),s=n(59060),u=n(74593),l=n(93923),d=n(7011),p=n(39507),f=n(4125),m=n(7948),h=n(74729),g=function(){return g=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},g.apply(this,arguments)},y=function(e,t,n,r){return new(n||(n=Promise))((function(o,i){function a(e){try{s(r.next(e))}catch(e){i(e)}}function c(e){try{s(r.throw(e))}catch(e){i(e)}}function s(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(a,c)}s((r=r.apply(e,t||[])).next())}))},v=function(e,t){var n,r,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:c(0),throw:c(1),return:c(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function c(i){return function(c){return function(i){if(n)throw new TypeError("Generator is already executing.");for(;a;)try{if(n=1,r&&(o=2&i[0]?r.return:i[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,i[1])).done)return o;switch(r=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return a.label++,{value:i[1],done:!1};case 5:a.label++,r=i[1],i=[0];continue;case 7:i=a.ops.pop(),a.trys.pop();continue;default:if(!(o=a.trys,(o=o.length>0&&o[o.length-1])||6!==i[0]&&2!==i[0])){a=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){a.label=i[1];break}if(6===i[0]&&a.label<o[1]){a.label=o[1],o=i;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(i);break}o[2]&&a.ops.pop(),a.trys.pop();continue}i=t.call(e,a)}catch(e){i=[6,e],r=0}finally{n=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,c])}}},b=function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,i=n.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(r=i.next()).done;)a.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return a},w=(0,i.ZP)("editor/file"),E=(0,a.ZP)(w),S="file",x=E("LOAD_EDITOR_FILE",(function(e,t){return y(void 0,void 0,void 0,(function(){var n;return v(this,(function(r){switch(r.label){case 0:if(!(null==e?void 0:e.data.isFile)||!e.id&&!e.data.rootPath)return[2,{content:"",node:e}];r.label=1;case 1:return r.trys.push([1,3,4,5]),t((0,u.Mq)(!0)),[4,(0,h.wrappedFetch)("".concat(AJS.contextPath(),"/rest/scriptrunner/latest/idea/file?").concat((0,p.F)({filePath:e.data.relativePath,rootPath:e.data.rootPath})))];case 2:return n=r.sent(),[2,{content:c.Base64.decode(n.result.content),node:e}];case 3:throw r.sent(),(0,d.k1)("Could not load content of the ".concat(e.data.name," file. Please try again."));case 4:return t((0,u.Mq)(!1)),[7];case 5:return[2,{content:"",node:e}]}}))}))})),P="scriptEditorNextStepsModal",I=E("SAVE_EDITOR_FILE",(function(e,t,n){return y(void 0,void 0,void 0,(function(){var r,o,i,a,s,u;return v(this,(function(l){switch(l.label){case 0:return r=L(n()),o=(0,h.wrappedFetch)("".concat(AJS.contextPath(),"/rest/scriptrunner/latest/idea/file?").concat((0,p.F)({filePath:e.data.relativePath,rootPath:e.data.rootPath})),{method:"PUT",body:c.Base64.encode(r),headers:{"Content-Type":"application/octet-stream"}}),m=n(),i=y(void 0,void 0,Promise,(function(){var e,t;return v(this,(function(n){switch(n.label){case 0:return U(m)?[2,Promise.resolve(!0)]:[4,(e=new f.z).getProperty(P)];case 1:return(t=n.sent())||e.setProperty(P,!0),[2,Promise.resolve(t)]}}))})),[4,Promise.all([o,i])];case 1:if(a=b.apply(void 0,[l.sent(),2]),s=a[0],u=a[1],!s.response.ok)throw(0,d.k1)("Could not save file. Please try again");return t(u?k():O(!0)),[2,{content:r}]}var m}))}))})),C=E("DELETE_EDITOR_FILE",(function(e,t){return y(void 0,void 0,void 0,(function(){return v(this,(function(n){switch(n.label){case 0:t((0,u.Mq)(!0)),n.label=1;case 1:return n.trys.push([1,,5,6]),[4,(0,h.wrappedFetch)("".concat(AJS.contextPath(),"/rest/scriptrunner/latest/idea/file?").concat((0,p.F)({filePath:e.data.relativePath,rootPath:e.data.rootPath})),{method:"DELETE"})];case 2:if(!n.sent().response.ok)throw(0,d.k1)("Failed to delete ".concat(e.data.name," file. Please try again."));return[4,t((0,s.yr)())];case 3:return n.sent(),[4,t((0,m.Ib)())];case 4:return n.sent(),[3,6];case 5:return t((0,u.Mq)(!1)),[7];case 6:return[2]}}))}))})),T=w("MODIFY_FILE"),O=w("SHOW_NEXT_STEPS_MODAL"),k=w("SEEN_NEXT_STEPS_MODAL"),A=w("RESET_EDIT_STATE"),_={contentOnDisk:"",content:"",error:"",showNextStepsModal:!1,seenNextStepsModal:!1},D=(0,r.reducerWithInitialState)(_).case(x.async.done,(function(e,t){return g(g({},e),{contentOnDisk:t.result.content,content:t.result.content})})).case(I.async.done,(function(e,t){return g(g({},e),{contentOnDisk:t.result.content,content:t.result.content})})).case(I.async.failed,(function(e,t){return g(g({},e),{error:t.error})})).case(C.async.done,(function(){return _})).case(T,(function(e,t){return g(g({},e),{content:t.newContent})})).case(O,(function(e,t){return g(g({},e),{showNextStepsModal:t,seenNextStepsModal:!0})})).case(k,(function(e){return g(g({},e),{seenNextStepsModal:!0})})).case(A,(function(){return _})),N=(0,o.P1)(l.D,(function(e){return e[S]})),F=(0,o.P1)([N],(function(e){return e.contentOnDisk})),L=(0,o.P1)([N],(function(e){return e.content})),R=(0,o.P1)([N],(function(e){return e.contentOnDisk===e.content})),j=(0,o.P1)([m.hn,m.rT],(function(e,t){return!!e&&!!t})),M=(0,o.P1)([N],(function(e){return e.showNextStepsModal})),U=(0,o.P1)([N],(function(e){return e.seenNextStepsModal}))},59060:(e,t,n)=>{"use strict";n.d(t,{Eg:()=>F,k3:()=>L,Q3:()=>B,YA:()=>M,VC:()=>U,fg:()=>Z,h4:()=>K,EG:()=>z,Kl:()=>j,Fe:()=>R,zw:()=>H,yr:()=>k,e1:()=>C,SM:()=>N,I6:()=>G,D3:()=>J});var r=n(37591),o=n(86530),i=n(96355),a=n(66494),c=n(9722),s=n(48432),u=n(93923),l=n(7948),d=n(7011),p=n(54174),f=n(39507),m=n(7802),h=n(26052),g=function(){return g=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},g.apply(this,arguments)},y=function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,i=n.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(r=i.next()).done;)a.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return a},v=n(74593),b=n(74729),w=function(){return w=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},w.apply(this,arguments)},E=function(e,t,n,r){return new(n||(n=Promise))((function(o,i){function a(e){try{s(r.next(e))}catch(e){i(e)}}function c(e){try{s(r.throw(e))}catch(e){i(e)}}function s(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(a,c)}s((r=r.apply(e,t||[])).next())}))},S=function(e,t){var n,r,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:c(0),throw:c(1),return:c(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function c(i){return function(c){return function(i){if(n)throw new TypeError("Generator is already executing.");for(;a;)try{if(n=1,r&&(o=2&i[0]?r.return:i[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,i[1])).done)return o;switch(r=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return a.label++,{value:i[1],done:!1};case 5:a.label++,r=i[1],i=[0];continue;case 7:i=a.ops.pop(),a.trys.pop();continue;default:if(!(o=a.trys,(o=o.length>0&&o[o.length-1])||6!==i[0]&&2!==i[0])){a=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){a.label=i[1];break}if(6===i[0]&&a.label<o[1]){a.label=o[1],o=i;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(i);break}o[2]&&a.ops.pop(),a.trys.pop();continue}i=t.call(e,a)}catch(e){i=[6,e],r=0}finally{n=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,c])}}},x=function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,i=n.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(r=i.next()).done;)a.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return a},P=(0,o.ZP)("editor/fileTree"),I=(0,i.ZP)(P),C="fileTree",T={showDirectories:function(){return"".concat(AJS.contextPath(),"/rest/scriptrunner/latest/idea/scriptroots?showDirectories=true&groovyFilesOnly=true")},moveFile:function(e){return"".concat(AJS.contextPath(),"/rest/scriptrunner/latest/idea/file/move?").concat((0,f.F)(e))},moveDirectory:function(e){return"".concat(AJS.contextPath(),"/rest/scriptrunner/latest/resource-directories/directory/move?").concat((0,f.F)(e))},saveFile:function(e){return"".concat(AJS.contextPath(),"/rest/scriptrunner/latest/idea/file?").concat((0,f.F)(e))},deleteResource:function(e){return"".concat(AJS.contextPath(),"/rest/scriptrunner/latest/resource-directories/directory?").concat((0,f.F)(e))}},O=function(e,t,n){var r=(0,l.hn)(e);if(r&&(o=t.find((function(e){return e.info.rootPath===r}))))return r;var o,i=(0,l.Ub)(e);if(i&&(o=t.find((function(e){return e.files[i]}))))return o.info.rootPath;return n},k=I("LOAD_FILE_TREE",(function(e,t,n){return E(void 0,void 0,void 0,(function(){var r,o,i,a,c;return S(this,(function(s){switch(s.label){case 0:return[4,(0,b.wrappedFetch)(T.showDirectories())];case 1:return r=s.sent(),o=r.result.find((function(e){return e.info.defaultRoot})),i=n(),a=function(e,t,n){var r=(null==t?void 0:t.path)&&(0,p.Dy)([t.root,t.path]);return e.map((function(e){var t,o=(0,p.Dy)([e.info.rootPath]),i=(t=e.files,function(e,n){return t[e].isFile!==t[n].isFile?t[e].isFile?1:-1:e.localeCompare(n)}),a=Object.keys(e.files),c=Object.entries(e.files).map((function(e){var t,c=y(e,2),s=c[0],u=c[1],l=a.filter((function(e){return e!==s&&0===e.indexOf(s)&&"/"===e.charAt(s.length)&&!function(e,t){return t.indexOf("/",e.length+1)>=0}(s,e)})).sort(i).map((function(e){return(0,p.Dy)([o,e])})),d=(0,p.Dy)([o,s]),f=s.substr(s.lastIndexOf("/")+1),m=null!=r&&r.startsWith(d)||(null===(t=null==n?void 0:n.items[d])||void 0===t?void 0:t.isExpanded);return{id:d,children:l,hasChildren:l.length>0,isExpanded:null!=m&&m,isChildrenLoading:!1,data:{name:f,isFile:u.isFile,isRoot:!1,isSelected:!1,rootPath:o,relativePath:s}}})),s=a.filter((function(e){return-1===e.indexOf("/")})).sort(i).map((function(e){return(0,p.Dy)([o,e])})),u=m.Z(h.Z("id"),c);return{scriptRoot:o,rootNodes:s,childrenMap:u}})).reduce((function(e,t){var n,r;return g(g({},e),((n={})[t.scriptRoot]={rootId:"/",items:g((r={"/":{id:"/",children:[t.scriptRoot],hasChildren:0!==Object.keys(t.childrenMap).length,isExpanded:!0,isChildrenLoading:!1,data:{name:""}}},r[t.scriptRoot]={id:t.scriptRoot,children:t.rootNodes,hasChildren:t.rootNodes.length>0,isExpanded:!0,isChildrenLoading:!1,data:{name:t.scriptRoot.replace("/",""),isFile:!1,isRoot:!0,isSelected:!1,rootPath:t.scriptRoot,relativePath:""}},r),t.childrenMap)},n))}),{})}(r.result,i.editor.selected,i.editor.fileTree.tree),c=O(i,r.result,(0,p.Dy)([o.info.rootPath])),(null==e?void 0:e.selectNode)&&t((0,l.l0)(e.selectNode)),t((0,l.HD)(c)),[2,{tree:a[c],loading:!1,scriptRoots:a}]}}))}))})),A=function(e,t){return e.data.relativePath?"".concat(e.data.relativePath,"/").concat(t.data.name):t.data.name},_=function(e,t){var n=e.movedNode,r=(0,p.IG)(t.tree,e.targetNode),o={oldRootPath:n.data.rootPath,oldFilePath:n.data.relativePath,newFilePath:A(r,n),newRootPath:r.data.rootPath};return(0,b.wrappedFetch)(T.moveFile(o),{method:"POST"})},D=function(e,t){var n=e.movedNode,r=(0,p.IG)(t.tree,e.targetNode),o={currentRootPath:n.data.rootPath,currentDirectoryPath:n.data.relativePath,newDirectoryPath:A(r,n),newRootPath:r.data.rootPath};return(0,b.wrappedFetch)(T.moveDirectory(o),{method:"POST"})},N=I("MOVE_RESOURCE",(function(e,t,n){return E(void 0,void 0,void 0,(function(){var r,o,i,a,c;return S(this,(function(u){switch(u.label){case 0:return t((0,v.Mq)(!0)),r=V(n()),[4,(o=e.movedNode.data).isFile?_(e,r):D(e,r)];case 1:if(u.sent().error)throw t((0,v.Mq)(!1)),new Error("Cannot move file");return i="".concat(e.targetNode.id,"/").concat(o.name),a=A(e.targetNode,e.movedNode),c=w(w({},e.targetNode),{id:i,children:[],data:w(w({},o),{relativePath:a})}),t(k()),z(n()).id!==e.movedNode.id?[3,3]:(t((0,l.l0)(c)),[4,t((0,s.IW)(c))]);case 2:u.sent(),u.label=3;case 3:return t((0,v.Mq)(!1)),[2]}}))}))})),F=I("ADD_FILE",(function(e,t,n){return E(void 0,void 0,void 0,(function(){var r,o,i,a,c;return S(this,(function(u){switch(u.label){case 0:return t((0,v.Mq)(!0)),r=$(n()),o=r.data.rootPath,i=(0,p.Dy)([r.data.relativePath,e]),a={rootPath:o,filePath:i},[4,(0,b.wrappedFetch)(T.saveFile(a),{method:"POST",body:"",headers:{"Content-Type":"application/octet-stream"}})];case 1:if(204!==u.sent().response.status)throw t((0,v.Mq)(!1)),(0,d.k1)("Cannot add new file");return c={id:(0,p.Dy)([o,i]),children:[],isExpanded:!1,hasChildren:!1,data:{name:e,isFile:!0,relativePath:i,rootPath:o}},[4,t(k({selectNode:c}))];case 2:return u.sent(),[4,t((0,s.IW)(c))];case 3:return u.sent(),[4,t((0,l.l0)(c))];case 4:return u.sent(),t((0,v.Mq)(!1)),[2]}}))}))})),L=I("ADD_FOLDER",(function(e,t,n){return E(void 0,void 0,void 0,(function(){var r,o,i,a,c,s;return S(this,(function(u){switch(u.label){case 0:return t((0,v.Mq)(!0)),r=$(n()),o=r.data.rootPath,i=(0,p.Qj)((0,p.Dy)([r.data.relativePath,e])),a=(0,f.F)({rootPath:o,directoryPath:i}),[4,(0,b.wrappedFetch)("".concat(AJS.contextPath(),"/rest/scriptrunner/latest/resource-directories/directory?").concat(a),{method:"POST"})];case 1:if((c=u.sent()).error||201!==c.response.status)throw t((0,v.Mq)(!1)),(0,d.k1)("Cannot create new folder. Please try again");return s=i.substr(i.lastIndexOf("/")+1),t((0,l.Ib)()),t(k({selectNode:{id:(0,p.Dy)([o,i]),children:[],isExpanded:!0,hasChildren:!1,data:{name:s,isSelected:!0,isFile:!1,rootPath:o,relativePath:i}}})),[2]}}))}))})),R=P("EXPAND_NODE"),j=P("EXPAND_ALL"),M=P("COLLAPSE_ALL"),U=P("COLLAPSE_NODE"),Z=I("DELETE_NODE",(function(e,t,n){return E(void 0,void 0,void 0,(function(){var r,o;return S(this,(function(i){switch(i.label){case 0:return t((0,v.Mq)(!0)),r={rootPath:e.data.rootPath,resourcePath:e.data.relativePath},[4,(0,b.wrappedFetch)(T.deleteResource(r),{method:"DELETE"})];case 1:if(i.sent().error)throw t((0,v.Mq)(!1)),(0,d.k1)("Failed to delete ".concat(e.data.name," ").concat(e.data.isFile?"file":"folder",". Please try again."));return t(k()),(o=z(n()))&&0==="".concat(o.id).indexOf("".concat(e.id))?[4,t((0,s.IW)(null))]:[3,3];case 2:i.sent(),i.label=3;case 3:return[4,t((0,l.Ib)())];case 4:return i.sent(),t((0,v.Mq)(!1)),[2]}}))}))})),W=function(e,t,n){var r,o=e.tree.items[t].data.rootPath,i=(0,c.U0)(e.scriptRoots[o],t,{isExpanded:n});return w(w({},e),{scriptRoots:w(w({},e.scriptRoots),(r={},r[o]=i,r)),tree:w(w({},e.tree),{items:i.items})})},q=function(e){return function(t,n){var r,o,i=t.tree.items["/"].children[0],a="".concat(null!==(o=null==n?void 0:n.id)&&void 0!==o?o:i),c=Object.entries(t.tree.items).reduce((function(t,n){var r,o,c=x(n,2),s=c[0],u=c[1];return i!==s&&s.startsWith(a)?w(w({},t),((r={})[s]=w(w({},u),{isExpanded:e}),r)):w(w({},t),((o={})[s]=u,o))}),{}),s=w(w({},t.tree),{items:c});return w(w({},t),{tree:s,scriptRoots:w(w({},t.scriptRoots),(r={},r[i]=w({},s),r))})}},G=(0,a.reducerWithInitialState)({tree:{rootId:"",items:{}},loading:!0,scriptRoots:{}}).case(k.async.started,(function(e){return w(w({},e),{loading:!0})})).case(k.async.done,(function(e,t){var n=t.result,r=n.tree,o=n.scriptRoots;return w(w({},e),{tree:r,scriptRoots:o,loading:!1})})).case(j,q(!0)).case(M,q(!1)).case(R,(function(e,t){return W(e,t,!0)})).case(U,(function(e,t){return W(e,t,!1)})).case(l.cT,(function(e,t){if("/scriptEditor"===t.location.pathname){var n=(0,p.A6)(t.location.search),r=e.scriptRoots[n.root];if(r){if(n.path){var o=r.items[n.path];if(o)return q(!0)(e,o)}return w(w({},e),{tree:r})}}return w({},e)})),V=(0,r.P1)(u.D,(function(e){return e.fileTree})),H=(0,r.P1)([V,l.rT,l.Ub],(function(e,t,n){if(void 0===t)return e.tree;var r=e.tree.items[n];return r?(0,c.U0)(e.tree,r.id,{data:w(w({},r.data),{isSelected:!0})}):e.tree})),J=(0,r.P1)([V,l.rT,l.Ub],(function(e,t,n){return!!e.loading||(!t||!e.tree.rootId||!!e.tree.items[n])})),B=(0,r.P1)([V],(function(e){return Object.keys(e.scriptRoots)})),z=(0,r.P1)([l.Ub,H],(function(e,t){return t.items[e]})),K=(0,r.P1)([z],(function(e){return e?e.data.name:""})),$=(0,r.P1)([l.hn,z,H],(function(e,t,n){return(0,p.IG)(n,t||n.items[e])}))},60562:(e,t,n)=>{"use strict";n.d(t,{I6:()=>l,e1:()=>u.e});var r,o=n(67609),i=n(48432),a=n(59060),c=n(74593),s=n(7948),u=n(93923),l=(0,o.UY)(((r={})[s.e1]=s.gn,r[i.e1]=i.I6,r[a.e1]=a.I6,r[c.e1]=c.I6,r))},74593:(e,t,n)=>{"use strict";n.d(t,{I6:()=>c,Mq:()=>a,e1:()=>i});var r=n(86530),o=n(66494),i="loading",a=(0,r.ZP)("editor")("SET_LOADING"),c=(0,o.reducerWithInitialState)(!1).case(a,(function(e,t){return t}))},93923:(e,t,n)=>{"use strict";n.d(t,{D:()=>o,e:()=>r});var r="editor",o=function(e){return e[r]}},7948:(e,t,n)=>{"use strict";n.d(t,{HD:()=>P,Ib:()=>S,QI:()=>x,Ub:()=>_,cT:()=>b,e1:()=>h,gn:()=>T,gr:()=>D,hn:()=>A,k7:()=>w,l0:()=>E,rT:()=>k});var r=n(32245),o=n.n(r),i=n(37591),a=n(86530),c=n(66494),s=n(93923),u=n(3845),l=n(71530),d=n(54174),p=n(96355),f=n(74593),m=function(){return m=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},m.apply(this,arguments)},h="selected",g="sr-selectedScriptRoot",y=(0,a.ZP)("editor/selected"),v=(0,p.ZP)(y),b=(0,a.ZP)("")(l.nk),w=v("SELECT_SCRIPT_ROOT",(function(e,t){var n=new URLSearchParams;n.set("root",e),(0,u.i)().push("scriptEditor?".concat(n.toString()))})),E=v("SELECT_NODE",(function(e,t,n){var r=n();if(_(r)!==e.id||!r.router.location.search){e.data.isFile&&t((0,f.Mq)(!0));var o=new URLSearchParams;o.set("root",e.data.rootPath),o.set("file",e.data.relativePath),(0,u.i)().push("scriptEditor?".concat(o.toString())),e.data.isFile&&r.router.location.search||t((0,f.Mq)(!1))}})),S=v("UNSELECT_NODE",(function(e,t,n){var r=n(),o=new URLSearchParams;o.set("root",r.editor.selected.root),(0,u.i)().replace("scriptEditor?".concat(o.toString()))})),x=y("SELECT_NODE_IN_CURRENT_ROOT"),P=y("SET_ROOT_PATH"),I=(0,d.A6)(window.location.search),C={root:o().get()["sr-selectedScriptRoot"]||I.root,path:I.path},T=(0,c.reducerWithInitialState)(C).case(w.async.done,(function(e,t){return m(m({},e),{root:t.params})})).case(E.async.done,(function(e,t){return m(m({},e),{root:t.params.data.rootPath,path:t.params.data.relativePath})})).case(S.async.done,(function(e){return m(m({},e),{path:void 0})})).case(b,(function(e,t){if("/scriptEditor"!==t.location.pathname)return m({},e);var n=(0,d.A6)(t.location.search);return n.root?(o().set(g,n.root),m(m({},e),n)):m({},e)})).case(P,(function(e,t){return m(m({},e),{root:t})})).case(x,(function(e,t){return m(m({},e),{path:t})})),O=(0,i.P1)(s.D,(function(e){return e[h]})),k=(0,i.P1)([O],(function(e){return e.path})),A=(0,i.P1)([O],(function(e){return e.root})),_=(0,i.P1)([O],(function(e){return(0,d.Qj)((0,d.Dy)([e.root||"",e.path||""]))})),D=(0,i.P1)([O],(function(e){return e.root}))},54174:(e,t,n)=>{"use strict";n.d(t,{$8:()=>p,A6:()=>m,Dy:()=>l,IG:()=>s,ME:()=>c,Qj:()=>d,oL:()=>u});var r=n(96288),o=n.n(r),i=function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,i=n.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(r=i.next()).done;)a.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return a},a=function(e,t,n){if(n||2===arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))},c=function(e){return(e||"").trim().replace(/(\s+)?\/(\s+)?/g,"/")},s=function(e,t){if(t.data.isFile){var n="".concat(t.id),r=n.substr(0,n.lastIndexOf("/"));return e.items[r]}return t},u=function(e,t,n){var r=s(e,t),o="".concat(r.id),i=l([o,n]);return void 0!==e.items[i]},l=function(e){return f(o().join.apply(o(),a([],i(e),!1)))},d=function(e){return"/"===e.charAt(e.length-1)?e.substr(0,e.length-1):e},p=function(e,t){var n=d(l(["".concat(t.id),"../"]))||e.rootId;return e.items[n]},f=function(e){var t=/^\\\\\?\\/.test(e),n=/[^\u0000-\u0080]+/.test(e);return t||n?e:e.replace(/\\/g,"/")},m=function(e){var t=new URLSearchParams(e);return{root:t.get("root")||"",path:t.get("file")||void 0}}},31269:(e,t,n)=>{"use strict";n.d(t,{L:()=>p});var r,o=n(63844),i=n(18390),a=n(76416),c=n(74729),s=function(e,t){return Object.defineProperty?Object.defineProperty(e,"raw",{value:t}):e.raw=t,e},u=function(e,t,n,r){return new(n||(n=Promise))((function(o,i){function a(e){try{s(r.next(e))}catch(e){i(e)}}function c(e){try{s(r.throw(e))}catch(e){i(e)}}function s(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(a,c)}s((r=r.apply(e,t||[])).next())}))},l=function(e,t){var n,r,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:c(0),throw:c(1),return:c(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function c(i){return function(c){return function(i){if(n)throw new TypeError("Generator is already executing.");for(;a;)try{if(n=1,r&&(o=2&i[0]?r.return:i[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,i[1])).done)return o;switch(r=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return a.label++,{value:i[1],done:!1};case 5:a.label++,r=i[1],i=[0];continue;case 7:i=a.ops.pop(),a.trys.pop();continue;default:if(!(o=a.trys,(o=o.length>0&&o[o.length-1])||6!==i[0]&&2!==i[0])){a=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){a.label=i[1];break}if(6===i[0]&&a.label<o[1]){a.label=o[1],o=i;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(i);break}o[2]&&a.ops.pop(),a.trys.pop();continue}i=t.call(e,a)}catch(e){i=[6,e],r=0}finally{n=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,c])}}},d=function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,i=n.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(r=i.next()).done;)a.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return a},p=function(e){var t,n=e.src,r=d((0,o.useState)(!1),2),i=r[0],s=r[1],p=d((0,o.useState)(!1),2),m=p[0],h=p[1],g=d((0,o.useState)(null),2),y=g[0],v=g[1],b=d((0,o.useState)(null),2),w=b[0],E=b[1];return(0,o.useEffect)((function(){s(!1),u(void 0,void 0,void 0,(function(){return l(this,(function(t){return e.onLoadStart&&e.onLoadStart(),[2,new Promise((function(e,t){return u(void 0,void 0,void 0,(function(){var r,o,i,a,s,u,d;return l(this,(function(l){switch(l.label){case 0:return[4,(0,c.wrappedFetch)(n)];case 1:return r=l.sent(),o=r.result,i=r.response,a=r.error,s=r.errorResult,a?400===i.status?[2,t(s.errorMessages.join(", "))]:[2,t("Could not retrieve execution history data: ".concat(a))]:"string"!=typeof o?[3,2]:(e(o),[3,4]);case 2:return u=new FileReader,[4,o];case 3:d=l.sent(),u.readAsDataURL(d),u.onloadend=function(){e(u.result)},u.onabort=function(){return t()},u.onerror=function(){return t()},l.label=4;case 4:return[2]}}))}))}))]}))})).then((function(e){v(e),h(!1)})).catch((function(e){h(!0),E(e)})).finally((function(){s(!0),e.onLoadComplete&&e.onLoadComplete()}))}),[n]),i?m?o.createElement("div",{className:"aui-message aui-message-error",style:{width:691}},o.createElement("p",null,w)):o.createElement("img",{src:y,alt:null!==(t=e.alt)&&void 0!==t?t:"Execution history chart"}):o.createElement(f,null,o.createElement(a.LoadingSpinner,{size:"large"}))},f=i.Z.div(r||(r=s(["\n    width: ","px;\n    height: ","px;\n    background-color: lightgray;\n    margin-bottom: 5px;\n"],["\n    width: ","px;\n    height: ","px;\n    background-color: lightgray;\n    margin-bottom: 5px;\n"])),691,254)},96226:(e,t,n)=>{"use strict";n.d(t,{AK:()=>_,B6:()=>L,Ee:()=>P,GM:()=>w,Px:()=>b,R6:()=>E,VL:()=>k,Wg:()=>S,YW:()=>I,Ze:()=>h,aX:()=>F,gV:()=>D,hg:()=>x,iD:()=>O,k$:()=>C,lm:()=>g,r4:()=>R,rW:()=>Z,rh:()=>N,uo:()=>y,wN:()=>v,x3:()=>T,xS:()=>A});var r=n(86530),o=n(96355),i=n(91845),a=n(71530),c=n(39507),s=n(77937),u=n(74729),l=function(){return l=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},l.apply(this,arguments)},d=function(e,t,n,r){return new(n||(n=Promise))((function(o,i){function a(e){try{s(r.next(e))}catch(e){i(e)}}function c(e){try{s(r.throw(e))}catch(e){i(e)}}function s(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(a,c)}s((r=r.apply(e,t||[])).next())}))},p=function(e,t){var n,r,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:c(0),throw:c(1),return:c(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function c(i){return function(c){return function(i){if(n)throw new TypeError("Generator is already executing.");for(;a;)try{if(n=1,r&&(o=2&i[0]?r.return:i[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,i[1])).done)return o;switch(r=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return a.label++,{value:i[1],done:!1};case 5:a.label++,r=i[1],i=[0];continue;case 7:i=a.ops.pop(),a.trys.pop();continue;default:if(!(o=a.trys,(o=o.length>0&&o[o.length-1])||6!==i[0]&&2!==i[0])){a=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){a.label=i[1];break}if(6===i[0]&&a.label<o[1]){a.label=o[1],o=i;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(i);break}o[2]&&a.ops.pop(),a.trys.pop();continue}i=t.call(e,a)}catch(e){i=[6,e],r=0}finally{n=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,c])}}},f=(0,r.ZP)("BEHAVIOUR_EDIT"),m=(0,o.ZP)(f),h=m("LOAD_ROLES",(function(){return d(void 0,void 0,void 0,(function(){return p(this,(function(e){switch(e.label){case 0:return[4,(0,u.wrappedFetch)("".concat(AJS.contextPath(),"/rest/api/2/role"))];case 1:return[2,e.sent().result]}}))}))})),g=m("LOAD_FIELDS",(function(){return d(void 0,void 0,void 0,(function(){return p(this,(function(e){switch(e.label){case 0:return[4,(0,u.wrappedFetch)("".concat(AJS.contextPath(),"/rest/api/2/field"))];case 1:return[2,e.sent().result]}}))}))})),y=m("LOAD_WORKFLOWS",(function(){return d(void 0,void 0,void 0,(function(){return p(this,(function(e){switch(e.label){case 0:return[4,(0,u.wrappedFetch)("".concat(AJS.contextPath(),"/rest/api/2/workflow"))];case 1:return[2,e.sent().result]}}))}))})),v=m("LOAD_SNIPPETS",(function(e,t,n){return d(void 0,void 0,void 0,(function(){var e;return p(this,(function(t){switch(t.label){case 0:return(null==(e=n().behaviourEdit.meta.snippets)?void 0:e.length)?[2,e]:[4,(0,u.wrappedFetch)("".concat(AJS.contextPath(),"/rest/scriptrunner/behaviours/latest/admin/snippets"))];case 1:return[2,t.sent().result]}}))}))})),b=m("FETCH_BEHAVIOUR",(function(e,t){return d(void 0,void 0,void 0,(function(){var n;return p(this,(function(r){switch(r.label){case 0:return[4,(0,u.wrappedFetch)("".concat(AJS.contextPath(),"/rest/scriptrunner/behaviours/latest/admin/behaviour/").concat(e))];case 1:return(n=r.sent()).response.status>300&&(404===n.response.status?(0,s.Sj)("Behaviour not found"):(0,s.Sj)("Behaviour could not be loaded"),t((0,a.VF)("/behaviours"))),[2,n.result]}}))}))})),w=m("INIT_EDIT",(function(e,t,n){return d(void 0,void 0,void 0,(function(){return p(this,(function(r){switch(r.label){case 0:return[4,Promise.all([t(h()),t(g()),t(y()),t(v()),t(b(e))])];case 1:return r.sent(),[4,t(N({name:n().behaviourEdit.editState.guideWorkflow}))];case 2:return r.sent(),[2,!0]}}))}))})),E=f("CLEAR_BEHAVIOUR_EDIT"),S=f("SET_BEHAVIOUR_PROPERTY"),x=f("UPDATE_SCRIPT"),P=f("UPDATE_NAME"),I=f("UPDATE_DESCRIPTION"),C=f("UPDATE_INITIALISER"),T=f("DELETE_INITIALISER"),O=f("UPDATE_USE_VALIDATOR_PLUGIN"),k=f("UPDATE_BEHAVIOUR_DISABLED"),A=f("ADD_FIELD"),_=f("DELETE_FIELD"),D=f("DELETE_SERVER_SIDE_SCRIPT"),N=m("UPDATE_GUIDE_WORKFLOW",(function(e){var t=e.name;return d(void 0,void 0,void 0,(function(){var e;return p(this,(function(n){switch(n.label){case 0:return e=[{name:t}],[4,(r=t,d(void 0,void 0,Promise,(function(){var e,t;return p(this,(function(n){switch(n.label){case 0:return e={actions:[],steps:[]},Boolean(r)?[4,(0,u.wrappedFetch)("".concat(AJS.contextPath(),"/rest/scriptrunner/behaviours/latest/admin/workflow?").concat((0,c.F)({workflowName:r})))]:[3,2];case 1:return(t=n.sent()).response.ok?[2,t.result]:((0,s.Sj)("There was a problem fetching the workflow, perhaps it was deleted. Check server logs."),[2,e]);case 2:return[2,e]}}))})))];case 1:return[2,l.apply(void 0,e.concat([n.sent()]))]}var r}))}))})),F=f("ADD_CONDITION"),L=f("DELETE_CONDITION"),R=f("UPDATE_CONDITION"),j=function(e){return!(!e.script&&!e.scriptPath)},M=function(e,t){var n=e&&e.validatorScript&&j(e.validatorScript)||t&&j(t)?l(l({},(e||{}).validatorScript),t):void 0;return l(l({},e),{validatorScript:n})},U=function(e,t){var n=function(e){return function(t){return M(t,e[t.id])}}(t),r=function(e,t){var n=M(e,t);if(n.validatorScript)return n}(e.init,t.initializer);return l(l({},e),{init:r,fields:e.fields.map(n)})},Z=m("SAVE_ALL",(function(e,t,n){return d(void 0,void 0,void 0,(function(){var e,r,o;return p(this,(function(a){switch(a.label){case 0:return e=n(),r=e.behaviourEdit.editState,o=e.edit,[4,(0,u.wrappedFetch)("".concat(AJS.contextPath(),"/rest/scriptrunner/behaviours/latest/admin/behaviour"),{method:"POST",body:JSON.stringify(U(r,o))})];case 1:return a.sent().response.ok?(window.require(["aui/flag"],(function(e){e({type:"success",title:"Saved",persistent:!1,close:"auto",body:"Successfully saved."})})),t((0,i.cT)({id:"".concat(r.id),name:r.name})),t((0,i.K$)({id:"".concat(r.id),description:r.description})),[2,{success:!0,newState:n().behaviourEdit.editState}]):((0,s.Sj)("There was a problem saving the behaviour, please copy any code externally, refresh the page and try again"),[2,{success:!1,newState:n().behaviourEdit.editState}])}}))}))}))},54775:(e,t,n)=>{"use strict";n.d(t,{a:()=>i});var r,o=function(e,t){return Object.defineProperty?Object.defineProperty(e,"raw",{value:t}):e.raw=t,e},i=n(18390).Z.a(r||(r=o(["\n    margin-left: 3px;\n    cursor: pointer;\n"],["\n    margin-left: 3px;\n    cursor: pointer;\n"])))},68814:(e,t,n)=>{"use strict";n.d(t,{a:()=>A});var r=n(63844),o=n(72142),i=n(58844),a=n(62629),c=(n(16662),n(20102)),s=n.n(c),u=n(65475),l=(n(5667),jQuery),d=function(e,t){return e.filter((function(e){return e.filepath===t}))},p=function(e,t){var n=/[^a-zA-Z]/g,r=/[^0-9]/g,o=function(e,t){return e.toLowerCase().replace(t,"")};return o(e.filename,n)===o(t.filename,n)?parseInt(o(e.filename,r),10)-parseInt(o(t.filename,r),10):o(e.filename,n)>o(t.filename,n)?1:-1},f=function(e,t){return"FILE"===e.filetype&&"DIRECTORY"===t.filetype?1:"DIRECTORY"===e.filetype&&"FILE"===t.filetype?-1:"FILE"===e.filetype&&"FILE"===t.filetype||"DIRECTORY"===e.filetype&&"DIRECTORY"===t.filetype?p(e,t):void 0},m=function(e,t,n){void 0===n&&(n="filename");var r={datumTokenizer:s().tokenizers.obj.whitespace(n),queryTokenizer:s().tokenizers.whitespace,sorter:f,prefetch:{url:(0,u.S)()+"/rest/scriptrunner/latest/scriptSearch/"+e,cache:!1}};return t&&(delete r.prefetch,r.local=t),new(s())(r)},h=function(e){var t=e.filename,n=e.filepath,r=e.filetype;if(!n)return t;var o=l("<div>");o.addClass("adaptavist-sr");var i=l("<div>",{class:"script-picker-suggestion-line"}),a=l("<span>",{class:"aui-icon aui-icon-small"}),c=l("<div>",{class:"script-picker-suggestion-filename"});return"DIRECTORY"===r?(o.addClass("script-picker-directory-suggestion"),a.addClass("aui-iconfont-devtools-folder-open script-picker-directory-icon").text("")):a.addClass("aui-iconfont-doc script-picker-file-icon").text(""),c.append(a).append(" ".concat(t)),i.append(c).append(n),o.append(i),o},g=function(e,t,n,r){e.attr("autocomplete","off"),l(e).typeahead("destroy");var o=m(n,t),i=m(n,t,"filepath");l(e).typeahead({hint:!1,highlight:!1,minLength:1,classNames:{menu:"tt-script-picker-menu",input:"tt-script-picker-input",hint:"tt-script-picker-selectable",suggestion:"tt-script-picker-suggestion",cursor:"tt-script-picker-cursor"}},{name:"scriptFiles",display:function(e){var t=e.filename,n=e.filepath,r=t;return"/"!==n&&(r=n+t),"DIRECTORY"===e.filetype&&(r+="/"),r},source:function(e,t){if(e.endsWith("/"))i.search(e,(function(n){e.endsWith("/")&&t(d(n,e))}));else if(e.includes("/")){var n=e.substring(e.lastIndexOf("/")+1);o.search(n,(function(n){t(d(n,e.substring(0,e.lastIndexOf("/")+1)))}))}else o.search(e,(function(e){t(function(e){return e.filter((function(e){var t=e.filetype,n=e.filepath;return"FILE"===t||"DIRECTORY"===t&&"/"===n}))}(e))}))},limit:1e3,templates:{suggestion:h}}),e.off("typeahead:select").on("typeahead:select",(function(t,n){if("DIRECTORY"!==n.filetype)r?setTimeout((function(){r(e.val().toString())}),0):l("input.scriptFileSource").trigger("paste");else var o=setInterval((function(){if(!l(t.target).siblings("div.tt-script-picker-menu").hasClass("tt-open")){var e=l(t.target).val();l(t.target).typeahead("val",e).focus(),clearInterval(o)}}),10)})),e.off("typeahead:active").on("typeahead:active",(function(e,t){o.initialize(!0),i.initialize(!0)}))},y=function(e){if(l(".typeahead")[0]){var t=l("input.typeahead");g(t,e)}};l((function(){y(),l(document).on("/start",(function(e,t){document.URL.includes("servlet/scriptrunner/console")||y(t)}))}));var v,b=n(3317),w=n(66661),E=n(59482),S=n(91688),x=n(74729),P=(v=function(e,t){return v=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},v(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}v(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),I=function(){return I=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},I.apply(this,arguments)},C=function(e,t,n,r){return new(n||(n=Promise))((function(o,i){function a(e){try{s(r.next(e))}catch(e){i(e)}}function c(e){try{s(r.throw(e))}catch(e){i(e)}}function s(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(a,c)}s((r=r.apply(e,t||[])).next())}))},T=function(e,t){var n,r,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:c(0),throw:c(1),return:c(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function c(i){return function(c){return function(i){if(n)throw new TypeError("Generator is already executing.");for(;a;)try{if(n=1,r&&(o=2&i[0]?r.return:i[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,i[1])).done)return o;switch(r=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return a.label++,{value:i[1],done:!1};case 5:a.label++,r=i[1],i=[0];continue;case 7:i=a.ops.pop(),a.trys.pop();continue;default:if(!(o=a.trys,(o=o.length>0&&o[o.length-1])||6!==i[0]&&2!==i[0])){a=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){a.label=i[1];break}if(6===i[0]&&a.label<o[1]){a.label=o[1],o=i;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(i);break}o[2]&&a.ops.pop(),a.trys.pop();continue}i=t.call(e,a)}catch(e){i=[6,e],r=0}finally{n=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,c])}}},O=AJS.$,k=function(e){function t(t,r){var o=e.call(this,t,r)||this;return o.handleVisibilityChange=function(e){e&&o.props.doCompile(o.props.initialValue)},o.addCompileContextAndOpenTab=function(){return C(o,void 0,void 0,(function(){var e;return T(this,(function(t){switch(t.label){case 0:return[4,(0,x.wrappedFetch)("".concat(AJS.contextPath(),"/rest/scriptrunner/latest/codeinsight/contexts"),{body:JSON.stringify({path:this.props.initialValue,compileContextDescriptor:this.props.compileContextDescriptor}),method:"POST"})];case 1:return t.sent().response&&((e=new URL(window.location.href)).pathname="".concat(AJS.contextPath(),"/plugins/servlet/scriptrunner/admin/scriptEditor"),e.searchParams.set("file",this.props.initialValue),window.open(e.href,"sr-script-editor")),[2]}}))}))},n(58297),o}return P(t,e),t.prototype.componentDidUpdate=function(e,t){var n=this.props.initialValue;e.initialValue!==n&&this.props.doCompile(n)},t.prototype.componentDidMount=function(){var e=this,t=this.props.initialValue;t&&this.props.doCompile(t),g(O(this.el),null,this.props.lang,this.props.doCompile),O(this.el).on("typeahead:close",(function(){return e.props.onChange(e.el.value)}))},t.prototype.render=function(){var e,t,n=this,o=this.props,c=o.expandedRight,s=o.initialValue,u=o.readonly,l=o.description,d="string"==typeof l?r.createElement("div",{style:{clear:"both"},className:"description",dangerouslySetInnerHTML:{__html:(0,i.sanitize)(l)}}):r.createElement("div",{style:{clear:"both"},className:"description"},l);return r.createElement(a.Z,{onChange:this.handleVisibilityChange},r.createElement("div",{className:"script-picker"},r.createElement("input",{type:"text",onChange:function(e){return n.props.doCompile(e.currentTarget.value)},defaultValue:s,style:{maxWidth:c?"100%":750,backgroundColor:u?"lightgray":"",minWidth:500},placeholder:"Start typing to search for files...",className:"text long-field scriptFileSource",readOnly:u,ref:function(e){return n.el=e}}),this.props.initialValue&&r.createElement("a",{className:"action",onClick:this.addCompileContextAndOpenTab},r.createElement("span",{className:"aui-icon aui-icon-small aui-iconfont-edit"},"Edit")),r.createElement("span",{style:c?{float:"right"}:{display:"inline-block",marginTop:6}},this.props.ragstatus&&r.createElement(b.w,{ragstatus:this.props.ragstatus})),r.createElement("div",{className:"description scriptInfo",style:c?{float:"left"}:{clear:"both"}},(null===(e=this.el)||void 0===e?void 0:e.value)&&this.props.markers&&r.createElement("ul",{className:"script-errors"},_(this.props.markers,null===(t=this.el)||void 0===t?void 0:t.value).map((function(e,t){return r.createElement("li",{key:t,className:"sr-".concat(w.MarkerSeverity[e.severity].toLowerCase()),dangerouslySetInnerHTML:{__html:(0,i.sanitize)(e.message)}})})))),d))},t.defaultProps={testMode:!1},t}(r.PureComponent),A=(0,o.$j)((function(e,t){var n=t.editorId;return{ragstatus:(0,E.up)(n)(e),markers:(0,E.pz)(n)(e)}}),(function(e,t){var n=t.editorId,r=t.compileContextDescriptor,o=t.lang,i=t.testMode,a=D(e,i);return{doCompile:function(e){return a({editorId:n,compileContextDescriptor:r,scriptFile:e,language:o})}}}))(k),_=function(e,t){return null==e?void 0:e.map((function(e){var n=e.startLineNumber;return I(I({},e),{severity:e.severity,message:"".concat(t,":").concat(n,"\n").concat(e.messageHTML," @ line ").concat(n,", column ").concat(e.startColumn,".")})}))},D=function(e,t,n){void 0===n&&(n=E.VN);var r=(0,S.debounce)((function(t){return e(n(t))}),t?0:1e3);return function(t){e((0,E.lH)({id:t.editorId,value:t.script||t.scriptFile})),r(t)}}},3317:(e,t,n)=>{"use strict";n.d(t,{w:()=>u});var r,o=n(63844),i=n(18390),a=n(78417),c=function(e,t){return Object.defineProperty?Object.defineProperty(e,"raw",{value:t}):e.raw=t,e},s={green:"sr-ok",amber:"sr-warning",red:"sr-error",waiting:""},u=function(e){var t=e.spinnerAppearance,n=e.ragstatus;return n?"waiting"===n?o.createElement(d,{className:"rag","data-qa":"wait"},o.createElement(a.Z,{size:"small",appearance:t})):o.createElement(d,{className:"rag ".concat(s[n]),title:l(n)}," "):null},l=function(e){switch(e){case"red":return"Static type checking errors found.";case"amber":return"Static type checking warnings found.";case"green":return"No errors found.";default:return null}},d=i.Z.span(r||(r=c(["\n    display: inline-block;\n"],["\n    display: inline-block;\n"])))},74785:(e,t,n)=>{"use strict";n.d(t,{Z:()=>at});var r=n(63844),o=n(96253),i=n(72142),a=n(97461),c=n(9827),s=n(28912),u=n(2610),l=n(3317),d=n(18390),p=n(14849),f=n(14438),m=n(21306),h=n(65202),g=n(60047),y=n(59462),v=function(e,t){return Object.defineProperty?Object.defineProperty(e,"raw",{value:t}):e.raw=t,e},b=function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,i=n.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(r=i.next()).done;)a.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return a},w=n(22106),E=function(e){var t=e.snippet.name,n=b((0,r.useState)(!1),2),o=n[0],i=n[1];return r.createElement(L,{isCopied:o,onMouseLeave:function(){return i(!1)},onClick:function(){i(!0),e.onClick()},"data-qa":"codeSnippet"},r.createElement(P,{id:"snippet-copy-".concat(t),isCopied:o},o?"Copied To Clipboard!":"Click To Copy"),r.createElement(F,{id:"snippet-".concat(t)},r.createElement(w,{searchWords:e.searchTerms,textToHighlight:e.snippet.name})))},S=(0,y.F4)(I||(I=v(["\n 0% {\n    opacity: 0;\n  }\n  100% {\n    opacity: 1;\n  }\n"],["\n 0% {\n    opacity: 0;\n  }\n  100% {\n    opacity: 1;\n  }\n"]))),x=(0,y.F4)(C||(C=v(["\n  0% {\n    opacity: 1;\n  }\n  \n 99% {\n    opacity: 0;\n  }\n  \n  100% {\n    display: none;\n  }\n"],["\n  0% {\n    opacity: 1;\n  }\n  \n 99% {\n    opacity: 0;\n  }\n  \n  100% {\n    display: none;\n  }\n"]))),P=d.Z.div(T||(T=v(["\n    padding-left: 8px;\n    padding-right: 8px;\n    border-radius: 0px 0px 3px 3px;\n    width: ",";\n    text-align: center;\n    top: 0;\n    left: calc(50% - ",");\n    color: white;\n    background: ",";\n    position: absolute;\n"],["\n    padding-left: 8px;\n    padding-right: 8px;\n    border-radius: 0px 0px 3px 3px;\n    width: ",";\n    text-align: center;\n    top: 0;\n    left: calc(50% - ",");\n    color: white;\n    background: ",";\n    position: absolute;\n"])),(function(e){return e.isCopied?"150px":"100px"}),(function(e){return e.isCopied?"75px":"50px"}),(function(e){return e.isCopied?"#344563":"#505F79"}));P.defaultProps={className:"copy-text"};var I,C,T,O,k,A,_,D,N,F=d.Z.span(O||(O=v([""],[""]))),L=d.Z.div(k||(k=v(["\n    background: white;\n    position: relative;\n    padding: 6px 8px;\n    border-radius: 3px;\n    margin-top: 3px;\n    margin-right: 3px;\n    margin-bottom: 3px;\n    border: 1px solid #ebecf0;\n    transition: all 400ms;\n    &:hover {\n        cursor: pointer;\n        border: 1px solid #b3bac5;\n        ","\n        ","\n        > .copy-text {\n            display: block;\n            animation: "," 400ms ease-out;\n        }\n    }\n    > .copy-text {\n        animation: "," 500ms linear;\n        display: none;\n    }\n"],["\n    background: white;\n    position: relative;\n    padding: 6px 8px;\n    border-radius: 3px;\n    margin-top: 3px;\n    margin-right: 3px;\n    margin-bottom: 3px;\n    border: 1px solid #ebecf0;\n    transition: all 400ms;\n    &:hover {\n        cursor: pointer;\n        border: 1px solid #b3bac5;\n        ","\n        ","\n        > .copy-text {\n            display: block;\n            animation: "," 400ms ease-out;\n        }\n    }\n    > .copy-text {\n        animation: "," 500ms linear;\n        display: none;\n    }\n"])),(0,g.yy)(),(function(e){return!e.isCopied&&"transform: translateY(-2px);"}),S,x),R=n(81995),j=(A=function(e,t){return A=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},A(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}A(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),M=function(e,t){return Object.defineProperty?Object.defineProperty(e,"raw",{value:t}):e.raw=t,e},U=function(e){function t(t){var n=e.call(this,t)||this;return n.state={searchTerms:[]},n.handleSearchQueryUpdate=function(e){var t=e.target.value;t&&t.length>0?n.setState({searchTerms:t.toLowerCase().split(" ")}):n.setState({searchTerms:[]})},n.afterInputElement=function(){return n.state.searchTerms&&n.state.searchTerms.length>0?r.createElement("span",{style:{cursor:"pointer"},onClick:function(){n.setState({searchTerms:[]}),n.textField.value=""}},r.createElement(m.Z,{label:"Clear"})):r.createElement(h.Z,{label:"Search snippets"})},n.filteredSnippets=function(){var e=n.state.searchTerms;return e&&e.length>0?n.props.snippets.filter((function(t){var n=t.name.toLowerCase();return e.find((function(e){return n.indexOf(e.toLowerCase())>=0}))})):n.props.snippets},n}return j(t,e),t.prototype.render=function(){var e=this;return r.createElement(G,null,r.createElement(p.Z,{ref:function(t){return e.textField=t},onChange:this.handleSearchQueryUpdate,name:"event-handlers",placeholder:"Search for snippets",elemAfterInput:this.afterInputElement(),isCompact:!0}),r.createElement(q,null,this.filteredSnippets().map((function(t){return r.createElement(E,{key:t.name,searchTerms:e.state.searchTerms,onClick:function(){var n,r;(0,R.J)(t.code),null===(r=(n=e.props).onCopy)||void 0===r||r.call(n)},snippet:t})}))))},t}(r.PureComponent),Z=function(e){return r.createElement(f.Z,{duration:500,height:e.isCollapsed?0:"auto",style:{width:"100%"}},r.createElement(V,null,r.createElement(U,{snippets:e.snippets,onCopy:e.onCopy})))},W=function(e){return r.createElement("a",{className:"action",onClick:e.onClick},"Show snippets"," ",r.createElement("span",{className:"aui-icon aui-icon-small aui-iconfont-arrows-".concat(e.isCollapsed?"down":"up")}))},q=d.Z.div(_||(_=M(["\n    max-height: 150px;\n    overflow-y: auto;\n"],["\n    max-height: 150px;\n    overflow-y: auto;\n"]))),G=d.Z.div(D||(D=M(["\n    background: ",";\n    colour: ",";\n    border-radius: 3px;\n    padding: 8px;\n"],["\n    background: ",";\n    colour: ",";\n    border-radius: 3px;\n    padding: 8px;\n"])),g.Ro,g.NK),V=d.Z.div(N||(N=M(["\n    padding-top: 10px;\n    padding-bottom: 10px;\n"],["\n    padding-top: 10px;\n    padding-bottom: 10px;\n"]))),H=n(66661),J=n(59482),B=n(49159),z=n(81314),K=n(94194),$=n(87483),Y=n(77510),Q=n(57700),X=n(78941),ee=n(40928),te=n(87333),ne=n(36754),re=n(98583),oe=function(e,t){return Object.defineProperty?Object.defineProperty(e,"raw",{value:t}):e.raw=t,e},ie=function(){return ie=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},ie.apply(this,arguments)},ae=function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,i=n.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(r=i.next()).done;)a.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return a},ce=[{label:"Binding information"},{label:"Keyboard shortcuts"}],se=function(e){var t=e.isOpen,n=e.compileContextDescriptor,o=e.onClose,i=ae(ce,2),a=i[0],c=i[1],s=ae(r.useState(a),2),u=s[0],l=s[1],d=ae((0,ne.T)(),2),p=d[0],f=d[1],m=f.isLoading,h=f.isError,g=f.data;return(0,r.useEffect)((function(){t&&p({compileContextDescriptor:n})}),[n,t]),r.createElement(z.Z,null,t&&r.createElement(K.Z,{width:"x-large",autoFocus:!1,onClose:o},r.createElement(ue,null,r.createElement(le,null,"Editor Help"),r.createElement(de,null,r.createElement(X.Z,{tabs:ce,selected:u,onSelect:function(e){return l(e)}}))),r.createElement($.Z,null,r.createElement(pe,{ref:ee.o},u===a&&r.createElement(re.T,{isLoading:m,isError:h,bindingInformation:g}),u===c&&r.createElement(ve,null,(0,te.rY)("jira").body))),r.createElement(Y.Z,null,[{text:"Close",onClick:o}].map((function(e,t){return r.createElement(B.Z,ie({},e,{autoFocus:0===t,appearance:0===t?"primary":"subtle"}),e.text)})).reverse())))},ue=d.Z.div(fe||(fe=oe(["\n    margin: 24px 0 0 24px;\n"],["\n    margin: 24px 0 0 24px;\n"])));ue.displayName="HeaderContainer";var le=(0,d.Z)(Q.Z)(me||(me=oe(["\n    padding-left: 6px;\n"],["\n    padding-left: 6px;\n"])));le.displayName="ModalTitleContainer";var de=d.Z.div(he||(he=oe(["\n    margin-top: 20px;\n"],["\n    margin-top: 20px;\n"])));de.displayName="TabContainer";var pe=d.Z.div(ge||(ge=oe(["\n    margin: 16px 8px;\n"],["\n    margin: 16px 8px;\n"])));pe.displayName="TabContent";var fe,me,he,ge,ye,ve=d.Z.div(ye||(ye=oe(["\n    line-height: 2.1;\n"],["\n    line-height: 2.1;\n"])));ve.displayName="KeyboardShortcutsContainer";var be=n(86936),we=n(4248),Ee=n(39507),Se=n(7011),xe=n(58844),Pe=n(7230),Ie=n(21705),Ce=n(93738),Te=n(90917),Oe=n(29284),ke=n(74729),Ae=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},e(t,n)};return function(t,n){if("function"!=typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),_e=function(){return _e=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},_e.apply(this,arguments)},De=function(e,t,n,r){return new(n||(n=Promise))((function(o,i){function a(e){try{s(r.next(e))}catch(e){i(e)}}function c(e){try{s(r.throw(e))}catch(e){i(e)}}function s(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(a,c)}s((r=r.apply(e,t||[])).next())}))},Ne=function(e,t){var n,r,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:c(0),throw:c(1),return:c(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function c(i){return function(c){return function(i){if(n)throw new TypeError("Generator is already executing.");for(;a;)try{if(n=1,r&&(o=2&i[0]?r.return:i[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,i[1])).done)return o;switch(r=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return a.label++,{value:i[1],done:!1};case 5:a.label++,r=i[1],i=[0];continue;case 7:i=a.ops.pop(),a.trys.pop();continue;default:if(!(o=a.trys,(o=o.length>0&&o[o.length-1])||6!==i[0]&&2!==i[0])){a=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){a.label=i[1];break}if(6===i[0]&&a.label<o[1]){a.label=o[1],o=i;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(i);break}o[2]&&a.ops.pop(),a.trys.pop();continue}i=t.call(e,a)}catch(e){i=[6,e],r=0}finally{n=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,c])}}};const Fe=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.state={disabled:t.props.disabled},t.toggleEnabled=function(){return De(t,void 0,void 0,(function(){var e,t,n,r,o;return Ne(this,(function(i){switch(i.label){case 0:return e=!this.state.disabled,this.setState({disabled:e}),t=new URLSearchParams(window.location.search),n={workflowName:t.get("workflowName"),workflowStep:t.get("workflowStep"),workflowTransition:t.get("workflowTransition"),descriptorTab:t.get("descriptorTab"),functionId:this.props.functionId,count:this.props.count},[4,(0,ke.fetchJson)("".concat(AJS.contextPath(),"/rest/scriptrunner-jira/latest/workflow/").concat(e?"disable":"enable","?").concat((0,Ee.F)(n)),{method:"POST"})];case 1:return(r=i.sent()).response.ok||(o="message"in r.errorResult?r.errorResult.message:"Unspecified error",(0,Se.$7)(null,{body:"An error occurred disabling/enabling: <b>".concat(o,"</b>. Usually this means someone else has edited the workflow. Please refresh."),type:"error",title:"Error",close:"manual"}),this.setState({disabled:!e})),[2]}}))}))},t}return Ae(t,e),t.prototype.render=function(){var e=this.props,t=e.innerHtml,n=e.functionId,o=e.count,i=this.state.disabled,a=new URLSearchParams(window.location.search),c=a.get("workflowMode"),s=null!==o&&a.get("highlight")===o;return r.createElement("div",{style:_e(_e({},i?we._9:{}),s?{backgroundColor:Ce.r6,padding:"10px",margin:"5 10 5 0"}:{})},s&&r.createElement("div",{style:{display:"inline-block",verticalAlign:"top",width:30}},r.createElement(Ie.Z,{label:"error",primaryColor:Ce.rt})),r.createElement("div",{style:s?{display:"inline-block"}:{}},r.createElement("div",{dangerouslySetInnerHTML:{__html:(0,xe.sanitize)(t)}}),"draft"===c&&r.createElement("div",{style:{marginTop:5,fontSize:"small"}},r.createElement(Pe.Z,{content:"Don't forget to publish the workflow after enabling or disabling!"},r.createElement("a",{className:"action",onClick:this.toggleEnabled},i?"Enable":"Disable"))),r.createElement("div",{style:{display:"flex",alignItems:"center",marginTop:10}},r.createElement(Oe.S,{functionId:n}),r.createElement(Te.P,{functionId:n}))))},t}(r.Component);n(5667);n(66573);var Le=jQuery,Re=function(){Me()},je=function(e){for(var t=e.parent().closest(".criteria-item"),n=[];null!==t&&0!==t.length;)n.unshift(t.index()+1),t=t.parent().closest(".criteria-item");return n.join(".")},Me=function(){var e=new URLSearchParams(window.location.search).has("highlight");Le(".sr-view-workflow-function").each((function(t,n){var o,i,a=Le(n),c=a.data("function-info"),s=a.closest("li").find("span.aui-iconfont-edit").closest("a").attr("href"),u=new URLSearchParams(s);be.render(r.createElement(Fe,{functionId:c.functionId,disabled:c.disabled,innerHtml:(o=c.cannedScriptPreview,i=document.createElement("textarea"),i.innerHTML=o,i.value),count:e?je(a):u.get("count")}),n)}))};Le((function(){AJS.toInit(Re),Le(document).on("/start",(function(){Re()}))}));var Ue,Ze,We,qe=n(74703),Ge=n(6173),Ve=n(54775),He=function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,i=n.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(r=i.next()).done;)a.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return a},Je=function(e){var t=e.description;return"string"==typeof t?r.createElement("div",{className:"description",dangerouslySetInnerHTML:{__html:t}}):r.createElement("div",{className:"description"},t)},Be=(0,i.$j)((function(e,t){var n=t.editorId;return{ragstatus:(0,J.up)(n)(e),areSnippetsCollapsed:!(0,J.eK)(n)(e)}}),(function(e,t){var n=t.editorId;return{enterFullScreen:function(){return e((0,J.Nv)({id:n,fullScreen:!0}))},onSnippetsExpand:function(t){return e((0,J.Nv)({id:n,snippetsVisible:!t}))}}}))((function(e){var t=He((0,r.useState)(!1),2),n=t[0],o=t[1],i=function(){setTimeout((function(){return e.onSnippetsExpand(!0)}),700)};return(0,r.useEffect)((function(){n&&(0,qe.w)("trackMonaco",{action:"helpIcon"})}),[n]),e.hideControls?e.examples&&e.examples.length?r.createElement(r.Fragment,null,r.createElement(W,{isCollapsed:e.areSnippetsCollapsed,onClick:function(){return e.onSnippetsExpand(!e.areSnippetsCollapsed)}}),r.createElement(Z,{snippets:e.examples,isCollapsed:e.areSnippetsCollapsed,onCopy:i})):null:r.createElement("div",{"data-qa":"codeSnippets"},r.createElement("div",{style:{float:"right"}},r.createElement(l.w,{ragstatus:e.ragstatus}),!e.omitExpandRight&&r.createElement(Ve.a,{onClick:e.onExpandRight,title:"Resize Code Area"},e.isExpandedRight?r.createElement(a.Z,{size:"small",label:"Resize Code Area Left"}):r.createElement(c.Z,{size:"small",label:"Resize Code Area Right"})),e.disableFullScreenMode||!e.readonly&&r.createElement(Ve.a,{onClick:e.enterFullScreen,title:"Toggle full screen. Press F11 or Esc twice to exit full screen"},r.createElement(u.Z,{size:"small",label:"Toggle Full Screen"})),(0,H.supportsCodeInsight)(e.lang)&&r.createElement(Ve.a,{title:"Script info",onClick:function(){return o(!0)}},r.createElement(s.Z,{size:"small",label:"Script info"})),r.createElement(Ge.Eq,{editorId:e.editorId})),"string"!=typeof e.description||e.examples&&e.examples.length||!(e.description.length<100)?r.createElement(r.Fragment,null,r.createElement(Je,{description:e.description}),r.createElement("div",{style:{clear:"both"}})):r.createElement(r.Fragment,null,r.createElement("div",{style:{float:"left"}},r.createElement(Je,{description:e.description})),r.createElement("div",{style:{clear:"both"}})),!e.readonly&&!!e.examples&&!!e.examples.length&&r.createElement(r.Fragment,null,r.createElement(W,{isCollapsed:e.areSnippetsCollapsed,onClick:function(){return e.onSnippetsExpand(!e.areSnippetsCollapsed)}}),r.createElement(Z,{snippets:e.examples,isCollapsed:e.areSnippetsCollapsed,onCopy:i})),r.createElement(se,{isOpen:n,compileContextDescriptor:e.compileContextDescriptor,onClose:function(){return o(!1)}}))})),ze=n(78417),Ke=n(31722),$e=n(57098),Ye=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},e(t,n)};return function(t,n){if("function"!=typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),Qe=function(e,t){return Object.defineProperty?Object.defineProperty(e,"raw",{value:t}):e.raw=t,e},Xe=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.wasVisible=!1,t}return Ye(t,e),t.prototype.render=function(){var e=this;return r.createElement(Ke.df,null,(function(t){var n=t.ref,o=t.inView;return!e.props.lazyRender||o||e.wasVisible?(e.wasVisible=!0,r.createElement("div",{ref:n},r.createElement(nt,null,r.createElement($e.Y,{editorId:e.props.editorId,value:e.props.initialValue,language:e.props.language,compileContextDescriptor:e.props.compileContextDescriptor,readonly:e.props.readonly,onChange:function(t){e.props.onChange(t)},disableStaticCompilationDebounce:e.props.testMode||e.props.isRegistryScript,isRegistryScript:e.props.isRegistryScript})))):r.createElement("div",{ref:n},r.createElement(et,null,r.createElement(ze.Z,{size:"medium"})),r.createElement(tt,null,e.props.initialValue))}))},t}(r.PureComponent),et=d.Z.div(Ue||(Ue=Qe(["\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    min-height: ","px;\n"],["\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    min-height: ","px;\n"])),150),tt=d.Z.p(Ze||(Ze=Qe(["\n    text-indent: 100%;\n    white-space: nowrap;\n    overflow: hidden;\n"],["\n    text-indent: 100%;\n    white-space: nowrap;\n    overflow: hidden;\n"]))),nt=d.Z.div(We||(We=Qe(["\n    display: flex;\n    flex: 1;\n    flex-direction: column;\n    box-sizing: border-box;\n"],["\n    display: flex;\n    flex: 1;\n    flex-direction: column;\n    box-sizing: border-box;\n"]))),rt=n(49278),ot=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},e(t,n)};return function(t,n){if("function"!=typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),it=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.state={expandedRight:t.props.expandedRight,examplesExpanded:!1,completionsOpen:!1,forceRenderCodeEditor:!1},t.onTabMadeVisible=function(){t.props.lazyRenderMonaco&&!t.state.forceRenderCodeEditor&&t.setState({forceRenderCodeEditor:!0},(function(){t.onTabMadeVisible()}))},t}return ot(t,e),t.prototype.componentDidUpdate=function(e){this.props.onCompileStateChanged&&e.ragStatus!==this.props.ragStatus&&this.props.onCompileStateChanged(this.props.ragStatus)},t.prototype.shouldComponentUpdate=function(e,t){var n=this.props,r=this.state;return n.readonly!==e.readonly||n.omitExpandRight!==e.omitExpandRight||n.description!==e.description||n.expandedRight!==e.expandedRight||n.lang!==e.lang||n.initialValue!==e.initialValue||!o.Z(n.compileContextDescriptor,e.compileContextDescriptor)||n.lazyRenderMonaco!==e.lazyRenderMonaco||r.examplesExpanded!==t.examplesExpanded||r.expandedRight!==t.expandedRight||r.forceRenderCodeEditor!==t.forceRenderCodeEditor||!o.Z(n.examples,e.examples)||n.fullScreen!==e.fullScreen||n.testMode!==e.testMode||n.ragStatus!==e.ragStatus},t.prototype.render=function(){var e=this,t=this.props,n=t.lang,o=t.style,i=t.initialValue,a=t.examples,c=t.onExpandRight,s=t.onChange,u=t.omitExpandRight,l=t.description,d=void 0===l?"Enter the script to execute":l;return r.createElement("div",{"data-qa":"codeEditor",className:this.state.expandedRight?"":"CodeEditorField",style:o},r.createElement(rt._3,{context:"codeEditor"}),r.createElement(Xe,{language:n,editorId:this.props.editorId,initialValue:i,onChange:function(e){return s(e)},readonly:this.props.readonly,compileContextDescriptor:this.props.compileContextDescriptor,fullscreen:this.props.fullScreen,lazyRender:this.props.lazyRenderMonaco&&!this.state.forceRenderCodeEditor||this.props.isRegistryScript,testMode:this.props.testMode,isRegistryScript:this.props.isRegistryScript,updateStrategy:this.props.updateStrategy}),r.createElement(Be,{editorId:this.props.editorId,lang:this.props.lang,examples:a,readonly:this.props.readonly,description:d,omitExpandRight:u,hideControls:this.props.displayControls,isExpandedRight:this.state.expandedRight,disableFullScreenMode:this.props.disableFullScreenMode,compileContextDescriptor:this.props.compileContextDescriptor,onExpandRight:function(){null==c||c(),e.setState({expandedRight:!e.state.expandedRight})}}))},t.defaultProps={testMode:!1,lang:"groovy",disableFullScreenMode:!1},t}(r.Component);const at=(0,i.$j)((function(e,t){var n=t.editorId;return{fullScreen:(0,J.Ie)(n)(e),ragStatus:(0,J.up)(n)(e)}}),void 0,void 0,{forwardRef:!0})(it)},87032:(e,t,n)=>{"use strict";n.d(t,{Z:()=>g});var r,o=n(63844),i=n(74785),a=n(70955),c=n.n(a),s=n(18390),u=function(e,t){return Object.defineProperty?Object.defineProperty(e,"raw",{value:t}):e.raw=t,e},l=function(e){var t=e.children.map((function(t){return t.props.hidden?null:o.createElement("li",{"data-cy":"tab-".concat(t.props.id),key:t.props.id,className:c()("menu-item",{disabled:!e.enabled&&t.props.id!==e.selectedId,"active-tab":e.selectedId===t.props.id})},o.createElement("a",{onClick:function(){return e.onTabSelected(t.props.id)}},t.props.label))})),n=e.children.find((function(t){return e.selectedId===t.props.id}));return o.createElement(o.Fragment,null,o.createElement(p,null,e.showError&&o.createElement("p",{className:"error-message"},"Empty the input field to switch tabs"),o.createElement("div",{className:"aui-tabs horizontal-tabs"},o.createElement("ul",{className:"tabs-menu"},t))),n)},d=function(e){return e.children},p=s.Z.div(r||(r=u(["\n    display: inline-flex;\n    justify-content: flex-end;\n    align-items: center;\n    width: 100%;\n    margin-bottom: 2px;\n    .aui-tabs {\n        margin: 0 !important;\n    }\n\n    .disabled a {\n        color: #808080 !important;\n    }\n\n    .error-message {\n        color: #d04437;\n        font-size: 12px;\n    }\n    .tabs-menu {\n        z-index: 0;\n    }\n"],["\n    display: inline-flex;\n    justify-content: flex-end;\n    align-items: center;\n    width: 100%;\n    margin-bottom: 2px;\n    .aui-tabs {\n        margin: 0 !important;\n    }\n\n    .disabled a {\n        color: #808080 !important;\n    }\n\n    .error-message {\n        color: #d04437;\n        font-size: 12px;\n    }\n    .tabs-menu {\n        z-index: 0;\n    }\n"]))),f=n(68814),m=function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,i=n.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(r=i.next()).done;)a.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return a},h=function(e){var t=m((0,o.useState)(function(e){var t=e.initialScriptText,n=e.initialScriptFile;return!t&&n?"scriptFile":"scriptText"}(e)),2),n=t[0],r=t[1],a=m((0,o.useState)(e.expandedRight),2),c=a[0],s=a[1],u=m((0,o.useState)(!1),2),p=u[0],h=u[1],g=e.initialScriptText||e.initialScriptFile;(0,o.useEffect)((function(){g||h(!1)}),[g]);var y=function(t){var n={showError:p,scriptFile:t.scriptFile,scriptText:t.scriptText};e.onChange(n)};return o.createElement("div",{onBlur:function(){var t=e.initialScriptFile,r=e.initialScriptText,o=e.onBlur;o&&o({selectedTab:n,expandedRight:e.expandedRight,scriptText:r,scriptFile:t})},className:"sr-script-file-wrapper",style:c?null:{maxWidth:750}},o.createElement(l,{selectedId:n,onTabSelected:function(e){var t,o;if(e!==n)return(null==g?void 0:g.trim())?(t=3e3,(o=h)(!0),void setTimeout((function(){return o(!1)}),t)):void r(e)},showError:p,enabled:!g},o.createElement(d,{id:"scriptText",label:"Inline"},o.createElement(i.Z,{editorId:e.editorId,compileContextDescriptor:e.compileContextDescriptor,initialValue:e.initialScriptText,readonly:e.readonly,onChange:function(e){return y({scriptText:e,scriptFile:""})},description:e.scriptDescription,omitExpandRight:e.omitExpandRight,onExpandRight:function(){return s(!c)},expandedRight:e.expandedRight,examples:e.examples,lang:e.lang||"groovy",disableFullScreenMode:e.disableFullScreenMode,testMode:e.testMode,updateStrategy:e.updateStrategy})),o.createElement(d,{id:"scriptFile",label:"File",hidden:e.hideFileTab},o.createElement(f.a,{editorId:e.editorId,initialValue:e.initialScriptFile,compileContextDescriptor:e.compileContextDescriptor,readonly:e.readonly,onChange:function(e){return y({scriptFile:e,scriptText:""})},description:e.fileDescription,expandedRight:e.expandedRight,lang:e.lang,testMode:e.testMode}))))};h.defaultProps={disableFullScreenMode:!1,lang:"groovy"};const g=h},47680:(e,t,n)=>{"use strict";n.d(t,{WP:()=>D,ZP:()=>_,b$:()=>h});var r,o=n(25880),i=n(67609),a=n(66494),c=n(96226),s=n(13033),u=n(91845),l=n(76529),d=n(37591),p=function(){return p=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},p.apply(this,arguments)},f=function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,i=n.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(r=i.next()).done;)a.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return a},m=function(e,t,n){if(n||2===arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))},h=["validatorScript","validatorMethod","validator"],g=function(e,t,n){return t.map((function(t){return t.id!==e?t:n(t)}))},y=(0,a.reducerWithInitialState)([]).case(c.Wg,(function(e,t){var n=t.fieldId,r=t.propId,o=t.value;return g(n,e,(function(e){var t;return p(p({},e),((t={})[r]=o,t))}))})).case(c.hg,(function(e,t){var n=t.fieldId,r=t.value;return g(n,e,(function(e){return p(p({},e),r)}))})).case(c.gV,(function(e,t){var n=t.fieldId;return g(n,e,(function(e){return o.Z(h,e)}))})).case(c.xS,(function(e,t){var n=t.fieldId,r=t.fieldName;return m(m([],f(e),!1),[{readonly:!1,hidden:!1,required:!1,id:n,name:r,when:[],except:[]}],!1)})).case(c.AK,(function(e,t){var n=t.fieldId;return e.filter((function(e){return e.id!==n}))})).case(c.B6,(function(e,t){var n=t.fieldId,r=t.applicability,o=t.idx;return g(n,e,(function(e){var t;return p(p({},e),((t={})[r]=e[r].filter((function(e,t){return o!==t})),t))}))})).case(c.r4,(function(e,t){var n=t.locator,r=n.idx,o=n.applicability,i=n.fieldId,a=t.condition;return g(i,e,(function(e){var t;return p(p({},e),((t={})[o]=e[o].map((function(e,t){return r!==t?e:a})),t))}))})).case(c.aX,(function(e,t){var n=t.locator,r=n.applicability,o=n.fieldId,i=t.condition;return g(o,e,(function(e){var t;return p(p({},e),((t={})[r]=m(m([],f(e[r]),!1),[i],!1),t))}))})),v=(0,a.reducerWithInitialState)("").case(c.rh.async.done,(function(e,t){return t.result.name})),b=(0,a.reducerWithInitialState)(null).case(c.iD,(function(e){return e})),w=(0,a.reducerWithInitialState)("").case(c.Ee,(function(e,t){return t})),E=(0,a.reducerWithInitialState)("").case(c.YW,(function(e,t){return t})),S=(0,a.reducerWithInitialState)(null).case(c.iD,(function(e,t){return t})),x=(0,a.reducerWithInitialState)(!1).case(c.VL,(function(e,t){return t})),P=(0,a.reducerWithInitialState)(null).case(c.k$,(function(e,t){return p(p({},e),t)})).case(c.x3,(function(){return null})),I=(0,a.reducerWithInitialState)({roles:[],fields:[],workflows:[],snippets:[]}).case(c.Ze.async.done,(function(e,t){var n=t.result;return p(p({},e),{roles:n})})).case(c.lm.async.done,(function(e,t){var n=t.result;return p(p({},e),{fields:n})})).case(c.uo.async.done,(function(e,t){var n=t.result;return p(p({},e),{workflows:n})})).case(c.wN.async.done,(function(e,t){var n=t.result;return p(p({},e),{snippets:n})})).case(c.rh.async.done,(function(e,t){var n=t.result,r=n.actions,o=n.steps;return p(p({},e),{actions:r,steps:o})})),C=(0,a.reducerWithInitialState)({id:-1,name:"",fields:[],mappings:[],useValidatorPlugin:!1,disabled:!1}).case(c.rW.async.done,(function(e,t){return t.result.newState})),T=(0,a.reducerWithInitialState)(!1).case(c.GM.async.done,(function(e,t){return t.result})).case(c.R6,(function(e){return!1})),O=(0,a.reducerWithInitialState)([]).case(u.O7.async.done,(function(e,t){var n=t.result;return n.behaviours.find((function(e){return e.id===n.behaviourId})).mappings})).case(u.oY.async.done,(function(e,t){var n=t.result;return m([],f(e.filter((function(e){return!(0,l.mp)(n.mapping,e)}))),!1)})),k=(0,i.UY)({id:b,guideWorkflow:v,name:w,description:E,useValidatorPlugin:S,init:P,fields:y,mappings:b,disabled:x}),A=(0,i.UY)(((r={initialised:T,editState:k,meta:I,savedState:C,mappings:O})[s.e1]=s.I6,r));const _=(0,a.reducerWithoutInitialState)().case(c.Px.async.done,(function(e,t){var n=t.result;return p(p({},e),{meta:p(p({},e.meta),{guideWorkflow:n.guideWorkflow}),savedState:p({},n),editState:p({},n),mappings:n.mappings})})).default(A);var D=(0,d.P1)((function(e){return e.behaviourEdit}),(function(e){return e.meta.snippets}))},91845:(e,t,n)=>{"use strict";n.d(t,{BR:()=>T,C3:()=>w,CS:()=>P,DK:()=>y,J3:()=>x,JI:()=>S,K$:()=>g,Lr:()=>p,O7:()=>I,V_:()=>f,b9:()=>v,cT:()=>h,i1:()=>E,jo:()=>m,oY:()=>C,xB:()=>b});var r=n(86530),o=n(28504),i=n(96355),a=n(77937),c=n(74729),s=function(e,t,n,r){return new(n||(n=Promise))((function(o,i){function a(e){try{s(r.next(e))}catch(e){i(e)}}function c(e){try{s(r.throw(e))}catch(e){i(e)}}function s(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(a,c)}s((r=r.apply(e,t||[])).next())}))},u=function(e,t){var n,r,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:c(0),throw:c(1),return:c(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function c(i){return function(c){return function(i){if(n)throw new TypeError("Generator is already executing.");for(;a;)try{if(n=1,r&&(o=2&i[0]?r.return:i[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,i[1])).done)return o;switch(r=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return a.label++,{value:i[1],done:!1};case 5:a.label++,r=i[1],i=[0];continue;case 7:i=a.ops.pop(),a.trys.pop();continue;default:if(!(o=a.trys,(o=o.length>0&&o[o.length-1])||6!==i[0]&&2!==i[0])){a=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){a.label=i[1];break}if(6===i[0]&&a.label<o[1]){a.label=o[1],o=i;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(i);break}o[2]&&a.ops.pop(),a.trys.pop();continue}i=t.call(e,a)}catch(e){i=[6,e],r=0}finally{n=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,c])}}},l=(0,r.ZP)("BEHAVIOURS"),d=(0,i.ZP)(l),p=l("TOGGLE_MAPPING_LOADING"),f=l("OPEN_ADD_MAPPING_DLG"),m=l("CLOSE_ADD_MAPPING_DLG"),h=l("UPDATE_NAME"),g=l("UPDATE_DESCRIPTION"),y=l("ADD_BEHAVIOUR"),v=d("LOAD_BEHAVIOUS",(function(){return s(void 0,void 0,void 0,(function(){return u(this,(function(e){switch(e.label){case 0:return[4,(0,c.wrappedFetch)("".concat(AJS.contextPath(),"/rest/scriptrunner/behaviours/latest/admin/behaviour"))];case 1:return[2,e.sent().result]}}))}))})),b=d("LOAD_META_PROJECT",(function(){return s(void 0,void 0,void 0,(function(){return u(this,(function(e){switch(e.label){case 0:return[4,(0,c.wrappedFetch)("".concat(AJS.contextPath(),"/rest/api/2/project"))];case 1:return[2,e.sent().result]}}))}))})),w=d("LOAD_META_SERVICE_DESK",(function(){return s(void 0,void 0,void 0,(function(){var e;return u(this,(function(t){switch(t.label){case 0:return[4,(0,c.wrappedFetch)("".concat(AJS.contextPath(),"/rest/servicedeskapi/servicedesk"))];case 1:return null==(e=t.sent()).result?[2,[]]:[2,e.result.values]}}))}))})),E=d("LOAD_META_ISSUE_TYPE",(function(){return s(void 0,void 0,void 0,(function(){return u(this,(function(e){switch(e.label){case 0:return[4,(0,c.wrappedFetch)("".concat(AJS.contextPath(),"/rest/api/2/issuetype"))];case 1:return[2,e.sent().result]}}))}))})),S=d("LOAD_META_REQUEST_TYPE",(function(e){return s(void 0,void 0,void 0,(function(){var t,n;return u(this,(function(r){switch(r.label){case 0:return t=e.map((function(e){return(0,c.wrappedFetch)("".concat(AJS.contextPath(),"/rest/servicedeskapi/servicedesk/").concat(e,"/requesttype"))})),[4,Promise.all(t)];case 1:return n=r.sent(),[2,n.flatMap((function(e){return null==e.result?[]:e.result.values}))]}}))}))})),x=d("INIT",(function(e,t){return s(void 0,void 0,void 0,(function(){var e,n;return u(this,(function(r){switch(r.label){case 0:return[4,t(w())];case 1:return e=r.sent(),n=e.map((function(e){return e.id})),[4,Promise.all([t(v()),t(b()),t(E()),t(S(n))])];case 2:return r.sent(),[2,!0]}}))}))})),P=d("DELETE_BEHAVIOUR",(function(e){return s(void 0,void 0,void 0,(function(){var t;return u(this,(function(n){switch(n.label){case 0:return[4,(0,c.wrappedFetch)("".concat(AJS.contextPath(),"/rest/scriptrunner/behaviours/latest/admin/behaviour?id=").concat(e.id),{credentials:"same-origin",method:"DELETE"})];case 1:if(!(t=n.sent().response).ok)throw(0,a.Sj)("There was a problem deleting the behaviour, please refresh the page and try again"),new Error("Server error: ".concat(t.status," ").concat(t.statusText));return[2,e]}}))}))})),I=d("ADD_MAPPING",(function(e,t){return s(void 0,void 0,void 0,(function(){var n,r;return u(this,(function(o){switch(o.label){case 0:return[4,(0,c.wrappedFetch)("".concat(AJS.contextPath(),"/rest/scriptrunner/behaviours/latest/admin/mapping"),{method:"POST",body:JSON.stringify(e)})];case 1:if(!(n=o.sent()).response.ok)throw(0,a.Sj)("There was a problem adding the mapping, please refresh the page and try again"),t(m()),new Error("Server error: ".concat(n.response.status," ").concat(n.response.statusText));return r=n.result,t(m()),[2,{behaviours:r,behaviourId:e.behaviourId}]}}))}))})),C=d("DELETE_MAPPING",(function(e){return s(void 0,void 0,void 0,(function(){var t,n,r;return u(this,(function(i){switch(i.label){case 0:return t=e.mapping,n=(0,o.GX)(t)?"id=".concat(e.id,"&pid=").concat(t.project.id,"&issueTypeId=").concat(t.issuetype.id):"id=".concat(e.id,"&serviceDeskId=").concat(t.servicedesk.id,"&requestTypeId=").concat(t.requesttype.id),[4,(0,c.wrappedFetch)("".concat(AJS.contextPath(),"/rest/scriptrunner/behaviours/latest/admin/mapping?type=").concat(t.type,"&").concat(n),{method:"DELETE"})];case 1:if(!(r=i.sent().response).ok)throw(0,a.Sj)("There was a problem deleting the mapping, please refresh the page and try again"),new Error("Server error: ".concat(r.status," ").concat(r.statusText));return[2,e]}}))}))})),T=d("TOGGLE_ENABLE_MAPPING",(function(e){return s(void 0,void 0,void 0,(function(){var t;return u(this,(function(n){switch(n.label){case 0:return[4,(0,c.wrappedFetch)("".concat(AJS.contextPath(),"/rest/scriptrunner/behaviours/latest/admin/behaviour/").concat(e.behaviourId,"/").concat(e.flag),{method:"POST"})];case 1:if((t=n.sent()).response.status>=400)throw(0,a.Sj)("There was a problem to ".concat(e.flag," behaviour, please refresh the page and try again")),new Error("Server error: ".concat(t.response.status," ").concat(t.error));return[2,{disabled:t.result.disabled,behaviourId:e.behaviourId}]}}))}))}))},28504:(e,t,n)=>{"use strict";n.d(t,{E:()=>s,GX:()=>c,VY:()=>a});var r=n(63844),o=function(){return o=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},o.apply(this,arguments)},i={width:"16px",height:"16px"};function a(e){return"SERVICEDESK"===e.type}function c(e){return"PROJECT"===e.type}var s=function(e){var t=e.mapping,n=e.handleDelete;return a(t)?r.createElement(u,{mapping:t,handleDelete:n}):r.createElement(l,{handleDelete:n,mapping:t})},u=function(e){var t=e.handleDelete,n=e.mapping,a=n.requesttype,c=n.servicedesk,s=n.fetching;return r.createElement("div",{key:a.id},c.icon?r.createElement("img",{src:c.icon,style:i}):-1===c.id?r.createElement("span",null):r.createElement("span",{style:{color:"#d04437"},className:"aui-icon aui-icon-small aui-iconfont-error"},"Missing ServiceDesk")," ",c.name," ",r.createElement("span",null,"(",a.icon&&a.icon.id?r.createElement("img",{src:"".concat(AJS.contextPath(),"/secure/viewavatar?avatarType=SD_REQTYPE&size=xsmall&avatarId=").concat(a.icon.id),style:o(o({},i),{paddingRight:"5px"})}):"",a.name,")")," ",Boolean(t)&&r.createElement("a",{className:"action",onClick:t},"Delete"),s&&r.createElement("span",{className:"aui-icon aui-icon-wait",style:{marginLeft:"5px"}},"Wait"))},l=function(e){var t=e.handleDelete,n=e.mapping,o=n.project,a=n.issuetype,c=n.fetching;return r.createElement("div",{key:o.id+"-"+a.id},function(e){return-1===e.id?r.createElement("span",null):e.avatarUrl?r.createElement("img",{src:e.avatarUrl,style:i}):r.createElement("span",{style:{color:"#d04437"},className:"aui-icon aui-icon-small aui-iconfont-error"},"Missing project")}(o)," ",o.name," ",r.createElement(d,{issuetype:a})," ",Boolean(t)&&r.createElement("a",{className:"delete-mapping action",rel:"".concat(o.id,"-").concat(a.id),onClick:t},"Delete"),c?r.createElement("span",{className:"aui-icon aui-icon-wait",style:{marginLeft:"5px"}},"Wait"):"")},d=function(e){var t=e.issuetype;return r.createElement("span",null,"(",t.avatarUrl?r.createElement("img",{src:t.avatarUrl,style:{paddingRight:"5px"}}):"",t.name,")")}},76529:(e,t,n)=>{"use strict";n.d(t,{ZP:()=>v,mp:()=>d});var r=n(67609),o=n(66494),i=n(91845),a=n(28504),c=n(20371),s=function(){return s=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},s.apply(this,arguments)},u=function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,i=n.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(r=i.next()).done;)a.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return a},l=function(e,t,n){if(n||2===arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))},d=function(e,t){return(0,a.VY)(e)&&(0,a.VY)(t)?e.requesttype===t.requesttype:!(!(0,a.GX)(e)||!(0,a.GX)(t))&&(e.project.id===t.project.id&&e.issuetype.id===t.issuetype.id)},p=function(e,t){var n=t.id,r=t.mapping;return e.map((function(e){return e.id!==n?s({},e):s(s({},e),{mappings:e.mappings.map((function(e){return d(e,r)?s(s({},r),{fetching:!e.fetching}):s({},e)}))})}))},f=(0,o.reducerWithInitialState)([]).case(i.b9.async.done,(function(e,t){var n=t.result;return l([],u(n),!1)})).case(i.CS.async.done,(function(e,t){var n=t.result.id;return e.filter((function(e){return e.id!==n}))})).case(i.oY.async.failed,(function(e,t){var n=t.params,r=n.id,o=n.mapping;return p(e,{id:r,mapping:o})})).cases([i.oY.async.started,i.Lr],p).case(i.oY.async.done,(function(e,t){var n=t.result,r=n.id,o=n.mapping;return e.map((function(e){return e.id!==r?e:s(s({},e),{mappings:e.mappings.filter((function(e){return!d(e,o)}))})}))})).case(i.O7.async.done,(function(e,t){return t.result.behaviours})).case(i.DK,(function(e,t){return l(l([],u(e),!1),[t],!1)})).case(i.cT,(function(e,t){return e.map((function(e){return"".concat(e.id)===t.id?s(s({},e),{name:t.name}):e}))})).case(i.K$,(function(e,t){return e.map((function(e){return"".concat(e.id)===t.id?s(s({},e),{description:t.description}):e}))})).case(i.BR.async.done,(function(e,t){var n=t.result;return e.map((function(e){return n.behaviourId===e.id?s(s({},e),{disabled:n.disabled}):s({},e)}))})),m=(0,o.reducerWithInitialState)({project:[],issuetype:[],servicedesk:[],requesttype:[]}).case(i.xB.async.done,(function(e,t){var n=t.result;return s(s({},e),{project:n})})).case(i.i1.async.done,(function(e,t){var n=t.result;return s(s({},e),{issuetype:n})})).case(i.C3.async.done,(function(e,t){var n=t.result;return s(s({},e),{servicedesk:n})})).case(i.JI.async.done,(function(e,t){var n=t.result;return s(s({},e),{requesttype:n})})),h=(0,o.reducerWithInitialState)((0,c.V)().isServiceDeskEnabled),g=(0,o.reducerWithInitialState)({isOpen:!1}).case(i.V_,(function(e,t){return{isOpen:!0,behaviourId:t.behaviourId}})).case(i.jo,(function(e){return{isOpen:!1}})),y=(0,o.reducerWithInitialState)(!1).case(i.J3.async.done,(function(e,t){return t.result}));const v=(0,r.UY)({initialised:y,behaviours:f,meta:m,dialog:g,serviceDeskEnabled:h})},20371:(e,t,n)=>{"use strict";n.d(t,{E:()=>o,V:()=>i});var r=n(70834);n(10692);var o=(0,r.P)("CommonSettings"),i=function(){return o}},57098:(e,t,n)=>{"use strict";n.d(t,{Y:()=>u});var r=n(63844),o=n(76416),i=function(){return i=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},i.apply(this,arguments)},a=function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,i=n.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(r=i.next()).done;)a.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return a},c=r.lazy((function(){return n.e("src_main_resources_js_monaco_MonacoEditorLoader_tsx").then(n.bind(n,71237))})),s=new Promise((function(e){window.location!==window.parent.location&&"complete"===document.readyState?e(0):window.addEventListener("load",e)})),u=function(e){return function(){var e=a(r.useState(!1),2),t=e[0],n=e[1];return r.useEffect((function(){s.then((function(){return n(!0)}))}),[]),t}()?r.createElement(r.Suspense,{fallback:l},r.createElement(c,i({},e))):l},l=r.createElement(o.LoadingSpinner,{size:"large"})},6173:(e,t,n)=>{"use strict";n.d(t,{xw:()=>m,Eq:()=>P,rL:()=>d,fu:()=>h});var r,o,i,a=n(66638),c=n(1399),s=n(74729),u=function(e,t,n,r){return new(n||(n=Promise))((function(o,i){function a(e){try{s(r.next(e))}catch(e){i(e)}}function c(e){try{s(r.throw(e))}catch(e){i(e)}}function s(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(a,c)}s((r=r.apply(e,t||[])).next())}))},l=function(e,t){var n,r,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:c(0),throw:c(1),return:c(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function c(i){return function(c){return function(i){if(n)throw new TypeError("Generator is already executing.");for(;a;)try{if(n=1,r&&(o=2&i[0]?r.return:i[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,i[1])).done)return o;switch(r=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return a.label++,{value:i[1],done:!1};case 5:a.label++,r=i[1],i=[0];continue;case 7:i=a.ops.pop(),a.trys.pop();continue;default:if(!(o=a.trys,(o=o.length>0&&o[o.length-1])||6!==i[0]&&2!==i[0])){a=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){a.label=i[1];break}if(6===i[0]&&a.label<o[1]){a.label=o[1],o=i;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(i);break}o[2]&&a.ops.pop(),a.trys.pop();continue}i=t.call(e,a)}catch(e){i=[6,e],r=0}finally{n=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,c])}}},d=function(){var e;return null===(e=AJS.DarkFeatures)||void 0===e?void 0:e.isEnabled("com.adaptavist.scriptrunner.ai.assist")},p=function(e,t){return u(void 0,void 0,Promise,(function(){var n,r,o;return l(this,(function(i){switch(i.label){case 0:return n="//".concat(e,"\n").concat((0,a.S)({jira:"import com.atlassian.jira.component.ComponentAccessor",default:"import com.atlassian.sal.api.component.ComponentLocator"}),"\n"),r="groovy"===e?"".concat(n).concat(t):t,o={prompt:r,max_tokens:120,n:1,temperature:.1,frequency_penalty:.2,presence_penalty:.1,echo:!1,logprobs:0,stream:!1,best_of:5},"https://api.openai.com/v1/engines/code-cushman-001/completions",[4,(0,s.fetchJson)("https://api.openai.com/v1/engines/code-cushman-001/completions",{method:"POST",body:JSON.stringify(o),headers:{Authorization:"Bearer ".concat("***************************************************")}})];case 1:return[2,i.sent().result.choices]}}))}))},f="adaptavist.ai-assist",m="AI_ASSIST",h=function(e,t,n,r){if(d()){var o=t.getModel(),i=n.KeyMod,a=n.KeyCode;t.addAction({run:function(t){for(var n=[],i=1;i<arguments.length;i++)n[i-1]=arguments[i];return u(this,void 0,Promise,(function(){var n,i,a;return l(this,(function(s){switch(s.label){case 0:return r((0,c.B)({id:e,value:"loading"})),n=o.getLanguageId(),[4,p(n,o.getValue())];case 1:return i=s.sent(),a=t.getSelection(),t.executeEdits(f,[{range:a,text:i[0].text}]),r((0,c.B)({id:e,value:"done"})),[2]}}))}))},id:m,label:"AI Code Assist",keybindings:[i.Alt|i.Shift|a.Enter]})}},g=n(66661),y=n(63844),v=n(54775),b=n(59462),w=n(18390),E=n(72142),S=n(59482),x=function(e,t){return Object.defineProperty?Object.defineProperty(e,"raw",{value:t}):e.raw=t,e},P=function(e){var t=(0,E.v9)((function(t){return(0,S.on)(e.editorId)(t)}));return d()?y.createElement(C,{isLoading:"loading"===t},y.createElement(v.a,{title:"AI Code Assistant",onClick:function(){return t=e.editorId,void g.editorInfoService.getInfoByEditorId(t).editor.trigger(null,m,{});var t}},"🤖")):null},I=(0,b.F4)(r||(r=x(["\n    transform-origin: center;\n    from {\n        transform: rotate(0deg);\n    }\n    to {\n        transform: rotate(360deg);\n    }\n"],["\n    transform-origin: center;\n    from {\n        transform: rotate(0deg);\n    }\n    to {\n        transform: rotate(360deg);\n    }\n"]))),C=w.Z.div(i||(i=x(["\n    display: inline-block;\n    & > :first-child {\n        margin-right: 3px !important;\n    }\n    ","\n"],["\n    display: inline-block;\n    & > :first-child {\n        margin-right: 3px !important;\n    }\n    ","\n"])),(function(e){return e.isLoading&&(0,b.iv)(o||(o=x(["\n            animation-name: ",";\n            animation-duration: 1500ms;\n            animation-iteration-count: infinite;\n            animation-timing-function: linear;\n        "],["\n            animation-name: ",";\n            animation-duration: 1500ms;\n            animation-iteration-count: infinite;\n            animation-timing-function: linear;\n        "])),I)}))},1399:(e,t,n)=>{"use strict";n.d(t,{B:()=>o,I:()=>i});var r=n(79949),o=(0,r.PH)("SET_AI_CODE_ASSIST_STATUS"),i=(0,r.Lq)({},(function(e){e.addCase(o,(function(e,t){return e[t.payload.id].aiCodeAssistStatus=t.payload.value,e}))}))},60047:(e,t,n)=>{"use strict";n.d(t,{$A:()=>i,CF:()=>c,HE:()=>m,KO:()=>d,NK:()=>u,Ro:()=>l,Xy:()=>s,be:()=>g,dd:()=>h,hc:()=>r,ly:()=>a,o2:()=>f,pG:()=>p,qH:()=>o,yy:()=>y});var r="#031534",o="#42526e",i="rgb(236, 237, 240)",a="#A5ADBA",c="white",s="#99cc33",u="#FFFFFF",l="#EBECF0",d="#A5ADBA",p="#344563",f="#205081",m="#3b73af",h="#296ca3",g="#faebd7",y=function(){return"\n    box-shadow: rgba(23, 43, 77, 0.32) 0px 4px 8px -2px, rgba(23, 43, 77, 0.25) 0px 0px 1px;\n    color: rgb(9, 30, 66);\n    text-decoration: none;\n"}},10692:(e,t,n)=>{e.exports=n},5667:(e,t,n)=>{e.exports=n}}]);