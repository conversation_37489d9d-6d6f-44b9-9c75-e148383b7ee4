"use strict";(self.webpackJsonpScriptRunner=self.webpackJsonpScriptRunner||[]).push([["default-frontend-components_packages_fetch-util_dist_index_js-src_main_resources_js_behaviour-9652e8"],{74729:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(a,o){function i(e){try{l(r.next(e))}catch(e){o(e)}}function s(e){try{l(r.throw(e))}catch(e){o(e)}}function l(e){var t;e.done?a(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(i,s)}l((r=r.apply(e,t||[])).next())}))},a=this&&this.__generator||function(e,t){var n,r,a,o,i={label:0,sent:function(){if(1&a[0])throw a[1];return a[1]},trys:[],ops:[]};return o={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function s(o){return function(s){return function(o){if(n)throw new TypeError("Generator is already executing.");for(;i;)try{if(n=1,r&&(a=2&o[0]?r.return:o[0]?r.throw||((a=r.return)&&a.call(r),0):r.next)&&!(a=a.call(r,o[1])).done)return a;switch(r=0,a&&(o=[2&o[0],a.value]),o[0]){case 0:case 1:a=o;break;case 4:return i.label++,{value:o[1],done:!1};case 5:i.label++,r=o[1],o=[0];continue;case 7:o=i.ops.pop(),i.trys.pop();continue;default:if(!(a=i.trys,(a=a.length>0&&a[a.length-1])||6!==o[0]&&2!==o[0])){i=0;continue}if(3===o[0]&&(!a||o[1]>a[0]&&o[1]<a[3])){i.label=o[1];break}if(6===o[0]&&i.label<a[1]){i.label=a[1],a=o;break}if(a&&i.label<a[2]){i.label=a[2],i.ops.push(o);break}a[2]&&i.ops.pop(),i.trys.pop();continue}o=t.call(e,i)}catch(e){o=[6,e],r=0}finally{n=a=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,s])}}},o=this&&this.__values||function(e){var t="function"==typeof Symbol&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},i=Object.create,s=Object.defineProperty,l=Object.defineProperties,c=Object.getOwnPropertyDescriptor,u=Object.getOwnPropertyDescriptors,d=Object.getOwnPropertyNames,p=Object.getOwnPropertySymbols,f=Object.getPrototypeOf,m=Object.prototype.hasOwnProperty,h=Object.prototype.propertyIsEnumerable,g=function(e,t,n){return t in e?s(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n},v=function(e,t){var n,r;for(var a in t||(t={}))m.call(t,a)&&g(e,a,t[a]);if(p)try{for(var i=o(p(t)),s=i.next();!s.done;s=i.next()){a=s.value;h.call(t,a)&&g(e,a,t[a])}}catch(e){n={error:e}}finally{try{s&&!s.done&&(r=i.return)&&r.call(i)}finally{if(n)throw n.error}}return e},E=function(e,t){return l(e,u(t))},y=function(e){return s(e,"__esModule",{value:!0})};!function(e,t){for(var n in y(e),t)s(e,n,{get:t[n],enumerable:!0})}(t,{fetchJson:function(){return _},getRequestCountValue:function(){return F},trackedFetchFactory:function(){return P},wrappedFetch:function(){return k}});var b,w=(b=n(60208),function(e,t,n){var r,a;if(t&&"object"==typeof t||"function"==typeof t){var i=function(r){m.call(e,r)||"default"===r||s(e,r,{get:function(){return t[r]},enumerable:!(n=c(t,r))||n.enumerable})};try{for(var l=o(d(t)),u=l.next();!u.done;u=l.next())i(u.value)}catch(e){r={error:e}}finally{try{u&&!u.done&&(a=l.return)&&a.call(l)}finally{if(r)throw r.error}}}return e}(y(s(null!=b?i(f(b)):{},"default",b&&b.__esModule&&"default"in b?{get:function(){return b.default},enumerable:!0}:{value:b,enumerable:!0})),b)),x="Content-Type",C="application/json",P=function(e){return function(t,n){return S(e),k(t,n).finally((function(){return T(e)}))}};function O(e){var t=e.headers.get(x);return t&&-1===t.indexOf("text/html")&&-1===t.indexOf("text/plain")?-1!==t.indexOf("application/json")||t.startsWith("application/")&&-1!==t.indexOf("+json;")?e.text().then((function(e){return e.length>0?JSON.parse(e):null})):t.startsWith("image/")?e.blob():Promise.resolve(null):e.text()}var k=function(e,t){return r(void 0,void 0,void 0,(function(){var n;return a(this,(function(r){return n=(0,w.deepmerge)(function(){var e;return{credentials:"same-origin",headers:(e={"Cache-Control":"no-cache"},e[x]=C,e["X-Atlassian-token"]="no-check",e)}}(),t||{}),[2,fetch(e,n).then((function(e){if(!e.ok){var t={error:e.statusText||"request failed",response:e};return O(e).then((function(e){return Promise.resolve(E(v({},t),{errorResult:e}))})).catch((function(e){return Promise.resolve(t)}))}return O(e).then((function(t){return Promise.resolve({result:t,response:e})})).catch((function(t){return n.method&&["delete","post"].includes(n.method.toLowerCase())?Promise.resolve({result:{},response:e}):(console.warn("Could not parse: ".concat(t)),Promise.resolve({error:"Could not parse: ".concat(t)}))}))})).catch((function(e){return console.warn("Error fetching",e),Promise.resolve({error:"Network ".concat(e)})}))]}))}))},_=function(e,t){return r(void 0,void 0,void 0,(function(){var n;return a(this,(function(r){return[2,k(e,E(v({},t),{headers:E(v({},null!=(n=null==t?void 0:t.headers)?n:{}),{Accept:C})}))]}))}))},S=function(e){e&&e.length&&R(e,F(e)+1)},T=function(e){e&&e.length&&R(e,F(e)-1)},F=function(e){return Number.parseInt(document.body.dataset[e]||"0",10)},R=function(e,t){document.body.dataset[e]=t.toString()}},39507:(e,t,n)=>{n.d(t,{F:()=>i});var r=n(17619),a=n(29577),o=n(16897),i=function(e){return r.stringify(a.Z((function(e){return!o.Z(e)}),e))}},64961:(e,t,n)=>{var r,a=n(63844),o=n(86936),i=n(72142),s=n(79949),l=n(67609),c=n(87094),u=(0,s.PH)("MAIL_HANDLER_LOADED"),d=(0,s.PH)("UPDATE_BULK"),p=(0,s.PH)("UPDATE_CATCH_EMAIL"),f=(0,s.PH)("UPDATE_EXPORT_EMAIL"),m=(0,s.PH)("UPDATE_STRIP_QUOTE"),h=(0,s.PH)("UPDATE_CREATE_USER"),g=n(91479),v=n(59482),E=n(99085),y=n(40653),b=n(83051),w=(0,b.GV)((function(e){e.addMatcher(E.w.run.matchRejected,(function(e){e.flags.push({body:"An error occurred. Please review server logs.",type:"error",options:{title:"Server Error",close:"manual"}})}))})),x=function(e,t){return function(n){return function(r,a){return e.match(a)?(n.value=a.payload,a.payload):r||t}}},C=x(d,""),P=x(p,""),O=x(f,""),k=x(m,!1),_=x(h,!1),S=(0,s.Lq)({},(function(e){e.addMatcher(E.w.run.matchPending,(function(){return{}})),e.addMatcher(E.w.run.matchFulfilled,(function(e,t){return t.payload}))})),T=(0,s.Lq)(!1,(function(e){e.addMatcher(E.w.run.matchPending,(function(){return!0})),e.addMatcher((0,s.Q)(E.w.run.matchFulfilled,E.w.run.matchRejected),(function(){return!1}))})),F=function(e){var t;return(0,l.UY)(((t={edit:c.eP,testResults:S,router:(0,g.iz)(history),bulk:C(e.bulk),catchEmail:P(e.catchEmail),forwardEmail:O(e.forwardEmail),stripQuote:k(e.stripQuote),createUser:_(e.createUser),executingTests:T,initialValues:function(e){return null!=e?e:{}}})[v.e1]=v.I6,t[y.B.reducerPath]=y.B.reducer,t[w.name]=w.reducer,t))},R=n(34281),M=n(79569),U=n(96253),I=n(36894),j=n(93738),A=n(14849),N=n(49054),L=n(77937),D=n(87032),B=n(24478),K=n(7011),Q=function(e){var t=e.children,n=(0,i.v9)((function(e){return e.uiFlag.flags})),r=(0,i.I0)();return a.useEffect((function(){if(0!==n.length){var e=n[0];"success"===e.type?(0,K.aT)(e.body,e.options):(0,K.k1)(e.body,e.options),r((0,b.YH)())}}),[n]),a.createElement(a.Fragment,null,t)},Z=n(92493),H=n(22909),V=(r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},r(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),z=function(){return z=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},z.apply(this,arguments)},q={"@type":"predefined",label:"com.onresolve.scriptrunner.mail.MailHandler.SCRIPT_MAILHANDLER_CONTEXT",compileContextOptions:{}},W=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.srFileUpdated=function(e){t.props.hasEditPermissions&&(t.onScriptFileUpdate(e.scriptFile),t.onScriptTextChanges(e.scriptText))},t.onBulkValueChanged=function(e){if(t.props.hasEditPermissions){var n=e.target.value;t.props.dispatchBulkUpdate(n)}},t.onScriptFileUpdate=function(e){t.props.hasEditPermissions&&(t.props.onScriptFileUpdate(e),t.props.onCheckedScriptFileChanged(e))},t.onScriptTextChanges=function(e){t.props.hasEditPermissions&&(t.props.onScriptUpdate(e),t.props.onScriptTextChanged(e))},t.onForwardEmailChanged=function(e){if(t.props.hasEditPermissions){var n=e.target.value;t.props.dispatchForwardEmailUpdate(n)}},t.onCatchEmailChanged=function(e){if(t.props.hasEditPermissions){var n=e.target.value;t.props.dispatchCatchEmailUpdate(n)}},t.onStripQuoteChanged=function(e){if(t.props.hasEditPermissions){var n=e.target.checked;t.props.dispatchStripQuoteUpdate(n)}},t.onCreateUserChanged=function(e){if(t.props.hasEditPermissions){var n=e.target.checked;t.props.dispatchCreateUserUpdate(n)}},t}return V(t,e),t.prototype.render=function(){var e=this.props;return a.createElement(Z.t,{className:"adaptavist-sr"},a.createElement(N.S,{onBeforeunload:function(t){e.hasChanges&&t.preventDefault()}}),a.createElement(Q,null,!this.props.hasEditPermissions&&a.createElement("h4",{style:{textAlign:"center",color:j.rt}},"You need"," ",a.createElement("a",{href:(0,B.w7)("automation-for-jira"),target:"_blank"}," ","ScriptRunner Script Edit permission")," ","to use ScriptRunner Mail Handler."),a.createElement("div",{className:"field-group"},a.createElement("label",{htmlFor:"catch-email"},"Catch Email"),a.createElement(A.Z,{isCompact:!0,width:H.Q,type:"text",id:"catchEmail",disabled:!e.hasEditPermissions,onChange:this.onCatchEmailChanged,value:e.catchEmail,isDisabled:!e.hasEditPermissions}),a.createElement("div",{className:"description long-field"},"If set, only emails having the specified recipient in fields To, Cc or Bcc will be processed.")),a.createElement("div",{className:"field-group"},a.createElement("label",{htmlFor:"forward-email"},"Forward Email"),a.createElement(A.Z,{isCompact:!0,width:H.Q,type:"text",id:"forwardEmail",onChange:this.onForwardEmailChanged,value:e.forwardEmail,isDisabled:!e.hasEditPermissions}),a.createElement("div",{className:"description long-field"},"Send an email to the given address if error occurred during email handler execution. This This functionality is available for IMAP/POP3 configuration only.")),a.createElement("div",{className:"field-group"},a.createElement("label",{htmlFor:"strip-quote"},"Strip Quotes"),a.createElement("input",{type:"checkbox",id:"stripQuote",className:"checkbox",disabled:!e.hasEditPermissions,onChange:this.onStripQuoteChanged,checked:"true"===e.stripQuote.toString()}),a.createElement("div",{className:"description long-field"},"if checked quoted text is removed from comments.")),a.createElement("div",{className:"field-group"},a.createElement("label",{htmlFor:"create-user"},"Create Users"),a.createElement("input",{type:"checkbox",id:"createUser",className:"checkbox",disabled:!e.hasEditPermissions,onChange:this.onCreateUserChanged,checked:"true"===e.createUser.toString()}),a.createElement("div",{className:"description long-field"},"If a message comes from an unrecognised address, create a new Jira user with the user name and email address set to the 'From' address of the message.")),a.createElement("div",{className:"field-group"},a.createElement("label",{htmlFor:"scriptInput"}),a.createElement(D.Z,{editorId:"mailHandler",readonly:!e.hasEditPermissions,disableFullScreenMode:!0,initialScriptFile:e.scriptFile,initialScriptText:e.scriptText,compileContextDescriptor:q,onChange:this.srFileUpdated}),a.createElement("div",{className:"error"},e.validationResult)),a.createElement("div",{className:"field-group"},a.createElement("label",{htmlFor:"bulkInput"},"Bulk"),a.createElement("select",{id:"bulkInput",className:"select",disabled:!e.hasEditPermissions,value:e.bulkValue,onChange:this.onBulkValueChanged},a.createElement("option",{value:"ignore"},"Ignore the email and do nothing"),a.createElement("option",{value:"forward"},"Forward the email"),a.createElement("option",{value:"delete"},"Delete the email permanently"),a.createElement("option",{value:"accept"},"Accept the email for processing")),a.createElement("div",{className:"description"},"Action that will be performed for emails with the 'Precedence: bulk' or emails with an 'Auto-Submitted' header that is not set to \"no\"."))))},t}(a.PureComponent);const $=(0,i.$j)((function(e){var t={bulkValue:e.bulk,scriptFile:e.edit.UPDATE_BUILTIN_SCRIPT_PARAM,scriptText:e.edit.FIELD_INLINE_SCRIPT,validationResult:e.edit.validationResult,forwardEmail:e.forwardEmail,catchEmail:e.catchEmail,stripQuote:e.stripQuote,createUser:e.createUser};return z(z({},t),{hasChanges:!U.Z(e.initialValues,I.Z(["bulkValue","catchEmail","forwardEmail","stripQuote","createUser","scriptText","scriptFile"],t))})}),(function(e){return{onScriptUpdate:function(t){return e((0,L.bB)({key:"FIELD_INLINE_SCRIPT",value:t}))},onScriptFileUpdate:function(t){return e((0,L.bB)({key:"UPDATE_BUILTIN_SCRIPT_PARAM",value:t}))},dispatchCatchEmailUpdate:function(t){return e(p(t))},dispatchForwardEmailUpdate:function(t){return e(f(t))},dispatchStripQuoteUpdate:function(t){return e(m(t))},dispatchCreateUserUpdate:function(t){return e(h(t))},dispatchBulkUpdate:function(t){return e(d(t))}}}))(W);var J=n(78417),X=n(18390),Y=n(95925),G=n(47492),ee=n(81981),te=n(98351),ne=n(69771),re=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},e(t,n)};return function(t,n){if("function"!=typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();const ae=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.containerRef=null,t.triggerRef=null,t.updateRef=function(e,t){"function"==typeof e?e(t):e.current=t},t.handleClickOutside=function(e){var n=t.props,r=n.isOpen,a=n.onClose;if(!e.defaultPrevented){var o=t.containerRef,i=t.triggerRef,s=e.target;i&&i.contains(s)||r&&o&&!o.contains(s)&&a({isOpen:!1,event:e})}},t}return re(t,e),t.prototype.componentDidUpdate=function(e){"undefined"!=typeof window&&(!e.isOpen&&this.props.isOpen?window.addEventListener("click",this.handleClickOutside,!0):e.isOpen&&!this.props.isOpen&&window.removeEventListener("click",this.handleClickOutside))},t.prototype.componentDidMount=function(){"undefined"!=typeof window&&this.props.isOpen&&window.addEventListener("click",this.handleClickOutside,!0)},t.prototype.componentWillUnmount=function(){"undefined"!=typeof window&&window.removeEventListener("click",this.handleClickOutside)},t.prototype.render=function(){var e=this,t=this.props,n=t.children,r=t.placement,o=t.isOpen,i=t.content,s=t.onContentBlur,l=t.onContentFocus,c=t.onContentClick,u=t.className,d=o?a.createElement(G.r,{placement:r},(function(t){var n=t.ref,r=t.style,o=t.hasPopperEscaped;return a.createElement(ne.W,{onBlur:s,onFocus:l,onClick:c,outOfBoundaries:o,ref:function(t){e.containerRef=t,e.updateRef(n,t)},style:r,className:u},i)})):null;return a.createElement(ee.dK,null,a.createElement(te.s,null,(function(t){var r=t.ref;return a.createElement(Y.Z,{innerRef:function(t){e.triggerRef=t,e.updateRef(r,t)}},n)})),d)},t.defaultProps={children:null,content:null,isOpen:!1,onContentBlur:function(){return{}},onContentClick:function(){return{}},onContentFocus:function(){return{}},onClose:function(){return{}},placement:"bottom-start"},t}(a.Component);var oe,ie,se,le,ce,ue=n(2047),de=n(1425),pe=n(25015),fe=function(e,t){return Object.defineProperty?Object.defineProperty(e,"raw",{value:t}):e.raw=t,e},me=function(){return me=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},me.apply(this,arguments)},he=function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,a,o=n.call(e),i=[];try{for(;(void 0===t||t-- >0)&&!(r=o.next()).done;)i.push(r.value)}catch(e){a={error:e}}finally{try{r&&!r.done&&(n=o.return)&&n.call(o)}finally{if(a)throw a.error}}return i},ge=["errorMessages","warningMessages","infoMessages"],ve={errorMessages:"Errors",warningMessages:"Warnings",infoMessages:"Info",reasonMessages:"Other"},Ee=function(e){var t=e.errorKey,n=e.size,r=void 0===n?"medium":n;switch(t){case"infoMessages":case"emptyInbox":return a.createElement(ue.Z,{size:r,label:"info",primaryColor:"#7a869a"});case"warningMessages":return a.createElement(de.Z,{size:r,label:"warning",primaryColor:"#FFAB00"});case"errorMessages":return a.createElement(pe.Z,{size:r,label:"error",primaryColor:"#FF5630"});default:return null}},ye=function(e){var t=e.messages,n=e.errorKey;if(!t||!t.length)return null;var r=he(t,1)[0];return a.createElement(a.Fragment,null,a.createElement(Ee,{errorKey:n}),a.createElement("span",{style:{paddingRight:"10px"}},r))},be=function(e){var t=e.isVisible,n=e.onClick;return t?a.createElement("a",{href:"#",style:{marginRight:"10px"},onClick:n},"More details..."):null},we=function(e){var t=e.content,n=e.messageKey,r=e.hasDetails,o=e.messages,i=he(a.useState(!1),2),s=i[0],l=i[1];return a.createElement(Se,{placement:"top-start",isOpen:s,onClose:function(){return l(!1)},content:t},a.createElement(ye,{errorKey:n,messages:o}),a.createElement(be,{isVisible:r,onClick:function(){return l(!s)}}))},xe=function(e){var t=e.messages,n=e.reasons,r=a.createElement(Ce,{reasons:n});return a.createElement(we,{hasDetails:!0,content:r,messageKey:"emptyInbox",messages:t})},Ce=function(e){var t=e.reasons;return a.createElement(Te,null,a.createElement(Fe,null,"Test Run Results"),a.createElement(Re,null,a.createElement("p",null,t[0]),a.createElement(Me,null,t[1])))},Pe=function(e){var t=e.messages;return 1===t.length?a.createElement("p",null,t[0]):a.createElement("ul",null,t.map((function(e,t){return a.createElement("li",{key:t},e)})))},Oe=function(e){var t=e.messages,n=e.messageKey;return t&&t.length?a.createElement(a.Fragment,null,a.createElement("h3",{style:{marginBlockStart:"15px"}},a.createElement(Ee,{errorKey:n,size:"small"})," ",ve[n],":"),a.createElement(Pe,{messages:t})):null},ke=function(e){var t=e.messages;return a.createElement(Te,null,a.createElement(Fe,null,"Test Run Results"),a.createElement(Re,null,a.createElement(Oe,{messageKey:"errorMessages",messages:t.errorMessages}),a.createElement(Oe,{messageKey:"warningMessages",messages:t.warningMessages}),a.createElement(Oe,{messageKey:"infoMessages",messages:t.infoMessages}),a.createElement(Oe,{messageKey:"reasonMessages",messages:t.reasonMessages})))},_e=function(e){var t,n=e.results;if(0===Object.values(n).filter((function(e){return!!e})).length)return null;if(n.emptyInbox)return a.createElement(xe,{messages:n.emptyInbox,reasons:n.reasonMessages});var r=he(a.useState(!1),2),o=r[0],i=r[1],s=ge.find((function(e){return!!n[e]&&n[e].length>0}));if(!s)return null;var l=me(me({},n),((t={})[s]=n[s].slice(1),t)),c=a.createElement(ke,{messages:l}),u=n[s].length>1||!!n.reasonMessages&&n.reasonMessages.length>0;return a.createElement(Se,{placement:"top-start",isOpen:o,onClose:function(){return i(!1)},content:c},a.createElement(ye,{errorKey:s,messages:n[s]}),a.createElement(be,{isVisible:u,onClick:function(){return i(!o)}}))},Se=(0,X.Z)(ae)(oe||(oe=fe(["\n    padding: 0;\n    overflow: hidden;\n    min-width: 500px;\n    max-width: 500px;\n"],["\n    padding: 0;\n    overflow: hidden;\n    min-width: 500px;\n    max-width: 500px;\n"]))),Te=X.Z.div(ie||(ie=fe(["\n    text-align: left;\n    overflow: hidden;\n    white-space: normal;\n"],["\n    text-align: left;\n    overflow: hidden;\n    white-space: normal;\n"]))),Fe=X.Z.h2(se||(se=fe(["\n    background: #f0f0f0;\n    color: #666;\n    font-size: 1.3em;\n    height: 43px;\n    line-height: 43px;\n    padding: 0 1em;\n    text-align: left;\n"],["\n    background: #f0f0f0;\n    color: #666;\n    font-size: 1.3em;\n    height: 43px;\n    line-height: 43px;\n    padding: 0 1em;\n    text-align: left;\n"]))),Re=X.Z.div(le||(le=fe(["\n    position: relative;\n    overflow: auto;\n    padding: 5px 15px 15px 15px;\n"],["\n    position: relative;\n    overflow: auto;\n    padding: 5px 15px 15px 15px;\n"]))),Me=X.Z.div(ce||(ce=fe(["\n    max-height: 150px;\n    border-radius: 5px;\n    border-style: solid;\n    border-width: 1px;\n    background-color: #eeeeee;\n    padding: 5px;\n    overflow: auto;\n    border-color: #aaaaaa;\n    margin-top: 3px;\n    word-break: break-all;\n"],["\n    max-height: 150px;\n    border-radius: 5px;\n    border-style: solid;\n    border-width: 1px;\n    background-color: #eeeeee;\n    padding: 5px;\n    overflow: auto;\n    border-color: #aaaaaa;\n    margin-top: 3px;\n    word-break: break-all;\n"]))),Ue=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},e(t,n)};return function(t,n){if("function"!=typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),Ie=function(e,t){return Object.defineProperty?Object.defineProperty(e,"raw",{value:t}):e.raw=t,e},je=function(){return je=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},je.apply(this,arguments)},Ae=function(e){var t="function"==typeof Symbol&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},Ne=function(e){var t=e.testResults,n=e.hasEditPermissions,r=e.executingTests,o=e.dispatchTest,i=e.addLabel,s=e.cancelLabel;return a.createElement(De,null,r&&a.createElement("div",{style:{padding:"0 10px"}},a.createElement(J.Z,null)),a.createElement(_e,{results:t}),a.createElement("input",{disabled:!n||r,onClick:function(){n&&o()},type:"button",className:"button",value:"Run Now","data-testid":"run-btn"}),n&&a.createElement("input",{style:{marginLeft:"10px"},id:"addButton",type:"submit",className:"button",value:i,accessKey:"$submitAccessKey",title:"$submitTitle","data-testid":"add-btn"}),a.createElement("a",{href:"IncomingMailServers.jspa",className:"cancel",accessKey:"$cancelAccessKey",title:"$cancelTitle","data-testid":"cancel-btn"},s))},Le=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return Ue(t,e),t.prototype.componentDidMount=function(){var e=document.querySelector("#srMailHandlerForm .throbber");if(e instanceof HTMLElement)e.style.cssFloat="left";else{var t=new MutationObserver((function(e){var n,r,a=e.map((function(e){return e.addedNodes.values()}));try{for(var o=Ae(a),i=o.next();!i.done;i=o.next()){var s=i.value.next();s&&s.value instanceof HTMLSpanElement&&s.value.classList.contains("throbber")&&(s.value.style.cssFloat="left",t.disconnect())}}catch(e){n={error:e}}finally{try{i&&!i.done&&(r=o.return)&&r.call(o)}finally{if(n)throw n.error}}}));t.observe(document.querySelector("#mail-handler-actions"),{childList:!0,subtree:!0})}},t.prototype.render=function(){return a.createElement(Ne,je({},this.props))},t}(a.PureComponent),De=X.Z.span(Ke||(Ke=Ie(["\n    display: flex;\n    align-items: center;\n"],["\n    display: flex;\n    align-items: center;\n"])));const Be=(0,i.$j)((function(e){return{testResults:e.testResults,executingTests:e.executingTests}}),(function(e){return{dispatchTest:function(){return e(E.w.run.initiate(void 0,{track:!1}))}}}))(Le);var Ke,Qe=n(20371),Ze=n(34968),He=n(38414);n(10692);var Ve=AJS.$,ze=function(e){return document.getElementById(e)};Ve((function(){(window.ScriptRunner||{}).initMailHandlerForm=function(){var e=document.getElementById("mail-handler-target"),t=document.getElementById("mail-handler-actions");if(e&&t){var n,r,l,c,d=ze("script-path"),p=function(e){d.value=e},f=ze("script-text"),m=function(e){f.value=e},h=ze("script-status"),g=function(e){h.value="green"===e?"OK":"ERROR"},v=ze("bulk"),E=ze("forward-email"),w=ze("catch-email"),x=ze("strip-quote"),C=ze("create-user"),P=ze("validation-result"),O=(n={edit:{FIELD_INLINE_SCRIPT:f.value,UPDATE_BUILTIN_SCRIPT_PARAM:d.value,validationResult:P.value},bulk:v.value,catchEmail:w.value,forwardEmail:E.value,stripQuote:!!x.value,createUser:!!C.value,testResults:{},executingTests:!1,query:void 0,uiFlag:b.Wz,initialValues:{bulkValue:v.value,catchEmail:w.value,forwardEmail:E.value,stripQuote:!!x.value,createUser:!!C.value,scriptText:f.value,scriptFile:d.value}},r={bulk:v,catchEmail:w,forwardEmail:E,stripQuote:x,createUser:C},(0,s.xC)({reducer:F(r),preloadedState:n,middleware:function(e){return e().concat((0,M.ZP)((0,R.pW)()),y.B.middleware)}})),k=Qe.E.hasEditPermissions;void 0===l&&(l=$),o.render(a.createElement(Ze.SV,{FallbackComponent:He.a},a.createElement(i.zt,{store:O},a.createElement(l,{hasEditPermissions:k,onCheckedScriptFileChanged:p,onCompileStateChange:g,onScriptTextChanged:m}))),e),void 0===c&&(c=Be),o.render(a.createElement(i.zt,{store:O},a.createElement(c,{hasEditPermissions:k,addLabel:t.getAttribute("data-add"),cancelLabel:t.getAttribute("data-cancel")})),t),O.dispatch(u())}},window.postMessage("sr-MailHandlerLoaded","*")}))}}]);