/*! For license information please see default-src_main_resources_js_admin_StandaloneParameterisedScriptOrFile_tsx.fad48c679a205bc90a1f.js.LICENSE.txt */
(self.webpackJsonpScriptRunner=self.webpackJsonpScriptRunner||[]).push([["default-src_main_resources_js_admin_StandaloneParameterisedScriptOrFile_tsx"],{23161:(e,t,n)=>{"use strict";n.d(t,{x:()=>g,O:()=>y});var r,a=n(63844),o=n(72142),i=n(23400),l=n(77937),c=n(40653),u="".concat(AJS.contextPath(),"/rest/scriptrunner/latest/console/snippets"),s={getSnippets:c.B.injectEndpoints({endpoints:function(e){return{getSnippets:e.query({query:function(){return u},keepUnusedDataFor:Number.MAX_SAFE_INTEGER})}}}).endpoints.getSnippets},p=(r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},r(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),d=function(){return d=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},d.apply(this,arguments)},f=function(e,t,n,r){return new(n||(n=Promise))((function(a,o){function i(e){try{c(r.next(e))}catch(e){o(e)}}function l(e){try{c(r.throw(e))}catch(e){o(e)}}function c(e){var t;e.done?a(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(i,l)}c((r=r.apply(e,t||[])).next())}))},h=function(e,t){var n,r,a,o,i={label:0,sent:function(){if(1&a[0])throw a[1];return a[1]},trys:[],ops:[]};return o={next:l(0),throw:l(1),return:l(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function l(o){return function(l){return function(o){if(n)throw new TypeError("Generator is already executing.");for(;i;)try{if(n=1,r&&(a=2&o[0]?r.return:o[0]?r.throw||((a=r.return)&&a.call(r),0):r.next)&&!(a=a.call(r,o[1])).done)return a;switch(r=0,a&&(o=[2&o[0],a.value]),o[0]){case 0:case 1:a=o;break;case 4:return i.label++,{value:o[1],done:!1};case 5:i.label++,r=o[1],o=[0];continue;case 7:o=i.ops.pop(),i.trys.pop();continue;default:if(!(a=i.trys,(a=a.length>0&&a[a.length-1])||6!==o[0]&&2!==o[0])){i=0;continue}if(3===o[0]&&(!a||o[1]>a[0]&&o[1]<a[3])){i.label=o[1];break}if(6===o[0]&&i.label<a[1]){i.label=a[1],a=o;break}if(a&&i.label<a[2]){i.label=a[2],i.ops.push(o);break}a[2]&&i.ops.pop(),i.trys.pop();continue}o=t.call(e,i)}catch(e){o=[6,e],r=0}finally{n=a=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,l])}}},m={type:"parameterisedCheckedScriptTextOrFile",description:"Enter the script to execute",fileDescription:"Enter the path to the script",expandedRight:!0,omitExpandRight:!0},y=function(e,t){return{item:{id:e,name:t,params:"",projects:[],friendlyEventNames:"",FIELD_LISTENER_NOTES:"",ownedBy:""}}},v=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.state={snippets:[]},t.getValue=function(){var e=t.props.value[t.props.fieldName];if(e&&(e.script||e.scriptPath))return e;var n=t.props.storedValue;return n?{script:n.scriptText,scriptPath:n.scriptFile,parameters:n.parameters}:{script:"",scriptPath:"",parameters:{}}},t}return p(t,e),t.prototype.componentDidMount=function(){var e;return f(this,void 0,void 0,(function(){var t;return h(this,(function(n){switch(n.label){case 0:return[4,this.props.getSnippets()];case 1:return t=n.sent(),this.setState({snippets:null!==(e=t.data)&&void 0!==e?e:[]}),[2]}}))}))},t.prototype.render=function(){var e,t,n;return a.createElement(i.Z,{param:d(d({},m),{value:this.getValue(),label:this.props.label,name:this.props.fieldName,compileContextDescriptor:this.props.compileContextDescriptor,disabled:null!==(e=this.props.disabled)&&void 0!==e&&e,description:null!==(t=this.props.description)&&void 0!==t?t:m.description,fileDescription:null!==(n=this.props.fileDescription)&&void 0!==n?n:m.fileDescription,examples:this.state.snippets}),config:y(this.props.fieldId,this.props.fieldName),overrides:null,validation:this.props.validation,onChangeFn:this.props.onChangeFn,updateValidation:this.props.updateValidation,updateStrategy:this.props.updateStrategy})},t}(a.Component),g=(0,o.$j)((function(e){return{value:e.edit,storedValue:e.console,annotatedFields:e.annotatedScriptParameters}}),(function(e){return{onChangeFn:function(t,n){"string"!=typeof n&&(e((0,l.Ge)({scriptText:null==n?void 0:n.script,scriptFile:null==n?void 0:n.scriptPath,parameters:(null==n?void 0:n.script)||(null==n?void 0:n.scriptPath)?n.parameters:{}})),e((0,l.bB)({key:t,value:n})))},updateValidation:function(t){return e((0,l.TO)(t))},getSnippets:function(){return e(s.getSnippets.initiate())}}}))(v)},23400:(e,t,n)=>{"use strict";n.d(t,{Z:()=>I});var r,a,o=n(43574),i=n(63844),l=n(7802),c=n(26052),u=n(19600),s=n(72142),p=n(59462),d=n(18390),f=n(26550),h=n(87094),m=n(77937),y=n(13033),v=function(e,t){return Object.defineProperty?Object.defineProperty(e,"raw",{value:t}):e.raw=t,e},g=function(){return g=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},g.apply(this,arguments)},b=function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,a,o=n.call(e),i=[];try{for(;(void 0===t||t-- >0)&&!(r=o.next()).done;)i.push(r.value)}catch(e){a={error:e}}finally{try{r&&!r.done&&(n=o.return)&&n.call(o)}finally{if(a)throw a.error}}return i},w=(0,s.$j)((function(e,t){var n,r,a=null!==(n=(0,h.Cq)(t.param.name)(e))&&void 0!==n?n:{},o=(0,y.oD)(t.param.name)(e),i=(0,y.eE)(t.param.name)(e),u=l.Z(c.Z("name"),null!==(r=null==o?void 0:o.parameters)&&void 0!==r?r:[]);return{errors:a,mergedParams:C(t.param,o,u),annotatedFields:o,hasAnnotatedFields:i}}),(function(e,t){return{onChildFieldChange:function(n){return function(r){var a,o=t.param.name,i=u.Z(t.param.value,{parameters:(a={},a[n.name]=r,a)});e((0,m.bB)({key:o,value:i}))}},updateValidation:function(t){return e((0,m.TO)(t))}}}))((function(e){var t;if(!e.hasAnnotatedFields)return null;var n=null!==(t=e.parameterRenderer)&&void 0!==t?t:{};return i.createElement(x,null,e.annotatedFields.parameters.map((function(t){return i.createElement(f.Z,{key:"".concat(e.param.name,"-").concat(t.name),param:e.mergedParams[t.name]||t,errorMessage:e.errors[t.name],config:e.config,onChange:e.onChildFieldChange(t),updateValidation:e.updateValidation,overrides:n[t.name]})})))})),E=(0,p.F4)(r||(r=v(["\n  from {\n    overflow: hidden;\n    transform: scaleY(0);\n  }\n  to {\n    transform: scaleY(1);\n  }\n"],["\n  from {\n    overflow: hidden;\n    transform: scaleY(0);\n  }\n  to {\n    transform: scaleY(1);\n  }\n"]))),x=d.Z.div(a||(a=v(["\n    animation: "," 200ms linear;\n"],["\n    animation: "," 200ms linear;\n"])),E),C=function(e,t,n){var r=e.value;if(!r)return{};var a=!(null==t?void 0:t.enabled);if(!r.parameters)return Object.entries(n).reduce((function(e,t){var n,r=b(t,2),o=r[0],i=r[1];return g(g({},e),((n={})[o]=g(g({},i),{disabled:a}),n))}),{});var o=r.parameters;return Object.entries(n).reduce((function(e,t){var n=b(t,2),r=n[0],i=n[1];return e[r]=g(g({},i),{value:o[r],disabled:a}),e}),{})},S=n(92417),O=n(87032),P=n(8163),j=function(){return j=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},j.apply(this,arguments)};const k=(0,s.$j)((function(e,t){return{selectedTab:(0,y.oD)(t.param.name)(e).editorMode,errorMessage:(0,h.Cq)(t.param.name)(e),context:e.context}}),(function(e){return{onTabSwitch:function(t,n){return e((0,P.G)({fieldName:t,editorMode:n}))}}}))((function(e){var t=e.param,n=e.context;return i.createElement(S.Z,{param:t,style:{marginBottom:"-30px",width:"50%"},errorMessage:e.errorMessage,customLabelComponent:e.customLabelComponent},i.createElement(O.Z,j({},e.param,{scriptDescription:t.description,fileDescription:t.fileDescription||t.description,initialScriptText:t.value&&t.value.script?t.value.script:"",initialScriptFile:t.value&&t.value.scriptPath?t.value.scriptPath:"",onChange:function(t){e.onChange({script:t.scriptText?t.scriptText:null,scriptPath:t.scriptFile?t.scriptFile:null})},compileContextDescriptor:t.compileContextDescriptor,readonly:t.disabled,editorId:t.name,selectedTab:e.selectedTab,onSwitchTab:function(n){return e.onTabSwitch(t.name,n)},hideFileTab:n&&(!!n.repo||!!n.project),updateStrategy:e.updateStrategy})),e.customFooterComponent)}));var T,A=(T=function(e,t){return T=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},T(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}T(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),D=function(e,t){return Object.defineProperty?Object.defineProperty(e,"raw",{value:t}):e.raw=t,e},M=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.initialRender=!0,t.onScriptFieldChange=function(e){"string"!=typeof e?e.script||e.scriptPath?t.props.onChangeFn(t.props.param.name,o.Z(t.props.param.value,e)):t.props.onChangeFn(t.props.param.name,void 0):console.error("Value for script field with Annotated Script Parameters must be object")},t}return A(t,e),t.prototype.componentDidMount=function(){this.initialRender=!1},t.prototype.render=function(){var e,t=null!==(e=this.props.overrides)&&void 0!==e?e:{ParameterRenderer:null};return i.createElement(J,{initialRender:this.initialRender,enabled:this.props.hasAnnotatedFields},i.createElement(w,{param:this.props.param,config:this.props.config,parameterRenderer:t.ParameterRenderer}),i.createElement(k,{param:this.props.param,customLabelComponent:this.props.customLabelComponent,customFooterComponent:this.props.customScriptFooterComponent,updateStrategy:this.props.updateStrategy,onChange:this.onScriptFieldChange}))},t}(i.Component);const I=(0,s.$j)((function(e,t){return{hasAnnotatedFields:(0,y.eE)(t.param.name)(e)}}))(M);var N,L,Z,_="#fff",F="10px",R="#e0e0e0",z="#f0f0f0",U=(0,p.F4)(N||(N=D(["\n  from {\n    background: ",";\n    border-color: ",";\n    padding-right: ",";\n  }\n  to {\n    background: ",";\n    border-color: ",";\n    padding-right: ",";\n  }\n"],["\n  from {\n    background: ",";\n    border-color: ",";\n    padding-right: ",";\n  }\n  to {\n    background: ",";\n    border-color: ",";\n    padding-right: ",";\n  }\n"])),_,_,"0",z,R,F),B=(0,p.F4)(L||(L=D(["\n  to {\n    background: ",";\n    border-color: ",";\n    padding-right: ",";\n  }\n  from {\n    background: ",";\n    border-color: ",";\n    padding-right: ",";\n  }\n"],["\n  to {\n    background: ",";\n    border-color: ",";\n    padding-right: ",";\n  }\n  from {\n    background: ",";\n    border-color: ",";\n    padding-right: ",";\n  }\n"])),_,_,"0",z,R,F),J=d.Z.div(Z||(Z=D(["\n    border: "," solid;\n    border-radius: 3px;\n    animation-name: ",";\n    animation-duration: ",";\n    animation-fill-mode: forwards;\n"],["\n    border: "," solid;\n    border-radius: 3px;\n    animation-name: ",";\n    animation-duration: ",";\n    animation-fill-mode: forwards;\n"])),(function(e){var t=e.enabled,n=e.initialRender;return t&&!n?"1px":"0"}),(function(e){return e.enabled?U:B}),(function(e){return e.initialRender?null:"300ms"}))},26550:(e,t,n)=>{"use strict";n.d(t,{Z:()=>Xa});var r,a=n(63844),o=n(14849),i=n(92417),l=n(58844),c=(r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},r(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),u=function(){return u=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},u.apply(this,arguments)};const s=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.state={examplesExpanded:!1},t}return c(t,e),t.prototype.render=function(){var e=this,t=this.props,n=t.examples,r=t.handleSelect,o=t.isCodeEditor,i=t.style,c=t.isIntervalOrCronExpression;if(!n||!n.length)return null;var s=!(o||c);return a.createElement(a.Fragment,null,s&&a.createElement("br",null),a.createElement("div",{className:"description",style:u(u({},i),{float:o?"left":"none"})},a.createElement("a",{onClick:function(){return e.setState({examplesExpanded:!e.state.examplesExpanded})},className:"action"},"Show examples",a.createElement("span",{className:"aui-icon aui-icon-small aui-iconfont-arrows-".concat(this.state.examplesExpanded?"up":"down")})),this.state.examplesExpanded&&a.createElement("ul",null,n.map((function(e){return a.createElement("li",{key:e.name},a.createElement("a",{className:"action",dangerouslySetInnerHTML:{__html:(0,l.sanitize)(e.name)},onClick:function(){return r(e.code)}}))})))))},t}(a.Component);var p=n(22909),d=function(){return d=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},d.apply(this,arguments)};const f=function(e){var t=e.param;return a.createElement(i.Z,d({},e,{param:t}),a.createElement(o.Z,{isCompact:!0,width:p.Q,"data-cy":t.name,name:t.name,id:t.name,type:t.type||"text",onChange:function(t){return e.onChange(t.currentTarget.value)},value:t.value||"","data-schema":t.schema||"",placeholder:t.placeholder||"",isDisabled:t.disabled}),a.createElement(s,{examples:t.examples,handleSelect:e.onChange,isCodeEditor:!1}))};var h=n(87032),m=function(){return m=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},m.apply(this,arguments)};const y=function(e){var t=e.param;return a.createElement(i.Z,m({},e,{param:t}),a.createElement(h.Z,m({},e.param,{editorId:t.name,scriptDescription:t.description,fileDescription:t.description,initialScriptText:t.value?t.value[0]:"",initialScriptFile:t.value?t.value[1]:"",onChange:function(t){e.onChange([t.scriptText,t.scriptFile])},compileContextDescriptor:t.compileContextDescriptor,readonly:t.disabled})))};const v=function(e){var t=e.param;return a.createElement("div",{className:"field-group"},a.createElement("div",{style:{maxWidth:500},dangerouslySetInnerHTML:{__html:(0,l.sanitize)(t.value)}}))};const g=function(e){var t=e.param,n=t.hidden?{display:"none"}:{};return a.createElement("div",{style:n,dangerouslySetInnerHTML:{__html:(0,l.sanitize)(t.value)}})};var b=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},e(t,n)};return function(t,n){if("function"!=typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),w=function(){return w=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},w.apply(this,arguments)},E=function(e,t,n,r){return new(n||(n=Promise))((function(a,o){function i(e){try{c(r.next(e))}catch(e){o(e)}}function l(e){try{c(r.throw(e))}catch(e){o(e)}}function c(e){var t;e.done?a(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(i,l)}c((r=r.apply(e,t||[])).next())}))},x=function(e,t){var n,r,a,o,i={label:0,sent:function(){if(1&a[0])throw a[1];return a[1]},trys:[],ops:[]};return o={next:l(0),throw:l(1),return:l(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function l(o){return function(l){return function(o){if(n)throw new TypeError("Generator is already executing.");for(;i;)try{if(n=1,r&&(a=2&o[0]?r.return:o[0]?r.throw||((a=r.return)&&a.call(r),0):r.next)&&!(a=a.call(r,o[1])).done)return a;switch(r=0,a&&(o=[2&o[0],a.value]),o[0]){case 0:case 1:a=o;break;case 4:return i.label++,{value:o[1],done:!1};case 5:i.label++,r=o[1],o=[0];continue;case 7:o=i.ops.pop(),i.trys.pop();continue;default:if(!(a=i.trys,(a=a.length>0&&a[a.length-1])||6!==o[0]&&2!==o[0])){i=0;continue}if(3===o[0]&&(!a||o[1]>a[0]&&o[1]<a[3])){i.label=o[1];break}if(6===o[0]&&i.label<a[1]){i.label=a[1],a=o;break}if(a&&i.label<a[2]){i.label=a[2],i.ops.push(o);break}a[2]&&i.ops.pop(),i.trys.pop();continue}o=t.call(e,i)}catch(e){o=[6,e],r=0}finally{n=a=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,l])}}},C=AJS.$;const S=function(e){function t(t){var r=e.call(this,t)||this;return r.ignoreSelectionChange=!1,r.isProcessed=!1,r.isPageTree=function(){return["pagetree"].includes(r.props.param.type)},n(56422),n(16113),r}return b(t,e),t.prototype.updateNodeUnselectable=function(e,t){e.hideChildren&&t.visit((function(e){t.isSelected()||t.unselectable?(e.setSelected(!1),e.unselectable=!0,C(e.li).find("span.fancytree-node").addClass("fancytree-unselectable")):(e.unselectable=!1,C(e.li).find("span.fancytree-node").removeClass("fancytree-unselectable"))}))},t.prototype.generateTreeOptions=function(){var e=this,t=this.props.param,n={};"options"in t&&((n=t.options).hideChildren=void 0===n.hideChildren||n.hideChildren);var r=this.props,a={escapeTitles:!0,select:function(t,a){var o=a.node,i=a.tree.getSelectedNodes().map((function(e){return e.key}));e.updateNodeUnselectable(n,o),e.ignoreSelectionChange||r.onChange(i)},init:function(n,r){t.keyPaths||(t.value||[]).forEach((function(t){return E(e,void 0,void 0,(function(){var e;return x(this,(function(n){switch(n.label){case 0:return(e=r.tree.getNodeByKey(t))?(e.setSelected(!0),[4,e.getParent().setExpanded(!0)]):[3,2];case 1:n.sent(),n.label=2;case 2:return[2]}}))}))}))},persist:!1,autoFocus:!1,selectMode:2,imagePath:"images/"};return n.singleSelect&&(a.select=function(e,t){var n=t.tree.getSelectedNodes().map((function(e){return e.key}));r.onChange(n)}),t.source&&(n.source=t.source),t.postProcess&&(a.postProcess=function(n,r){e.isProcessed||(t.postProcess(n,r),e.isProcessed=!0)}),t.lazyLoad&&(n.lazyLoad=t.lazyLoad),n.loadChildren=function(t,r){e.updateNodeUnselectable(n,r.node)},C.extend(a,n),a},t.prototype.componentDidMount=function(){var e=this.generateTreeOptions();C(this.el).fancytree(e),C(".fancytree-container").css("min-height","85px")},t.prototype.componentDidUpdate=function(e){if(this.isPageTree()){C(this.el).fancytree("destroy");var t=this.generateTreeOptions();C(this.el).fancytree(t),C(".fancytree-container").css("min-height","85px")}},t.prototype.shouldComponentUpdate=function(e){return!this.isPageTree()||void 0!==this.props.update&&this.props.update!==e.param.source.data.nodeId},t.prototype.render=function(){var e=this,t=this.props.param;return a.createElement(i.Z,w({},this.props,{param:t}),a.createElement("div",{className:"clearfix"},a.createElement("div",{style:{float:"left",margin:0,width:t.treeWidth?t.treeWidth:"70%",maxHeight:300,overflowY:"auto"},className:"long-field"},a.createElement("div",{className:"pagetree",ref:function(t){return e.el=t}}))),a.createElement("div",{style:{clear:"both"}}))},t}(a.Component);var O=function(){return O=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},O.apply(this,arguments)};const P=function(e){var t=e.param,n=t.value,r=t.values;return a.createElement(i.Z,O({},e,{param:t}),a.createElement("div",{key:r[0][0]},a.createElement("input",{className:"checkbox",type:"checkbox",defaultChecked:"string"==typeof n?"true"===n:n,onChange:function(t){return e.onChange(t.currentTarget.checked?"true":"")},disabled:t.disabled}),a.createElement("label",{style:{paddingLeft:"5px"}},r[0][1])))};var j=function(){return j=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},j.apply(this,arguments)};const k=function(e){var t=e.param,n=t.value,r=void 0===n?[]:n,o=t.values,l=void 0===o?[[t.name,""]]:o,c=new Set(r);return a.createElement(i.Z,j({},e,{param:t}),l.map((function(n){var r="".concat(t.name,"-").concat(n[0]);return a.createElement("div",{key:n[0],className:"checkbox"},a.createElement("input",{id:r,className:"checkbox",type:"checkbox",checked:c.has(n[0]),onChange:function(){return t=n[0],c.has(t)?c.delete(t):c.add(t),e.onChange(Array.from(c));var t},disabled:t.disabled}),a.createElement("label",{htmlFor:r},n[1]))})))};var T=function(){return T=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},T.apply(this,arguments)};const A=function(e){var t=e.param,n="on"===t.value;return a.createElement(i.Z,T({},e,{param:t}),a.createElement("input",{className:"checkbox",type:"checkbox",checked:n,onChange:function(t){return e.onChange(t.currentTarget.checked?"on":"")},disabled:t.disabled}))};const D=function(e){var t=e.param;return e.param,"values"in t?a.createElement(k,{param:e.param,onChange:e.onChange,errorMessage:e.errorMessage}):a.createElement(A,{param:e.param,onChange:e.onChange,errorMessage:e.errorMessage})};var M=function(){return M=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},M.apply(this,arguments)};const I=function(e){var t=e.param;return a.createElement(i.Z,M({},e,{param:t}),t.values.map((function(n){return a.createElement("div",{className:"radio",key:n[0]},a.createElement("input",{className:"radio",type:"radio",name:t.name,id:n[0],checked:n[0]===t.value,onChange:function(){return e.onChange(n[0])},disabled:t.disabled}),a.createElement("label",{htmlFor:n[0],onClick:function(){return e.onChange(n[0])}},n[1]))})))};var N=n(88162),L=n(39507),Z=function(){return Z=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},Z.apply(this,arguments)},_=function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,a,o=n.call(e),i=[];try{for(;(void 0===t||t-- >0)&&!(r=o.next()).done;)i.push(r.value)}catch(e){a={error:e}}finally{try{r&&!r.done&&(n=o.return)&&n.call(o)}finally{if(a)throw a.error}}return i},F=function(e){return{value:e.id,label:e.text}},R={"System Fields":function(e){return["assignee","reporter","componentlead","projectlead","watchers"].includes(e.toLowerCase())},"Custom Fields":function(e){return e.startsWith("customfield_")},Groups:function(e){return e.startsWith("group:")},"Project roles":function(e){return e.startsWith("role:")},Other:function(){return!0}},z={invalidItemSelected:function(e){return"Failed to find item with key: ".concat(e)},placeholder:"Select field(s) / role(s) / group(s)",fetchSingleItemByKey:function(e){return"".concat(AJS.contextPath(),"/rest/scriptrunner-jira/latest/sendcustomemail?id=").concat(e)},searchUrl:function(e){return"".concat(AJS.contextPath(),"/rest/scriptrunner-jira/latest/sendcustomemail/search?").concat((0,L.F)({query:e}))},mapSearchResultToSuggestions:function(e){var t=e.results.reduce((function(e,t){var n=Object.entries(R).find((function(e){var n=_(e,2);n[0];return(0,n[1])(t.id)}))[0];return e[n]?e[n].push(t):e[n]=[t],e}),{});return Object.entries(t).map((function(e){var t=_(e,2);return{label:t[0],options:t[1].map(F)}}))},mapItemToOption:function(e){return{value:e.id,label:e.text}},getTotalSearchResults:function(e){return e.total}};const U=function(e){return a.createElement(N.Z,Z({},e,z,{minCharacters:1}))};var B=function(){return B=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},B.apply(this,arguments)};const J=function(e){var t=e.param;return a.createElement(i.Z,B({},e,{param:t,key:t.name}),a.createElement(U,{type:"multiple",value:t.value,handleChange:e.onChange,isDisabled:t.disabled}))};var q=n(68814),V=function(){return V=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},V.apply(this,arguments)};const W=function(e){var t=e.param;return a.createElement(i.Z,V({},e,{param:t,key:t.name}),a.createElement(q.a,{editorId:t.name,initialValue:t.value,compileContextDescriptor:t.compileContextDescriptor,onChange:e.onChange,description:t.description,readonly:t.disabled}))};var G=function(){return G=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},G.apply(this,arguments)},Q=function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,a,o=n.call(e),i=[];try{for(;(void 0===t||t-- >0)&&!(r=o.next()).done;)i.push(r.value)}catch(e){a={error:e}}finally{try{r&&!r.done&&(n=o.return)&&n.call(o)}finally{if(a)throw a.error}}return i},K=function(e,t,n){if(n||2===arguments.length)for(var r,a=0,o=t.length;a<o;a++)!r&&a in t||(r||(r=Array.prototype.slice.call(t,0,a)),r[a]=t[a]);return e.concat(r||Array.prototype.slice.call(t))};const Y=function(e){var t=e.param;return a.createElement(i.Z,G({},e,{param:t,key:t.name}),a.createElement("select",{"data-cy":t.name,value:Boolean(t.value)?t.value:[],onChange:function(t){return e.onChange(K([],Q(Array.from(t.currentTarget.options)),!1).filter((function(e){return e.selected})).map((function(e){return e.value})))},disabled:t.disabled,className:"select",multiple:!0,size:6},t.values.map((function(e){return a.createElement("option",{key:e[0],value:e[0]},e[1])}))))};var H=function(){return H=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},H.apply(this,arguments)};const X=function(e){var t=e.param;return a.useEffect((function(){if(!t.value&&t.values.length>0&&!t.values.some((function(e){return!e[0]}))){var n=t.values.length>0?t.values[0][0]:null;e.onChange(n)}}),[t.value,t.values]),a.createElement(i.Z,H({},e,{param:t}),a.createElement("select",{"data-cy":t.name,className:"select long-field",value:t.value,onChange:function(t){return e.onChange(t.currentTarget.value)},disabled:t.disabled},t.values.map((function(e){return a.createElement("option",{key:e[0],value:e[0]},e[1])}))),t.waiting&&a.createElement("span",{className:"aui-icon aui-icon-wait",style:{marginTop:7,marginLeft:5}},"Wait"))};var ee=n(39997),te=function(){return te=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},te.apply(this,arguments)},ne=te(te({},ee._.BaseUserOptions),{fetchSingleItemByKey:function(e){return"".concat(AJS.contextPath(),"/rest/api/2/user?key=").concat(e)},searchUrl:function(e){return"".concat(AJS.contextPath(),"/rest/api/2/user/search?").concat((0,L.F)({username:e,includeInactive:!0}))},mapItemToOption:function(e){return{value:e.key,label:e.displayName,icon:e.avatarUrls["16x16"]}},mapSearchResultToSuggestions:function(e){return e?e.map((function(e){return{value:e.key,label:e.displayName,icon:e.avatarUrls["16x16"],html:"<strong>".concat(e.displayName," - ").concat(e.emailAddress,"</strong>")}})):[]},formatOptionElement:function(e){return a.createElement("span",{dangerouslySetInnerHTML:{__html:(0,l.sanitize)(e.data.html)}})},getTotalSearchResults:function(e){return e.total}});const re=function(e){return a.createElement(N.Z,te({},e,ne))};var ae=function(){return ae=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},ae.apply(this,arguments)},oe=ae(ae({},ee._.BaseUserOptions),{fetchSingleItemByKey:function(e){return"".concat(AJS.contextPath(),"/rest/scriptrunner-bamboo/latest/user/").concat(e)},mapItemToOption:function(e){return{value:e.id,label:e.fullName,icon:e.avatarUrl}},searchUrl:function(e){return"".concat(AJS.contextPath(),"/rest/api/latest/search/users.json?").concat((0,L.F)({searchTerm:e,includeAvatars:!0}))},mapSearchResultToSuggestions:function(e){return e.searchResults.map((function(e){return{value:e.id,label:e.searchEntity.fullName,icon:e.searchEntity.avatarUrl}}))},getTotalSearchResults:function(e){return e.size}});const ie=function(e){return a.createElement(N.Z,ae({},e,oe))};var le=n(66638),ce=n(98581),ue=n(97330),se=function(){return se=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},se.apply(this,arguments)},pe=se(se({},ee._.BaseUserOptions),{fetchSingleItemByKey:function(e){return"".concat(AJS.contextPath(),"/rest/scriptrunner-bitbucket/latest/user/").concat(e)},mapItemToOption:function(e){return{value:e.id,label:e.displayName,icon:e.avatarUrl}},invalidItemSelected:function(e){return"Failed to find user with ID: ".concat(e)},searchUrl:function(e){return"".concat(AJS.contextPath(),"/rest/api/latest/users?&").concat((0,L.F)({"permission.1":"LICENSED_USER",avatarSize:32,start:0,filter:e}))},mapSearchResultToSuggestions:function(e){return e.values.map((function(e){return{value:e.id,label:e.displayName,icon:e.avatarUrl}}))},getTotalSearchResults:function(e){return e.size}}),de=function(e){if(e){if("string"==typeof e)return[e];if("number"==typeof e)return[String(e)];if(Array.isArray(e))return e.map(String);throw new Error("Invalid bitbucket user picker value type: "+typeof e)}return[]};const fe=function(e){return a.createElement(N.Z,se({},e,{value:de(e.value)},pe))};var he=function(){return he=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},he.apply(this,arguments)};const me=function(e){var t=e.param,n={type:"multiple"===t.plurality?"multiple":"single",handleChange:e.onChange,value:t.value,dataContainerClass:"long",isDisabled:t.disabled},r=(0,le.S)({confluence:a.createElement(ce.U,he({},n)),bamboo:a.createElement(ie,he({},n)),bitbucket:a.createElement(fe,he({},n)),jira:"userIncludingInactive"===t.type?a.createElement(re,he({},n)):a.createElement(ue.Z,he({},n)),default:a.createElement("span",null,"Not supported")});return a.createElement(i.Z,he({},e,{param:t}),a.createElement("div",{style:{maxWidth:500}},r))};var ye=n(85767),ve=n(24692),ge=n(67001),be=function(){return be=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},be.apply(this,arguments)};const we=function(e){var t=e.param,n={type:"multiple"===t.plurality?"multiple":"single",handleChange:e.onChange,value:t.value,dataContainerClass:"long",placeholder:null===t.placeholder||void 0===t.placeholder?ee._.BaseGroupOptions.placeholder:t.placeholder},r=(0,le.S)({confluence:a.createElement(ve.Yj,be({},n)),jira:a.createElement(ge.Z,be({},n)),bitbucket:a.createElement(ye.Z,be({},n)),default:a.createElement("span",null,"Not supported")});return a.createElement(i.Z,be({},e,{param:t}),a.createElement("div",{style:{maxWidth:500}},r))};var Ee=n(39731),xe=function(){return xe=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},xe.apply(this,arguments)};const Ce=function(e){var t=e.param,n={type:"multiple"===t.plurality?"multiple":"single",handleChange:e.onChange,value:t.value};return a.createElement(i.Z,xe({},e,{param:t}),a.createElement("div",{style:{maxWidth:500}},a.createElement(Ee.p,xe({},n,{isDisabled:t.disabled}))))};var Se=function(){return Se=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},Se.apply(this,arguments)};const Oe=function(e){var t=e.param;return a.createElement(i.Z,Se({},e,{param:t}),a.createElement("textarea",{className:"textarea long-field",style:{maxWidth:500},value:t.value,onChange:function(t){return e.onChange(t.currentTarget.value)}}))};var Pe,je,ke,Te,Ae=n(18390),De=n(5661),Me=n(78417),Ie=n(21705),Ne=function(e,t){return Object.defineProperty?Object.defineProperty(e,"raw",{value:t}):e.raw=t,e},Le=function(){return Le=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},Le.apply(this,arguments)},Ze=function(e,t,n,r){return new(n||(n=Promise))((function(a,o){function i(e){try{c(r.next(e))}catch(e){o(e)}}function l(e){try{c(r.throw(e))}catch(e){o(e)}}function c(e){var t;e.done?a(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(i,l)}c((r=r.apply(e,t||[])).next())}))},_e=function(e,t){var n,r,a,o,i={label:0,sent:function(){if(1&a[0])throw a[1];return a[1]},trys:[],ops:[]};return o={next:l(0),throw:l(1),return:l(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function l(o){return function(l){return function(o){if(n)throw new TypeError("Generator is already executing.");for(;i;)try{if(n=1,r&&(a=2&o[0]?r.return:o[0]?r.throw||((a=r.return)&&a.call(r),0):r.next)&&!(a=a.call(r,o[1])).done)return a;switch(r=0,a&&(o=[2&o[0],a.value]),o[0]){case 0:case 1:a=o;break;case 4:return i.label++,{value:o[1],done:!1};case 5:i.label++,r=o[1],o=[0];continue;case 7:o=i.ops.pop(),i.trys.pop();continue;default:if(!(a=i.trys,(a=a.length>0&&a[a.length-1])||6!==o[0]&&2!==o[0])){i=0;continue}if(3===o[0]&&(!a||o[1]>a[0]&&o[1]<a[3])){i.label=o[1];break}if(6===o[0]&&i.label<a[1]){i.label=a[1],a=o;break}if(a&&i.label<a[2]){i.label=a[2],i.ops.push(o);break}a[2]&&i.ops.pop(),i.trys.pop();continue}o=t.call(e,i)}catch(e){o=[6,e],r=0}finally{n=a=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,l])}}},Fe=function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,a,o=n.call(e),i=[];try{for(;(void 0===t||t-- >0)&&!(r=o.next()).done;)i.push(r.value)}catch(e){a={error:e}}finally{try{r&&!r.done&&(n=o.return)&&n.call(o)}finally{if(a)throw a.error}}return i},Re=function(e){var t=e.data,n=e.isOpen,r=e.style,o=e.toggle,i=e.iconRenderer,l=t.id,c=t.checked,u=t.disabled,s=t.name,p=t.nestingLevel,d=t.lazyLoadChildren,f=t.loadChildren,h=t.checkNode,m=t.isLeaf,y=t.checkbox,v=Fe((0,a.useState)(!1),2),g=v[0],b=v[1],w=Fe((0,a.useState)(!1),2),E=w[0],x=w[1],C=function(e){return Ze(void 0,void 0,void 0,(function(){var t;return _e(this,(function(r){switch(r.label){case 0:if(e.preventDefault(),e.stopPropagation(),n||!d)return[3,5];b(!0),r.label=1;case 1:return r.trys.push([1,3,,4]),[4,f(l)];case 2:return r.sent(),o(),[3,4];case 3:return t=r.sent(),console.error("Error loading child nodes:",t),x(!0),[3,4];case 4:return[3,6];case 5:o(),r.label=6;case 6:return[2]}}))}))};return a.createElement(Ue,{style:r,nestingLevel:p,id:l},y&&a.createElement("div",{style:{display:"flex",alignItems:"center"}},a.createElement("input",{onChange:function(e){return h(l,e.currentTarget.checked)},className:"checkbox",type:"checkbox",name:"".concat(l,"-checkbox"),id:"".concat(l,"-checkbox"),checked:c,disabled:u})),a.createElement(ze,{id:"".concat(l,"-icon-container"),onClick:C,isLeaf:m},E&&a.createElement(Ie.Z,{label:"Error loading children for node: ".concat(s),size:"medium"}),!E&&(g?a.createElement(Me.Z,{size:"small"}):i(t,n))),a.createElement("span",{onClick:C,id:"name-span-".concat(l.replace(":","-")),style:Le({marginLeft:5},m?{}:{cursor:"pointer"})},s))},ze=Ae.Z.div(Pe||(Pe=Ne(["\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    margin-left: 7px;\n    ",";\n"],["\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    margin-left: 7px;\n    ",";\n"])),(function(e){return!e.isLeaf&&{cursor:"pointer"}})),Ue=Ae.Z.div(je||(je=Ne(["\n    display: flex;\n    align-items: center;\n    margin-left: ","px;\n\n    input {\n        margin: 0;\n    }\n    width: max-content !important;\n"],["\n    display: flex;\n    align-items: center;\n    margin-left: ","px;\n\n    input {\n        margin: 0;\n    }\n    width: max-content !important;\n"])),(function(e){return 23*e.nestingLevel})),Be=n(74729),Je=function(e,t,n,r){return new(n||(n=Promise))((function(a,o){function i(e){try{c(r.next(e))}catch(e){o(e)}}function l(e){try{c(r.throw(e))}catch(e){o(e)}}function c(e){var t;e.done?a(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(i,l)}c((r=r.apply(e,t||[])).next())}))},qe=function(e,t){var n,r,a,o,i={label:0,sent:function(){if(1&a[0])throw a[1];return a[1]},trys:[],ops:[]};return o={next:l(0),throw:l(1),return:l(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function l(o){return function(l){return function(o){if(n)throw new TypeError("Generator is already executing.");for(;i;)try{if(n=1,r&&(a=2&o[0]?r.return:o[0]?r.throw||((a=r.return)&&a.call(r),0):r.next)&&!(a=a.call(r,o[1])).done)return a;switch(r=0,a&&(o=[2&o[0],a.value]),o[0]){case 0:case 1:a=o;break;case 4:return i.label++,{value:o[1],done:!1};case 5:i.label++,r=o[1],o=[0];continue;case 7:o=i.ops.pop(),i.trys.pop();continue;default:if(!(a=i.trys,(a=a.length>0&&a[a.length-1])||6!==o[0]&&2!==o[0])){i=0;continue}if(3===o[0]&&(!a||o[1]>a[0]&&o[1]<a[3])){i.label=o[1];break}if(6===o[0]&&i.label<a[1]){i.label=a[1],a=o;break}if(a&&i.label<a[2]){i.label=a[2],i.ops.push(o);break}a[2]&&i.ops.pop(),i.trys.pop();continue}o=t.call(e,i)}catch(e){o=[6,e],r=0}finally{n=a=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,l])}}},Ve=function(e){var t="function"==typeof Symbol&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},We=function(e){return{id:e.id,name:e.name,lazyLoadChildren:e.lazyLoadChildren,openByDefault:e.openByDefault,checked:!1,disabled:!1,children:e.children?e.children.map(We):[],checkbox:void 0===e.checkbox||e.checkbox}},Ge=function(e){return Je(void 0,void 0,Promise,(function(){var t;return qe(this,(function(n){switch(n.label){case 0:return[4,Qe(e)];case 1:return t=n.sent(),et(t,e),[2,t]}}))}))},Qe=function(e){return Je(void 0,void 0,Promise,(function(){return qe(this,(function(t){return e.options.lazyLoad?e.value?[2,Xe(e,{currentValue:e.value})]:[2,e.options.isFancyTreeApi?He(e):$e(e)]:[2,e.options.nodes.map((function(e){return We(e)}))]}))}))},Ke=function(e,t,n){return Je(void 0,void 0,Promise,(function(){var r,a,o,i;return qe(this,(function(l){switch(l.label){case 0:return r=t.map(at),a=Ye(r,e),o=a,n.options.isFancyTreeApi?[4,He(n,e)]:[3,2];case 1:return i=l.sent(),[3,4];case 2:return[4,$e(n,e)];case 3:i=l.sent(),l.label=4;case 4:return o.children=i,n.options.isFancyTreeApi&&!n.options.hideChildren||et(r,n),[2,r]}}))}))},Ye=function(e,t){var n,r;try{for(var a=Ve(e),o=a.next();!o.done;o=a.next()){var i=o.value;if(i.id===t)return i;if(i.children&&i.children.length){var l=Ye(i.children,t);if(l)return l}}}catch(e){n={error:e}}finally{try{o&&!o.done&&(r=a.return)&&r.call(a)}finally{if(n)throw n.error}}},$e=function(e,t){var n=e.options;e.value;return Je(void 0,void 0,Promise,(function(){var e,r,a,o;return qe(this,(function(i){switch(i.label){case 0:return e="".concat(AJS.contextPath(),"/").concat(n.path),t&&(e+="/".concat(t)),[4,(0,Be.wrappedFetch)(e)];case 1:if(r=i.sent(),a=r.result,o=r.error)throw new Error(o);return[2,a.map((function(e){return We(e)}))]}}))}))},He=function(e,t){var n=e.options;return Je(void 0,void 0,Promise,(function(){var e,r,a,o;return qe(this,(function(i){switch(i.label){case 0:return e="".concat(AJS.contextPath(),"/").concat(n.path),e=t?e.replace(/(nodeId=).*?(&)/,"$1"+t+"$2")+"&mode=children":"".concat(e,"&mode=root"),[4,(0,Be.wrappedFetch)(e)];case 1:if(r=i.sent(),a=r.result,o=r.error)throw new Error(o);return[2,a.map((function(e){return{id:e.key+"",name:e.title,lazyLoadChildren:e.lazy,openByDefault:!1,children:[],checkbox:e.checkbox}})).map((function(e){return We(e)}))]}}))}))},Xe=function(e,t){var n=e.options;return Je(void 0,void 0,Promise,(function(){var e,r,a,o;return qe(this,(function(i){switch(i.label){case 0:return e="".concat(AJS.contextPath(),"/").concat(n.path),[4,(0,Be.wrappedFetch)(e,{method:"POST",body:JSON.stringify(t)})];case 1:if(r=i.sent(),a=r.result,o=r.error)throw new Error(o);return[2,a.map((function(e){return We(e)}))]}}))}))},et=function(e,t){t.value&&nt(e,(function(e){return t.value.includes(e.id)&&(e.checked=!0,tt(e,!0)),!0}))},tt=function(e,t){rt(e,(function(n){return n.id!==e.id&&(t&&(n.checked=!1),n.disabled=t),!0}))},nt=function(e,t){e.forEach((function(e){return rt(e,t)}))},rt=function(e,t){t(e)&&e.children&&nt(e.children,t)},at=function(e){return{id:e.id,name:e.name,children:e.children.map(at),lazyLoadChildren:e.lazyLoadChildren,checked:e.checked,disabled:e.disabled,openByDefault:e.openByDefault,checkbox:e.checkbox}},ot=n(7011),it=function(e,t){var n,r,a,o,i={label:0,sent:function(){if(1&a[0])throw a[1];return a[1]},trys:[],ops:[]};return o={next:l(0),throw:l(1),return:l(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function l(o){return function(l){return function(o){if(n)throw new TypeError("Generator is already executing.");for(;i;)try{if(n=1,r&&(a=2&o[0]?r.return:o[0]?r.throw||((a=r.return)&&a.call(r),0):r.next)&&!(a=a.call(r,o[1])).done)return a;switch(r=0,a&&(o=[2&o[0],a.value]),o[0]){case 0:case 1:a=o;break;case 4:return i.label++,{value:o[1],done:!1};case 5:i.label++,r=o[1],o=[0];continue;case 7:o=i.ops.pop(),i.trys.pop();continue;default:if(!(a=i.trys,(a=a.length>0&&a[a.length-1])||6!==o[0]&&2!==o[0])){i=0;continue}if(3===o[0]&&(!a||o[1]>a[0]&&o[1]<a[3])){i.label=o[1];break}if(6===o[0]&&i.label<a[1]){i.label=a[1],a=o;break}if(a&&i.label<a[2]){i.label=a[2],i.ops.push(o);break}a[2]&&i.ops.pop(),i.trys.pop();continue}o=t.call(e,i)}catch(e){o=[6,e],r=0}finally{n=a=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,l])}}},lt=function(e,t){return Object.defineProperty?Object.defineProperty(e,"raw",{value:t}):e.raw=t,e},ct=function(e,t,n,r){return new(n||(n=Promise))((function(a,o){function i(e){try{c(r.next(e))}catch(e){o(e)}}function l(e){try{c(r.throw(e))}catch(e){o(e)}}function c(e){var t;e.done?a(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(i,l)}c((r=r.apply(e,t||[])).next())}))},ut=function(e,t){var n,r,a,o,i={label:0,sent:function(){if(1&a[0])throw a[1];return a[1]},trys:[],ops:[]};return o={next:l(0),throw:l(1),return:l(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function l(o){return function(l){return function(o){if(n)throw new TypeError("Generator is already executing.");for(;i;)try{if(n=1,r&&(a=2&o[0]?r.return:o[0]?r.throw||((a=r.return)&&a.call(r),0):r.next)&&!(a=a.call(r,o[1])).done)return a;switch(r=0,a&&(o=[2&o[0],a.value]),o[0]){case 0:case 1:a=o;break;case 4:return i.label++,{value:o[1],done:!1};case 5:i.label++,r=o[1],o=[0];continue;case 7:o=i.ops.pop(),i.trys.pop();continue;default:if(!(a=i.trys,(a=a.length>0&&a[a.length-1])||6!==o[0]&&2!==o[0])){i=0;continue}if(3===o[0]&&(!a||o[1]>a[0]&&o[1]<a[3])){i.label=o[1];break}if(6===o[0]&&i.label<a[1]){i.label=a[1],a=o;break}if(a&&i.label<a[2]){i.label=a[2],i.ops.push(o);break}a[2]&&i.ops.pop(),i.trys.pop();continue}o=t.call(e,i)}catch(e){o=[6,e],r=0}finally{n=a=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,l])}}},st=function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,a,o=n.call(e),i=[];try{for(;(void 0===t||t-- >0)&&!(r=o.next()).done;)i.push(r.value)}catch(e){a={error:e}}finally{try{r&&!r.done&&(n=o.return)&&n.call(o)}finally{if(a)throw a.error}}return i},pt=function(e){var t=e.param,n=e.onChange,r=e.errorMessage,o=e.iconRenderer,l=st((0,a.useState)(!0),2),c=l[0],u=l[1],s=st((0,a.useState)(),2),p=s[0],d=s[1];(0,a.useEffect)((function(){ct(void 0,void 0,void 0,(function(){var e,n;return ut(this,(function(r){switch(r.label){case 0:u(!0),r.label=1;case 1:return r.trys.push([1,3,,4]),[4,Ge(t)];case 2:return e=r.sent(),d(e),u(!1),[3,4];case 3:return n=r.sent(),(0,ot.k1)("Error loading tree data: ".concat(n)),[3,4];case 4:return[2]}}))}))}),[t.options.path]);var f=st((0,a.useState)(!1),2),h=f[0],m=f[1],y=(0,a.useCallback)(function(e,t,n){return function(r){var a,o,i,l,c,u,s,p,d,f,h,m,y,v,g;return it(this,(function(b){switch(b.label){case 0:a=e.map((function(e){return{node:e,nestingLevel:0}})),b.label=1;case 1:return 0===a.length?[3,3]:(o=a.pop(),i=o.node,l=i.children,c=void 0===l?[]:l,u=i.id,s=i.name,p=i.lazyLoadChildren,d=i.checked,f=i.disabled,h=i.openByDefault,m=i.checkbox,y=o.nestingLevel,[4,r?{id:u,isLeaf:!p&&0===c.length,checked:d,disabled:f,isOpenByDefault:h,name:s,nestingLevel:y,lazyLoadChildren:p,loadChildren:t,checkNode:n,checkbox:m}:u]);case 2:if(v=b.sent(),0!==c.length&&v)for(g=c.length-1;g>=0;g--)a.push({nestingLevel:y+1,node:c[g]});return[3,1];case 3:return[2]}}))}}(p,(function(e){return ct(void 0,void 0,void 0,(function(){var n;return ut(this,(function(r){switch(r.label){case 0:return n=d,[4,Ke(e,p,t)];case 1:return n.apply(void 0,[r.sent()]),[2]}}))}))}),t.options.isFancyTreeApi?function(e,r){var a=t.options.selectMode,o=t.options.hideChildren,i=function(e,t,n,r){var a=e.map(at),o=Ye(a,t),i=r.selectMode,l=r.hideChildren;return 1===i?nt(a,(function(e){return e.checked=!1,e.disabled=!1,!0})):3===i&&rt(o,(function(e){return e.checked=n,!0})),l&&tt(o,n),o.checked=n,a}(p,e,r,{selectMode:a,hideChildren:o});d(i),n(function(e,t){var n=[];return nt(e,(function(e){return!e.checked||(n.push(e.id),3===t)})),n}(i,a))}:function(e,t){var r=function(e,t,n){var r=e.map(at),a=Ye(r,t);return a.checked=n,tt(a,n),r}(p,e,t);d(r),n(function(e){var t=[];return nt(e,(function(e){return!e.checked||(t.push(e.id),!1)})),t}(r))}),[p]);return a.createElement(i.Z,{param:t,errorMessage:r},a.createElement(dt,{showOverflow:t.options.isFancyTreeApi},c&&a.createElement(ft,{"data-test-id":"vtree-loading"},a.createElement(Me.Z,{size:"large"})),!c&&a.createElement(De.NC,{treeWalker:y,itemSize:25,height:400,maxHeight:400,width:492,style:{overflowX:t.options.isFancyTreeApi?"auto":"hidden"},ref:function(e){e&&!h&&(t.value&&t.value.length&&e.scrollToItem(t.value[0],"center"),m(!0))}},(function(e){var t=e.data,n=e.isOpen,r=e.toggle,i=e.style;return a.createElement(Re,{data:t,isOpen:n,toggle:r,iconRenderer:o,style:i})}))))},dt=Ae.Z.div(ke||(ke=lt(["\n    width: 500px;\n    border: 1px solid #dfe1e6;\n    border-radius: 3px;\n    padding-left: 7px;\n    box-sizing: border-box;\n    ","\n"],["\n    width: 500px;\n    border: 1px solid #dfe1e6;\n    border-radius: 3px;\n    padding-left: 7px;\n    box-sizing: border-box;\n    ","\n"])),(function(e){return e.showOverflow?"\n            max-height: 400px;     \n            min-height: 85px;\n            & > div {\n                max-height: inherit; \n                height: auto !important;  \n            }\n           ":"\n           overflow: hidden;\n           "})),ft=Ae.Z.div(Te||(Te=lt(["\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    width: 100%;\n    height: 100%;\n"],["\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    width: 100%;\n    height: 100%;\n"])));const ht="data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMTZweCIgaGVpZ2h0PSIxNnB4IiB2aWV3Qm94PSIwIDAgMTYgMTYiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgo+CiAgICA8ZyB0cmFuc2Zvcm09InRyYW5zbGF0ZSgwLjAwMDAwMCwgMi4wMDAwMDApIj4KICAgICAgICA8cGF0aCBkPSJNMTUuOTAwMDAwMSw4IEwxNS45MDAwMDAxLDguMiBMMTUuNTk5MTc2OSwxMC4yIEMxNS41MDAyMzUxLDExLjIgMTQuNTk5NzY0NSwxMiAxMy42MDAzNTIsMTIgTDIuNDA3OTMxNTIsMTIgQzEuNDA4NTE5MDQsMTIgMC41MDkwNDc4LDExLjIgMC40MDkxMDY1NTEsMTAuMiBMMC4xMDgyODMzOTMsOC4yIEMwLjAwOTM0MTU1NjU2LDcuMSAwLjgwNzg3MjEzMyw2LjEgMS45MDgyMjUyOCw2IEwyLjEwNzEwODM3LDYgTDEzLjkwMTE3NTEsNiBDMTUuMDAwNTI4OSw2IDE1LjkwMDAwMDEsNi45IDE1LjkwMDAwMDEsOCBaIE0xLjgxODI3ODE2LDUuMDA0IEMxLjUzMTQ0Njc3LDUuMDMgMS4yNjM2MDQyMyw1LjEwOSAxLjAwODc1NDA0LDUuMjExIEwxLjAwODc1NDA0LDIgQzEuMDA4NzU0MDQsMC44OTcgMS45MDUyMjcwNCwwIDMuMDA3NTc5MDIsMCBMNy4wOTIxNzc4NSwwIEM3LjY2ODgzODg1LDAgOC4yMTU1MTc0OCwwLjI0OCA4LjU5NDI5NDgyLDAuNjgyIEw5Ljc1MzYxMzMsMiBMMTMuMDAxNzAzOSwyIEMxNC4xMDMwNTY0LDIgMTUuMDAwNTI4OSwyLjg5NyAxNS4wMDA1Mjg5LDQgTDE1LjAwMDUyODksNS4yMTYgQzE0LjY4ODcxMjIsNS4wOTMgMTQuMzUxOTEwMiw1LjAyMSAxNC4wMDExMTY0LDUuMDEgTDE0LjAwMTExNjQsNCBDMTQuMDAxMTE2NCwzLjQ0OCAxMy41NTIzODAyLDMgMTMuMDAxNzAzOSwzIEw5LjI5OTg4MDAzLDMgTDcuODQyNzM2NjMsMS4zNDEgQzcuNjUzODQ3NjcsMS4xMjQgNy4zODEwMDgwNiwxIDcuMDkyMTc3ODUsMSBMMy4wMDc1NzkwMiwxIEMyLjQ1NTkwMzMyLDEgMi4wMDgxNjY1MywxLjQ0OCAyLjAwODE2NjUzLDIgTDIuMDA4MTY2NTMsNS4wMDIgTDEuODE4Mjc4MTYsNS4wMDQgWiIKICAgICAgICAgICAgICBmaWxsPSIjNTA1Rjc5Ii8+CiAgICA8L2c+Cjwvc3ZnPgo=",mt="data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMTZweCIgaGVpZ2h0PSIxNnB4IiB2aWV3Qm94PSIwIDAgMTYgMTYiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgo+CiAgICA8cGF0aCBkPSJNMTMsNCBMOS43NSw0IEw4LjU5MSwyLjY4MSBDOC4yMTEsMi4yNDggNy42NjQsMiA3LjA4OCwyIEwzLDIgQzEuOSwyIDEsMi45IDEsNCBMMSwxMiBDMSwxMy4xIDEuOSwxNCAzLDE0IEwxMywxNCBDMTQuMSwxNCAxNSwxMy4xIDE1LDEyIEwxNSw2IEMxNSw0LjkgMTQuMSw0IDEzLDQiCiAgICAgICAgICBmaWxsPSIjNTA1Rjc5Ii8+Cjwvc3ZnPgo=";var yt=function(e,t){var n=e.isLeaf,r=e.name;return n?a.createElement("span",{className:"aui-icon aui-icon-small aui-iconfont-page"},r):t?a.createElement("img",{src:ht,alt:"Collapse",width:20,height:20}):a.createElement("img",{src:mt,alt:"Expand",width:20,height:20})},vt=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},e(t,n)};return function(t,n){if("function"!=typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),gt=function(){return gt=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},gt.apply(this,arguments)};const bt=function(e){function t(t){var n=e.call(this,t)||this;return n.value="",n.state={param:t.param,spacePickerProps:{param:t.param.spacePickerParam,onChange:function(e){if(null===e)t.param.treeParam.options={},n.setState({selectedSpace:[]}),t.onChange();else{e="space_"+e,n.value=e;var r=t.param.treeParam.options,a=r.hideCheckbox,o=r.selectMode,i=void 0===r.hideChildren||r.hideChildren;t.param.treeParam.options={path:"rest/scriptrunner-confluence/latest/spacetree?nodeId=".concat(e,"&hideCheckbox=").concat(a,"&lazy=true&spaceKeys=").concat(AJS.params.spaceKey),lazyLoad:!0,isFancyTreeApi:!0,selectMode:o,hideChildren:i},n.setState({selectedSpace:e,errorMessage:null,treeProps:{param:t.param.treeParam,onChange:t.onChange,update:e}}),t.onChange()}}},treeProps:{},selectedSpace:[],onChange:t.onChange,errorMessage:t.errorMessage},n}return vt(t,e),t.prototype.componentWillReceiveProps=function(e){this.setState({errorMessage:e.errorMessage})},t.prototype.componentDidUpdate=function(e){var t,n;if((null===(t=this.props.param)||void 0===t?void 0:t.value)!==(null===(n=e.param)||void 0===n?void 0:n.value)){var r=this.state.treeProps;r.param.value=this.props.param.value,this.setState({treeProps:r})}},t.prototype.render=function(){var e=this.state.spacePickerProps,t=this.state.treeProps,n=this.state&&this.state.selectedSpace&&this.state.selectedSpace.length>0;return a.createElement(a.Fragment,null,a.createElement(Ce,gt({},e)),n&&a.createElement(pt,{param:t.param,onChange:t.onChange,iconRenderer:yt}),n&&this.state.errorMessage&&a.createElement("div",{className:"error",style:{color:"#de350b",display:"block",margin:"0px 0px 5px 145px"}},this.state.errorMessage),!n&&a.createElement(i.Z,gt({},this.props,{param:t})))},t}(a.Component);var wt=n(74785),Et=function(){return Et=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},Et.apply(this,arguments)};const xt=function(e){if(e.param.hidden)return null;var t=e.param;return a.createElement(i.Z,Et({},e,{param:t,key:t.name}),a.createElement(wt.Z,Et({},e.param,{editorId:t.name,onChange:e.onChange,compileContextDescriptor:t.compileContextDescriptor,initialValue:t.value,description:t.description,expandedRight:!1,readonly:t.disabled})))};var Ct=n(10203),St=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},e(t,n)};return function(t,n){if("function"!=typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),Ot=function(){return Ot=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},Ot.apply(this,arguments)};const Pt=function(e){function t(t){var n=e.call(this,t)||this;return n.translate=function(e){return n.props.translations[e]},n.parseDuration=function(e){var t,r,a=parseInt(e,10),o=a%60,i=(a=Math.floor(a/60))%60;return a=Math.floor(a/60),n.props.showDays?(t=a%24,r=Math.floor(a/24)):(t=a,r=0),{days:r,hours:t,minutes:i,seconds:o}},n.calculateDuration=function(e,t){var r,a=Ot(Ot({},n.state),((r={})[e]=t,r));n.setState(a,(function(){var e=24*n.state.days*60*60+60*n.state.hours*60+60*n.state.minutes+n.state.seconds;n.props.onChange(e)}))},n.state=n.parseDuration(t.defaultValue||0),n}return St(t,e),t.prototype.buildDisplayBlock=function(e,t,n){var r=this;return a.createElement("div",{className:"bdp-block",style:{display:"inline-block",marginRight:10}},a.createElement("input",{style:{width:60},onChange:function(t){r.calculateDuration(e,t.target.value)},className:"text form-control input-sm",type:"number",min:"0",defaultValue:n,max:t}),a.createElement("div",{style:{textAlign:"center"}},this.translate(e)))},t.prototype.render=function(){var e=this.parseDuration(this.props.defaultValue||0),t=e.days,n=e.hours,r=e.minutes,o=e.seconds;return a.createElement("div",{className:"bdp-input"},this.props.showDays&&this.buildDisplayBlock("days",31,t),this.buildDisplayBlock("hours",this.props.showDays?23:null,n),this.buildDisplayBlock("minutes",59,r),this.props.showSeconds&&this.buildDisplayBlock("seconds",59,o))},t.defaultProps={translations:{day:"day",hour:"hour",minute:"minute",second:"second",days:"days",hours:"hours",minutes:"minutes",seconds:"seconds"},showSeconds:!1,showDays:!0},t}(a.Component);var jt=function(){return jt=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},jt.apply(this,arguments)};const kt=function(e){var t=e.param;return a.createElement(i.Z,jt({},e,{param:t}),a.createElement(Pt,{showDays:!0,onChange:e.onChange,defaultValue:t.value}))};var Tt=n(72142),At=n(77937);const Dt=(0,Tt.$j)((function(e){return{param:e.editParams.filter((function(e){return"replaceText"===e.type}))[0]}}))((function(e){var t=e.param,n=e.dispatch;return a.createElement(i.Z,{param:t},a.createElement(o.Z,{isCompact:!0,width:p.I,"data-cy":t.name,name:t.name,id:t.name,type:"text",onChange:function(e){return n((0,At.bB)({key:"FIELD_FIND",value:e.currentTarget.value}))},"data-schema":t.schema||"",placeholder:"find in title...",isDisabled:t.disabled}),a.createElement(o.Z,{isCompact:!0,width:p.I,"data-cy":t.name,name:t.name,id:t.name,type:"text",onChange:function(e){return n((0,At.bB)({key:"FIELD_REPLACE",value:e.currentTarget.value}))},"data-schema":t.schema||"",placeholder:"replace with...",isDisabled:t.disabled}))}));n(67213),n(46086);var Mt=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},e(t,n)};return function(t,n){if("function"!=typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),It=function(){return It=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},It.apply(this,arguments)};const Nt=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return Mt(t,e),t.prototype.componentDidMount=function(){var e=this,t=this.props.param,r=t.options;r.startval=t.value,n(65325);var a=new JSONEditor(this.el,It(It({},r),{theme:"aui",iconlib:"aui"})),o=$(this.el);o.find("[data-schemaid='root']").find("div").first().remove(),a.on("change",(function(){return e.props.onChange(a.getValue())})),o.livequery((function(){$("th:contains('Parameter Type')").remove(),$("div[data-schematype='object']").css("width","355px"),$(".remove-me").find("div.field-group").css("padding-left","105px"),$(".json-editor-btn-add").on("click",(function(){$(".remove-me").find("div.field-group").css("padding-left","105px"),$("div[data-schematype='object']").css("width","355px"),$("select.select").on("change",(function(e){$("div[data-schematype='object']").css("width","355px"),$(".remove-me").find("div.field-group").css("padding-left","105px"),"enum"===$(e.currentTarget).val()&&$("button:contains('Enum value')").parent().css("width","84%").css("padding","10px 104px 0px"),$("button:contains('Enum value')").on("click",(function(e){$("button:contains('Enum value')").parent().css("width","84%").css("padding","10px 104px 0px"),$(e.currentTarget).parent().siblings().find("div.aui-item").css("padding-left","14px")}))}))}))}))},t.prototype.render=function(){var e=this,t=this.props.param;return a.createElement(i.Z,It({},this.props,{param:t}),a.createElement(o.Z,{isCompact:!0,width:p.Q,style:{display:"none"},"data-cy":t.name,name:t.name,id:t.name,type:"text",onChange:function(t){return e.props.onChange(t.currentTarget.value)},value:t.value||"",isDisabled:t.disabled}),a.createElement("div",{id:"".concat(t.name,"-editor"),ref:function(t){return e.el=t}}))},t}(a.Component);var Lt=n(53997),Zt=function(){return Zt=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},Zt.apply(this,arguments)};const _t=function(e){var t=e.scope,n=e.eventCategory,r=void 0===n?"all":n;return a.createElement(Lt.Z,Zt({},e,function(e,t){return{loadAllUrl:function(){return"".concat(AJS.contextPath(),"/rest/scriptrunner/latest/events?").concat((0,L.F)({scope:e,eventCategory:t}))},invalidItemSelected:function(e){return"Failed to find event with class name: ".concat(e)},mapResultsToOption:function(e){return e.map((function(e){return{label:e[0],options:e[1].map((function(e){return{value:e[0],label:e[1]}}))}}))},placeholder:"Select event(s)"}}(t,r)))};var Ft=function(){return Ft=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},Ft.apply(this,arguments)},Rt=n(23400),zt=n(149),Ut=n.n(zt),Bt=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},e(t,n)};return function(t,n){if("function"!=typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),Jt=function(){return Jt=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},Jt.apply(this,arguments)},qt=function(e,t,n,r){return new(n||(n=Promise))((function(a,o){function i(e){try{c(r.next(e))}catch(e){o(e)}}function l(e){try{c(r.throw(e))}catch(e){o(e)}}function c(e){var t;e.done?a(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(i,l)}c((r=r.apply(e,t||[])).next())}))},Vt=function(e,t){var n,r,a,o,i={label:0,sent:function(){if(1&a[0])throw a[1];return a[1]},trys:[],ops:[]};return o={next:l(0),throw:l(1),return:l(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function l(o){return function(l){return function(o){if(n)throw new TypeError("Generator is already executing.");for(;i;)try{if(n=1,r&&(a=2&o[0]?r.return:o[0]?r.throw||((a=r.return)&&a.call(r),0):r.next)&&!(a=a.call(r,o[1])).done)return a;switch(r=0,a&&(o=[2&o[0],a.value]),o[0]){case 0:case 1:a=o;break;case 4:return i.label++,{value:o[1],done:!1};case 5:i.label++,r=o[1],o=[0];continue;case 7:o=i.ops.pop(),i.trys.pop();continue;default:if(!(a=i.trys,(a=a.length>0&&a[a.length-1])||6!==o[0]&&2!==o[0])){i=0;continue}if(3===o[0]&&(!a||o[1]>a[0]&&o[1]<a[3])){i.label=o[1];break}if(6===o[0]&&i.label<a[1]){i.label=a[1],a=o;break}if(a&&i.label<a[2]){i.label=a[2],i.ops.push(o);break}a[2]&&i.ops.pop(),i.trys.pop();continue}o=t.call(e,i)}catch(e){o=[6,e],r=0}finally{n=a=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,l])}}};const Wt=function(e){function t(t){var n=e.call(this,t)||this;return n.fetchCqlResults=Ut()((function(e){return qt(n,void 0,void 0,(function(){var t,n,r;return Vt(this,(function(a){switch(a.label){case 0:return t=encodeURI(e),n="".concat(AJS.contextPath(),"/rest/scriptrunner-confluence/latest/cql/searchCount?cql=").concat(t,"&excerpt=none"),[4,(0,Be.wrappedFetch)(n)];case 1:return(r=a.sent()).result?(this.setState({cql:e,numberOfResults:r.result.size,errorMessage:""}),[2]):(r&&this.setState({cql:null,errorMessage:"Invalid CQL"}),[2])}}))}))}),1e3),n.getCqlSearchResults=function(e){var t=e.target.value;t?n.fetchCqlResults(t):n.setState({cql:null})},n.state={numberOfResults:0,cql:null,errorMessage:""},n}return Bt(t,e),t.prototype.render=function(){var e=this,t=this.props.param,n=encodeURI(this.state.cql),r="".concat(AJS.contextPath(),"/plugins/servlet/scriptrunner/enhancedsearch/?cqlQuery=").concat(n);return a.createElement(i.Z,Jt({},this.props,{param:t}),a.createElement(o.Z,{isCompact:!0,width:p.Q,onChange:function(t){e.props.onChange(t.currentTarget.value),e.getCqlSearchResults(t)},type:"text","data-schema":t.schema||"",name:t.name||"",id:t.name,value:t.value||"","data-cy":t.name,isDisabled:t.disabled}),this.state.cql&&a.createElement("span",{className:"cql-info",style:{paddingLeft:"10px"}},a.createElement("a",{target:"_blank",href:r},this.state.numberOfResults," hit(s)")),t.value&&a.createElement("span",{className:"cql-info",style:{paddingLeft:"10px",color:"#d04437"}},this.state.errorMessage),a.createElement(s,{examples:t.examples,handleSelect:function(t){e.props.onChange(t),e.fetchCqlResults(t)},isCodeEditor:!1}))},t}(a.Component);var Gt,Qt=function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,a,o=n.call(e),i=[];try{for(;(void 0===t||t-- >0)&&!(r=o.next()).done;)i.push(r.value)}catch(e){a={error:e}}finally{try{r&&!r.done&&(n=o.return)&&n.call(o)}finally{if(a)throw a.error}}return i};!function(e){e[e.all=0]="all",e[e.afterDate=1]="afterDate"}(Gt||(Gt={}));const Kt=function(e){var t=a.useRef(),n=Qt(a.useState(function(e){return e.param&&e.param.value&&![null,void 0].includes(e.param.value.value)?e.param.value.value>0?Gt.afterDate:Gt.all:null}(e)),2),r=n[0],o=n[1],i=Qt(a.useState(e.param?e.param.value:null),2),l=i[0],c=i[1],u=r===Gt.afterDate;return a.useEffect((function(){u&&t.current&&t.current.focus()}),[r]),a.useEffect((function(){r!==Gt.all?r===Gt.afterDate&&l&&e.onChange(l):e.onChange({value:-1,unit:"day"})}),[r,l]),a.createElement("div",{style:{display:"flex",flexDirection:"column"}},a.createElement("div",{className:"radio"},a.createElement("input",{type:"radio",className:"radio",name:"date-diff-picker",id:"date-diff-radio-all",checked:r===Gt.all,onChange:function(){return o(Gt.all)}}),a.createElement("label",{htmlFor:"date-radio-all"},"All")),a.createElement("div",{className:"radio"},a.createElement("input",{type:"radio",className:"radio",name:"date-diff-picker",id:"date-diff-radio-older",checked:r===Gt.afterDate,onChange:function(){return o(Gt.afterDate)}}),a.createElement("label",{htmlFor:"date-radio-older"},"Older than"),a.createElement($t,{value:l,enabled:r===Gt.afterDate,onChange:c})))};var Yt={day:"Days",week:"Weeks",month:"Months",year:"Years"},$t=function(e){var t=e.value?e.value.value:-1,n=e.value?e.value.unit:"day";return a.createElement(a.Fragment,null,a.createElement("span",{style:e.style},a.createElement("input",{type:"text",style:{textAlign:"right"},className:"text short-field",value:t>0?"".concat(t):"",disabled:!e.enabled,onChange:function(t){if(t.target.value&&0!==t.target.value.length){var r=Number.parseFloat(t.target.value);r&&Number.isFinite(r)&&e.onChange({value:r,unit:n})}else e.onChange({value:0,unit:n})}})),a.createElement("select",{value:n,disabled:!e.enabled,className:"select short-field",onChange:function(n){return e.onChange({value:t,unit:n.target.value})}},Object.entries(Yt).map((function(e){var t=Qt(e,2),n=t[0],r=t[1];return a.createElement("option",{value:n,key:n},r)}))))};$t.defaultProps={style:{marginLeft:"10px",marginRight:"5px"}};var Ht=function(){return Ht=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},Ht.apply(this,arguments)};const Xt=function(e){return a.createElement(i.Z,Ht({},e),a.createElement(Kt,{param:e.param,onChange:e.onChange}))};var en=n(89492),tn=function(){return tn=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},tn.apply(this,arguments)};const nn=function(e){var t;return a.createElement(i.Z,tn({},e),a.createElement(en.Z,{value:e.param.value,handleChange:e.onChange,type:null!==(t=e.param.plurality)&&void 0!==t?t:"single",isDisabled:e.param.disabled}))};var rn=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},e(t,n)};return function(t,n){if("function"!=typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),an=function(){return an=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},an.apply(this,arguments)},on=function(e,t,n,r){return new(n||(n=Promise))((function(a,o){function i(e){try{c(r.next(e))}catch(e){o(e)}}function l(e){try{c(r.throw(e))}catch(e){o(e)}}function c(e){var t;e.done?a(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(i,l)}c((r=r.apply(e,t||[])).next())}))},ln=function(e,t){var n,r,a,o,i={label:0,sent:function(){if(1&a[0])throw a[1];return a[1]},trys:[],ops:[]};return o={next:l(0),throw:l(1),return:l(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function l(o){return function(l){return function(o){if(n)throw new TypeError("Generator is already executing.");for(;i;)try{if(n=1,r&&(a=2&o[0]?r.return:o[0]?r.throw||((a=r.return)&&a.call(r),0):r.next)&&!(a=a.call(r,o[1])).done)return a;switch(r=0,a&&(o=[2&o[0],a.value]),o[0]){case 0:case 1:a=o;break;case 4:return i.label++,{value:o[1],done:!1};case 5:i.label++,r=o[1],o=[0];continue;case 7:o=i.ops.pop(),i.trys.pop();continue;default:if(!(a=i.trys,(a=a.length>0&&a[a.length-1])||6!==o[0]&&2!==o[0])){i=0;continue}if(3===o[0]&&(!a||o[1]>a[0]&&o[1]<a[3])){i.label=o[1];break}if(6===o[0]&&i.label<a[1]){i.label=a[1],a=o;break}if(a&&i.label<a[2]){i.label=a[2],i.ops.push(o);break}a[2]&&i.ops.pop(),i.trys.pop();continue}o=t.call(e,i)}catch(e){o=[6,e],r=0}finally{n=a=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,l])}}};const cn=function(e){function t(t){var n=e.call(this,t)||this;return n.fetchCronDescription=Ut()((function(e){return on(n,void 0,void 0,(function(){var t,n;return ln(this,(function(r){switch(r.label){case 0:return t="".concat(AJS.contextPath(),"/rest/scriptrunner/latest/scheduled-jobs/cronDescription?").concat((0,L.F)({cronExpression:e})),[4,(0,Be.wrappedFetch)(t)];case 1:return n=r.sent(),this.setState({cronDescription:n.error?"":n.result}),[2]}}))}))}),100),n.updateCronDescription=function(e){n.setState({cronDescription:null}),e&&n.fetchCronDescription(e)},n.state={cronDescription:""},n}return rn(t,e),t.prototype.componentDidMount=function(){this.updateCronDescription(this.props.param.value)},t.prototype.render=function(){var e=this,t=this.props.param;return a.createElement(a.Fragment,null,a.createElement(i.Z,an({},this.props,{param:t}),a.createElement("div",{style:{display:"flex",alignItems:"center"}},a.createElement(o.Z,{isCompact:!0,width:p.Q,onChange:function(t){e.props.onChange(t.currentTarget.value),e.updateCronDescription(t.currentTarget.value)},type:"text","data-schema":t.schema||"",name:t.name||"",id:t.name,value:t.value||"","data-cy":t.name,isDisabled:t.disabled}),this.state.cronDescription&&a.createElement("span",{className:"cron-description",style:{paddingLeft:"10px"}},this.state.cronDescription))),a.createElement(s,{examples:t.examples,handleSelect:function(t){e.props.onChange(t),e.fetchCronDescription(t)},isCodeEditor:!1,isIntervalOrCronExpression:!0,style:{marginTop:"-8px",marginBottom:"10px"}}))},t}(a.Component);var un=function(){return un=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},un.apply(this,arguments)},sn=function(e){return a.createElement(Lt.Z,un({},function(e){return{invalidItemSelected:function(e){return"Failed to find project with key: ".concat(e)},mapResultsToOption:function(t){return t.filter((function(t){return!e.projectTypeKey||t.projectTypeKey===e.projectTypeKey})).map((function(t){return{value:e.returnsProjectKey?t.key:t.id,key:t.key,label:t.name,icon:t.avatarUrls["16x16"]}}))},placeholder:"Select project(s)",hasIdentifier:function(e,t){return isNaN(Number(t))?t===e.key:t===e.value}}}(e),e,{loadAllUrl:function(){return"".concat(AJS.contextPath(),"/rest/api/2/project?").concat((0,L.F)({includeArchived:e.includeArchived}))}}))},pn=function(){return pn=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},pn.apply(this,arguments)};const dn=function(e){var t;return a.createElement(i.Z,pn({},e),a.createElement(sn,pn({},e.param,{key:"".concat(e.param.name,":").concat(e.param.includeArchived?1:0),value:e.param.value,type:null!==(t=e.param.plurality)&&void 0!==t?t:"single",handleChange:e.onChange})))};var fn=function(){return fn=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},fn.apply(this,arguments)},hn={fetchSingleItemByKey:function(e){return"".concat(AJS.contextPath(),"/rest/api/1.0/projects/").concat(e)},searchUrl:function(e){return"".concat(AJS.contextPath(),"/rest/api/1.0/projects?").concat((0,L.F)({name:e}))},invalidItemSelected:function(e){return"Failed to find project with ID: ".concat(e)},mapItemToOption:function(e){return{value:e.key,label:e.name}},mapSearchResultToSuggestions:function(e){return e.values.map((function(e){return{value:e.key,label:e.name}}))},placeholder:"Select project",itemNamePlural:"projects"};const mn=function(e){return a.createElement(N.Z,fn({},e,hn))};var yn=function(){return yn=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},yn.apply(this,arguments)};const vn=function(e){var t=e.param,n={type:"multiple"===t.plurality?"multiple":"single",handleChange:e.onChange,value:t.value};return a.createElement(i.Z,yn({},e,{param:t}),a.createElement("div",{style:{maxWidth:500}},a.createElement(mn,yn({},n,{isDisabled:t.disabled}))))};var gn=n(72236),bn=n(86632),wn=function(e,t){return Object.defineProperty?Object.defineProperty(e,"raw",{value:t}):e.raw=t,e},En=function(){return En=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},En.apply(this,arguments)},xn=Ae.Z.div(Pn||(Pn=wn(["\n    flex: 50%;\n    padding-right: 25px;\n"],["\n    flex: 50%;\n    padding-right: 25px;\n"]))),Cn=Ae.Z.div(jn||(jn=wn(["\n    flex: 50%;\n"],["\n    flex: 50%;\n"]))),Sn=function(e){return a.createElement(i.Z,En({},e,{param:e.param}),a.createElement("div",{style:{maxWidth:500,display:"flex"}},a.createElement(xn,null," ",a.createElement(bn.Z,{onChange:function(t){return e.onChange({from:t,to:e.value.to})}})," "),a.createElement(Cn,null," ",a.createElement(bn.Z,{onChange:function(t){return e.onChange({from:e.value.from,to:t})}})," ")))};const On=function(e){return a.createElement(Sn,{value:e.dateRange,param:{type:"dateRangePicker",name:"",label:e.label,description:e.description},onChange:function(t){e.onChange(t)}})};var Pn,jn,kn=function(){return kn=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},kn.apply(this,arguments)},Tn=function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,a,o=n.call(e),i=[];try{for(;(void 0===t||t-- >0)&&!(r=o.next()).done;)i.push(r.value)}catch(e){a={error:e}}finally{try{r&&!r.done&&(n=o.return)&&n.call(o)}finally{if(a)throw a.error}}return i},An={menu:function(e){return kn(kn({},e),{position:"relative"})}},Dn={label:"PRESET FILTERS",options:[{value:"OLDER_THAN_6_MONTHS",label:"Older than 6 months"},{value:"OLDER_THAN_1_YEAR",label:"Older than 1 year"},{value:"OLDER_THAN_2_YEARS",label:"Older than 2 years"},{value:"LAST_MONTH",label:"Created within the last month"},{value:"ALL",label:"All"}]},Mn={label:"CUSTOM FILTERS",options:[{value:"WITHIN_LAST",label:"Created within the last"},{value:"OLDER_THAN",label:"Older than"},{value:"BETWEEN_RANGE",label:"Created between / range"}]},In=function(e){return a.createElement(i.Z,{param:{description:"",name:"",label:e.label,type:"dateDiff"}},a.createElement("div",{className:"textFieldFieldGroup"},a.createElement($t,{value:e.timeLength,enabled:!0,onChange:function(t){e.onChange(t)},style:{marginRight:"5px"}})))},Nn=function(e){return a.createElement("div",{style:{maxWidth:500}},a.createElement(gn.Z,{classNamePrefix:"sr-rs",options:e.options,onChange:e.onChange,value:e.ageRange,styles:An}))},Ln=function(e){var t=e.configuration,n=Tn(a.useState(t.timeLength),2),r=n[0],o=n[1],i=Tn(a.useState(t.dateRange),2),l=i[0],c=i[1],u=t.agePickerRange;if(a.useEffect((function(){e.setAgePickerConfiguration({agePickerRange:u,dateRange:l,timeLength:r})}),[r,l]),u){if("WITHIN_LAST"===u.value||"OLDER_THAN"===u.value)return a.createElement("div",null,a.createElement(In,{timeLength:r,onChange:o,label:u.label}));if("BETWEEN_RANGE"===u.value)return a.createElement("div",null,a.createElement(On,{dateRange:l,description:e.dateRangeDescription,label:u.label,onChange:c}))}return null},Zn=function(e){return e&&e.length>0?[Dn,Mn].map((function(t){return{label:t.label,options:t.options.filter((function(t){return e.find((function(e){return e===t.value}))}))}})):[Dn,Mn]};const _n=function(e){var t=e.param.value||{agePickerRange:{value:null,label:""},dateRange:{from:null,to:null},timeLength:{unit:"day",value:null}},n=Tn(a.useState(t),2),r=n[0],o=n[1],l=function(t){t!==r&&e.onChange(kn({},t)),o(t)};return a.createElement("div",null,a.createElement(i.Z,kn({},e),a.createElement(Nn,{options:Zn(e.param.options),ageRange:r.agePickerRange,onChange:function(e){l(kn(kn({},r),{agePickerRange:e}))},label:e.param.label})),a.createElement(Ln,{setAgePickerConfiguration:function(e){return l(e)},dateRangeDescription:e.param.dateRangeDescription,configuration:r}))};var Fn=function(e){return Object.values(e.values.reduce((function(e,t){var n,r=t.project.name,a=[{label:t.name,value:String(t.id)}];return null!==(n=e[r])&&void 0!==n||(e[r]={label:r,options:[]}),e[r].options=e[r].options.concat(a),e}),{}))},Rn=function(e){var t=e.permission,n=e.handleChange,r=e.value,o=e.isDisabled,i=e.plurality;return a.createElement(N.Z,{minCharacters:0,fetchSingleItemByKey:function(e){return"".concat(AJS.contextPath(),"/rest/scriptrunner-bitbucket/latest/repository/").concat(e)},searchUrl:function(e){return"".concat(AJS.contextPath(),"/rest/api/1.0/repos?").concat((0,L.F)({name:e,permission:t}))},invalidItemSelected:function(e){return"Failed to find repository with ID: ".concat(e)},mapItemToOption:function(e){return{value:String(e.id),label:e.projectAndSlug}},mapSearchResultToSuggestions:Fn,getTotalSearchResults:function(e){return e.size},value:r,placeholder:"Select ".concat("single"===i?"repository":"repositories"),itemNamePlural:"repositories",handleChange:n,isDisabled:o,type:i})};const zn=function(e){return a.createElement(Lt.Z,{value:e.value,handleChange:e.handleChange,invalidItemSelected:function(e){return"Failed to find priority with key: ".concat(e)},loadAllUrl:function(){return"".concat(AJS.contextPath(),"/rest/api/2/priority")},mapResultsToOption:function(e){return e.map((function(e){return{value:e.id,label:e.name,icon:e.iconUrl}}))},type:e.plurality,placeholder:e.placeholder})};var Un=function(){return Un=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},Un.apply(this,arguments)};const Bn=function(e){var t;return a.createElement(i.Z,Un({},e),a.createElement(zn,{value:e.param.value,handleChange:e.onChange,placeholder:e.param.placeholder,plurality:null!==(t=e.param.plurality)&&void 0!==t?t:"single"}))};const Jn=function(e){var t;return a.createElement(Lt.Z,{value:e.value,handleChange:e.handleChange,invalidItemSelected:function(e){return"Failed to find issue type with ID: ".concat(e)},loadAllUrl:function(){return"".concat(AJS.contextPath(),"/rest/api/2/issuetype")},mapResultsToOption:function(t){return t.filter((function(t){return!e.standardIssueTypesOnly||!t.subtask})).filter((function(t){return!e.subTaskIssueTypesOnly||t.subtask})).map((function(e){return{value:e.id,label:e.name,icon:e.iconUrl}}))},type:e.plurality,placeholder:null!==(t=e.placeholder)&&void 0!==t?t:"Select issue type(s)"})};var qn=function(){return qn=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},qn.apply(this,arguments)};const Vn=function(e){var t;return a.createElement(i.Z,qn({},e),a.createElement(Jn,qn({},e.param,{value:e.param.value,handleChange:e.onChange,placeholder:e.param.placeholder,plurality:null!==(t=e.param.plurality)&&void 0!==t?t:"single"})))};const Wn=function(e){return a.createElement(Lt.Z,{value:e.value,handleChange:e.handleChange,invalidItemSelected:function(e){return"Failed to find issue link type with key: ".concat(e)},loadAllUrl:function(){return"".concat(AJS.contextPath(),"/rest/api/2/issueLinkType")},mapResultsToOption:function(e){return e.issueLinkTypes.map((function(e){return{value:e.id,label:"".concat(e.name," (").concat(e.inward,"/").concat(e.outward,")")}}))},type:e.plurality,placeholder:e.placeholder})};var Gn=n(63363),Qn=function(){return Qn=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},Qn.apply(this,arguments)},Kn=function(e){return a.createElement(N.Z,{value:e.value,handleChange:e.handleChange,invalidItemSelected:function(e){return"Failed to find saved filter with id: ".concat(e)},searchUrl:function(e){return"".concat(AJS.contextPath(),"/rest/scriptrunner-jira/latest/savedFilter?").concat((0,L.F)({filterNamePart:e}))},mapItemToOption:function(e){return{value:e.id,label:"".concat(e.name," (author: ").concat(e.owner.name,")")}},type:e.plurality,placeholder:e.placeholder,fetchSingleItemByKey:function(e){return"".concat(AJS.contextPath(),"/rest/api/2/filter/").concat(e)},mapSearchResultToSuggestions:function(e){return e.results.map((function(e){return{value:e.id,label:"".concat(e.name," (author: ").concat(e.owner,")")}}))},getTotalSearchResults:function(e){return e.totalResultCount},minCharacters:0})},Yn=function(e){return a.createElement(Lt.Z,{value:e.value,handleChange:e.handleChange,invalidItemSelected:function(e){return"Failed to find status with key: ".concat(e)},loadAllUrl:function(){return"".concat(AJS.contextPath(),"/rest/api/2/status")},mapResultsToOption:function(e){return e.map((function(e){return{value:e.id,label:e.name}}))},type:e.plurality,placeholder:e.placeholder})},$n=n(25580),Hn=n(51746),Xn=n(864),er=n(72073),tr=n(26052),nr=function(e){return a.createElement($n.Z,{value:e.value,handleChange:e.handleChange,loadAllUrl:function(){return"".concat(AJS.contextPath(),"/rest/api/2/customFields")},invalidItemSelected:function(e){return"Failed to find custom field with id: ".concat(e)},mapResultsToOption:function(e){var t=Hn.Z(Xn.Z(er.Z,tr.Z("name")))(e);return e?e.map((function(e){return{value:e.id,label:t[e.name.trim()].length>1?"".concat(e.name.trim()," (").concat(e.id,")"):e.name.trim()}})):[]},placeholder:e.placeholder,startParam:"startAt",limitParam:"maxResults",type:e.plurality,incrementPageNumber:function(e,t){return e+1},startIterationFrom:1,convertResultToPagedData:function(e){return{start:e.startAt,limit:e.maxResults,isLastPage:e.isLast,values:e.values}}})},rr=function(){return rr=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},rr.apply(this,arguments)},ar=function(e){return a.createElement(Lt.Z,rr({},e,{value:e.value,handleChange:e.handleChange,invalidItemSelected:function(e){return"Failed to find resolution with key: ".concat(e)},loadAllUrl:function(){return"".concat(AJS.contextPath(),"/rest/api/2/resolution")},mapResultsToOption:function(e){return e.map((function(e){return{value:e.id,label:e.name}}))},placeholder:e.placeholder}))},or=function(){return or=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},or.apply(this,arguments)},ir=function(e){return a.createElement(Lt.Z,{key:"".concat(e.value,":").concat(e.projectId),value:e.value,isLoadingSelectedValue:e.isLoadingSelectedValue,error:e.error,isDisabled:!e.projectId,handleChange:e.handleChange,invalidItemSelected:function(e){return"Failed to find version with id: ".concat(e)},loadAllUrl:function(){return e.projectId?"".concat(AJS.contextPath(),"/rest/api/2/project/").concat(e.projectId,"/versions"):null},mapResultsToOption:function(e){return e.map((function(e){return{value:e.id.toString(),label:e.name}}))},type:e.plurality,placeholder:e.placeholder})},lr=function(){return lr=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},lr.apply(this,arguments)},cr=function(e,t,n,r){return new(n||(n=Promise))((function(a,o){function i(e){try{c(r.next(e))}catch(e){o(e)}}function l(e){try{c(r.throw(e))}catch(e){o(e)}}function c(e){var t;e.done?a(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(i,l)}c((r=r.apply(e,t||[])).next())}))},ur=function(e,t){var n,r,a,o,i={label:0,sent:function(){if(1&a[0])throw a[1];return a[1]},trys:[],ops:[]};return o={next:l(0),throw:l(1),return:l(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function l(o){return function(l){return function(o){if(n)throw new TypeError("Generator is already executing.");for(;i;)try{if(n=1,r&&(a=2&o[0]?r.return:o[0]?r.throw||((a=r.return)&&a.call(r),0):r.next)&&!(a=a.call(r,o[1])).done)return a;switch(r=0,a&&(o=[2&o[0],a.value]),o[0]){case 0:case 1:a=o;break;case 4:return i.label++,{value:o[1],done:!1};case 5:i.label++,r=o[1],o=[0];continue;case 7:o=i.ops.pop(),i.trys.pop();continue;default:if(!(a=i.trys,(a=a.length>0&&a[a.length-1])||6!==o[0]&&2!==o[0])){i=0;continue}if(3===o[0]&&(!a||o[1]>a[0]&&o[1]<a[3])){i.label=o[1];break}if(6===o[0]&&i.label<a[1]){i.label=a[1],a=o;break}if(a&&i.label<a[2]){i.label=a[2],i.ops.push(o);break}a[2]&&i.ops.pop(),i.trys.pop();continue}o=t.call(e,i)}catch(e){o=[6,e],r=0}finally{n=a=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,l])}}},sr=function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,a,o=n.call(e),i=[];try{for(;(void 0===t||t-- >0)&&!(r=o.next()).done;)i.push(r.value)}catch(e){a={error:e}}finally{try{r&&!r.done&&(n=o.return)&&n.call(o)}finally{if(a)throw a.error}}return i},pr=function(e){var t=sr(a.useState(),2),n=t[0],r=t[1],o=sr(a.useState(),2),l=o[0],c=o[1];return a.useEffect((function(){cr(void 0,void 0,void 0,(function(){var t,n,a,o,i;return ur(this,(function(l){switch(l.label){case 0:return t=null!==(i=e.param.value)&&void 0!==i?i:"",(n=Array.isArray(t)?t[0]:t)?(a=e.childUrl(n),[4,(0,Be.wrappedFetch)(a)]):[3,2];case 1:(o=l.sent()).error?404===o.response.status?(e.onChange(null),c("Failed to fetch selected item with id: ".concat(e.param.value))):(console.error("error fetching url : ".concat(a," ").concat(o.error)),c(o.error.toString())):(c(null),r(o.result.projectId.toString())),l.label=2;case 2:return[2]}}))}))}),[n,e.param.value]),a.createElement(i.Z,lr({},e),a.createElement(sn,{value:n,isLoadingSelectedValue:!n&&null!=e.param.value,handleChange:function(t){n!==t&&(c(null),r(t),e.onChange(null))},placeholder:e.param.projectPlaceholder,type:"single"}),a.createElement("div",{style:{height:10}}),e.children(n,l))},dr=function(e){return a.createElement(Lt.Z,{key:"".concat(e.value,":").concat(e.projectId),value:e.value,isLoadingSelectedValue:e.isLoadingSelectedValue,error:e.error,isDisabled:!e.projectId,handleChange:e.handleChange,invalidItemSelected:function(e){return"Failed to find component with id: ".concat(e)},loadAllUrl:function(){return e.projectId?"".concat(AJS.contextPath(),"/rest/api/2/project/").concat(e.projectId,"/components"):null},mapResultsToOption:function(e){return e.map((function(e){return{value:e.id.toString(),label:e.name}}))},type:e.plurality,placeholder:e.placeholder})},fr=n(7664),hr=function(){return hr=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},hr.apply(this,arguments)};const mr=function(e){var t=e.param,n=e.param.values;return a.createElement(i.Z,hr({},e),a.createElement("div",{style:{maxWidth:500}},a.createElement(fr.ZP,{classNamePrefix:"sr-rs",options:n,onChange:function(t){return e.onChange(null==t?void 0:t.value)},value:n.find((function(e){return e.value===t.value})),formatOptionLabel:function(e){var t=e.label,n=e.description;return a.createElement("div",null,a.createElement("span",null,t),a.createElement("div",{style:{color:"#6b778c",fontSize:"12px"}},n))},styles:{control:function(e){return hr(hr({},e),{height:60})}}})))};var yr,vr,gr,br,wr,Er,xr,Cr,Sr=n(27126),Or=function(e,t){var n=e.id,r=e.isLeaf,o=e.name;return["repo:-1","repo:-2","repo:-3"].includes(n)||!r?t?a.createElement("img",{src:ht,alt:"Collapse",width:20,height:20}):a.createElement("img",{src:mt,alt:"Expand",width:20,height:20}):a.createElement(Sr.Z,{label:o,primaryColor:"#505F79",size:"small"})},Pr=function(e,t){var n=e.isLeaf,r=e.name;return n?a.createElement("span",{className:"aui-icon aui-icon-small aui-iconfont-jira"},r):t?a.createElement("img",{src:ht,alt:"Collapse",width:20,height:20}):a.createElement("img",{src:mt,alt:"Expand",width:20,height:20})},jr=n(56598),kr=n(38469),Tr=function(){return Tr=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},Tr.apply(this,arguments)},Ar="".concat(AJS.contextPath(),"/images/icons/contenttypes/blog_post_16.png"),Dr="".concat(AJS.contextPath(),"/images/icons/contenttypes/page_16.png"),Mr=Tr(Tr({},ee._.BasePageOptions),{searchUrl:function(e){return"".concat(AJS.contextPath(),'/rest/api/content/search?cql=type in (page, blogpost) AND title~"').concat(e,'*"')},fetchSingleItemByKey:function(e){return"".concat(AJS.contextPath(),"/rest/api/content/").concat(e)},mapItemToOption:function(e){return{value:e.title,label:e.title,id:e.id,icon:"blogpost"===e.type?Ar:Dr}},mapSearchResultToSuggestions:function(e){return e.results.map((function(e){return{value:e.title,label:e.title,id:e.id,icon:"blogpost"===e.type?Ar:Dr}}))},getTotalSearchResults:function(e){return e.totalSize}}),Ir=function(e){return a.createElement(N.Z,Tr({},e,Mr))},Nr=function(){return Nr=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},Nr.apply(this,arguments)},Lr="".concat(AJS.contextPath(),"/images/icons/contenttypes/attachment_16.png"),Zr=Nr(Nr({},ee._.BaseAttachmentOptions),{searchUrl:function(e){return"".concat(AJS.contextPath(),'/rest/api/content/search?cql=type = attachment AND title~"').concat(e,'*"')},fetchSingleItemByKey:function(e){return"".concat(AJS.contextPath(),"/rest/api/content/").concat(e)},mapItemToOption:function(e){return{value:e.title,label:e.title,id:e.id,icon:Lr}},mapSearchResultToSuggestions:function(e){return e.results.map((function(e){return{value:e.title,label:e.title,id:e.id,icon:Lr}}))},getTotalSearchResults:function(e){return e.totalSize}}),_r=function(e){return a.createElement(N.Z,Nr({},e,Zr))},Fr=n(94522),Rr=n.n(Fr),zr=function(e,t){return Object.defineProperty?Object.defineProperty(e,"raw",{value:t}):e.raw=t,e},Ur=function(){return Ur=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},Ur.apply(this,arguments)},Br=function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,a,o=n.call(e),i=[];try{for(;(void 0===t||t-- >0)&&!(r=o.next()).done;)i.push(r.value)}catch(e){a={error:e}}finally{try{r&&!r.done&&(n=o.return)&&n.call(o)}finally{if(a)throw a.error}}return i},Jr=Ae.Z.div(yr||(yr=zr(["\n    width: 500px;\n    padding-bottom: 10px;\n"],["\n    width: 500px;\n    padding-bottom: 10px;\n"]))),qr=Ae.Z.div(vr||(vr=zr(["\n    width: 500px;\n"],["\n    width: 500px;\n"]))),Vr=Ae.Z.i(gr||(gr=zr(["\n    color: red;\n    background-color: white;\n"],["\n    color: red;\n    background-color: white;\n"]))),Wr=Ae.Z.span(br||(br=zr(["\n    display: inline-block;\n"],["\n    display: inline-block;\n"]))),Gr="boolean",Qr="username",Kr="enum",Yr="int",$r="spacekey",Hr="confluence-content",Xr="attachment",ea="group",ta=function(e,t){return e.find((function(e){return e.name===t.name})).userValue=t.userValue,e},na=function(e){var t=Br((0,a.useState)(!0),2),n=t[0],r=t[1];return(0,a.useEffect)((function(){return e.setMacroParameters(ta(e.macroParams,Ur(Ur({},e.macroParam),{userValue:n?"true":"false"})))}),[n]),a.createElement(jr.X,{label:e.macroParam.label,value:"".concat(n),name:"controlled-checkbox",isChecked:n,onChange:function(){return r(!n)}})},ra=function(e){return a.createElement(a.Fragment,null,a.createElement("label",{key:e.label},e.label),e.children)},aa=function(e){var t=Br((0,a.useState)([]),2),n=t[0],r=t[1];(0,a.useEffect)((function(){return e.setMacroParameters(n)}),[n]);var i;return a.createElement("div",null,null==(i=e.macroParams)?void 0:i.map((function(t){return a.createElement(Jr,{key:t.name,className:"paramContainer"},function(t){var n=t.type;return n===Gr?a.createElement(na,{macroParams:e.macroParams,macroParam:t,setMacroParameters:r}):n===Kr?a.createElement(ra,{label:t.label},a.createElement(gn.Z,{className:"enumSelectField",classNamePrefix:"sr-rs",options:t.selectOptions,isSearchable:!0,onChange:function(n){return r(ta(e.macroParams,Ur(Ur({},t),{userValue:n.value,selectOptions:null})))}})):n===$r?a.createElement(ra,{label:t.label},a.createElement(Ee.p,{type:t.multiple?"multiple":"single",handleChange:function(n){return r(ta(e.macroParams,Ur(Ur({},t),{userValue:n})))}})):n===ea?a.createElement(ra,{label:t.label},a.createElement(ve.Yj,{type:t.multiple?"multiple":"single",handleChange:function(n){return r(ta(e.macroParams,Ur(Ur({},t),{userValue:n})))}})):n===Qr?a.createElement(ra,{label:t.label},a.createElement(ce.U,{type:t.multiple?"multiple":"single",handleChange:function(n){return r(ta(e.macroParams,Ur(Ur({},t),{userValue:n})))}})):n===Hr?a.createElement(ra,{label:t.label},a.createElement(Ir,{type:t.multiple?"multiple":"single",handleChange:function(n){return r(ta(e.macroParams,Ur(Ur({},t),{userValue:n})))}})):n===Xr?a.createElement(ra,{label:t.label},a.createElement(_r,{type:t.multiple?"multiple":"single",handleChange:function(n){return r(ta(e.macroParams,Ur(Ur({},t),{userValue:n})))}})):a.createElement(ra,{label:t.label},a.createElement(o.Z,{onChange:function(n){return r(ta(e.macroParams,Ur(Ur({},t),{userValue:n.currentTarget.value})))},name:t.label,isRequired:t.required,type:n===Yr?"number":"text"}))}(t),a.createElement(kr.W3,{key:t.name},a.createElement(Wr,{className:"paramDescription"},Rr()(t.description))),t.required&&!t.userValue&&a.createElement(kr.W3,null,a.createElement(Vr,{className:"paramRequired"},"Field value is required")))})))},oa=function(){return oa=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},oa.apply(this,arguments)},ia=function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,a,o=n.call(e),i=[];try{for(;(void 0===t||t-- >0)&&!(r=o.next()).done;)i.push(r.value)}catch(e){a={error:e}}finally{try{r&&!r.done&&(n=o.return)&&n.call(o)}finally{if(a)throw a.error}}return i},la=n(90531),ca=n(28740),ua=function(){return ua=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},ua.apply(this,arguments)},sa=function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,a,o=n.call(e),i=[];try{for(;(void 0===t||t-- >0)&&!(r=o.next()).done;)i.push(r.value)}catch(e){a={error:e}}finally{try{r&&!r.done&&(n=o.return)&&n.call(o)}finally{if(a)throw a.error}}return i},pa=function(e){return e.length>0&&Array.isArray(e[0][1])},da=function(e){var t=sa(e,2);return{value:t[0],label:t[1]}},fa=function(e){var t=e.map((function(e){var t=sa(e,2),n=(t[0],t[1]);return null==n?void 0:n.trim()}));return e.map((function(e){var n,r,a=sa(e,2),o=a[0],i=a[1];return[o,(n=t,r=null==i?void 0:i.trim(),n.reduce((function(e,t){return t===r?e+1:e}),0)>1?"".concat(i," (").concat(o,")"):i)]})).map(da)},ha=n(56353),ma=n(1425),ya=n(25015),va=function(e,t){return Object.defineProperty?Object.defineProperty(e,"raw",{value:t}):e.raw=t,e},ga=Ae.Z.span(wr||(wr=va(["\n    color: rgb(52, 69, 99);\n    font-weight: 500;\n    padding: 0px 4px;\n    white-space: nowrap;\n    vertical-align: middle;\n"],["\n    color: rgb(52, 69, 99);\n    font-weight: 500;\n    padding: 0px 4px;\n    white-space: nowrap;\n    vertical-align: middle;\n"]))),ba=Ae.Z.span(Er||(Er=va(["\n    color: rgb(94, 108, 132);\n    padding: 0px 4px;\n    overflow: hidden;\n    text-overflow: ellipsis;\n    white-space: nowrap;\n    vertical-align: middle;\n"],["\n    color: rgb(94, 108, 132);\n    padding: 0px 4px;\n    overflow: hidden;\n    text-overflow: ellipsis;\n    white-space: nowrap;\n    vertical-align: middle;\n"]))),wa=Ae.Z.div(xr||(xr=va(["\n    display: flex;\n    margin-bottom: 20px;\n"],["\n    display: flex;\n    margin-bottom: 20px;\n"]))),Ea=n(7554),xa=n(91594),Ca=function(e,t){return Object.defineProperty?Object.defineProperty(e,"raw",{value:t}):e.raw=t,e},Sa=function(e){var t=e.text,n=e.onChange,r=e.deleteTask,o=e.deletable;return a.createElement("div",{style:{position:"relative"}},a.createElement(Ea.Z,{value:t,onChange:function(e){return n(e.target.value)},placeholder:"This is an *example* task"}),o&&a.createElement(Oa,{onClick:r},a.createElement(xa.Z,{label:"delete",size:"small"})))},Oa=Ae.Z.div(Cr||(Cr=Ca(["\n    color: #a5adba;\n    position: absolute;\n    right: 10px;\n    top: 50%;\n    transform: translateY(-50%);\n    display: flex;\n    align-items: center;\n\n    &:hover {\n        cursor: pointer;\n        color: #42526e;\n    }\n"],["\n    color: #a5adba;\n    position: absolute;\n    right: 10px;\n    top: 50%;\n    transform: translateY(-50%);\n    display: flex;\n    align-items: center;\n\n    &:hover {\n        cursor: pointer;\n        color: #42526e;\n    }\n"]))),Pa=function(e){var t=e.text,n=e.onChange,r=e.deleteTask,o=e.deletable;return a.createElement("div",{style:{width:500,marginBottom:10}},a.createElement(Sa,{text:t,onChange:n,deleteTask:r,deletable:o}))},ja=n(49159),ka=function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,a,o=n.call(e),i=[];try{for(;(void 0===t||t-- >0)&&!(r=o.next()).done;)i.push(r.value)}catch(e){a={error:e}}finally{try{r&&!r.done&&(n=o.return)&&n.call(o)}finally{if(a)throw a.error}}return i},Ta=function(e,t,n){if(n||2===arguments.length)for(var r,a=0,o=t.length;a<o;a++)!r&&a in t||(r||(r=Array.prototype.slice.call(t,0,a)),r[a]=t[a]);return e.concat(r||Array.prototype.slice.call(t))},Aa=function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,a,o=n.call(e),i=[];try{for(;(void 0===t||t-- >0)&&!(r=o.next()).done;)i.push(r.value)}catch(e){a={error:e}}finally{try{r&&!r.done&&(n=o.return)&&n.call(o)}finally{if(a)throw a.error}}return i},Da=function(e,t,n){if(n||2===arguments.length)for(var r,a=0,o=t.length;a<o;a++)!r&&a in t||(r||(r=Array.prototype.slice.call(t,0,a)),r[a]=t[a]);return e.concat(r||Array.prototype.slice.call(t))},Ma={jira_subtask_link:{name:"Parent-Subtask Link",inward:"parent",outward:"subtask"}},Ia=function(e,t){var n=Ma[e.name];return"outward"===t?n?"".concat(n.outward," (").concat(n.name,")"):"".concat(e.outward," (").concat(e.name,")"):n?"".concat(n.inward," (").concat(n.name,")"):"".concat(e.inward," (").concat(e.name,")")},Na=function(e){var t=e.excludeUniDirectional;return function(e){var n=e.issueLinkTypes.filter((function(e){return!t||e.inward!==e.outward})).reduce((function(e,t){var n=t.isSystemLink?"System Links":"Custom Links";return e[n]?e[n].push(t):e[n]=[t],e}),{});return Object.entries(n).map((function(e){var t,n=Aa(e,2),r=n[0],a=n[1];return{label:r,options:(t=a,t.reduce((function(e,t){return Da(Da([],Aa(e),!1),[{value:"".concat(t.id,"-outward"),label:Ia(t,"outward")},{value:"".concat(t.id,"-inward"),label:Ia(t,"inward")}],!1)}),[]))}}))}},La=function(e){var t;return a.createElement(Lt.Z,{value:e.value,handleChange:e.handleChange,invalidItemSelected:function(e){return"Failed to find issue link type with id and direction: ".concat(e)},loadAllUrl:function(){return"".concat(AJS.contextPath(),"/rest/scriptrunner-jira/latest/issueLinking/linkTypes")},mapResultsToOption:Na(e),type:null!==(t=e.plurality)&&void 0!==t?t:"single",placeholder:e.placeholder})},Za=function(){return Za=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},Za.apply(this,arguments)},_a=function(){return _a=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},_a.apply(this,arguments)},Fa=function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,a,o=n.call(e),i=[];try{for(;(void 0===t||t-- >0)&&!(r=o.next()).done;)i.push(r.value)}catch(e){a={error:e}}finally{try{r&&!r.done&&(n=o.return)&&n.call(o)}finally{if(a)throw a.error}}return i},Ra=function(){return Ra=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},Ra.apply(this,arguments)},za=function(e){return e.filter((function(e){return!e.default})).map((function(e){return{value:e.name,label:e.name}}))},Ua=function(e){return a.createElement(Lt.Z,Ra({},e,{value:e.value,handleChange:e.handleChange,invalidItemSelected:function(e){return"Failed to find workflow with name: ".concat(e)},loadAllUrl:function(){return"".concat(AJS.contextPath(),"/rest/api/2/workflow")},mapResultsToOption:za,placeholder:e.placeholder}))},Ba=function(){return Ba=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},Ba.apply(this,arguments)},Ja=function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,a,o=n.call(e),i=[];try{for(;(void 0===t||t-- >0)&&!(r=o.next()).done;)i.push(r.value)}catch(e){a={error:e}}finally{try{r&&!r.done&&(n=o.return)&&n.call(o)}finally{if(a)throw a.error}}return i},qa=function(e){var t=e.value,n=e.repositoryId,r=e.handleChange,o=e.isLoadingSelectedValue,i=e.error,l=e.placeholder;return a.createElement(N.Z,{minCharacters:0,value:t,fetchSingleItemByKey:function(e){return"".concat(AJS.contextPath(),"/rest/scriptrunner-bitbucket/latest/branch?compositeId=").concat(e)},searchUrl:function(e){return"".concat(AJS.contextPath(),"/rest/scriptrunner-bitbucket/latest/repository/").concat(n,"/branches?filterText=").concat(e)},invalidItemSelected:function(e){return"Failed to find branch with repositoryId/ref: ".concat(e)},mapItemToOption:function(e){return{value:String(n+"/"+e.id),label:e.displayId}},mapSearchResultToSuggestions:function(e){return e.map((function(e){return{value:n+"/"+e.id,label:e.displayId}}))},handleChange:r,isDisabled:!n,isLoadingSelectedValue:o,getTotalSearchResults:function(e){return e.length},itemNamePlural:"branches",error:i,placeholder:l,type:"single"})},Va=function(){return Va=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},Va.apply(this,arguments)},Wa=function(e,t,n,r){return new(n||(n=Promise))((function(a,o){function i(e){try{c(r.next(e))}catch(e){o(e)}}function l(e){try{c(r.throw(e))}catch(e){o(e)}}function c(e){var t;e.done?a(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(i,l)}c((r=r.apply(e,t||[])).next())}))},Ga=function(e,t){var n,r,a,o,i={label:0,sent:function(){if(1&a[0])throw a[1];return a[1]},trys:[],ops:[]};return o={next:l(0),throw:l(1),return:l(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function l(o){return function(l){return function(o){if(n)throw new TypeError("Generator is already executing.");for(;i;)try{if(n=1,r&&(a=2&o[0]?r.return:o[0]?r.throw||((a=r.return)&&a.call(r),0):r.next)&&!(a=a.call(r,o[1])).done)return a;switch(r=0,a&&(o=[2&o[0],a.value]),o[0]){case 0:case 1:a=o;break;case 4:return i.label++,{value:o[1],done:!1};case 5:i.label++,r=o[1],o=[0];continue;case 7:o=i.ops.pop(),i.trys.pop();continue;default:if(!(a=i.trys,(a=a.length>0&&a[a.length-1])||6!==o[0]&&2!==o[0])){i=0;continue}if(3===o[0]&&(!a||o[1]>a[0]&&o[1]<a[3])){i.label=o[1];break}if(6===o[0]&&i.label<a[1]){i.label=a[1],a=o;break}if(a&&i.label<a[2]){i.label=a[2],i.ops.push(o);break}a[2]&&i.ops.pop(),i.trys.pop();continue}o=t.call(e,i)}catch(e){o=[6,e],r=0}finally{n=a=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,l])}}},Qa=function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,a,o=n.call(e),i=[];try{for(;(void 0===t||t-- >0)&&!(r=o.next()).done;)i.push(r.value)}catch(e){a={error:e}}finally{try{r&&!r.done&&(n=o.return)&&n.call(o)}finally{if(a)throw a.error}}return i},Ka=function(e){var t=Qa(a.useState(),2),n=t[0],r=t[1],o=Qa(a.useState(),2),l=o[0],c=o[1];return a.useEffect((function(){Wa(void 0,void 0,void 0,(function(){var t;return Ga(this,(function(n){return e.param.value&&((t=e.param.value.split("/",2))?(c(null),r(t[0])):console.error("error resolving repository / ref from composite key : ".concat(e.param.value))),[2]}))}))}),[n,e.param.value]),a.createElement(i.Z,Va({},e),a.createElement(Rn,{value:n,handleChange:function(t){n!==t&&(c(null),r(t),e.onChange(null))},plurality:"single"}),a.createElement("div",{style:{height:10}}),e.children(n,l))},Ya=function(e){var t=e.value,n=e.repositoryId,r=e.handleChange,o=e.isLoadingSelectedValue,i=e.error,l=e.placeholder;return a.createElement(N.Z,{minCharacters:0,value:t,fetchSingleItemByKey:function(e){return"".concat(AJS.contextPath(),"/rest/scriptrunner-bitbucket/latest/tag?compositeId=").concat(e)},searchUrl:function(e){return"".concat(AJS.contextPath(),"/rest/scriptrunner-bitbucket/latest/repository/").concat(n,"/tags?filterText=").concat(e)},invalidItemSelected:function(e){return"Failed to find tag with repositoryId/ref: ".concat(e)},mapItemToOption:function(e){return{value:String("".concat(n,"/").concat(e.id)),label:e.displayId}},mapSearchResultToSuggestions:function(e){return e.map((function(e){return{value:"".concat(n,"/").concat(e.id),label:e.displayId}}))},handleChange:r,isDisabled:!n,isLoadingSelectedValue:o,getTotalSearchResults:function(e){return e.length},itemNamePlural:"tags",error:i,placeholder:l,type:"single"})},$a=function(){return $a=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},$a.apply(this,arguments)},Ha=[{component:f},{type:"password",component:f},{type:"number",component:f},{type:"text",component:f},{type:"checkedScriptTextOrFile",component:y},{type:"parameterisedCheckedScriptTextOrFile",component:function(e){return a.createElement(Rt.Z,$a({},e))}},{type:"html",component:v},{type:"html",component:g,predicates:[function(e){return e.value&&!!e.value.startsWith("<table")}]},{type:"full-width-html",component:g},{type:"pagetree",component:bt},{type:"tree",component:S},{type:"repositoryAndProjectSelection",component:function(e){var t=e.param,n=e.onChange,r=e.errorMessage;return a.createElement(pt,{param:t,onChange:n,iconRenderer:Or,errorMessage:r})}},{type:"applicationLinkSelection",component:function(e){var t=e.param,n=e.onChange,r=e.errorMessage;return a.createElement(pt,{param:t,onChange:n,iconRenderer:Pr,errorMessage:r})}},{type:"checkbox",predicates:[function(e,t){return t.item&&"com.onresolve.scriptrunner.canned.jira.admin.EditCannedComments"===t.item["canned-script"]}],component:P},{type:"checkbox",component:D},{type:"radio",component:I},{type:"SendCustomEmailOptionsPicker",component:J},{type:"select",component:function(e){var t=e.param,n=t.disabled,r=t.plurality,o=t.value,l=t.values,c=t.placeholder,u=e.onChange,s=pa(l)?l.map((function(e){return{label:e[0],options:fa(e[1])}})):fa(l),p=pa(l)?l.flatMap((function(e){return e[1].map(da)})):s,d="multiple"===r,f=n?{placeholder:null}:{},h=d?p.filter((function(e){return o&&o.includes(e.value)})):p.find((function(e){return e.value===o})),m=p.map((function(e){return e.value})),y=[];o&&(y=Array.isArray(o)?o:[o]);var v=y.filter((function(e){return!m.includes(e)})),g=y.filter((function(e){return m.includes(e)})),b=v.length?"The previously selected value(s): ".concat(v.map((function(e){return'"'.concat(e,'"')})).join(", ")," are no longer valid."):null,w=ua({classNamePrefix:"sr-rs",isMulti:d,options:s,onChange:function(e){return u(Array.isArray(e)?e.map((function(e){return null==e?void 0:e.value})):null==e?void 0:e.value)},value:h,formatOptionLabel:ca.s,filterOption:(0,la.c)({ignoreAccents:!1}),isClearable:!0,isDisabled:n,placeholder:c},f);return a.createElement(i.Z,ua({},e),a.createElement("div",{style:{maxWidth:500}},a.createElement(fr.ZP,ua({},w))),b?a.createElement(a.Fragment,null,a.createElement("div",{className:"error"},b),a.createElement("a",{className:"action",onClick:function(){return u(d?g:null)}},"Clear invalid value(s)")):null)}},{type:"checkedScriptFile",component:W},{type:"multilist",component:Y},{type:"list",component:X},{type:"stashUsers",component:me},{type:"user",component:me},{type:"userIncludingInactive",component:me},{type:"confluenceGroupField",component:we},{type:"stashGroups",component:we},{type:"groupPicker",component:we},{type:"confluenceSpaceField",component:Ce},{type:"confluencePageField",component:Ir},{type:"confluenceAttachmentField",component:_r},{type:"textarea",component:Oe},{type:"textarea",component:xt,predicates:[function(e){return e.cssClass&&!!e.cssClass.match(/\bCodeMirror\b/i)}]},{type:"jqlQuery",component:Ct.Z},{type:"durationPicker",component:kt},{type:"replaceText",component:Dt},{type:"jsonSchemaParameter",component:Nt},{type:"eventsPicker",component:function(e){var t=e.param;return a.createElement(i.Z,Ft({},e,{param:t}),a.createElement("div",{style:{maxWidth:500}},a.createElement(_t,{type:"multiple",handleChange:e.onChange,value:t.value,scope:t.scope,eventCategory:t.eventCategory,isDisabled:t.disabled})))}},{type:"cql",component:Wt},{type:"intervalOrCronExpression",component:cn},{type:"dateDiff",component:Xt},{type:"jira-field",component:nn},{type:"projectPicker",component:dn},{type:"projectPickerWithAllProjects",component:function(e){var t=e.param,n=t.includeArchived,r=t.name,o=t.value,l=ia((0,a.useState)(!o||o.includes("")),2),c=l[0],u=l[1],s="".concat(r,"_ALL_PROJECTS"),p="".concat(r,"_SELECTED_PROJECTS"),d=function(){var t=!c;e.onChange(t?[""]:null),u(t)};return a.createElement(i.Z,oa({},e),a.createElement("div",{style:{marginTop:5,marginBottom:5}},a.createElement("input",{type:"radio",checked:c,name:r,id:s,onChange:d}),a.createElement("label",{htmlFor:s},"Global (All projects)"),a.createElement("br",null),a.createElement("input",{type:"radio",checked:!c,name:r,id:p,onChange:d}),a.createElement("label",{htmlFor:p},"Select project(s)")),a.createElement(sn,oa({},e.param,{returnsProjectKey:!0,key:"".concat(r,":").concat(n?1:0),value:c||!o?null:o.filter((function(e){return""!==e})),type:"multiple",handleChange:e.onChange,isDisabled:c,placeholder:c?"":"Select project(s)"})))}},{type:"projectKeyPicker",component:function(e){var t=$a($a({},e.param),{returnsProjectKey:!0});return a.createElement(dn,$a({},e,{param:t}))}},{type:"bitbucketProjectPicker",component:vn},{type:"agePicker",component:_n},{type:"multiParamSelector",component:function(e){var t=Br((0,a.useState)([]),2),n=t[0],r=t[1];return(0,a.useEffect)((function(){return e.onChange(n)}),[n]),(0,a.useEffect)((function(){return r([])}),[e.param.macroKey]),a.createElement("div",null,a.createElement(i.Z,Ur({},e),a.createElement(qr,null,a.createElement(gn.Z,{className:"macroParamSelector",classNamePrefix:"sr-rs",options:e.param.options,isMulti:!0,key:e.param.macroKey,isSearchable:!0,placeholder:"Select parameter(s)",onChange:function(e){return r(e)}}))),a.createElement(i.Z,{param:{name:"params",label:"",description:"",type:"list"},id:"macroParameters"},a.createElement(aa,{macroParams:n,setMacroParameters:r})))}},{type:"dateRangePicker",component:On},{type:"repositoryPicker",component:function(e){var t=e.param,n=e.errorMessage,r=e.onChange;return a.createElement(i.Z,{param:t,errorMessage:n},a.createElement("div",{style:{maxWidth:500}},a.createElement(Rn,{handleChange:r,value:t.value,permission:t.permission,isDisabled:t.disabled,plurality:t.plurality})))}},{type:"branchPicker",component:function(e){return a.createElement(Ka,{param:e.param,childUrl:function(e){return"".concat(AJS.contextPath(),"/rest/scriptrunner-bitbucket/latest/branch?compositeId=").concat(e)},onChange:e.onChange},(function(t){return a.createElement(qa,{handleChange:e.onChange,value:e.param.value,placeholder:e.param.placeholder,repositoryId:t,isLoadingSelectedValue:!t&&null!=e.param.value})}))}},{type:"tagPicker",component:function(e){return a.createElement(Ka,{param:e.param,childUrl:function(e){return"".concat(AJS.contextPath(),"/rest/scriptrunner-bitbucket/latest/tag?compositeId=").concat(e)},onChange:e.onChange},(function(t){return a.createElement(Ya,{handleChange:e.onChange,value:e.param.value,placeholder:e.param.placeholder,repositoryId:t,isLoadingSelectedValue:!t&&null!=e.param.value})}))}},{type:"priorityPicker",component:Bn},{type:"issueTypePicker",component:Vn},{type:"issueLinkTypePicker",component:function(e){var t,n=e.param,r=e.onChange;return a.createElement(i.Z,{param:n},a.createElement(Wn,{value:n.value,handleChange:r,placeholder:n.placeholder,plurality:null!==(t=n.plurality)&&void 0!==t?t:"single"}))}},{type:"issueLinkDirectionPicker",component:function(e){return a.createElement(i.Z,Za({},e),a.createElement(La,{value:e.param.value,handleChange:e.onChange,placeholder:e.param.placeholder,plurality:e.param.plurality,excludeUniDirectional:e.param.excludeUniDirectionalLinks}))}},{type:"projectRolePicker",component:function(e){var t=e.param,n=e.onChange;return a.createElement(i.Z,Qn({},e),a.createElement(Gn.k,{value:t.value,handleChange:n,placeholder:t.placeholder,plurality:t.plurality}))}},{type:"savedFilterPicker",component:function(e){var t,n=e.param,r=e.onChange;return a.createElement(i.Z,{param:n},a.createElement(Kn,{value:n.value,handleChange:r,placeholder:n.placeholder,plurality:null!==(t=n.plurality)&&void 0!==t?t:"single"}))}},{type:"issueStatusPicker",component:function(e){var t,n=e.param,r=e.onChange;return a.createElement(i.Z,{param:n},a.createElement(Yn,{value:n.value,handleChange:r,placeholder:n.placeholder,plurality:null!==(t=n.plurality)&&void 0!==t?t:"single"}))}},{type:"customFieldPicker",component:function(e){var t,n=e.param,r=e.onChange;return a.createElement(i.Z,{param:n},a.createElement(nr,{value:n.value,handleChange:r,placeholder:n.placeholder,plurality:null!==(t=n.plurality)&&void 0!==t?t:"single"}))}},{type:"resolutionPicker",component:function(e){var t;return a.createElement(i.Z,or({},e),a.createElement(ar,{value:e.param.value,handleChange:e.onChange,placeholder:e.param.placeholder,type:null!==(t=e.param.plurality)&&void 0!==t?t:"single"}))}},{type:"workflowNamePicker",component:function(e){var t=e.param,n=t.name,r=t.value,o=Ja((0,a.useState)(!r||r.includes("")),2),l=o[0],c=o[1],u="".concat(n,"_ALL_WORKFLOWS"),s="".concat(n,"_SELECTED_WORKFLOWS"),p=function(){var t=!l;e.onChange(t?[""]:null),c(t)};return a.createElement(i.Z,Ba({},e),a.createElement("div",{style:{marginTop:5,marginBottom:5}},a.createElement("input",{type:"radio",checked:l,name:n,id:u,onChange:p}),a.createElement("label",{htmlFor:u},"Global (All Workflows)"),a.createElement("br",null),a.createElement("input",{type:"radio",checked:!l,name:n,id:s,onChange:p}),a.createElement("label",{htmlFor:s},"Select workflow(s)")),!l&&a.createElement(Ua,Ba({},e.param,{key:n,value:r,type:"multiple",handleChange:e.onChange,placeholder:"Select workflow(s)"})))}},{type:"resolutionPickerWithAny",component:function(e){var t=e.param,n=t.name,r=t.value,o=Fa((0,a.useState)(!r||r.includes("-1")),2),l=o[0],c=o[1],u="".concat(n,"_ANY_RESOLUTION"),s="".concat(n,"_SELECTED_RESOLUTIONS"),p=function(){var t=!l;e.onChange(t?["-1"]:null),c(t)};return a.createElement(i.Z,_a({},e),a.createElement("div",{style:{marginTop:5,marginBottom:5}},a.createElement("input",{type:"radio",checked:l,name:n,id:u,onChange:p}),a.createElement("label",{htmlFor:u},"Any resolution"),a.createElement("br",null),a.createElement("input",{type:"radio",checked:!l,name:n,id:s,onChange:p}),a.createElement("label",{htmlFor:s},"Select resolution(s)")),!l&&a.createElement(ar,_a({},e.param,{key:n,value:r,type:"multiple",handleChange:e.onChange,placeholder:"Select resolutions(s)"})))}},{type:"versionPicker",component:function(e){return a.createElement(pr,{param:e.param,childUrl:function(e){return"".concat(AJS.contextPath(),"/rest/api/2/version/").concat(e)},onChange:e.onChange},(function(t,n){var r;return a.createElement(ir,{value:e.param.value,isLoadingSelectedValue:!t&&null!=e.param.value,projectId:t,error:n,plurality:null!==(r=e.param.plurality)&&void 0!==r?r:"single",placeholder:e.param.placeholder,handleChange:e.onChange})}))}},{type:"componentPicker",component:function(e){return a.createElement(pr,{param:e.param,childUrl:function(e){return"".concat(AJS.contextPath(),"/rest/api/2/component/").concat(e)},onChange:e.onChange},(function(t,n){var r;return a.createElement(dr,{value:e.param.value,isLoadingSelectedValue:!t&&null!=e.param.value,projectId:t,error:n,placeholder:e.param.placeholder,handleChange:e.onChange,plurality:null!==(r=e.param.plurality)&&void 0!==r?r:"single"})}))}},{type:"describedOptionSelectList",component:mr},{type:"message",component:function(e){var t=e.param;return a.createElement("div",{className:"field-group"},a.createElement(wa,null,function(e){switch(e){case"info":return a.createElement(ha.Z,{size:"medium",label:e,primaryColor:"#7a869a"});case"warning":return a.createElement(ma.Z,{size:"medium",label:e,primaryColor:"#FFAB00"});case"error":return a.createElement(ya.Z,{size:"medium",label:e,primaryColor:"#FF5630"})}}(t.value.messageLevel),a.createElement(ga,null,t.value.title),a.createElement(ba,null,t.value.text)))}},{type:"pullRequestTasks",component:function(e){var t,n=e.param,r=e.errorMessage,o=e.onChange,l=ka(a.useState(null!==(t=n.value)&&void 0!==t?t:[""]),2),c=l[0],u=l[1];a.useEffect((function(){o(c)}),[c]);return a.createElement(i.Z,{param:n,errorMessage:r},c.map((function(e,t){return a.createElement(Pa,{key:t,text:e,onChange:function(e){return function(e,t){var n=Ta([],ka(c),!1);n[e]=t,u(n)}(t,e)},deleteTask:function(){return function(e){var t=Ta([],ka(c),!1);t.splice(e,1),u(t)}(t)},deletable:c.length>1})})),a.createElement(ja.Z,{onClick:function(){u(Ta(Ta([],ka(c),!1),[""],!1))}},"Add task"))}}];const Xa=function(e){var t=e.config?e.config:{},n=function(e,t,n){var r=e.filter((function(e){return void 0===e.type&&1===Object.keys(e).length}))[0].component,a=e.filter((function(e){return e.type===n.type}));return a.length<1?r:a.map((function(e){var r=0;return e.predicates&&e.predicates.length>0&&e.predicates.forEach((function(e){!0===e(n,t)?r++:r--})),$a($a({},e),{matchingPredicates:r})})).sort((function(e,t){return t.matchingPredicates<e.matchingPredicates?-1:1}))[0].component}(Ha,t,e.param);return e.overrides?a.createElement("div",null,e.overrides()):a.createElement(n,{onChange:e.onChange,key:e.param.name,param:e.param,errorMessage:e.errorMessage,overrides:e.overrides,updateValidation:e.updateValidation,validation:e.validation,onChangeFn:e.onChangeFn})}},85767:(e,t,n)=>{"use strict";n.d(t,{Z:()=>u});var r=n(63844),a=n(88162),o=n(39997),i=n(39507),l=function(){return l=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},l.apply(this,arguments)},c=l(l({},o._.BaseGroupOptions),{fetchSingleItemByKey:function(e){return"".concat(AJS.contextPath(),"/rest/scriptrunner-bitbucket/latest/group/").concat(e)},searchUrl:function(e){return"".concat(AJS.contextPath(),"/rest/api/latest/groups?").concat((0,i.F)({filter:e,start:0}))},mapItemToOption:function(e){return{value:e,label:e,icon:null}},mapSearchResultToSuggestions:function(e){return e.values.map((function(e){return{value:e,label:e}}))},getTotalSearchResults:function(e){return e.size},invalidItemSelected:function(e){return"Failed to find group with name: ".concat(e)}});const u=function(e){return r.createElement(a.Z,l({},c,e))}},24692:(e,t,n)=>{"use strict";n.d(t,{Yj:()=>u,ZP:()=>s});var r=n(63844),a=n(88162),o=n(39997),i=n(39507),l=function(){return l=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},l.apply(this,arguments)},c=l(l({},o._.BaseGroupOptions),{searchUrl:function(e){return"".concat(AJS.contextPath(),"/rest/scriptrunner-confluence/latest/groups?").concat((0,i.F)({searchTerm:e}))},mapSearchResultToSuggestions:function(e){return e.groups.map((function(e){return{value:e,label:e,icon:"".concat(AJS.contextPath(),"/images/icons/avatar_group_48.png")}}))},fetchSingleItemByKey:function(e){return"".concat(AJS.contextPath(),"/rest/api/group/").concat(e)},mapItemToOption:function(e){return{value:e.name,label:e.name}}}),u=function(e){return r.createElement(a.Z,l({},c,e))};const s=u},39731:(e,t,n)=>{"use strict";n.d(t,{p:()=>c});var r=n(63844),a=n(88162),o=n(39507),i=function(){return i=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},i.apply(this,arguments)},l={fetchSingleItemByKey:function(e){return"".concat(AJS.contextPath(),"/rest/api/space/").concat(e,"?expand=icon")},searchUrl:function(e){return"".concat(AJS.contextPath(),"/rest/scriptrunner-confluence/latest/spaces/getspaces?").concat((0,o.F)({query:e}))},invalidItemSelected:function(e){return"Failed to find space with ID: ".concat(e)},mapItemToOption:function(e){return{value:e.key,label:e.name,icon:AJS.contextPath()+e.icon.path}},mapSearchResultToSuggestions:function(e){return e.results.map((function(e){return{value:e.key,label:e.name,icon:e.icon}}))},placeholder:"Select space(s)",itemNamePlural:"spaces",getTotalSearchResults:function(e){return e.totalSize}},c=function(e){return r.createElement(a.Z,i({},e,l))}},98581:(e,t,n)=>{"use strict";n.d(t,{U:()=>u});var r=n(63844),a=n(88162),o=n(39997),i=n(39507),l=function(){return l=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},l.apply(this,arguments)},c=l(l({},o._.BaseUserOptions),{searchUrl:function(e){return"".concat(AJS.contextPath(),"/rest/prototype/1/search/user.json?").concat((0,i.F)({"max-results":20,showAvatar:!0,query:e}))},fetchSingleItemByKey:function(e){return"".concat(AJS.contextPath(),"/rest/api/user?key=").concat(e)},mapItemToOption:function(e){return{value:e.userKey,label:e.displayName,icon:e.profilePicture.path}},mapSearchResultToSuggestions:function(e){return e.result.map((function(e){return{value:e.userKey,label:e.title,icon:e.thumbnailLink.href}}))},getTotalSearchResults:function(e){return e.totalSize}}),u=function(e){return r.createElement(a.Z,l({},e,c))}},63363:(e,t,n)=>{"use strict";n.d(t,{k:()=>o});var r=n(63844),a=n(53997),o=function(e){var t;return r.createElement(a.Z,{value:e.value,handleChange:e.handleChange,invalidItemSelected:function(e){return"Failed to find project role with id: ".concat(e)},loadAllUrl:function(){return"".concat(AJS.contextPath(),"/rest/api/2/role")},mapResultsToOption:function(e){return e.map((function(e){return{value:e.id.toString(),label:e.name}}))},type:null!==(t=e.plurality)&&void 0!==t?t:"single",placeholder:e.placeholder,styles:e.styles})}},25580:(e,t,n)=>{"use strict";n.d(t,{Z:()=>f});var r,a=n(53997),o=n(88162),i=n(74729),l=(r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},r(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),c=function(){return c=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},c.apply(this,arguments)},u=function(e,t,n,r){return new(n||(n=Promise))((function(a,o){function i(e){try{c(r.next(e))}catch(e){o(e)}}function l(e){try{c(r.throw(e))}catch(e){o(e)}}function c(e){var t;e.done?a(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(i,l)}c((r=r.apply(e,t||[])).next())}))},s=function(e,t){var n,r,a,o,i={label:0,sent:function(){if(1&a[0])throw a[1];return a[1]},trys:[],ops:[]};return o={next:l(0),throw:l(1),return:l(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function l(o){return function(l){return function(o){if(n)throw new TypeError("Generator is already executing.");for(;i;)try{if(n=1,r&&(a=2&o[0]?r.return:o[0]?r.throw||((a=r.return)&&a.call(r),0):r.next)&&!(a=a.call(r,o[1])).done)return a;switch(r=0,a&&(o=[2&o[0],a.value]),o[0]){case 0:case 1:a=o;break;case 4:return i.label++,{value:o[1],done:!1};case 5:i.label++,r=o[1],o=[0];continue;case 7:o=i.ops.pop(),i.trys.pop();continue;default:if(!(a=i.trys,(a=a.length>0&&a[a.length-1])||6!==o[0]&&2!==o[0])){i=0;continue}if(3===o[0]&&(!a||o[1]>a[0]&&o[1]<a[3])){i.label=o[1];break}if(6===o[0]&&i.label<a[1]){i.label=a[1],a=o;break}if(a&&i.label<a[2]){i.label=a[2],i.ops.push(o);break}a[2]&&i.ops.pop(),i.trys.pop();continue}o=t.call(e,i)}catch(e){o=[6,e],r=0}finally{n=a=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,l])}}},p=function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,a,o=n.call(e),i=[];try{for(;(void 0===t||t-- >0)&&!(r=o.next()).done;)i.push(r.value)}catch(e){a={error:e}}finally{try{r&&!r.done&&(n=o.return)&&n.call(o)}finally{if(a)throw a.error}}return i},d=function(e,t,n){if(n||2===arguments.length)for(var r,a=0,o=t.length;a<o;a++)!r&&a in t||(r||(r=Array.prototype.slice.call(t,0,a)),r[a]=t[a]);return e.concat(r||Array.prototype.slice.call(t))};const f=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.getAll=function(e){return u(t,void 0,void 0,(function(){var t,n,r,a,o;return s(this,(function(i){switch(i.label){case 0:t=[],n=!1,r=this.props.startIterationFrom,i.label=1;case 1:return n?[3,3]:[4,this.pagedFetch(r)];case 2:return(a=i.sent()).result?(o=this.props.convertResultToPagedData(a.result),r=this.props.incrementPageNumber(r,e),n=o.isLastPage,t=t.concat.apply(t,d([],p(o.values),!1)),[3,1]):(console.warn("Fetchnig data from ".concat(this.props.loadAllUrl()," for page ").concat(r," failed.")),[2,null]);case 3:return[2,t]}}))}))},t.pagedFetch=function(e){return u(t,void 0,Promise,(function(){var t,n,r,a,o,l,c;return s(this,(function(u){switch(u.label){case 0:return t=this.props,n=t.limitParam,r=t.pageSize,a=t.startParam,o=t.loadAllUrl,l="".concat(o(),"?").concat(n,"=").concat(r,"&").concat(a,"=").concat(e),[4,(0,i.wrappedFetch)(l,{credentials:"same-origin",headers:{Accept:"application/json"}})];case 1:return(c=u.sent()).error&&(this.showLoading(!1),this.setState({errors:[{message:"Failed to load options"}],isDisabled:!0})),[2,c]}}))}))},t}return l(t,e),t.prototype.componentDidMount=function(){return u(this,void 0,Promise,(function(){var e,t;return s(this,(function(n){switch(n.label){case 0:return this.state.itemsLoaded||this.props.isLoadingSelectedValue?[3,2]:(this.setState({itemsLoaded:!0}),this.showLoading(!0),[4,this.getAll(this.props.pageSize)]);case 1:null!=(e=n.sent())&&(t=this.props.mapResultsToOption(e),this.filterResults(t)),this.showLoading(!1),n.label=2;case 2:return[2]}}))}))},t.defaultProps=c(c(c({},o.Z.defaultProps),a.Z.defaultProps),{pageSize:50,startParam:"start",limitParam:"limit",incrementPageNumber:function(e,t){return e+t},startIterationFrom:0,convertResultToPagedData:function(e){return e}}),t}(a.Z)},46086:(e,t,n)=>{"use strict";var r,a,o;a=[n(65311)],r=function(e,t){function n(e,t,n,r){return!(e.selector!=t.selector||e.context!=t.context||n&&n.$lqguid!=t.fn.$lqguid||r&&r.$lqguid!=t.fn2.$lqguid)}e.extend(e.fn,{livequery:function(t,a){var o,i=this;return e.each(r.queries,(function(e,r){if(n(i,r,t,a))return(o=r)&&!1})),(o=o||new r(i.selector,i.context,t,a)).stopped=!1,o.run(),i},expire:function(t,a){var o=this;return e.each(r.queries,(function(e,i){n(o,i,t,a)&&!o.stopped&&r.stop(i.id)})),o}});var r=e.livequery=function(t,n,a,o){var i=this;return i.selector=t,i.context=n,i.fn=a,i.fn2=o,i.elements=e([]),i.stopped=!1,i.id=r.queries.push(i)-1,a.$lqguid=a.$lqguid||r.guid++,o&&(o.$lqguid=o.$lqguid||r.guid++),i};r.prototype={stop:function(){var t=this;t.stopped||(t.fn2&&t.elements.each(t.fn2),t.elements=e([]),t.stopped=!0)},run:function(){var t=this;if(!t.stopped){var n=t.elements,r=e(t.selector,t.context),a=r.not(n),o=n.not(r);t.elements=r,a.each(t.fn),t.fn2&&o.each(t.fn2)}}},e.extend(r,{guid:0,queries:[],queue:[],running:!1,timeout:null,registered:[],checkQueue:function(){if(r.running&&r.queue.length)for(var e=r.queue.length;e--;)r.queries[r.queue.shift()].run()},pause:function(){r.running=!1},play:function(){r.running=!0,r.run()},registerPlugin:function(){e.each(arguments,(function(t,n){if(e.fn[n]&&!(e.inArray(n,r.registered)>0)){var a=e.fn[n];e.fn[n]=function(){var e=a.apply(this,arguments);return r.run(),e},r.registered.push(n)}}))},run:function(n){n!==t?e.inArray(n,r.queue)<0&&r.queue.push(n):e.each(r.queries,(function(t){e.inArray(t,r.queue)<0&&r.queue.push(t)})),r.timeout&&clearTimeout(r.timeout),r.timeout=setTimeout(r.checkQueue,20)},stop:function(n){n!==t?r.queries[n].stop():e.each(r.queries,r.prototype.stop)}}),r.registerPlugin("append","prepend","after","before","wrap","attr","removeAttr","addClass","removeClass","toggleClass","empty","remove","html","prop","removeProp"),e((function(){r.play()}))},void 0===(o="function"==typeof r?r.apply(t,a):r)||(e.exports=o)},65325:()=>{"use strict";JSONEditor.defaults.themes.aui=JSONEditor.AbstractTheme.extend({getRangeInput:function(e,t,n){return this._super(e,t,n)},getGridColumn:function(){var e=document.createElement("div");return e.className="remove-me",e},getGridContainer:function(){var e=document.createElement("div");return e.className="aui-group",e},getGridRow:function(){var e=document.createElement("div");return e.className="aui-item",e},getFormInputLabel:function(e){var t=this._super(e);return t.style.display="inline-block",t.style.fontWeight="bold",t},getSwitcher:function(e){return this.getSelectInput(e)},setGridColumnSize:function(e,t){},getSelectInput:function(e){var t=this._super(e);return t.className="select",t},getFormInputField:function(e){var t=this._super(e);return t.className=e,t},afterInputReady:function(e){if(e.group=this.closest(e,".field-group"),"groovy"===e.dataset.schemaformat){var t=window.ace.edit(e.parentElement.querySelector(".ace_editor")),n=e.attributes.getNamedItem("name").value.replace(/]/g,"").replace(/\[/g,".");setupCodeLinting(n,t,e)}},getIndentedPanel:function(){var e=document.createElement("div");return e.className="aui-group",e.style.paddingBottom=0,e},getFormInputDescription:function(e){var t=document.createElement("div");return t.className="description",t.textContent=e,t},getFormControl:function(e,t,n){var r=document.createElement("div");r.className="field-group";var a=document.createElement("div");return a.className="controls",e&&"checkbox"===t.getAttribute("type")?(r.appendChild(a),e.className+=" checkbox",e.appendChild(t),a.appendChild(e),a.style.height="30px"):(e&&(e.className+=" control-label",r.appendChild(e)),a.appendChild(t),r.appendChild(a)),n&&a.appendChild(n),r},getHeaderButtonHolder:function(){return this.getButtonHolder()},getHeader:function(e){var t=document.createElement("div");return(t&&"none"===e||"none"===t.textContent)&&(t.className="hidden"),t},getButtonHolder:function(){var e=document.createElement("div"),t=document.createElement("div");return e.className="buttons-container",e.style.float="left",e.style.width="50%",e.style.padding="10px 0 0 0",t.className="buttons",e.appendChild(t),e},getButton:function(e,t,n){var r=this._super(e,t,n);return r.className+=" aui-button"+("Save"===e?" aui-button-primary":""),r},getTable:function(){var e=document.createElement("table");return e.className="aui",e.style.width="100%",e.style.maxWidth="none",e},addInputError:function(e,t){e.group&&(e.errmsg?e.errmsg.style.display="":(e.errmsg=document.createElement("div"),e.errmsg.className="error",e.group.appendChild(e.errmsg)),e.errmsg.textContent=t)},removeInputError:function(e){e.errmsg&&(e.errmsg.style.display="none")},getTabHolder:function(){var e=document.createElement("div");return e.className="tabbable tabs-left",e.innerHTML="<ul class='nav nav-tabs span2' style='margin-right: 0;'></ul><div class='tab-content span10' style='overflow:visible;'></div>",e},getTab:function(e){var t=document.createElement("li"),n=document.createElement("a");return n.setAttribute("href","#"),n.appendChild(e),t.appendChild(n),t},getTabContentHolder:function(e){return e.children[1]},getTabContent:function(){var e=document.createElement("div");return e.className="tab-pane active",e},markTabActive:function(e){e.className+=" active"},markTabInactive:function(e){e.className=e.className.replace(/\s?active/g,"")},addTab:function(e,t){e.children[0].appendChild(t)},getProgressBar:function(){var e=document.createElement("div");e.className="progress";var t=document.createElement("div");return t.className="bar",t.style.width="0%",e.appendChild(t),e},updateProgressBar:function(e,t){e&&(e.firstChild.style.width=t+"%")},updateProgressBarUnknown:function(e){e&&(e.className="progress progress-striped active",e.firstChild.style.width="100%")}}),JSONEditor.defaults.iconlibs.aui=JSONEditor.AbstractIconLib.extend({mapping:{collapse:"expanded",expand:"collapsed",delete:"delete",edit:"edit",add:"add",cancel:"remove",save:"approve",moveup:"arrow-up",movedown:"arrow-down"},icon_prefix:"aui-icon aui-icon-small aui-iconfont-"})},69862:()=>{},40964:()=>{}}]);