"use strict";(self.webpackJsonpScriptRunner=self.webpackJsonpScriptRunner||[]).push([["default-src_main_resources_js_admin_TopLevelErrorPage_tsx-src_main_resources_js_admin_common_-ee9508"],{38414:(e,t,n)=>{n.d(t,{a:()=>M});var r,i,o,c=n(63844),a=n(52824),u=n(18390),s=n(75337),l=function(e,t){return Object.defineProperty?Object.defineProperty(e,"raw",{value:t}):e.raw=t,e},M=function(e){var t=e.error;return c.createElement("div",null,c.createElement(p,null,c.createElement(d,{src:a}),c.createElement(y,null,"ScriptRunner")),c.createElement(s.B,{error:t}))},p=u.Z.header(r||(r=l(["\n    display: flex;\n    margin-bottom: 15px;\n"],["\n    display: flex;\n    margin-bottom: 15px;\n"]))),d=u.Z.img(i||(i=l(["\n    max-width: 34px;\n    max-height: 34px;\n    margin-right: 12px;\n    margin-left: 8px;\n"],["\n    max-width: 34px;\n    max-height: 34px;\n    margin-right: 12px;\n    margin-left: 8px;\n"]))),y=u.Z.h1(o||(o=l(["\n    margin-top: 0px;\n    font-size: 26px;\n"],["\n    margin-top: 0px;\n    font-size: 26px;\n"])))},92493:(e,t,n)=>{n.d(t,{l:()=>p,t:()=>d});var r,i,o,c,a=n(59462),u=n(18390),s=function(e,t){return Object.defineProperty?Object.defineProperty(e,"raw",{value:t}):e.raw=t,e},l=(0,a.iv)(r||(r=s(["\n    margin-bottom: 0 !important;\n"],["\n    margin-bottom: 0 !important;\n"]))),M=(0,a.iv)(i||(i=s(["\n    .field-group {\n        margin-bottom: 8px !important;\n\n        & label {\n            font-size: 0.88em !important;\n            line-height: 1.3;\n        }\n    }\n"],["\n    .field-group {\n        margin-bottom: 8px !important;\n\n        & label {\n            font-size: 0.88em !important;\n            line-height: 1.3;\n        }\n    }\n"]))),p=u.Z.form(o||(o=s(["\n    ","\n    div:last-child .field-group {\n        ","\n    }\n"],["\n    ","\n    div:last-child .field-group {\n        ","\n    }\n"])),M,l),d=u.Z.div(c||(c=s(["\n    ","\n    .field-group:last-child {\n        ","\n    }\n"],["\n    ","\n    .field-group:last-child {\n        ","\n    }\n"])),M,l)},30080:(e,t,n)=>{n.d(t,{Bc:()=>E,DD:()=>A});var r,i,o,c=n(63844),a=n(49159),u=n(94194),s=n(77510),l=n(56598),M=n(18390),p=n(21705),d=n(93738),y=n(49268),g=n(72142),f=n(79569),N=n(74729),m=function(e,t){return Object.defineProperty?Object.defineProperty(e,"raw",{value:t}):e.raw=t,e},D=function(e,t,n,r){return new(n||(n=Promise))((function(i,o){function c(e){try{u(r.next(e))}catch(e){o(e)}}function a(e){try{u(r.throw(e))}catch(e){o(e)}}function u(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(c,a)}u((r=r.apply(e,t||[])).next())}))},j=function(e,t){var n,r,i,o,c={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return o={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function a(o){return function(a){return function(o){if(n)throw new TypeError("Generator is already executing.");for(;c;)try{if(n=1,r&&(i=2&o[0]?r.return:o[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,o[1])).done)return i;switch(r=0,i&&(o=[2&o[0],i.value]),o[0]){case 0:case 1:i=o;break;case 4:return c.label++,{value:o[1],done:!1};case 5:c.label++,r=o[1],o=[0];continue;case 7:o=c.ops.pop(),c.trys.pop();continue;default:if(!(i=c.trys,(i=i.length>0&&i[i.length-1])||6!==o[0]&&2!==o[0])){c=0;continue}if(3===o[0]&&(!i||o[1]>i[0]&&o[1]<i[3])){c.label=o[1];break}if(6===o[0]&&c.label<i[1]){c.label=i[1],i=o;break}if(i&&c.label<i[2]){c.label=i[2],c.ops.push(o);break}i[2]&&c.ops.pop(),c.trys.pop();continue}o=t.call(e,c)}catch(e){o=[6,e],r=0}finally{n=i=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,a])}}},T=function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,i,o=n.call(e),c=[];try{for(;(void 0===t||t-- >0)&&!(r=o.next()).done;)c.push(r.value)}catch(e){i={error:e}}finally{try{r&&!r.done&&(n=o.return)&&n.call(o)}finally{if(i)throw i.error}}return c},z=function(e,t,n){if(n||2===arguments.length)for(var r,i=0,o=t.length;i<o;i++)!r&&i in t||(r||(r=Array.prototype.slice.call(t,0,i)),r[i]=t[i]);return e.concat(r||Array.prototype.slice.call(t))},x=function(e){return e.error?c.createElement(E,{message:"Error! There was a problem loading the examples."}):c.createElement(h,null,c.createElement("p",null,e.content.bodyText))},w=function(e){var t=e.selectedExamples,n=e.configuredExamples,r=e.onSelectedExamples;return void 0===n||0===n.length?null:c.createElement(c.Fragment,null,c.createElement("h6",null,"Examples to display"),n.map((function(e){return c.createElement(l.X,{isChecked:t.includes(e.uuid),label:e.description,value:e.uuid,key:e.uuid,onChange:function(e){return e.target.checked?r(z(z([],T(t),!1),[e.target.value],!1)):r(t.filter((function(t){return t!==e.target.value})))}})})))},E=function(e){var t=e.message;return c.createElement(I,null,c.createElement(p.Z,{label:t,primaryColor:d.$H}),t)},O=function(e){return D(void 0,void 0,Promise,(function(){var t,n,r;return j(this,(function(i){switch(i.label){case 0:return[4,(0,N.wrappedFetch)("".concat(AJS.contextPath(),"/rest/scriptrunner/latest/examples/").concat(e))];case 1:if(t=i.sent(),n=t.result,r=t.error,n)return[2,n];throw Error(r)}}))}))},I=M.Z.div(r||(r=m(["\n    display: flex;\n    align-items: center;\n"],["\n    display: flex;\n    align-items: center;\n"]))),S=M.Z.div(i||(i=m(["\n    padding: 20px 20px 0 20px;\n    text-align: left;\n"],["\n    padding: 20px 20px 0 20px;\n    text-align: left;\n"]))),h=M.Z.div(o||(o=m(["\n    font-size: 14px;\n    letter-spacing: 0;\n    line-height: 22px;\n    letter: normal;\n    margin-top: 12px;\n"],["\n    font-size: 14px;\n    letter-spacing: 0;\n    line-height: 22px;\n    letter: normal;\n    margin-top: 12px;\n"]))),A=(0,g.$j)(null,(function(e){return{postConfigureExamples:function(t){return e((0,y.i2)(t))},trackConfigureExamples:function(t){return e((0,f.wv)(t))}}}))((function(e){var t=e.isOpen,n=e.setClosed,r=e.featureType,i=e.exampleType,o=e.content,l=e.config,M=e.postConfigureExamples,p=e.trackConfigureExamples,d=T((0,c.useState)([]),2),y=d[0],g=d[1],f=T((0,c.useState)([]),2),N=f[0],m=f[1],z=T((0,c.useState)(!0),2),E=z[0],I=z[1],h=T((0,c.useState)(),2),A=h[0],L=h[1];return(0,c.useEffect)((function(){var e;(e=r,D(void 0,void 0,void 0,(function(){var t,n;return j(this,(function(r){switch(r.label){case 0:return r.trys.push([0,2,3,4]),[4,O(e)];case 1:return t=r.sent(),g(t),m(t.map((function(e){return e.uuid}))),[3,4];case 2:return n=r.sent(),L(n),[3,4];case 3:return I(!1),[7];case 4:return[2]}}))}))).catch(console.warn)}),[r]),t?c.createElement(u.Z,{shouldCloseOnEscapePress:!0,onClose:n},c.createElement(S,null,c.createElement("h2",null,"Add ",o.featureName," Examples"),c.createElement(x,{content:o,loading:E,error:A}),c.createElement(w,{selectedExamples:N,configuredExamples:y,onSelectedExamples:m})),c.createElement(s.Z,null,c.createElement(a.Z,{autoFocus:!0,appearance:"primary",isDisabled:!N.length,onClick:function(){n();var e=y.filter((function(e){return N.includes(e.uuid)}));void 0!==e&&0!==e.length&&M({examples:e,moduleType:i,config:l})}},"Add Examples"),c.createElement(a.Z,{onClick:function(){n(),p({moduleType:i,action:"cancel"}),m(y.map((function(e){return e.uuid})))}},"Cancel"))):null}))},79569:(e,t,n)=>{n.d(t,{ZP:()=>Ce,TR:()=>be,wv:()=>Le,Xi:()=>Ie,D0:()=>ve,Ui:()=>Se,Fk:()=>Ae,LA:()=>ke,em:()=>he,RD:()=>Qe});var r=n(71530),i=n(65171),o=n(50382),c=function(e){return e.payload&&e.payload.result},a=function(e,t){return{Success:function(){this.matches=e.done.match,this.execute=t("success")},Failure:function(){this.matches=e.failed.match,this.execute=t("failure")}}},u=function(e,t){return{Success:function(){this.matches=e.matchFulfilled,this.execute=t("success")},Failure:function(){this.matches=e.matchRejected,this.execute=t("failure")}}},s=function(e,t){var n=e.getState(),r=(0,i.c0)(n),o=t.firstLevelTag||(0,i.wm)(n),c=t.secondLevelTag||(0,i.N9)(n);if(r||o||c)return{query:r,firstLevelTag:o,secondLevelTag:c,resultCount:(0,i.AP)(n).length}},l=function(e,t){var n=o.j.available.scriptWithId(e,t);if(n)return n.source},M=n(65705),p=n(77937),d=["agePicker"],y=function(e){var t,n=null===(t=e.editParams)||void 0===t?void 0:t.filter((function(e){return d.includes(e.type)}));return(null==n?void 0:n.reduce((function(t,n){return t[n.name]=e.edit[n.name],t}),{}))||{}},g=a(p.$9.async,(function(e){return function(t,n,r){var i=n.getState().console.scriptFile?"file":"script",o=function(e,t){if("failure"===e)return"failure";var n=t.payload;return n.result&&n.result["status-code"]?"failure":"success"}(e,r);t.fire({type:M.U3,action:"consoleExecution",properties:{result:e,mode:i,executionStatus:o}})}})),f=a(p.Ko.async,(function(e){return function(t,n,r){var i=r.payload.params.scriptType,o=r.payload.params.preview,c=r.payload.params.builtinScript,a=n.getState(),u=l(a,c),s=y(a);t.fire({type:M.U3,action:"builtinExecution",properties:{scriptType:i,result:e,scriptName:c,preview:o,scriptSource:u,params:s}})}})),N=[new g.Success,new g.Failure,new f.Success,new f.Failure],m=function(){function e(){}return e.SelectFirstLevelTag=function(){function t(){this.matches=i.Mo.match}return t.prototype.execute=function(t,n,r){var i=r.payload.tag;e.track(t,n,{firstLevelTag:i})},t}(),e.SelectSecondLevelTag=function(){function t(){this.matches=i.ev.match}return t.prototype.execute=function(t,n,r){var i=r.payload.tag;e.track(t,n,{secondLevelTag:i})},t}(),e.SearchQueryAccepted=function(){function t(){this.matches=i.rK.match}return t.prototype.execute=function(t,n,r){e.track(t,n,{})},t}(),e.track=function(e,t,n){var r=s(t,n);r&&e.fire({type:M.U3,action:"search",properties:r})},e}(),D=[new m.SelectFirstLevelTag,new m.SelectSecondLevelTag,new m.SearchQueryAccepted],j=n(59060),T=n(7948),z=n(48432),x=function(e,t){return a(e,(function(e){return function(n,r,i){n.fire({type:M.U3,action:t,properties:{result:e}})}}))},w=x(j.Eg.async,"fileEditor/newFile"),E=x(z.IW.async,"fileEditor/loadFile"),O=x(z.$8.async,"fileEditor/saveFile"),I=x(j.SM.async,"fileEditor/moveFile"),S=x(z.aC.async,"fileEditor/deleteFile"),h=x(j.k3.async,"fileEditor/newFolder"),A=a(j.yr.async,(function(e){return function(e,t,n){c(n)&&e.fire({type:M.U3,action:"fileEditor/loadScriptRoots",properties:{rootsCount:Object.keys(n.payload.result.scriptRoots).length}})}})),L=a(T.k7.async,(function(e){return function(e,t,n){e.fire({type:M.U3,action:"fileEditor/selectScriptRoots",properties:{}})}})),v=[new w.Failure,new w.Success,new E.Failure,new E.Success,new O.Failure,new O.Success,new I.Failure,new I.Success,new S.Failure,new S.Success,new h.Failure,new h.Success,new A.Success,new L.Success],b=n(79949),k=n(99085),Q=function(){function e(){}return e.prototype.matches=function(e){return e&&"MAIL_HANDLER_LOADED"===e.type},e.prototype.execute=function(e,t,n){e.fire({type:M._Y,url:"secure/admin/IncomingMailServers.jspa"})},e}(),C=a(k.w.run.asyncActions,(function(e){return function(t,n,r){var i={result:e};if((0,b.KD)(r)){var o=r.payload;i.infoCount=(o.infoMessages||[]).length,i.warningCount=(o.warningMessages||[]).length,i.errorCount=(o.errorMessages||[]).length,i.emptyInbox=(o.emptyInbox||[]).length}t.fire({type:"track",action:"mailHandlerTest",properties:i})}})),P=[new Q,new C.Success,new C.Failure],U=n(46290),Y=function(){function e(){}return e.prototype.matches=function(e){return e.type===U.Tx.type},e.prototype.execute=function(e,t,n,r){r&&!r.hintsAndTips.expanded&&e.fire({type:M.U3,action:"hintsAndTips/expand",properties:{url:r.router.location.pathname}})},e}(),R=function(){function e(){}return e.prototype.matches=function(e){return e.type===U.fz.type},e.prototype.execute=function(e,t,n,r){var i,o;e.fire({type:M.U3,action:"hintsAndTips/next",properties:{url:null===(o=null===(i=null==r?void 0:r.router)||void 0===i?void 0:i.location)||void 0===o?void 0:o.pathname,hintId:n.payload.analyticsId}})},e}(),F=[new Y,new R],B=n(5290),G=n(30644),Z=n(83002),_=u(B.pJ.toggleAnalytics,(function(e){return function(t){"success"===e&&t.toggleEnabled()}})),q=u(B.pJ.toggleScriptEditPermission,(function(e){return function(t,n,r){t.fire({type:M.U3,action:"toggleScriptEditPermission",properties:{enabled:r.meta.arg.originalArgs.codeEditPermissionsEnabled,result:e}})}})),H=u(B.pJ.scriptEditPermissionsGroups,(function(e){return function(t,n,r){t.fire({type:M.U3,action:"scriptEditPermissionGroups",properties:{groupCount:(r.meta.arg.originalArgs.newGroups||[]).length,result:e}})}})),J=u(G.Z.toggleRestrictedProjectRepoAccess,(function(e){return function(t,n,r){t.fire({type:M.U3,action:"toggleRestrictedProjectRepoAccess",properties:{enabled:r.meta.arg.originalArgs.restrictProjectAndRepositoryAdminAccess,result:e}})}})),W=u(G.Z.updateRestrictedProjectRepoAccessExemptGroups,(function(e){return function(t,n,r){t.fire({type:M.U3,action:"updateRestrictedProjectRepoAccessGroups",properties:{groupCount:r.meta.arg.originalArgs.restrictProjectAndRepositoryAdminAccessExemptedGroups.length,result:e}})}})),V=u(B.pJ.toggleSwitchUser,(function(e){return function(t,n,r){t.fire({type:M.U3,action:"toggleSwitchUser",properties:{enabled:r.meta.arg.originalArgs.switchUserEnabled,result:e}})}})),X=u(Z.u.toggleSpaceAdminPermission,(function(e){return function(t,n,r){t.fire({type:M.U3,action:"setSpaceAdminPermission",properties:{enabled:r.meta.arg.originalArgs.spaceAdminPermissionsRestrictionsEnabled,result:e}})}})),K=[new _.Success,new q.Success,new q.Failure,new H.Success,new H.Failure,new J.Success,new J.Failure,new W.Success,new W.Failure,new V.Success,new V.Failure,new X.Success,new X.Failure],$=function(e){return a(e,(function(e){return function(t,n,r){var i=n.getState(),o=r.payload.params,c=o.scriptType,a=o.scriptName,u=function(e){switch(e.ui.submit.title){case"Add":return"add";case"Update":return"update";default:return}}(i),s=l(i,a),p=y(i);t.fire({type:M.U3,action:"builtinConfig",properties:{scriptType:c,result:e,scriptName:a,scriptSource:s,action:u,params:p}})}}))},ee=$(p.fY.async),te=$(p.kq.async),ne=function(e){return function(t){return function(n,r,i){var o=i.payload.params.scriptType,c=i.payload.params.scriptName,a=l(r.getState(),c);n.fire({type:M.U3,action:e,properties:{scriptType:o,result:t,scriptName:c,scriptSource:a}})}}},re=a(p.PT.async,ne("builtinDelete")),ie=a(p.qS.async,ne("builtinDisable")),oe=a(p.x$.async,ne("builtinEnable")),ce=[new ee.Success,new ee.Failure,new re.Success,new re.Failure,new ie.Success,new ie.Failure,new oe.Success,new oe.Failure,new te.Success,new te.Failure],ae=n(59482),ue=n(49268),se=n(90517),le=function(){return le=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e},le.apply(this,arguments)},Me=["NAVIGATION_SPOTLIGHT","MACRO_SPOTLIGHT"],pe=["MACROS_CONFIG","LISTENERS_CONFIG","JOBS_CONFIG","FRAGMENTS_CONFIG","REST_ENDPOINTS_CONFIG"],de=function(e){return function(t){return function(n,r,i){var o,c=i.payload.params;n.fire({type:M.U3,action:(o=c.moduleType,Me.includes(o)?"spotlightAction":pe.includes(o)?"configAction":"HOME_PAGE"===o?"homeVisitedAction":"modalAction"),properties:le(le({},c),{progress:(0,se.SB)(r.getState().onboarding),action:e,result:t})})}}},ye=a(ue.XF.async,de("dismiss")),ge=a(ue.o4.async,de("done")),fe=a(ue.gX.async,(function(e){return function(t,n,r){t.fire({type:M.U3,action:"homePageAction",properties:{homePageItem:"exploreCard",moduleType:r.payload.params,result:e}})}})),Ne=a(ue.i2.async,(function(e){return function(t,n,r){var i=r.payload.params;t.fire({type:M.U3,action:"exampleModalAction",properties:{action:"add",examples:i.examples.map((function(e){return e.description})),moduleType:i.moduleType,result:e}})}})),me=[new ye.Success,new ye.Failure,new ge.Success,new ge.Failure,new fe.Success,new fe.Failure,new Ne.Success,new Ne.Failure],De=function(){return De=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e},De.apply(this,arguments)},je=function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,i,o=n.call(e),c=[];try{for(;(void 0===t||t-- >0)&&!(r=o.next()).done;)c.push(r.value)}catch(e){i={error:e}}finally{try{r&&!r.done&&(n=o.return)&&n.call(o)}finally{if(i)throw i.error}}return c},Te=function(e,t,n){if(n||2===arguments.length)for(var r,i=0,o=t.length;i<o;i++)!r&&i in t||(r||(r=Array.prototype.slice.call(t,0,i)),r[i]=t[i]);return e.concat(r||Array.prototype.slice.call(t))},ze=function(){function e(){}return e.prototype.matches=function(e){return e&&e.type===r.nk},e.prototype.execute=function(e,t,n){if(n.payload.location.state&&"browsePage"===n.payload.location.state.source){var r=s(t,{});e.fire({type:M._Y,url:n.payload.location.pathname,properties:De(De({},r),{source:"browsePage",scriptSource:n.payload.location.state.scriptSource})})}else e.fire({type:M._Y,url:n.payload.location.pathname,properties:{scriptSource:(n.payload.location.state||{}).scriptSource}})},e}(),xe=function(){function e(){}return e.prototype.matches=function(e){return e&&e.type===M.yk},e.prototype.execute=function(e,t,n){e.fire({type:M.U3,action:n.action,properties:{context:n.value}})},e}(),we=function(){function e(){}return e.prototype.matches=function(e){return e&&e.type===M.s4},e.prototype.execute=function(e,t,n){e.fire({type:M.U3,action:n.action,properties:De({},n.value)})},e}(),Ee=function(){function e(){this.getParametersPerField=function(e){return Object.values(e.annotatedScriptParameters).map((function(e){return e.parameters?e.parameters.length:0}))},this.getScript=function(e){return"/console"===e.router.location.pathname?"/console":e.edit["canned-script"]}}return e.prototype.matches=function(e){return e&&e.type===ae.VN.async.done.type},e.prototype.execute=function(e,t,n,r){if(c(n)){var i=n.payload.result.compilationResult.parameters.metadata;if(r&&i&&0!==i.length){var o=n.payload.params.editorId,a=r.annotatedScriptParameters[o];if(!a||!a.parameters||a.parameters.length!==i.length){var u=t.getState();e.fire({type:M.U3,action:"annotatedScriptParameters",properties:{parametersPerField:this.getParametersPerField(u),scriptName:this.getScript(u)}})}}}},e}();const Oe=Te(Te(Te(Te(Te(Te(Te(Te(Te(Te([new ze,new xe],je(D),!1),je(N),!1),je(ce),!1),je(P),!1),je(K),!1),je(v),!1),[new Ee],!1),je(F),!1),[new we],!1),je(me),!1);var Ie=function(e){return{type:M.s4,action:"emptyStateAction",value:e}},Se=function(e){return{type:M.s4,action:"homePageAction",value:e}},he=function(e){return{type:M.s4,action:"spotlightAction",value:e}},Ae=function(e){return{type:M.s4,action:"modalAction",value:e}},Le=function(e){return{type:M.s4,action:"exampleModalAction",value:e}},ve=function(e){return{type:M.yk,action:"help",value:e}},be=function(e){return{type:M.yk,action:"community",value:e}},ke=function(e){return{type:M.yk,action:"postInstall",value:e}},Qe=function(){return{type:M.yk,action:"walkthroughVideoPlayed",value:""}};const Ce=function(e,t){return void 0===t&&(t=Oe),function(n){return function(r){return function(i){var o=n.getState();try{return r(i)}finally{!function(e,t,n,r,i){var o=e.find((function(e){return e.matches(r)}));o&&o.execute(t,n,r,i)}(t,e,n,i,o)}}}}}},61720:(e,t,n)=>{n.d(t,{a:()=>s,H:()=>l});var r=n(82633),i=n.n(r),o=n(61696),c=n.n(o),a=n(66661),u=function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,i,o=n.call(e),c=[];try{for(;(void 0===t||t-- >0)&&!(r=o.next()).done;)c.push(r.value)}catch(e){i={error:e}}finally{try{r&&!r.done&&(n=o.return)&&n.call(o)}finally{if(i)throw i.error}}return c},s=function(e,t){return"application/json"===t?i()(e,{indent:"  ",quoteProperties:!0}):"application/xml"===t?c()(e,{indent:2,newline:"\n"}):e},l=function(e){return e?"document-error"===e.type?[{startLineNumber:e.line,endLineNumber:e.line,startColumn:e.column,endColumn:e.column,message:(0,a.stripHtml)(e.errorMessages[0]),severity:a.MarkerSeverity.Error}]:function(e){return"builtin-script-errors"===e.type}(e)?[{startLineNumber:0,endLineNumber:0,startColumn:0,endColumn:0,message:Object.entries(e.errors).map((function(e){var t=u(e,2),n=t[0],r=t[1];return"".concat(n,": ").concat(r)})).join("\n")[0],severity:a.MarkerSeverity.Error}]:function(e){return"exception-error"===e.type}(e)?[{startLineNumber:0,endLineNumber:0,startColumn:0,endColumn:0,message:e.errorMessages.join("\n"),severity:a.MarkerSeverity.Error}]:void 0:[]}},5290:(e,t,n)=>{n.d(t,{TO:()=>d,c5:()=>s,pJ:()=>g});var r=n(5215),i=n(61720),o=n(40653),c=function(){return c=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e},c.apply(this,arguments)},a=function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,i,o=n.call(e),c=[];try{for(;(void 0===t||t-- >0)&&!(r=o.next()).done;)c.push(r.value)}catch(e){i={error:e}}finally{try{r&&!r.done&&(n=o.return)&&n.call(o)}finally{if(i)throw i.error}}return c},u=function(e,t,n){if(n||2===arguments.length)for(var r,i=0,o=t.length;i<o;i++)!r&&i in t||(r||(r=Array.prototype.slice.call(t,0,i)),r[i]=t[i]);return e.concat(r||Array.prototype.slice.call(t))},s="".concat(AJS.contextPath(),"/rest/scriptrunner/latest/settings/permissions"),l="".concat(AJS.contextPath(),"/rest/scriptrunner/latest/settings/analytics"),M="".concat(AJS.contextPath(),"/rest/scriptrunner/latest/settings/permissions/groups"),p="".concat(AJS.contextPath(),"/rest/scriptrunner/latest/storage-registry"),d=o.B.injectEndpoints({endpoints:function(e){return{permissions:e.query({query:function(){return s},providesTags:["SettingsPermissions"],onQueryStarted:function(e,t){t.queryFulfilled.catch((function(){return(0,r.x)(t.dispatch)}))}}),analytics:e.query({query:function(){return l},providesTags:["SettingsAnalytics"],onQueryStarted:function(e,t){t.queryFulfilled.catch((function(){return(0,r.x)(t.dispatch)}))}}),toggleSwitchUser:e.mutation({query:function(e){return{url:s,method:"POST",body:e}},invalidatesTags:function(e){return e&&["SettingsPermissions"]},onQueryStarted:function(e,t){var n=e.switchUserEnabled;return t.queryFulfilled.then((function(){return(0,r.v)(t.dispatch,"Switch user ".concat(n?"enabled":"disabled"," successfully."))})).catch((function(){return(0,r.x)(t.dispatch)}))}}),toggleInAppNotifications:e.mutation({query:function(e){return{url:s,method:"POST",body:e}},invalidatesTags:function(e){return e&&["SettingsPermissions"]},onQueryStarted:function(e,t){var n=e.inAppCommunications;return t.queryFulfilled.then((function(){return(0,r.v)(t.dispatch,"In-app notifications ".concat(n.security?"enabled":"disabled"," successfully."))})).catch((function(){return(0,r.x)(t.dispatch)}))}}),toggleScriptEditPermission:e.mutation({query:function(e){return{url:s,method:"POST",body:e}},invalidatesTags:function(e){return e&&["SettingsPermissions"]},onQueryStarted:function(e,t){var n=e.codeEditPermissionsEnabled;return t.queryFulfilled.then((function(){(0,r.v)(t.dispatch,"Script Edit Permissions ".concat(n?"enabled":"disabled"," successfully."))})).catch((function(){(0,r.x)(t.dispatch)}))}}),scriptEditPermissionsGroups:e.mutation({query:function(e){var t=e.newGroups;return{url:M,method:"POST",body:{adminGroupsWithCodeEditPermissions:t}}},invalidatesTags:function(e,t){return!t&&["SettingsPermissions"]},onQueryStarted:function(e,t){return t.queryFulfilled.then((function(){(0,r.v)(t.dispatch,"Groups with Script Edit Permissions updated successfully.")})).catch((function(){return(0,r.x)(t.dispatch)}))}}),toggleAnalytics:e.mutation({query:function(e){var t=e.newState;return{url:l,method:"PUT",body:{adaptavistAnalyticsEnabled:t}}},invalidatesTags:function(e){return e&&["SettingsAnalytics"]},onQueryStarted:function(e,t){return t.queryFulfilled.then((function(){(0,r.v)(t.dispatch,"Analytics ".concat(e.newState?"enabled":"disabled"," successfully."))})).catch((function(){(0,r.x)(t.dispatch)}))}}),getStorage:e.query({query:function(){return p}}),getStorageTabConfig:e.query({query:function(e){return{url:"".concat(p,"/").concat(e.id),headers:{"Content-Type":e.contentType},responseHandler:function(e){return e.text()}}},transformResponse:function(e,t){return{result:(0,i.a)(e,t.request.headers.get("Content-type")),id:(n=t.request.url,r=n.indexOf(p)+"".concat(p,"/").length,n.substring(r))};var n,r},providesTags:function(e,t,n){return e&&[{type:"SettingsGetStorageTab",id:n.id}]}}),updateStorage:e.mutation({query:function(e){var t=e.info,n=e.newContent;return{url:"".concat(p,"/").concat(t.id),method:"PUT",body:n,headers:{"Content-Type":"text/plain;charset=UTF-8"},responseHandler:function(e){return"application/json"===t.contentType?e.json():e.text()}}},transformResponse:function(){return[]},invalidatesTags:function(e,t,n){return e&&[{type:"SettingsGetStorageTab",id:n.info.id}]}})}}}),y=function(e){return{type:"SettingsGetStorageTab",id:e.id}},g={toggleScriptEditPermission:d.endpoints.toggleScriptEditPermission,scriptEditPermissionsGroups:d.endpoints.scriptEditPermissionsGroups,toggleSwitchUser:d.endpoints.toggleSwitchUser,toggleAnalytics:d.endpoints.toggleAnalytics,toggleInAppNotifications:d.endpoints.toggleInAppNotifications,usePermissionsQuery:d.usePermissionsQuery,useAnalyticsQuery:d.useAnalyticsQuery,useToggleScriptEditPermissionMutation:d.useToggleScriptEditPermissionMutation,useScriptEditPermissionsGroupsMutation:d.useScriptEditPermissionsGroupsMutation,useToggleSwitchUserMutation:d.useToggleSwitchUserMutation,useToggleInAppNotificationsMutation:d.useToggleInAppNotificationsMutation,useToggleAnalyticsMutation:d.useToggleAnalyticsMutation,useGetStorageQuery:d.useGetStorageQuery,useGetStorageTabConfigQuery:d.useGetStorageTabConfigQuery,useUpdateStorageMutation:d.useUpdateStorageMutation,getStorageTabConfig:c(c({},d.endpoints.getStorageTabConfig),{invalidate:function(e){return d.util.invalidateTags(u([],a(e.map(y)),!1))}})}},30644:(e,t,n)=>{n.d(t,{Z:()=>c});var r=n(5215),i=n(5290),o=i.TO.injectEndpoints({endpoints:function(e){return{toggleRestrictedProjectRepoAccess:e.mutation({query:function(e){return{url:i.c5,method:"POST",body:e}},invalidatesTags:function(e){return e&&["SettingsPermissions"]},onQueryStarted:function(e,t){var n=e.restrictProjectAndRepositoryAdminAccess;return t.queryFulfilled.then((function(){return(0,r.v)(t.dispatch,"Restricted project and repository access ".concat(n?"enabled":"disabled"," successfully."))})).catch((function(){return(0,r.x)(t.dispatch)}))}}),updateRestrictedProjectRepoAccessExemptGroups:e.mutation({query:function(e){return{url:i.c5,method:"POST",body:e}},invalidatesTags:function(e){return e&&["SettingsPermissions"]},onQueryStarted:function(e,t){return t.queryFulfilled.then((function(){return(0,r.v)(t.dispatch,"Restricted project and repository access groups updated successfully.")})).catch((function(){return(0,r.x)(t.dispatch)}))}})}}}),c={toggleRestrictedProjectRepoAccess:o.endpoints.toggleRestrictedProjectRepoAccess,updateRestrictedProjectRepoAccessExemptGroups:o.endpoints.updateRestrictedProjectRepoAccessExemptGroups,useToggleRestrictedProjectRepoAccessMutation:o.useToggleRestrictedProjectRepoAccessMutation,useUpdateRestrictedProjectRepoAccessExemptGroupsMutation:o.useUpdateRestrictedProjectRepoAccessExemptGroupsMutation}},83002:(e,t,n)=>{n.d(t,{u:()=>a});var r=n(5215),i=n(5290),o="".concat(AJS.contextPath(),"/rest/scriptrunner-confluence/latest/space_administrator/canned/available"),c=i.TO.injectEndpoints({endpoints:function(e){return{toggleSpaceAdminPermission:e.mutation({query:function(e){return{url:i.c5,method:"POST",body:e}},invalidatesTags:function(e){return e&&["SettingsPermissions"]},onQueryStarted:function(e,t){var n=e.spaceAdminPermissionsRestrictionsEnabled;return t.queryFulfilled.then((function(){return(0,r.v)(t.dispatch,"Space admin built-in script permissions ".concat(n?"enabled":"disabled"," successfully."))})).catch((function(){return(0,r.x)(t.dispatch)}))}}),updateSpaceAdminPermission:e.mutation({query:function(e){return{url:i.c5,method:"POST",body:e}},invalidatesTags:function(e,t){return!t&&["SettingsPermissions"]},onQueryStarted:function(e,t){return t.queryFulfilled.then((function(){return(0,r.v)(t.dispatch,"Space admin built-in script permissions updated successfully.")})).catch((function(){return(0,r.x)(t.dispatch)}))}}),loadBuiltInScripts:e.query({query:function(){return o},providesTags:["SettingsPermissions"],onQueryStarted:function(e,t){t.queryFulfilled.catch((function(){return(0,r.x)(t.dispatch)}))}})}}}),a={toggleSpaceAdminPermission:c.endpoints.toggleSpaceAdminPermission,useLoadBuiltInScriptsQuery:c.useLoadBuiltInScriptsQuery,useToggleSpaceAdminPermissionMutation:c.useToggleSpaceAdminPermissionMutation,useUpdateSpaceAdminPermissionMutation:c.useUpdateSpaceAdminPermissionMutation}},5215:(e,t,n)=>{n.d(t,{v:()=>o,x:()=>i});var r=n(77937),i=function(e){e((0,r.mB)({body:"An error occurred. Please review server logs.",type:"error",title:"Server Error",close:"manual"}))},o=function(e,t){e((0,r.mB)({body:t,type:"success",title:"Settings updated",close:"auto"}))}},90517:(e,t,n)=>{n.d(t,{wT:()=>Ue,SB:()=>ke,T7:()=>be});var r,i,o,c,a,u,s,l,M=n(63844),p=n(62975),d=n(18390),y=n(52824),g=function(e,t){return Object.defineProperty?Object.defineProperty(e,"raw",{value:t}):e.raw=t,e},f=function(e){return M.createElement(N,null,M.createElement(m,{src:y}),M.createElement(D,null,e.title),M.createElement(j,null,e.desc))},N=d.Z.header(r||(r=g(["\n    margin-top: 36px;\n"],["\n    margin-top: 36px;\n"]))),m=d.Z.img(i||(i=g(["\n    max-width: 50px;\n    max-height: 50px;\n"],["\n    max-width: 50px;\n    max-height: 50px;\n"]))),D=d.Z.h1(o||(o=g(["\n    margin: 15px 0 6px;\n    font-size: 28px;\n"],["\n    margin: 15px 0 6px;\n    font-size: 28px;\n"]))),j=d.Z.p(c||(c=g(["\n    margin: 0;\n"],["\n    margin: 0;\n"]))),T=n(72142),z=n(71530),x=n(21306),w=function(e,t){return Object.defineProperty?Object.defineProperty(e,"raw",{value:t}):e.raw=t,e},E=function(e){return M.createElement(O,{"data-qa":e["data-qa"]},M.createElement("h2",null,e.heading),e.children)},O=d.Z.section(a||(a=w(["\n    margin-top: 36px;\n"],["\n    margin-top: 36px;\n"]))),I=n(93738),S=n(88109),h=function(e){var t=e.progress,n={datasets:[{data:[t,100-t],text:t+"%",backgroundColor:[I.AX,"#DEE0E6"],hoverBackgroundColor:[I.AX,"#DEE0E6"],borderWidth:0}]},r=[{beforeDraw:function(e){var t=e.width,n=e.height,r=e.ctx;r.restore();var i=(22/14).toFixed(2);r.font=i+"em sans-serif",r.textBaseline="middle";var o=e.config.data.datasets[0].text,c=Math.round((t-r.measureText(o).width)/2),a=n/2+6;r.fillText(o,c,a),r.save()}}];return M.createElement(S.$I,{type:"doughnut",data:n,"data-qa":t,options:{legend:{display:!1},title:{display:!1},plugins:{tooltip:{enabled:!1}}},plugins:r,"aria-label":"Feature discovery at "+t+"%"})},A={beginner:{title:"ScriptRunner Feature Discovery - Let's get you started",message:"To increase your progress score, start exploring the features highlighted below. These feature suggestions have been personalised for each user so this list is just for you",link:null},advanced:{title:"ScriptRunner Feature Discovery - You're on your way!",message:"You've made a great start on diving into your suggested features, keep going on your ScriptRunner discovery journey! Even if you don't have a use case right now, it's good to know what's available to you as part of your ScriptRunner toolkit",link:null},completed:{title:"ScriptRunner Feature Discovery - Congratulations!",message:"You did it! You've completed your ScriptRunner feature discovery journey. Make sure to check back here: any new suggestions will appear below as ScriptRunner evolves. Got a question or want to give us feedback? Let us know ",link:"https://productsupport.adaptavist.com/servicedesk/customer/portal/13"}},L=function(e){switch(e>=0){case e<=49:return A.beginner;case e<=99:return A.advanced;case 100===e:return A.completed}},v=function(e){var t=e.progress;return M.createElement(M.Fragment,null,M.createElement("h3",null,L(t).title),M.createElement("p",null,L(t).message,"string"==typeof L(t).link?M.createElement("a",{href:L(t).link,target:"_blank"},"here"):null,"."))},b=function(e,t){return Object.defineProperty?Object.defineProperty(e,"raw",{value:t}):e.raw=t,e},k=function(e){var t=e.completion;return M.createElement(Q,null,M.createElement(C,null,M.createElement(h,{progress:t})),M.createElement(P,null,M.createElement(v,{progress:t})))},Q=d.Z.div(u||(u=b(["\n    background-color: #f5f6f8;\n    border-radius: 3px;\n    display: flex;\n    flex-direction: row;\n    align-items: center;\n    margin-top: 18px;\n"],["\n    background-color: #f5f6f8;\n    border-radius: 3px;\n    display: flex;\n    flex-direction: row;\n    align-items: center;\n    margin-top: 18px;\n"]))),C=d.Z.div(s||(s=b(["\n    position: relative;\n    width: 150px;\n    margin: 36px 90px 36px 36px;\n"],["\n    position: relative;\n    width: 150px;\n    margin: 36px 90px 36px 36px;\n"]))),P=d.Z.div(l||(l=b(["\n    margin: 36px 36px 24px 0;\n"],["\n    margin: 36px 36px 24px 0;\n"]))),U=n(64077),Y=n(46275),R=function(e,t){return Object.defineProperty?Object.defineProperty(e,"raw",{value:t}):e.raw=t,e},F=function(e){var t=e.subtitle,n=e.title,r=e.link,i=e.modal,o=e.onLinkClick;return M.createElement(B,{to:r+"?referrer=home",onClick:function(){return o(i)}},M.createElement(te,null,t),M.createElement(ee,null,n),M.createElement(ne,null,"Get Started ",M.createElement(U.Z,{label:"Go to link"})))},B=(0,d.Z)(Y.rU)(G||(G=R(["\n    display: flex;\n    flex-direction: column;\n    justify-content: space-between;\n    padding: 24px 18px;\n    margin-bottom: 18px;\n    border-radius: 2px;\n    background: #f5f6f8;\n    outline: none !important;\n    &:hover {\n        text-decoration: none;\n    }\n"],["\n    display: flex;\n    flex-direction: column;\n    justify-content: space-between;\n    padding: 24px 18px;\n    margin-bottom: 18px;\n    border-radius: 2px;\n    background: #f5f6f8;\n    outline: none !important;\n    &:hover {\n        text-decoration: none;\n    }\n"])));B.defaultProps={className:"router-link"};var G,Z,_,q,H,J,W,V,X,K,$,ee=d.Z.h3(Z||(Z=R(["\n    margin: 9px 0 12px;\n    font-size: 18px;\n    color: ",";\n"],["\n    margin: 9px 0 12px;\n    font-size: 18px;\n    color: ",";\n"])),I.q2),te=d.Z.h4(_||(_=R(["\n    margin: 0;\n    font-size: 12px;\n    font-weight: normal;\n    color: ",";\n"],["\n    margin: 0;\n    font-size: 12px;\n    font-weight: normal;\n    color: ",";\n"])),I.iw),ne=d.Z.span(q||(q=R(["\n    font-size: 14px;\n    display: flex;\n    align-items: center;\n    .router-link:hover & {HomeHub.test.tsx\n        text-decoration: underline !important;\n    }\n"],["\n    font-size: 14px;\n    display: flex;\n    align-items: center;\n    .router-link:hover & {HomeHub.test.tsx\n        text-decoration: underline !important;\n    }\n"]))),re=n(1026),ie=n(49268),oe=n(30080),ce=n(74729),ae=function(e,t){return Object.defineProperty?Object.defineProperty(e,"raw",{value:t}):e.raw=t,e},ue=function(e,t,n,r){return new(n||(n=Promise))((function(i,o){function c(e){try{u(r.next(e))}catch(e){o(e)}}function a(e){try{u(r.throw(e))}catch(e){o(e)}}function u(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(c,a)}u((r=r.apply(e,t||[])).next())}))},se=function(e,t){var n,r,i,o,c={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return o={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function a(o){return function(a){return function(o){if(n)throw new TypeError("Generator is already executing.");for(;c;)try{if(n=1,r&&(i=2&o[0]?r.return:o[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,o[1])).done)return i;switch(r=0,i&&(o=[2&o[0],i.value]),o[0]){case 0:case 1:i=o;break;case 4:return c.label++,{value:o[1],done:!1};case 5:c.label++,r=o[1],o=[0];continue;case 7:o=c.ops.pop(),c.trys.pop();continue;default:if(!(i=c.trys,(i=i.length>0&&i[i.length-1])||6!==o[0]&&2!==o[0])){c=0;continue}if(3===o[0]&&(!i||o[1]>i[0]&&o[1]<i[3])){c.label=o[1];break}if(6===o[0]&&c.label<i[1]){c.label=i[1],i=o;break}if(i&&c.label<i[2]){c.label=i[2],c.ops.push(o);break}i[2]&&c.ops.pop(),c.trys.pop();continue}o=t.call(e,c)}catch(e){o=[6,e],r=0}finally{n=i=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,a])}}},le=function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,i,o=n.call(e),c=[];try{for(;(void 0===t||t-- >0)&&!(r=o.next()).done;)c.push(r.value)}catch(e){i={error:e}}finally{try{r&&!r.done&&(n=o.return)&&n.call(o)}finally{if(i)throw i.error}}return c},Me={LISTENERS_CONFIG:{name:"Listener",url:"".concat(AJS.contextPath(),"/plugins/servlet/scriptrunner/admin/listeners/")},JOBS_CONFIG:{name:"Job",url:"".concat(AJS.contextPath(),"/plugins/servlet/scriptrunner/admin/jobs")},FRAGMENTS_CONFIG:{name:"Fragment",url:"".concat(AJS.contextPath(),"/plugins/servlet/scriptrunner/admin/fragments")},MACROS_CONFIG:{name:"Custom Macro",url:"".concat(AJS.contextPath(),"/plugins/servlet/scriptrunner/admin/macros")},REST_ENDPOINTS_CONFIG:{name:"REST Endpoint",url:"".concat(AJS.contextPath(),"/plugins/servlet/scriptrunner/admin/restendpoints/")}},pe=function(e){var t=e.trackHomeAction,n=e.setModuleCompleted,r=e.onboarding,i=le((0,M.useState)([]),2),o=i[0],c=i[1],a=le((0,M.useState)(!0),2),u=a[0],s=a[1],l=le((0,M.useState)(!1),2),p=l[0],d=l[1];return(0,M.useEffect)((function(){ue(void 0,void 0,void 0,(function(){var e;return se(this,(function(t){switch(t.label){case 0:return t.trys.push([0,2,3,4]),[4,ye()];case 1:return e=t.sent(),c(e),function(e,t,n){e.forEach((function(e){var r=e.configType,i=e.count;"COMPLETED"!==t[r]&&i>0&&n({moduleType:r})}))}(e,r,n),[3,4];case 2:return t.sent(),d(!0),[3,4];case 3:return s(!1),[7];case 4:return[2]}}))}))}),[]),M.createElement(E,{heading:"Active Scripts in ScriptRunner","data-qa":u?"loading":"loaded"},M.createElement(de,{activeScriptsConfig:o,loading:u,error:p,trackHomeAction:t}))},de=function(e){var t=e.activeScriptsConfig,n=e.error,r=e.trackHomeAction;return n?M.createElement(oe.Bc,{message:"Error! There was a problem loading the active scripts."}):M.createElement(ge,null,t.map((function(e){return M.createElement(fe,{className:e.configType,key:e.configType,"data-qa":"active-scripts-type"},M.createElement("h1",null,e.count),M.createElement("a",{target:"_blank",href:Me[e.configType].url,onClick:function(){return r({homePageItem:"activeScript",moduleType:e.configType})}},Me[e.configType].name,1!==e.count?"s":""),M.createElement("br",null),"Active")})))},ye=function(){return ue(void 0,void 0,Promise,(function(){var e,t,n;return se(this,(function(r){switch(r.label){case 0:return[4,(0,ce.wrappedFetch)("".concat(AJS.contextPath(),"/rest/scriptrunner/1.0/countfeatures"))];case 1:if(e=r.sent(),t=e.result,n=e.error,t)return[2,t];throw Error(n)}}))}))},ge=(0,d.Z)(Q)(H||(H=ae(["\n    justify-content: space-between;\n    align-items: baseline;\n"],["\n    justify-content: space-between;\n    align-items: baseline;\n"]))),fe=d.Z.div(J||(J=ae(["\n    padding: 40px;\n"],["\n    padding: 40px;\n"]))),Ne=function(e,t){return Object.defineProperty?Object.defineProperty(e,"raw",{value:t}):e.raw=t,e},me=function(e){var t=e.resourceLinks,n=e.trackHomeAction;return M.createElement(De,null,t.map((function(e){return M.createElement(je,{target:"_blank",href:e.url,key:e.url,onClick:function(){return n({homePageItem:e.resourceType,url:e.url})}},e.text)})))},De=(0,d.Z)(Q)(W||(W=Ne(["\n    background-color: unset;\n    flex-wrap: wrap;\n"],["\n    background-color: unset;\n    flex-wrap: wrap;\n"]))),je=d.Z.a(V||(V=Ne(["\n    padding-bottom: 10px;\n    flex: 0 0 33.333333%;\n"],["\n    padding-bottom: 10px;\n    flex: 0 0 33.333333%;\n"]))),Te=n(79569),ze=n(98492),xe=n.n(ze),we=n(66638),Ee=function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,i,o=n.call(e),c=[];try{for(;(void 0===t||t-- >0)&&!(r=o.next()).done;)c.push(r.value)}catch(e){i={error:e}}finally{try{r&&!r.done&&(n=o.return)&&n.call(o)}finally{if(i)throw i.error}}return c},Oe=function(e,t,n){if(n||2===arguments.length)for(var r,i=0,o=t.length;i<o;i++)!r&&i in t||(r||(r=Array.prototype.slice.call(t,0,i)),r[i]=t[i]);return e.concat(r||Array.prototype.slice.call(t))},Ie=[{modal:"CONSOLE_MODAL",subtitle:"Console",title:"Build, run, and try scripts with ease",link:"/console"},{modal:"BUILTIN_SCRIPTS_MODAL",subtitle:"Built-in Scripts",title:"Discover pre-built scripts for instant use",link:"/builtin"},{modal:"JOBS_MODAL",subtitle:"Jobs",title:"Schedule automated tasks to save you time",link:"/jobs"},{modal:"LISTENERS_MODAL",subtitle:"Listeners",title:"Automate scripts to respond to events",link:"/listeners"},{modal:"FRAGMENTS_MODAL",subtitle:"Fragments",title:"Customise the user interface and experience",link:"/fragments"},{modal:"RESOURCES_MODAL",subtitle:"Resources",title:"Connect databases to enable more powerful scripts",link:"/resources"},{modal:"REST_ENDPOINTS_MODAL",subtitle:"REST Endpoints",title:"Expose additional data to expand script possibilities",link:"/restendpoints"}],Se=(0,we.S)({confluence:Oe(Oe([],Ee(Ie),!1),[{modal:"MACROS_MODAL",subtitle:"Macros",title:"Enhance content experience for your end users",link:"/macros"}],!1),default:Ie}),he=function(e,t){return Object.defineProperty?Object.defineProperty(e,"raw",{value:t}):e.raw=t,e},Ae=function(){return Ae=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e},Ae.apply(this,arguments)},Le=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var i=0;for(r=Object.getOwnPropertySymbols(e);i<r.length;i++)t.indexOf(r[i])<0&&Object.prototype.propertyIsEnumerable.call(e,r[i])&&(n[r[i]]=e[r[i]])}return n},ve=function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,i,o=n.call(e),c=[];try{for(;(void 0===t||t-- >0)&&!(r=o.next()).done;)c.push(r.value)}catch(e){i={error:e}}finally{try{r&&!r.done&&(n=o.return)&&n.call(o)}finally{if(i)throw i.error}}return c},be=function(e,t){return Object.fromEntries(Object.entries(e).filter(t))},ke=function(e){var t=["REST_ENDPOINTS_CONFIG","MACROS_CONFIG"],n=(0,re.tw)().filter((function(e){return!t.includes(e)})).length,r=be(e,(function(e){var n=ve(e,2),r=n[0];return"COMPLETED"===n[1]&&!t.includes(r)}));return Math.round(Object.keys(r).length/n*100)},Qe=function(e){var t=e.onClick,n=Le(e,["onClick"]);return M.createElement("button",Ae({onClick:t,style:{background:0,border:0,cursor:"pointer",position:"fixed",right:"40px"}},n))},Ce=function(e){var t=e.onboarding,n=e.name,r=e.onClose,i=e.onClick,o=e.resources,c=e.trackHomeAction,a=e.onboardingProgress,u=e.setModuleCompleted,s=ve((0,M.useState)(!0),2),l=s[0],p=s[1];(0,M.useEffect)((function(){"UNVISITED"===t.HOME_PAGE&&u({moduleType:"HOME_PAGE"})}),[]),void 0===n&&(n=document.querySelector('meta[name="ajs-current-user-fullname"]').getAttribute("content"));var d=Se.filter((function(e){return function(e){var t,n,r=be(e,(function(e){var t=Ee(e,2);return t[0],"COMPLETED"!==t[1]})),i=Object.keys(r),o=(0,re.tw)(),c=Object.keys(e),a=o.filter((function(e){return!c.includes(e)})),u=i.concat(a),s=["MACROS_MODAL","MACRO_SPOTLIGHT"];return t=s,n=be(e,(function(e){var t=Ee(e,2);return t[0],"COMPLETED"===t[1]})),t.every((function(e){return e in n}))?u:u.concat(s)}(t).includes(e.modal)}));return xe().defaultStyles={overlay:{overscrollBehavior:"none",position:"fixed",top:0,left:0,right:0,bottom:0,overflowY:"auto",zIndex:999},content:{minHeight:"100%",maxWidth:"100%",minWidth:"min-content",background:"#fff",outline:"none",padding:"20px"}},M.createElement(xe(),{id:"home-modal",ariaHideApp:!1,onRequestClose:function(){p(!1),r()},isOpen:l},M.createElement(Re,null,M.createElement(Fe,null,M.createElement(f,{title:"".concat("Welcome to your ScriptRunner Home",", ").concat(n),desc:"This is a personalised hub, just for you. From here, you can explore your recommended features, get an overview of your active scripts and grow your confidence with ScriptRunner."}),M.createElement(E,{heading:"Feature Discovery"},M.createElement(k,{completion:a})),d.length>0?M.createElement(E,{"data-qa":"home-explore",heading:"Features You Haven't Explored"},M.createElement(Ye,null,d.map((function(e){return M.createElement(F,{key:e.modal,subtitle:e.subtitle,title:e.title,link:e.link,modal:e.modal,onLinkClick:"MACROS_MODAL"!==e.modal||t.MACROS_MODAL?function(e){return i(e)}:function(){return null}})})))):null,M.createElement(pe,{onboarding:t,trackHomeAction:c,setModuleCompleted:u}),M.createElement(E,{heading:"Additional Resources"},M.createElement(me,{resourceLinks:o,trackHomeAction:c}))),M.createElement(Qe,{onClick:function(){p(!1),r()}},M.createElement(x.Z,{size:"medium",label:"close-button"}))))},Pe=(0,T.$j)((function(e){return{onboarding:e.onboarding,onboardingProgress:ke(e.onboarding)}}),(function(e){return{onClose:function(){location.pathname.includes("postInstall")||!history.state?e((0,z.VF)("/browse")):e((0,z.Hm)())},onClick:function(t){return e((0,ie.gX)(t))},trackHomeAction:function(t){return e((0,Te.Ui)(t))},setModuleCompleted:function(t){return e((0,ie.o4)(t))}}}))(Ce),Ue=function(e){var t=e.resources;return M.createElement("div",null,M.createElement(p.AW,{path:"/home",exact:!0,component:function(){return M.createElement(Pe,{resources:t})}}),M.createElement(p.AW,{path:"/postInstall",exact:!0,component:function(){return M.createElement(Pe,{resources:t})}}))},Ye=d.Z.div(X||(X=he(["\n    display: grid;\n    grid-template-columns: 1fr 1fr 1fr;\n    grid-column-gap: 25px;\n    padding-top: 18px;\n    justify-content: space-between;\n"],["\n    display: grid;\n    grid-template-columns: 1fr 1fr 1fr;\n    grid-column-gap: 25px;\n    padding-top: 18px;\n    justify-content: space-between;\n"]))),Re=d.Z.div(K||(K=he(["\n    display: grid;\n    grid-template-columns: repeat(13, 1fr);\n    padding-top: 20px;\n    overflow: auto;\n"],["\n    display: grid;\n    grid-template-columns: repeat(13, 1fr);\n    padding-top: 20px;\n    overflow: auto;\n"]))),Fe=d.Z.div($||($=he(["\n    min-width: min-content;\n    grid-column: 4/11;\n"],["\n    min-width: min-content;\n    grid-column: 4/11;\n"])))},99085:(e,t,n)=>{n.d(t,{w:()=>l});var r,i=n(36894),o=n(40653),c=function(e,t){var n=function(e,t){return{}};return n.type="".concat(o.B.reducerPath,"/executeQuery/").concat(t),n.match=function(t){return e(t)},n},a=function(){return a=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e},a.apply(this,arguments)},u="".concat(AJS.contextPath(),"/rest/scriptrunner-jira/latest/mail-handler/run"),s=o.B.injectEndpoints({endpoints:function(e){return{run:e.mutation({queryFn:function(e,t,n,r){var o=t.getState,c=a(a({},i.Z(["bulk","forwardEmail","catchEmail","stripQuote","createUser"],o())),{scriptText:o().edit.FIELD_INLINE_SCRIPT,scriptPath:o().edit.UPDATE_BUILTIN_SCRIPT_PARAM});return r({url:u,method:"POST",body:c})}})}}}),l={run:a(a({},s.endpoints.run),{asyncActions:(r=s.endpoints.run,{type:"".concat(o.B.reducerPath,"/executeQuery"),done:c(r.matchFulfilled,"fulfilled"),failed:c(r.matchRejected,"rejected"),started:c(r.matchPending,"pending")})}),useMailHandlerRunQuery:s.useRunMutation}},52824:e=>{e.exports="data:image/svg+xml;base64,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"}}]);