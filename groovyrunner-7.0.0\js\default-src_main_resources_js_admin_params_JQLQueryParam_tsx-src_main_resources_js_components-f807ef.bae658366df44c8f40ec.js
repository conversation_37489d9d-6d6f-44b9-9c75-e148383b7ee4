"use strict";(self.webpackJsonpScriptRunner=self.webpackJsonpScriptRunner||[]).push([["default-src_main_resources_js_admin_params_JQLQueryParam_tsx-src_main_resources_js_components-f807ef"],{92417:(t,e,n)=>{n.d(e,{Z:()=>a});var r=n(63844),o=n(58844);const a=function(t){var e=t.param,n=t.id,a=t.errorMessage,i=t.mandatory,s=t.style,l=t.children,u=e.hidden?{display:"none"}:{},c=r.Children.toArray(t.children).some((function(t){return t.props&&!!t.props.editorId}));return r.createElement("div",{id:n},r.createElement("div",{className:"field-group",style:u},r.createElement("label",{htmlFor:e.name,style:s},t.customLabelComponent?t.customLabelComponent:r.createElement(r.Fragment,null,e.label,i&&r.createElement("span",{className:"aui-icon icon-required"}))),l,a&&r.createElement("div",{className:"error",dangerouslySetInnerHTML:{__html:(0,o.sanitize)(a)}}),!c&&r.createElement("div",{className:"description long-field",dangerouslySetInnerHTML:{__html:(0,o.sanitize)(e.description)}})))}},10203:(t,e,n)=>{n.d(e,{Z:()=>R});var r,o=n(63844),a=n(92417),i=n(62224),s=n(37360),l=n(14849),u=n(93738),c=n(46648),p=n(59451),f=n(82031),d=n(35541),h=n(26841),m=n(88797),v=n.n(m),y=n(149),g=n.n(y),b=n(39507),w=n(58844),I=n(74729),S=(r=function(t,e){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])},r(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}),O=function(t,e,n,r){return new(n||(n=Promise))((function(o,a){function i(t){try{l(r.next(t))}catch(t){a(t)}}function s(t){try{l(r.throw(t))}catch(t){a(t)}}function l(t){var e;t.done?o(t.value):(e=t.value,e instanceof n?e:new n((function(t){t(e)}))).then(i,s)}l((r=r.apply(t,e||[])).next())}))},x=function(t,e){var n,r,o,a,i={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return a={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function s(a){return function(s){return function(a){if(n)throw new TypeError("Generator is already executing.");for(;i;)try{if(n=1,r&&(o=2&a[0]?r.return:a[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,a[1])).done)return o;switch(r=0,o&&(a=[2&a[0],o.value]),a[0]){case 0:case 1:o=a;break;case 4:return i.label++,{value:a[1],done:!1};case 5:i.label++,r=a[1],a=[0];continue;case 7:a=i.ops.pop(),i.trys.pop();continue;default:if(!(o=i.trys,(o=o.length>0&&o[o.length-1])||6!==a[0]&&2!==a[0])){i=0;continue}if(3===a[0]&&(!o||a[1]>o[0]&&a[1]<o[3])){i.label=a[1];break}if(6===a[0]&&i.label<o[1]){i.label=o[1],o=a;break}if(o&&i.label<o[2]){i.label=o[2],i.ops.push(a);break}o[2]&&i.ops.pop(),i.trys.pop();continue}a=e.call(t,i)}catch(t){a=[6,t],r=0}finally{n=o=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,s])}}},C=function(t,e){var n="function"==typeof Symbol&&t[Symbol.iterator];if(!n)return t;var r,o,a=n.call(t),i=[];try{for(;(void 0===e||e-- >0)&&!(r=a.next()).done;)i.push(r.value)}catch(t){o={error:t}}finally{try{r&&!r.done&&(n=a.return)&&n.call(a)}finally{if(o)throw o.error}}return i},E=function(t,e,n){if(n||2===arguments.length)for(var r,o=0,a=e.length;o<a;o++)!r&&o in e||(r||(r=Array.prototype.slice.call(e,0,o)),r[o]=e[o]);return t.concat(r||Array.prototype.slice.call(e))};function j(){return o.createElement(h.Z,{size:"medium",primaryColor:u.$H,label:"JQL valid"})}function q(){return o.createElement(d.Z,{size:"medium",primaryColor:u.ak,label:"JQL invalid"})}var _=AJS.$;const A=function(t){function e(e){var n=t.call(this,e)||this;return n.setSuggestions=function(t){n.setState({suggestions:E([],C(t),!1)},n.registerOnClickHandlerOnDropListItems)},n.updateHitCount=function(t){n.countHits(t)},n.countHits=g()((function(t){return O(n,void 0,void 0,(function(){var e;return x(this,(function(n){switch(n.label){case 0:return[4,(0,I.wrappedFetch)("".concat(AJS.contextPath(),"/rest/api/2/search?").concat((0,b.F)({jql:t,maxResults:0})))];case 1:return e=n.sent(),this.setState({hitsCount:e.result?e.result.total:null}),[2]}}))}))}),1e3),n.renderItems=function(){return n.state.suggestions.map((function(t,e){return o.createElement(c.Z,{onActivate:function(){return n.handleItemSelect(t)},isFocused:e===n.state.focusedItemIndex,key:t.text,type:"option"},o.createElement((function(){return o.createElement("span",{dangerouslySetInnerHTML:{__html:(0,w.sanitize)(t.text)}})}),null))}))},n.handleKeyboardInteractions=function(t){var e=n.state.isOpen;switch(t.key){case"ArrowDown":t.preventDefault(),e||n.onOpenChange({event:t,isOpen:!0}),n.focusNextItem();break;case"ArrowUp":t.preventDefault(),e&&n.focusPreviousItem();break;case"Enter":e&&(t.preventDefault(),void 0!==n.state.focusedItemIndex?n.handleItemSelect(n.state.suggestions[n.state.focusedItemIndex]):n.onOpenChange({event:t,isOpen:!1}));break;case"Tab":n.onOpenChange({event:t,isOpen:!1});break;default:return!1}},n.registerOnClickHandlerOnDropListItems=function(){n.constructorData.input.closest("div.field-group").find('span[data-role="droplistItem"]').each((function(t,e){e.onclick=function(){var e=n.state.suggestions[t];n.handleItemSelect(e)}}))},n.onOpenChange=function(t){n.setState({focusedItemIndex:void 0,isOpen:t.isOpen},n.registerOnClickHandlerOnDropListItems)},n.focusNextItem=function(){var t=n.state,e=t.focusedItemIndex,r=t.suggestions,o=n.getNextFocusable(e,r.length);n.setState({focusedItemIndex:o}),n.scrollToFocused(o)},n.focusPreviousItem=function(){var t=n.state,e=t.focusedItemIndex,r=t.suggestions,o=n.getPrevFocusable(e,r.length);n.setState({focusedItemIndex:o}),n.scrollToFocused(o)},n.getNextFocusable=function(t,e){var n=t;return void 0===n?n=0:n<e?n+=1:n=0,n},n.handleItemSelect=function(t){n.setState({focusedItemIndex:void 0,isOpen:!1}),t&&t.onClick()},n.getPrevFocusable=function(t,e){var n=t;return n>0?n-=1:n=e,n},n.scrollToFocused=function(t){var e,r=n.containerNode.querySelector('[data-role="droplistContent"]');r&&void 0!==t&&(e=r.querySelectorAll('[data-role="droplistItem"]')[t]),e&&r&&(r.scrollTop=e.offsetTop-r.clientHeight+e.clientHeight)},n.handleInputChange=function(t){var e=t.target.value;e.trim()!==n.props.inputValue&&(n.validateInput(t),n.onOpenChange({event:t,isOpen:!0}),n.updateHitCount(e),n.props.onChange(t,n.state.jqlError),n.setState({jql:e}))},n.validateRequest=function(t){t?n.jqlTimer=setTimeout((function(){return O(n,void 0,void 0,(function(){var e,n;return x(this,(function(r){switch(r.label){case 0:return[4,this.props.validationRequest(t)];case 1:return 200===(e=r.sent()).response.status?(this.setState({jqlError:null}),this.props.onValidate&&this.props.onValidate(null)):(n=e.errorResult,this.props.onValidate?(this.setState({jqlError:"err"}),this.props.onValidate(n.errorMessages)):this.setState({jqlError:JSON.stringify(n)})),[2]}}))}))}),500):n.setState({jqlError:null})},n.validateInput=function(t){n.jqlTimer&&clearTimeout(n.jqlTimer);var e=t.currentTarget.value;n.validateRequest(e)},n.validateWhenComponentDidMount=function(t){return n.validateRequest(t)},n.state={jql:n.props.initialValue,isOpen:!1,suggestions:[],jqlError:!1,hitsCount:null},n}return S(e,t),e.prototype.componentDidMount=function(){var t=this,e=_("#"+this.props.inputId);this.constructorData={input:e,render:this.setSuggestions,getSuggestions:g()((function(e,n,r){void 0===r&&(r=""),t.props.getSuggestionsRequest(e,r).then((function(t){n(t.result.results)}))}),400)},this.jql=new(v())(this.constructorData),this.props.getAutocompleteDataRequest().then((function(e){t.jql.passAutocompleteData(e.result)})),this.validateWhenComponentDidMount(this.props.initialValue),e.attr("autocomplete","off"),e.attr("data-cy",this.props.inputId),this.updateHitCount(this.props.initialValue)},e.prototype.render=function(){var t=this,e=this.state,n=e.isOpen,r=e.suggestions,a=this.props,i=a.maxHeight,u=a.shouldFlip,c=this.state.jqlError?o.createElement(j,null):o.createElement(q,null);return""===this.state.jql&&(c=""),o.createElement("div",{style:{display:"flex"}},o.createElement("div",{style:{width:"500px",cursor:"default",marginTop:"-25px"},onKeyDown:this.handleKeyboardInteractions,ref:function(e){t.containerNode=e}},o.createElement(s.C,{label:this.props.label,name:this.props.inputId},(function(){return o.createElement(p.Z,{isKeyboardInteractionDisabled:!0,shouldFitContainer:!0,isOpen:n&&r.length>0,onOpenChange:t.onOpenChange,maxHeight:i,shouldFlip:u,trigger:o.createElement(l.Z,{type:"text",elemBeforeInput:c,defaultValue:t.props.initialValue,onInput:t.handleInputChange,autoComplete:"off",id:t.props.inputId,style:{paddingLeft:8,cursor:"auto",marginTop:0},ref:function(e){t.textInput=e}})},o.createElement(f.Z,null,t.renderItems()))}))),o.createElement("div",{style:{paddingLeft:10,display:"flex",alignItems:"center"}},null!=this.state.hitsCount?o.createElement("a",{target:"_blank",href:"".concat(AJS.contextPath(),"/issues/?").concat((0,b.F)({jql:this.state.jql}))},this.state.hitsCount," hit(s)"):null))},e}(o.PureComponent);var P=function(){return P=Object.assign||function(t){for(var e,n=1,r=arguments.length;n<r;n++)for(var o in e=arguments[n])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t},P.apply(this,arguments)},k=function(t,e,n,r){return new(n||(n=Promise))((function(o,a){function i(t){try{l(r.next(t))}catch(t){a(t)}}function s(t){try{l(r.throw(t))}catch(t){a(t)}}function l(t){var e;t.done?o(t.value):(e=t.value,e instanceof n?e:new n((function(t){t(e)}))).then(i,s)}l((r=r.apply(t,e||[])).next())}))},L=function(t,e){var n,r,o,a,i={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return a={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function s(a){return function(s){return function(a){if(n)throw new TypeError("Generator is already executing.");for(;i;)try{if(n=1,r&&(o=2&a[0]?r.return:a[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,a[1])).done)return o;switch(r=0,o&&(a=[2&a[0],o.value]),a[0]){case 0:case 1:o=a;break;case 4:return i.label++,{value:a[1],done:!1};case 5:i.label++,r=a[1],a=[0];continue;case 7:a=i.ops.pop(),i.trys.pop();continue;default:if(!(o=i.trys,(o=o.length>0&&o[o.length-1])||6!==a[0]&&2!==a[0])){i=0;continue}if(3===a[0]&&(!o||a[1]>o[0]&&a[1]<o[3])){i.label=a[1];break}if(6===a[0]&&i.label<o[1]){i.label=o[1],o=a;break}if(o&&i.label<o[2]){i.label=o[2],i.ops.push(a);break}o[2]&&i.ops.pop(),i.trys.pop();continue}a=e.call(t,i)}catch(t){a=[6,t],r=0}finally{n=o=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,s])}}},D=function(t){return(0,I.wrappedFetch)("".concat(AJS.contextPath(),"/rest/api/2/jql/autocompletedata/suggestions?").concat((0,b.F)({fieldName:t})))},F=function(t){return k(void 0,void 0,Promise,(function(){return L(this,(function(e){switch(e.label){case 0:return[4,new Promise((function(t){return setTimeout(t,1e3)}))];case 1:return e.sent(),[2,(0,I.wrappedFetch)("".concat(AJS.contextPath(),"/rest/api/2/search?").concat((0,b.F)({jql:t,maxResults:1,startAt:0,validateQuery:"strict",fields:"summary"})))]}}))}))},T=function(){return(0,I.wrappedFetch)("".concat(AJS.contextPath(),"/rest/api/2/jql/autocompletedata"))},V=(0,i.CA)({onValidate:function(t){return function(e){var n;null===(n=t.updateValidation)||void 0===n||n.call(t,{FIELD_JQL_QUERY:e})}},onChangeJql:function(t){return function(e){t.onChange(e.target.value)}}});const R=(0,i.qC)(V)((function(t){return o.createElement(a.Z,P({},t,{param:t.param}),o.createElement(A,{getAutocompleteDataRequest:T,getSuggestionsRequest:D,inputStyle:"ak-field-text",initialValue:t.param.value,inputId:t.param.name,label:"    ",onChange:t.onChangeJql,validationRequest:F,onValidate:t.onValidate}))}))},89492:(t,e,n)=>{n.d(e,{Z:()=>s});var r=n(63844),o=n(53997),a=function(){return a=Object.assign||function(t){for(var e,n=1,r=arguments.length;n<r;n++)for(var o in e=arguments[n])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t},a.apply(this,arguments)},i={loadAllUrl:function(){return"".concat(AJS.contextPath(),"/rest/scriptrunner-jira/latest/fields")},invalidItemSelected:function(t){return"Failed to find field with ID: ".concat(t)},mapResultsToOption:function(t){return t},placeholder:"Select field"};const s=function(t){return r.createElement(o.Z,a({},t,i))}},53997:(t,e,n)=>{n.d(e,{Z:()=>d});var r,o=n(63844),a=n(88162),i=n(7664),s=n(7120),l=n(74729),u=(r=function(t,e){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])},r(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}),c=function(){return c=Object.assign||function(t){for(var e,n=1,r=arguments.length;n<r;n++)for(var o in e=arguments[n])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t},c.apply(this,arguments)},p=function(t,e,n,r){return new(n||(n=Promise))((function(o,a){function i(t){try{l(r.next(t))}catch(t){a(t)}}function s(t){try{l(r.throw(t))}catch(t){a(t)}}function l(t){var e;t.done?o(t.value):(e=t.value,e instanceof n?e:new n((function(t){t(e)}))).then(i,s)}l((r=r.apply(t,e||[])).next())}))},f=function(t,e){var n,r,o,a,i={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return a={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function s(a){return function(s){return function(a){if(n)throw new TypeError("Generator is already executing.");for(;i;)try{if(n=1,r&&(o=2&a[0]?r.return:a[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,a[1])).done)return o;switch(r=0,o&&(a=[2&a[0],o.value]),a[0]){case 0:case 1:o=a;break;case 4:return i.label++,{value:a[1],done:!1};case 5:i.label++,r=a[1],a=[0];continue;case 7:a=i.ops.pop(),i.trys.pop();continue;default:if(!(o=i.trys,(o=o.length>0&&o[o.length-1])||6!==a[0]&&2!==a[0])){i=0;continue}if(3===a[0]&&(!o||a[1]>o[0]&&a[1]<o[3])){i.label=a[1];break}if(6===a[0]&&i.label<o[1]){i.label=o[1],o=a;break}if(o&&i.label<o[2]){i.label=o[2],i.ops.push(a);break}o[2]&&i.ops.pop(),i.trys.pop();continue}a=e.call(t,i)}catch(t){a=[6,t],r=0}finally{n=o=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,s])}}};const d=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.findSelectedValues=function(t){return!!e.props.value&&("string"==typeof e.props.value?e.props.hasIdentifier(t,e.props.value):Array.isArray(e.props.value)?e.props.value.some((function(n){return e.props.hasIdentifier(t,n)})):(console.warn("Unsupported type of component value: ".concat(typeof e.props.value)),!1))},e.loadAll=function(){return p(e,void 0,void 0,(function(){var t,e;return f(this,(function(n){switch(n.label){case 0:if(!(t=this.props.loadAllUrl()))return[2,[]];n.label=1;case 1:return n.trys.push([1,,3,4]),this.showLoading(!0),[4,(0,l.wrappedFetch)(t)];case 2:return(e=n.sent()).error?(console.error("error fetching url : ".concat(t," ").concat(JSON.stringify(e.error))),[2]):[2,this.props.mapResultsToOption(e.result)];case 3:return this.showLoading(!1),[7];case 4:return[2]}}))}))},e.getOptions=function(){return{options:e.state.allItems}},e.getComponent=function(t){return o.createElement(i.ZP,c({},t))},e}return u(e,t),e.prototype.componentDidMount=function(){return p(this,void 0,Promise,(function(){var t;return f(this,(function(e){switch(e.label){case 0:return this.state.itemsLoaded||this.props.isLoadingSelectedValue?[3,2]:(this.setState({itemsLoaded:!0}),[4,this.loadAll()]);case 1:t=e.sent(),this.filterResults(t),this.props.error&&this.setState({errors:[{message:this.props.error}]}),e.label=2;case 2:return[2]}}))}))},e.prototype.flattenOptions=function(t){var e=this,n=t.map((function(t){return t.options?e.flattenOptions(t.options):t}));return(0,s.Z)(n)},e.prototype.filterResults=function(t){var e=this,n=this.props.value?this.isSingle(this.props)?[this.props.value]:this.props.value:[];this.setState({allItems:t,errors:n.filter((function(n){return!e.flattenOptions(t).some((function(t){return e.props.hasIdentifier(t,n)}))})).map((function(t){return{message:e.props.invalidItemSelected(t),invalidItemId:t}})),selectedItems:this.flattenOptions(t).filter(this.findSelectedValues)})},e.getDerivedStateFromProps=function(t,e){return null!=t.isLoadingSelectedValue?{isLoading:t.isLoadingSelectedValue,isDisabled:t.isLoadingSelectedValue}:null},e.prototype.showLoading=function(t){this.setState({isLoading:t,isDisabled:t})},e.prototype.componentDidUpdate=function(t,e,n){t.loadAllUrl()===this.props.loadAllUrl()&&this.state.itemsLoaded?t.value===this.props.value||this.props.isLoadingSelectedValue||this.filterResults(this.state.allItems):this.componentDidMount()},e.defaultProps=c(c({},a.Z.defaultProps),{hasIdentifier:function(t,e){return e===t.value}}),e}(a.Z)}}]);