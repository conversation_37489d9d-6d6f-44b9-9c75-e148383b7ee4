(self.webpackJsonpScriptRunner=self.webpackJsonpScriptRunner||[]).push([["default-src_main_resources_js_admin_util_generalUtils_ts-src_main_resources_js_behaviours_deb-a911b4"],{84331:(e,t,r)=>{"use strict";r.d(t,{QO:()=>l,e5:()=>a});var n=r(3514),i=function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,i,o=r.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(n=o.next()).done;)a.push(n.value)}catch(e){i={error:e}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(i)throw i.error}}return a},o=function(e,t,r){if(r||2===arguments.length)for(var n,i=0,o=t.length;i<o;i++)!n&&i in t||(n||(n=Array.prototype.slice.call(t,0,i)),n[i]=t[i]);return e.concat(n||Array.prototype.slice.call(t))},a=function(e,t){var r=Array.isArray(e)?e:[],i=Array.isArray(t)?t:[];return n.Z(r,i)},l=function(e,t,r){return e.length>=t?o(o(o([],i(e.slice(0,t)),!1),i(r),!1),i(e.slice(t)),!1):o(o([],i(e),!1),i(r),!1)}},28790:(e,t,r)=>{"use strict";var n=r(53281),i=r(85321),o=r(39507),a=(r(17775),r(5667),r(86936)),l=r(63844),c=function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,i,o=r.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(n=o.next()).done;)a.push(n.value)}catch(e){i={error:e}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(i)throw i.error}}return a},s=function(e){var t=c((0,l.useState)(""),2),r=t[0],n=t[1];return l.createElement("textarea",{style:{backgroundColor:r},className:"textarea",placeholder:e.placeholder,onChange:function(t){var r=t.currentTarget.value;try{var i=JSON.parse(r);n("rgb(223, 240, 216)"),e.onChange(i)}catch(t){n("rgb(250, 220, 220)")}}})},u=function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,i,o=r.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(n=o.next()).done;)a.push(n.value)}catch(e){i={error:e}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(i)throw i.error}}return a},d=function(e){var t=e.onClose,r=e.behaviour,i=e.behaviours,o=e.displayValue,a=e.highlightChangedValue,c=i.getFieldLabel(r.field),d=i.isRequired(c),f=u((0,l.useState)("inherit"),2),p=f[0],h=f[1],v=l.useRef(null);return a&&!v.current&&(h("yellow"),v.current=setTimeout((function(){h("inherit"),v.current=null}),1e3)),l.createElement("div",null,l.createElement("h3",null,"Field: ",r.fieldId),l.createElement("form",{className:"aui ajs-dirty-warning-exempt"},l.createElement("fieldset",{className:"group"},l.createElement("legend",null,l.createElement("span",null,"Attributes")),l.createElement("div",{className:"checkbox"},l.createElement("label",null,"Read only",l.createElement("input",{className:"checkbox",type:"checkbox",onClick:function(t){var o=n.kc.findFieldForId(e.$form,r.fieldId);t.currentTarget.checked?i.makeReadOnly(o):i.makeWritable(o)}}))),l.createElement("div",{className:"checkbox"},l.createElement("label",null,"Required",l.createElement("input",{defaultChecked:d,className:"checkbox",type:"checkbox",onClick:function(t){var o=n.kc.findFieldForId(e.$form,r.fieldId);t.currentTarget.checked?i.requireField(o):i.unRequireField(o)}}))),l.createElement("div",{className:"checkbox"},l.createElement("label",null,"Hidden",l.createElement("input",{className:"checkbox",type:"checkbox",onClick:function(t){var o=n.kc.findFieldForId(e.$form,r.fieldId);i.setHidden(o,t.currentTarget.checked)}}))),l.createElement("div",{className:"checkbox"},l.createElement("label",null,"Invalid",l.createElement("input",{className:"checkbox",type:"checkbox",onClick:function(t){var o=t.currentTarget.checked,a=n.kc.findFieldForId(e.$form,r.fieldId);i.setValidity(r.fieldId,a,o?n.EY.INVALID:n.EY.NONE),i.setErrorText(a,r)}})))),l.createElement("div",{className:"field-group"},l.createElement("label",null,"Set value"),l.createElement(s,{onChange:function(e){return i.setFieldValue(r.field,e,r.fieldId)},placeholder:'Enter text as json, example: "foo"'})),l.createElement("div",{className:"field-group"},l.createElement("label",null,"Current value"),l.createElement("div",{style:{backgroundColor:p}},o)),l.createElement("div",{className:"field-group"},l.createElement("label",null,"Field Options"),l.createElement(s,{onChange:function(t){return i.setFieldOptions(e.$form,r.field,r.fieldId,t)},placeholder:'Enter text as json. Format: [{"key": 10012, "value": "Foo"}]'})),l.createElement("div",{className:"field-group"},l.createElement("label",null,"Set Field Label"),l.createElement("input",{className:"text",placeholder:"Enter fieldname...",onChange:function(e){return i.setFieldLabel(r.field,e.currentTarget.value)}})),l.createElement("a",{className:"aui-button aui-button-link",onClick:t},"Close")))},f=r(35484),p=r(74729),h=function(){return h=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var i in t=arguments[r])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e},h.apply(this,arguments)},v=function(e,t,r,n){return new(r||(r=Promise))((function(i,o){function a(e){try{c(n.next(e))}catch(e){o(e)}}function l(e){try{c(n.throw(e))}catch(e){o(e)}}function c(e){var t;e.done?i(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(a,l)}c((n=n.apply(e,t||[])).next())}))},m=function(e,t){var r,n,i,o,a={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return o={next:l(0),throw:l(1),return:l(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function l(o){return function(l){return function(o){if(r)throw new TypeError("Generator is already executing.");for(;a;)try{if(r=1,n&&(i=2&o[0]?n.return:o[0]?n.throw||((i=n.return)&&i.call(n),0):n.next)&&!(i=i.call(n,o[1])).done)return i;switch(n=0,i&&(o=[2&o[0],i.value]),o[0]){case 0:case 1:i=o;break;case 4:return a.label++,{value:o[1],done:!1};case 5:a.label++,n=o[1],o=[0];continue;case 7:o=a.ops.pop(),a.trys.pop();continue;default:if(!(i=a.trys,(i=i.length>0&&i[i.length-1])||6!==o[0]&&2!==o[0])){a=0;continue}if(3===o[0]&&(!i||o[1]>i[0]&&o[1]<i[3])){a.label=o[1];break}if(6===o[0]&&a.label<i[1]){a.label=i[1],i=o;break}if(i&&a.label<i[2]){a.label=i[2],a.ops.push(o);break}i[2]&&a.ops.pop(),a.trys.pop();continue}o=t.call(e,a)}catch(e){o=[6,e],n=0}finally{r=i=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,l])}}},g=function(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},y="ScriptRunner.Behaviours.enableDevTools",b=jQuery;function E(e,t){return v(this,void 0,void 0,(function(){var e,r,c,s,u,y,E,w,k,x,S,A,F=this;return m(this,(function(C){switch(C.label){case 0:return"form#issue-create,form#issue-workflow-transition,form#assign-issue,form#log-work,div.issue-setup-fields,form[name='jiraform'],form#subtask-create-details,form#issue-edit,form.vp-form",t.find("form#issue-create,form#issue-workflow-transition,form#assign-issue,form#log-work,div.issue-setup-fields,form[name='jiraform'],form#subtask-create-details,form#issue-edit,form.vp-form").length?((e=t.find("form.aui")).addClass("adaptavist-sr ajs-dirty-warning-exempt"),r=(0,f.nM)()?new i.A:new n.kc,c="".concat(AJS.contextPath(),"/rest/scriptrunner/behaviours/latest/admin/fieldsForScreen?").concat((0,o.F)(r.getCoordinates(e))),[4,(0,p.wrappedFetch)(c)]):[2];case 1:return s=C.sent(),u=s.result,y=Object.keys(u).filter((function(t){return n.kc.findFieldForId(e,t).length>0})).reduce((function(t,r){return t[r]=u[r],t[r].field=n.kc.findFieldForId(e,r),t}),{}),[4,r.addFieldListeners(e,y,!0)];case 2:C.sent(),E=function(t){var n=h({},y[t]);n.fieldId=t;var i=b(n.field).closest("div.field-group");0===i.length&&(i=b("input[name=".concat(n.fieldId,"]:first")).closest("fieldset.group"));var o="configure-field-dlg-".concat(t);b('\n            <a data-aui-trigger aria-controls="'.concat(o,'" href="#').concat(o,"\">\n                <span style='color: grey' class='aui-icon aui-icon-small aui-iconfont-configure'>\n                    Configure\n                </span>\n\n            </a>\n            <aui-inline-dialog id=\"").concat(o,'" alignment="bottom right" persistent/>')).appendTo(i),b("#".concat(o)).on("aui-show",(function(i){var c=b("<span/>");b(i.currentTarget).find(".aui-inline-dialog-contents").empty().append(c);var s=function(i){var s=r.constructForm(e)[t],u=JSON.stringify(s);return a.render(l.createElement(d,{onClose:function(){r.unbindChangeEvents(n.fieldId,n.field,n,".jbhv-devtool"),document.getElementById(o).removeAttribute("open")},displayValue:u,behaviour:n,$form:e,behaviours:r,highlightChangedValue:i}),c.get(0))};r.bindChangeEvents(n.fieldId,n.field,e,n,".jbhv-devtool",(function(){return v(F,void 0,void 0,(function(){return m(this,(function(e){switch(e.label){case 0:return[4,s(!0)];case 1:return e.sent(),[2]}}))}))})),s(!1)}))};try{for(w=g(Object.getOwnPropertyNames(y)),k=w.next();!k.done;k=w.next())x=k.value,E(x)}catch(e){S={error:e}}finally{try{k&&!k.done&&(A=w.return)&&A.call(w)}finally{if(S)throw S.error}}return[2]}}))}))}function w(){return"true"===localStorage[y]}w()&&b((function(){JIRA.bind(JIRA.Events.NEW_CONTENT_ADDED,E),setTimeout((function(){E(0,b(document)).catch((function(e){console.log("Failed to add devtools",e)}))}),250)})),void 0===window.ScriptRunner&&(window.ScriptRunner={}),void 0===window.ScriptRunner.Behaviours&&(window.ScriptRunner.Behaviours={}),window.ScriptRunner.Behaviours=h(h({},window.ScriptRunner.Behaviours),{enableDevTools:function(){return v(this,void 0,void 0,(function(){return m(this,(function(e){return w()?(console.log("Dev tools already enabled"),[2]):(localStorage[y]=!0,[2,E(0,b(document))])}))}))},disableDevTools:function(){localStorage.removeItem(y),console.log("Disabled, now refresh browser")}})},85321:(e,t,r)=>{"use strict";r.d(t,{A:()=>y});var n,i=r(53281),o=r(35484),a=r(40270),l=r(62973),c=(r(55778),r(5667),r(75583)),s=r(84806),u=r(22499),d=r(47144),f=r(25972),p=(n=function(e,t){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])},n(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=e}n(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}),h=function(e,t,r,n){return new(r||(r=Promise))((function(i,o){function a(e){try{c(n.next(e))}catch(e){o(e)}}function l(e){try{c(n.throw(e))}catch(e){o(e)}}function c(e){var t;e.done?i(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(a,l)}c((n=n.apply(e,t||[])).next())}))},v=function(e,t){var r,n,i,o,a={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return o={next:l(0),throw:l(1),return:l(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function l(o){return function(l){return function(o){if(r)throw new TypeError("Generator is already executing.");for(;a;)try{if(r=1,n&&(i=2&o[0]?n.return:o[0]?n.throw||((i=n.return)&&i.call(n),0):n.next)&&!(i=i.call(n,o[1])).done)return i;switch(n=0,i&&(o=[2&o[0],i.value]),o[0]){case 0:case 1:i=o;break;case 4:return a.label++,{value:o[1],done:!1};case 5:a.label++,n=o[1],o=[0];continue;case 7:o=a.ops.pop(),a.trys.pop();continue;default:if(!(i=a.trys,(i=i.length>0&&i[i.length-1])||6!==o[0]&&2!==o[0])){a=0;continue}if(3===o[0]&&(!i||o[1]>i[0]&&o[1]<i[3])){a.label=o[1];break}if(6===o[0]&&a.label<i[1]){a.label=i[1],i=o;break}if(i&&a.label<i[2]){a.label=i[2],a.ops.push(o);break}i[2]&&a.ops.pop(),a.trys.pop();continue}o=t.call(e,a)}catch(e){o=[6,e],n=0}finally{r=i=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,l])}}},m=jQuery,g=(0,c.j)("sr-behaviours"),y=function(e){function t(){var t=e.call(this)||this,r=document.location,n=r.pathname;return t.coords=(0,o.EA)(),n=n.replace(/\/servicedesk\/.*/,""),AJS.params.baseURL="".concat(r.protocol,"//").concat(r.hostname,":").concat(r.port).concat(n),t}return p(t,e),t.prototype.getCoordinates=function(e){return this.coords},t.prototype.getRestPrefix=function(){return"jsd/jsd/"},t.prototype.getFieldLabel=function(e){var t="string"==typeof e?m(e):e,r=t.is(".radio, .checkbox, input[name=dnd-dropzone]")?t.closest("fieldset.group").find(".field-label, legend"):t.closest(".field-group").find(".field-label").not(".select2-offscreen");return r.length?r:m("#".concat(t.attr("name"),"-label"))},t.prototype.getSelect2CssClass=function(){return"js-select2-picker"},t.prototype.isRequired=function(e){return!e.find("span.vp-optional").length},t.prototype.setDescription=function(e,t){var r=m("<div class='description'>".concat(t,"</div>")),n=e.closest(".field-group, fieldset");n.find("div.description").remove(),n.find(".field-container").length>0?n.find(".field-container").after(r):n.append(r)},t.prototype.unRequireField=function(e){var t=this.getFieldLabel(e);this.isRequired(t)&&t.append('<span class="vp-optional"> (optional)</span>')},t.prototype.requireField=function(e){this.getFieldLabel(e).find("span.vp-optional").remove()},t.prototype.setFieldValue=function(t,r,n){var i;return h(this,void 0,Promise,(function(){var o,a,c,s;return v(this,(function(u){switch(u.label){case 0:return"select-one"!==t[0].type?[3,1]:(t.hasClass("cascadingselect-parent")?((o=t.parent().find("select.cascadingselect-parent")).val()!==r[0]&&o.val(r[0]).trigger("change"),r.length>1&&(a=t.parent().find("select.cascadingselect-child")).val()!==r[1]&&a.val(r[1]).trigger("change")):(c=t.parent().find("select"),null==r&&(r=-1),"string"==typeof r?(s=t.find("option").filter((function(){return m(this).text().trim()===r||m(this).val()===r}))).length&&s.val()!==c.val()&&c.val(s.val()).trigger("change"):"number"==typeof r&&r!==parseInt(null===(i=c.val())||void 0===i?void 0:i.toString(),10)&&c.val(r).trigger("change")),[3,6]);case 1:return(0,f.z8)(t)||"select-multiple"!==t[0].type||t[0].classList.contains("insight-init")?[3,2]:(l.Z(t.val()||[],r||[]).length&&t.val(r).trigger("change"),[3,6]);case 2:return t.closest("sd-user-picker").length?(this.setUserPickerFieldData(t,r),[3,6]):[3,3];case 3:return this.isAKEditor(t)?[4,this.setAKEditorValue(t,r)]:[3,5];case 4:return u.sent(),[3,6];case 5:if("labels"!==t.attr("id"))return[2,e.prototype.setFieldValue.call(this,t,r,n)];this.setSelect2Value(t,r),u.label=6;case 6:return[2]}}))}))},t.prototype.makeReadOnly=function(t){return h(this,void 0,void 0,(function(){return v(this,(function(r){switch(r.label){case 0:return e.prototype.makeReadOnly.call(this,t),this.isAKEditor(t)?[4,this.setAKEditorWritableState(t,!1)]:[3,2];case 1:r.sent(),r.label=2;case 2:return[2]}}))}))},t.prototype.makeWritable=function(t){return h(this,void 0,void 0,(function(){return v(this,(function(r){switch(r.label){case 0:return e.prototype.makeWritable.call(this,t),this.isAKEditor(t)?[4,this.setAKEditorWritableState(t,!0)]:[3,2];case 1:r.sent(),r.label=2;case 2:return[2]}}))}))},t.prototype.setUserPickerFieldData=function(e,t){var r=e.closest("sd-user-picker").data("multiple");"string"==typeof t&&(t=t.split(",").map((function(e){return e.trim()})).filter((function(e){return!!e}))),0!==t.length?Promise.all(t.map((function(e){return(0,a.k)("".concat(AJS.contextPath(),"/rest/api/2/user?username=").concat(e)).then((function(t){return{request:t,username:e}}))}))).then((function(t){var n=t.filter((function(e){var t=e.request,r=e.username;return!!t.result||(g.warn("Error retrieving user: ".concat(r)),!1)})).map((function(e){var t=e.request,r=t.result,n=t.response,i=e.username;return n.redirected?{id:i,displayName:i}:{id:r.name,emailAddress:r.emailAddress,displayName:r.displayName,avatar:r.avatarUrls["16x16"]}})),i=r?n:n[0];(0,d.UW)(e,(function(){return e.trigger("change").select2("data",i)}))})):e.trigger("change").select2("data",r?[]:"")},t.prototype.bindChangeEvents=function(t,r,n,o,a,l){return void 0===a&&(a=i.A7),void 0===l&&(l=this.postValidator.bind(this)),h(this,void 0,Promise,(function(){return v(this,(function(i){switch(i.label){case 0:return this.getAKEditor(r).length?[4,this.bindChangeEventToEditor(r)]:[3,2];case 1:i.sent(),i.label=2;case 2:return[4,e.prototype.bindChangeEvents.call(this,t,r,n,o,a,l)];case 3:return i.sent(),[2]}}))}))},t.prototype.bindChangeEventToEditor=function(e){return h(this,void 0,void 0,(function(){var t,r,n;return v(this,(function(i){switch(i.label){case 0:return t=this.getAKEditor(e),[4,(0,s.X7)(t[0],".akEditor")];case 1:return r=i.sent(),(n=m(r).find(".ProseMirror")).on("focus",(function(){var t=e.val(),r=new MutationObserver((function(n){n.forEach((function(n){"class"===n.attributeName&&(m(n.target).hasClass("ProseMirror-focused")||(r.disconnect(),t!==e.val()&&e.trigger("change")))}))}));r.observe(n[0],{attributes:!0})})),[2]}}))}))},t.prototype.setAKEditorWritableState=function(e,t){return h(this,void 0,void 0,(function(){var r,n;return v(this,(function(i){switch(i.label){case 0:return[4,this.getProseMirror(e)];case 1:return r=i.sent(),n=r.attr("contenteditable",t.toString()).closest(".akEditor"),t?n.removeClass("clientreadonly").find(">:first-child").show():n.addClass("clientreadonly").find(">:first-child").hide(),[2]}}))}))},t.prototype.getProseMirror=function(e){return h(this,void 0,void 0,(function(){var t,r;return v(this,(function(n){switch(n.label){case 0:return t=this.getAKEditor(e),[4,(0,s.X7)(t[0],".akEditor")];case 1:return r=n.sent(),[2,m(r).find(".ProseMirror")]}}))}))},t.prototype.setAKEditorValue=function(e,t){return h(this,void 0,void 0,(function(){var r,n,i;return v(this,(function(o){switch(o.label){case 0:return r=this.getAKEditor(e),[4,(0,s.X7)(r[0],".akEditor")];case 1:return n=o.sent(),i=(0,u.s)(n,4).props.editorActions,t?i.replaceDocument(t):i.clear(),[2]}}))}))},t.prototype.isAKEditor=function(e){return!!this.getAKEditor(e).length},t.prototype.getAKEditor=function(e){return e.next(".wysiwyg-create-request")},t.prototype.setSelect2Value=function(e,t){t||(t=[]);var r=(t=Array.isArray(t)?t:[t]).map((function(e){return{html:e,id:e,text:e}}));(0,d.UW)(e,(function(){return e.select2("data",r)}))},t}(i.kc)},55778:(e,t,r)=>{e.exports=r},5667:(e,t,r)=>{e.exports=r}}]);