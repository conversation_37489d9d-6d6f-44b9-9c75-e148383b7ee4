(self.webpackJsonpScriptRunner=self.webpackJsonpScriptRunner||[]).push([["default-src_main_resources_js_behaviours_index_ts"],{84806:(e,t,n)=>{"use strict";n.d(t,{X7:()=>a,bK:()=>i});var r=function(){return r=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e},r.apply(this,arguments)},i=function(e,t){return void 0===t&&(t={}),new Promise((function(n){return o(document.body,e,n,r({subtree:!1},t))}))},a=function(e,t,n){return void 0===n&&(n={}),new Promise((function(r){return o(e,t,r,n)}))},o=function(e,t,n,i){void 0===i&&(i={});var a=e.querySelector(t);a?n(a):new MutationObserver((function(r,i){var a=e.querySelector(t);a&&(i.disconnect(),n(a))})).observe(e,r({childList:!0,subtree:!0,attributes:!1,characterData:!1},i))}},22499:(e,t,n)=>{"use strict";n.d(t,{s:()=>r});var r=function(e,t){if(void 0===t&&(t=0),null==e)return null;var n=e[Object.keys(e).find((function(e){return e.startsWith("__reactInternalInstance$")}))];if(null==n)return null;if(n._currentElement){for(var r=n._currentElement._owner,i=0;i<t;i++)r=r._currentElement._owner;return r._instance}var a=function(e){for(var t=e.return;"string"==typeof t.type;)t=t.return;return t},o=a(n);for(i=0;i<t;i++)o=a(o);return o.stateNode}},47144:(e,t,n)=>{"use strict";n.d(t,{Kg:()=>a,UW:()=>i,ri:()=>o});var r="jbhv-suppress-event",i=function(e,t){e.toggleClass(r);try{t()}finally{e.toggleClass(r)}},a=function(e){var t;return null===(t=null==e?void 0:e.classList)||void 0===t?void 0:t.contains(r)},o=function(e){var t=document.activeElement;try{e()}finally{t&&t.focus()}}},40270:(e,t,n)=>{"use strict";n.d(t,{e:()=>a,k:()=>o});var r=n(74729),i="behavioursServerCallCount",a=function(){return(0,r.getRequestCountValue)(i)},o=(0,r.trackedFetchFactory)(i)},25972:(e,t,n)=>{"use strict";n.d(t,{$u:()=>i,B7:()=>a,wy:()=>s,z8:()=>o});var r=AJS.$,i=function(e){if(e.hasClass("multi-select-select")){var t=[];return e.find(":selected:not(.free-input)").each((function(){t.push(r(this).val())})),t.join("-*-*-")}if(o(e))return e.data("behavioursShim").getValue();if(e.hasClass("aui-ss-select"))return s(e.parent().find("select").val());if("radio"===e.attr("type"))return e.parent().parent().find(":checked").val();if("checkbox"===e.attr("type")){var n=[];return e.closest("fieldset").find(":checked").each((function(){n.push(r(this).val())})),n.join("-*-*-")}if(e.hasClass("cascadingselect-parent")){var i=e.parent().find("option:selected").map((function(){return r(this).val()}));return r.makeArray(i).join("-*-*-")}if(e.hasClass("insight-init")&&e.parent().find("#".concat(e.attr("name"),"_multi")).length)return e.parent().find("#".concat(e.attr("name"),"_multi")).val().toString().split(" ");if(e.hasClass("js-rlabs-customfield-edit")){var a=e.data("keys");return e.attr("multiple")?null==a?void 0:a.split(","):a}return e.val()},a=function(e){return e.hasClass("textarea")?e.val().toString():e.hasClass("multi-select-select")?e.find(":selected:not(.free-input)").toArray().map((function(e){return e.textContent})).join(", "):o(e)?e.data("behavioursShim").getValue():e.hasClass("cascadingselect-parent")?e.parent().find("option:selected").map((function(e,t){return t.textContent.trim()})).toArray().join(", "):e.hasClass("aui-ss-select")||e.hasClass("select")?e.find("option:selected").map((function(e,t){return t.textContent.trim()})).toArray().join(", "):e.val().toString()},o=function(e){return e.hasClass("behaviours-common-api")},s=function(e){return e instanceof Array&&1===e.length?e[0]:e}},53281:(e,t,n)=>{"use strict";n.d(t,{kc:()=>qe,EY:()=>Te,A7:()=>ge,sf:()=>ve,YK:()=>Le});var r=n(40270),i=n(7091),a=n.n(i),o=n(14500),s=n(96253),c=n(84331),l=n(75583),u=n(47144),d=(n(5667),function(e){var t="function"==typeof Symbol&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}),f=function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,i,a=n.call(e),o=[];try{for(;(void 0===t||t-- >0)&&!(r=a.next()).done;)o.push(r.value)}catch(e){i={error:e}}finally{try{r&&!r.done&&(n=a.return)&&n.call(a)}finally{if(i)throw i.error}}return o},p=function(e,t,n){if(n||2===arguments.length)for(var r,i=0,a=t.length;i<a;i++)!r&&i in t||(r||(r=Array.prototype.slice.call(t,0,i)),r[i]=t[i]);return e.concat(r||Array.prototype.slice.call(t))},h=(0,l.j)("sr-fields"),v=jQuery;function m(e,t){e.is(".multi-select-select,.aui-ss-select")?e.find("option").each((function(n,r){var i=v(r).data("descriptor");i.value()===t&&(h.debug("removing item",i.label()),function(e,t){var n=e.parent().find("div.representation");(0,u.UW)(e,(function(){return n.find("span.value-text").filter((function(e,n){return n.innerText===t})).closest("li").find("em.item-delete").click()}))}(e,i.label()))})):e.parent().find("div.representation").find("option").filter((function(e,n){return v(n).val()===t})).remove()}function g(e,t){var n=v("#issuetype-field");if(!t.length)return function(e){e.val("").prop("disabled",!0).addClass("clientreadonly"),e.siblings("img").hide(),e.siblings("span.drop-menu").hide(),e.closest(".field-group").append('<div class="error jbhverror">'.concat(Le,"</div>"))}(n);var r=e.val();""!==n.val()&&t.map(O).includes(r)||(0,u.UW)(e,(function(){return e.trigger("set-selection-value",t[0].key)}));var i=e.parent().find("span.drop-menu");1===t.length?i.hide():i.show(),i.on("click".concat(ge),{options:t},y),n.on("click".concat(ge," aui:keydown").concat(ge," input").concat(ge),{options:t},y)}var y=function(e){var t=v("#issuetype-suggestions");if(t){var n=e.data.options.map(j);t.find("li").each((function(e,t){var r=v(t);n.includes(r.text())?r.show():r.hide()}))}};var b=function(e,t,n,r,i){return'\n        <div class="'.concat(r,'" >\n            <input class="').concat(r,'" id="').concat(e,"-").concat(i,'" name="').concat(e,'" type="').concat(r,'" value="').concat(t,'" title="">\n            <label for="').concat(e,"-").concat(i,'">').concat(n,"</label>\n        </div>\n    ")};function k(e,t,n,r){if([ve.CheckBoxes,ve.RadioButtons].includes(n))return function(e,t,n){var r,i,a=v("[data-jbhv-checkbox-radio-fieldset-name=".concat(e,"]")),o=a.find("input").filter(":checked").map((function(e,t){return v(t).val()})).get();a.find("div.checkbox, div.radio").remove();var s=a.find("legend").length?a.find("legend"):a.find("div.field-container"),c=1;try{for(var l=d(n.slice().reverse()),u=l.next();!u.done;u=l.next()){var f=u.value,p=b(e,f.key,f.value,t?"radio":"checkbox",c);c++,s.after(p)}}catch(e){r={error:e}}finally{try{u&&!u.done&&(i=l.return)&&i.call(l)}finally{if(r)throw r.error}}qe.setRadioOrCheckboxFieldValue(o,e)}(t,n===ve.RadioButtons,r);if(e.hasClass("issuetype-field"))return g(e,r);var i=e.val(),s=function(e){return![ve.Select,ve.MultiSelect,ve.Resolution,ve.ProjectCustomField,ve.VersionCustomField].includes(n)||!!e&&"-1"!==e},l=v.map(r,(function(e){return String(e.key)})).filter(s),h=!1,y=function(e){return e.find("option").get().map((function(e){return v(e).val()}))}(e),k=(0,c.e5)(y,l).filter(s),F=i?Array.isArray(i)?i:[i]:[];if(v.each(k,(function(t,n){h=h||F.indexOf(n)>-1,m(e,n),e.find("option[value='"+n+"']").remove()})),"issue-create-issue-type"!==e.attr("id")){var x=(0,c.e5)(l,y);v.each(x,(function(t,n){var i={text:a()(r,(function(e){return String(e.key)===n})).value,value:n,class:null};e.attr("class")&&e.attr("class").match("cascadingselect-parent")&&(i.class="option-group-"+n),e.append(v("<option/>",i))}));var I=e.find("option"),O=o.Z((function(e){return a()(I,(function(t){return t.value===e}))}),p(["","-1"],f(l),!1)).filter((function(e){return!!e}));e.empty(),v.each(O,(function(t,n){e.append(n.outerHTML)})),h&&S(e)||(h&&e.is(".aui-ss-select, .select")||e.hasClass("select")&&!i&&!S(e)?w(e):e.val(i)),(0,u.UW)(e,(function(){return e.trigger("reset")}))}}var w=function(e){var t=e.find("option:first");t&&(e.val(t.val()),h.debug("Triggering reset"),e.trigger("change.jbhv"))},S=function(e){return!!e.attr("multiple")};function F(e,t){v.each(t,(function(t,n){!function(e,t){var n=jQuery.grep(e.find("option"),(function(e){var n=v(e).data("descriptor");return!!n&&n.value()===t.toString()}));n.length||e.hasClass("aui-field-issuepicker")&&(n=[new AJS.ItemDescriptor({highlighted:!0,value:t,label:t,html:t})]),n.length?(0,u.UW)(e,(function(){return e.trigger("selectOption",n)})):h.warn(AJS.format("Could not add invalid option {0} to {1}",t,e.selector))}(e,n)})),(0,c.e5)(e.val(),t).forEach((function(t){m(e,t)}))}var x=function(e){e.find("option").removeAttr("selected"),e.parent().find("div.aui-ss").remove(),e.removeData("aui-ss"),v(document).trigger(JIRA.Events.NEW_CONTENT_ADDED,[e.parent(),"pageLoad"])},I=function(e,t){return{key:e,value:t}},O=function(e){return e.key},j=function(e){return e.value},C=function(e,t,n,r){return new(n||(n=Promise))((function(i,a){function o(e){try{c(r.next(e))}catch(e){a(e)}}function s(e){try{c(r.throw(e))}catch(e){a(e)}}function c(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,s)}c((r=r.apply(e,t||[])).next())}))},A=function(e,t){var n,r,i,a,o={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return a={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function s(a){return function(s){return function(a){if(n)throw new TypeError("Generator is already executing.");for(;o;)try{if(n=1,r&&(i=2&a[0]?r.return:a[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,a[1])).done)return i;switch(r=0,i&&(a=[2&a[0],i.value]),a[0]){case 0:case 1:i=a;break;case 4:return o.label++,{value:a[1],done:!1};case 5:o.label++,r=a[1],a=[0];continue;case 7:a=o.ops.pop(),o.trys.pop();continue;default:if(!(i=o.trys,(i=i.length>0&&i[i.length-1])||6!==a[0]&&2!==a[0])){o=0;continue}if(3===a[0]&&(!i||a[1]>i[0]&&a[1]<i[3])){o.label=a[1];break}if(6===a[0]&&o.label<i[1]){o.label=i[1],i=a;break}if(i&&o.label<i[2]){o.label=i[2],o.ops.push(a);break}i[2]&&o.ops.pop(),o.trys.pop();continue}a=t.call(e,o)}catch(e){a=[6,e],r=0}finally{n=i=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,s])}}},T=function(){return T=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e},T.apply(this,arguments)},R=function(e){var t="function"==typeof Symbol&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},P=jQuery,E={configureGeneralPickerDescriptor:function(e,t){var n,r,i=new AJS.GroupDescriptor({weight:e,label:t.footer});if(t&&t.items)try{for(var a=R(t.items),o=a.next();!o.done;o=a.next()){var s=o.value;i.addItem(new AJS.ItemDescriptor(T({},E.configureGeneralPickerItems(s))))}}catch(e){n={error:e}}finally{try{o&&!o.done&&(r=a.return)&&r.call(a)}finally{if(n)throw n.error}}return i},configureGeneralPickerDescriptorJSD:function(e,t){var n=t.items.map((function(e){return T(T({},E.configureGeneralPickerItems(e)),{id:e.value,text:e.value})}));return{weight:e,text:t.footer,children:n}},configureGeneralPickerItems:function(e){return{value:e.value,label:e.label,html:e.html,icon:e.icon,allowDuplicate:!1,highlighted:!0}},formatResponse:function(e,t){void 0===t&&(t=!1);var n=[],r=!0===t?E.configureGeneralPickerDescriptorJSD:E.configureGeneralPickerDescriptor;return e&&P(e).each((function(e,t){n.push(r(e,t))})),n}},D={configureIssuePickerDescriptor:function(e,t){var n,r,i=new AJS.GroupDescriptor({weight:e,label:t.label,description:t.sub});if(t.issues&&t.issues.length>0)try{for(var a=R(t.issues),o=a.next();!o.done;o=a.next()){var s=o.value;i.addItem(new AJS.ItemDescriptor(D.configureIssuePickerItems(s)))}}catch(e){n={error:e}}finally{try{o&&!o.done&&(r=a.return)&&r.call(a)}finally{if(n)throw n.error}}return i},configureIssuePickerDescriptorJSD:function(e,t){var n=[];return t.issues&&t.issues.length>0&&(n=t.issues.map((function(e){return T(T({},D.configureIssuePickerItems(e)),{id:e.key,text:"".concat(e.key," - ").concat(e.summaryText)})}))),{weight:e,text:"".concat(t.label," (").concat(t.sub,")"),children:n}},configureIssuePickerItems:function(e){var t=AJS.contextPath();return{highlighted:!0,value:e.key,label:"".concat(e.key," - ").concat(e.summaryText),icon:e.img?t+e.img:null,html:"".concat(e.keyHtml," - ").concat(e.summary)}},formatResponse:function(e,t){void 0===t&&(t=!1);var n=[],r=!0===t?D.configureIssuePickerDescriptorJSD:D.configureIssuePickerDescriptor;return e&&e.sections&&e.sections.forEach((function(e,t){console.log("section",e),"cs"===e.id&&n.push(r(t,e))})),n}},L={IssuePickerUtil:D,GeneralPickerUtil:E},V=n(3503),q=n(35484),N=function(){return N=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e},N.apply(this,arguments)},M=(0,l.j)("sr-types"),J=jQuery;function B(e,t,n,r,i){var a,o,s;M.debug("Conversion.... to "+r);var c=t.val(),l=null!==(o=null===(a=t.parent().find(".select2-container"))||void 0===a?void 0:a.select2("data"))&&void 0!==o?o:[];"single-select"!==r&&"multi-select"!==r||(t.hasClass("aui-ss-select")||"true"===t.data("is-ms-field")||t.is(":hidden"))&&(M.debug("Field already is a select/multiselect list"),B(e,t,n,"short-text",i));var u=n+"-ms";if("short-text"===r){t.show(),t.removeData("is-ms-field"),J("#"+n+"-ms-multi-select").remove(),J("#"+n+"-ms-single-select").remove();var d=J("#"+n+"-ms");return d.prev("div.select2-container").remove(),void d.remove()}if("single-select"===r){if(t.is(".select"))return void new AJS.SingleSelect({element:t,itemAttrDisplayed:"label",errorMessage:"Invalid value selected"});(0,q.nM)()?t.after("<input id='"+u+"' name='"+u+"' class='select2 cf-select'>").hide():t.after("<select id='"+u+"' name='"+u+"' class='select cf-select'></select>").hide(),(s=e.find("#"+u)).data("redirect-to",t.attr("id")),s.on("change, change.jbhv, unselect.jbhv, selected.jbhv",(function(e){var n=J(e.currentTarget),r=n.parent().find("#"+n.data("redirect-to")),i=J.makeArray(n.val());r.val(i.join("; ")),t.trigger("change"+ge)}))}else{if("multi-select"!==r)return void M.warn("Conversion to "+r+" not supported");if(t.is(".select"))return void new AJS.MultiSelect({element:t,itemAttrDisplayed:"label",errorMessage:"Invalid value selected"});(0,q.nM)()?t.after("<input id='"+u+"' name='"+u+"' class='select2 cf-select'>").hide():t.after("<select id='"+u+"' name='"+u+"' class='select cf-select' multiple='multiple'></select>").hide(),s=J("#"+u),t.data("is-ms-field","true"),s.on("change, change.jbhv, unselect.jbhv, selected.jbhv",(function(e){var n=AJS.$(e.currentTarget),r=n.parent().find("#"+n.data("redirect-to")),i=J.makeArray(n.val());r.val(i.join("; ")),t.trigger("change"+ge)}))}t=qe.findFieldForId(e,n);var f,p={element:s,itemAttrDisplayed:"label",errorMessage:"Invalid value selected"},h=i.getBehaviours()[n],v=h.fieldOptions,m=h.selectOptions||{};(J.isEmptyObject(h.selectOptions)||J.isEmptyObject(h.selectOptions.ajaxOptions)||("single-select"===r&&(v=[I(String(c),String(c))]),"issue"===m.ajaxOptions.formatResponse?m.ajaxOptions.formatResponse=L.IssuePickerUtil.formatResponse:"general"===m.ajaxOptions.formatResponse&&(m.ajaxOptions.formatResponse=L.GeneralPickerUtil.formatResponse)),v&&i.setFieldOptions(e,s,n,v),(0,q.nM)())&&(f={minimumInputLength:m.ajaxOptions.minQueryLength||0,ajax:{url:m.ajaxOptions.url,dataType:"json",delay:250,data:function(e){return N(N({},m.ajaxOptions.data),{query:e})},results:function(e){return{results:m.ajaxOptions.formatResponse(e,!0).map((function(e){return e}))}}},formatResult:function(e){return function(e){return e.icon?'<img src="'.concat(e.icon,'" height="16px" width="16px"> ').concat(e.html):e.html?e.html:e.text}(e)}});if("single-select"===r)if((0,q.nM)())s.select2(f),l.value&&s.select2("data",l);else{var g=new(AJS.SingleSelect.extend({handleFreeInput:function(e){""!==(e=e||J.trim(this.$field.val()))?this.options.revertOnInvalid&&!this.model.getDescriptor(e)?this.setSelection(this.lastSelection||""):this.$container.hasClass("aui-ss-editing")&&(this._setDescriptorWithValue(e)?this.hideErrorMessage():this.options.submitInputVal||this.showErrorMessage(e)):this.hideErrorMessage()}}))(J.extend(p,m));i.setFieldValue(s,c,u),"css"in m&&g.$container.add(g.$container.find("input")).attr("style",m.css)}else if("multi-select"===r){if((0,q.nM)())s.select2(N(N({},f),{multiple:!0})),s.select2("data",l);else{if(!(t.val()instanceof Array||V.Z(t.val()))){var y=t.val().split("; "),b=y.map((function(e){return I(e,e)}));m&&m.ajaxOptions&&i.setFieldOptions(e,s,n,b),i.setFieldValue(s,y,u)}new AJS.MultiSelect(J.extend(p,m))}s.data("redirect-to",t.attr("id"))}}n(19024);var U,W=n(39507),G=n(60247),K=(n(17775),n(37018),n(92688),n(44148)),H=n(88433),z=n(84806),Z=n(25972),X=function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,i,a=n.call(e),o=[];try{for(;(void 0===t||t-- >0)&&!(r=a.next()).done;)o.push(r.value)}catch(e){i={error:e}}finally{try{r&&!r.done&&(n=a.return)&&n.call(a)}finally{if(i)throw i.error}}return o},Q=function(e,t,n){if(n||2===arguments.length)for(var r,i=0,a=t.length;i<a;i++)!r&&i in t||(r||(r=Array.prototype.slice.call(t,0,i)),r[i]=t[i]);return e.concat(r||Array.prototype.slice.call(t))},Y=function(e){return"string"==typeof e?e.replace(/\s/g,""):e},ee=function(e){return function(e){return Q([],X(e[0].querySelectorAll(".textarea, .textfield, input.text.long-field, select.aui-ss-select, select:not([multiple])")),!1).map((function(e){var t=jQuery(e);return[e.id,(0,Z.$u)(t),(0,Z.B7)(t)]}))}(e).filter((function(e){var t=X(e,2)[1];return!!(null==t?void 0:t.trim())}))},te=function(){return te=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e},te.apply(this,arguments)},ne=AJS.$,re=function(e,t){var n=e.label,r=e.value,i=e.icon,a=(e.id,ne("<option/>",{value:r,text:n,title:n})),o=new AJS.ItemDescriptor(te({value:r,label:n,icon:i,id:r},t));return a.data("descriptor",o),a},ie=function(e,t,n,r){return new(n||(n=Promise))((function(i,a){function o(e){try{c(r.next(e))}catch(e){a(e)}}function s(e){try{c(r.throw(e))}catch(e){a(e)}}function c(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,s)}c((r=r.apply(e,t||[])).next())}))},ae=function(e,t){var n,r,i,a,o={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return a={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function s(a){return function(s){return function(a){if(n)throw new TypeError("Generator is already executing.");for(;o;)try{if(n=1,r&&(i=2&a[0]?r.return:a[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,a[1])).done)return i;switch(r=0,i&&(a=[2&a[0],i.value]),a[0]){case 0:case 1:i=a;break;case 4:return o.label++,{value:a[1],done:!1};case 5:o.label++,r=a[1],a=[0];continue;case 7:a=o.ops.pop(),o.trys.pop();continue;default:if(!(i=o.trys,(i=i.length>0&&i[i.length-1])||6!==a[0]&&2!==a[0])){o=0;continue}if(3===a[0]&&(!i||a[1]>i[0]&&a[1]<i[3])){o.label=a[1];break}if(6===a[0]&&o.label<i[1]){o.label=i[1],i=a;break}if(i&&o.label<i[2]){o.label=i[2],o.ops.push(a);break}i[2]&&o.ops.pop(),o.trys.pop();continue}a=t.call(e,o)}catch(e){a=[6,e],r=0}finally{n=i=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,s])}}},oe=function(e,t){return ie(void 0,void 0,void 0,(function(){var n,i,a;return ae(this,(function(o){switch(o.label){case 0:return[4,(0,r.k)("".concat(AJS.contextPath(),"/rest/api/2/issue/").concat(t,"?fields=summary,issuetype"))];case 1:return null==(n=o.sent()).result||(i=n.result.fields.summary,a="".concat(t," - ").concat(i),e.append(re({label:a,value:t,icon:n.result.fields.issuetype.iconUrl})),(0,u.UW)(e,(function(){return e.trigger("set-selection-value",t)}))),[2]}}))}))},se=n(22499),ce=function(e,t){var n=t.split(".");return e.split(".").every((function(e,t){return e>=n[t]}))},le=function(e){return ce(AJS.Meta.get("sdPluginVersion"),e)},ue=function(){return ue=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e},ue.apply(this,arguments)},de=function(e,t,n,r){return new(n||(n=Promise))((function(i,a){function o(e){try{c(r.next(e))}catch(e){a(e)}}function s(e){try{c(r.throw(e))}catch(e){a(e)}}function c(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,s)}c((r=r.apply(e,t||[])).next())}))},fe=function(e,t){var n,r,i,a,o={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return a={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function s(a){return function(s){return function(a){if(n)throw new TypeError("Generator is already executing.");for(;o;)try{if(n=1,r&&(i=2&a[0]?r.return:a[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,a[1])).done)return i;switch(r=0,i&&(a=[2&a[0],i.value]),a[0]){case 0:case 1:i=a;break;case 4:return o.label++,{value:a[1],done:!1};case 5:o.label++,r=a[1],a=[0];continue;case 7:a=o.ops.pop(),o.trys.pop();continue;default:if(!(i=o.trys,(i=i.length>0&&i[i.length-1])||6!==a[0]&&2!==a[0])){o=0;continue}if(3===a[0]&&(!i||a[1]>i[0]&&a[1]<i[3])){o.label=a[1];break}if(6===a[0]&&o.label<i[1]){o.label=i[1],i=a;break}if(i&&o.label<i[2]){o.label=i[2],o.ops.push(a);break}i[2]&&o.ops.pop(),o.trys.pop();continue}a=t.call(e,o)}catch(e){a=[6,e],r=0}finally{n=i=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,s])}}},pe=function(e){var t="function"==typeof Symbol&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},he=function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,i,a=n.call(e),o=[];try{for(;(void 0===t||t-- >0)&&!(r=a.next()).done;)o.push(r.value)}catch(e){i={error:e}}finally{try{r&&!r.done&&(n=a.return)&&n.call(a)}finally{if(i)throw i.error}}return o};n(48963),n(50777);var ve,me=jQuery,ge=".jbhv",ye=!1,be={},ke=0,we=new Set,Se=new Set;!function(e){e.GroupPicker="com.atlassian.jira.plugin.system.customfieldtypes:grouppicker",e.MultiGroupPicker="com.atlassian.jira.plugin.system.customfieldtypes:multigrouppicker",e.UserPicker="com.atlassian.jira.plugin.system.customfieldtypes:userpicker",e.MultiUserPicker="com.atlassian.jira.plugin.system.customfieldtypes:multiuserpicker",e.DatePicker="com.atlassian.jira.plugin.system.customfieldtypes:datepicker",e.MultiVersion="com.atlassian.jira.plugin.system.customfieldtypes:multiversion",e.DueDate="com.atlassian.jira.issue.fields.DueDateSystemField",e.Assignee="com.atlassian.jira.issue.fields.AssigneeSystemField",e.RadioButtons="com.atlassian.jira.plugin.system.customfieldtypes:radiobuttons",e.CheckBoxes="com.atlassian.jira.plugin.system.customfieldtypes:multicheckboxes",e.CascadingSelect="com.atlassian.jira.plugin.system.customfieldtypes:cascadingselect",e.Components="com.atlassian.jira.issue.fields.ComponentsSystemField",e.IssueType="com.atlassian.jira.issue.fields.IssueTypeSystemField",e.InsightCustomField="com.riadalabs.jira.plugins.insight:rlabs-customfield-default-object",e.InsightCustomFieldReference="com.riadalabs.jira.plugins.insight:rlabs-customfield-object-reference",e.InsightCustomFieldReferenceMulti="com.riadalabs.jira.plugins.insight:rlabs-customfield-object-reference-multi",e.Select="com.atlassian.jira.plugin.system.customfieldtypes:select",e.MultiSelect="com.atlassian.jira.plugin.system.customfieldtypes:multiselect",e.Resolution="com.atlassian.jira.issue.fields.ResolutionSystemField",e.ProjectCustomField="com.atlassian.jira.plugin.system.customfieldtypes:project",e.VersionCustomField="com.atlassian.jira.plugin.system.customfieldtypes:version"}(ve||(ve={}));var Fe="#timetracking_originalestimate",xe="#timetracking_remainingestimate",Ie="#log-work-time-logged",Oe="input[name=dnd-dropzone]",je="div.sd-attachment-container",Ce={project:["#project","#issue-create-project-name"],issuetype:["#issuetype","#issue-create-issue-type"],timeoriginalestimate:[Fe],timeestimate:[xe],timespent:[Ie],attachment:[Oe,je]},Ae={submitSelected:!1};me.fn.exists=function(){return 0!==this.length};var Te,Re=[],Pe=["MoveIssueUpdateFields.jspa"],Ee=[ve.InsightCustomField,ve.InsightCustomFieldReference,ve.InsightCustomFieldReferenceMulti],De=(0,l.j)("sr-behaviours"),_e="bhv-required",Le="The administrator has prevented you from creating any issue types in this project. Please select another project or contact your administator";!function(e){e[e.VALID=0]="VALID",e[e.INVALID=1]="INVALID",e[e.NONE=2]="NONE"}(Te||(Te={}));var Ve=null!==(U=(window.WRM||window.parent.WRM).data.claim("com.onresolve.jira.groovy.groovyrunner:behaviours-translations.behaviours-translations-data-provider"))&&void 0!==U?U:{},qe=function(){function e(){var e=this;this.fieldsWithDefaultBehaviourValues=[],this.originalFormValues={},this.getFormValue=Z.$u,be={},me(document).on("Dialog.hide",(function(){e.clearOriginalValuesState()}))}return e.prototype.clearOriginalValuesState=function(){this.originalFormValues={}},e.prototype.setLogLevel=function(e){De.setLevel(e)},Object.defineProperty(e.prototype,"getTabOperations",{get:function(){return this.tabOperations},enumerable:!1,configurable:!0}),e.isFieldRequiringTimer=function(e){var t=[ve.UserPicker,ve.DueDate,ve.DatePicker,ve.GroupPicker,ve.MultiGroupPicker,ve.MultiVersion,ve.MultiUserPicker];return me.inArray(e,t)>-1},e.prototype.checkFields=function(){if(this.timerFieldsNotInDOM())clearInterval(this.fieldTimer);else for(var t in De.debug("checkFields"),be)if(t.length>0){var n=e.findFieldForId(me("body"),t);if(n.length>0&&e.isFieldRequiringTimer(be[t].fieldType)){null==Re[t]&&(Re[t]="");var r=!1;if(null!=n.val()&&null!=Re[t]&&"object"==typeof n.val())try{r=n.val().join("")===Re[t].join("")}catch(e){De.debug("parse error ".concat(t),e)}else r=n.val()===Re[t]||null==n.val()&&!Re[t];r||(De.debug("Field changed, revalidating: ".concat(n.attr("name"),": ").concat(n.val())),Re[t]=n.val(),n.trigger("change".concat(ge)))}}},e.prototype.timerFieldsNotInDOM=function(){return Object.keys(be).filter((function(t){return e.isFieldRequiringTimer(be[t].fieldType)})).map((function(t){return e.getSelectorForBackendId(me("body"),t)})).every((function(e){return null===e}))},e.prototype.getText=function(e){var t;return null!==(t=Ve[e])&&void 0!==t?t:e},e.prototype.getBehaviours=function(){return be},e.prototype.applyReadOnlyAttributes=function(e){return de(this,void 0,void 0,(function(){var t,n;return fe(this,(function(r){switch(r.label){case 0:return e.prop("disabled",!0).addClass("clientreadonly"),[4,(0,K.pO)(e)];case 1:return t=r.sent(),(0,K.KL)(t)&&(t.rteInstance.then((function(e){e.editor.setMode("readonly")})),(n=e.siblings("textarea")).prop("disabled",!0),n.addClass("clientreadonly"),t.toolbar.style.pointerEvents="none"),[2]}}))}))},e.prototype.setFieldValue=function(t,n,i){return de(this,void 0,Promise,(function(){var a,o,s,c;return fe(this,(function(l){switch(l.label){case 0:return De.debug("setFieldValue:",i),(0,K.th)(t)?((0,K.pN)(t,n),[2]):((a=me("#".concat(i,"-ms"))).length>0&&(t=a),i in be||(be[i]={},be[i].fieldType=""),e.isCheckboxOrRadio(i)?(e.setRadioOrCheckboxFieldValue(n,i),[3,16]):[3,1]);case 1:return(0,Z.z8)(t)?(t.data("behavioursShim").setValue(n),[3,16]):[3,2];case 2:return"commentLevel"!==t.attr("name")?[3,3]:(t.find("option").removeAttr("selected"),o=t.find("option[value='".concat(n,"']")),s=t.prev("div").find(".security-level-drop-icon"),n&&o.length?s.removeClass("aui-iconfont-unlocked").addClass("aui-iconfont-locked"):s.addClass("aui-iconfont-unlocked").removeClass("aui-iconfont-locked"),o.attr("selected","selected"),c=n?AJS.params.securityLevelViewableRestrictedTo:AJS.params.securityLevelViewableByAll,t.parent().find(".current-level").html(AJS.format(c,t.find("option:selected").html())),[3,16]);case 3:return t.hasClass("single-user-picker")?[4,this.doGetUserPicker(t,n).catch((function(e){De.error("Failed to get and set user",e)}))]:[3,5];case 4:return l.sent(),[3,16];case 5:return"comment"!==t.attr("name")?[3,6]:(t.is(":visible")&&t.val(n),[3,16]);case 6:return Ee.includes(be[i].fieldType)?[4,this.setInsightFieldValue(t,n,i)]:[3,8];case 7:return l.sent(),[3,16];case 8:return t.hasClass("multi-select-select")?("string"==typeof n?n=""===n?[]:n.split(",").map((function(e){return e.trim()})):"number"==typeof n?n=[n]:n||(n=[]),n=n.map((function(e){return String(e)})),De.debug("Set value to : ",n),t.hasClass("edit-labels-inline")?this.setLabelField(t,n):F(t,n),[3,16]):[3,9];case 9:return["com.pyxis.greenhopper.jira:gh-sprint"].includes(be[i].fieldType)?((u=n,C(void 0,void 0,void 0,(function(){var e;return A(this,(function(t){switch(t.label){case 0:return[4,(0,r.k)("".concat(AJS.contextPath(),"/rest/agile/1.0/sprint/").concat(u))];case 1:if((e=t.sent()).error)throw console.log("Erroring, can't set sprint, status:",e.error),new Error("Sprint not found");return[2,e.result]}}))}))).then((function(e){return function(e,t){var n=t.id,r=t.name,i=n.toString(),a=$("<option/>",{text:r,value:i,title:r});a.data("descriptor",new AJS.ItemDescriptor({value:i,label:r})),e.append(a),e.trigger("set-selection-value",i)}(t,e)})).catch((function(e){De.error("Failed to set sprint field",e)})),[3,16]):[3,10];case 10:return t.hasClass("aui-ss-select")?t.hasClass("js-epic-picker")?(function(e,t){return C(void 0,void 0,void 0,(function(){var n,i,a,o,s,c,l;return A(this,(function(u){switch(u.label){case 0:return[4,(0,r.k)("".concat(AJS.contextPath(),"/rest/api/2/field"))];case 1:if(n=u.sent(),i=n.result,!(a=_.find(i,(function(e){return e.schema&&e.schema.custom&&"com.pyxis.greenhopper.jira:gh-epic-label"===e.schema.custom}))))throw"Failed to find ID of Epic Link field";return e||x(t),[4,(0,r.k)("".concat(AJS.contextPath(),"/rest/api/2/issue/").concat(e,"?fields=").concat(a.id))];case 2:if(o=u.sent(),s=o.result,c="key:"+s.key,!s.fields||!s.fields[a.id])throw new Error("Failed to read epic name field from issue: "+e+" - perhaps it wasn't an epic");return(l=$("<option/>",{text:s.fields[a.id],value:c})).data("descriptor",new AJS.ItemDescriptor({value:c,label:s.fields[a.id]})),t.append(l),t.trigger("set-selection-value",c),[2]}}))}))}(n,t).catch((function(e){De.error("Failed to set epic field",e)})),[3,14]):[3,11]:[3,15];case 11:return t.hasClass("jpo-parent-selection-dropdown")?[4,oe(t,n)]:[3,13];case 12:return l.sent(),[3,14];case 13:t.trigger("set-selection-value",n),l.label=14;case 14:return[3,16];case 15:t.hasClass("cascadingselect-parent")?(t.parent().find("select:first").val(n[0]),n.length>1&&(t.parent().find("select:first").trigger("change"),t.parent().find("select:nth(1)").val(n[1]))):t.val(n),l.label=16;case 16:return[2]}var u}))}))},e.setRadioOrCheckboxFieldValue=function(e,t){e=Array.isArray(e)?e:[e],Array.from(document.querySelectorAll("[name=".concat(t,"]"))).forEach((function(t){t.checked=e.map((function(e){return e.toString()})).includes(t.value)}))},e.prototype.bindRTETextFieldEvents=function(e,t,n,r){return void 0===n&&(n=ge),void 0===r&&(r=this.postValidator),de(this,void 0,void 0,(function(){var i,a=this;return fe(this,(function(o){switch(o.label){case 0:return[4,(0,K.pO)(e)];case 1:return i=o.sent(),(0,K.KL)(i)&&(i.onSwitchToText((function(e){me(e.textArea).off("change".concat(n),r).on("change".concat(n),{docRoot:t},r)})),i.rteInstance.then((function(e){var t=e.editor;t&&t.on("blur",r===a.postValidator?a.postValidatorForRTE:r)}))),[2]}}))}))},e.prototype.unBindRTETextFieldEvents=function(e){return de(this,void 0,void 0,(function(){var t,n=this;return fe(this,(function(r){switch(r.label){case 0:return[4,(0,K.pO)(e)];case 1:return t=r.sent(),(0,K.KL)(t)&&(me(t.textArea).off("change".concat(ge),this.postValidator),t.rteInstance.then((function(e){var t=e.editor;t&&t.off("blur",n.postValidatorForRTE)}))),[2]}}))}))},e.prototype.stubMissingField=function(e){e in be||(be[e]={},be[e].fieldType="")},e.prototype.hideElementWhenCreated=function(e,t){new MutationObserver((function(n,r){e.querySelector(t)&&(me(e).find(t).hide(),r.disconnect())})).observe(e,{childList:!0,subtree:!0,attributes:!1,characterData:!1})},e.prototype.makeReadOnly=function(t){var n,r,i,a=this;De.debug("makeReadOnly:",t.attr("id"));var o=function(){if(be[i].fieldType===ve.InsightCustomField){t.closest("div.field-group").find("button.rlabs-customfield-object-picker, span.rlabs-customfield-object-picker, div.rlabs-cs-customfield-object-picker").hide();var e=me("#".concat(i,"_multi"));e.length>0&&e.select2("readonly",!0)}};if(t.find(":radio").length>0)t.find(":radio").each((function(e,t){a.applyReadOnlyAttributes(me(t))}));else if(t.find(":checkbox").length>0)t.find(":checkbox").each((function(e,t){a.applyReadOnlyAttributes(me(t))}));else{if(i=e.getFieldNameForField(t),this.stubMissingField(i),t.hasClass("cascadingselect-parent"))t.parent().find("select").each((function(e,t){a.applyReadOnlyAttributes(me(t))}));else if((0,Z.z8)(t))return void t.data("behavioursShim").makeReadOnly();if([ve.GroupPicker,ve.MultiGroupPicker].includes(be[i].fieldType))me("#".concat(i)).parent().find("a.grouppicker-trigger").hide();else if(t.hasClass("aui-ss-select"))this.disableIconClick(t),t=t.parent().find("input.aui-ss-field"),this.applyReadOnlyAttributes(t),t.parent().find("span.drop-menu").hide(),o();else if(t.hasClass("multi-select-select")){var s=t.parent();try{for(var c=pe(["textarea","select"]),l=c.next();!l.done;l=c.next()){var u=l.value;s.find(u).addClass("clientreadonly"),s.find(u).prop("disabled",!0)}}catch(e){n={error:e}}finally{try{l&&!l.done&&(r=c.return)&&r.call(c)}finally{if(n)throw n.error}}s.find("span.drop-menu").hide(),s.find("a.issue-picker-popup").hide(),t.parent().find("div.representation").find("li.item-row button").on("click.jbhv",(function(e){e.stopPropagation()})),this.hideElementWhenCreated(s.get(0),"em"),o()}else if(t.parent().is("div.aui-field-datepicker")||t.is("input.date-picker"))t.parent().find("a").hide(),t.parent().find(".show-date-picker").hide();else if("commentLevel"===i){var d=t.closest("div.field-group");d.find("#commentLevel-multi-select a.drop span:last").removeClass("drop-menu");var f=d.find("#commentLevel-multi-select a.drop");f.data("disabled-dropdown-handler",this.preventEvent),this.bindFirst(f,"click",this.preventEvent,null)}else t.hasClass("insight-init")&&o();[ve.UserPicker,ve.MultiUserPicker].includes(be[i].fieldType)&&me("#".concat(i)).closest(".field-group").find(".popup-trigger").hide(),"assignee"===i&&me("#".concat(i)).parent().find("#assign-to-me-trigger").hide(),["issuetype-field","project-field"].includes(i)&&t.parent().find("span.drop-menu").hide(),t.each((function(e,t){a.applyReadOnlyAttributes(me(t))}))}},e.prototype.enableIconClick=function(e){var t=e.parent().find("img.aui-ss-entity-icon")[0];t&&(t.style.pointerEvents="auto")},e.prototype.disableIconClick=function(e){var t=e.parent().find("img.aui-ss-entity-icon")[0],n=e.parent().find("div.aui-ss-select")[0];t&&n&&(t.style.pointerEvents="none",n.style.cursor="not-allowed")},e.prototype.removeReadOnlyAttributes=function(e){return de(this,void 0,void 0,(function(){var t,n;return fe(this,(function(r){switch(r.label){case 0:return e.attr("title",""),e.prop("disabled",!1),e.removeClass("clientreadonly"),[4,(0,K.pO)(e)];case 1:return(t=r.sent())&&(0,K.KL)(t)&&(t.rteInstance.then((function(e){var t=e.editor;if("4"===t.editorManager.majorVersion)t.setMode("code");else try{t.mode.set("code")}catch(e){return void console.warn("Could not set TinyMCE to mode: 'code'",e)}})),(n=e.siblings("textarea")).attr("title",""),n.prop("disabled",!1),n.addClass("clientreadonly"),t.toolbar.style.pointerEvents="all"),[2]}}))}))},e.prototype.makeAllFieldsWritable=function(e){for(var t in be)be[t].readonly&&this.makeFieldWritable(e,t)},e.prototype.makeFieldWritable=function(t,n){De.debug("Submit form make writable: ".concat(n));var r=e.isCheckboxOrRadio(n)?t.find("input[name='".concat(n,"']")).parent():e.findFieldForId(t,n);r.length>0&&(this.makeWritable(r),we.add(n))},e.prototype.makeWritable=function(t){var n,r,i,a=this;if(De.debug("makeWritable",t.attr("id")),t.find(":radio").length>0)t.find(":radio").each((function(e,t){a.removeReadOnlyAttributes(me(t))}));else if(t.find(":checkbox").length>0)t.find(":checkbox").each((function(e,t){a.removeReadOnlyAttributes(me(t))}));else{i=e.getFieldNameForField(t),this.stubMissingField(i);var o=function(){if(be[i].fieldType===ve.InsightCustomField){t.closest("div.field-group").find("button.rlabs-customfield-object-picker, span.rlabs-customfield-object-picker, div.rlabs-cs-customfield-object-picker").show();var e=me("#".concat(i,"_multi"));e.length>0&&e.select2("readonly",!1)}};if(t.hasClass("cascadingselect-parent"))t.parent().find("select").each((function(e,t){a.removeReadOnlyAttributes(me(t))}));else{if((0,Z.z8)(t))return void t.data("behavioursShim").makeWritable();if(be[i].fieldType===ve.GroupPicker)me("#".concat(i)).parent().find("a.grouppicker-trigger").show();else if(t.hasClass("aui-ss-select"))t=t.parent().find("input.aui-ss-field"),this.removeReadOnlyAttributes(t),this.enableIconClick(t),t.parent().find("span.drop-menu").show(),o();else if(t.hasClass("multi-select-select")){var s=t.parent();try{for(var c=pe(["textarea","select"]),l=c.next();!l.done;l=c.next()){var u=l.value;s.find(u).removeClass("clientreadonly"),s.find(u).prop("disabled",!1)}}catch(e){n={error:e}}finally{try{l&&!l.done&&(r=c.return)&&r.call(c)}finally{if(n)throw n.error}}t.parent().find("div.representation").find("li.item-row button").off("click.jbhv"),s.find("span.drop-menu").show(),s.find("a.issue-picker-popup").show(),o()}else if(t.parent().is("div.aui-field-datepicker")||t.is("input.date-picker"))t.parent().find("a").show(),t.parent().find(".show-date-picker").show();else if("commentLevel"===i){var d=t.closest("div.field-group");d.find("#commentLevel-multi-select a.drop span:last").addClass("drop-menu");var f=d.find("#commentLevel-multi-select a.drop"),p=f.data("disabled-dropdown-handler");f.off("click",p)}else t.hasClass("insight-init")&&o()}[ve.UserPicker,ve.MultiUserPicker].includes(be[i].fieldType)&&me("#".concat(i)).parent().find(".popup-trigger").show(),"assignee"===i&&me("#".concat(i)).parent().find("#assign-to-me-trigger").show(),["issuetype-field","project-field"].includes(i)&&t.parent().find("span.drop-menu").show(),De.debug("Pre remove read only attr"),t.each((function(e,t){a.removeReadOnlyAttributes(me(t))}))}},e.prototype.preventEvent=function(e){return De.debug("preventing event",e.name),e.preventDefault(),e.stopImmediatePropagation(),!1},e.prototype.triggerValidator=function(e){De.debug("triggerValidator"),e.blur()},e.prototype.submitForm=function(e){return De.debug("submitForm"),(0,r.e)()>0?(Ae.submitSelected=!0,this.preventEvent(e)):this.submitFormAllowed(e.data.docRoot)?(this.clearOriginalValuesState(),!0):this.preventEvent(e)},e.prototype.submitFormAllowed=function(t){var n,r;De.debug("submitFormAllowed");var i,a=!1;me("div.jbhverror").remove();var o=me("#issuetype-field");if(""===o.val())return o.closest(".field-group").append('<div class="error jbhverror">'.concat(Le,"</div>")),!1;try{for(var s=pe(Object.getOwnPropertyNames(this.getBehaviours())),c=s.next();!c.done;c=s.next()){if((b=c.value).length>0){De.debug("Submit field: ".concat(b));var l=void 0;if((l=e.isCheckboxOrRadio(b)?me("input[name='".concat(b,"']:first")):"issuelinks"===b?me(t).find("#issuelinks-issues"):"timeoriginalestimate"===b?me(Fe):"timeestimate"===b?me(xe):"timespent"===b?me(Ie):me(t).find("#".concat(b))).length>0&&(!0===be[b].required&&this.validateRequiredField({currentTarget:l}),be[b].title||be[b].errorMsg)){a=!0;var u=e.findFieldForId(t,b);this.setErrorText(u,be[b]),i||(i=u);var d=l.closest(".tabs-pane").not(".active-pane").attr("id");if(void 0!==d){var f=me("a[href='#".concat(d,"'] strong"));f.parent().addClass("adaptavist-sr"),f.addClass("tabinvalid"),f.on("click",(function(e){me(e.currentTarget).removeClass("tabinvalid")}))}}}}}catch(e){n={error:e}}finally{try{c&&!c.done&&(r=s.return)&&r.call(s)}finally{if(n)throw n.error}}if(a){var p=!!navigator.webdriver||!!window.currentSpec?0:500,h="DIV"===t.prop("tagName"),v=i.closest("div.field-group, fieldset.group"),m=h?v.parents(".aui-dialog2-content, .form-body").last():me(window),g=i.closest("div.tabs-pane");if(!g.length||g.is(".active-pane")||"comment"===i.attr("id"))if("DIV"===t.prop("tagName")){var y=v.offset().top-m.offset().top+m.scrollTop()-m.height()/2;m.animate({scrollTop:y},p)}else{y=v.offset().top-m.height()/2;me("html, body").animate({scrollTop:y},p)}return!1}for(var b in be)b.length>0&&(be[b].readonly&&this.makeFieldWritable(t,b),be[b].required&&Se.add(b));return!0},e.getFieldNameForField=function(e){return e.find(":radio").length>0?e.find(":radio:first").attr("name"):e.is(":radio")?e.attr("name"):e.find(":checkbox").length>0?e.find(":checkbox")[0].name:e.is("#issuelinks-issues-multi-select")||["issuelinks-issues","issuelinks-linktype"].includes(e.attr("name"))?"issuelinks":e.is(Fe)?"timeoriginalestimate":e.is(xe)?"timeestimate":e.is(Ie)?"timespent":e.is(je)?"attachment":e.attr("name")?e.attr("name").replace(/_multi$/,""):e.attr("id")?e.attr("id").replace(/^s2id_/,"").replace(/_multi$/,""):void 0},e.notEmptyOrBlank=function(e){return!!e.val()&&!!e.val().toString().trim()},e.prototype.validateRequiredField=function(t){var n,r,i=me(t.currentTarget),a=e.getFieldNameForField(i);(De.debug("Validate field: ".concat(a)),be[a])?be[a].fieldType===ve.RadioButtons?this.validateRadioButtons(i,a):i.hasClass(this.getSelect2CssClass())||(r=be[a].fieldType,["com.tempoplugin.tempo-accounts:accounts.customfield","com.tempoplugin.tempo-teams:team.customfield"].includes(r))?be[a].title=(n=i.val())&&"-1"!==n.toString()&&""!==n&&!s.Z(n,[""])?"":this.getText("field.title.missing.value"):"select-multiple"===i.attr("type")&&null!=i.val()&&"-1"!==i.val()?be[a].title="":be[a].fieldType===ve.CheckBoxes?this.validateMultiCheckboxes(i,a):(0,Z.z8)(i)&&i.data("behavioursShim").getValue()||!i.hasClass(this.getSelect2CssClass())&&e.notEmptyOrBlank(i)?be[a].title="":be[a].title=this.getText("field.title.missing.value"):De.warn("Failed to find type of field: ".concat(a))},e.prototype.validateRadioButtons=function(e,t){var n=e.closest("fieldset").find("input:radio:checked").val();n&&"-1"!==n?be[t].title="":(be[t].title=this.getText("field.title.missing.value"),this.requireField(e))},e.prototype.validateMultiCheckboxes=function(e,t){e.closest("fieldset.group").find("input:checkbox:checked").length>0?be[t].title="":(be[t].title=this.getText("field.title.missing.value"),this.requireField(e))},e.prototype.getSelect2CssClass=function(){return"select"},e.prototype.requireField=function(t){var n=e.getFieldNameForField(t);De.debug("Require Field: ".concat(n));var r=this.getFieldLabel(t);this.isRequired(r)?r.find("span.icon-required").addClass(_e):r.append('<span class="content visually-hidden">Required</span><span class="aui-icon icon-required '.concat(_e,'"></span>'))},e.prototype.unRequireField=function(t){var n=e.getFieldNameForField(t);De.debug("Un-require Field: ".concat(n));var r=this.getFieldLabel(t);r.length||(r=me(":input[name='".concat(n,"']")).parent().parent().find("legend > span"));var i=r.find("span.icon-required.".concat(_e));i.next("span").remove(),i.remove(),r.find("span.visually-hidden").remove()},e.prototype.randomString=function(){for(var e="0123456789ABCDEFGHIJKLMNOPQRSTUVWXTZabcdefghiklmnopqrstuvwxyz",t="",n=0;n<8;n++){var r=Math.floor(Math.random()*e.length);t+=e.substring(r,r+1)}return t},e.prototype.constructForm=function(e){var t,n={};return(t=me(e).find(":input")?me(e):e.find("form.aui")).find(":input, div.behaviours-common-api").each((function(e,t){var r=me(t);if(!r.is("input[type=hidden]")||!r.closest("div.field-group").find("select.js-sprint-picker").length){var i=r.attr("name");(0,Z.z8)(r)?n[r.attr("id")]=(0,Z.$u)(r):n[i]=(0,Z.$u)(r)}})),Object.assign(n,this.getCoordinates(t)),n},e.prototype.postValidator=function(t){var n=t.target;if(!(0,u.Kg)(n)){var r,i="blur"===t.type&&n.targetElm&&n.targetElm.$textarea,a=i?n.targetElm.$textarea.attr("id"):e.getFieldNameForField(me(t.currentTarget));r=i?me(n.formElement).parent():t.data.docRoot.is("form")?me(t.data.docRoot).parent():t.data.docRoot.find("form.aui:has(#".concat(t.currentTarget.id,")")).parent();var o=this.constructForm(r);return De.debug("posting runvalid + ",o,a),this.doPostRunValidator(a,o,r)}},e.prototype.postValidatorForRTE=function(e){var t=e.target.targetElm.$textarea.attr("id"),n=e.target.formElement,r=this.constructForm(n);return De.debug("posting runvalid RTE + ",r,t),this.doPostRunValidator(t,r,n)},e.findFieldForId=function(t,n){return me(t).find(e.getSelectorForBackendId(t,n))},e.getSelectorForBackendId=function(t,n){var r,i;if(n.length>0){if(!((n=CSS.escape(n))in Ce)){if(e.isCheckboxOrRadio(n)){var a=me(t).find("input[name='".concat(n,"']"));return a.length<=0&&(n=me(t).find("#".concat(n)).attr("name"),a=me(t).find("input[name='".concat(n,"']"))),a.closest("fieldset").attr("data-jbhv-checkbox-radio-fieldset-name",n),a.parent().attr("data-jbhv-checkbox-radio-fieldname",n),"[data-jbhv-checkbox-radio-fieldname=".concat(n,"]")}return"issuelinks"===n?"#issuelinks-issues,#issuelinks-linktype":me("#".concat(n)).length?"#".concat(n):me('select[name="'.concat(n,'"]')).length?me('select[name="'.concat(n,'"]')):(De.debug("no selector for ID:",n),null)}var o=Ce[n];try{for(var s=pe(o),c=s.next();!c.done;c=s.next()){var l=c.value;if(me(t).find(l).length>0)return l}}catch(e){r={error:e}}finally{try{c&&!c.done&&(i=s.return)&&i.call(s)}finally{if(r)throw r.error}}}},e.closestFieldGroup=function(e){return e.is(Oe)?e.closest("fieldset.group"):e.closest("div.field-group, fieldset.group")},e.prototype.setHidden=function(t,n){var r=e.closestFieldGroup(t);n?r.hide():r.show()},e.prototype.getFieldLabel=function(e){var t="string"==typeof e?me(e):e;return t.is(".radio, .checkbox, input[name=dnd-dropzone]")?t.closest("fieldset.group").find("legend span"):t.closest(".field-group").find("label").not(".select2-offscreen")},e.prototype.isRequired=function(e){return e.find("span.icon-required").length>0},e.prototype.setFieldLabel=function(e,t){var n=this.getFieldLabel(e),r=this.isRequired(n);n.empty().append(t),r?this.requireField(e):this.unRequireField(e)},e.prototype.setUserData=function(e,t){(0,Z.z8)(e)?e.data("behavioursShim").setUserData(t):De.warn("Unsupported operation 'setUserData' on field: ",e)},e.prototype.setErrorText=function(e,t){var n=t.errorMsg,r=t.title,i=t.hidden,a=t.readonly,o=t.helpText,s=n||r||o,c=this.getFieldLabel(e),l=(c.text(),e.attr("id")),u=me("[field-id='".concat(l,"']"));u.length&&(e=me(u));var d=e.closest("div.field-group, fieldset.group"),f=d.length?d:e.parent();if(f.find("div.jbhverror").remove(),s){var p=f.find("div.error:last"),h=e.closest(".qf-field"),v='<div class="error jbhverror">'.concat(s,"</div>"),m=h.is(":hidden")||a;if(i&&(n||this.isRequired(c)||m))return De.warn("Preventing the form from being submitted. ".concat(t.displayName," field is required or in an error state, but the administrator hid it. Please contact your administrator.")),(0,G.Sj)("There are fields that are required or in an error state, but the administrator has hidden them "+"or made them readonly using a behaviour.<p id='jbhv-submit-error'><b>".concat(t.displayName,"</b>: ").concat(s,"</p><p>Please contact your administrator.</p>"));var g=f.find("div.description:first");p.length>0?p.before(v):g.length?g.before(v):f.append(v),De.warn("Preventing the form from being submitted. \n ".concat(t.displayName,": ").concat(s))}},e.prototype.getFieldForCss=function(e,t){return e.closest("div.field-group").find("#".concat(t,"-field")).length?e.closest("div.field-group").find("#".concat(t,"-field")):"wiki-edit-content"===e.parent().attr("class")?e.parent().parent():e},e.prototype.setValidity=function(e,t,n){if((0,Z.z8)(t))t.data("behavioursShim").setValid(n);else{var r=this.getFieldForCss(t,e);n===Te.VALID?r.removeClass("invalid").addClass("clientok"):n===Te.INVALID?r.removeClass("clientok").addClass("invalid"):r.removeClass("clientok").removeClass("invalid")}},e.prototype.setDescription=function(t,n){var r=e.closestFieldGroup(t),i=r.find("div.description");i.length?i.empty().append(n):r.append(me("<div/>",{class:"description",html:n}))},e.prototype.setFieldOptions=function(t,n,r,i){De.debug("Setting field options for ".concat(r)),De.debug(i),k(n=t.find("#".concat(r,"-ms")).length?t.find("#".concat(r,"-ms")):n,r,be[r].fieldType,i),e.isCheckboxOrRadio(r)&&this.bindChangeEvents(r,n,t,be)},e.prototype.bindChangeEvents=function(t,n,r,i,a,o){return void 0===a&&(a=ge),void 0===o&&(o=this.postValidator.bind(this)),de(this,void 0,void 0,(function(){var s,c,l,u,d,f,p,h,v=this;return fe(this,(function(m){switch(m.label){case 0:return De.debug("bindChangeEvents"),s=n.parent(),e.isCheckboxOrRadio(t)?(r.find("[data-jbhv-checkbox-radio-fieldset-name=".concat(t,"] input")).off("change".concat(a)).on("change".concat(a),{docRoot:r},o),[3,10]):[3,1];case 1:return"issuelinks"!==t?[3,2]:(r.find("#issuelinks-linktype").off("change".concat(a)).on("change".concat(a),{docRoot:r},o),r.find("#issuelinks-issues").off("unselect".concat(a)).on("unselect".concat(a),{docRoot:r},o).on("selected".concat(a),{docRoot:r},o),[3,10]);case 2:return i.fieldType!==ve.CascadingSelect?[3,3]:(De.debug("Add select fns"),s.find("select").off("change".concat(a)).on("change".concat(a),{docRoot:r},o),[3,10]);case 3:return["aui-ss-select","multi-select-select","js-epic-picker"].some((function(e){return n.hasClass(e)}))?(n.off("unselect".concat(a)).on("unselect".concat(a),{docRoot:r},o),n.off("selected".concat(a)).on("selected".concat(a),{docRoot:r},o),[3,10]):[3,4];case 4:return(0,Z.z8)(n)?(n.off("change".concat(a)).on("change".concat(a),{docRoot:r},o),[3,10]):[3,5];case 5:return this.bindRTETextFieldEvents(n,r,ge,o),c=me("#".concat(t,"_multi")),(0,q.nM)()&&i.fieldType===ve.InsightCustomField?le("5.2.0")?[4,(0,z.X7)(s.get(0),".atlas-select__input")]:[3,7]:[3,9];case 6:return l=m.sent(),1===s.find(".atlas-select__value-container--is-multi").length?((u=(0,se.s)(l,10)).jbhvMonkeyPatchedOnChange||(u.jbhvMonkeyPatchedOnChange=!0,d=u.onChange,u.onChange=function(e,t,i){void 0===i&&(i=!1),d(e,t),i||o({type:"change",currentTarget:n.get(0),data:{docRoot:r}})}),(f=new MutationObserver((function(){var e=document.querySelector("#rlabs-insight-dialog");if(e){f.disconnect();var t=new MutationObserver((function(){return de(v,void 0,void 0,(function(){var i,a;return fe(this,(function(c){return document.querySelector("#".concat(e.id))||(t.disconnect(),i=s.find("select"),(a=new MutationObserver((function(e){a.disconnect(),o({type:"change",currentTarget:n.get(0),data:{docRoot:r}})}))).observe(i.get(0),{attributes:!0}),f.observe(document,{childList:!0,subtree:!0})),[2]}))}))}));t.observe(e,{childList:!0,subtree:!0})}}))).observe(document,{childList:!0,subtree:!0})):s.find(".atlas-select__value-container").on("DOMNodeInserted DOMNodeRemoved",".atlas-select__single-value",{docRoot:r},(function(){return o({type:"change",currentTarget:n.get(0),data:{docRoot:r}})})),[3,9];case 7:return[4,(0,z.X7)(s.get(0),".insight-init")];case 8:m.sent(),p=0===c.length?"s2id_".concat(t):"s2id_".concat(t,"_multi"),me("#".concat(p)).parent().on("DOMNodeInserted","#".concat(p),{docRoot:r},(function(e){e.target.getAttribute("id")===p&&o(e)})),m.label=9;case 9:h="change".concat(a),c.length>0?c.off(h).on(h,{docRoot:r},o):n.off(h).on(h,{docRoot:r},o),m.label=10;case 10:return[2]}}))}))},e.prototype.setFieldDefaultValues=function(t,n){n.forEach((function(n){var r=he(n,2),i=r[0],a=r[1],o=e.findFieldForId(t,i)[0];["text","textarea"].includes(null==o?void 0:o.type)&&(JIRA.Version.isGreaterThanOrEqualTo("8.19.0")?o.defaultValue=a:Array.isArray(a)?o.defaultValue=a.join(", "):o.defaultValue=a.replace(/\n/gm,"\n "))}))},e.prototype.addFieldListeners=function(t,n,r){return de(this,void 0,void 0,(function(){var i,a,o,s,c,l,u,d,f,p,h,v,m,g,y,b,k,w,S,F,x,I;return fe(this,(function(O){switch(O.label){case 0:if(De.debug("Adding listeners... data:",n,"firstRun:",r),null===n||me.isEmptyObject(n))return De.debug("There are no behaviours attached."),[2];for(s in i=!1,n.__TABS__&&(G.ty(n.__TABS__),this.tabOperations=n.__TABS__,delete n.__TABS__),a=me.extend(!0,{},be),(j=n).issueTypeId&&(j.issuetype=ue(ue({},j.issuetype),j.issueTypeId),delete j.issueTypeId),be=j,r&&(this.showDialogWithUserValues(t),this.fieldsWithDefaultBehaviourValues=function(e){return Object.entries(e).filter((function(e){return"setValue"in X(e,2)[1]})).map((function(e){var t=X(e,2);return[t[0],t[1].setValue]}))}(be)),o=[],be)o.push(s);c=0,O.label=1;case 1:return c<o.length?(l=o[c]).length>0?(u=be[l],(d=e.findFieldForId(t,l)).length>0?(De.debug("Field: ".concat(l)),[4,this.addBehaviourToField(l,u,i,d,t)]):[3,3]):[3,4]:[3,5];case 2:return i=O.sent(),[3,4];case 3:De.debug("Field not found: ".concat(l)),O.label=4;case 4:return c++,[3,1];case 5:try{for(f=pe(Object.entries(be)),p=f.next();!p.done;p=f.next())if(h=he(p.value,2),v=h[0],m=h[1],v in a)try{for(x=void 0,g=pe(Object.entries(be[v])),y=g.next();!y.done;y=g.next())b=he(y.value,2),k=b[0],w=b[1],a[v][k]=w}catch(e){x={error:e}}finally{try{y&&!y.done&&(I=g.return)&&I.call(g)}finally{if(x)throw x.error}}else a[v]=m}catch(e){S={error:e}}finally{try{p&&!p.done&&(F=f.return)&&F.call(f)}finally{if(S)throw S.error}}return be=a,r&&(i&&(this.fieldTimer=setInterval(this.checkFields.bind(this),300)),this.setFieldDefaultValues(t,this.fieldsWithDefaultBehaviourValues)),[2]}var j}))}))},e.prototype.addBehaviourToField=function(t,n,r,i,a){return de(this,void 0,void 0,(function(){var o,s,c,l,u,d,f=this;return fe(this,(function(p){switch(p.label){case 0:return De.debug("Field: ".concat(t)),void 0===n?[3,5]:(!0===(o=be[t]).hidden?this.setHidden(i,!0):!1===o.hidden&&this.setHidden(i,!1),e.isFieldRequiringTimer(o.fieldType)&&(De.debug("Field requires timer: ",t),r=!0),o.fieldOptions&&(s=o.fieldOptions,this.setFieldOptions(a,i,t,s),i=e.findFieldForId(a,t)),!0===o.required?(De.debug("Make required: ".concat(t)),this.requireField(i)):!1===o.required&&(o.title="",this.unRequireField(i)),void 0!==o.helpText&&this.setErrorText(i,o),void 0!==o.description&&this.setDescription(i,o.description),"server"!==o.validator?[3,2]:[4,this.bindChangeEvents(t,i,a,n)]);case 1:p.sent(),p.label=2;case 2:return void 0!==o.label&&this.setFieldLabel(i,o.label),void 0!==o.userData&&this.setUserData(i,o.userData),!0===o.readonly?(De.debug("Make readonly: ".concat(t)),this.makeReadOnly(i)):!1===o.readonly&&this.makeWritable(i),void 0!==o.overlay&&(c=o.overlay,De.debug(c),l=this.randomString(),De.debug("Overlay id: ".concat(l)),me("body").append("\n                    <div class='adaptavist-sr'>\n                        <div class='simple_overlay show-overlay' id='".concat(l,"'>\n                            <a class='close' /> ").concat(c,"\n                        </div>\n                        <div id='").concat(l,"exposeMask' class='expose-mask'></div>\n                     </div>\n                    ")),me(".simple_overlay .close").on("click",(function(){me("#".concat(l,"exposeMask")).fadeOut("slow",(function(){me(f).removeClass("expose-mask")})),me("#".concat(l)).removeClass("show-overlay")}))),void 0!==o.convertTo&&(u=o.convertTo,B(a,i,t,u,this)),void 0===(d=o.setValue)?[3,4]:[4,this.setFieldValue(i,d,t)];case 3:p.sent(),p.label=4;case 4:"object"==typeof o.parameters&&i.data("behavioursShim").setUserData(o.parameters),"invalid"===o.validity?this.setValidity(t,i,Te.INVALID):"valid"===o.validity?this.setValidity(t,i,Te.VALID):this.setValidity(t,i,Te.NONE),void 0!==o.errorMsg&&(o.title=o.errorMsg),p.label=5;case 5:return[2,r]}}))}))},e.isCheckboxOrRadio=function(e){if(be[e]&&be[e].fieldType){var t=be[e].fieldType;return t===ve.RadioButtons||t===ve.CheckBoxes}var n="#".concat(e," , input[name='").concat(e,"']"),r=me(n).attr("type");return"checkbox"===r||"radio"===r},e.prototype.bindFirst=function(e,t,n,r){e.off(t,n),e.on(t,r,n),e.each((function(e,n){var r=me(n),i=r.data("events")[t];i.unshift(i.pop()),r.data("events")[t]=i}))},e.prototype.unbindChangeEvents=function(t,n,r,i){void 0===i&&(i=ge),e.isCheckboxOrRadio(t)?n.find("input").off("click".concat(i)):r.fieldType===ve.CascadingSelect?n.parent().find("select").off("change".concat(i)):r.fieldType===ve.IssueType?n.closest("form").find("#issuetype-field").siblings("span.drop-menu").show().addBack().off(ge):n.off("change".concat(i)),this.unBindRTETextFieldEvents(n)},e.prototype.clearAllBehaviours=function(){for(var t in De.debug("New content added, clearing outstanding errors..."),be)if(t){var n=e.findFieldForId(me("body"),t);n.length&&(De.debug("Clearing conditions on: ".concat(t)),n.parent().find("div.jbhverror").remove(),be[t].helpText="",be[t].title="",be[t].errorMsg="",be[t].required&&(this.unRequireField(n),be[t].required=!1),be[t].readonly&&this.makeWritable(n),this.unbindChangeEvents(t,n,be[t]))}this.checkFields()},e.prototype.getPid=function(e){return(0,Z.wy)(e.find("#project,input[name=pid]").val())},e.prototype.setBehaviours=function(e){be=e},e.prototype.initialiseBehaviours=function(t,n,r,i){var a=this;ye=i;var o=!1;r||(o=!0),n&&n.is(".qf-field")&&n.is(".qf-field-active")?(n=me(document).find("div.jira-dialog-content, div.jira-dialog-core-content"),r="configuredField"):n&&"HEADER"===n.prop("tagName")&&"stalker"===n.attr("id")&&(n=me(document),o=!0),o||r&&[JIRA.CONTENT_ADDED_REASON.dialogReady,JIRA.CONTENT_ADDED_REASON.pageLoad,"configuredField"].includes(r)&&(o=!0),o&&(this.canSwitchIssueContext(n)&&me(document).off("dialogBeforeContentChange".concat(ge)).on("dialogBeforeContentChange".concat(ge),(function(){var t,r,i,o,s=(t=a.fieldsWithDefaultBehaviourValues,r=ee(n),i=a.originalFormValues,o=Object.fromEntries(r),t.filter((function(e){var t=X(e,2),n=t[0],r=t[1];return Y(r)===Y(o[n])})).map((function(e){var t=X(e,1)[0];return[t,i[t]]})));s.forEach((function(t){var r=he(t,2),i=r[0],a=r[1];e.findFieldForId(n,i).val(a)}))})),this.setUpBehavioursInForm(n))},e.prototype.canSwitchIssueContext=function(e){return!!e.find("#issuetype").length},e.prototype.getCoordinates=function(e){return{actionId:(0,Z.wy)(e.find("input[name='action']").val()),pid:this.getPid(e),issueTypeId:(0,Z.wy)(e.find("#issuetype,input[name=issuetype]").val()),issueId:(0,Z.wy)(e.find("input[name=id]").val())}},e.prototype.inputFieldsThatWillGetOverwrittenByNextBehaviour=function(e){var t=this;return function(e,t){var n=ee(e),r=Object.keys(t);return n.filter((function(e){var t=X(e,1)[0];return r.includes(t)}))}(e,be).filter((function(e){var n=he(e,2),r=n[0],i=n[1];return be[r].setValue&&t.originalFormValues[r]!==i&&i!==be[r].setValue&&"-1"!==i})).map((function(e){var n=he(e,3),r=n[0],i=n[1],a=n[2];return"<p><b>".concat(t.escapeHtml(be[r].displayName),"</b>: ").concat(t.escapeHtml(null!=a?a:i),"</p>")}))},e.prototype.escapeHtml=function(e){var t=document.createElement("div");return t.appendChild(document.createTextNode(e)),t.innerHTML},e.prototype.showDialogWithUserValues=function(e){var t=this.inputFieldsThatWillGetOverwrittenByNextBehaviour(e);t.length&&Promise.resolve().then((function(){var e=[n(38860)];(function(e){e({type:"warning",title:"These fields have default values for this project and issue type combination. We have preserved the existing values in case you need them.",close:"manual",body:'<div id="updated-fields" style="overflow-y: scroll;height: 400px; padding-top: inherit; white-space: pre-wrap;">'.concat(t.join("<br><br>"),"</div>")})}).apply(null,e)})).catch(n.oe)},e.prototype.checkDirtyFields=function(e){me.isEmptyObject(this.originalFormValues)&&(this.originalFormValues=this.constructForm(e))},e.prototype.setupListenersForConfigureFieldLinks=function(e){var t=this;JIRA.Version.isGreaterThanOrEqualTo("8.19.0")?this.bindFirst(me("#configure-fields"),"change",(function(){return t.makeAllFieldsWritable(e)}),{}):JIRA.Version.isGreaterThanOrEqualTo("8.14.0")?document.querySelectorAll(".qf-configurable, .qf-unconfigurable").forEach((function(n){t.bindConfigureFieldLinkElement(n,e)})):(0,z.bK)(".qf-configurable, .qf-unconfigurable").then((function(n){t.bindConfigureFieldLinkElement(n,e)}))},e.prototype.bindConfigureFieldLinkElement=function(e,t){this.bindFirst(me(e),"click",this.clickConfigureFieldLink.bind(this),{docRoot:t})},e.prototype.clickConfigureFieldLink=function(e){this.makeAllFieldsWritable(e.data.docRoot)},e.prototype.setUpBehavioursInForm=function(e){var t="form#issue-create,form#issue-workflow-transition,form#assign-issue,form#log-work,div.issue-setup-fields,form[name='jiraform'],form#subtask-create-details,form#issue-edit,form.vp-form,form.cp-request-form";if(e.find(t).length){var n=me(e.find(t)[0]).attr("action");if(-1!==me.inArray(n,Pe))return void De.debug("Behaviours not applicable to the current action");this.checkDirtyFields(e),this.clearAllBehaviours();var r=this.getCoordinates(e),i=r.actionId,a=r.pid,o=r.issueTypeId,s=r.issueId,c=this.getCoordinates(e),l=(0,H.X_)()?(0,H.X_)().content:null,u=(0,H.bj)()?(0,H.bj)().content:null;ke!==s&&(ke=s,be={},Se.clear(),we.clear()),(0,q.kK)()?this.doGetValidatorsByPid(c,e,l,null):null!=s?this.doGetValidators(s,i,e):void 0!==a&&void 0!==o&&a&&o?this.doGetValidatorsByPid(c,e,l,u):De.warn("This context does NOT contain an IssueId or Pid/IssueTypeId. Field Listeners NOT added",e.find(t)),this.setupListenersForConfigureFieldLinks(e),ye||this.bindFirst(e.find(t).closest("form"),"submit",this.submitForm.bind(this),{docRoot:e})}else De.debug("Behaviours not applicable to the current form / screen")},e.prototype.doPostRunValidator=function(t,n,i){return de(this,void 0,void 0,(function(){var a,o,s,c,l;return fe(this,(function(u){switch(u.label){case 0:return t=(t="issuelinks-issues"===t?"issuelinks":t).replace(/:\d$/,""),(a=e.findFieldForId(i,t)).length&&a.addClass("behaviours-wait"),o={fieldName:t,form:n},[4,(0,r.k)("".concat(AJS.contextPath(),"/rest/scriptrunner/behaviours/latest/").concat(this.getRestPrefix(),"runvalidator.json"),{method:"POST",body:JSON.stringify(o),headers:{"X-Atlassian-token":"no-check"}})];case 1:return s=u.sent(),c=s.result,l=s.error,c?[4,this.addFieldListeners(i,c,!1)]:[3,3];case 2:return u.sent(),[3,4];case 3:De.debug("Behaviours post failed.",l),u.label=4;case 4:return a.length&&a.removeClass("behaviours-wait"),Ae.submitSelected&&0===(0,r.e)()&&(Ae.submitSelected=!1,this.submitFormAllowed(i)&&i.find("form:first").trigger("submit")),[2]}}))}))},e.prototype.doGetValidators=function(e,t,n){this.fetchAndProcessBehaviours("".concat(AJS.contextPath(),"/rest/scriptrunner/behaviours/latest/").concat(this.getRestPrefix(),"validators.json?").concat((0,W.F)({issueId:e,actionId:t,ignoreInitialiser:ye})),n)},e.prototype.doGetValidatorsByPid=function(e,t,n,r){this.fetchAndProcessBehaviours("".concat(AJS.contextPath(),"/rest/scriptrunner/behaviours/latest/").concat(this.getRestPrefix(),"validatorsByPid.json?").concat((0,W.F)(ue(ue({},e),{behaviourContextId:n,contextIssueId:r,ignoreInitialiser:ye}))),t)},e.prototype.getRestPrefix=function(){return""},e.prototype.fetchAndProcessBehaviours=function(e,t){var n=this,i={form:this.constructForm(t)};(0,r.k)(e,{method:"POST",body:JSON.stringify(i)}).then((function(e){var r=e.result,i=e.error;r?(n.applySelectedBehaviourOnFailedSubmit(t),n.addFieldListeners(t,r,!0)):De.error("Error retrieving validators:",i)}))},e.prototype.setInsightFieldValue=function(e,t,n){return de(this,void 0,void 0,(function(){var i,a,o,s,c,l,d,f,p,h,v,m,g;return fe(this,(function(y){switch(y.label){case 0:return(i=e.hasClass("multi-select-select")||e.parent().find(".".concat(n,"_multi-container")).length>0)&&((0,q.nM)()?(a=me("#".concat(n,"_multi")),(0,u.UW)(a,(function(){return a.select2("data",[])}))):(0,u.UW)(e,(function(){return e.trigger("clearSelection")}))),t&&0!==t.length?(t=Array.isArray(t)?t:[t],[4,(0,r.k)("".concat(AJS.contextPath(),"/rest/insight/1.0/customfield/objects?").concat((0,W.F)({objectKeys:t.join(",")})))]):(0,q.nM)()?(le("5.2.0")?(d=(0,se.s)(e.parent().find(".atlas-select__input").get(0),10)).onChange([],[],!0):e.select2("data",null),[2]):((0,u.UW)(e,(function(){return e.trigger("set-selection-value","")})),me("#".concat(n,"-field")).val(""),e.find("option:selected").removeAttr("selected"),e.parent().find(".aui-ss-entity-icon").remove(),[2]);case 1:return(o=y.sent()).error&&De.error("Network error retrieving options",o.error),0===(s=o.result).length?(De.error("No such Insight object retrieving:",t),[2]):(0,q.nM)()?[4,(0,z.X7)(e.get(0).parentElement,".insight-init")]:[3,4];case 2:return y.sent(),le("5.2.0")?[4,(0,z.X7)(e.get(0).parentElement,".atlas-select__input")]:[3,4];case 3:y.sent(),y.label=4;case 4:if(c=s.map((function(t){var n=re({label:t.label,value:t.objectKey,id:t.id,icon:t.avatar.url16},{objectId:t.id}),r=n.data("descriptor");return e.append(n),r.properties})),(0,q.nM)())l=me("#".concat(n,"_multi")),d=(0,se.s)(e.parent().find(".atlas-select__input").get(0),10),l.length>0?null!=d?d.onChange(c,[],!0):l.select2("data",c):null!=d?d.onChange(c,[],!0):e.select2("data",c[0]);else{f=function(t){(0,u.UW)(e,(function(){return i?e.trigger("selectOption",t):e.trigger("set-selection-value",t.value)}))};try{for(p=pe(c),h=p.next();!h.done;h=p.next())v=h.value,f(v)}catch(e){m={error:e}}finally{try{h&&!h.done&&(g=p.return)&&g.call(p)}finally{if(m)throw m.error}}}return[2]}}))}))},e.prototype.applySelectedBehaviourOnFailedSubmit=function(t){var n=this;!0===ye&&(we.forEach((function(r){var i=e.findFieldForId(t,r);n.makeReadOnly(i)})),we.clear(),Se.forEach((function(r){var i=e.findFieldForId(t,r);n.requireField(i),r in be||(be[r]={},be[r].required=!0)})),Se.clear())},e.prototype.setLabelField=function(e,t){(0,u.ri)((function(){return(0,u.UW)(e,(function(){return e.trigger("clearSelection")}))})),me.each(t,(function(t,n){var r=me("<option/>",{text:n,value:n,title:n,selected:"selected"}),i=new AJS.ItemDescriptor({value:n,title:n,selected:"selected",label:n});r.data("descriptor",i),e.append(r),e.trigger("selectOption",i)}))},e.prototype.doGetUserPicker=function(e,t){return de(this,void 0,Promise,(function(){var n,i,a,o;return fe(this,(function(s){switch(s.label){case 0:return["","-1","-2"].includes(t)?(e.is("#assignee")?(e.find("option").removeAttr("selected"),e.trigger("set-selection-value",t)):x(e),[2]):[4,(0,r.k)("".concat(AJS.contextPath(),"/rest/api/2/user/picker?showAvatar=true&query=").concat(t))];case 1:return(n=s.sent()).error?[2]:0!==(i=n.result).total&&me.grep(i.users,(function(e){return e.name===t}))?void 0===(a=me.grep(JIRA.UserPickerUtil.formatResponse(i)[0].properties.items,(function(e){return e.properties.value===t})))||1!==a.length?(De.warn("Failed to find user:",t),[2]):(e.trigger("clearSelection"),e.find("option").removeAttr("selected"),e.find("option[value='".concat(t,"']")).remove(),o=e.find("optgroup:first"),(o.length?o:e).append(me("<option/>",{selected:"selected",class:"","data-field-text":a[0].label(),value:t,style:"background-image: url(".concat(a[0].icon(),")")}).html(a[0].label())),(0,u.UW)(e,(function(){return e.trigger("reset")})),[2]):(De.warn("Failed to find user:",t),[2])}}))}))},e}()},60247:(e,t,n)=>{"use strict";n.d(t,{Sj:()=>s,ty:()=>k,uj:()=>b});var r=n(53281),i=function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,i,a=n.call(e),o=[];try{for(;(void 0===t||t-- >0)&&!(r=a.next()).done;)o.push(r.value)}catch(e){i={error:e}}finally{try{r&&!r.done&&(n=a.return)&&n.call(a)}finally{if(i)throw i.error}}return o},a=function(e,t,n){if(n||2===arguments.length)for(var r,i=0,a=t.length;i<a;i++)!r&&i in t||(r||(r=Array.prototype.slice.call(t,0,i)),r[i]=t[i]);return e.concat(r||Array.prototype.slice.call(t))},o=function(e){var t="function"==typeof Symbol&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},s=function(e){window.require(["aui/flag"],(function(t){t({type:"error",title:"Error",persistent:!1,body:e})}))},c=function(e,t){var n=p();if(m()){var r=Boolean(t)?n.filter((function(e,n){return $(n).text()===t})):n.eq(e);if(0!==r.length){r.parent().hide();var i=u(r);if($("#"+i).hide(),e<h()){var a=n.eq(e+1);a.css("border-top-left-radius","3px").css("border-left","1px solid #ccc"),a.parent().css("margin-left","20px")}d()===e&&n.eq(h()).trigger("click")}}},l=function(e,t){var n=p(),r=void 0!==t?n.filter((function(e,n){return $(n).text()===t})):n.eq(e);if(r.parent().is(":visible"))console.log("already shown");else{r.parent().show();var i=u(r);if($("#"+i).css("display",""),e===h()){var a=n.not(n.filter($(":first")));a.css("border-top-left-radius","").css("border-left",""),a.parent().css("margin-left","")}}},u=function(e){return e.attr("href").replace("#","")},d=function(){return p().index($("[aria-selected=true]"))},f=function(e){p().eq(e).trigger("click")},p=function(){return $("a[role=tab][href^=#tab]")},h=function(){return p().index(v())},v=function(){return p().filter($(":visible").not(".disabled-tab"))},m=function(){return 1!==v().length||(console.warn("You can't hide or disable all tabs"),!1)},g=function(e){var t=p();if(m()){t.eq(e).on("click".concat(r.A7),(function(e){e.stopPropagation()})).addClass("disabled-tab").parent().addClass("adaptavist-sr");var n=u(t.eq(e));$("#"+n).hide(),d()===e&&t.eq(h()).trigger("click")}},y=function(e){var t=p();t.eq(e).off(r.A7).removeClass("disabled-tab").parent().removeClass("adaptavist-sr");var n=u(t.eq(e));$("#"+n).css("display","")},b=function(e,t,n){var r=e&&p().length?Object.entries(e).filter((function(e){var t=i(e,2)[1];return t.hide||t.disable})).map((function(e){return i(e,2)[1].fields})):[],o=Object.entries(n).filter((function(e){var t=i(e,2)[1];return t.hidden||t.readonly})).map((function(e){return i(e,1)[0]})),c=a([o],i(r),!1).reduce((function(e,t){return t.forEach((function(t){return e.add(t)})),e}),new Set),l=Object.keys(t.errors).filter((function(e){return c.has(e)}));if(l.length){var u=l.map((function(e){return"<b>".concat(e,"</b>: ").concat(t.errors[e])})).join("<br>");s("There are fields that are required or in an error state, but the administrator has hidden them "+"or made them readonly using a behaviour.<p id='jbhv-submit-error'>".concat(u,"</p><p>Please contact your administrator.</p>"))}},k=function(e){var t,n,r,a;if(p().length)try{for(var s=o(Object.entries(e)),u=s.next();!u.done;u=s.next()){var d=i(u.value,2),h=d[0],v=d[1],m=parseInt(h,10),b=v.name;try{for(var k=(r=void 0,o(Object.entries(v))),w=k.next();!w.done;w=k.next()){var S=i(w.value,2),F=S[0],x=S[1];"hide"===F&&(x?c(m,b):l(m,b)),"switch"===F&&f(m),"disable"===F&&(x?g(m):y(m))}}catch(e){r={error:e}}finally{try{w&&!w.done&&(a=k.return)&&a.call(k)}finally{if(r)throw r.error}}}}catch(e){t={error:e}}finally{try{u&&!u.done&&(n=s.return)&&n.call(s)}finally{if(t)throw t.error}}}},39997:(e,t,n)=>{"use strict";n.d(t,{_:()=>r});var r={BaseGroupOptions:{invalidItemSelected:function(e){return"Failed to find group with name: ".concat(e)},placeholder:"Select groups",itemNamePlural:"groups"},BaseUserOptions:{invalidItemSelected:function(e){return"Failed to find user with key: ".concat(e)},placeholder:"Select user(s)",itemNamePlural:"users"},BasePageOptions:{invalidItemSelected:function(e){return"Failed to find page with title: ".concat(e)},placeholder:"Select pages(s)",itemNamePlural:"pages"},BaseAttachmentOptions:{invalidItemSelected:function(e){return"Failed to find attachhment with title: ".concat(e)},placeholder:"Select attachment(s)",itemNamePlural:"attachments"}}},67001:(e,t,n)=>{"use strict";n.d(t,{Z:()=>l});var r=n(63844),i=n(88162),a=n(39997),o=n(39507),s=function(){return s=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e},s.apply(this,arguments)},c=s(s({},a._.BaseGroupOptions),{fetchSingleItemByKey:function(e){return"".concat(AJS.contextPath(),"/rest/api/2/group?").concat((0,o.F)({groupname:e,maxResults:1}))},mapItemToOption:function(e){return{value:e.name,label:e.name}},searchUrl:function(e,t){return"".concat(AJS.contextPath(),"/rest/api/2/groups/picker?").concat((0,o.F)({showAvatar:!0,query:e,exclude:t}))},mapSearchResultToSuggestions:function(e){return e.groups.map((function(e){return{value:e.name,label:e.name}}))},getTotalSearchResults:function(e){return e.total}});const l=function(e){return r.createElement(i.Z,s({minCharacters:0},c,e))}},97330:(e,t,n)=>{"use strict";n.d(t,{Z:()=>u});var r=n(63844),i=n(88162),a=n(39997),o=n(39507),s=n(58844),c=function(){return c=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e},c.apply(this,arguments)},l=c(c({},a._.BaseUserOptions),{fetchSingleItemByKey:function(e){return"".concat(AJS.contextPath(),"/rest/api/2/user?").concat((0,o.F)({key:e}))},searchUrl:function(e){return"".concat(AJS.contextPath(),"/rest/api/2/user/picker?").concat((0,o.F)({showAvatar:!0,query:e}))},mapItemToOption:function(e){return{value:e.key,label:e.displayName,icon:e.avatarUrls["16x16"]}},mapSearchResultToSuggestions:function(e){return e.users?e.users.map((function(e){return{value:e.key,label:e.displayName,icon:e.avatarUrl,html:e.html}})):[]},formatOptionElement:function(e){return r.createElement("span",{dangerouslySetInnerHTML:{__html:(0,s.sanitize)(e.data.html)}})},getTotalSearchResults:function(e){return e.total}});const u=function(e){return r.createElement(i.Z,c({},e,l))}},88433:(e,t,n)=>{"use strict";n.d(t,{NU:()=>d,X_:()=>o,Xs:()=>u,bj:()=>l,or:()=>f,sd:()=>s,zM:()=>c});var r=n(91478),i="adaptavist-scriptrunner-behaviour-context-id",a="adaptavist-scriptrunner-context-issue-id",o=function(){return document.head.querySelector("meta[name=".concat(i,"]"))},s=function(e){return $(document.head).append("<meta name=".concat(i," content=").concat(e,">"))},c=function(){var e=o();e&&e.remove()},l=function(){return document.head.querySelector("meta[name=".concat(a,"]"))},u=function(e){return $(document.head).append("<meta name=".concat(a," content=").concat(e,">"))},d=function(){var e=l();e&&e.remove()},f=function(e){return e.startsWith(r.UI)?e.substring(r.UI.length):e}},91478:(e,t,n)=>{"use strict";n.d(t,{UI:()=>r});n(5667),jQuery;var r="sr-"},50777:(e,t,n)=>{"use strict";n.r(t),n.d(t,{renderPickers:()=>A,serializeForm:()=>C});var r=n(63844),i=n(86936),a=n(67001),o=n(97330),s=n(88162),c=n(39507),l=n(58844),u=function(){return u=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e},u.apply(this,arguments)},d={invalidItemSelected:function(e){return"Failed to find issue with ID: ".concat(e)},placeholder:"Select issue(s)",itemNamePlural:"issues",fetchSingleItemByKey:function(e){return"".concat(AJS.contextPath(),"/rest/api/2/issue/").concat(e,"?").concat((0,c.F)({fields:"summary,issuetype,key"}))},searchUrl:function(){return""},mapItemToOption:function(e){return{value:e.key,label:"".concat(e.key," - ").concat(e.fields.summary),icon:e.fields.issuetype.iconUrl}},mapSearchResultToSuggestions:function(e){return e.sections?e.sections[0].issues.map((function(e){return{value:e.key,label:"".concat(e.key," - ").concat(e.summaryText),html:"".concat(e.keyHtml," ").concat(e.summary),icon:"".concat(AJS.contextPath()).concat(e.img)}})):[]},formatOptionElement:function(e){return r.createElement("span",{dangerouslySetInnerHTML:{__html:(0,l.sanitize)(e.data.html)}})},getTotalSearchResults:function(e){return e.sections?e.sections[0].totalIssues:null}};const f=function(e){d.searchUrl=function(t){return"".concat(AJS.contextPath(),"/rest/scriptrunner-jira/latest/issue/picker?").concat((0,c.F)({showAvatar:!0,showSubTasks:!0,query:t,currentJql:e.configuration.currentJql,max:30}))};var t=e.configuration.FIELD_PLACEHOLDER||d.placeholder;return r.createElement(s.Z,u({},d,e,{placeholder:t,minCharacters:0}))};var p=function(){return p=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e},p.apply(this,arguments)};const h=function(e){var t,n,i=(t=e.configuration,n={currentJQL:t.currentJql,showSubTasks:!0,showSubTaskParent:!0,appId:t.FIELD_APP_LINK_ID},{invalidItemSelected:function(e){return"Failed to find issue with key: ".concat(e," in remote instance")},placeholder:t.FIELD_PLACEHOLDER||"Select issue(s)",itemNamePlural:"issues",fetchSingleItemByKey:function(e){return"".concat(AJS.contextPath(),"/rest/scriptrunner-jira/latest/remoteissue/").concat(t.FIELD_APP_LINK_ID,"/").concat(e,"?fields=summary,issuetype,key")},searchUrl:function(e){return"".concat(AJS.contextPath(),"/rest/remoteJiraIssueLink/1/remoteJira/picker?").concat((0,c.F)(p({query:e},n)))},mapItemToOption:function(e){return{value:e.key,label:e.key+" "+e.fields.summary,icon:e.fields.issuetype.iconUrl}},mapSearchResultToSuggestions:function(e){if(!e.sections)return[];var t=e.sections.find((function(e){return"cs"===e.id}));return t?t.issues.map((function(e){return{value:e.key,label:e.key+" "+e.summaryText,html:e.keyHtml+" "+e.summary,icon:"".concat(AJS.contextPath(),"/").concat(e.img)}})):[]},formatOptionElement:function(e){return r.createElement("span",{dangerouslySetInnerHTML:{__html:(0,l.sanitize)(e.data.html)}})},getTotalSearchResults:function(e){return e.sections?e.sections[0].totalIssues:null}});return r.createElement(s.Z,p({},e,i,{minCharacters:0}))};var v=n(53281),m=(n(17775),n(5667),n(36894)),g=n(74729),y=function(){return y=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e},y.apply(this,arguments)},b={invalidItemSelected:function(e){return"Failed to find record with ID: ".concat(e)},itemNamePlural:"records",mapItemToOption:function(e){return e},mapSearchResultToSuggestions:function(e){return e.items?e.items:[]},getTotalSearchResults:function(e){return e.totalHits},formatOptionElement:function(e){return r.createElement("span",{dangerouslySetInnerHTML:{__html:(0,l.sanitize)(e.data.html)}})}},k=function(e,t,n,r){return(0,g.wrappedFetch)("".concat(AJS.contextPath(),"/rest/scriptrunner-jira/latest/generic-picker/").concat(e,"?").concat((0,c.F)(t)),r?{method:n,body:r}:{method:n})};const w=function(e){return b.placeholder=e.configuration.FIELD_PLACEHOLDER||("com.onresolve.scriptrunner.canned.jira.fields.model.IssuePickerCommand"===e.configuration["@class"]?"Select issue(s)":"Select record(s)"),b.fetchSingleItemByKeyFunction=function(t){var n={id:t,fcsId:e.fcsId,issueId:e.issueId};return e.previewMode?k("admingetitem",n,"POST",JSON.stringify(e.configuration)):k("getitem",n,"GET")},b.searchFetchFunction=function(t){var n=y({inputValue:t,userData:JSON.stringify(e.userData),fcsId:e.fcsId},m.Z(["pid","issueTypeId","issueId"],e));return e.previewMode?k("adminsearch",n,"POST",JSON.stringify(e.configuration)):k("search",n,"POST",JSON.stringify({issueFields:e.getIssueFields()}))},r.createElement(s.Z,y({},e,b,{minCharacters:0}))};var S=function(){return S=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e},S.apply(this,arguments)},F=function(e,t,n,r){return new(n||(n=Promise))((function(i,a){function o(e){try{c(r.next(e))}catch(e){a(e)}}function s(e){try{c(r.throw(e))}catch(e){a(e)}}function c(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,s)}c((r=r.apply(e,t||[])).next())}))},x=function(e,t){var n,r,i,a,o={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return a={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function s(a){return function(s){return function(a){if(n)throw new TypeError("Generator is already executing.");for(;o;)try{if(n=1,r&&(i=2&a[0]?r.return:a[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,a[1])).done)return i;switch(r=0,i&&(a=[2&a[0],i.value]),a[0]){case 0:case 1:i=a;break;case 4:return o.label++,{value:a[1],done:!1};case 5:o.label++,r=a[1],a=[0];continue;case 7:a=o.ops.pop(),o.trys.pop();continue;default:if(!(i=o.trys,(i=i.length>0&&i[i.length-1])||6!==a[0]&&2!==a[0])){o=0;continue}if(3===a[0]&&(!i||a[1]>i[0]&&a[1]<i[3])){o.label=a[1];break}if(6===a[0]&&o.label<i[1]){o.label=i[1],i=a;break}if(i&&o.label<i[2]){o.label=i[2],o.ops.push(a);break}i[2]&&o.ops.pop(),o.trys.pop();continue}a=t.call(e,o)}catch(e){a=[6,e],r=0}finally{n=i=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,s])}}},I=function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,i,a=n.call(e),o=[];try{for(;(void 0===t||t-- >0)&&!(r=a.next()).done;)o.push(r.value)}catch(e){i={error:e}}finally{try{r&&!r.done&&(n=a.return)&&n.call(a)}finally{if(i)throw i.error}}return o},O=function(e,t,n){if(n||2===arguments.length)for(var r,i=0,a=t.length;i<a;i++)!r&&i in t||(r||(r=Array.prototype.slice.call(t,0,i)),r[i]=t[i]);return e.concat(r||Array.prototype.slice.call(t))},j=jQuery;var C=function(e){return e.serializeArray().reduce((function(e,t){var n,r,i=t.name,a=t.value;return e[i]?S(S({},e),((r={})[i]=O(O([],I(e[i]),!1),[a],!1),r)):S(S({},e),((n={})[i]=[a],n))}),{})},A=function(e,t,n,s){void 0===n&&(n=!1),void 0===s&&(s=!1),t.find("div.sr-custompicker").each((function(e,c){var l=j(c).closest("div.inline-edit-fields, #modal-field-view, #issue-filter").length>0;l||j(c).closest("div.sr-custompicker").css("max-width","500px");var u={container:function(e){return S(S({},e),{width:n||l?"unset":500})}},d=l?S(S({},u),{menu:function(e){return S(S({},e),{position:"relative"})}}):u;j(c).append('<div class="react-target adaptavist-sr"/>'),j(c).removeClass("sr-custompicker");var p,m,g=c.dataset.customfieldid,y=c.dataset.fieldconfigid,b=c.dataset.fieldparams,k=c.querySelector(".react-target"),F=t.find("#".concat(g));try{p=F.find("option:selected").get().map((function(e){return j(e).val().toString()})),m=JSON.parse(b)}catch(e){return console.warn("Failed to parse json: fieldparamsStr:",b),void i.render(r.createElement("div",null,"Broken custom field"),k)}if(m.element){var x=j("<div/>",{id:"".concat(g,"_value")});x.appendTo(j(c));var I=function(e){p=e,x.empty();var t=Array.isArray(e)?e:[e];F.empty(),t.forEach((function(e){return F.append(j("<option/>",{selected:"selected",value:e}))})),x.append(j("<input/>",{type:"hidden",name:"".concat(g,":params"),value:JSON.stringify(m)})),x.append(j("<input/>",{type:"hidden",name:"".concat(g,":fieldConfigId"),value:y}))},O={JiraUserPicker:o.Z,JiraGroupPicker:a.Z,JiraIssuePicker:f,JiraRemoteIssuePicker:h,ConfigurableObjectPicker:w},A=S(S({},m),{handleChange:function(e){I(e),F.trigger("change",{value:e}),T({})},value:p,styles:d,context:t,userData:{},getIssueFields:function(){var e=C(j(c).closest("form"));return m.pid&&(e.pid=[m.pid.toString()]),m.issueTypeId&&(e.issuetype=[m.issueTypeId]),m.issueId&&(e.issueId=[m.issueId.toString()]),e},previewMode:s,standaloneSearch:l});i.render(r.createElement(O[m.element],A),k),I(p);var T=function(e,t){void 0===t&&(t=!1),A=S(S(S({},A),{value:p}),e),m=S(S({},m),e),t&&i.unmountComponentAtNode(k),i.render(r.createElement(O[m.element],A),k),I(p)};F.addClass("behaviours-common-api").data("behavioursShim",{setValue:function(e){return I("string"==typeof e?[e]:e),T({value:e})},getValue:function(){return Array.isArray(p)?p.length?"single"===m.type?p[0]:p:null:p},setFieldOptions:function(){console.warn("Set field options via changing search URLs, eg call setOption({jql: project = foo})")},makeReadOnly:function(){return T({isDisabled:!0})},makeWritable:function(){return T({isDisabled:!1})},setUserData:function(e){return T({userData:e},!0)},setValid:function(e){return T({styles:S(S({},d),{control:function(t){return S(S({},t),{borderColor:e===v.EY.INVALID?"red":"hsl(0, 0%, 80%)"})}})})}})}else i.render(r.createElement("div",null,"Broken custom field, missing element type"),k)})),t.find("div.sr-remoteissuelink").each((function(e,t){var n=j(t).find(".sr-remoteGlobalIds").val().toString();j(t).removeClass("sr-remoteissuelink"),JSON.parse(n).map((function(e){return F(void 0,void 0,void 0,(function(){var n,r,i;return x(this,(function(a){switch(a.label){case 0:return a.trys.push([0,2,,3]),(n=j(t).find(".sr-remoteissueview")).empty(),[4,(0,g.wrappedFetch)("".concat(AJS.contextPath(),"/rest/scriptrunner-jira/latest/remoteissue/render/").concat(e.appId,"/").concat(e.id),{headers:{"Content-Type":"text/html"}})];case 1:return 200===(r=a.sent()).response.status?n.append(r.result):(n.append("Failed to load remote issue"),console.log("Could not load remove issue",r.error)),[3,3];case 2:return i=a.sent(),console.error(i),[3,3];case 3:return[2]}}))}))}))}))};j((function(){JIRA.bind(JIRA.Events.NEW_CONTENT_ADDED,A),A(null,j(document))}))},44148:(e,t,n)=>{"use strict";n.d(t,{KL:()=>l,pN:()=>u,pO:()=>d,th:()=>c});var r=n(84806),i=function(e,t,n,r){return new(n||(n=Promise))((function(i,a){function o(e){try{c(r.next(e))}catch(e){a(e)}}function s(e){try{c(r.throw(e))}catch(e){a(e)}}function c(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,s)}c((r=r.apply(e,t||[])).next())}))},a=function(e,t){var n,r,i,a,o={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return a={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function s(a){return function(s){return function(a){if(n)throw new TypeError("Generator is already executing.");for(;o;)try{if(n=1,r&&(i=2&a[0]?r.return:a[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,a[1])).done)return i;switch(r=0,i&&(a=[2&a[0],i.value]),a[0]){case 0:case 1:i=a;break;case 4:return o.label++,{value:a[1],done:!1};case 5:o.label++,r=a[1],a=[0];continue;case 7:a=o.ops.pop(),o.trys.pop();continue;default:if(!(i=o.trys,(i=i.length>0&&i[i.length-1])||6!==a[0]&&2!==a[0])){o=0;continue}if(3===a[0]&&(!i||a[1]>i[0]&&a[1]<i[3])){o.label=a[1];break}if(6===a[0]&&o.label<i[1]){o.label=i[1],i=a;break}if(i&&o.label<i[2]){o.label=i[2],o.ops.push(a);break}i[2]&&o.ops.pop(),o.trys.pop();continue}a=t.call(e,o)}catch(e){a=[6,e],r=0}finally{n=i=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,s])}}},o=function(e){return i(void 0,void 0,Promise,(function(){return a(this,(function(t){switch(t.label){case 0:return[4,(0,r.X7)(e[0].parentElement,"textarea[data-editor-id]")];case 1:return t.sent(),[2,e.data("editor-id")]}}))}))},s=function(e,t){var r=n(38871),i=r.getEntry(e);if(i)t(i);else{var a="register::".concat(e),o=function(e){r.off(a,o),t(e)};r.on(a,o)}},c=function(e){return!!e.siblings(".rte-container").length};function l(e){return void 0!==e.rteInstance}var u=function(e,t){return i(void 0,void 0,void 0,(function(){var n,r;return a(this,(function(i){switch(i.label){case 0:return n=Array.isArray(t)?t.join(", "):t,e.is(":visible")||e.val(n),[4,o(e)];case 1:return r=i.sent(),s(r,(function(){return e.val(n)})),[2]}}))}))},d=function(e){return i(void 0,void 0,Promise,(function(){var t;return a(this,(function(n){switch(n.label){case 0:return[4,o(e)];case 1:return t=n.sent(),[2,new Promise((function(e){return s(t,e)}))]}}))}))}},35484:(e,t,n)=>{"use strict";n.d(t,{EA:()=>r,kK:()=>i,nM:()=>a,vo:()=>o});var r=function(){var e=document.location.pathname.match(/customer\/portal\/(\d+)\/create\/(\d+)/);return e?{portalId:parseInt(e[1],10),requestTypeId:parseInt(e[2],10)}:null},i=function(){return/portal\/\d+\/create\/\d+/.test(document.location.href)},a=function(){return/servicedesk\/customer/.test(document.location.href)},o=function(){var e=document.location.href.match(/servicedesk\/customer\/portal\/(\d+)\/([A-Z][(A-Z0-9_]+-\d+)/);return e?{portalId:e[1],issueKey:e[2]}:null}},75583:(e,t,n)=>{"use strict";n.d(t,{j:()=>s});var r=n(46412),i=n.n(r),a=function(e){var t="function"==typeof Symbol&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},o=i().methodFactory;i().methodFactory=function(e,t,n){return o(e,t,n).bind(void 0,"[".concat(n,"]"))},i().setLevel(i().getLevel());var s=function(e){var t=i().getLogger(e),n=i().levels.WARN;return t.setDefaultLevel(n),t},c=function(e){var t,n,r=Object.values(i().getLoggers());try{for(var o=a(r),s=o.next();!s.done;s=o.next()){s.value.setLevel(e)}}catch(e){t={error:e}}finally{try{s&&!s.done&&(n=o.return)&&n.call(o)}finally{if(t)throw t.error}}},l=window.ScriptRunner||{};l.setLogLevel||(l.setLogLevel=function(e,t){i().getLogger(e).setLevel(t)},l.enableVerboseLogging=function(){return c(i().levels.DEBUG)},l.disableVerboseLogging=function(){return c(i().levels.WARN)})},48963:(e,t,n)=>{e.exports=n},37018:(e,t,n)=>{e.exports=n},17775:(e,t,n)=>{e.exports=n}}]);