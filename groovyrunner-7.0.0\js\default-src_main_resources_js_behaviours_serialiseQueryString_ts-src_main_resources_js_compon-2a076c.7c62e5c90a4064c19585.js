"use strict";(self.webpackJsonpScriptRunner=self.webpackJsonpScriptRunner||[]).push([["default-src_main_resources_js_behaviours_serialiseQueryString_ts-src_main_resources_js_compon-2a076c"],{74729:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(o,a){function i(e){try{l(r.next(e))}catch(e){a(e)}}function s(e){try{l(r.throw(e))}catch(e){a(e)}}function l(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(i,s)}l((r=r.apply(e,t||[])).next())}))},o=this&&this.__generator||function(e,t){var n,r,o,a,i={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return a={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function s(a){return function(s){return function(a){if(n)throw new TypeError("Generator is already executing.");for(;i;)try{if(n=1,r&&(o=2&a[0]?r.return:a[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,a[1])).done)return o;switch(r=0,o&&(a=[2&a[0],o.value]),a[0]){case 0:case 1:o=a;break;case 4:return i.label++,{value:a[1],done:!1};case 5:i.label++,r=a[1],a=[0];continue;case 7:a=i.ops.pop(),i.trys.pop();continue;default:if(!(o=i.trys,(o=o.length>0&&o[o.length-1])||6!==a[0]&&2!==a[0])){i=0;continue}if(3===a[0]&&(!o||a[1]>o[0]&&a[1]<o[3])){i.label=a[1];break}if(6===a[0]&&i.label<o[1]){i.label=o[1],o=a;break}if(o&&i.label<o[2]){i.label=o[2],i.ops.push(a);break}o[2]&&i.ops.pop(),i.trys.pop();continue}a=t.call(e,i)}catch(e){a=[6,e],r=0}finally{n=o=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,s])}}},a=this&&this.__values||function(e){var t="function"==typeof Symbol&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},i=Object.create,s=Object.defineProperty,l=Object.defineProperties,u=Object.getOwnPropertyDescriptor,c=Object.getOwnPropertyDescriptors,p=Object.getOwnPropertyNames,f=Object.getOwnPropertySymbols,h=Object.getPrototypeOf,d=Object.prototype.hasOwnProperty,m=Object.prototype.propertyIsEnumerable,y=function(e,t,n){return t in e?s(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n},v=function(e,t){var n,r;for(var o in t||(t={}))d.call(t,o)&&y(e,o,t[o]);if(f)try{for(var i=a(f(t)),s=i.next();!s.done;s=i.next()){o=s.value;m.call(t,o)&&y(e,o,t[o])}}catch(e){n={error:e}}finally{try{s&&!s.done&&(r=i.return)&&r.call(i)}finally{if(n)throw n.error}}return e},g=function(e,t){return l(e,c(t))},b=function(e){return s(e,"__esModule",{value:!0})};!function(e,t){for(var n in b(e),t)s(e,n,{get:t[n],enumerable:!0})}(t,{fetchJson:function(){return _},getRequestCountValue:function(){return T},trackedFetchFactory:function(){return P},wrappedFetch:function(){return x}});var O,S=(O=n(60208),function(e,t,n){var r,o;if(t&&"object"==typeof t||"function"==typeof t){var i=function(r){d.call(e,r)||"default"===r||s(e,r,{get:function(){return t[r]},enumerable:!(n=u(t,r))||n.enumerable})};try{for(var l=a(p(t)),c=l.next();!c.done;c=l.next())i(c.value)}catch(e){r={error:e}}finally{try{c&&!c.done&&(o=l.return)&&o.call(l)}finally{if(r)throw r.error}}}return e}(b(s(null!=O?i(h(O)):{},"default",O&&O.__esModule&&"default"in O?{get:function(){return O.default},enumerable:!0}:{value:O,enumerable:!0})),O)),w="Content-Type",E="application/json",P=function(e){return function(t,n){return I(e),x(t,n).finally((function(){return C(e)}))}};function M(e){var t=e.headers.get(w);return t&&-1===t.indexOf("text/html")&&-1===t.indexOf("text/plain")?-1!==t.indexOf("application/json")||t.startsWith("application/")&&-1!==t.indexOf("+json;")?e.text().then((function(e){return e.length>0?JSON.parse(e):null})):t.startsWith("image/")?e.blob():Promise.resolve(null):e.text()}var x=function(e,t){return r(void 0,void 0,void 0,(function(){var n;return o(this,(function(r){return n=(0,S.deepmerge)(function(){var e;return{credentials:"same-origin",headers:(e={"Cache-Control":"no-cache"},e[w]=E,e["X-Atlassian-token"]="no-check",e)}}(),t||{}),[2,fetch(e,n).then((function(e){if(!e.ok){var t={error:e.statusText||"request failed",response:e};return M(e).then((function(e){return Promise.resolve(g(v({},t),{errorResult:e}))})).catch((function(e){return Promise.resolve(t)}))}return M(e).then((function(t){return Promise.resolve({result:t,response:e})})).catch((function(t){return n.method&&["delete","post"].includes(n.method.toLowerCase())?Promise.resolve({result:{},response:e}):(console.warn("Could not parse: ".concat(t)),Promise.resolve({error:"Could not parse: ".concat(t)}))}))})).catch((function(e){return console.warn("Error fetching",e),Promise.resolve({error:"Network ".concat(e)})}))]}))}))},_=function(e,t){return r(void 0,void 0,void 0,(function(){var n;return o(this,(function(r){return[2,x(e,g(v({},t),{headers:g(v({},null!=(n=null==t?void 0:t.headers)?n:{}),{Accept:E})}))]}))}))},I=function(e){e&&e.length&&k(e,T(e)+1)},C=function(e){e&&e.length&&k(e,T(e)-1)},T=function(e){return Number.parseInt(document.body.dataset[e]||"0",10)},k=function(e,t){document.body.dataset[e]=t.toString()}},28740:(e,t,n)=>{n.d(t,{s:()=>p});var r,o=n(18390),a=n(22106),i=n.n(a),s=n(63844),l=function(e,t){return Object.defineProperty?Object.defineProperty(e,"raw",{value:t}):e.raw=t,e},u=o.Z.span(r||(r=l(["\n    font-weight: bold;\n"],["\n    font-weight: bold;\n"]))),c=function(e){var t=e.children;return s.createElement(u,{className:"react-select-highlighted"},t)},p=function(e,t){var n=e.label,r=t.inputValue;return"menu"===t.context?s.createElement(i(),{searchWords:[r],textToHighlight:n,autoEscape:!0,highlightTag:c}):n}},39507:(e,t,n)=>{n.d(t,{F:()=>i});var r=n(17619),o=n(29577),a=n(16897),i=function(e){return r.stringify(o.Z((function(e){return!a.Z(e)}),e))}},88162:(e,t,n)=>{n.d(t,{Z:()=>w});var r,o=n(63844),a=n(22586),i=n(2829),s=n(18390),l=n(28740),u=n(96253),c=function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,a=n.call(e),i=[];try{for(;(void 0===t||t-- >0)&&!(r=a.next()).done;)i.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=a.return)&&n.call(a)}finally{if(o)throw o.error}}return i},p=function(e){var t=c((0,o.useState)(!0),2),n=t[0],r=t[1];return o.createElement(o.Fragment,null,o.createElement("span",{className:"action error",style:{cursor:"pointer"},onClick:function(){return r(!n)}},e.summary," ",o.createElement("span",{className:"aui-icon aui-icon-small aui-iconfont-arrows-".concat(n?"up":"down")})),!n&&e.children)},f=n(74729),h=function(e,t){return Object.defineProperty?Object.defineProperty(e,"raw",{value:t}):e.raw=t,e},d=(r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},r(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),m=function(){return m=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},m.apply(this,arguments)},y=function(e,t,n,r){return new(n||(n=Promise))((function(o,a){function i(e){try{l(r.next(e))}catch(e){a(e)}}function s(e){try{l(r.throw(e))}catch(e){a(e)}}function l(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(i,s)}l((r=r.apply(e,t||[])).next())}))},v=function(e,t){var n,r,o,a,i={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return a={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function s(a){return function(s){return function(a){if(n)throw new TypeError("Generator is already executing.");for(;i;)try{if(n=1,r&&(o=2&a[0]?r.return:a[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,a[1])).done)return o;switch(r=0,o&&(a=[2&a[0],o.value]),a[0]){case 0:case 1:o=a;break;case 4:return i.label++,{value:a[1],done:!1};case 5:i.label++,r=a[1],a=[0];continue;case 7:a=i.ops.pop(),i.trys.pop();continue;default:if(!(o=i.trys,(o=o.length>0&&o[o.length-1])||6!==a[0]&&2!==a[0])){i=0;continue}if(3===a[0]&&(!o||a[1]>o[0]&&a[1]<o[3])){i.label=a[1];break}if(6===a[0]&&i.label<o[1]){i.label=o[1],o=a;break}if(o&&i.label<o[2]){i.label=o[2],i.ops.push(a);break}o[2]&&i.ops.pop(),i.trys.pop();continue}a=t.call(e,i)}catch(e){a=[6,e],r=0}finally{n=o=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,s])}}},g=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n},b=function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,a=n.call(e),i=[];try{for(;(void 0===t||t-- >0)&&!(r=a.next()).done;)i.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=a.return)&&n.call(a)}finally{if(o)throw o.error}}return i},O=function(e,t,n){if(n||2===arguments.length)for(var r,o=0,a=t.length;o<a;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))},S=s.Z.div(E||(E=h(["\n    padding: 8px 12px;\n"],["\n    padding: 8px 12px;\n"])));const w=function(e){function t(){var n=null!==e&&e.apply(this,arguments)||this;return n.defaultMinCharsMessage="Type ".concat(n.props.minCharacters," more character(s)..."),n.state={selectedItems:null,errors:[],allItems:[],totalSearchResults:null,noOptionsMessage:t.EMPTY_OPTIONS_SET_MESSAGE,isLoading:!1,isDisabled:!1,defaultOptions:[]},n.getSearchResults=function(e){var t=n.props.minCharacters;if(t&&e.length<t){var r=t-e.length,o=r>1?"s":"";return n.setState({noOptionsMessage:"Type ".concat(r," more character").concat(o,"...")}),Promise.resolve([])}return(n.props.searchFetchFunction?n.props.searchFetchFunction(e):(0,f.wrappedFetch)(n.props.searchUrl(e,n.props.value))).then((function(e){return y(n,void 0,void 0,(function(){var t;return v(this,(function(n){if(e.error)throw t=e.errorResult,this.setState({errors:O([m(m({},t),{message:"Error searching: "+t.message})],b(this.state.errors.filter((function(e){return!!e.invalidItemId}))),!1),noOptionsMessage:"Error searching"}),new Error("Error in picker search ".concat(t.message));return[2,e]}))}))})).then((function(e){var t=e.result,r=n.props.mapSearchResultToSuggestions(t);return n.setState({totalSearchResults:r.length&&n.props.getTotalSearchResults?n.props.getTotalSearchResults(t):null,noOptionsMessage:null,errors:n.state.errors.filter((function(e){return e.invalidItemId}))}),r})).catch((function(e){console.warn("mapSearchResultToSuggestions failure",e)}))},n.MenuList=function(e){var t=n.isSingle(n.props)?e.options.length:e.options.filter((function(t){return!e.getValue().map((function(e){return e.value})).includes(t.value)})).length;return o.createElement(a.y.MenuList,m({},e),n.state.totalSearchResults>0&&e.options.length>0&&o.createElement(S,null,o.createElement("b",null,"Showing ",t," of ",n.state.totalSearchResults," matching"," ",n.props.itemNamePlural?n.props.itemNamePlural:"options")),e.children)},n.OptionDisplayValueWithIcon=function(e){return o.createElement(o.Fragment,null,e.data.icon&&o.createElement("img",{width:"16",height:"16",style:{paddingRight:"5px"},src:e.data.icon}),e.data.html&&n.props.formatOptionElement?n.props.formatOptionElement(e):e.innerLabel)},n.CustomOption=function(e){var t=e.innerProps,r=e.isDisabled;delete t.innerRef;var i=n.props.standaloneSearch?m(m({},t),{onMouseDown:t.onClick,onClick:void 0}):t;return r?null:o.createElement(a.y.Option,m({},e),o.createElement("div",m({},i),o.createElement(n.OptionDisplayValueWithIcon,m({},e,{innerLabel:e.children}))))},n.MultiValue=function(e){return o.createElement(a.y.MultiValue,m({},e),o.createElement(n.OptionDisplayValueWithIcon,m({},e,{innerLabel:e.data.label})))},n.MultiValueRemove=function(e){return o.createElement(a.y.MultiValueRemove,m({},e,{innerProps:m(m({},e.innerProps),{onMouseUp:e.innerProps.onClick})}),e.children)},n.SingleValue=function(e){e.children;var t=g(e,["children"]);return o.createElement(a.y.SingleValue,m({},t),o.createElement(n.OptionDisplayValueWithIcon,m({},t,{innerLabel:t.data.label})))},n.NoOptionsMessage=function(e){var t=m(m({},e),{children:n.state.noOptionsMessage||e.children});return o.createElement(a.y.NoOptionsMessage,m({},t))},n.getComponent=function(e){var t;return o.createElement(i.ZP,m({},e,{className:"sr-rs ".concat(null!==(t=e.className)&&void 0!==t?t:"")}))},n.getOptions=function(){return{onMenuOpen:function(){return y(n,void 0,void 0,(function(){var e;return v(this,(function(t){switch(t.label){case 0:if(0!==this.state.defaultOptions.length)return[3,4];this.setState({isLoading:!0,noOptionsMessage:"Loading..."}),t.label=1;case 1:return t.trys.push([1,,3,4]),[4,this.getSearchResults("")];case 2:return e=t.sent(),this.setState({defaultOptions:e}),[3,4];case 3:return this.setState({isLoading:!1}),[7];case 4:return[2]}}))}))},loadOptions:n.getSearchResults,defaultOptions:n.state.defaultOptions}},n.callHandleChange=function(e){n.isSingle(n.props)?n.props.handleChange(e?e.value:null):n.props.handleChange(e?e.map((function(e){return e.value})):[])},n}return d(t,e),t.prototype.componentDidMount=function(){var e=this.props.value;e&&this.fetchItems(e)},t.prototype.componentDidUpdate=function(e,t,n){u.Z(e.value,this.props.value)||this.fetchItems(this.props.value)},t.prototype.isSingle=function(e){return"single"===e.type},t.prototype.fetchItems=function(e){return y(this,void 0,void 0,(function(){var t,n,r,o,a,i=this;return v(this,(function(s){switch(s.label){case 0:return(e="string"==typeof e||"number"==typeof e?[e]:e)?(this.setState({isLoading:!0}),t=function(e){return y(i,void 0,void 0,(function(){var t,n;return v(this,(function(r){switch(r.label){case 0:return n={key:e},this.props.fetchSingleItemByKeyFunction?[4,this.props.fetchSingleItemByKeyFunction(e)]:[3,2];case 1:return t=r.sent(),[3,4];case 2:return[4,(0,f.wrappedFetch)(this.props.fetchSingleItemByKey(e))];case 3:t=r.sent(),r.label=4;case 4:return[2,(n.request=t,n)]}}))}))},[4,Promise.all(e.map((function(e){return t(e)})))]):[3,3];case 1:return n=s.sent(),r=n.filter((function(e){return 200===e.request.response.status})).map((function(e){var t=e.request;return i.props.mapItemToOption(t.result)})),[4,Promise.all(n.filter((function(e){return 500===e.request.response.status})).map((function(e){var t=e.request,n=e.key;return y(i,void 0,void 0,(function(){var e,r,o;return v(this,(function(a){return e=t.errorResult,r=e.message,o=e.stackTrace,[2,{message:this.props.invalidItemSelected(n)+": "+r,stackTrace:o}]}))}))})))];case 2:return o=s.sent(),a=n.filter((function(e){return e.request.response.status>=300&&![401,403,500].includes(e.request.response.status)})).map((function(e){return{message:i.props.invalidItemSelected(e.key),invalidItemId:e.key}})),this.setState({isLoading:!1,errors:a.concat(o),selectedItems:r}),[3,4];case 3:this.setState({selectedItems:null,errors:[]}),s.label=4;case 4:return[2]}}))}))},t.prototype.render=function(){var e=this,n=this.isSingle(this.props),r={menuPortalTarget:this.props.menuPortalTarget,classNamePrefix:"sr-rs",styles:m({container:function(e){return m(m({},e),{maxWidth:500})},multiValueLabel:function(e){return m(m({},e),{fontSize:"100%"})},valueContainer:function(e){return m(m({},e),{overflowX:"hidden"})},menu:function(e){return m(m({},e),{zIndex:"2"})}},this.props.styles),components:{Option:this.CustomOption,SingleValue:this.SingleValue,MultiValue:this.MultiValue,MultiValueRemove:this.MultiValueRemove,MenuList:this.MenuList,NoOptionsMessage:this.NoOptionsMessage},isMulti:!n,onChange:function(t){e.setState({selectedItems:t,errors:[]}),e.callHandleChange(t)},value:this.state.selectedItems,placeholder:this.props.placeholder,isClearable:!0,isDisabled:this.props.isDisabled||this.state.isDisabled,isLoading:this.state.isLoading,closeMenuOnSelect:n,formatOptionLabel:l.s,onMenuClose:function(){e.setState({defaultOptions:[]}),e.props.onMenuClose&&e.props.onMenuClose()},defaultOptions:!0,cacheOptions:!1,onInputChange:function(n){var r,o=e.props.minCharacters;return 0===(null===(r=e.state.allItems)||void 0===r?void 0:r.length)?e.setState({noOptionsMessage:t.EMPTY_OPTIONS_SET_MESSAGE}):!Boolean(n)&&o?e.setState({noOptionsMessage:e.defaultMinCharsMessage}):n&&n.length>=o&&!e.state.allItems.find((function(e){return e.label.indexOf(n)>-1}))?e.setState({noOptionsMessage:t.EMPTY_OPTIONS_SET_MESSAGE}):void 0}};return o.createElement("span",null,this.getComponent(m(m({},r),this.getOptions())),this.state.errors.map((function(e,t){return e.stackTrace?o.createElement(p,{summary:e.message,key:e.message},o.createElement("div",{className:" serverError"},o.createElement("pre",{className:"error",style:{fontSize:"small",overflow:"auto",background:"#F6F6F6"}},e.stackTrace),o.createElement("span",null,"Note: this message is only shown to administrators"))):o.createElement("div",{className:"error",key:e.message},e.message)})),!!this.state.errors.some((function(e){return e.invalidItemId}))&&o.createElement("a",{className:"action",onClick:function(){return e.callHandleChange(e.state.selectedItems)}},"Clear invalid value(s)"))},t.EMPTY_OPTIONS_SET_MESSAGE="No options",t.defaultProps={styles:{},isDisabled:!1,minCharacters:1},t}(o.Component);var E}}]);