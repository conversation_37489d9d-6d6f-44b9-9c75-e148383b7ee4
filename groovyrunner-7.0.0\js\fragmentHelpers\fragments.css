.fragment-item {
    border: 1px solid red ! important;
    outline-color: red ! important;
}

.binding-text {
    margin-left: 5px;
    cursor: pointer;
    color: #0052cc;
    mix-blend-mode: multiply;
}

.sr-custom-info-popup {
    position: absolute;
    top: 50%;
    left: 50%;
    display: inline-block;
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    background-color: rgb(43, 43, 43);
    color: #FFF;
    text-align: left;
    border-radius: 6px;
    padding: 8px 5px;
    border: #f79232;
    border-width: 1px;
    border-style: solid;
    visibility: visible;
    -webkit-animation: fadeIn 1s;
    animation: fadeIn 1s;
    z-index: 9999;
}

/* The popup for loading msg*/
.sr-custom-info-popup .sr-custom-info-popup-loading {
    width: 200px;
    text-align: center;
}

.sr-infoheader {
    font-size: medium;
    font-weight: bold;
    color: beige;
}

.sr-infoheader-text {
    color: #9dc1e8;
}

.sr-infoheader-line {
    height: 1px;
    background: #FFF;
}

.sr-infodiv {
    padding-left: 6px;
    background: rgba(19, 40, 62, 0.2);
    border-radius: 4px;
    border-width: 2px;
    border-style: solid;
    border-color: rgba(19, 40, 62, 0.3);
}

.sr-infoheader-line-empty {
    height: 2px;
}

.sr-th-element {
    width: 23%;
    color: #ffedb7;
}

.sr-th-class {
    width: 25%;
    color: #ffedb7;
}

.sr-thDescription {
    width: 50%;
    color: #ffedb7;
}


.sr-td-text {
    vertical-align: top;
    text-align: left;
}

/* Toggle this class - hide and show the popup */
.sr-custom-info-popup .show {
    visibility: visible;
    -webkit-animation: fadeIn 1s;
    animation: fadeIn 1s;
}

/* Add animation (fade in the popup) */
@-webkit-keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.sr-hover{
    background-color : #e78f08;
}

.sr-warn-custom-ico{
    color: orange;
    vertical-align: middle;
    padding-left: 7px;
}
.sr-condition-info-heading {
    font-weight: bold;
    color: orange;
    line-height: 45px;
    font-size: medium;
    padding-left: 12px;
}

.condition-info-p{
    padding-left: 7px;
}

.sr-condition-info-txt{
    line-height: 2;
    padding-left: 12px;
    display: block;
}

.info{
    -moz-user-select: text;
    -webkit-user-select: text;
    -ms-user-select: text;
    user-select: text;
    cursor: auto;
}