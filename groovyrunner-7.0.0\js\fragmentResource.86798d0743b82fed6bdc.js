"use strict";(self.webpackJsonpScriptRunner=self.webpackJsonpScriptRunner||[]).push([["fragmentResource"],{76416:function(e,t,n){var r=this&&this.__makeTemplateObject||function(e,t){return Object.defineProperty?Object.defineProperty(e,"raw",{value:t}):e.raw=t,e},a=this&&this.__values||function(e){var t="function"==typeof Symbol&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},i=Object.create,l=Object.defineProperty,o=Object.getOwnPropertyDescriptor,c=Object.getOwnPropertyNames,u=Object.getPrototypeOf,s=Object.prototype.hasOwnProperty,d=function(e){return l(e,"__esModule",{value:!0})},f=function(e){return function(e,t,n){var r,i;if(t&&"object"==typeof t||"function"==typeof t){var u=function(r){s.call(e,r)||"default"===r||l(e,r,{get:function(){return t[r]},enumerable:!(n=o(t,r))||n.enumerable})};try{for(var d=a(c(t)),f=d.next();!f.done;f=d.next())u(f.value)}catch(e){r={error:e}}finally{try{f&&!f.done&&(i=d.return)&&i.call(d)}finally{if(r)throw r.error}}}return e}(d(l(null!=e?i(u(e)):{},"default",e&&e.__esModule&&"default"in e?{get:function(){return e.default},enumerable:!0}:{value:e,enumerable:!0})),e)};!function(e,t){for(var n in d(e),t)l(e,n,{get:t[n],enumerable:!0})}(t,{LoadingSpinner:function(){return y}});var m=f(n(63844)),g=f(n(21401)),p=f(n(40267)),y=function(e){var t=e.size,n=void 0===t?"xlarge":t,r=e.appearance,a=(e.testId,e.delay),i=e.fullHeight,l=void 0===i||i;return m.createElement(b,{fullHeight:l,className:"spinner-wrapper"},m.createElement(g.default,{size:n,appearance:r,testId:"loading-spinner",delay:a}))},b=p.default.div(r(["\n    min-height: ",";\n    display: flex;\n    align-items: center;\n    justify-content: center;\n"],["\n    min-height: ",";\n    display: flex;\n    align-items: center;\n    justify-content: center;\n"]),(function(e){return e.fullHeight?"100%":"unset"}))},98583:(e,t,n)=>{n.d(t,{T:()=>p,U:()=>y});var r=n(63844),a=n(49159),i=n(94194),l=n(3835),o=n(57700),c=n(87483),u=n(77510),s=n(76416),d=n(32248),f=n(58844),m=function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,a,i=n.call(e),l=[];try{for(;(void 0===t||t-- >0)&&!(r=i.next()).done;)l.push(r.value)}catch(e){a={error:e}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(a)throw a.error}}return l},g=function(e){var t,n=e.bindingInformation,a=Object.entries(null!==(t=n.bindingVariables)&&void 0!==t?t:[]),i=Object.entries(n.other);return r.createElement(r.Fragment,null,r.createElement("table",{className:"aui aui-table-list",style:{marginTop:15}},r.createElement("thead",null,r.createElement("tr",null,r.createElement("th",null,"Name"),r.createElement("th",null,"Type"),r.createElement("th",null,"Description"))),r.createElement("tbody",null,0===a.length?r.createElement("tr",null,r.createElement("td",{colSpan:3},"No binding variables available")):a.map((function(e){var t=m(e,2),n=t[0],a=t[1];return r.createElement("tr",{key:n},r.createElement("td",null,n),r.createElement("td",{dangerouslySetInnerHTML:{__html:(0,f.sanitize)(a[0])}}),r.createElement("td",{dangerouslySetInnerHTML:{__html:(0,f.sanitize)(a[1])}}))})))),i.length>0&&r.createElement(r.Fragment,null,r.createElement("h3",null,"Additional information"),r.createElement("table",{className:"aui aui-table-list"},r.createElement("tbody",null,i.map((function(e){var t=m(e,2),n=t[0],a=t[1];return r.createElement("tr",{key:n},r.createElement("td",null,n),r.createElement("td",null,a))}))))))},p=function(e){var t=e.isLoading,n=e.isError,a=e.bindingInformation;return t?r.createElement(s.LoadingSpinner,{size:"large"}):n?r.createElement(d.Z,{appearance:"error",title:"Error loading binding information"},"Please reload this page and try again."):a?r.createElement(r.Fragment,null,r.createElement("p",null,"The table below lists the variables that are automatically accessible within your script."),r.createElement(g,{bindingInformation:a})):null},y=function(e){var t=e.heading,n=e.isLoading,s=e.isError,d=e.bindingInformation,f=e.blanketHidden,m=e.onClose;return r.createElement(i.Z,{width:"x-large",autoFocus:!1,onClose:m,isBlanketHidden:f},r.createElement(l.Z,null,r.createElement(o.Z,null,t)),r.createElement(c.Z,null,r.createElement("div",{style:{marginTop:5,marginBottom:10}},r.createElement(p,{isLoading:n,isError:s,bindingInformation:d}))),r.createElement(u.Z,null,r.createElement(a.Z,{autoFocus:!0,appearance:"primary",onClick:m},"Close")))}},36754:(e,t,n)=>{n.d(t,{T:()=>a,e:()=>i});var r=n(40653).B.injectEndpoints({endpoints:function(e){return{getBindingInformation:e.mutation({query:function(e){var t=e.compileContextDescriptor;return{url:"".concat(AJS.contextPath(),"/rest/scriptrunner/latest/diagnostics/binding"),method:"POST",body:t}}}),getElementBindingInformation:e.query({query:function(e){return{url:"".concat(AJS.contextPath(),"/rest/scriptrunner/latest/diagnostics/element"),params:{element:e}}}})}}}),a=r.useGetBindingInformationMutation,i=r.useGetElementBindingInformationQuery},66638:(e,t,n)=>{n.d(t,{S:()=>a});var r,a=(r="jira",function(e){return void 0!==e[r]?e[r]:e.default})},40653:(e,t,n)=>{n.d(t,{B:()=>i});var r=n(65685),a=n(27451),i=(0,r.LC)({reducerPath:"query",baseQuery:(0,a.ni)({cache:"no-cache",credentials:"same-origin",prepareHeaders:function(e){return e.append("X-Atlassian-token","no-check"),e}}),tagTypes:["SettingsPermissions","SettingsAnalytics","SettingsGetStorageTab","Configured"],endpoints:function(){return{}}})},78497:(e,t,n)=>{var r=n(63844),a=n(86936),i=n(36754),l=n(98583),o=function(e){var t=e.element,n=e.onClose,a=(0,i.e)(t),o=a.isFetching,c=a.isError,u=a.data;return r.createElement(l.U,{heading:"Binding information for ".concat(t),isLoading:o,isError:c,bindingInformation:u,onClose:n,blanketHidden:!0})},c=n(65685),u=n(40653),s=n(66638),d=function(e){var t="function"==typeof Symbol&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},f="(Binding info)",m="sr-fragment-finder-modal",g=(0,s.S)({bitbucket:function(e){return!e.querySelector(".fragment-item")},default:function(e){return!0}}),p=function(){var e=document.getElementById(m);if(e)return e;var t=document.createElement("div");return t.id=m,document.body.append(t),t};AJS.toInit((function(){var e,t,n,i,l,s;try{for(var m=d(document.querySelectorAll("[id^=sr-generated]")),y=m.next();!y.done;y=m.next()){y.value.classList.add("fragment-item")}}catch(t){e={error:t}}finally{try{y&&!y.done&&(t=m.return)&&t.call(m)}finally{if(e)throw e.error}}try{for(var b=d(Array.from(document.querySelectorAll(".fragment-item")).filter(g)),v=b.next();!v.done;v=b.next()){v.value.insertAdjacentHTML("beforeend",'<span class="adaptavist-sr"><span class="binding-text">'.concat(f,"</span></span>"))}}catch(e){n={error:e}}finally{try{v&&!v.done&&(i=b.return)&&i.call(b)}finally{if(n)throw n.error}}try{for(var h=d(document.querySelectorAll(".binding-text")),E=h.next();!E.done;E=h.next()){E.value.addEventListener("click",(function(e){e.preventDefault(),e.stopPropagation();var t=e.currentTarget.closest(".fragment-item").textContent,n=t.substring(0,t.indexOf(f)),i=p();a.render(r.createElement(c.gs,{api:u.B},r.createElement(o,{element:n,onClose:function(){return a.unmountComponentAtNode(i)}})),i)}))}}catch(e){l={error:e}}finally{try{E&&!E.done&&(s=h.return)&&s.call(h)}finally{if(l)throw l.error}}}))}},e=>{e.O(0,["bhResources","fragmentResources"],(()=>{return t=78497,e(e.s=t);var t}));e.O()}]);