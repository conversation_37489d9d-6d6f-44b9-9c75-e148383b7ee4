/*! For license information please see ghShowInNavigator.cef6ee547a26a33b38fe.js.LICENSE.txt */
"use strict";(self.webpackJsonpScriptRunner=self.webpackJsonpScriptRunner||[]).push([["ghShowInNavigator"],{93510:(e,t,n)=>{n(46086);!function(e){n(46086);e("div.ghx-backlog-container").find("div.ghx-issue-count").livequery((function(t,n){var i,r=e(n).closest("div.ghx-backlog-container"),a=e(n),s=a.html(),o=e("li.selected-scope-filter").attr("title")||e("#ghx-board-name").text();if(!/View in Issue Navigator/.test(s)&&(!(i=r).hasClass("ghx-kanban-column")&&!i.hasClass("ghx-kanban-backlog"))){var u=void 0;if(r.hasClass("ghx-everything-else")){var l=decodeURIComponent((new RegExp("[?|&]"+"rapidView"+"=([^&;]+?)(&|#|;|$)").exec(location.search)||[,""])[1].replace(/\+/g,"%20"))||null,c="".concat(AJS.contextPath(),"/rest/scriptrunner-jira/latest/querylinks/").concat(l,"/backlog");a.html(s+' - <a target="_blank" title="View in Issue Navigator" href="'+c+'"><span class="aui-icon aui-icon-small aui-iconfont-view-list sr-tooltip"></span></a>')}else{u="sprint = "+r.data("sprint-id");c=AJS.contextPath()+"/issues/?jql="+encodeURIComponent(u);a.html(s+' - <a target="_blank" title="View in Issue Navigator" href="'+c+'"><span class="aui-icon aui-icon-small aui-iconfont-view-list sr-tooltip"></span></a>')}if(r.hasClass("ghx-sprint-active")){s=a.html();var p='issueFunction in addedAfterSprintStart("'+o+'", "'+r.find("div.ghx-name span").data("fieldvalue")+'")',f=s+' <a target="_blank" title="Added after sprint start" href="'+(c=AJS.contextPath()+"/issues/?jql="+encodeURIComponent(p))+'"><span class="aui-icon aui-icon-small aui-iconfont-flag sr-tooltip"></span></a>';a.html(f)}}}))}(AJS.$)},46086:(e,t,n)=>{var i,r,a;r=[n(65311)],i=function(e,t){function n(e,t,n,i){return!(e.selector!=t.selector||e.context!=t.context||n&&n.$lqguid!=t.fn.$lqguid||i&&i.$lqguid!=t.fn2.$lqguid)}e.extend(e.fn,{livequery:function(t,r){var a,s=this;return e.each(i.queries,(function(e,i){if(n(s,i,t,r))return(a=i)&&!1})),(a=a||new i(s.selector,s.context,t,r)).stopped=!1,a.run(),s},expire:function(t,r){var a=this;return e.each(i.queries,(function(e,s){n(a,s,t,r)&&!a.stopped&&i.stop(s.id)})),a}});var i=e.livequery=function(t,n,r,a){var s=this;return s.selector=t,s.context=n,s.fn=r,s.fn2=a,s.elements=e([]),s.stopped=!1,s.id=i.queries.push(s)-1,r.$lqguid=r.$lqguid||i.guid++,a&&(a.$lqguid=a.$lqguid||i.guid++),s};i.prototype={stop:function(){var t=this;t.stopped||(t.fn2&&t.elements.each(t.fn2),t.elements=e([]),t.stopped=!0)},run:function(){var t=this;if(!t.stopped){var n=t.elements,i=e(t.selector,t.context),r=i.not(n),a=n.not(i);t.elements=i,r.each(t.fn),t.fn2&&a.each(t.fn2)}}},e.extend(i,{guid:0,queries:[],queue:[],running:!1,timeout:null,registered:[],checkQueue:function(){if(i.running&&i.queue.length)for(var e=i.queue.length;e--;)i.queries[i.queue.shift()].run()},pause:function(){i.running=!1},play:function(){i.running=!0,i.run()},registerPlugin:function(){e.each(arguments,(function(t,n){if(e.fn[n]&&!(e.inArray(n,i.registered)>0)){var r=e.fn[n];e.fn[n]=function(){var e=r.apply(this,arguments);return i.run(),e},i.registered.push(n)}}))},run:function(n){n!==t?e.inArray(n,i.queue)<0&&i.queue.push(n):e.each(i.queries,(function(t){e.inArray(t,i.queue)<0&&i.queue.push(t)})),i.timeout&&clearTimeout(i.timeout),i.timeout=setTimeout(i.checkQueue,20)},stop:function(n){n!==t?i.queries[n].stop():e.each(i.queries,i.prototype.stop)}}),i.registerPlugin("append","prepend","after","before","wrap","attr","removeAttr","addClass","removeClass","toggleClass","empty","remove","html","prop","removeProp"),e((function(){i.play()}))},void 0===(a="function"==typeof i?i.apply(t,r):i)||(e.exports=a)},65311:e=>{e.exports=jQuery}},e=>{var t;t=93510,e(e.s=t)}]);