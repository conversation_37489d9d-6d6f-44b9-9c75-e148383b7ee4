(function ($) {
    $(function () {
        /**
         * Binding is required for link such as
         * http://localhost:8080/jira/projects/JRA/issues/JRA-4?filter=allopenissues
         */
        JIRA.bind(JIRA.Events.NEW_CONTENT_ADDED, function (event, $context, reason) { // <1>
            if (reason === JIRA.CONTENT_ADDED_REASON.pageLoad || reason === JIRA.CONTENT_ADDED_REASON.panelRefreshed) {
                hideCloneMenuItem();
            }
        });

        hideCloneMenuItem();
    });

    /**
     * Hide the clone menu
     */
    function hideCloneMenuItem() {
        var projectKey = "JRA"; // <2>
        var issue = JIRA.Issue.getIssueKey();
        if (typeof (issue) !== "undefined") {
            var currentProject = issue.substr(0, issue.indexOf('-')); // <3>
            if (currentProject == projectKey) {
                $("a#clone-issue").hide(); // <4>
            }
        }
    }
})(AJS.$);
