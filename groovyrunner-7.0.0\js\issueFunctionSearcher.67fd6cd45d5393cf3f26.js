"use strict";(self.webpackJsonpScriptRunner=self.webpackJsonpScriptRunner||[]).push([["issueFunctionSearcher"],{84806:(e,n,t)=>{t.d(n,{X7:()=>a,bK:()=>o});var r=function(){return r=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++)for(var o in n=arguments[t])Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o]);return e},r.apply(this,arguments)},o=function(e,n){return void 0===n&&(n={}),new Promise((function(t){return i(document.body,e,t,r({subtree:!1},n))}))},a=function(e,n,t){return void 0===t&&(t={}),new Promise((function(r){return i(e,n,r,t)}))},i=function(e,n,t,o){void 0===o&&(o={});var a=e.querySelector(n);a?t(a):new MutationObserver((function(r,o){var a=e.querySelector(n);a&&(o.disconnect(),t(a))})).observe(e,r({childList:!0,subtree:!0,attributes:!1,characterData:!1},o))}},71033:(e,n,t)=>{var r=t(63844),o=t(86936),a=t(49159),i=t(94194),u=t(3835),c=t(57700),l=t(87483),s=t(77510),f=t(40653),p="".concat(AJS.contextPath(),"/rest/scriptrunner-jira/latest/jqldiscovery"),d=f.B.injectEndpoints({endpoints:function(e){return{getFunctions:e.query({query:function(){return p}}),addClause:e.mutation({query:function(e){return{url:"".concat(p,"/addclause"),method:"POST",body:e}}})}}}),m=d.useGetFunctionsQuery,v=d.useAddClauseMutation,y=t(76416),h=t(40928),b=function(){return r.createElement(i.Z,null,r.createElement(l.Z,null,r.createElement("div",{style:{margin:25},ref:h.o},r.createElement(y.LoadingSpinner,{size:"large"}))))},g=t(34968),E=t(72236),S=t(37360),w=function(){return w=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++)for(var o in n=arguments[t])Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o]);return e},w.apply(this,arguments)},_=function(e,n){return"menu"===n.context?r.createElement(r.Fragment,null,r.createElement("div",null,e.label),e.description&&r.createElement("div",{style:{fontSize:12,color:"#6B778C",lineHeight:"12px"}},e.description)):e.label},j=function(e){var n=e.functions,t=e.setSelectedJqlFunction;return r.createElement(S.C,{name:"function",label:"Function",isRequired:!0},(function(e){var o=e.fieldProps;return r.createElement(E.Z,w({},o,{formatOptionLabel:_,options:n.map((function(e){return{label:e.name,value:e.name,description:e.description}})),spacing:"compact",placeholder:"Select function",onChange:function(e){t(n.find((function(n){return n.name===e.value})))},classNamePrefix:"sr-rs"}))}))},O=t(31762),q=t(10203),x=function(){return x=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++)for(var o in n=arguments[t])Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o]);return e},x.apply(this,arguments)},C=t(89492),P=function(){return P=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++)for(var o in n=arguments[t])Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o]);return e},P.apply(this,arguments)},k=function(e,n,t){var r=n.map((function(e){return{label:e,value:e}}));return e?r.filter((function(e){var n;return null===(n=t.value)||void 0===n?void 0:n.includes(e.value)})):r.find((function(e){return e.value===t.value}))},F=t(14849),A=function(){return A=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++)for(var o in n=arguments[t])Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o]);return e},A.apply(this,arguments)},Z={JQL_QUERY:function(e){var n=e.fieldProps,t={param:{name:"query",value:n.value},onChange:n.onChange};return r.createElement(q.Z,x({},t))},FIELD:function(e){var n=e.fieldProps;return r.createElement(C.Z,{value:n.value,handleChange:n.onChange,placeholder:"Select field",type:"single"})},SELECT:function(e){var n=e.fieldProps,t=e.multiple,o=e.values,a=e.error,i=o.map((function(e){return{label:e,value:e}})),u=k(t,o,n);return r.createElement(E.Z,P({},n,{onChange:function(e){t?n.onChange(e.map((function(e){return e.value}))):n.onChange(e.value)},isMulti:t,options:i,value:u,spacing:"compact",validationState:a?"error":"default",menuPortalTarget:document.body,styles:{menuPortal:function(e){return P(P({},e),{zIndex:15e3})}}}))},SHORT_TEXT:function(e){var n=e.fieldProps;return r.createElement(F.Z,A({},n,{isCompact:!0,autoComplete:"false"}))}},R=t(38469),L=function(e){return"JQL_QUERY"!==e.componentType&&!e.optional},T=function(e){var n=e.functionInformation;return r.createElement(r.Fragment,null,n.arguments.map((function(e,n){var t=Z[e.componentType];return r.createElement(S.C,{name:"parameter-".concat(n),label:e.description,isRequired:L(e),key:e.description,validate:function(n){return!n&&L(e)&&"Please enter a value"},defaultValue:"JQL_QUERY"===e.componentType?"":void 0},(function(n){var o=n.fieldProps,a=n.error;return r.createElement(r.Fragment,null,r.createElement(t,{values:J(e)&&e.values,error:a,multiple:e.multiple,fieldProps:o}),a&&r.createElement(R.Bc,null,a))}))})))},J=function(e){return void 0!==e.values},I=function(){return I=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++)for(var o in n=arguments[t])Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o]);return e},I.apply(this,arguments)},M=function(e){var n=e.selectedFunction,t=e.handleSubmit;return r.createElement(O.ZP,{onSubmit:t},(function(e){var t=e.formProps;return r.createElement("form",I({id:"jql-form"},t),r.createElement(T,{functionInformation:n}))}))},Q=t(32248),U=function(e){var n=e.error;return B(n)&&N(n.data)?r.createElement("div",{style:{marginTop:10}},r.createElement(Q.Z,{appearance:"error",title:"Error"},n.data.errorMessages.map((function(e){return r.createElement("p",{key:e},e)})))):null},B=function(e){return null!==e.data},N=function(e){return null!==e.errorMessages},H=t(74703),D=function(e,n,t,r){return new(t||(t=Promise))((function(o,a){function i(e){try{c(r.next(e))}catch(e){a(e)}}function u(e){try{c(r.throw(e))}catch(e){a(e)}}function c(e){var n;e.done?o(e.value):(n=e.value,n instanceof t?n:new t((function(e){e(n)}))).then(i,u)}c((r=r.apply(e,n||[])).next())}))},z=function(e,n){var t,r,o,a,i={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return a={next:u(0),throw:u(1),return:u(2)},"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function u(a){return function(u){return function(a){if(t)throw new TypeError("Generator is already executing.");for(;i;)try{if(t=1,r&&(o=2&a[0]?r.return:a[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,a[1])).done)return o;switch(r=0,o&&(a=[2&a[0],o.value]),a[0]){case 0:case 1:o=a;break;case 4:return i.label++,{value:a[1],done:!1};case 5:i.label++,r=a[1],a=[0];continue;case 7:a=i.ops.pop(),i.trys.pop();continue;default:if(!(o=i.trys,(o=o.length>0&&o[o.length-1])||6!==a[0]&&2!==a[0])){i=0;continue}if(3===a[0]&&(!o||a[1]>o[0]&&a[1]<o[3])){i.label=a[1];break}if(6===a[0]&&i.label<o[1]){i.label=o[1],o=a;break}if(o&&i.label<o[2]){i.label=o[2],i.ops.push(a);break}o[2]&&i.ops.pop(),i.trys.pop();continue}a=n.call(e,i)}catch(e){a=[6,e],r=0}finally{t=o=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,u])}}},G=function(e,n){var t="function"==typeof Symbol&&e[Symbol.iterator];if(!t)return e;var r,o,a=t.call(e),i=[];try{for(;(void 0===n||n-- >0)&&!(r=a.next()).done;)i.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(t=a.return)&&t.call(a)}finally{if(o)throw o.error}}return i},K=function(e){var n=e.queryContext,t=e.selectedFunction,o=G((0,r.useState)(),2),a=o[0],i=o[1],u=G(v(),1)[0];return(0,r.useEffect)((function(){i(void 0)}),[t]),t?r.createElement("div",{style:{marginTop:15,marginBottom:15}},r.createElement(M,{handleSubmit:function(e){return D(void 0,void 0,void 0,(function(){var r,o;return z(this,(function(a){switch(a.label){case 0:return r=Object.values(e).filter((function(e){return void 0!==e})).flat(),[4,u({originalJql:"",arguments:r,functionName:t.name})];case 1:return"data"in(o=a.sent())?((0,H.w)("jqlUIAction",{params:{action:"functionAdded",functionName:t.name}}),n.setQuery(o.data)):i(o.error),[2]}}))}))},selectedFunction:t}),a&&r.createElement(U,{error:a})):null},Y=function(){return Y=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++)for(var o in n=arguments[t])Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o]);return e},Y.apply(this,arguments)},V=function(e,n){var t="function"==typeof Symbol&&e[Symbol.iterator];if(!t)return e;var r,o,a=t.call(e),i=[];try{for(;(void 0===n||n-- >0)&&!(r=a.next()).done;)i.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(t=a.return)&&t.call(a)}finally{if(o)throw o.error}}return i},X=t(49212),W=function(e){var n=e.closeModal,t=e.queryContext,o=m(),f=o.data,p=o.isLoading,d=o.error,v=V((0,r.useState)(void 0),2),y=v[0],E=v[1];return(0,r.useEffect)((function(){return X.disable(),X.enable}),[]),p?r.createElement(b,null):r.createElement(i.Z,{onClose:n,width:"medium",shouldScrollInViewport:!0,testId:"add-jql-function"},r.createElement(u.Z,null,r.createElement(c.Z,null,"Add ScriptRunner JQL Function")),r.createElement(l.Z,null,r.createElement("div",{ref:h.o},r.createElement(g.UC,null,d&&r.createElement(Q.Z,{appearance:"error",title:"Error loading JQL functions"},"Please try reopening this modal or reloading the page."),!d&&r.createElement(r.Fragment,null,r.createElement("p",null,"Select a function from the list below, and then fill out any required parameters."),r.createElement("div",{style:{marginTop:15}},r.createElement(j,{functions:f,setSelectedJqlFunction:E}),r.createElement(K,{queryContext:t,selectedFunction:y})))))),r.createElement(s.Z,null,[{text:"Cancel",onClick:n,appearance:"subtle"},{text:"Add function",form:"jql-form",type:"submit",appearance:"primary",isDisabled:p||!y,isLoading:p}].map((function(e,n){return r.createElement(a.Z,Y({},e,{appearance:0===n?e.appearance||"primary":e.appearance||"subtle"}),e.text)}))))},ee=t(65685),ne=t(2589),te=t(27462),re=function(e,n){var t="function"==typeof Symbol&&e[Symbol.iterator];if(!t)return e;var r,o,a=t.call(e),i=[];try{for(;(void 0===n||n-- >0)&&!(r=a.next()).done;)i.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(t=a.return)&&t.call(a)}finally{if(o)throw o.error}}return i},oe=function(e,n,t){if(t||2===arguments.length)for(var r,o=0,a=n.length;o<a;o++)!r&&o in n||(r||(r=Array.prototype.slice.call(n,0,o)),r[o]=n[o]);return e.concat(r||Array.prototype.slice.call(n))},ae=function(e){var n=e.functions,t=e.setFunctions,o=e.openModal;return 0===n.length?r.createElement(te.Z,{header:"No functions selected",description:"Click Add function below to add one.",primaryAction:r.createElement(a.Z,{appearance:"primary",onMouseUp:o},"Add function")}):r.createElement(r.Fragment,null,r.createElement(ne.Z,{head:{cells:[{key:"function",content:"Function"},{key:"actions",content:r.createElement("div",{style:{textAlign:"right"}},"Actions"),width:30}]},rows:n.map((function(e,o){return{cells:[{key:e,content:e},{key:"".concat(e,"-remove"),content:r.createElement("div",{style:{textAlign:"right"}},r.createElement(a.Z,{appearance:"link",spacing:"none",onMouseUp:function(){var e=oe([],re(n),!1);e.splice(o,1),t(e)}},"Remove"))}]}}))}),r.createElement("span",{className:"aui-button",onMouseUp:o},"Add function"))},ie=function(e,n){var t="function"==typeof Symbol&&e[Symbol.iterator];if(!t)return e;var r,o,a=t.call(e),i=[];try{for(;(void 0===n||n-- >0)&&!(r=a.next()).done;)i.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(t=a.return)&&t.call(a)}finally{if(o)throw o.error}}return i},ue=function(e,n,t){if(t||2===arguments.length)for(var r,o=0,a=n.length;o<a;o++)!r&&o in n||(r||(r=Array.prototype.slice.call(n,0,o)),r[o]=n[o]);return e.concat(r||Array.prototype.slice.call(n))},ce=function(e){AJS.currentLayerItem&&(AJS.currentLayerItem._validateClickToClose=function(){return e})},le=function(e){var n=e.searchedFunctions,t=e.updateSearchedFunctions,o=ie((0,r.useState)(!1),2),a=o[0],i=o[1],u=ie((0,r.useState)(n),2),c=u[0],l=u[1],s=(0,r.useRef)(!0);(0,r.useEffect)((function(){s.current?s.current=!1:t(c)}),[c]);var p=function(){ce(!0),i(!1)};return r.createElement("div",{style:{minWidth:400}},r.createElement(ae,{functions:c,setFunctions:l,openModal:function(){ce(!1),i(!0)}}),a&&r.createElement(ee.gs,{api:f.B},r.createElement(W,{closeModal:p,queryContext:{setQuery:function(e){l(ue(ue([],ie(c),!1),[e],!1)),p()}}})))},se=t(19600),fe=t(84806),pe=function(e,n,t,r){return new(t||(t=Promise))((function(o,a){function i(e){try{c(r.next(e))}catch(e){a(e)}}function u(e){try{c(r.throw(e))}catch(e){a(e)}}function c(e){var n;e.done?o(e.value):(n=e.value,n instanceof t?n:new t((function(e){e(n)}))).then(i,u)}c((r=r.apply(e,n||[])).next())}))},de=function(e,n){var t,r,o,a,i={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return a={next:u(0),throw:u(1),return:u(2)},"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function u(a){return function(u){return function(a){if(t)throw new TypeError("Generator is already executing.");for(;i;)try{if(t=1,r&&(o=2&a[0]?r.return:a[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,a[1])).done)return o;switch(r=0,o&&(a=[2&a[0],o.value]),a[0]){case 0:case 1:o=a;break;case 4:return i.label++,{value:a[1],done:!1};case 5:i.label++,r=a[1],a=[0];continue;case 7:a=i.ops.pop(),i.trys.pop();continue;default:if(!(o=i.trys,(o=o.length>0&&o[o.length-1])||6!==a[0]&&2!==a[0])){i=0;continue}if(3===a[0]&&(!o||a[1]>o[0]&&a[1]<o[3])){i.label=a[1];break}if(6===a[0]&&i.label<o[1]){i.label=o[1],o=a;break}if(o&&i.label<o[2]){i.label=o[2],i.ops.push(a);break}o[2]&&i.ops.pop(),i.trys.pop();continue}a=n.call(e,i)}catch(e){a=[6,e],r=0}finally{t=o=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,u])}}},me=function(){var e=document.getElementById("issue-function-searcher-root"),n=e.parentElement.querySelector("input"),t=function(e){document.querySelector(".atlaskit-portal.adaptavist-sr")&&e.preventDefault()};$(document).on("InlineLayer.beforeHide",t);var a=n.value?JSON.parse(n.value):[];document.querySelector("#issue-filter .form-footer").style.display="none",(0,H.w)("jqlUIAction",{params:{action:"searcherOpened"}}),o.render(r.createElement(le,{searchedFunctions:a,updateSearchedFunctions:function(e){n.value=JSON.stringify(e);var r=document.querySelector("#issue-filter .form-footer input");$(document).off("InlineLayer.beforeHide",t),r.click()}}),e)},ve=function(){return pe(void 0,void 0,void 0,(function(){var e,n,t,r;return de(this,(function(o){switch(o.label){case 0:return[4,(0,fe.bK)("#all-criteria")];case 1:return e=o.sent(),(n=null===(r=e.querySelector('[title="issueFunction"]'))||void 0===r?void 0:r.parentElement)?(e.previousSibling.insertAdjacentHTML("beforebegin",'\n            <h5>ScriptRunner</h5>\n            <ul id="scriptrunner-criteria" class="aui-list-section" aria-label="ScriptRunner" />\n        '),document.getElementById("scriptrunner-criteria").appendChild(n),t=document.getElementById("criteria-multi-select"),new MutationObserver((function(){t.dataset.query||setTimeout(ve,0)})).observe(t,{attributes:!0}),[2]):[2]}}))}))},ye=function(){setTimeout(ve,0)},he=function(){return pe(void 0,void 0,void 0,(function(){var e;return de(this,(function(n){return(e=document.querySelector(".add-criteria"))&&e.addEventListener("click",ye),[2]}))}))};AJS.toInit((function(){return pe(void 0,void 0,void 0,(function(){var e,n;return de(this,(function(t){switch(t.label){case 0:return window.ScriptRunner=se.Z(null!==(n=window.ScriptRunner)&&void 0!==n?n:{},{feature:{renderSearcher:me}}),[4,(0,fe.bK)(".search-container")];case 1:return e=t.sent(),new MutationObserver(he).observe(e,{attributes:!0,subtree:!0}),he().catch(console.warn),[2]}}))}))}))},49212:e=>{e.exports=require("jira/ajs/keyboardshortcut/keyboard-shortcut-toggle")}},e=>{e.O(0,["bhResources","fragmentResources","jsdCannedCommentResources","jqlQueryResources","issueFunctionSearcherResources","default-src_main_resources_js_behaviours_serialiseQueryString_ts-src_main_resources_js_compon-2a076c","default-frontend-components_packages_loading-spinner_dist_index_js-src_main_resources_js_admi-afd5fc","default-src_main_resources_js_admin_params_JQLQueryParam_tsx-src_main_resources_js_components-f807ef"],(()=>{return n=71033,e(e.s=n);var n}));e.O()}]);