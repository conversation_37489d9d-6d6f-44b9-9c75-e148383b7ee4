import 'wr-dependency!com.atlassian.plugins.jquery:jquery'

console.log('Initialize jasmine')

// expose core environment
global.jasmineRequire = require('jasmine-core/lib/jasmine-core/jasmine')
global.getJasmineRequireObj = () => global.jasmineRequire

require('jasmine-core/lib/jasmine-core/jasmine.css')

// require jasmine HTML
require('jasmine-core/lib/jasmine-core/jasmine-html')

// require reporter
global.jasmineReporters = require('jasmine-reporters/src/junit_reporter')
global.bililiteRange = {}

// boot up jasmine
require('jasmine-core/lib/jasmine-core/boot')

require('jasmine-ajax')
require('jasmine-jquery')
require('jquery-sendkeys')
require('./bililiteRange')

const reporterOptions = {
    consolidateAll: true,
}

const queryString = global.jasmine.QueryString({
    getWindowLocation: function () {
        return window.location
    },
})

if (queryString.getParam('delayLoad')) {
    // store and remove current onload function
    const oldOnload = window.onload
    window.onload = function () {}
    // run tests after everything is loaded
    document.addEventListener('runtests', function () {
        oldOnload()
    })
}

const suiteName = queryString.getParam('suite')
if (suiteName) {
    reporterOptions.modifySuiteName = (fullName) => {
        return `${suiteName}-${fullName}`
    }
}

const xmlRecordingJUnitReporter = new global.jasmineReporters.JUnitXmlReporter(reporterOptions)

xmlRecordingJUnitReporter.writeFile = (filename, text) => {
    let $results = AJS.$('#sr-test-results')
    if (!$results.length) {
        $results = AJS.$('<textarea/>', {
            id: 'sr-test-results',
        })
        AJS.$('body').addClass('adaptavist-sr')
        AJS.$('body').append($results)
    }

    $results.val(text)
}

global.jasmine.getEnv().addReporter(xmlRecordingJUnitReporter)

const jasmineUnsupportedJestMatchers = require('./unsupportedJestMatchers')
global.jasmine.getEnv().beforeAll(() => {
    jasmine.addMatchers(jasmineUnsupportedJestMatchers)
})

global.currentSpec = null

class CurrentSpecReporter {
    specStarted(spec) {
        global.currentSpec = spec
        // Uncomment the line below if you have test failures that may be caused by execution of a previous test etc
        // console.info('Starting spec:', spec.fullName)
    }

    specDone() {
        global.currentSpec = null
    }
}

global.jasmine.getEnv().addReporter(new CurrentSpecReporter())

global.jasmine.getEnv().configure({ ...global.jasmine.getEnv(), random: false })
global.jasmine.DEFAULT_TIMEOUT_INTERVAL = 60_000
