// Attempt to throw a more specific error message when using Jest matchers in Jasmine tests

const jestDomMatchers = require('@testing-library/jest-dom/matchers')
const jasmineUnsupportedJestMatchers = Object.keys(jestDomMatchers).reduce((matchers, currentMatcher) => {
    matchers[currentMatcher] = () => ({
        compare: (_actual, _expected) => ({
            pass: false,
            message: 'Jest matchers are not supported in Jasmine tests, please use Jasmine matcher instead.',
        }),
    })
    return matchers
}, {})

module.exports = jasmineUnsupportedJestMatchers
