"use strict";(self.webpackJsonpScriptRunner=self.webpackJsonpScriptRunner||[]).push([["jqlQueryDoc"],{74729:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(o,a){function i(e){try{u(r.next(e))}catch(e){a(e)}}function c(e){try{u(r.throw(e))}catch(e){a(e)}}function u(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(i,c)}u((r=r.apply(e,t||[])).next())}))},o=this&&this.__generator||function(e,t){var n,r,o,a,i={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return a={next:c(0),throw:c(1),return:c(2)},"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function c(a){return function(c){return function(a){if(n)throw new TypeError("Generator is already executing.");for(;i;)try{if(n=1,r&&(o=2&a[0]?r.return:a[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,a[1])).done)return o;switch(r=0,o&&(a=[2&a[0],o.value]),a[0]){case 0:case 1:o=a;break;case 4:return i.label++,{value:a[1],done:!1};case 5:i.label++,r=a[1],a=[0];continue;case 7:a=i.ops.pop(),i.trys.pop();continue;default:if(!(o=i.trys,(o=o.length>0&&o[o.length-1])||6!==a[0]&&2!==a[0])){i=0;continue}if(3===a[0]&&(!o||a[1]>o[0]&&a[1]<o[3])){i.label=a[1];break}if(6===a[0]&&i.label<o[1]){i.label=o[1],o=a;break}if(o&&i.label<o[2]){i.label=o[2],i.ops.push(a);break}o[2]&&i.ops.pop(),i.trys.pop();continue}a=t.call(e,i)}catch(e){a=[6,e],r=0}finally{n=o=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,c])}}},a=this&&this.__values||function(e){var t="function"==typeof Symbol&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},i=Object.create,c=Object.defineProperty,u=Object.defineProperties,l=Object.getOwnPropertyDescriptor,s=Object.getOwnPropertyDescriptors,f=Object.getOwnPropertyNames,d=Object.getOwnPropertySymbols,p=Object.getPrototypeOf,h=Object.prototype.hasOwnProperty,v=Object.prototype.propertyIsEnumerable,b=function(e,t,n){return t in e?c(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n},y=function(e,t){var n,r;for(var o in t||(t={}))h.call(t,o)&&b(e,o,t[o]);if(d)try{for(var i=a(d(t)),c=i.next();!c.done;c=i.next()){o=c.value;v.call(t,o)&&b(e,o,t[o])}}catch(e){n={error:e}}finally{try{c&&!c.done&&(r=i.return)&&r.call(i)}finally{if(n)throw n.error}}return e},g=function(e,t){return u(e,s(t))},m=function(e){return c(e,"__esModule",{value:!0})};!function(e,t){for(var n in m(e),t)c(e,n,{get:t[n],enumerable:!0})}(t,{fetchJson:function(){return q},getRequestCountValue:function(){return E},trackedFetchFactory:function(){return P},wrappedFetch:function(){return S}});var w,x=(w=n(60208),function(e,t,n){var r,o;if(t&&"object"==typeof t||"function"==typeof t){var i=function(r){h.call(e,r)||"default"===r||c(e,r,{get:function(){return t[r]},enumerable:!(n=l(t,r))||n.enumerable})};try{for(var u=a(f(t)),s=u.next();!s.done;s=u.next())i(s.value)}catch(e){r={error:e}}finally{try{s&&!s.done&&(o=u.return)&&o.call(u)}finally{if(r)throw r.error}}}return e}(m(c(null!=w?i(p(w)):{},"default",w&&w.__esModule&&"default"in w?{get:function(){return w.default},enumerable:!0}:{value:w,enumerable:!0})),w)),j="Content-Type",O="application/json",P=function(e){return function(t,n){return C(e),S(t,n).finally((function(){return _(e)}))}};function k(e){var t=e.headers.get(j);return t&&-1===t.indexOf("text/html")&&-1===t.indexOf("text/plain")?-1!==t.indexOf("application/json")||t.startsWith("application/")&&-1!==t.indexOf("+json;")?e.text().then((function(e){return e.length>0?JSON.parse(e):null})):t.startsWith("image/")?e.blob():Promise.resolve(null):e.text()}var S=function(e,t){return r(void 0,void 0,void 0,(function(){var n;return o(this,(function(r){return n=(0,x.deepmerge)(function(){var e;return{credentials:"same-origin",headers:(e={"Cache-Control":"no-cache"},e[j]=O,e["X-Atlassian-token"]="no-check",e)}}(),t||{}),[2,fetch(e,n).then((function(e){if(!e.ok){var t={error:e.statusText||"request failed",response:e};return k(e).then((function(e){return Promise.resolve(g(y({},t),{errorResult:e}))})).catch((function(e){return Promise.resolve(t)}))}return k(e).then((function(t){return Promise.resolve({result:t,response:e})})).catch((function(t){return n.method&&["delete","post"].includes(n.method.toLowerCase())?Promise.resolve({result:{},response:e}):(console.warn("Could not parse: ".concat(t)),Promise.resolve({error:"Could not parse: ".concat(t)}))}))})).catch((function(e){return console.warn("Error fetching",e),Promise.resolve({error:"Network ".concat(e)})}))]}))}))},q=function(e,t){return r(void 0,void 0,void 0,(function(){var n;return o(this,(function(r){return[2,S(e,g(y({},t),{headers:g(y({},null!=(n=null==t?void 0:t.headers)?n:{}),{Accept:O})}))]}))}))},C=function(e){e&&e.length&&T(e,E(e)+1)},_=function(e){e&&e.length&&T(e,E(e)-1)},E=function(e){return Number.parseInt(document.body.dataset[e]||"0",10)},T=function(e,t){document.body.dataset[e]=t.toString()}},84806:(e,t,n)=>{n.d(t,{X7:()=>a,bK:()=>o});var r=function(){return r=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},r.apply(this,arguments)},o=function(e,t){return void 0===t&&(t={}),new Promise((function(n){return i(document.body,e,n,r({subtree:!1},t))}))},a=function(e,t,n){return void 0===n&&(n={}),new Promise((function(r){return i(e,t,r,n)}))},i=function(e,t,n,o){void 0===o&&(o={});var a=e.querySelector(t);a?n(a):new MutationObserver((function(r,o){var a=e.querySelector(t);a&&(o.disconnect(),n(a))})).observe(e,r({childList:!0,subtree:!0,attributes:!1,characterData:!1},o))}},61792:(e,t,n)=>{var r=n(149),o=n.n(r),a=n(27698),i=n.n(a),c=n(84806),u=n(74729),l=function(){return l=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},l.apply(this,arguments)},s=function(e,t,n,r){return new(n||(n=Promise))((function(o,a){function i(e){try{u(r.next(e))}catch(e){a(e)}}function c(e){try{u(r.throw(e))}catch(e){a(e)}}function u(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(i,c)}u((r=r.apply(e,t||[])).next())}))},f=function(e,t){var n,r,o,a,i={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return a={next:c(0),throw:c(1),return:c(2)},"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function c(a){return function(c){return function(a){if(n)throw new TypeError("Generator is already executing.");for(;i;)try{if(n=1,r&&(o=2&a[0]?r.return:a[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,a[1])).done)return o;switch(r=0,o&&(a=[2&a[0],o.value]),a[0]){case 0:case 1:o=a;break;case 4:return i.label++,{value:a[1],done:!1};case 5:i.label++,r=a[1],a=[0];continue;case 7:a=i.ops.pop(),i.trys.pop();continue;default:if(!(o=i.trys,(o=o.length>0&&o[o.length-1])||6!==a[0]&&2!==a[0])){i=0;continue}if(3===a[0]&&(!o||a[1]>o[0]&&a[1]<o[3])){i.label=a[1];break}if(6===a[0]&&i.label<o[1]){i.label=o[1],o=a;break}if(o&&i.label<o[2]){i.label=o[2],i.ops.push(a);break}o[2]&&i.ops.pop(),i.trys.pop();continue}a=t.call(e,i)}catch(e){a=[6,e],r=0}finally{n=o=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,c])}}},d=function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,a=n.call(e),i=[];try{for(;(void 0===t||t-- >0)&&!(r=a.next()).done;)i.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=a.return)&&n.call(a)}finally{if(o)throw o.error}}return i},p=AJS.$,h=n(34166),v="true"===p("meta[name='ajs-is-admin']").attr("content"),b=function(){if(!(p("#sr-jql-query-profile").length>0)){p(".search-button").after(p(".search-button").clone().off("click").on("click",(function(e){var t=p(this);t.find("span").removeClass("aui-iconfont-nav-children").addClass("aui-icon-wait"),e.preventDefault(),e.stopImmediatePropagation(),p.ajax({type:"GET",dataType:"json",contentType:"application/json",url:"".concat(AJS.contextPath(),"/rest/scriptrunner-jira/latest/jqlfunctions/profile"),data:p("form.navigator-search").serialize(),success:function(e){var t=new AJS.Dialog({width:800,height:500,id:"jql-profile-dialog",closeOnOutsideClick:!0});t.addHeader("Query Profile"),t.addPanel("Profile","<pre>"+e.profile+"</pre>","panel-body"),t.addPanel("Lucene","<p>"+e.queryTree+"</p>","panel-body"),t.addPanel("Resources","<pre>"+e.opTimers.join("\n")+"</pre>","panel-body"),t.addButton("Close",(function(e){e.hide()})),t.gotoPage(0),t.gotoPanel(0),t.show()},error:function(e){alert("Error: the provided query probably wasn't valid, please run it first.")},complete:function(){t.find("span").removeClass("aui-icon-wait").addClass("aui-iconfont-nav-children")}})})));var e=p(".search-button:last");e.hasClass("aui-button-primary")?(e.text(""),e.removeClass("aui-button-primary").addClass("aui-item"),e.append("<span class='icon aui-icon aui-icon-small aui-iconfont-location' id='sr-jql-query-profile'>Search</span>")):e.find("span.aui-icon").removeClass("aui-iconfont-search").addClass("aui-iconfont-nav-children").attr("id","sr-jql-query-profile")}},y=function(){return p("#advanced-search").val()},g=(0,u.trackedFetchFactory)("aggregateExpressionServerCallCount"),m=function(){return s(void 0,void 0,void 0,(function(){var e,t,n,r,o;return f(this,(function(a){switch(a.label){case 0:return/aggregateExpression/i.test(y())?(e=p("form.navigator-search").serialize(),[4,g("".concat(AJS.contextPath(),"/rest/scriptrunner-jira/latest/jqlfunctions/aggregateResult?").concat(e))]):[3,2];case 1:if(null!=(t=a.sent()).result&&!p.isEmptyObject(t.result))return p("#aggregate-results").length||(p("div.notifications").append('<div class="aui-message aui-message-info" style="font-size: unset; color: unset; padding-left: 42px; padding-top: 12px;" id="aggregate-results"><div id="aggregate-data"></div></div>'),n=p("#aggregate-data").append("<table></table>"),p.each(t.result,(function(e,t){n.append("<tr><td ><em>"+e+": </em> </td><td>"+t+"</td></tr>")}))),[2];(r=null===(o=t.error)||void 0===o?void 0:o.message)&&p("div.notifications").append('<div class="aui-message aui-message-error"><div id="aggregate-data">'+r+"</div></div>"),a.label=2;case 2:return[2]}}))}))},w=i()((function(){return o()(m,500,{leading:!0,trailing:!1})})),x=function(){var e=y();w.cache.has(e)||w.cache.clear(),w(e)()},j=function(){document.getElementById("advanced-search")&&(x(),p("#advanced-search").on("keypress",(function(e){13===e.which&&x()})),v&&b(),p("button.search-button").off("click.sr").on("click.sr",x))};p((function(){return s(void 0,void 0,void 0,(function(){var e,t;return f(this,(function(n){switch(n.label){case 0:return[4,(0,c.bK)("form.query-component")];case 1:return e=n.sent(),[4,(0,c.X7)(e,'div.search-container[data-mode="advanced"]')];case 2:return t=n.sent(),(0,u.wrappedFetch)("".concat(AJS.contextPath(),"/rest/scriptrunner-jira/latest/jqlfunctions/docs")).then((function(e){if(e.result){var n=Object.entries(e.result).reduce((function(e,t){var n,r=d(t,2),o=r[0],a=r[1];return a.enabled?l(l({},e),((n={})[o]=a,n)):e}),{}),r=new MutationObserver((function(){if(document.getElementById("jql_function_suggest_0")){var e=p("div.suggestions");e.attr("style",k(e)),function(e){var t=p("div.suggestions.dropdown-ready li");t.each((function(n,r){var o=p(r);if(0===o.attr("id").indexOf("jql_function_suggest_")){var a=o.text(),i=a.substr(0,a.indexOf("(")),c=e[i];c&&o.html(S(c,i,o.html(),1===t.length))}}))}(n)}})),o=p("#advanced-search");o.on("input",(function(e){var t=e.originalEvent.data;if(t&&t.endsWith("()")){var r=n[t.substring(0,t.length-"()".length)];if(o.trigger("click"),r&&r.args.find((function(e){return!e.optional}))){var a=o.get(0);return void(a.selectionEnd=a.selectionEnd-1)}o.val("".concat(o.val()," "))}})),r.observe(t,{childList:!0,subtree:!0})}})),j(),new MutationObserver(j).observe(t,{attributes:!0}),h().on("change",(function(){x()})),v&&b(),[2]}}))}))}));var O=/!important/,P=function(){return p("#jqltext,  #advanced-search").width()},k=function(e,t){void 0===t&&(t=P);var n=e.attr("style");if(!O.test(n)){var r=t();return"".concat(n,"; width: ").concat(r,"px !important; max-width: ").concat(r,"px !important;")}return n},S=function(e,t,n,r){var o=n.indexOf("</b>");n=n.replace(/\(.*\)/," (");var a=o>t.length+3;return r&&a&&(n=n.substr(3)),0===(n+=e.args.map(q(r&&a)).join(", ")+")").indexOf("<b>")&&-1===n.indexOf("</b>")&&(n+="</b>"),e.docUrl&&(n+=C(e.docUrl)),e.description&&(n+=' <span style="float: right;">'.concat(e.description,"</span>")),n},q=function(e){return function(t,n){var r=e&&0===n,o=(t.optional?"[":"")+"<i>"+(r?"<b>":""),a=(r?"</b>":"")+"</i>"+(t.optional?"]":"");return o+t.description+a}},C=function(e){return' <a href="'.concat(e,'" target="_blank" style="color: #707070; margin-left: 4px; float: right;">')+'<span class="aui-icon aui-icon-small aui-iconfont-help">Function Help</span></a>'}}},e=>{e.O(0,["bhResources","jqlQueryResources"],(()=>{return t=61792,e(e.s=t);var t}));e.O()}]);