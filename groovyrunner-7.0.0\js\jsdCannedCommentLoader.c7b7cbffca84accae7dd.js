(self.webpackJsonpScriptRunner=self.webpackJsonpScriptRunner||[]).push([["jsdCannedCommentLoader"],{74729:function(e,n,t){"use strict";var r=this&&this.__awaiter||function(e,n,t,r){return new(t||(t=Promise))((function(o,i){function c(e){try{u(r.next(e))}catch(e){i(e)}}function a(e){try{u(r.throw(e))}catch(e){i(e)}}function u(e){var n;e.done?o(e.value):(n=e.value,n instanceof t?n:new t((function(e){e(n)}))).then(c,a)}u((r=r.apply(e,n||[])).next())}))},o=this&&this.__generator||function(e,n){var t,r,o,i,c={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function a(i){return function(a){return function(i){if(t)throw new TypeError("Generator is already executing.");for(;c;)try{if(t=1,r&&(o=2&i[0]?r.return:i[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,i[1])).done)return o;switch(r=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return c.label++,{value:i[1],done:!1};case 5:c.label++,r=i[1],i=[0];continue;case 7:i=c.ops.pop(),c.trys.pop();continue;default:if(!(o=c.trys,(o=o.length>0&&o[o.length-1])||6!==i[0]&&2!==i[0])){c=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){c.label=i[1];break}if(6===i[0]&&c.label<o[1]){c.label=o[1],o=i;break}if(o&&c.label<o[2]){c.label=o[2],c.ops.push(i);break}o[2]&&c.ops.pop(),c.trys.pop();continue}i=n.call(e,c)}catch(e){i=[6,e],r=0}finally{t=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,a])}}},i=this&&this.__values||function(e){var n="function"==typeof Symbol&&Symbol.iterator,t=n&&e[n],r=0;if(t)return t.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(n?"Object is not iterable.":"Symbol.iterator is not defined.")},c=Object.create,a=Object.defineProperty,u=Object.defineProperties,s=Object.getOwnPropertyDescriptor,l=Object.getOwnPropertyDescriptors,f=Object.getOwnPropertyNames,d=Object.getOwnPropertySymbols,p=Object.getPrototypeOf,h=Object.prototype.hasOwnProperty,m=Object.prototype.propertyIsEnumerable,v=function(e,n,t){return n in e?a(e,n,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[n]=t},b=function(e,n){var t,r;for(var o in n||(n={}))h.call(n,o)&&v(e,o,n[o]);if(d)try{for(var c=i(d(n)),a=c.next();!a.done;a=c.next()){o=a.value;m.call(n,o)&&v(e,o,n[o])}}catch(e){t={error:e}}finally{try{a&&!a.done&&(r=c.return)&&r.call(c)}finally{if(t)throw t.error}}return e},y=function(e,n){return u(e,l(n))},g=function(e){return a(e,"__esModule",{value:!0})};!function(e,n){for(var t in g(e),n)a(e,t,{get:n[t],enumerable:!0})}(n,{fetchJson:function(){return C},getRequestCountValue:function(){return A},trackedFetchFactory:function(){return k},wrappedFetch:function(){return P}});var w,O=(w=t(60208),function(e,n,t){var r,o;if(n&&"object"==typeof n||"function"==typeof n){var c=function(r){h.call(e,r)||"default"===r||a(e,r,{get:function(){return n[r]},enumerable:!(t=s(n,r))||t.enumerable})};try{for(var u=i(f(n)),l=u.next();!l.done;l=u.next())c(l.value)}catch(e){r={error:e}}finally{try{l&&!l.done&&(o=u.return)&&o.call(u)}finally{if(r)throw r.error}}}return e}(g(a(null!=w?c(p(w)):{},"default",w&&w.__esModule&&"default"in w?{get:function(){return w.default},enumerable:!0}:{value:w,enumerable:!0})),w)),x="Content-Type",S="application/json",k=function(e){return function(n,t){return R(e),P(n,t).finally((function(){return E(e)}))}};function j(e){var n=e.headers.get(x);return n&&-1===n.indexOf("text/html")&&-1===n.indexOf("text/plain")?-1!==n.indexOf("application/json")||n.startsWith("application/")&&-1!==n.indexOf("+json;")?e.text().then((function(e){return e.length>0?JSON.parse(e):null})):n.startsWith("image/")?e.blob():Promise.resolve(null):e.text()}var P=function(e,n){return r(void 0,void 0,void 0,(function(){var t;return o(this,(function(r){return t=(0,O.deepmerge)(function(){var e;return{credentials:"same-origin",headers:(e={"Cache-Control":"no-cache"},e[x]=S,e["X-Atlassian-token"]="no-check",e)}}(),n||{}),[2,fetch(e,t).then((function(e){if(!e.ok){var n={error:e.statusText||"request failed",response:e};return j(e).then((function(e){return Promise.resolve(y(b({},n),{errorResult:e}))})).catch((function(e){return Promise.resolve(n)}))}return j(e).then((function(n){return Promise.resolve({result:n,response:e})})).catch((function(n){return t.method&&["delete","post"].includes(t.method.toLowerCase())?Promise.resolve({result:{},response:e}):(console.warn("Could not parse: ".concat(n)),Promise.resolve({error:"Could not parse: ".concat(n)}))}))})).catch((function(e){return console.warn("Error fetching",e),Promise.resolve({error:"Network ".concat(e)})}))]}))}))},C=function(e,n){return r(void 0,void 0,void 0,(function(){var t;return o(this,(function(r){return[2,P(e,y(b({},n),{headers:y(b({},null!=(t=null==n?void 0:n.headers)?t:{}),{Accept:S})}))]}))}))},R=function(e){e&&e.length&&I(e,A(e)+1)},E=function(e){e&&e.length&&I(e,A(e)-1)},A=function(e){return Number.parseInt(document.body.dataset[e]||"0",10)},I=function(e,n){document.body.dataset[e]=n.toString()}},84806:(e,n,t)=>{"use strict";t.d(n,{X7:()=>i,bK:()=>o});var r=function(){return r=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++)for(var o in n=arguments[t])Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o]);return e},r.apply(this,arguments)},o=function(e,n){return void 0===n&&(n={}),new Promise((function(t){return c(document.body,e,t,r({subtree:!1},n))}))},i=function(e,n,t){return void 0===t&&(t={}),new Promise((function(r){return c(e,n,r,t)}))},c=function(e,n,t,o){void 0===o&&(o={});var i=e.querySelector(n);i?t(i):new MutationObserver((function(r,o){var i=e.querySelector(n);i&&(o.disconnect(),t(i))})).observe(e,r({childList:!0,subtree:!0,attributes:!1,characterData:!1},o))}},7011:(e,n,t)=>{"use strict";t.d(n,{$7:()=>o,Cc:()=>i,aT:()=>u,k1:()=>a});var r=function(){return r=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++)for(var o in n=arguments[t])Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o]);return e},r.apply(this,arguments)},o=function(e,n){void 0===n&&(n={}),window.require(["aui/flag"],(function(t){return t(r({type:"success",title:"Success",persistent:!1,close:"auto",body:e},n))}))},i=function(e){return void 0===e&&(e={}),o(null,e)},c=function(e,n,t){return function(r,o){var c=void 0===o?{}:o,a=c.title,u=void 0===a?n:a,s=c.close;return i({type:e,title:u,close:void 0===s?t:s,body:r,persistent:!1}),r}},a=c("error","Error","auto"),u=c("success","Success","auto")},39507:(e,n,t)=>{"use strict";t.d(n,{F:()=>c});var r=t(17619),o=t(29577),i=t(16897),c=function(e){return r.stringify(o.Z((function(e){return!i.Z(e)}),e))}},75171:(e,n,t)=>{"use strict";t(83212);var r,o=t(86936),i=t(63844),c=t(72236),a=t(37360),u=t(18390),s=function(e,n){return Object.defineProperty?Object.defineProperty(e,"raw",{value:n}):e.raw=n,e},l=function(){return l=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++)for(var o in n=arguments[t])Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o]);return e},l.apply(this,arguments)},f=function(e){var n=e.comments,t=e.context,r=e.fillComment,o=n.map((function(e){return{label:e.name,value:e.id}})),u=function(e){return r(e.value,t)},s={menu:function(e){return l(l({},e),{zIndex:"200"})}};return i.createElement(d,{className:"canned-responses"},i.createElement(a.C,{label:"Optionally, choose a canned response:",name:"canned-comment-picker"},(function(){return i.createElement(c.Z,{styles:s,placeholder:"Select...",options:o,className:"canned-comments-picker",onChange:u,value:null})})))},d=u.Z.div(r||(r=s(["\n    display: flex;\n    justify-content: flex-end;\n    align-items: center;\n\n    label {\n        color: inherit;\n        font-weight: inherit;\n        font-size: inherit;\n    }\n\n    & > div {\n        margin: 0;\n        display: flex;\n        align-items: center;\n    }\n\n    .canned-comments-picker {\n        margin-left: 0.5em;\n        width: 250px;\n    }\n"],["\n    display: flex;\n    justify-content: flex-end;\n    align-items: center;\n\n    label {\n        color: inherit;\n        font-weight: inherit;\n        font-size: inherit;\n    }\n\n    & > div {\n        margin: 0;\n        display: flex;\n        align-items: center;\n    }\n\n    .canned-comments-picker {\n        margin-left: 0.5em;\n        width: 250px;\n    }\n"]))),p=t(39507),h=t(7011),m=t(44148),v=(t(17775),t(74729)),b=function(e,n,t,r){return new(t||(t=Promise))((function(o,i){function c(e){try{u(r.next(e))}catch(e){i(e)}}function a(e){try{u(r.throw(e))}catch(e){i(e)}}function u(e){var n;e.done?o(e.value):(n=e.value,n instanceof t?n:new t((function(e){e(n)}))).then(c,a)}u((r=r.apply(e,n||[])).next())}))},y=function(e,n){var t,r,o,i,c={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function a(i){return function(a){return function(i){if(t)throw new TypeError("Generator is already executing.");for(;c;)try{if(t=1,r&&(o=2&i[0]?r.return:i[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,i[1])).done)return o;switch(r=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return c.label++,{value:i[1],done:!1};case 5:c.label++,r=i[1],i=[0];continue;case 7:i=c.ops.pop(),c.trys.pop();continue;default:if(!(o=c.trys,(o=o.length>0&&o[o.length-1])||6!==i[0]&&2!==i[0])){c=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){c.label=i[1];break}if(6===i[0]&&c.label<o[1]){c.label=o[1],o=i;break}if(o&&c.label<o[2]){c.label=o[2],c.ops.push(i);break}o[2]&&c.ops.pop(),c.trys.pop();continue}i=n.call(e,c)}catch(e){i=[6,e],r=0}finally{t=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,a])}}},g=AJS.$;function w(e,n,t){return b(this,void 0,void 0,(function(){var r,o,i,c,a,u,s,l,f,d,w,x,S;return y(this,(function(k){switch(k.label){case 0:return""===e?[2]:(g("#comment").off("focus"),(o=n.find("#comment")).val()&&t?[2]:(i=O(n))?(i.id=e,[4,(0,v.wrappedFetch)("".concat(AJS.contextPath(),"/rest/jsdcanned/1.0/jsd/comments/comment?").concat((0,p.F)(i)))]):[2]);case 1:return(c=k.sent()).error?((0,h.k1)("ScriptRunner: Error fetching canned comments: ".concat(c.error)),[2]):(a=c.result,u=a.template,o.hasClass("richeditor-cover")?(l={rendererType:"atlassian-wiki-renderer",unrenderedMarkup:u},[4,(0,v.wrappedFetch)("".concat(AJS.contextPath(),"/rest/api/1.0/render"),{method:"POST",body:JSON.stringify(l)})]):[3,3]);case 2:return(f=k.sent()).error?((0,h.k1)("ScriptRunner: Error rendering text: ".concat(f.error)),[2]):(d=f.result,function(e,n){b(this,void 0,void 0,(function(){var t;return y(this,(function(r){switch(r.label){case 0:return[4,(0,m.pO)(e)];case 1:t=r.sent();try{(0,m.KL)(t)&&t.rteInstance.then((function(e){var r=e.editor;if(n.includes("{CURSOR}")){n=n.replace("{CURSOR}",'<p id="'+t.id+'"></p>'),r.setContent(n,{format:"text"});var o=r.dom.select("p#"+t.id)[0];r.selection.select(o)}else r.selection.setContent(n,{format:"text"});r.undoManager.add(),r.focus()}))}catch(e){console.log("An error occurred setting the editor contents",e),(0,h.k1)("ScriptRunner: An error occurred setting the editor contents.")}return[2]}}))}))}(n.find("#comment"),d),[3,4]);case 3:o.focus(),r=g("#comment").prop("selectionStart"),w=o.val(),x=w.substring(0,r),S=w.substring(r,w.length),s=a.snippet?x+u+S:u,r=s.indexOf("{CURSOR}"),s=s.replace("{CURSOR}",""),o.val(s),r>-1||(r=x.length+u.length+1),o.setSelection(r,r),k.label=4;case 4:return[2]}}))}))}function O(e){return{issueId:function(){try{var e=JIRA.Issue.getIssueId();if(!e)return;return e}catch(e){return}}(),actionId:parseInt(e.find("input[name=action]").val(),10),formId:e.find("form").attr("id")}}function x(e){return b(this,void 0,void 0,(function(){var n,t,r,c,a;return y(this,(function(u){switch(u.label){case 0:return g(".canned-responses").length>0?[2]:(n=O(e))?[4,(0,v.wrappedFetch)("".concat(AJS.contextPath(),"/rest/jsdcanned/1.0/jsd/comments/keys?").concat((0,p.F)(n)))]:[2];case 1:return(t=u.sent()).error?((0,h.k1)("ScriptRunner: Error fetching canned comments: ".concat(t.error)),[2]):(0===(r=t.result).length||((c=r.filter((function(e){return!0===e.default}))).length>0&&(e.is(".jira-dialog-content")&&!e.parent().is("#comment-add-dialog")?e.find(".sd-comment-form-container").one("click",(function(){w(c[0].id,e)})):w(c[0].id,e)),e.find(".sd-comment-message").before('<div class="comment-picker-container"/>'),e.find(".sd-add-comment-container").prepend('<div class="comment-picker-container"/>'),(a=e.find(".comment-picker-container")).length&&o.render(i.createElement(f,{comments:r,fillComment:w,context:e}),a[0])),[2])}}))}))}function S(e){e.find(".sd-comment-field-edit-root").on("DOMNodeInserted",".sd-comment-form-container",(function(n){g(n.target).is(".rte-container")&&x(e)})),(e.find(".sd-add-comment-container,.sd-comment-field-form-container #comment:visible").length>0||e.find(".sd-comment-message").length>0)&&x(e)}JIRA.bind(JIRA.Events.NEW_CONTENT_ADDED,(function(e,n,t){t!==JIRA.CONTENT_ADDED_REASON.pageLoad&&t!==JIRA.CONTENT_ADDED_REASON.panelRefreshed||S(n)})),AJS.$(document).bind("dialogContentReady",(function(e,n){S(n.$popupContent)}))},44148:(e,n,t)=>{"use strict";t.d(n,{KL:()=>s,pN:()=>l,pO:()=>f,th:()=>u});var r=t(84806),o=function(e,n,t,r){return new(t||(t=Promise))((function(o,i){function c(e){try{u(r.next(e))}catch(e){i(e)}}function a(e){try{u(r.throw(e))}catch(e){i(e)}}function u(e){var n;e.done?o(e.value):(n=e.value,n instanceof t?n:new t((function(e){e(n)}))).then(c,a)}u((r=r.apply(e,n||[])).next())}))},i=function(e,n){var t,r,o,i,c={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function a(i){return function(a){return function(i){if(t)throw new TypeError("Generator is already executing.");for(;c;)try{if(t=1,r&&(o=2&i[0]?r.return:i[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,i[1])).done)return o;switch(r=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return c.label++,{value:i[1],done:!1};case 5:c.label++,r=i[1],i=[0];continue;case 7:i=c.ops.pop(),c.trys.pop();continue;default:if(!(o=c.trys,(o=o.length>0&&o[o.length-1])||6!==i[0]&&2!==i[0])){c=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){c.label=i[1];break}if(6===i[0]&&c.label<o[1]){c.label=o[1],o=i;break}if(o&&c.label<o[2]){c.label=o[2],c.ops.push(i);break}o[2]&&c.ops.pop(),c.trys.pop();continue}i=n.call(e,c)}catch(e){i=[6,e],r=0}finally{t=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,a])}}},c=function(e){return o(void 0,void 0,Promise,(function(){return i(this,(function(n){switch(n.label){case 0:return[4,(0,r.X7)(e[0].parentElement,"textarea[data-editor-id]")];case 1:return n.sent(),[2,e.data("editor-id")]}}))}))},a=function(e,n){var r=t(38871),o=r.getEntry(e);if(o)n(o);else{var i="register::".concat(e),c=function(e){r.off(i,c),n(e)};r.on(i,c)}},u=function(e){return!!e.siblings(".rte-container").length};function s(e){return void 0!==e.rteInstance}var l=function(e,n){return o(void 0,void 0,void 0,(function(){var t,r;return i(this,(function(o){switch(o.label){case 0:return t=Array.isArray(n)?n.join(", "):n,e.is(":visible")||e.val(t),[4,c(e)];case 1:return r=o.sent(),a(r,(function(){return e.val(t)})),[2]}}))}))},f=function(e){return o(void 0,void 0,Promise,(function(){var n;return i(this,(function(t){switch(t.label){case 0:return[4,c(e)];case 1:return n=t.sent(),[2,new Promise((function(e){return a(n,e)}))]}}))}))}},17775:(e,n,t)=>{e.exports=t},38871:e=>{"use strict";e.exports=require("jira/editor/registry")}},e=>{e.O(0,["bhResources","fragmentResources","jsdCannedCommentResources"],(()=>{return n=75171,e(e.s=n);var n}));e.O()}]);