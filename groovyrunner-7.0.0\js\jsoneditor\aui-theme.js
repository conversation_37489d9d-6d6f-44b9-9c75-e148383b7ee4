JSONEditor.defaults.themes.aui = JSONEditor.AbstractTheme.extend({
  getRangeInput: function(min, max, step) {
    // TODO: use bootstrap slider
    return this._super(min, max, step);
  },
  getGridColumn: function() {
    var el = document.createElement('div');
    el.className = 'remove-me';
    return el;
  },
  getGridContainer: function() {
    var el = document.createElement('div');
    el.className = 'aui-group';
    return el;
  },
  getGridRow: function() {
    var el = document.createElement('div');
    el.className = 'aui-item';
    return el;
  },
  getFormInputLabel: function(text) {
    var el = this._super(text);
    el.style.display = 'inline-block';
    el.style.fontWeight = 'bold';
    return el;
  },
  getSwitcher: function(options) {
    return this.getSelectInput(options);
  },
  setGridColumnSize: function(el,size) {
    //el.className = 'span'+size;
  },
  getSelectInput: function(options) {
    var input = this._super(options);
    input.className = 'select';
    return input;
  },
  getFormInputField: function(type) {
    var el = this._super(type);
    el.className = type;
    return el;
  },
  afterInputReady: function(input) {
    input.group = this.closest(input,'.field-group');
    const format = input.dataset['schemaformat'];
    if (format === 'groovy') {
      var editor = window.ace.edit(input.parentElement.querySelector('.ace_editor'));
      var fieldRef = input.attributes.getNamedItem('name').value.replace(/]/g, '').replace(/\[/g, '.');
      setupCodeLinting(fieldRef, editor, input);
    }
  },
  getIndentedPanel: function() {
    var el = document.createElement('div');
    el.className = 'aui-group';
    el.style.paddingBottom = 0;
    return el;
  },
  getFormInputDescription: function(text) {
    var el = document.createElement('div');
    el.className = 'description';
    el.textContent = text;
    return el;
  },
  getFormControl: function(label, input, description) {
    var ret = document.createElement('div');
    ret.className = 'field-group';

    var controls = document.createElement('div');
    controls.className = 'controls';

    if(label && input.getAttribute('type') === 'checkbox') {
      ret.appendChild(controls);
      label.className += ' checkbox';
      label.appendChild(input);
      controls.appendChild(label);
      controls.style.height = '30px';
    }
    else {
      if(label) {
        label.className += ' control-label';
        ret.appendChild(label);
      }
      controls.appendChild(input);
      ret.appendChild(controls);
    }

    if(description) controls.appendChild(description);

    return ret;
  },
  getHeaderButtonHolder: function() {
    var el = this.getButtonHolder();
    //el.style.marginLeft = '10px';
    return el;
  },
  getHeader: function(text) {
    var el = document.createElement('div');

    if (el && (text === 'none') || el.textContent === 'none') {
      el.className = 'hidden';
    }
    return el;
  },
  getButtonHolder: function() {
    var el = document.createElement('div');
    var buttons = document.createElement('div');
    el.className = 'buttons-container';
      el.style.float = "left";
    el.style.width = "50%";
      el.style.padding = "10px 0 0 0";
    buttons.className = 'buttons';
    el.appendChild(buttons);
    return el;
  },
  getButton: function(text, icon, title) {
    var el =  this._super(text, icon, title);
    el.className += ' aui-button' + (text === 'Save' ? ' aui-button-primary' : '');
    return el;
  },
  getTable: function() {
    var el = document.createElement('table');
    el.className = 'aui';
    el.style.width = '100%';
    el.style.maxWidth = 'none';
    return el;
  },
  addInputError: function(input,text) {
    if(!input.group) return;
    if(!input.errmsg) {
      input.errmsg = document.createElement('div');
      input.errmsg.className = 'error';
      input.group.appendChild(input.errmsg);
    }
    else {
      input.errmsg.style.display = '';
    }

    input.errmsg.textContent = text;
  },
  removeInputError: function(input) {
    if(!input.errmsg) return;
    input.errmsg.style.display = 'none';
  },
  getTabHolder: function() {
    var el = document.createElement('div');
    el.className = 'tabbable tabs-left';
    el.innerHTML = "<ul class='nav nav-tabs span2' style='margin-right: 0;'></ul><div class='tab-content span10' style='overflow:visible;'></div>";
    return el;
  },
  getTab: function(text) {
    var el = document.createElement('li');
    var a = document.createElement('a');
    a.setAttribute('href','#');
    a.appendChild(text);
    el.appendChild(a);
    return el;
  },
  getTabContentHolder: function(tab_holder) {
    return tab_holder.children[1];
  },
  getTabContent: function() {
    var el = document.createElement('div');
    el.className = 'tab-pane active';
    return el;
  },
  markTabActive: function(tab) {
    tab.className += ' active';
  },
  markTabInactive: function(tab) {
    tab.className = tab.className.replace(/\s?active/g,'');
  },
  addTab: function(holder, tab) {
    holder.children[0].appendChild(tab);
  },
  getProgressBar: function() {
    var container = document.createElement('div');
    container.className = 'progress';

    var bar = document.createElement('div');
    bar.className = 'bar';
    bar.style.width = '0%';
    container.appendChild(bar);

    return container;
  },
  updateProgressBar: function(progressBar, progress) {
    if (!progressBar) return;

    progressBar.firstChild.style.width = progress + "%";
  },
  updateProgressBarUnknown: function(progressBar) {
    if (!progressBar) return;

    progressBar.className = 'progress progress-striped active';
    progressBar.firstChild.style.width = '100%';
  }
});

JSONEditor.defaults.iconlibs.aui = JSONEditor.AbstractIconLib.extend({
  mapping: {
    collapse: 'expanded',
    expand: 'collapsed',
    "delete": 'delete',
    edit: 'edit',
    add: 'add',
    cancel: 'remove',
    save: 'approve',
    moveup: 'arrow-up',
    movedown: 'arrow-down'
  },
  icon_prefix: 'aui-icon aui-icon-small aui-iconfont-'
});
