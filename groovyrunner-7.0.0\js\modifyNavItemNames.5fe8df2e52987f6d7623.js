"use strict";(self.webpackJsonpScriptRunner=self.webpackJsonpScriptRunner||[]).push([["modifyNavItemNames"],{66638:(e,n,r)=>{r.d(n,{S:()=>o});var t,o=(t="jira",function(e){return void 0!==e[t]?e[t]:e.default})},84806:(e,n,r)=>{r.d(n,{X7:()=>c,bK:()=>o});var t=function(){return t=Object.assign||function(e){for(var n,r=1,t=arguments.length;r<t;r++)for(var o in n=arguments[r])Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o]);return e},t.apply(this,arguments)},o=function(e,n){return void 0===n&&(n={}),new Promise((function(r){return s(document.body,e,r,t({subtree:!1},n))}))},c=function(e,n,r){return void 0===r&&(r={}),new Promise((function(t){return s(e,n,t,r)}))},s=function(e,n,r,o){void 0===o&&(o={});var c=e.querySelector(n);c?r(c):new MutationObserver((function(t,o){var c=e.querySelector(n);c&&(o.disconnect(),r(c))})).observe(e,t({childList:!0,subtree:!0,attributes:!1,characterData:!1},o))}},87999:(e,n,r)=>{var t=r(84806),o=r(66638),c=function(){return c=Object.assign||function(e){for(var n,r=1,t=arguments.length;r<t;r++)for(var o in n=arguments[r])Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o]);return e},c.apply(this,arguments)},s=function(e){var n="function"==typeof Symbol&&Symbol.iterator,r=n&&e[n],t=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&t>=e.length&&(e=void 0),{value:e&&e[t++],done:!e}}};throw new TypeError(n?"Object is not iterable.":"Symbol.iterator is not defined.")},i=function(e,n){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var t,o,c=r.call(e),s=[];try{for(;(void 0===n||n-- >0)&&!(t=c.next()).done;)s.push(t.value)}catch(e){o={error:e}}finally{try{t&&!t.done&&(r=c.return)&&r.call(c)}finally{if(o)throw o.error}}return s},a={console:"Console",script_listeners:"Listeners",scriptfields:"Fields",jqlfunctions:"JQL Functions",scriptworkflows:"Workflows",mailhandler:"Mail Handler"},u={prehooks:"Pre Hooks",posthooks:"Post Hooks",mergechecks:"Merge Checks",stash_events:"Listeners",bitbucket_scheduled_jobs:"Jobs"},l={script_console_conf:"Console",confluence_macros_conf:"Macros",confluence_cql_functions_conf:"CQL Functions",fragments_conf:"Fragments",scheduled_jobs_conf:"Jobs",scriptrunnerhome_link:"Home"},f={fragments:"Fragments",script_console:"Console",scriptrunnerbrowse_link:"Browse",scriptrunnersettings_link:"Settings",scheduled_jobs_jira:"Jobs"},d=function(){var e,n,r=c(c({},f),(0,o.S)({jira:a,confluence:l,bitbucket:u,default:{}})),d=function(e,n){(0,t.bK)("#".concat(e)).then((function(r){try{r.innerHTML=n}catch(n){console.warn("modifyNavItemNames - '".concat(e,"' something went wrong:"),n)}}))};try{for(var b=s(Object.entries(r)),p=b.next();!p.done;p=b.next()){var v=i(p.value,2);d(v[0],v[1])}}catch(n){e={error:n}}finally{try{p&&!p.done&&(n=b.return)&&n.call(b)}finally{if(e)throw e.error}}};document.body?d():document.addEventListener("DOMContentLoaded",d,!1)}},e=>{var n;n=87999,e(e.s=n)}]);