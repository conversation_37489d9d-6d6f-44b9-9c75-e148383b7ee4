import {
    removeBehaviourContextIdElement,
    getBehaviourContextIdElement,
    setBehaviourContextId,
    removeContextIssueIdElement,
    getContextIssueIdElement,
    setContextIssueId,
    extractBehaviourContextId,
} from "./fragmentHelpers/create-constrained-issue-helper";

require('./jquery.livequery');

(function ($) {

    function addTriggers() {
        $(".sr-create-bound-issue").off("click").on("click", function (e) {

            function getURLParameter(url, name) {
                return decodeURIComponent((new RegExp('[?|&]' + name + '=' + '([^&;]+?)(&|#|;|$)').exec(url) || [, ""])[1].replace(/\+/g, '%20')) || null;
            }

            let $link = $(this);
            e.preventDefault();

            let form = JIRA.Forms.createCreateIssueForm({
                issueType: getURLParameter($link.attr("href"), "issuetype"),
                pid      : getURLParameter($link.attr("href"), "pid")
            });

            // todo: title and windowTitle, although you can't do much useful with them
            let dialog = form.asDialog({
                id: "create-constrained-issue-dialog"
            });

            let onFormEvent = ($link) => {
                if (!getBehaviourContextIdElement()) {
                    setBehaviourContextId(extractBehaviourContextId($link.attr('id')))
                }
                if (!getContextIssueIdElement()) {
                    setContextIssueId(JIRA.Issue.getIssueId())
                }
            };

            form.bind("contentRefreshed", onFormEvent($link));

            form.bind("sessionComplete", function () {
                location.reload();
            });

            // handler unregister when the dialog closes
            dialog.bind('Dialog.hide', function () {
                removeBehaviourContextIdElement();
                removeContextIssueIdElement();
                AJS.$(document).unbind("QuickCreateIssue.validationError");
            });

            dialog.show();
        });
    }

    JIRA.bind(JIRA.Events.NEW_CONTENT_ADDED, function (event, $context, reason) {
        // hack for JA, which emits no events - https://jira.atlassian.com/browse/JSW-13527
        // When only supporting IE11+ switch to MutationObserver, or expire and re-add the livequery
        if (window.location.href.indexOf("RapidBoard.jspa?") > -1) {
            $("#ghx-detail-issue").livequery(function () {
                addTriggers();
            });
        }

        if (reason === JIRA.CONTENT_ADDED_REASON.pageLoad || reason === JIRA.CONTENT_ADDED_REASON.panelRefreshed) {
            addTriggers();
        }
    });
})(AJS.$);
