"use strict";(self.webpackJsonpScriptRunner=self.webpackJsonpScriptRunner||[]).push([["react-syntax-highlighter_languages_refractor_ejs","react-syntax-highlighter_languages_refractor_markupTemplating"],{37565:(e,a,n)=>{var t=n(62030);function r(e){e.register(t),function(e){e.languages.ejs={delimiter:{pattern:/^<%[-_=]?|[-_]?%>$/,alias:"punctuation"},comment:/^#[\s\S]*/,"language-javascript":{pattern:/[\s\S]+/,inside:e.languages.javascript}},e.hooks.add("before-tokenize",(function(a){e.languages["markup-templating"].buildPlaceholders(a,"ejs",/<%(?!%)[\s\S]+?%>/g)})),e.hooks.add("after-tokenize",(function(a){e.languages["markup-templating"].tokenizePlaceholders(a,"ejs")})),e.languages.eta=e.languages.ejs}(e)}e.exports=r,r.displayName="ejs",r.aliases=["eta"]},62030:e=>{function a(e){!function(e){function a(e,a){return"___"+e.toUpperCase()+a+"___"}Object.defineProperties(e.languages["markup-templating"]={},{buildPlaceholders:{value:function(n,t,r,s){if(n.language===t){var i=n.tokenStack=[];n.code=n.code.replace(r,(function(e){if("function"==typeof s&&!s(e))return e;for(var r,o=i.length;-1!==n.code.indexOf(r=a(t,o));)++o;return i[o]=e,r})),n.grammar=e.languages.markup}}},tokenizePlaceholders:{value:function(n,t){if(n.language===t&&n.tokenStack){n.grammar=e.languages[t];var r=0,s=Object.keys(n.tokenStack);!function i(o){for(var g=0;g<o.length&&!(r>=s.length);g++){var l=o[g];if("string"==typeof l||l.content&&"string"==typeof l.content){var u=s[r],c=n.tokenStack[u],p="string"==typeof l?l:l.content,f=a(t,u),k=p.indexOf(f);if(k>-1){++r;var m=p.substring(0,k),d=new e.Token(t,e.tokenize(c,n.grammar),"language-"+t,c),h=p.substring(k+f.length),_=[];m&&_.push.apply(_,i([m])),_.push(d),h&&_.push.apply(_,i([h])),"string"==typeof l?o.splice.apply(o,[g,1].concat(_)):l.content=_}}else l.content&&i(l.content)}return o}(n.tokens)}}}})}(e)}e.exports=a,a.displayName="markupTemplating",a.aliases=[]}}]);