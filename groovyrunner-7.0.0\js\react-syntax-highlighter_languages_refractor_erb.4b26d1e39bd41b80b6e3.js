"use strict";(self.webpackJsonpScriptRunner=self.webpackJsonpScriptRunner||[]).push([["react-syntax-highlighter_languages_refractor_erb","react-syntax-highlighter_languages_refractor_markupTemplating","react-syntax-highlighter_languages_refractor_ruby"],{87071:(e,n,r)=>{var t=r(80215),a=r(62030);function s(e){e.register(t),e.register(a),function(e){e.languages.erb=e.languages.extend("ruby",{}),e.languages.insertBefore("erb","comment",{delimiter:{pattern:/^<%=?|%>$/,alias:"punctuation"}}),e.hooks.add("before-tokenize",(function(n){e.languages["markup-templating"].buildPlaceholders(n,"erb",/<%=?(?:[^\r\n]|[\r\n](?!=begin)|[\r\n]=begin\s[\s\S]*?^=end)+?%>/gm)})),e.hooks.add("after-tokenize",(function(n){e.languages["markup-templating"].tokenizePlaceholders(n,"erb")}))}(e)}e.exports=s,s.displayName="erb",s.aliases=[]},62030:e=>{function n(e){!function(e){function n(e,n){return"___"+e.toUpperCase()+n+"___"}Object.defineProperties(e.languages["markup-templating"]={},{buildPlaceholders:{value:function(r,t,a,s){if(r.language===t){var i=r.tokenStack=[];r.code=r.code.replace(a,(function(e){if("function"==typeof s&&!s(e))return e;for(var a,o=i.length;-1!==r.code.indexOf(a=n(t,o));)++o;return i[o]=e,a})),r.grammar=e.languages.markup}}},tokenizePlaceholders:{value:function(r,t){if(r.language===t&&r.tokenStack){r.grammar=e.languages[t];var a=0,s=Object.keys(r.tokenStack);!function i(o){for(var u=0;u<o.length&&!(a>=s.length);u++){var l=o[u];if("string"==typeof l||l.content&&"string"==typeof l.content){var g=s[a],c=r.tokenStack[g],p="string"==typeof l?l:l.content,d=n(t,g),b=p.indexOf(d);if(b>-1){++a;var f=p.substring(0,b),m=new e.Token(t,e.tokenize(c,r.grammar),"language-"+t,c),h=p.substring(b+d.length),y=[];f&&y.push.apply(y,i([f])),y.push(m),h&&y.push.apply(y,i([h])),"string"==typeof l?o.splice.apply(o,[u,1].concat(y)):l.content=y}}else l.content&&i(l.content)}return o}(r.tokens)}}}})}(e)}e.exports=n,n.displayName="markupTemplating",n.aliases=[]},80215:e=>{function n(e){!function(e){e.languages.ruby=e.languages.extend("clike",{comment:[/#.*/,{pattern:/^=begin\s[\s\S]*?^=end/m,greedy:!0}],"class-name":{pattern:/(\b(?:class)\s+|\bcatch\s+\()[\w.\\]+/i,lookbehind:!0,inside:{punctuation:/[.\\]/}},keyword:/\b(?:alias|and|BEGIN|begin|break|case|class|def|define_method|defined|do|each|else|elsif|END|end|ensure|extend|for|if|in|include|module|new|next|nil|not|or|prepend|protected|private|public|raise|redo|require|rescue|retry|return|self|super|then|throw|undef|unless|until|when|while|yield)\b/});var n={pattern:/#\{[^}]+\}/,inside:{delimiter:{pattern:/^#\{|\}$/,alias:"tag"},rest:e.languages.ruby}};delete e.languages.ruby.function,e.languages.insertBefore("ruby","keyword",{regex:[{pattern:RegExp(/%r/.source+"(?:"+[/([^a-zA-Z0-9\s{(\[<])(?:(?!\1)[^\\]|\\[\s\S])*\1[gim]{0,3}/.source,/\((?:[^()\\]|\\[\s\S])*\)[gim]{0,3}/.source,/\{(?:[^#{}\\]|#(?:\{[^}]+\})?|\\[\s\S])*\}[gim]{0,3}/.source,/\[(?:[^\[\]\\]|\\[\s\S])*\][gim]{0,3}/.source,/<(?:[^<>\\]|\\[\s\S])*>[gim]{0,3}/.source].join("|")+")"),greedy:!0,inside:{interpolation:n}},{pattern:/(^|[^/])\/(?!\/)(?:\[[^\r\n\]]+\]|\\.|[^[/\\\r\n])+\/[gim]{0,3}(?=\s*(?:$|[\r\n,.;})]))/,lookbehind:!0,greedy:!0}],variable:/[@$]+[a-zA-Z_]\w*(?:[?!]|\b)/,symbol:{pattern:/(^|[^:]):[a-zA-Z_]\w*(?:[?!]|\b)/,lookbehind:!0},"method-definition":{pattern:/(\bdef\s+)[\w.]+/,lookbehind:!0,inside:{function:/\w+$/,rest:e.languages.ruby}}}),e.languages.insertBefore("ruby","number",{builtin:/\b(?:Array|Bignum|Binding|Class|Continuation|Dir|Exception|FalseClass|File|Stat|Fixnum|Float|Hash|Integer|IO|MatchData|Method|Module|NilClass|Numeric|Object|Proc|Range|Regexp|String|Struct|TMS|Symbol|ThreadGroup|Thread|Time|TrueClass)\b/,constant:/\b[A-Z]\w*(?:[?!]|\b)/}),e.languages.ruby.string=[{pattern:RegExp(/%[qQiIwWxs]?/.source+"(?:"+[/([^a-zA-Z0-9\s{(\[<])(?:(?!\1)[^\\]|\\[\s\S])*\1/.source,/\((?:[^()\\]|\\[\s\S])*\)/.source,/\{(?:[^#{}\\]|#(?:\{[^}]+\})?|\\[\s\S])*\}/.source,/\[(?:[^\[\]\\]|\\[\s\S])*\]/.source,/<(?:[^<>\\]|\\[\s\S])*>/.source].join("|")+")"),greedy:!0,inside:{interpolation:n}},{pattern:/("|')(?:#\{[^}]+\}|#(?!\{)|\\(?:\r\n|[\s\S])|(?!\1)[^\\#\r\n])*\1/,greedy:!0,inside:{interpolation:n}}],e.languages.rb=e.languages.ruby}(e)}e.exports=n,n.displayName="ruby",n.aliases=["rb"]}}]);