"use strict";(self.webpackJsonpScriptRunner=self.webpackJsonpScriptRunner||[]).push([["react-syntax-highlighter_languages_refractor_gcode"],{90618:e=>{function t(e){e.languages.gcode={comment:/;.*|\B\(.*?\)\B/,string:{pattern:/"(?:""|[^"])*"/,greedy:!0},keyword:/\b[GM]\d+(?:\.\d+)?\b/,property:/\b[A-Z]/,checksum:{pattern:/\*\d+/,alias:"punctuation"},punctuation:/:/}}e.exports=t,t.displayName="gcode",t.aliases=[]}}]);