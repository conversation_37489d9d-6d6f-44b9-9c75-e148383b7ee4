/*! For license information please see react-syntax-highlighter~~refractor-core-import.a869c74fde24ae4f4724.js.LICENSE.txt */
"use strict";(self.webpackJsonpScriptRunner=self.webpackJsonpScriptRunner||[]).push([["react-syntax-highlighter~~refractor-core-import","react-syntax-highlighter_languages_refractor_clike","react-syntax-highlighter_languages_refractor_css","react-syntax-highlighter_languages_refractor_javascript","react-syntax-highlighter_languages_refractor_markup"],{11744:(e,n,t)=>{n.Q=function(e){var n,t=[],r=String(e||""),l=r.indexOf(","),i=0,s=!1;for(;!s;)-1===l&&(l=r.length,s=!0),!(n=a(r.slice(i,l)))&&s||t.push(n),i=l+1,l=r.indexOf(",",i);return t};var a=t(67240)},21968:e=>{e.exports=function(e,t){var a,r,l,i=e||"",s=t||"div",o={},u=0;for(;u<i.length;)n.lastIndex=u,l=n.exec(i),(a=i.slice(u,l?l.index:i.length))&&(r?"#"===r?o.id=a:o.className?o.className.push(a):o.className=[a]:s=a,u+=a.length),l&&(r=l[0],u++);return{type:"element",tagName:s,properties:o,children:[]}};var n=/[#.]/g},18895:(e,n,t)=>{var a=t(6045),r=t(19015),l=t(21968),i=t(87120).Q,s=t(11744).Q;e.exports=function(e,n,t){var r=t?function(e){var n,t=e.length,a=-1,r={};for(;++a<t;)r[(n=e[a]).toLowerCase()]=n;return r}(t):null;return function(e,t){var a,i=l(e,n),s=Array.prototype.slice.call(arguments,2),p=i.tagName.toLowerCase();i.tagName=r&&o.call(r,p)?r[p]:p,t&&u(t,i)&&(s.unshift(t),t=null);if(t)for(a in t)g(i.properties,a,t[a]);c(i.children,s),"template"===i.tagName&&(i.content={type:"root",children:i.children},i.children=[]);return i};function g(n,t,r){var l,o,u;null!=r&&r==r&&(o=(l=a(e,t)).property,"string"==typeof(u=r)&&(l.spaceSeparated?u=i(u):l.commaSeparated?u=s(u):l.commaOrSpaceSeparated&&(u=i(s(u).join(" ")))),"style"===o&&"string"!=typeof r&&(u=function(e){var n,t=[];for(n in e)t.push([n,e[n]].join(": "));return t.join("; ")}(u)),"className"===o&&n.className&&(u=n.className.concat(u)),n[o]=function(e,n,t){var a,r,l;if("object"!=typeof t||!("length"in t))return p(e,n,t);r=t.length,a=-1,l=[];for(;++a<r;)l[a]=p(e,n,t[a]);return l}(l,o,u))}};var o={}.hasOwnProperty;function u(e,n){return"string"==typeof e||"length"in e||function(e,n){var t=n.type;if("input"===e||!t||"string"!=typeof t)return!1;if("object"==typeof n.children&&"length"in n.children)return!0;if(t=t.toLowerCase(),"button"===e)return"menu"!==t&&"submit"!==t&&"reset"!==t&&"button"!==t;return"value"in n}(n.tagName,e)}function c(e,n){var t,a;if("string"!=typeof n&&"number"!=typeof n)if("object"==typeof n&&"length"in n)for(t=-1,a=n.length;++t<a;)c(e,n[t]);else{if("object"!=typeof n||!("type"in n))throw new Error("Expected node, nodes, or string, got `"+n+"`");e.push(n)}else e.push({type:"text",value:String(n)})}function p(e,n,t){var a=t;return e.number||e.positiveNumber?isNaN(a)||""===a||(a=Number(a)):(e.boolean||e.overloadedBoolean)&&("string"!=typeof a||""!==a&&r(t)!==r(n)||(a=!0)),a}},65342:(e,n,t)=>{var a=t(2531),r=t(18895)(a,"div");r.displayName="html",e.exports=r},46651:(e,n,t)=>{e.exports=t(65342)},89764:e=>{e.exports=function(e){var n="string"==typeof e?e.charCodeAt(0):e;return n>=97&&n<=122||n>=65&&n<=90}},73872:(e,n,t)=>{var a=t(89764),r=t(26731);e.exports=function(e){return a(e)||r(e)}},26731:e=>{e.exports=function(e){var n="string"==typeof e?e.charCodeAt(0):e;return n>=48&&n<=57}},35586:e=>{e.exports=function(e){var n="string"==typeof e?e.charCodeAt(0):e;return n>=97&&n<=102||n>=65&&n<=70||n>=48&&n<=57}},14206:e=>{var n;e.exports=function(e){var t,a="&"+e+";";if((n=n||document.createElement("i")).innerHTML=a,59===(t=n.textContent).charCodeAt(t.length-1)&&"semi"!==e)return!1;return t!==a&&t}},61950:(e,n,t)=>{var a=t(37452),r=t(93580),l=t(26731),i=t(35586),s=t(73872),o=t(14206);e.exports=function(e,n){var t,l,i={};n||(n={});for(l in g)t=n[l],i[l]=null==t?g[l]:t;(i.position.indent||i.position.start)&&(i.indent=i.position.indent||[],i.position=i.position.start);return function(e,n){var t,l,i,g,w,k,A,F,S,C,N,O,E,$,L,j,_,P,T,M=n.additional,z=n.nonTerminated,I=n.text,B=n.reference,R=n.warning,D=n.textContext,q=n.referenceContext,U=n.warningContext,H=n.position,W=n.indent||[],Z=e.length,J=0,V=-1,G=H.column||1,Q=H.line||1,Y="",K=[];"string"==typeof M&&(M=M.charCodeAt(0));j=X(),F=R?ee:p,J--,Z++;for(;++J<Z;)if(10===w&&(G=W[V]||1),38===(w=e.charCodeAt(J))){if(9===(A=e.charCodeAt(J+1))||10===A||12===A||32===A||38===A||60===A||A!=A||M&&A===M){Y+=c(w),G++;continue}for(O=E=J+1,T=E,35===A?(T=++O,88===(A=e.charCodeAt(T))||120===A?($=f,T=++O):$=m):$=d,t="",N="",g="",L=v[$],T--;++T<Z&&L(A=e.charCodeAt(T));)g+=c(A),$===d&&u.call(a,g)&&(t=g,N=a[g]);(i=59===e.charCodeAt(T))&&(T++,(l=$===d&&o(g))&&(t=g,N=l)),P=1+T-E,(i||z)&&(g?$===d?(i&&!N?F(5,1):(t!==g&&(P=1+(T=O+t.length)-O,i=!1),i||(S=t?1:3,n.attribute?61===(A=e.charCodeAt(T))?(F(S,P),N=null):s(A)?N=null:F(S,P):F(S,P))),k=N):(i||F(2,P),b(k=parseInt(g,h[$]))?(F(7,P),k=c(65533)):k in r?(F(6,P),k=r[k]):(C="",x(k)&&F(6,P),k>65535&&(C+=c((k-=65536)>>>10|55296),k=56320|1023&k),k=C+c(k))):$!==d&&F(4,P)),k?(ne(),j=X(),J=T-1,G+=T-E+1,K.push(k),(_=X()).offset++,B&&B.call(q,k,{start:j,end:_},e.slice(E-1,T)),j=_):(g=e.slice(E-1,T),Y+=g,G+=g.length,J=T-1)}else 10===w&&(Q++,V++,G=0),w==w?(Y+=c(w),G++):ne();return K.join("");function X(){return{line:Q,column:G,offset:J+(H.offset||0)}}function ee(e,n){var t=X();t.column+=n,t.offset+=n,R.call(U,y[e],t,e)}function ne(){Y&&(K.push(Y),I&&I.call(D,Y,{start:j,end:X()}),Y="")}}(e,i)};var u={}.hasOwnProperty,c=String.fromCharCode,p=Function.prototype,g={warning:null,reference:null,text:null,warningContext:null,referenceContext:null,textContext:null,position:{},additional:null,attribute:!1,nonTerminated:!0},d="named",f="hexadecimal",m="decimal",h={hexadecimal:16,decimal:10},v={};v.named=s,v[m]=l,v[f]=i;var y={};function b(e){return e>=55296&&e<=57343||e>1114111}function x(e){return e>=1&&e<=8||11===e||e>=13&&e<=31||e>=127&&e<=159||e>=64976&&e<=65007||65535==(65535&e)||65534==(65535&e)}y[1]="Named character references must be terminated by a semicolon",y[2]="Numeric character references must be terminated by a semicolon",y[3]="Named character references cannot be empty",y[4]="Numeric character references cannot be empty",y[5]="Named character references must be known",y[6]="Numeric character references cannot be disallowed",y[7]="Numeric character references cannot be outside the permissible Unicode range"},7088:(e,n,t)=>{var a=function(e){var n=/\blang(?:uage)?-([\w-]+)\b/i,t=0,a={manual:e.Prism&&e.Prism.manual,disableWorkerMessageHandler:e.Prism&&e.Prism.disableWorkerMessageHandler,util:{encode:function e(n){return n instanceof r?new r(n.type,e(n.content),n.alias):Array.isArray(n)?n.map(e):n.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/\u00a0/g," ")},type:function(e){return Object.prototype.toString.call(e).slice(8,-1)},objId:function(e){return e.__id||Object.defineProperty(e,"__id",{value:++t}),e.__id},clone:function e(n,t){var r,l;switch(t=t||{},a.util.type(n)){case"Object":if(l=a.util.objId(n),t[l])return t[l];for(var i in r={},t[l]=r,n)n.hasOwnProperty(i)&&(r[i]=e(n[i],t));return r;case"Array":return l=a.util.objId(n),t[l]?t[l]:(r=[],t[l]=r,n.forEach((function(n,a){r[a]=e(n,t)})),r);default:return n}},getLanguage:function(e){for(;e&&!n.test(e.className);)e=e.parentElement;return e?(e.className.match(n)||[,"none"])[1].toLowerCase():"none"},currentScript:function(){if("undefined"==typeof document)return null;if("currentScript"in document)return document.currentScript;try{throw new Error}catch(a){var e=(/at [^(\r\n]*\((.*):.+:.+\)$/i.exec(a.stack)||[])[1];if(e){var n=document.getElementsByTagName("script");for(var t in n)if(n[t].src==e)return n[t]}return null}},isActive:function(e,n,t){for(var a="no-"+n;e;){var r=e.classList;if(r.contains(n))return!0;if(r.contains(a))return!1;e=e.parentElement}return!!t}},languages:{extend:function(e,n){var t=a.util.clone(a.languages[e]);for(var r in n)t[r]=n[r];return t},insertBefore:function(e,n,t,r){var l=(r=r||a.languages)[e],i={};for(var s in l)if(l.hasOwnProperty(s)){if(s==n)for(var o in t)t.hasOwnProperty(o)&&(i[o]=t[o]);t.hasOwnProperty(s)||(i[s]=l[s])}var u=r[e];return r[e]=i,a.languages.DFS(a.languages,(function(n,t){t===u&&n!=e&&(this[n]=i)})),i},DFS:function e(n,t,r,l){l=l||{};var i=a.util.objId;for(var s in n)if(n.hasOwnProperty(s)){t.call(n,s,n[s],r||s);var o=n[s],u=a.util.type(o);"Object"!==u||l[i(o)]?"Array"!==u||l[i(o)]||(l[i(o)]=!0,e(o,t,s,l)):(l[i(o)]=!0,e(o,t,null,l))}}},plugins:{},highlightAll:function(e,n){a.highlightAllUnder(document,e,n)},highlightAllUnder:function(e,n,t){var r={callback:t,container:e,selector:'code[class*="language-"], [class*="language-"] code, code[class*="lang-"], [class*="lang-"] code'};a.hooks.run("before-highlightall",r),r.elements=Array.prototype.slice.apply(r.container.querySelectorAll(r.selector)),a.hooks.run("before-all-elements-highlight",r);for(var l,i=0;l=r.elements[i++];)a.highlightElement(l,!0===n,r.callback)},highlightElement:function(t,r,l){var i=a.util.getLanguage(t),s=a.languages[i];t.className=t.className.replace(n,"").replace(/\s+/g," ")+" language-"+i;var o=t.parentElement;o&&"pre"===o.nodeName.toLowerCase()&&(o.className=o.className.replace(n,"").replace(/\s+/g," ")+" language-"+i);var u={element:t,language:i,grammar:s,code:t.textContent};function c(e){u.highlightedCode=e,a.hooks.run("before-insert",u),u.element.innerHTML=u.highlightedCode,a.hooks.run("after-highlight",u),a.hooks.run("complete",u),l&&l.call(u.element)}if(a.hooks.run("before-sanity-check",u),!u.code)return a.hooks.run("complete",u),void(l&&l.call(u.element));if(a.hooks.run("before-highlight",u),u.grammar)if(r&&e.Worker){var p=new Worker(a.filename);p.onmessage=function(e){c(e.data)},p.postMessage(JSON.stringify({language:u.language,code:u.code,immediateClose:!0}))}else c(a.highlight(u.code,u.grammar,u.language));else c(a.util.encode(u.code))},highlight:function(e,n,t){var l={code:e,grammar:n,language:t};return a.hooks.run("before-tokenize",l),l.tokens=a.tokenize(l.code,l.grammar),a.hooks.run("after-tokenize",l),r.stringify(a.util.encode(l.tokens),l.language)},tokenize:function(e,n){var t=n.rest;if(t){for(var a in t)n[a]=t[a];delete n.rest}var r=new s;return o(r,r.head,e),i(e,r,n,r.head,0),function(e){var n=[],t=e.head.next;for(;t!==e.tail;)n.push(t.value),t=t.next;return n}(r)},hooks:{all:{},add:function(e,n){var t=a.hooks.all;t[e]=t[e]||[],t[e].push(n)},run:function(e,n){var t=a.hooks.all[e];if(t&&t.length)for(var r,l=0;r=t[l++];)r(n)}},Token:r};function r(e,n,t,a){this.type=e,this.content=n,this.alias=t,this.length=0|(a||"").length}function l(e,n,t,a){e.lastIndex=n;var r=e.exec(t);if(r&&a&&r[1]){var l=r[1].length;r.index+=l,r[0]=r[0].slice(l)}return r}function i(e,n,t,s,c,p){for(var g in t)if(t.hasOwnProperty(g)&&t[g]){var d=t[g];d=Array.isArray(d)?d:[d];for(var f=0;f<d.length;++f){if(p&&p.cause==g+","+f)return;var m=d[f],h=m.inside,v=!!m.lookbehind,y=!!m.greedy,b=m.alias;if(y&&!m.pattern.global){var x=m.pattern.toString().match(/[imsuy]*$/)[0];m.pattern=RegExp(m.pattern.source,x+"g")}for(var w=m.pattern||m,k=s.next,A=c;k!==n.tail&&!(p&&A>=p.reach);A+=k.value.length,k=k.next){var F=k.value;if(n.length>e.length)return;if(!(F instanceof r)){var S,C=1;if(y){if(!(S=l(w,A,e,v)))break;var N=S.index,O=S.index+S[0].length,E=A;for(E+=k.value.length;N>=E;)E+=(k=k.next).value.length;if(A=E-=k.value.length,k.value instanceof r)continue;for(var $=k;$!==n.tail&&(E<O||"string"==typeof $.value);$=$.next)C++,E+=$.value.length;C--,F=e.slice(A,E),S.index-=A}else if(!(S=l(w,0,F,v)))continue;N=S.index;var L=S[0],j=F.slice(0,N),_=F.slice(N+L.length),P=A+F.length;p&&P>p.reach&&(p.reach=P);var T=k.prev;j&&(T=o(n,T,j),A+=j.length),u(n,T,C),k=o(n,T,new r(g,h?a.tokenize(L,h):L,b,L)),_&&o(n,k,_),C>1&&i(e,n,t,k.prev,A,{cause:g+","+f,reach:P})}}}}}function s(){var e={value:null,prev:null,next:null},n={value:null,prev:e,next:null};e.next=n,this.head=e,this.tail=n,this.length=0}function o(e,n,t){var a=n.next,r={value:t,prev:n,next:a};return n.next=r,a.prev=r,e.length++,r}function u(e,n,t){for(var a=n.next,r=0;r<t&&a!==e.tail;r++)a=a.next;n.next=a,a.prev=n,e.length-=r}if(e.Prism=a,r.stringify=function e(n,t){if("string"==typeof n)return n;if(Array.isArray(n)){var r="";return n.forEach((function(n){r+=e(n,t)})),r}var l={type:n.type,content:e(n.content,t),tag:"span",classes:["token",n.type],attributes:{},language:t},i=n.alias;i&&(Array.isArray(i)?Array.prototype.push.apply(l.classes,i):l.classes.push(i)),a.hooks.run("wrap",l);var s="";for(var o in l.attributes)s+=" "+o+'="'+(l.attributes[o]||"").replace(/"/g,"&quot;")+'"';return"<"+l.tag+' class="'+l.classes.join(" ")+'"'+s+">"+l.content+"</"+l.tag+">"},!e.document)return e.addEventListener?(a.disableWorkerMessageHandler||e.addEventListener("message",(function(n){var t=JSON.parse(n.data),r=t.language,l=t.code,i=t.immediateClose;e.postMessage(a.highlight(l,a.languages[r],r)),i&&e.close()}),!1),a):a;var c=a.util.currentScript();function p(){a.manual||a.highlightAll()}if(c&&(a.filename=c.src,c.hasAttribute("data-manual")&&(a.manual=!0)),!a.manual){var g=document.readyState;"loading"===g||"interactive"===g&&c&&c.defer?document.addEventListener("DOMContentLoaded",p):window.requestAnimationFrame?window.requestAnimationFrame(p):window.setTimeout(p,16)}return a}("undefined"!=typeof window?window:"undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope?self:{});e.exports&&(e.exports=a),void 0!==t.g&&(t.g.Prism=a)},6045:(e,n,t)=>{var a=t(19015),r=t(27725),l=t(20848),i="data";e.exports=function(e,n){var t=a(n),g=n,d=l;if(t in e.normal)return e.property[e.normal[t]];t.length>4&&t.slice(0,4)===i&&s.test(n)&&("-"===n.charAt(4)?g=function(e){var n=e.slice(5).replace(o,p);return i+n.charAt(0).toUpperCase()+n.slice(1)}(n):n=function(e){var n=e.slice(4);if(o.test(n))return e;"-"!==(n=n.replace(u,c)).charAt(0)&&(n="-"+n);return i+n}(n),d=r);return new d(g,n)};var s=/^data[-a-z0-9.:_]+$/i,o=/-[a-z]/g,u=/[A-Z]/g;function c(e){return"-"+e.toLowerCase()}function p(e){return e.charAt(1).toUpperCase()}},2531:(e,n,t)=>{var a=t(10408),r=t(31768),l=t(82029),i=t(60511),s=t(27394),o=t(86666);e.exports=a([l,r,i,s,o])},27394:(e,n,t)=>{var a=t(60072),r=t(48727),l=a.booleanish,i=a.number,s=a.spaceSeparated;e.exports=r({transform:function(e,n){return"role"===n?n:"aria-"+n.slice(4).toLowerCase()},properties:{ariaActiveDescendant:null,ariaAtomic:l,ariaAutoComplete:null,ariaBusy:l,ariaChecked:l,ariaColCount:i,ariaColIndex:i,ariaColSpan:i,ariaControls:s,ariaCurrent:null,ariaDescribedBy:s,ariaDetails:null,ariaDisabled:l,ariaDropEffect:s,ariaErrorMessage:null,ariaExpanded:l,ariaFlowTo:s,ariaGrabbed:l,ariaHasPopup:null,ariaHidden:l,ariaInvalid:null,ariaKeyShortcuts:null,ariaLabel:null,ariaLabelledBy:s,ariaLevel:i,ariaLive:null,ariaModal:l,ariaMultiLine:l,ariaMultiSelectable:l,ariaOrientation:null,ariaOwns:s,ariaPlaceholder:null,ariaPosInSet:i,ariaPressed:l,ariaReadOnly:l,ariaRelevant:null,ariaRequired:l,ariaRoleDescription:s,ariaRowCount:i,ariaRowIndex:i,ariaRowSpan:i,ariaSelected:l,ariaSetSize:i,ariaSort:null,ariaValueMax:i,ariaValueMin:i,ariaValueNow:i,ariaValueText:null,role:null}})},86666:(e,n,t)=>{var a=t(60072),r=t(48727),l=t(34588),i=a.boolean,s=a.overloadedBoolean,o=a.booleanish,u=a.number,c=a.spaceSeparated,p=a.commaSeparated;e.exports=r({space:"html",attributes:{acceptcharset:"accept-charset",classname:"class",htmlfor:"for",httpequiv:"http-equiv"},transform:l,mustUseProperty:["checked","multiple","muted","selected"],properties:{abbr:null,accept:p,acceptCharset:c,accessKey:c,action:null,allowFullScreen:i,allowPaymentRequest:i,allowUserMedia:i,alt:null,as:null,async:i,autoCapitalize:null,autoComplete:c,autoFocus:i,autoPlay:i,capture:i,charSet:null,checked:i,cite:null,className:c,cols:u,colSpan:null,content:null,contentEditable:o,controls:i,controlsList:c,coords:u|p,crossOrigin:null,data:null,dateTime:null,decoding:null,default:i,defer:i,dir:null,dirName:null,disabled:i,download:s,draggable:o,encType:null,form:null,formAction:null,formEncType:null,formMethod:null,formNoValidate:i,formTarget:null,headers:c,height:u,hidden:i,high:u,href:null,hrefLang:null,htmlFor:c,httpEquiv:c,id:null,inputMode:null,integrity:null,is:null,isMap:i,itemId:null,itemProp:c,itemRef:c,itemScope:i,itemType:c,kind:null,label:null,lang:null,language:null,list:null,loop:i,low:u,manifest:null,max:null,maxLength:u,media:null,method:null,min:null,minLength:u,multiple:i,muted:i,name:null,nonce:null,noModule:i,noValidate:i,open:i,optimum:u,pattern:null,ping:c,placeholder:null,playsInline:i,poster:null,preload:null,readOnly:i,referrerPolicy:null,rel:c,required:i,reversed:i,rows:u,rowSpan:u,sandbox:c,scope:null,scoped:i,seamless:i,selected:i,shape:null,size:u,sizes:null,slot:null,span:u,spellCheck:o,src:null,srcDoc:null,srcLang:null,srcSet:p,start:u,step:null,style:null,tabIndex:u,target:null,title:null,translate:null,type:null,typeMustMatch:i,useMap:null,value:o,width:u,wrap:null,align:null,aLink:null,archive:c,axis:null,background:null,bgColor:null,border:u,borderColor:null,bottomMargin:u,cellPadding:null,cellSpacing:null,char:null,charOff:null,classId:null,clear:null,code:null,codeBase:null,codeType:null,color:null,compact:i,declare:i,event:null,face:null,frame:null,frameBorder:null,hSpace:u,leftMargin:u,link:null,longDesc:null,lowSrc:null,marginHeight:u,marginWidth:u,noResize:i,noHref:i,noShade:i,noWrap:i,object:null,profile:null,prompt:null,rev:null,rightMargin:u,rules:null,scheme:null,scrolling:o,standby:null,summary:null,text:null,topMargin:u,valueType:null,version:null,vAlign:null,vLink:null,vSpace:u,allowTransparency:null,autoCorrect:null,autoSave:null,prefix:null,property:null,results:u,security:null,unselectable:null}})},34588:(e,n,t)=>{var a=t(26609);e.exports=function(e,n){return a(e,n.toLowerCase())}},26609:e=>{e.exports=function(e,n){return n in e?e[n]:n}},48727:(e,n,t)=>{var a=t(19015),r=t(63939),l=t(27725);e.exports=function(e){var n,t,i=e.space,s=e.mustUseProperty||[],o=e.attributes||{},u=e.properties,c=e.transform,p={},g={};for(n in u)t=new l(n,c(o,n),u[n],i),-1!==s.indexOf(n)&&(t.mustUseProperty=!0),p[n]=t,g[a(n)]=n,g[a(t.attribute)]=n;return new r(p,g,i)}},27725:(e,n,t)=>{var a=t(20848),r=t(60072);function l(e,n,t,l){i(this,"space",l),a.call(this,e,n),i(this,"boolean",s(t,r.boolean)),i(this,"booleanish",s(t,r.booleanish)),i(this,"overloadedBoolean",s(t,r.overloadedBoolean)),i(this,"number",s(t,r.number)),i(this,"commaSeparated",s(t,r.commaSeparated)),i(this,"spaceSeparated",s(t,r.spaceSeparated)),i(this,"commaOrSpaceSeparated",s(t,r.commaOrSpaceSeparated))}function i(e,n,t){t&&(e[n]=t)}function s(e,n){return(e&n)===n}e.exports=l,l.prototype=new a,l.prototype.defined=!0},20848:e=>{e.exports=t;var n=t.prototype;function t(e,n){this.property=e,this.attribute=n}n.space=null,n.attribute=null,n.property=null,n.boolean=!1,n.booleanish=!1,n.overloadedBoolean=!1,n.number=!1,n.commaSeparated=!1,n.spaceSeparated=!1,n.commaOrSpaceSeparated=!1,n.mustUseProperty=!1,n.defined=!1},10408:(e,n,t)=>{var a=t(90285),r=t(63939);e.exports=function(e){var n,t,l=e.length,i=[],s=[],o=-1;for(;++o<l;)n=e[o],i.push(n.property),s.push(n.normal),t=n.space;return new r(a.apply(null,i),a.apply(null,s),t)}},63939:e=>{e.exports=t;var n=t.prototype;function t(e,n,t){this.property=e,this.normal=n,t&&(this.space=t)}n.space=null,n.normal={},n.property={}},60072:(e,n)=>{var t=0;function a(){return Math.pow(2,++t)}n.boolean=a(),n.booleanish=a(),n.overloadedBoolean=a(),n.number=a(),n.spaceSeparated=a(),n.commaSeparated=a(),n.commaOrSpaceSeparated=a()},31768:(e,n,t)=>{var a=t(48727);e.exports=a({space:"xlink",transform:function(e,n){return"xlink:"+n.slice(5).toLowerCase()},properties:{xLinkActuate:null,xLinkArcRole:null,xLinkHref:null,xLinkRole:null,xLinkShow:null,xLinkTitle:null,xLinkType:null}})},82029:(e,n,t)=>{var a=t(48727);e.exports=a({space:"xml",transform:function(e,n){return"xml:"+n.slice(3).toLowerCase()},properties:{xmlLang:null,xmlBase:null,xmlSpace:null}})},60511:(e,n,t)=>{var a=t(48727),r=t(34588);e.exports=a({space:"xmlns",attributes:{xmlnsxlink:"xmlns:xlink"},transform:r,properties:{xmlns:null,xmlnsXLink:null}})},19015:e=>{e.exports=function(e){return e.toLowerCase()}},41744:(e,n,t)=>{var a,r,l="object"==typeof globalThis?globalThis:"object"==typeof self?self:"object"==typeof window?window:"object"==typeof t.g?t.g:{},i=(r=(a="Prism"in l)?l.Prism:void 0,function(){a?l.Prism=r:delete l.Prism,a=void 0,r=void 0});l.Prism={manual:!0,disableWorkerMessageHandler:!0};var s=t(46651),o=t(61950),u=t(7088),c=t(87644),p=t(72477),g=t(50921),d=t(83905);i();var f={}.hasOwnProperty;function m(){}m.prototype=u;var h=new m;function v(e){if("function"!=typeof e||!e.displayName)throw new Error("Expected `function` for `grammar`, got `"+e+"`");void 0===h.languages[e.displayName]&&e(h)}e.exports=h,h.highlight=function(e,n){var t,a=u.highlight;if("string"!=typeof e)throw new Error("Expected `string` for `value`, got `"+e+"`");if("Object"===h.util.type(n))t=n,n=null;else{if("string"!=typeof n)throw new Error("Expected `string` for `name`, got `"+n+"`");if(!f.call(h.languages,n))throw new Error("Unknown language: `"+n+"` is not registered");t=h.languages[n]}return a.call(this,e,t,n)},h.register=v,h.alias=function(e,n){var t,a,r,l,i=h.languages,s=e;n&&((s={})[e]=n);for(t in s)for(a=s[t],r=(a="string"==typeof a?[a]:a).length,l=-1;++l<r;)i[a[l]]=i[t]},h.registered=function(e){if("string"!=typeof e)throw new Error("Expected `string` for `language`, got `"+e+"`");return f.call(h.languages,e)},h.listLanguages=function(){var e,n=h.languages,t=[];for(e in n)f.call(n,e)&&"object"==typeof n[e]&&t.push(e);return t},v(c),v(p),v(g),v(d),h.util.encode=function(e){return e},h.Token.stringify=function(e,n,t){var a;if("string"==typeof e)return{type:"text",value:e};if("Array"===h.util.type(e))return function(e,n){var t,a=[],r=e.length,l=-1;for(;++l<r;)""!==(t=e[l])&&null!=t&&a.push(t);l=-1,r=a.length;for(;++l<r;)t=a[l],a[l]=h.Token.stringify(t,n,a);return a}(e,n);a={type:e.type,content:h.Token.stringify(e.content,n,t),tag:"span",classes:["token",e.type],attributes:{},language:n,parent:t},e.alias&&(a.classes=a.classes.concat(e.alias));return h.hooks.run("wrap",a),s(a.tag+"."+a.classes.join("."),function(e){var n;for(n in e)e[n]=o(e[n]);return e}(a.attributes),a.content)}},50921:e=>{function n(e){e.languages.clike={comment:[{pattern:/(^|[^\\])\/\*[\s\S]*?(?:\*\/|$)/,lookbehind:!0,greedy:!0},{pattern:/(^|[^\\:])\/\/.*/,lookbehind:!0,greedy:!0}],string:{pattern:/(["'])(?:\\(?:\r\n|[\s\S])|(?!\1)[^\\\r\n])*\1/,greedy:!0},"class-name":{pattern:/(\b(?:class|interface|extends|implements|trait|instanceof|new)\s+|\bcatch\s+\()[\w.\\]+/i,lookbehind:!0,inside:{punctuation:/[.\\]/}},keyword:/\b(?:if|else|while|do|for|return|in|instanceof|function|new|try|throw|catch|finally|null|break|continue)\b/,boolean:/\b(?:true|false)\b/,function:/\w+(?=\()/,number:/\b0x[\da-f]+\b|(?:\b\d+(?:\.\d*)?|\B\.\d+)(?:e[+-]?\d+)?/i,operator:/[<>]=?|[!=]=?=?|--?|\+\+?|&&?|\|\|?|[?*/~^%]/,punctuation:/[{}[\];(),.:]/}}e.exports=n,n.displayName="clike",n.aliases=[]},72477:e=>{function n(e){!function(e){var n=/("|')(?:\\(?:\r\n|[\s\S])|(?!\1)[^\\\r\n])*\1/;e.languages.css={comment:/\/\*[\s\S]*?\*\//,atrule:{pattern:/@[\w-](?:[^;{\s]|\s+(?![\s{]))*(?:;|(?=\s*\{))/,inside:{rule:/^@[\w-]+/,"selector-function-argument":{pattern:/(\bselector\s*\(\s*(?![\s)]))(?:[^()\s]|\s+(?![\s)])|\((?:[^()]|\([^()]*\))*\))+(?=\s*\))/,lookbehind:!0,alias:"selector"},keyword:{pattern:/(^|[^\w-])(?:and|not|only|or)(?![\w-])/,lookbehind:!0}}},url:{pattern:RegExp("\\burl\\((?:"+n.source+"|"+/(?:[^\\\r\n()"']|\\[\s\S])*/.source+")\\)","i"),greedy:!0,inside:{function:/^url/i,punctuation:/^\(|\)$/,string:{pattern:RegExp("^"+n.source+"$"),alias:"url"}}},selector:RegExp("[^{}\\s](?:[^{};\"'\\s]|\\s+(?![\\s{])|"+n.source+")*(?=\\s*\\{)"),string:{pattern:n,greedy:!0},property:/(?!\s)[-_a-z\xA0-\uFFFF](?:(?!\s)[-\w\xA0-\uFFFF])*(?=\s*:)/i,important:/!important\b/i,function:/[-a-z0-9]+(?=\()/i,punctuation:/[(){};:,]/},e.languages.css.atrule.inside.rest=e.languages.css;var t=e.languages.markup;t&&(t.tag.addInlined("style","css"),e.languages.insertBefore("inside","attr-value",{"style-attr":{pattern:/(^|["'\s])style\s*=\s*(?:"[^"]*"|'[^']*')/i,lookbehind:!0,inside:{"attr-value":{pattern:/=\s*(?:"[^"]*"|'[^']*'|[^\s'">=]+)/,inside:{style:{pattern:/(["'])[\s\S]+(?=["']$)/,lookbehind:!0,alias:"language-css",inside:e.languages.css},punctuation:[{pattern:/^=/,alias:"attr-equals"},/"|'/]}},"attr-name":/^style/i}}},t.tag))}(e)}e.exports=n,n.displayName="css",n.aliases=[]},83905:e=>{function n(e){e.languages.javascript=e.languages.extend("clike",{"class-name":[e.languages.clike["class-name"],{pattern:/(^|[^$\w\xA0-\uFFFF])(?!\s)[_$A-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\.(?:prototype|constructor))/,lookbehind:!0}],keyword:[{pattern:/((?:^|})\s*)(?:catch|finally)\b/,lookbehind:!0},{pattern:/(^|[^.]|\.\.\.\s*)\b(?:as|async(?=\s*(?:function\b|\(|[$\w\xA0-\uFFFF]|$))|await|break|case|class|const|continue|debugger|default|delete|do|else|enum|export|extends|for|from|function|(?:get|set)(?=\s*[\[$\w\xA0-\uFFFF])|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)\b/,lookbehind:!0}],function:/#?(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\s*(?:\.\s*(?:apply|bind|call)\s*)?\()/,number:/\b(?:(?:0[xX](?:[\dA-Fa-f](?:_[\dA-Fa-f])?)+|0[bB](?:[01](?:_[01])?)+|0[oO](?:[0-7](?:_[0-7])?)+)n?|(?:\d(?:_\d)?)+n|NaN|Infinity)\b|(?:\b(?:\d(?:_\d)?)+\.?(?:\d(?:_\d)?)*|\B\.(?:\d(?:_\d)?)+)(?:[Ee][+-]?(?:\d(?:_\d)?)+)?/,operator:/--|\+\+|\*\*=?|=>|&&=?|\|\|=?|[!=]==|<<=?|>>>?=?|[-+*/%&|^!=<>]=?|\.{3}|\?\?=?|\?\.?|[~:]/}),e.languages.javascript["class-name"][0].pattern=/(\b(?:class|interface|extends|implements|instanceof|new)\s+)[\w.\\]+/,e.languages.insertBefore("javascript","keyword",{regex:{pattern:/((?:^|[^$\w\xA0-\uFFFF."'\])\s]|\b(?:return|yield))\s*)\/(?:\[(?:[^\]\\\r\n]|\\.)*]|\\.|[^/\\\[\r\n])+\/[gimyus]{0,6}(?=(?:\s|\/\*(?:[^*]|\*(?!\/))*\*\/)*(?:$|[\r\n,.;:})\]]|\/\/))/,lookbehind:!0,greedy:!0,inside:{"regex-source":{pattern:/^(\/)[\s\S]+(?=\/[a-z]*$)/,lookbehind:!0,alias:"language-regex",inside:e.languages.regex},"regex-flags":/[a-z]+$/,"regex-delimiter":/^\/|\/$/}},"function-variable":{pattern:/#?(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\s*[=:]\s*(?:async\s*)?(?:\bfunction\b|(?:\((?:[^()]|\([^()]*\))*\)|(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*)\s*=>))/,alias:"function"},parameter:[{pattern:/(function(?:\s+(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*)?\s*\(\s*)(?!\s)(?:[^()\s]|\s+(?![\s)])|\([^()]*\))+(?=\s*\))/,lookbehind:!0,inside:e.languages.javascript},{pattern:/(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\s*=>)/i,inside:e.languages.javascript},{pattern:/(\(\s*)(?!\s)(?:[^()\s]|\s+(?![\s)])|\([^()]*\))+(?=\s*\)\s*=>)/,lookbehind:!0,inside:e.languages.javascript},{pattern:/((?:\b|\s|^)(?!(?:as|async|await|break|case|catch|class|const|continue|debugger|default|delete|do|else|enum|export|extends|finally|for|from|function|get|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|set|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)(?![$\w\xA0-\uFFFF]))(?:(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*\s*)\(\s*|\]\s*\(\s*)(?!\s)(?:[^()\s]|\s+(?![\s)])|\([^()]*\))+(?=\s*\)\s*\{)/,lookbehind:!0,inside:e.languages.javascript}],constant:/\b[A-Z](?:[A-Z_]|\dx?)*\b/}),e.languages.insertBefore("javascript","string",{"template-string":{pattern:/`(?:\\[\s\S]|\${(?:[^{}]|{(?:[^{}]|{[^}]*})*})+}|(?!\${)[^\\`])*`/,greedy:!0,inside:{"template-punctuation":{pattern:/^`|`$/,alias:"string"},interpolation:{pattern:/((?:^|[^\\])(?:\\{2})*)\${(?:[^{}]|{(?:[^{}]|{[^}]*})*})+}/,lookbehind:!0,inside:{"interpolation-punctuation":{pattern:/^\${|}$/,alias:"punctuation"},rest:e.languages.javascript}},string:/[\s\S]+/}}}),e.languages.markup&&e.languages.markup.tag.addInlined("script","javascript"),e.languages.js=e.languages.javascript}e.exports=n,n.displayName="javascript",n.aliases=["js"]},87644:e=>{function n(e){e.languages.markup={comment:/<!--[\s\S]*?-->/,prolog:/<\?[\s\S]+?\?>/,doctype:{pattern:/<!DOCTYPE(?:[^>"'[\]]|"[^"]*"|'[^']*')+(?:\[(?:[^<"'\]]|"[^"]*"|'[^']*'|<(?!!--)|<!--(?:[^-]|-(?!->))*-->)*\]\s*)?>/i,greedy:!0,inside:{"internal-subset":{pattern:/(\[)[\s\S]+(?=\]>$)/,lookbehind:!0,greedy:!0,inside:null},string:{pattern:/"[^"]*"|'[^']*'/,greedy:!0},punctuation:/^<!|>$|[[\]]/,"doctype-tag":/^DOCTYPE/,name:/[^\s<>'"]+/}},cdata:/<!\[CDATA\[[\s\S]*?]]>/i,tag:{pattern:/<\/?(?!\d)[^\s>\/=$<%]+(?:\s(?:\s*[^\s>\/=]+(?:\s*=\s*(?:"[^"]*"|'[^']*'|[^\s'">=]+(?=[\s>]))|(?=[\s/>])))+)?\s*\/?>/,greedy:!0,inside:{tag:{pattern:/^<\/?[^\s>\/]+/,inside:{punctuation:/^<\/?/,namespace:/^[^\s>\/:]+:/}},"attr-value":{pattern:/=\s*(?:"[^"]*"|'[^']*'|[^\s'">=]+)/,inside:{punctuation:[{pattern:/^=/,alias:"attr-equals"},/"|'/]}},punctuation:/\/?>/,"attr-name":{pattern:/[^\s>\/]+/,inside:{namespace:/^[^\s>\/:]+:/}}}},entity:[{pattern:/&[\da-z]{1,8};/i,alias:"named-entity"},/&#x?[\da-f]{1,8};/i]},e.languages.markup.tag.inside["attr-value"].inside.entity=e.languages.markup.entity,e.languages.markup.doctype.inside["internal-subset"].inside=e.languages.markup,e.hooks.add("wrap",(function(e){"entity"===e.type&&(e.attributes.title=e.content.value.replace(/&amp;/,"&"))})),Object.defineProperty(e.languages.markup.tag,"addInlined",{value:function(n,t){var a={};a["language-"+t]={pattern:/(^<!\[CDATA\[)[\s\S]+?(?=\]\]>$)/i,lookbehind:!0,inside:e.languages[t]},a.cdata=/^<!\[CDATA\[|\]\]>$/i;var r={"included-cdata":{pattern:/<!\[CDATA\[[\s\S]*?\]\]>/i,inside:a}};r["language-"+t]={pattern:/[\s\S]+/,inside:e.languages[t]};var l={};l[n]={pattern:RegExp(/(<__[^>]*>)(?:<!\[CDATA\[(?:[^\]]|\](?!\]>))*\]\]>|(?!<!\[CDATA\[)[\s\S])*?(?=<\/__>)/.source.replace(/__/g,(function(){return n})),"i"),lookbehind:!0,greedy:!0,inside:r},e.languages.insertBefore("markup","cdata",l)}}),e.languages.html=e.languages.markup,e.languages.mathml=e.languages.markup,e.languages.svg=e.languages.markup,e.languages.xml=e.languages.extend("markup",{}),e.languages.ssml=e.languages.xml,e.languages.atom=e.languages.xml,e.languages.rss=e.languages.xml}e.exports=n,n.displayName="markup",n.aliases=["html","mathml","svg","xml","ssml","atom","rss"]},87120:(e,n,t)=>{var a=t(67240);n.Q=function(e){var n=a(String(e||""));return""===n?[]:n.split(r)};var r=/[ \t\n\r\f]+/g},67240:(e,n)=>{(n=e.exports=function(e){return e.replace(/^\s*|\s*$/g,"")}).left=function(e){return e.replace(/^\s*/,"")},n.right=function(e){return e.replace(/\s*$/,"")}},90285:e=>{e.exports=function(){for(var e={},t=0;t<arguments.length;t++){var a=arguments[t];for(var r in a)n.call(a,r)&&(e[r]=a[r])}return e};var n=Object.prototype.hasOwnProperty},37452:e=>{e.exports=JSON.parse('{"AElig":"Æ","AMP":"&","Aacute":"Á","Acirc":"Â","Agrave":"À","Aring":"Å","Atilde":"Ã","Auml":"Ä","COPY":"©","Ccedil":"Ç","ETH":"Ð","Eacute":"É","Ecirc":"Ê","Egrave":"È","Euml":"Ë","GT":">","Iacute":"Í","Icirc":"Î","Igrave":"Ì","Iuml":"Ï","LT":"<","Ntilde":"Ñ","Oacute":"Ó","Ocirc":"Ô","Ograve":"Ò","Oslash":"Ø","Otilde":"Õ","Ouml":"Ö","QUOT":"\\"","REG":"®","THORN":"Þ","Uacute":"Ú","Ucirc":"Û","Ugrave":"Ù","Uuml":"Ü","Yacute":"Ý","aacute":"á","acirc":"â","acute":"´","aelig":"æ","agrave":"à","amp":"&","aring":"å","atilde":"ã","auml":"ä","brvbar":"¦","ccedil":"ç","cedil":"¸","cent":"¢","copy":"©","curren":"¤","deg":"°","divide":"÷","eacute":"é","ecirc":"ê","egrave":"è","eth":"ð","euml":"ë","frac12":"½","frac14":"¼","frac34":"¾","gt":">","iacute":"í","icirc":"î","iexcl":"¡","igrave":"ì","iquest":"¿","iuml":"ï","laquo":"«","lt":"<","macr":"¯","micro":"µ","middot":"·","nbsp":" ","not":"¬","ntilde":"ñ","oacute":"ó","ocirc":"ô","ograve":"ò","ordf":"ª","ordm":"º","oslash":"ø","otilde":"õ","ouml":"ö","para":"¶","plusmn":"±","pound":"£","quot":"\\"","raquo":"»","reg":"®","sect":"§","shy":"­","sup1":"¹","sup2":"²","sup3":"³","szlig":"ß","thorn":"þ","times":"×","uacute":"ú","ucirc":"û","ugrave":"ù","uml":"¨","uuml":"ü","yacute":"ý","yen":"¥","yuml":"ÿ"}')},93580:e=>{e.exports=JSON.parse('{"0":"�","128":"€","130":"‚","131":"ƒ","132":"„","133":"…","134":"†","135":"‡","136":"ˆ","137":"‰","138":"Š","139":"‹","140":"Œ","142":"Ž","145":"‘","146":"’","147":"“","148":"”","149":"•","150":"–","151":"—","152":"˜","153":"™","154":"š","155":"›","156":"œ","158":"ž","159":"Ÿ"}')}}]);