(self.webpackJsonpScriptRunner=self.webpackJsonpScriptRunner||[]).push([["sdCustomFields"],{74729:function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(o,u){function i(e){try{c(r.next(e))}catch(e){u(e)}}function a(e){try{c(r.throw(e))}catch(e){u(e)}}function c(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(i,a)}c((r=r.apply(e,t||[])).next())}))},o=this&&this.__generator||function(e,t){var n,r,o,u,i={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return u={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(u[Symbol.iterator]=function(){return this}),u;function a(u){return function(a){return function(u){if(n)throw new TypeError("Generator is already executing.");for(;i;)try{if(n=1,r&&(o=2&u[0]?r.return:u[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,u[1])).done)return o;switch(r=0,o&&(u=[2&u[0],o.value]),u[0]){case 0:case 1:o=u;break;case 4:return i.label++,{value:u[1],done:!1};case 5:i.label++,r=u[1],u=[0];continue;case 7:u=i.ops.pop(),i.trys.pop();continue;default:if(!(o=i.trys,(o=o.length>0&&o[o.length-1])||6!==u[0]&&2!==u[0])){i=0;continue}if(3===u[0]&&(!o||u[1]>o[0]&&u[1]<o[3])){i.label=u[1];break}if(6===u[0]&&i.label<o[1]){i.label=o[1],o=u;break}if(o&&i.label<o[2]){i.label=o[2],i.ops.push(u);break}o[2]&&i.ops.pop(),i.trys.pop();continue}u=t.call(e,i)}catch(e){u=[6,e],r=0}finally{n=o=0}if(5&u[0])throw u[1];return{value:u[0]?u[1]:void 0,done:!0}}([u,a])}}},u=this&&this.__values||function(e){var t="function"==typeof Symbol&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},i=Object.create,a=Object.defineProperty,c=Object.defineProperties,l=Object.getOwnPropertyDescriptor,s=Object.getOwnPropertyDescriptors,f=Object.getOwnPropertyNames,d=Object.getOwnPropertySymbols,p=Object.getPrototypeOf,h=Object.prototype.hasOwnProperty,b=Object.prototype.propertyIsEnumerable,v=function(e,t,n){return t in e?a(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n},y=function(e,t){var n,r;for(var o in t||(t={}))h.call(t,o)&&v(e,o,t[o]);if(d)try{for(var i=u(d(t)),a=i.next();!a.done;a=i.next()){o=a.value;b.call(t,o)&&v(e,o,t[o])}}catch(e){n={error:e}}finally{try{a&&!a.done&&(r=i.return)&&r.call(i)}finally{if(n)throw n.error}}return e},w=function(e,t){return c(e,s(t))},m=function(e){return a(e,"__esModule",{value:!0})};!function(e,t){for(var n in m(e),t)a(e,n,{get:t[n],enumerable:!0})}(t,{fetchJson:function(){return C},getRequestCountValue:function(){return E},trackedFetchFactory:function(){return j},wrappedFetch:function(){return k}});var g,O=(g=n(60208),function(e,t,n){var r,o;if(t&&"object"==typeof t||"function"==typeof t){var i=function(r){h.call(e,r)||"default"===r||a(e,r,{get:function(){return t[r]},enumerable:!(n=l(t,r))||n.enumerable})};try{for(var c=u(f(t)),s=c.next();!s.done;s=c.next())i(s.value)}catch(e){r={error:e}}finally{try{s&&!s.done&&(o=c.return)&&o.call(c)}finally{if(r)throw r.error}}}return e}(m(a(null!=g?i(p(g)):{},"default",g&&g.__esModule&&"default"in g?{get:function(){return g.default},enumerable:!0}:{value:g,enumerable:!0})),g)),x="Content-Type",P="application/json",j=function(e){return function(t,n){return _(e),k(t,n).finally((function(){return q(e)}))}};function S(e){var t=e.headers.get(x);return t&&-1===t.indexOf("text/html")&&-1===t.indexOf("text/plain")?-1!==t.indexOf("application/json")||t.startsWith("application/")&&-1!==t.indexOf("+json;")?e.text().then((function(e){return e.length>0?JSON.parse(e):null})):t.startsWith("image/")?e.blob():Promise.resolve(null):e.text()}var k=function(e,t){return r(void 0,void 0,void 0,(function(){var n;return o(this,(function(r){return n=(0,O.deepmerge)(function(){var e;return{credentials:"same-origin",headers:(e={"Cache-Control":"no-cache"},e[x]=P,e["X-Atlassian-token"]="no-check",e)}}(),t||{}),[2,fetch(e,n).then((function(e){if(!e.ok){var t={error:e.statusText||"request failed",response:e};return S(e).then((function(e){return Promise.resolve(w(y({},t),{errorResult:e}))})).catch((function(e){return Promise.resolve(t)}))}return S(e).then((function(t){return Promise.resolve({result:t,response:e})})).catch((function(t){return n.method&&["delete","post"].includes(n.method.toLowerCase())?Promise.resolve({result:{},response:e}):(console.warn("Could not parse: ".concat(t)),Promise.resolve({error:"Could not parse: ".concat(t)}))}))})).catch((function(e){return console.warn("Error fetching",e),Promise.resolve({error:"Network ".concat(e)})}))]}))}))},C=function(e,t){return r(void 0,void 0,void 0,(function(){var n;return o(this,(function(r){return[2,k(e,w(y({},t),{headers:w(y({},null!=(n=null==t?void 0:t.headers)?n:{}),{Accept:P})}))]}))}))},_=function(e){e&&e.length&&F(e,E(e)+1)},q=function(e){e&&e.length&&F(e,E(e)-1)},E=function(e){return Number.parseInt(document.body.dataset[e]||"0",10)},F=function(e,t){document.body.dataset[e]=t.toString()}},84806:(e,t,n)=>{"use strict";n.d(t,{X7:()=>u,bK:()=>o});var r=function(){return r=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},r.apply(this,arguments)},o=function(e,t){return void 0===t&&(t={}),new Promise((function(n){return i(document.body,e,n,r({subtree:!1},t))}))},u=function(e,t,n){return void 0===n&&(n={}),new Promise((function(r){return i(e,t,r,n)}))},i=function(e,t,n,o){void 0===o&&(o={});var u=e.querySelector(t);u?n(u):new MutationObserver((function(r,o){var u=e.querySelector(t);u&&(o.disconnect(),n(u))})).observe(e,r({childList:!0,subtree:!0,attributes:!1,characterData:!1},o))}},39507:(e,t,n)=>{"use strict";n.d(t,{F:()=>i});var r=n(17619),o=n(29577),u=n(16897),i=function(e){return r.stringify(o.Z((function(e){return!u.Z(e)}),e))}},61671:(e,t,n)=>{"use strict";var r=n(84806),o=n(39507),u=n(1559),i=(n(17775),n(5667),n(74729)),a=function(e,t,n,r){return new(n||(n=Promise))((function(o,u){function i(e){try{c(r.next(e))}catch(e){u(e)}}function a(e){try{c(r.throw(e))}catch(e){u(e)}}function c(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(i,a)}c((r=r.apply(e,t||[])).next())}))},c=function(e,t){var n,r,o,u,i={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return u={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(u[Symbol.iterator]=function(){return this}),u;function a(u){return function(a){return function(u){if(n)throw new TypeError("Generator is already executing.");for(;i;)try{if(n=1,r&&(o=2&u[0]?r.return:u[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,u[1])).done)return o;switch(r=0,o&&(u=[2&u[0],o.value]),u[0]){case 0:case 1:o=u;break;case 4:return i.label++,{value:u[1],done:!1};case 5:i.label++,r=u[1],u=[0];continue;case 7:u=i.ops.pop(),i.trys.pop();continue;default:if(!(o=i.trys,(o=o.length>0&&o[o.length-1])||6!==u[0]&&2!==u[0])){i=0;continue}if(3===u[0]&&(!o||u[1]>o[0]&&u[1]<o[3])){i.label=u[1];break}if(6===u[0]&&i.label<o[1]){i.label=o[1],o=u;break}if(o&&i.label<o[2]){i.label=o[2],i.ops.push(u);break}o[2]&&i.ops.pop(),i.trys.pop();continue}u=t.call(e,i)}catch(e){u=[6,e],r=0}finally{n=o=0}if(5&u[0])throw u[1];return{value:u[0]?u[1]:void 0,done:!0}}([u,a])}}},l=function(e){var t="function"==typeof Symbol&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},s=jQuery;s((function(){n(34166)().on("change",(function(e){return a(void 0,void 0,void 0,(function(){var e,t,n;return c(this,(function(f){switch(f.label){case 0:return e=document.location.pathname,(t=e.match(/\/servicedesk\/admin\/.+\/request-types\/request-type\/(\d+)/))?[4,(0,r.bK)(".sd-edit-fields-container",{subtree:!0})]:[3,2];case 1:f.sent(),n=new MutationObserver((function(e){return function(e,t){return a(void 0,void 0,void 0,(function(){var n,r,a,f,d,p,h;return c(this,(function(c){switch(c.label){case 0:return n=[],t.forEach((function(e){return e.addedNodes.forEach((function(e){if(e.parentNode)return e.parentNode.querySelectorAll("tr.aui-restfultable-row").forEach((function(e){return n.push(e.dataset.id)}))}))})),n.length?[4,(0,i.wrappedFetch)("".concat(AJS.contextPath(),"/rest/scriptrunner/behaviours/latest/portal/fields/updateFieldTypes?").concat((0,o.F)({projectKey:JIRA.API.Projects.getCurrentProjectKey(),requestTypeId:e,rowIds:u.Z(n)})),{method:"POST"})]:[3,2];case 1:if((r=c.sent()).result.requiresRefresh)document.location.reload();else try{for(a=l(r.result.ourRowIds),f=a.next();!f.done;f=a.next())d=f.value,s("tr.aui-restfultable-row[data-id=".concat(d,"] div.no-preset-warning")).remove()}catch(e){p={error:e}}finally{try{f&&!f.done&&(h=a.return)&&h.call(a)}finally{if(p)throw p.error}}c.label=2;case 2:return[2]}}))}))}(t[1],e)})),n.observe(s(".sd-edit-fields-container form").last()[0],{childList:!0,subtree:!0}),s(window).on("beforeunload",(function(){n.disconnect()})),f.label=2;case 2:return[2]}}))}))}))}))},5667:(e,t,n)=>{e.exports=n},17775:(e,t,n)=>{e.exports=n}},e=>{e.O(0,["bhResources","sdCustomFieldsResources"],(()=>{return t=61671,e(e.s=t);var t}));e.O()}]);