///<reference path="../../../../../../node_modules/@types/jasmine/index.d.ts"/>
///<reference path="../../../../../../node_modules/@types/jasmine-jquery/index.d.ts"/>
// ///<reference path="node_modules/@types/underscore/index.d.ts"/>
'use strict'

import {
    getFieldIdForName,
    getFieldsForProjectAndIssueType,
    moveJasmineStuffToTop,
    removeDirtyFormWarning,
    whenSetSelectionValueTriggered
} from '../utils/test-utils'
import fetchMock from 'fetch-mock'
import { Behaviours } from '../../behaviours'
import 'wr-dependency!js-test-resources'
import { nextTick } from '../utils/concurrency-utils'
import '../utils/jasmine-matchers-jest-bridge'

describe('jira agile fields', () => {
    let customFieldData
    const JBHV = new Behaviours()

    beforeAll(async () => {
        removeDirtyFormWarning()

        customFieldData = await getFieldsForProjectAndIssueType('JRA' ,'Bug')
    })

    afterAll(moveJasmineStuffToTop)

    afterEach(() => {
        fetchMock.restore()
    })

    describe('sprints', () => {
        let $field
        let fieldId

        beforeEach(async () => {
            fieldId = getFieldIdForName(customFieldData,'Sprint')

            $field = $(`#${fieldId}`)

            await JBHV.addFieldListeners(
                $field.closest('form'),
                {
                    [fieldId]: {
                        field: $field,
                        fieldType: 'com.pyxis.greenhopper.jira:gh-sprint',
                        validator: 'server',
                    },
                },
                true
            )
        })

        const getFieldValue = () => JBHV.constructForm($field.closest('form'))[fieldId]

        it('operations on sprints', async () => {
            JBHV.makeReadOnly($field)

            const $displayField = $(`#${fieldId}-field`)
            expect($displayField).toBeDisabled()

            JBHV.makeWritable($field)
            expect($displayField).not.toBeDisabled()

            fetchMock.get(`${AJS.contextPath()}/rest/agile/1.0/sprint/7`, {
                id: 7,
                self: 'http://localhost:8080/jira/rest/agile/1.0/sprint/1',
                state: 'active',
                name: 'Sample Sprint 7',
            })
            fetchMock.post('*', {}) // /runvalidator

            JBHV.setFieldValue($field, '7', fieldId)

            await nextTick()
            expect($displayField.val()).toEqual('Sample Sprint 7')
            expect(getFieldValue()).toEqual('7')

            // todo: test missing values etc and using ints
        })
    })

    const targetEpicKey = 'JRA-9999'

    describe('epics', () => {
        let $field
        let fieldId

        beforeEach(async () => {
            fieldId = getFieldIdForName(customFieldData, 'Epic Link')

            $field = $(`#${fieldId}`)

            await JBHV.addFieldListeners(
                $field.closest('form'),
                {
                    [fieldId]: {
                        field: $field,
                        fieldType: 'com.pyxis.greenhopper.jira:gh-epic-link',
                        validator: 'server',
                    },
                },
                true
            )

            fetchMock.get(`${AJS.contextPath()}/rest/api/2/field`, [
                {
                    id: 'customfield_10005',
                    name: 'Epic Name',
                    schema: {
                        custom: 'com.pyxis.greenhopper.jira:gh-epic-label',
                    },
                },
            ])

            fetchMock.get(`${AJS.contextPath()}/rest/api/2/issue/${targetEpicKey}?fields=customfield_10005`, {
                expand: 'renderedFields,names,schema,operations,editmeta,changelog,versionedRepresentations',
                id: '10201',
                key: 'JRA-3',
                fields: {
                    customfield_10005: 'MY EPIC 1',
                },
            })
        })

        it('operations on epics', async () => {
            JBHV.makeReadOnly($field)

            const $displayField = $(`#${fieldId}-field`)
            expect($displayField).toBeDisabled()

            JBHV.makeWritable($field)
            expect($displayField).not.toBeDisabled()

            fetchMock.post('*', {}) // /runvalidator

            JBHV.setFieldValue($field, targetEpicKey, fieldId)

            await whenSetSelectionValueTriggered($field)
            expect($displayField).toHaveValue('MY EPIC 1')

            JBHV.setFieldValue($field, null, fieldId)
            expect($displayField).toBeEmpty()
        })

        it('failures on epics', () => {
            fetchMock.get(
                `${AJS.contextPath()}/rest/api/2/issue/${targetEpicKey}?fields=customfield_10005`,
                {
                    expand: 'renderedFields,names,schema,operations,editmeta,changelog,versionedRepresentations',
                    id: '10201',
                    key: 'JRA-3',
                    fields: {},
                },
                { method: 'POST', overwriteRoutes: true }
            )
            fetchMock.post('*', {}) // /runvalidator

            JBHV.setFieldValue($field, targetEpicKey, fieldId)

            // expect log value, nothing else
            expect().nothing()
        })
    })
})
