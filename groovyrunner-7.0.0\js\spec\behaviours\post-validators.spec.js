///<reference path="../../../../../../node_modules/@types/jasmine/index.d.ts"/>
///<reference path="../../../../../../node_modules/@types/jasmine-jquery/index.d.ts"/>
'use strict'

import { moveJasmineStuffToTop, removeDirtyFormWarning } from '../utils/test-utils'
import fetchMock from 'fetch-mock'
import { Behaviours } from '../../behaviours'
import 'wr-dependency!js-test-resources'
import { nextTick } from '../utils/concurrency-utils'
import '../utils/jasmine-matchers-jest-bridge'

describe('behaviours are updated in response to events', () => {
    beforeAll(removeDirtyFormWarning)
    afterAll(moveJasmineStuffToTop)

    const JBHV = new Behaviours()

    afterEach(() => {
        fetchMock.restore()
    })

    it('priority changes on summary change', async () => {
        const fieldId = 'summary'
        const $field = $('#summary')

        await JBHV.addFieldListeners(
            $field.closest('form'),
            {
                [fieldId]: {
                    field: $field,
                    fieldId: fieldId,
                    fieldType: 'com.atlassian.jira.issue.fields.SummarySystemField',
                    validator: 'server',
                },
            },
            true
        )

        const validators = {
            priority: {
                readonly: true,
                fieldType: 'com.atlassian.jira.issue.fields.PrioritySystemField',
                setValue: '1',
            },
        }

        fetchMock.post('*', validators)

        JBHV.setFieldValue($field, 'some changed summary', fieldId)
        $field.trigger('change')

        await nextTick()

        const $priorityField = $('#priority-field')
        expect($priorityField).toHaveValue('Highest')
        expect($priorityField).toBeDisabled()
    })

    it('priority does not change without server validator', async () => {
        const fieldId = 'summary'
        const $field = $('#summary')

        // we currently have no support for unregistering change listeners, so we have to do it manually
        $field.off('change.jbhv')

        await JBHV.addFieldListeners(
            $field.closest('form'),
            {
                [fieldId]: {
                    field: $field,
                    fieldType: 'com.atlassian.jira.issue.fields.SummarySystemField',
                },
            },
            true
        )

        fetchMock.post('*', {})

        await JBHV.setFieldValue($field, 'some changed summary', fieldId)
        $field.trigger('change')

        console.log(fetchMock.calls())
        expect(fetchMock.called('*')).toBeFalsy()
    })

    it("summary changes summary but doesn't add new events", async () => {
        const fieldId = 'summary'
        const $field = $('#summary')

        await JBHV.addFieldListeners($field.closest('form'), {
            [fieldId]: {
                field: $field,
                fieldType: 'com.atlassian.jira.issue.fields.SummarySystemField',
                validator: 'server',
            },
        })

        const validators = {
            summary: {
                fieldType: 'com.atlassian.jira.issue.fields.SummarySystemField',
                setValue: 'bar',
            },
        }

        fetchMock.post('*', validators)

        JBHV.setFieldValue($field, 'some changed summary', fieldId)
        $field.trigger('change')

        await nextTick()
        expect($field).toHaveValue('bar')
    })
})
