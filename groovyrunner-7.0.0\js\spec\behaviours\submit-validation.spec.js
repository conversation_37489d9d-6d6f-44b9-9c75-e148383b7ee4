///<reference path="../../../../../../node_modules/@types/jasmine/index.d.ts"/>
///<reference path="../../../../../../node_modules/@types/jasmine-jquery/index.d.ts"/>
'use strict'

import { eventually, moveJasmineStuffToTop, removeDirtyFormWarning } from '../utils/test-utils'
import { Behaviours } from '../../behaviours/index'
import 'wr-dependency!js-test-resources'
import { getServerCallCount } from '../../behaviours/fetch-utils'
import { nextTick } from '../utils/concurrency-utils'
import '../utils/jasmine-matchers-jest-bridge'

/**
 * Test fields that are made up of several inputs
 */
describe('submit validation', () => {
    const JBHV = new Behaviours()

    beforeAll(removeDirtyFormWarning)
    afterAll(moveJasmineStuffToTop)

    it("can't submit when fields are required", async () => {
        let fieldId = 'environment'
        let $field = $(`#${fieldId}`)
        const $form = $field.closest('form')

        await JBHV.addFieldListeners($form, {
            [fieldId]: {
                field: $field,
                validator: 'server',
                required: true,
            },
        })

        await eventually(() => getServerCallCount() === 0, 1000)
        $form.submit()

        expect($field.val()).toBeFalsy()
        expect($field.closest('div.field-group').find('.jbhverror')).toExist()
    })
})
