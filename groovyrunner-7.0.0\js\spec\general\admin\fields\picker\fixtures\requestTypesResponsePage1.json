{"size": 4, "start": 0, "limit": 4, "isLastPage": false, "_links": {"base": "http://localhost:8080/jira", "context": "/jira", "next": "http://localhost:8080/jira/rest/servicedeskapi/servicedesk/1/requesttype?limit=4&start=4", "self": "http://localhost:8080/jira/rest/servicedeskapi/servicedesk/1/requesttype?limit=4&start=0"}, "values": [{"id": "1", "_links": {"self": "http://localhost:8080/jira/rest/servicedeskapi/servicedesk/1/requesttype/1"}, "name": "IT help", "description": "Get general tech support, like help with the Wi-Fi or printing.", "helpText": "", "serviceDeskId": "1", "groupIds": ["1"], "icon": {"id": "10527", "_links": {"iconUrls": {"48x48": "http://localhost:8080/jira/secure/viewavatar?avatarType=SD_REQTYPE&size=large&avatarId=10527", "24x24": "http://localhost:8080/jira/secure/viewavatar?avatarType=SD_REQTYPE&size=small&avatarId=10527", "16x16": "http://localhost:8080/jira/secure/viewavatar?avatarType=SD_REQTYPE&size=xsmall&avatarId=10527", "32x32": "http://localhost:8080/jira/secure/viewavatar?avatarType=SD_REQTYPE&size=medium&avatarId=10527"}}}}, {"id": "2", "_links": {"self": "http://localhost:8080/jira/rest/servicedeskapi/servicedesk/1/requesttype/2"}, "name": "Computer support", "description": "If you have problems with your laptop, let us know here.", "helpText": "", "serviceDeskId": "1", "groupIds": ["1"], "icon": {"id": "10510", "_links": {"iconUrls": {"48x48": "http://localhost:8080/jira/secure/viewavatar?avatarType=SD_REQTYPE&size=large&avatarId=10510", "24x24": "http://localhost:8080/jira/secure/viewavatar?avatarType=SD_REQTYPE&size=small&avatarId=10510", "16x16": "http://localhost:8080/jira/secure/viewavatar?avatarType=SD_REQTYPE&size=xsmall&avatarId=10510", "32x32": "http://localhost:8080/jira/secure/viewavatar?avatarType=SD_REQTYPE&size=medium&avatarId=10510"}}}}, {"id": "3", "_links": {"self": "http://localhost:8080/jira/rest/servicedeskapi/servicedesk/1/requesttype/3"}, "name": "Purchase under $100", "description": "Order something small, like a keyboard. If it's under $100, you don't need approval.", "helpText": "", "serviceDeskId": "1", "groupIds": ["1"], "icon": {"id": "10512", "_links": {"iconUrls": {"48x48": "http://localhost:8080/jira/secure/viewavatar?avatarType=SD_REQTYPE&size=large&avatarId=10512", "24x24": "http://localhost:8080/jira/secure/viewavatar?avatarType=SD_REQTYPE&size=small&avatarId=10512", "16x16": "http://localhost:8080/jira/secure/viewavatar?avatarType=SD_REQTYPE&size=xsmall&avatarId=10512", "32x32": "http://localhost:8080/jira/secure/viewavatar?avatarType=SD_REQTYPE&size=medium&avatarId=10512"}}}}, {"id": "4", "_links": {"self": "http://localhost:8080/jira/rest/servicedeskapi/servicedesk/1/requesttype/4"}, "name": "Employee exit", "description": "Moving on to better things? Start your transition here.", "helpText": "", "serviceDeskId": "1", "groupIds": ["1"], "icon": {"id": "10526", "_links": {"iconUrls": {"48x48": "http://localhost:8080/jira/secure/viewavatar?avatarType=SD_REQTYPE&size=large&avatarId=10526", "24x24": "http://localhost:8080/jira/secure/viewavatar?avatarType=SD_REQTYPE&size=small&avatarId=10526", "16x16": "http://localhost:8080/jira/secure/viewavatar?avatarType=SD_REQTYPE&size=xsmall&avatarId=10526", "32x32": "http://localhost:8080/jira/secure/viewavatar?avatarType=SD_REQTYPE&size=medium&avatarId=10526"}}}}]}