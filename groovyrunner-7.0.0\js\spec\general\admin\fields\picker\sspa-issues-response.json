{"sections": [{"label": "Select Issue", "sub": "Showing 10 of 23 matching issues", "id": "cs", "issues": [{"key": "SSPA-23", "keyHtml": "SSPA-23", "img": "/images/icons/issuetypes/story.svg", "summary": "As a user, I&#39;d like a historical story to show in reports", "summaryText": "As a user, I'd like a historical story to show in reports", "id": 10022}, {"key": "SSPA-22", "keyHtml": "SSPA-22", "img": "/images/icons/issuetypes/story.svg", "summary": "As a user, I&#39;d like a historical story to show in reports", "summaryText": "As a user, I'd like a historical story to show in reports", "id": 10021}, {"key": "SSPA-21", "keyHtml": "SSPA-21", "img": "/images/icons/issuetypes/story.svg", "summary": "As a user, I&#39;d like a historical story to show in reports", "summaryText": "As a user, I'd like a historical story to show in reports", "id": 10020}, {"key": "SSPA-20", "keyHtml": "SSPA-20", "img": "/images/icons/issuetypes/story.svg", "summary": "As a user, I&#39;d like a historical story to show in reports", "summaryText": "As a user, I'd like a historical story to show in reports", "id": 10019}, {"key": "SSPA-19", "keyHtml": "SSPA-19", "img": "/images/icons/issuetypes/story.svg", "summary": "As a user, I&#39;d like a historical story to show in reports", "summaryText": "As a user, I'd like a historical story to show in reports", "id": 10018}, {"key": "SSPA-18", "keyHtml": "SSPA-18", "img": "/images/icons/issuetypes/story.svg", "summary": "As a user, I&#39;d like a historical story to show in reports", "summaryText": "As a user, I'd like a historical story to show in reports", "id": 10017}, {"key": "SSPA-17", "keyHtml": "SSPA-17", "img": "/secure/viewavatar?size=xsmall&avatarId=10303&avatarType=issuetype", "summary": "Instructions for deleting this sample board and project are in the description for this issue &gt;&gt; Click the &quot;SSPA-17&quot; link and read the description tab of the detail view for more", "summaryText": "Instructions for deleting this sample board and project are in the description for this issue >> Click the \"SSPA-17\" link and read the description tab of the detail view for more", "id": 10016}, {"key": "SSPA-16", "keyHtml": "SSPA-16", "img": "/images/icons/issuetypes/story.svg", "summary": "As a team, we can finish the sprint by clicking the cog icon next to the sprint name above the &quot;To Do&quot; column then selecting &quot;Complete Sprint&quot; &gt;&gt; Try closing this sprint now", "summaryText": "As a team, we can finish the sprint by clicking the cog icon next to the sprint name above the \"To Do\" column then selecting \"Complete Sprint\" >> Try closing this sprint now", "id": 10015}, {"key": "SSPA-15", "keyHtml": "SSPA-15", "img": "/images/icons/issuetypes/story.svg", "summary": "As a scrum master, I can see the progress of a sprint via the Burndown Chart &gt;&gt; Click &quot;Reports&quot; to view the Burndown Chart", "summaryText": "As a scrum master, I can see the progress of a sprint via the Burndown Chart >> Click \"Reports\" to view the Burndown Chart", "id": 10014}, {"key": "SSPA-14", "keyHtml": "SSPA-14", "img": "/images/icons/issuetypes/story.svg", "summary": "As a user, I can find important items on the board by using the customisable &quot;Quick Filters&quot; above &gt;&gt; Try clicking the &quot;Only My Issues&quot; Quick Filter above", "summaryText": "As a user, I can find important items on the board by using the customisable \"Quick Filters\" above >> Try clicking the \"Only My Issues\" Quick Filter above", "id": 10013}], "totalIssues": 23}]}