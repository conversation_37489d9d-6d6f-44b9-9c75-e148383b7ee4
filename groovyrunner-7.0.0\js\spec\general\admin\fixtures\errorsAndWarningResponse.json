{"compilationResult": {"errors": [{"startColumn": 1, "sourceLocator": "Script44.groovy", "suppressed": [], "message": "[Static type checking] - The variable [ss] is undeclared.\n @ line 13, column 1.", "endLine": 13, "stackTraceDepth": 275, "cause": null, "originalMessage": "[Static type checking] - The variable [ss] is undeclared.\n", "fatal": false, "localizedMessage": "[Static type checking] - The variable [ss] is undeclared.\n @ line 13, column 1.", "stackTrace": [{"className": "sun.reflect.GeneratedConstructorAccessor434", "nativeMethod": false, "lineNumber": -1, "fileName": null, "methodName": "newInstance"}], "endColumn": 51, "line": 11, "startLine": 11}], "warnings": [{"startColumn": 1, "sourceLocator": "Script44.groovy", "suppressed": [], "message": "<span class=\"deprecationComment\">Use <a href=\"com/atlassian/jira/issue/IssueManager.html#getIssueObject-java.lang.Long-\"><code>IssueManager.getIssueObject(Long)</code></a> instead.</span> @ line 11, column 1.", "endLine": 11, "stackTraceDepth": 339, "cause": null, "originalMessage": "<span class=\"deprecationComment\">Use <a href=\"com/atlassian/jira/issue/IssueManager.html#getIssueObject-java.lang.Long-\"><code>IssueManager.getIssueObject(Long)</code></a> instead.</span>", "fatal": false, "localizedMessage": "<span class=\"deprecationComment\">Use <a href=\"com/atlassian/jira/issue/IssueManager.html#getIssueObject-java.lang.Long-\"><code>IssueManager.getIssueObject(Long)</code></a> instead.</span> @ line 11, column 1.", "endColumn": 51, "line": 11, "startLine": 11}], "parameters": {"known": false}}}