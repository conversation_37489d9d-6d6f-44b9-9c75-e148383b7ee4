{"compilationResult": {"errors": [{"startColumn": 1, "sourceLocator": "def", "suppressed": [], "message": "Cannot find file: def @ line 1, column 1.", "endLine": 1, "stackTraceDepth": 277, "cause": null, "originalMessage": "Cannot find file: def", "fatal": false, "localizedMessage": "Cannot find file: def @ line 1, column 1.", "stackTrace": [{"className": "sun.reflect.GeneratedConstructorAccessor427", "nativeMethod": false, "lineNumber": -1, "fileName": null, "methodName": "newInstance"}], "endColumn": 2, "line": 1, "startLine": 1}], "warnings": [], "parameters": {"known": false}}}