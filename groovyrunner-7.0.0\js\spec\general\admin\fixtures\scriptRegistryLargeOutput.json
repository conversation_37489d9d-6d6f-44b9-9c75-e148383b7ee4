{"output": {"workflows": [{"active": true, "transitions": [{"href": "http://localhost:8080/jira/secure/admin/workflows/ViewWorkflowTransition.jspa?workflowMode=live&workflowName=BXKOVG&workflowTransition=1", "name": "Create Issue", "configuredItems": [{"scripts": [{"scriptFile": "", "description": "Enter the condition for which this function will fire. <PERSON><PERSON> will evaluate to \"true\".\n                You can click one of the examples below, or see the wiki page for further examples.", "scriptCompileCtxOptions": null, "inlineScript": "issue.priority?.name == 'High'", "name": "Condition", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.JiraCommonCompileContexts.CONDITION_AND_ADDITIONAL_CONTEXT"}, {"scriptFile": "", "description": "Enter any customisations to the target issue, e.g. hard-coding specific field values.", "scriptCompileCtxOptions": null, "inlineScript": "hello", "name": "Additional issue actions", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.AbstractCloneIssue.CLONE_ISSUE_ADDITIONAL_CODE_CONTEXT"}], "description": "<PERSON>lones this issue to another issue, optionally another project and issue type, and creates a link.\n        ", "clazz": null, "name": "Clones an issue, and links"}]}, {"href": "http://localhost:8080/jira/secure/admin/workflows/ViewWorkflowTransition.jspa?workflowMode=live&workflowName=BXKOVG&workflowTransition=2&workflowStep=1", "name": "Close Issue", "configuredItems": [{"scripts": [{"scriptFile": "", "description": "Enter the condition for which this function will fire. <PERSON><PERSON> will evaluate to \"true\".\n                You can click one of the examples below, or see the wiki page for further examples.", "scriptCompileCtxOptions": null, "inlineScript": "issue.priority?.name == 'High'", "name": "Condition", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.JiraCommonCompileContexts.CONDITION_AND_ADDITIONAL_CONTEXT"}, {"scriptFile": "", "description": "Enter any customisations to the target issue, e.g. hard-coding specific field values.", "scriptCompileCtxOptions": null, "inlineScript": "null", "name": "Additional issue actions", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.AbstractCloneIssue.CLONE_ISSUE_ADDITIONAL_CODE_CONTEXT"}], "description": "<PERSON>lones this issue to another issue, optionally another project and issue type, and creates a link.\n        ", "clazz": null, "name": "Clones an issue, and links"}]}, {"href": "http://localhost:8080/jira/secure/admin/workflows/ViewWorkflowTransition.jspa?workflowMode=live&workflowName=BXKOVG&workflowTransition=3&workflowStep=4", "name": "Reopen Issue", "configuredItems": [{"scripts": [{"scriptFile": "", "description": "Enter the condition for which this function will fire. <PERSON><PERSON> will evaluate to \"true\".\n                You can click one of the examples below, or see the wiki page for further examples.", "scriptCompileCtxOptions": null, "inlineScript": "issue.priority?.name == 'High'", "name": "Condition", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.JiraCommonCompileContexts.CONDITION_AND_ADDITIONAL_CONTEXT"}, {"scriptFile": "", "description": "Enter any customisations to the target issue, e.g. hard-coding specific field values.", "scriptCompileCtxOptions": null, "inlineScript": "null", "name": "Additional issue actions", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.AbstractCloneIssue.CLONE_ISSUE_ADDITIONAL_CODE_CONTEXT"}], "description": "<PERSON>lones this issue to another issue, optionally another project and issue type, and creates a link.\n        ", "clazz": null, "name": "Clones an issue, and links"}]}, {"href": "http://localhost:8080/jira/secure/admin/workflows/ViewWorkflowTransition.jspa?workflowMode=live&workflowName=BXKOVG&workflowTransition=4&workflowStep=1", "name": "Start Progress", "configuredItems": [{"scripts": [{"scriptFile": "", "description": "Enter the condition for which this function will fire. <PERSON><PERSON> will evaluate to \"true\".\n                You can click one of the examples below, or see the wiki page for further examples.", "scriptCompileCtxOptions": null, "inlineScript": "issue.priority?.name == 'High'", "name": "Condition", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.JiraCommonCompileContexts.CONDITION_AND_ADDITIONAL_CONTEXT"}, {"scriptFile": "", "description": "Enter any customisations to the target issue, e.g. hard-coding specific field values.", "scriptCompileCtxOptions": null, "inlineScript": "null", "name": "Additional issue actions", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.AbstractCloneIssue.CLONE_ISSUE_ADDITIONAL_CODE_CONTEXT"}], "description": "<PERSON>lones this issue to another issue, optionally another project and issue type, and creates a link.\n        ", "clazz": null, "name": "Clones an issue, and links"}]}, {"href": "http://localhost:8080/jira/secure/admin/workflows/ViewWorkflowTransition.jspa?workflowMode=live&workflowName=BXKOVG&workflowTransition=5&workflowStep=1", "name": "Resolve Issue", "configuredItems": [{"scripts": [{"scriptFile": "", "description": "Enter the condition for which this function will fire. <PERSON><PERSON> will evaluate to \"true\".\n                You can click one of the examples below, or see the wiki page for further examples.", "scriptCompileCtxOptions": null, "inlineScript": "issue.priority?.name == 'High'", "name": "Condition", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.JiraCommonCompileContexts.CONDITION_AND_ADDITIONAL_CONTEXT"}, {"scriptFile": "", "description": "Enter any customisations to the target issue, e.g. hard-coding specific field values.", "scriptCompileCtxOptions": null, "inlineScript": "null", "name": "Additional issue actions", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.AbstractCloneIssue.CLONE_ISSUE_ADDITIONAL_CODE_CONTEXT"}], "description": "<PERSON>lones this issue to another issue, optionally another project and issue type, and creates a link.\n        ", "clazz": null, "name": "Clones an issue, and links"}]}, {"href": "http://localhost:8080/jira/secure/admin/workflows/ViewWorkflowTransition.jspa?workflowMode=live&workflowName=BXKOVG&workflowTransition=301", "name": "Stop Progress", "configuredItems": [{"scripts": [{"scriptFile": "", "description": "Enter the condition for which this function will fire. <PERSON><PERSON> will evaluate to \"true\".\n                You can click one of the examples below, or see the wiki page for further examples.", "scriptCompileCtxOptions": null, "inlineScript": "issue.priority?.name == 'High'", "name": "Condition", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.JiraCommonCompileContexts.CONDITION_AND_ADDITIONAL_CONTEXT"}, {"scriptFile": "", "description": "Enter any customisations to the target issue, e.g. hard-coding specific field values.", "scriptCompileCtxOptions": null, "inlineScript": "null", "name": "Additional issue actions", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.AbstractCloneIssue.CLONE_ISSUE_ADDITIONAL_CODE_CONTEXT"}], "description": "<PERSON>lones this issue to another issue, optionally another project and issue type, and creates a link.\n        ", "clazz": null, "name": "Clones an issue, and links"}]}, {"href": "http://localhost:8080/jira/secure/admin/workflows/ViewWorkflowTransition.jspa?workflowMode=live&workflowName=BXKOVG&workflowTransition=701", "name": "Close Issue", "configuredItems": [{"scripts": [{"scriptFile": "", "description": "Enter the condition for which this function will fire. <PERSON><PERSON> will evaluate to \"true\".\n                You can click one of the examples below, or see the wiki page for further examples.", "scriptCompileCtxOptions": null, "inlineScript": "issue.priority?.name == 'High'", "name": "Condition", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.JiraCommonCompileContexts.CONDITION_AND_ADDITIONAL_CONTEXT"}, {"scriptFile": "", "description": "Enter any customisations to the target issue, e.g. hard-coding specific field values.", "scriptCompileCtxOptions": null, "inlineScript": "null", "name": "Additional issue actions", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.AbstractCloneIssue.CLONE_ISSUE_ADDITIONAL_CODE_CONTEXT"}], "description": "<PERSON>lones this issue to another issue, optionally another project and issue type, and creates a link.\n        ", "clazz": null, "name": "Clones an issue, and links"}]}], "draft": false, "name": "BXKOVG"}, {"active": true, "transitions": [{"href": "http://localhost:8080/jira/secure/admin/workflows/ViewWorkflowTransition.jspa?workflowMode=live&workflowName=COZGAQ&workflowTransition=1", "name": "Create Issue", "configuredItems": [{"scripts": [{"scriptFile": "", "description": "Enter the condition for which this function will fire. <PERSON><PERSON> will evaluate to \"true\".\n                You can click one of the examples below, or see the wiki page for further examples.", "scriptCompileCtxOptions": null, "inlineScript": "issue.priority?.name == 'High'", "name": "Condition", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.JiraCommonCompileContexts.CONDITION_AND_ADDITIONAL_CONTEXT"}, {"scriptFile": "", "description": "Enter any customisations to the target issue, e.g. hard-coding specific field values.", "scriptCompileCtxOptions": null, "inlineScript": "null", "name": "Additional issue actions", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.AbstractCloneIssue.CLONE_ISSUE_ADDITIONAL_CODE_CONTEXT"}], "description": "<PERSON>lones this issue to another issue, optionally another project and issue type, and creates a link.\n        ", "clazz": null, "name": "Clones an issue, and links"}]}, {"href": "http://localhost:8080/jira/secure/admin/workflows/ViewWorkflowTransition.jspa?workflowMode=live&workflowName=COZGAQ&workflowTransition=2&workflowStep=1", "name": "Close Issue", "configuredItems": [{"scripts": [{"scriptFile": "", "description": "Enter the condition for which this function will fire. <PERSON><PERSON> will evaluate to \"true\".\n                You can click one of the examples below, or see the wiki page for further examples.", "scriptCompileCtxOptions": null, "inlineScript": "issue.priority?.name == 'High'", "name": "Condition", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.JiraCommonCompileContexts.CONDITION_AND_ADDITIONAL_CONTEXT"}, {"scriptFile": "", "description": "Enter any customisations to the target issue, e.g. hard-coding specific field values.", "scriptCompileCtxOptions": null, "inlineScript": "null", "name": "Additional issue actions", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.AbstractCloneIssue.CLONE_ISSUE_ADDITIONAL_CODE_CONTEXT"}], "description": "<PERSON>lones this issue to another issue, optionally another project and issue type, and creates a link.\n        ", "clazz": null, "name": "Clones an issue, and links"}]}, {"href": "http://localhost:8080/jira/secure/admin/workflows/ViewWorkflowTransition.jspa?workflowMode=live&workflowName=COZGAQ&workflowTransition=3&workflowStep=4", "name": "Reopen Issue", "configuredItems": [{"scripts": [{"scriptFile": "", "description": "Enter the condition for which this function will fire. <PERSON><PERSON> will evaluate to \"true\".\n                You can click one of the examples below, or see the wiki page for further examples.", "scriptCompileCtxOptions": null, "inlineScript": "issue.priority?.name == 'High'", "name": "Condition", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.JiraCommonCompileContexts.CONDITION_AND_ADDITIONAL_CONTEXT"}, {"scriptFile": "", "description": "Enter any customisations to the target issue, e.g. hard-coding specific field values.", "scriptCompileCtxOptions": null, "inlineScript": "null", "name": "Additional issue actions", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.AbstractCloneIssue.CLONE_ISSUE_ADDITIONAL_CODE_CONTEXT"}], "description": "<PERSON>lones this issue to another issue, optionally another project and issue type, and creates a link.\n        ", "clazz": null, "name": "Clones an issue, and links"}]}, {"href": "http://localhost:8080/jira/secure/admin/workflows/ViewWorkflowTransition.jspa?workflowMode=live&workflowName=COZGAQ&workflowTransition=4&workflowStep=1", "name": "Start Progress", "configuredItems": [{"scripts": [{"scriptFile": "", "description": "Enter the condition for which this function will fire. <PERSON><PERSON> will evaluate to \"true\".\n                You can click one of the examples below, or see the wiki page for further examples.", "scriptCompileCtxOptions": null, "inlineScript": "issue.priority?.name == 'High'", "name": "Condition", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.JiraCommonCompileContexts.CONDITION_AND_ADDITIONAL_CONTEXT"}, {"scriptFile": "", "description": "Enter any customisations to the target issue, e.g. hard-coding specific field values.", "scriptCompileCtxOptions": null, "inlineScript": "null", "name": "Additional issue actions", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.AbstractCloneIssue.CLONE_ISSUE_ADDITIONAL_CODE_CONTEXT"}], "description": "<PERSON>lones this issue to another issue, optionally another project and issue type, and creates a link.\n        ", "clazz": null, "name": "Clones an issue, and links"}]}, {"href": "http://localhost:8080/jira/secure/admin/workflows/ViewWorkflowTransition.jspa?workflowMode=live&workflowName=COZGAQ&workflowTransition=5&workflowStep=1", "name": "Resolve Issue", "configuredItems": [{"scripts": [{"scriptFile": "", "description": "Enter the condition for which this function will fire. <PERSON><PERSON> will evaluate to \"true\".\n                You can click one of the examples below, or see the wiki page for further examples.", "scriptCompileCtxOptions": null, "inlineScript": "issue.priority?.name == 'High'", "name": "Condition", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.JiraCommonCompileContexts.CONDITION_AND_ADDITIONAL_CONTEXT"}, {"scriptFile": "", "description": "Enter any customisations to the target issue, e.g. hard-coding specific field values.", "scriptCompileCtxOptions": null, "inlineScript": "null", "name": "Additional issue actions", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.AbstractCloneIssue.CLONE_ISSUE_ADDITIONAL_CODE_CONTEXT"}], "description": "<PERSON>lones this issue to another issue, optionally another project and issue type, and creates a link.\n        ", "clazz": null, "name": "Clones an issue, and links"}]}, {"href": "http://localhost:8080/jira/secure/admin/workflows/ViewWorkflowTransition.jspa?workflowMode=live&workflowName=COZGAQ&workflowTransition=301", "name": "Stop Progress", "configuredItems": [{"scripts": [{"scriptFile": "", "description": "Enter the condition for which this function will fire. <PERSON><PERSON> will evaluate to \"true\".\n                You can click one of the examples below, or see the wiki page for further examples.", "scriptCompileCtxOptions": null, "inlineScript": "issue.priority?.name == 'High'", "name": "Condition", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.JiraCommonCompileContexts.CONDITION_AND_ADDITIONAL_CONTEXT"}, {"scriptFile": "", "description": "Enter any customisations to the target issue, e.g. hard-coding specific field values.", "scriptCompileCtxOptions": null, "inlineScript": "null", "name": "Additional issue actions", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.AbstractCloneIssue.CLONE_ISSUE_ADDITIONAL_CODE_CONTEXT"}], "description": "<PERSON>lones this issue to another issue, optionally another project and issue type, and creates a link.\n        ", "clazz": null, "name": "Clones an issue, and links"}]}, {"href": "http://localhost:8080/jira/secure/admin/workflows/ViewWorkflowTransition.jspa?workflowMode=live&workflowName=COZGAQ&workflowTransition=701", "name": "Close Issue", "configuredItems": [{"scripts": [{"scriptFile": "", "description": "Enter the condition for which this function will fire. <PERSON><PERSON> will evaluate to \"true\".\n                You can click one of the examples below, or see the wiki page for further examples.", "scriptCompileCtxOptions": null, "inlineScript": "issue.priority?.name == 'High'", "name": "Condition", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.JiraCommonCompileContexts.CONDITION_AND_ADDITIONAL_CONTEXT"}, {"scriptFile": "", "description": "Enter any customisations to the target issue, e.g. hard-coding specific field values.", "scriptCompileCtxOptions": null, "inlineScript": "null", "name": "Additional issue actions", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.AbstractCloneIssue.CLONE_ISSUE_ADDITIONAL_CODE_CONTEXT"}], "description": "<PERSON>lones this issue to another issue, optionally another project and issue type, and creates a link.\n        ", "clazz": null, "name": "Clones an issue, and links"}]}], "draft": false, "name": "COZGAQ"}, {"active": true, "transitions": [{"href": "http://localhost:8080/jira/secure/admin/workflows/ViewWorkflowTransition.jspa?workflowMode=live&workflowName=NJZRXJ&workflowTransition=1", "name": "Create Issue", "configuredItems": [{"scripts": [{"scriptFile": "", "description": "Enter the condition for which this function will fire. <PERSON><PERSON> will evaluate to \"true\".\n                You can click one of the examples below, or see the wiki page for further examples.", "scriptCompileCtxOptions": null, "inlineScript": "issue.priority?.name == 'High'", "name": "Condition", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.JiraCommonCompileContexts.CONDITION_AND_ADDITIONAL_CONTEXT"}, {"scriptFile": "", "description": "Enter any customisations to the target issue, e.g. hard-coding specific field values.", "scriptCompileCtxOptions": null, "inlineScript": "null", "name": "Additional issue actions", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.AbstractCloneIssue.CLONE_ISSUE_ADDITIONAL_CODE_CONTEXT"}], "description": "<PERSON>lones this issue to another issue, optionally another project and issue type, and creates a link.\n        ", "clazz": null, "name": "Clones an issue, and links"}]}, {"href": "http://localhost:8080/jira/secure/admin/workflows/ViewWorkflowTransition.jspa?workflowMode=live&workflowName=NJZRXJ&workflowTransition=2&workflowStep=1", "name": "Close Issue", "configuredItems": [{"scripts": [{"scriptFile": "", "description": "Enter the condition for which this function will fire. <PERSON><PERSON> will evaluate to \"true\".\n                You can click one of the examples below, or see the wiki page for further examples.", "scriptCompileCtxOptions": null, "inlineScript": "issue.priority?.name == 'High'", "name": "Condition", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.JiraCommonCompileContexts.CONDITION_AND_ADDITIONAL_CONTEXT"}, {"scriptFile": "", "description": "Enter any customisations to the target issue, e.g. hard-coding specific field values.", "scriptCompileCtxOptions": null, "inlineScript": "null", "name": "Additional issue actions", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.AbstractCloneIssue.CLONE_ISSUE_ADDITIONAL_CODE_CONTEXT"}], "description": "<PERSON>lones this issue to another issue, optionally another project and issue type, and creates a link.\n        ", "clazz": null, "name": "Clones an issue, and links"}]}, {"href": "http://localhost:8080/jira/secure/admin/workflows/ViewWorkflowTransition.jspa?workflowMode=live&workflowName=NJZRXJ&workflowTransition=3&workflowStep=4", "name": "Reopen Issue", "configuredItems": [{"scripts": [{"scriptFile": "", "description": "Enter the condition for which this function will fire. <PERSON><PERSON> will evaluate to \"true\".\n                You can click one of the examples below, or see the wiki page for further examples.", "scriptCompileCtxOptions": null, "inlineScript": "issue.priority?.name == 'High'", "name": "Condition", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.JiraCommonCompileContexts.CONDITION_AND_ADDITIONAL_CONTEXT"}, {"scriptFile": "", "description": "Enter any customisations to the target issue, e.g. hard-coding specific field values.", "scriptCompileCtxOptions": null, "inlineScript": "null", "name": "Additional issue actions", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.AbstractCloneIssue.CLONE_ISSUE_ADDITIONAL_CODE_CONTEXT"}], "description": "<PERSON>lones this issue to another issue, optionally another project and issue type, and creates a link.\n        ", "clazz": null, "name": "Clones an issue, and links"}]}, {"href": "http://localhost:8080/jira/secure/admin/workflows/ViewWorkflowTransition.jspa?workflowMode=live&workflowName=NJZRXJ&workflowTransition=4&workflowStep=1", "name": "Start Progress", "configuredItems": [{"scripts": [{"scriptFile": "", "description": "Enter the condition for which this function will fire. <PERSON><PERSON> will evaluate to \"true\".\n                You can click one of the examples below, or see the wiki page for further examples.", "scriptCompileCtxOptions": null, "inlineScript": "issue.priority?.name == 'High'", "name": "Condition", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.JiraCommonCompileContexts.CONDITION_AND_ADDITIONAL_CONTEXT"}, {"scriptFile": "", "description": "Enter any customisations to the target issue, e.g. hard-coding specific field values.", "scriptCompileCtxOptions": null, "inlineScript": "null", "name": "Additional issue actions", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.AbstractCloneIssue.CLONE_ISSUE_ADDITIONAL_CODE_CONTEXT"}], "description": "<PERSON>lones this issue to another issue, optionally another project and issue type, and creates a link.\n        ", "clazz": null, "name": "Clones an issue, and links"}]}, {"href": "http://localhost:8080/jira/secure/admin/workflows/ViewWorkflowTransition.jspa?workflowMode=live&workflowName=NJZRXJ&workflowTransition=5&workflowStep=1", "name": "Resolve Issue", "configuredItems": [{"scripts": [{"scriptFile": "", "description": "Enter the condition for which this function will fire. <PERSON><PERSON> will evaluate to \"true\".\n                You can click one of the examples below, or see the wiki page for further examples.", "scriptCompileCtxOptions": null, "inlineScript": "issue.priority?.name == 'High'", "name": "Condition", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.JiraCommonCompileContexts.CONDITION_AND_ADDITIONAL_CONTEXT"}, {"scriptFile": "", "description": "Enter any customisations to the target issue, e.g. hard-coding specific field values.", "scriptCompileCtxOptions": null, "inlineScript": "null", "name": "Additional issue actions", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.AbstractCloneIssue.CLONE_ISSUE_ADDITIONAL_CODE_CONTEXT"}], "description": "<PERSON>lones this issue to another issue, optionally another project and issue type, and creates a link.\n        ", "clazz": null, "name": "Clones an issue, and links"}]}, {"href": "http://localhost:8080/jira/secure/admin/workflows/ViewWorkflowTransition.jspa?workflowMode=live&workflowName=NJZRXJ&workflowTransition=301", "name": "Stop Progress", "configuredItems": [{"scripts": [{"scriptFile": "", "description": "Enter the condition for which this function will fire. <PERSON><PERSON> will evaluate to \"true\".\n                You can click one of the examples below, or see the wiki page for further examples.", "scriptCompileCtxOptions": null, "inlineScript": "issue.priority?.name == 'High'", "name": "Condition", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.JiraCommonCompileContexts.CONDITION_AND_ADDITIONAL_CONTEXT"}, {"scriptFile": "", "description": "Enter any customisations to the target issue, e.g. hard-coding specific field values.", "scriptCompileCtxOptions": null, "inlineScript": "null", "name": "Additional issue actions", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.AbstractCloneIssue.CLONE_ISSUE_ADDITIONAL_CODE_CONTEXT"}], "description": "<PERSON>lones this issue to another issue, optionally another project and issue type, and creates a link.\n        ", "clazz": null, "name": "Clones an issue, and links"}]}, {"href": "http://localhost:8080/jira/secure/admin/workflows/ViewWorkflowTransition.jspa?workflowMode=live&workflowName=NJZRXJ&workflowTransition=701", "name": "Close Issue", "configuredItems": [{"scripts": [{"scriptFile": "", "description": "Enter the condition for which this function will fire. <PERSON><PERSON> will evaluate to \"true\".\n                You can click one of the examples below, or see the wiki page for further examples.", "scriptCompileCtxOptions": null, "inlineScript": "issue.priority?.name == 'High'", "name": "Condition", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.JiraCommonCompileContexts.CONDITION_AND_ADDITIONAL_CONTEXT"}, {"scriptFile": "", "description": "Enter any customisations to the target issue, e.g. hard-coding specific field values.", "scriptCompileCtxOptions": null, "inlineScript": "null", "name": "Additional issue actions", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.AbstractCloneIssue.CLONE_ISSUE_ADDITIONAL_CODE_CONTEXT"}], "description": "<PERSON>lones this issue to another issue, optionally another project and issue type, and creates a link.\n        ", "clazz": null, "name": "Clones an issue, and links"}]}], "draft": false, "name": "NJZRXJ"}, {"active": true, "transitions": [{"href": "http://localhost:8080/jira/secure/admin/workflows/ViewWorkflowTransition.jspa?workflowMode=live&workflowName=PEIKTS&workflowTransition=1", "name": "Create Issue", "configuredItems": [{"scripts": [{"scriptFile": "", "description": "Enter the condition for which this function will fire. <PERSON><PERSON> will evaluate to \"true\".\n                You can click one of the examples below, or see the wiki page for further examples.", "scriptCompileCtxOptions": null, "inlineScript": "issue.priority?.name == 'High'", "name": "Condition", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.JiraCommonCompileContexts.CONDITION_AND_ADDITIONAL_CONTEXT"}, {"scriptFile": "", "description": "Enter any customisations to the target issue, e.g. hard-coding specific field values.", "scriptCompileCtxOptions": null, "inlineScript": "null", "name": "Additional issue actions", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.AbstractCloneIssue.CLONE_ISSUE_ADDITIONAL_CODE_CONTEXT"}], "description": "<PERSON>lones this issue to another issue, optionally another project and issue type, and creates a link.\n        ", "clazz": null, "name": "Clones an issue, and links"}]}, {"href": "http://localhost:8080/jira/secure/admin/workflows/ViewWorkflowTransition.jspa?workflowMode=live&workflowName=PEIKTS&workflowTransition=2&workflowStep=1", "name": "Close Issue", "configuredItems": [{"scripts": [{"scriptFile": "", "description": "Enter the condition for which this function will fire. <PERSON><PERSON> will evaluate to \"true\".\n                You can click one of the examples below, or see the wiki page for further examples.", "scriptCompileCtxOptions": null, "inlineScript": "issue.priority?.name == 'High'", "name": "Condition", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.JiraCommonCompileContexts.CONDITION_AND_ADDITIONAL_CONTEXT"}, {"scriptFile": "", "description": "Enter any customisations to the target issue, e.g. hard-coding specific field values.", "scriptCompileCtxOptions": null, "inlineScript": "null", "name": "Additional issue actions", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.AbstractCloneIssue.CLONE_ISSUE_ADDITIONAL_CODE_CONTEXT"}], "description": "<PERSON>lones this issue to another issue, optionally another project and issue type, and creates a link.\n        ", "clazz": null, "name": "Clones an issue, and links"}]}, {"href": "http://localhost:8080/jira/secure/admin/workflows/ViewWorkflowTransition.jspa?workflowMode=live&workflowName=PEIKTS&workflowTransition=3&workflowStep=4", "name": "Reopen Issue", "configuredItems": [{"scripts": [{"scriptFile": "", "description": "Enter the condition for which this function will fire. <PERSON><PERSON> will evaluate to \"true\".\n                You can click one of the examples below, or see the wiki page for further examples.", "scriptCompileCtxOptions": null, "inlineScript": "issue.priority?.name == 'High'", "name": "Condition", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.JiraCommonCompileContexts.CONDITION_AND_ADDITIONAL_CONTEXT"}, {"scriptFile": "", "description": "Enter any customisations to the target issue, e.g. hard-coding specific field values.", "scriptCompileCtxOptions": null, "inlineScript": "null", "name": "Additional issue actions", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.AbstractCloneIssue.CLONE_ISSUE_ADDITIONAL_CODE_CONTEXT"}], "description": "<PERSON>lones this issue to another issue, optionally another project and issue type, and creates a link.\n        ", "clazz": null, "name": "Clones an issue, and links"}]}, {"href": "http://localhost:8080/jira/secure/admin/workflows/ViewWorkflowTransition.jspa?workflowMode=live&workflowName=PEIKTS&workflowTransition=4&workflowStep=1", "name": "Start Progress", "configuredItems": [{"scripts": [{"scriptFile": "", "description": "Enter the condition for which this function will fire. <PERSON><PERSON> will evaluate to \"true\".\n                You can click one of the examples below, or see the wiki page for further examples.", "scriptCompileCtxOptions": null, "inlineScript": "issue.priority?.name == 'High'", "name": "Condition", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.JiraCommonCompileContexts.CONDITION_AND_ADDITIONAL_CONTEXT"}, {"scriptFile": "", "description": "Enter any customisations to the target issue, e.g. hard-coding specific field values.", "scriptCompileCtxOptions": null, "inlineScript": "null", "name": "Additional issue actions", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.AbstractCloneIssue.CLONE_ISSUE_ADDITIONAL_CODE_CONTEXT"}], "description": "<PERSON>lones this issue to another issue, optionally another project and issue type, and creates a link.\n        ", "clazz": null, "name": "Clones an issue, and links"}]}, {"href": "http://localhost:8080/jira/secure/admin/workflows/ViewWorkflowTransition.jspa?workflowMode=live&workflowName=PEIKTS&workflowTransition=5&workflowStep=1", "name": "Resolve Issue", "configuredItems": [{"scripts": [{"scriptFile": "", "description": "Enter the condition for which this function will fire. <PERSON><PERSON> will evaluate to \"true\".\n                You can click one of the examples below, or see the wiki page for further examples.", "scriptCompileCtxOptions": null, "inlineScript": "issue.priority?.name == 'High'", "name": "Condition", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.JiraCommonCompileContexts.CONDITION_AND_ADDITIONAL_CONTEXT"}, {"scriptFile": "", "description": "Enter any customisations to the target issue, e.g. hard-coding specific field values.", "scriptCompileCtxOptions": null, "inlineScript": "null", "name": "Additional issue actions", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.AbstractCloneIssue.CLONE_ISSUE_ADDITIONAL_CODE_CONTEXT"}], "description": "<PERSON>lones this issue to another issue, optionally another project and issue type, and creates a link.\n        ", "clazz": null, "name": "Clones an issue, and links"}]}, {"href": "http://localhost:8080/jira/secure/admin/workflows/ViewWorkflowTransition.jspa?workflowMode=live&workflowName=PEIKTS&workflowTransition=301", "name": "Stop Progress", "configuredItems": [{"scripts": [{"scriptFile": "", "description": "Enter the condition for which this function will fire. <PERSON><PERSON> will evaluate to \"true\".\n                You can click one of the examples below, or see the wiki page for further examples.", "scriptCompileCtxOptions": null, "inlineScript": "issue.priority?.name == 'High'", "name": "Condition", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.JiraCommonCompileContexts.CONDITION_AND_ADDITIONAL_CONTEXT"}, {"scriptFile": "", "description": "Enter any customisations to the target issue, e.g. hard-coding specific field values.", "scriptCompileCtxOptions": null, "inlineScript": "null", "name": "Additional issue actions", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.AbstractCloneIssue.CLONE_ISSUE_ADDITIONAL_CODE_CONTEXT"}], "description": "<PERSON>lones this issue to another issue, optionally another project and issue type, and creates a link.\n        ", "clazz": null, "name": "Clones an issue, and links"}]}, {"href": "http://localhost:8080/jira/secure/admin/workflows/ViewWorkflowTransition.jspa?workflowMode=live&workflowName=PEIKTS&workflowTransition=701", "name": "Close Issue", "configuredItems": [{"scripts": [{"scriptFile": "", "description": "Enter the condition for which this function will fire. <PERSON><PERSON> will evaluate to \"true\".\n                You can click one of the examples below, or see the wiki page for further examples.", "scriptCompileCtxOptions": null, "inlineScript": "issue.priority?.name == 'High'", "name": "Condition", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.JiraCommonCompileContexts.CONDITION_AND_ADDITIONAL_CONTEXT"}, {"scriptFile": "", "description": "Enter any customisations to the target issue, e.g. hard-coding specific field values.", "scriptCompileCtxOptions": null, "inlineScript": "null", "name": "Additional issue actions", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.AbstractCloneIssue.CLONE_ISSUE_ADDITIONAL_CODE_CONTEXT"}], "description": "<PERSON>lones this issue to another issue, optionally another project and issue type, and creates a link.\n        ", "clazz": null, "name": "Clones an issue, and links"}]}], "draft": false, "name": "PEIKTS"}, {"active": true, "transitions": [{"href": "http://localhost:8080/jira/secure/admin/workflows/ViewWorkflowTransition.jspa?workflowMode=live&workflowName=QKCDLT&workflowTransition=1", "name": "Create Issue", "configuredItems": [{"scripts": [{"scriptFile": "", "description": "Enter the condition for which this function will fire. <PERSON><PERSON> will evaluate to \"true\".\n                You can click one of the examples below, or see the wiki page for further examples.", "scriptCompileCtxOptions": null, "inlineScript": "issue.priority?.name == 'High'", "name": "Condition", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.JiraCommonCompileContexts.CONDITION_AND_ADDITIONAL_CONTEXT"}, {"scriptFile": "", "description": "Enter any customisations to the target issue, e.g. hard-coding specific field values.", "scriptCompileCtxOptions": null, "inlineScript": "null", "name": "Additional issue actions", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.AbstractCloneIssue.CLONE_ISSUE_ADDITIONAL_CODE_CONTEXT"}], "description": "<PERSON>lones this issue to another issue, optionally another project and issue type, and creates a link.\n        ", "clazz": null, "name": "Clones an issue, and links"}]}, {"href": "http://localhost:8080/jira/secure/admin/workflows/ViewWorkflowTransition.jspa?workflowMode=live&workflowName=QKCDLT&workflowTransition=2&workflowStep=1", "name": "Close Issue", "configuredItems": [{"scripts": [{"scriptFile": "", "description": "Enter the condition for which this function will fire. <PERSON><PERSON> will evaluate to \"true\".\n                You can click one of the examples below, or see the wiki page for further examples.", "scriptCompileCtxOptions": null, "inlineScript": "issue.priority?.name == 'High'", "name": "Condition", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.JiraCommonCompileContexts.CONDITION_AND_ADDITIONAL_CONTEXT"}, {"scriptFile": "", "description": "Enter any customisations to the target issue, e.g. hard-coding specific field values.", "scriptCompileCtxOptions": null, "inlineScript": "null", "name": "Additional issue actions", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.AbstractCloneIssue.CLONE_ISSUE_ADDITIONAL_CODE_CONTEXT"}], "description": "<PERSON>lones this issue to another issue, optionally another project and issue type, and creates a link.\n        ", "clazz": null, "name": "Clones an issue, and links"}]}, {"href": "http://localhost:8080/jira/secure/admin/workflows/ViewWorkflowTransition.jspa?workflowMode=live&workflowName=QKCDLT&workflowTransition=3&workflowStep=4", "name": "Reopen Issue", "configuredItems": [{"scripts": [{"scriptFile": "", "description": "Enter the condition for which this function will fire. <PERSON><PERSON> will evaluate to \"true\".\n                You can click one of the examples below, or see the wiki page for further examples.", "scriptCompileCtxOptions": null, "inlineScript": "issue.priority?.name == 'High'", "name": "Condition", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.JiraCommonCompileContexts.CONDITION_AND_ADDITIONAL_CONTEXT"}, {"scriptFile": "", "description": "Enter any customisations to the target issue, e.g. hard-coding specific field values.", "scriptCompileCtxOptions": null, "inlineScript": "null", "name": "Additional issue actions", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.AbstractCloneIssue.CLONE_ISSUE_ADDITIONAL_CODE_CONTEXT"}], "description": "<PERSON>lones this issue to another issue, optionally another project and issue type, and creates a link.\n        ", "clazz": null, "name": "Clones an issue, and links"}]}, {"href": "http://localhost:8080/jira/secure/admin/workflows/ViewWorkflowTransition.jspa?workflowMode=live&workflowName=QKCDLT&workflowTransition=4&workflowStep=1", "name": "Start Progress", "configuredItems": [{"scripts": [{"scriptFile": "", "description": "Enter the condition for which this function will fire. <PERSON><PERSON> will evaluate to \"true\".\n                You can click one of the examples below, or see the wiki page for further examples.", "scriptCompileCtxOptions": null, "inlineScript": "issue.priority?.name == 'High'", "name": "Condition", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.JiraCommonCompileContexts.CONDITION_AND_ADDITIONAL_CONTEXT"}, {"scriptFile": "", "description": "Enter any customisations to the target issue, e.g. hard-coding specific field values.", "scriptCompileCtxOptions": null, "inlineScript": "null", "name": "Additional issue actions", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.AbstractCloneIssue.CLONE_ISSUE_ADDITIONAL_CODE_CONTEXT"}], "description": "<PERSON>lones this issue to another issue, optionally another project and issue type, and creates a link.\n        ", "clazz": null, "name": "Clones an issue, and links"}]}, {"href": "http://localhost:8080/jira/secure/admin/workflows/ViewWorkflowTransition.jspa?workflowMode=live&workflowName=QKCDLT&workflowTransition=5&workflowStep=1", "name": "Resolve Issue", "configuredItems": [{"scripts": [{"scriptFile": "", "description": "Enter the condition for which this function will fire. <PERSON><PERSON> will evaluate to \"true\".\n                You can click one of the examples below, or see the wiki page for further examples.", "scriptCompileCtxOptions": null, "inlineScript": "issue.priority?.name == 'High'", "name": "Condition", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.JiraCommonCompileContexts.CONDITION_AND_ADDITIONAL_CONTEXT"}, {"scriptFile": "", "description": "Enter any customisations to the target issue, e.g. hard-coding specific field values.", "scriptCompileCtxOptions": null, "inlineScript": "null", "name": "Additional issue actions", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.AbstractCloneIssue.CLONE_ISSUE_ADDITIONAL_CODE_CONTEXT"}], "description": "<PERSON>lones this issue to another issue, optionally another project and issue type, and creates a link.\n        ", "clazz": null, "name": "Clones an issue, and links"}]}, {"href": "http://localhost:8080/jira/secure/admin/workflows/ViewWorkflowTransition.jspa?workflowMode=live&workflowName=QKCDLT&workflowTransition=301", "name": "Stop Progress", "configuredItems": [{"scripts": [{"scriptFile": "", "description": "Enter the condition for which this function will fire. <PERSON><PERSON> will evaluate to \"true\".\n                You can click one of the examples below, or see the wiki page for further examples.", "scriptCompileCtxOptions": null, "inlineScript": "issue.priority?.name == 'High'", "name": "Condition", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.JiraCommonCompileContexts.CONDITION_AND_ADDITIONAL_CONTEXT"}, {"scriptFile": "", "description": "Enter any customisations to the target issue, e.g. hard-coding specific field values.", "scriptCompileCtxOptions": null, "inlineScript": "null", "name": "Additional issue actions", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.AbstractCloneIssue.CLONE_ISSUE_ADDITIONAL_CODE_CONTEXT"}], "description": "<PERSON>lones this issue to another issue, optionally another project and issue type, and creates a link.\n        ", "clazz": null, "name": "Clones an issue, and links"}]}, {"href": "http://localhost:8080/jira/secure/admin/workflows/ViewWorkflowTransition.jspa?workflowMode=live&workflowName=QKCDLT&workflowTransition=701", "name": "Close Issue", "configuredItems": [{"scripts": [{"scriptFile": "", "description": "Enter the condition for which this function will fire. <PERSON><PERSON> will evaluate to \"true\".\n                You can click one of the examples below, or see the wiki page for further examples.", "scriptCompileCtxOptions": null, "inlineScript": "issue.priority?.name == 'High'", "name": "Condition", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.JiraCommonCompileContexts.CONDITION_AND_ADDITIONAL_CONTEXT"}, {"scriptFile": "", "description": "Enter any customisations to the target issue, e.g. hard-coding specific field values.", "scriptCompileCtxOptions": null, "inlineScript": "null", "name": "Additional issue actions", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.AbstractCloneIssue.CLONE_ISSUE_ADDITIONAL_CODE_CONTEXT"}], "description": "<PERSON>lones this issue to another issue, optionally another project and issue type, and creates a link.\n        ", "clazz": null, "name": "Clones an issue, and links"}]}], "draft": false, "name": "QKCDLT"}, {"active": true, "transitions": [{"href": "http://localhost:8080/jira/secure/admin/workflows/ViewWorkflowTransition.jspa?workflowMode=live&workflowName=QPSQQD&workflowTransition=1", "name": "Create Issue", "configuredItems": [{"scripts": [{"scriptFile": "", "description": "Enter the condition for which this function will fire. <PERSON><PERSON> will evaluate to \"true\".\n                You can click one of the examples below, or see the wiki page for further examples.", "scriptCompileCtxOptions": null, "inlineScript": "issue.priority?.name == 'High'", "name": "Condition", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.JiraCommonCompileContexts.CONDITION_AND_ADDITIONAL_CONTEXT"}, {"scriptFile": "", "description": "Enter any customisations to the target issue, e.g. hard-coding specific field values.", "scriptCompileCtxOptions": null, "inlineScript": "null", "name": "Additional issue actions", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.AbstractCloneIssue.CLONE_ISSUE_ADDITIONAL_CODE_CONTEXT"}], "description": "<PERSON>lones this issue to another issue, optionally another project and issue type, and creates a link.\n        ", "clazz": null, "name": "Clones an issue, and links"}]}, {"href": "http://localhost:8080/jira/secure/admin/workflows/ViewWorkflowTransition.jspa?workflowMode=live&workflowName=QPSQQD&workflowTransition=2&workflowStep=1", "name": "Close Issue", "configuredItems": [{"scripts": [{"scriptFile": "", "description": "Enter the condition for which this function will fire. <PERSON><PERSON> will evaluate to \"true\".\n                You can click one of the examples below, or see the wiki page for further examples.", "scriptCompileCtxOptions": null, "inlineScript": "issue.priority?.name == 'High'", "name": "Condition", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.JiraCommonCompileContexts.CONDITION_AND_ADDITIONAL_CONTEXT"}, {"scriptFile": "", "description": "Enter any customisations to the target issue, e.g. hard-coding specific field values.", "scriptCompileCtxOptions": null, "inlineScript": "null", "name": "Additional issue actions", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.AbstractCloneIssue.CLONE_ISSUE_ADDITIONAL_CODE_CONTEXT"}], "description": "<PERSON>lones this issue to another issue, optionally another project and issue type, and creates a link.\n        ", "clazz": null, "name": "Clones an issue, and links"}]}, {"href": "http://localhost:8080/jira/secure/admin/workflows/ViewWorkflowTransition.jspa?workflowMode=live&workflowName=QPSQQD&workflowTransition=3&workflowStep=4", "name": "Reopen Issue", "configuredItems": [{"scripts": [{"scriptFile": "", "description": "Enter the condition for which this function will fire. <PERSON><PERSON> will evaluate to \"true\".\n                You can click one of the examples below, or see the wiki page for further examples.", "scriptCompileCtxOptions": null, "inlineScript": "issue.priority?.name == 'High'", "name": "Condition", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.JiraCommonCompileContexts.CONDITION_AND_ADDITIONAL_CONTEXT"}, {"scriptFile": "", "description": "Enter any customisations to the target issue, e.g. hard-coding specific field values.", "scriptCompileCtxOptions": null, "inlineScript": "null", "name": "Additional issue actions", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.AbstractCloneIssue.CLONE_ISSUE_ADDITIONAL_CODE_CONTEXT"}], "description": "<PERSON>lones this issue to another issue, optionally another project and issue type, and creates a link.\n        ", "clazz": null, "name": "Clones an issue, and links"}]}, {"href": "http://localhost:8080/jira/secure/admin/workflows/ViewWorkflowTransition.jspa?workflowMode=live&workflowName=QPSQQD&workflowTransition=4&workflowStep=1", "name": "Start Progress", "configuredItems": [{"scripts": [{"scriptFile": "", "description": "Enter the condition for which this function will fire. <PERSON><PERSON> will evaluate to \"true\".\n                You can click one of the examples below, or see the wiki page for further examples.", "scriptCompileCtxOptions": null, "inlineScript": "issue.priority?.name == 'High'", "name": "Condition", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.JiraCommonCompileContexts.CONDITION_AND_ADDITIONAL_CONTEXT"}, {"scriptFile": "", "description": "Enter any customisations to the target issue, e.g. hard-coding specific field values.", "scriptCompileCtxOptions": null, "inlineScript": "null", "name": "Additional issue actions", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.AbstractCloneIssue.CLONE_ISSUE_ADDITIONAL_CODE_CONTEXT"}], "description": "<PERSON>lones this issue to another issue, optionally another project and issue type, and creates a link.\n        ", "clazz": null, "name": "Clones an issue, and links"}]}, {"href": "http://localhost:8080/jira/secure/admin/workflows/ViewWorkflowTransition.jspa?workflowMode=live&workflowName=QPSQQD&workflowTransition=5&workflowStep=1", "name": "Resolve Issue", "configuredItems": [{"scripts": [{"scriptFile": "", "description": "Enter the condition for which this function will fire. <PERSON><PERSON> will evaluate to \"true\".\n                You can click one of the examples below, or see the wiki page for further examples.", "scriptCompileCtxOptions": null, "inlineScript": "issue.priority?.name == 'High'", "name": "Condition", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.JiraCommonCompileContexts.CONDITION_AND_ADDITIONAL_CONTEXT"}, {"scriptFile": "", "description": "Enter any customisations to the target issue, e.g. hard-coding specific field values.", "scriptCompileCtxOptions": null, "inlineScript": "null", "name": "Additional issue actions", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.AbstractCloneIssue.CLONE_ISSUE_ADDITIONAL_CODE_CONTEXT"}], "description": "<PERSON>lones this issue to another issue, optionally another project and issue type, and creates a link.\n        ", "clazz": null, "name": "Clones an issue, and links"}]}, {"href": "http://localhost:8080/jira/secure/admin/workflows/ViewWorkflowTransition.jspa?workflowMode=live&workflowName=QPSQQD&workflowTransition=301", "name": "Stop Progress", "configuredItems": [{"scripts": [{"scriptFile": "", "description": "Enter the condition for which this function will fire. <PERSON><PERSON> will evaluate to \"true\".\n                You can click one of the examples below, or see the wiki page for further examples.", "scriptCompileCtxOptions": null, "inlineScript": "issue.priority?.name == 'High'", "name": "Condition", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.JiraCommonCompileContexts.CONDITION_AND_ADDITIONAL_CONTEXT"}, {"scriptFile": "", "description": "Enter any customisations to the target issue, e.g. hard-coding specific field values.", "scriptCompileCtxOptions": null, "inlineScript": "null", "name": "Additional issue actions", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.AbstractCloneIssue.CLONE_ISSUE_ADDITIONAL_CODE_CONTEXT"}], "description": "<PERSON>lones this issue to another issue, optionally another project and issue type, and creates a link.\n        ", "clazz": null, "name": "Clones an issue, and links"}]}, {"href": "http://localhost:8080/jira/secure/admin/workflows/ViewWorkflowTransition.jspa?workflowMode=live&workflowName=QPSQQD&workflowTransition=701", "name": "Close Issue", "configuredItems": [{"scripts": [{"scriptFile": "", "description": "Enter the condition for which this function will fire. <PERSON><PERSON> will evaluate to \"true\".\n                You can click one of the examples below, or see the wiki page for further examples.", "scriptCompileCtxOptions": null, "inlineScript": "issue.priority?.name == 'High'", "name": "Condition", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.JiraCommonCompileContexts.CONDITION_AND_ADDITIONAL_CONTEXT"}, {"scriptFile": "", "description": "Enter any customisations to the target issue, e.g. hard-coding specific field values.", "scriptCompileCtxOptions": null, "inlineScript": "null", "name": "Additional issue actions", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.AbstractCloneIssue.CLONE_ISSUE_ADDITIONAL_CODE_CONTEXT"}], "description": "<PERSON>lones this issue to another issue, optionally another project and issue type, and creates a link.\n        ", "clazz": null, "name": "Clones an issue, and links"}]}], "draft": false, "name": "QPSQQD"}, {"active": true, "transitions": [{"href": "http://localhost:8080/jira/secure/admin/workflows/ViewWorkflowTransition.jspa?workflowMode=live&workflowName=QSBLRT&workflowTransition=1", "name": "Create Issue", "configuredItems": [{"scripts": [{"scriptFile": "", "description": "Enter the condition for which this function will fire. <PERSON><PERSON> will evaluate to \"true\".\n                You can click one of the examples below, or see the wiki page for further examples.", "scriptCompileCtxOptions": null, "inlineScript": "issue.priority?.name == 'High'", "name": "Condition", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.JiraCommonCompileContexts.CONDITION_AND_ADDITIONAL_CONTEXT"}, {"scriptFile": "", "description": "Enter any customisations to the target issue, e.g. hard-coding specific field values.", "scriptCompileCtxOptions": null, "inlineScript": "null", "name": "Additional issue actions", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.AbstractCloneIssue.CLONE_ISSUE_ADDITIONAL_CODE_CONTEXT"}], "description": "<PERSON>lones this issue to another issue, optionally another project and issue type, and creates a link.\n        ", "clazz": null, "name": "Clones an issue, and links"}]}, {"href": "http://localhost:8080/jira/secure/admin/workflows/ViewWorkflowTransition.jspa?workflowMode=live&workflowName=QSBLRT&workflowTransition=2&workflowStep=1", "name": "Close Issue", "configuredItems": [{"scripts": [{"scriptFile": "", "description": "Enter the condition for which this function will fire. <PERSON><PERSON> will evaluate to \"true\".\n                You can click one of the examples below, or see the wiki page for further examples.", "scriptCompileCtxOptions": null, "inlineScript": "issue.priority?.name == 'High'", "name": "Condition", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.JiraCommonCompileContexts.CONDITION_AND_ADDITIONAL_CONTEXT"}, {"scriptFile": "", "description": "Enter any customisations to the target issue, e.g. hard-coding specific field values.", "scriptCompileCtxOptions": null, "inlineScript": "null", "name": "Additional issue actions", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.AbstractCloneIssue.CLONE_ISSUE_ADDITIONAL_CODE_CONTEXT"}], "description": "<PERSON>lones this issue to another issue, optionally another project and issue type, and creates a link.\n        ", "clazz": null, "name": "Clones an issue, and links"}]}, {"href": "http://localhost:8080/jira/secure/admin/workflows/ViewWorkflowTransition.jspa?workflowMode=live&workflowName=QSBLRT&workflowTransition=3&workflowStep=4", "name": "Reopen Issue", "configuredItems": [{"scripts": [{"scriptFile": "", "description": "Enter the condition for which this function will fire. <PERSON><PERSON> will evaluate to \"true\".\n                You can click one of the examples below, or see the wiki page for further examples.", "scriptCompileCtxOptions": null, "inlineScript": "issue.priority?.name == 'High'", "name": "Condition", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.JiraCommonCompileContexts.CONDITION_AND_ADDITIONAL_CONTEXT"}, {"scriptFile": "", "description": "Enter any customisations to the target issue, e.g. hard-coding specific field values.", "scriptCompileCtxOptions": null, "inlineScript": "null", "name": "Additional issue actions", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.AbstractCloneIssue.CLONE_ISSUE_ADDITIONAL_CODE_CONTEXT"}], "description": "<PERSON>lones this issue to another issue, optionally another project and issue type, and creates a link.\n        ", "clazz": null, "name": "Clones an issue, and links"}]}, {"href": "http://localhost:8080/jira/secure/admin/workflows/ViewWorkflowTransition.jspa?workflowMode=live&workflowName=QSBLRT&workflowTransition=4&workflowStep=1", "name": "Start Progress", "configuredItems": [{"scripts": [{"scriptFile": "", "description": "Enter the condition for which this function will fire. <PERSON><PERSON> will evaluate to \"true\".\n                You can click one of the examples below, or see the wiki page for further examples.", "scriptCompileCtxOptions": null, "inlineScript": "issue.priority?.name == 'High'", "name": "Condition", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.JiraCommonCompileContexts.CONDITION_AND_ADDITIONAL_CONTEXT"}, {"scriptFile": "", "description": "Enter any customisations to the target issue, e.g. hard-coding specific field values.", "scriptCompileCtxOptions": null, "inlineScript": "null", "name": "Additional issue actions", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.AbstractCloneIssue.CLONE_ISSUE_ADDITIONAL_CODE_CONTEXT"}], "description": "<PERSON>lones this issue to another issue, optionally another project and issue type, and creates a link.\n        ", "clazz": null, "name": "Clones an issue, and links"}]}, {"href": "http://localhost:8080/jira/secure/admin/workflows/ViewWorkflowTransition.jspa?workflowMode=live&workflowName=QSBLRT&workflowTransition=5&workflowStep=1", "name": "Resolve Issue", "configuredItems": [{"scripts": [{"scriptFile": "", "description": "Enter the condition for which this function will fire. <PERSON><PERSON> will evaluate to \"true\".\n                You can click one of the examples below, or see the wiki page for further examples.", "scriptCompileCtxOptions": null, "inlineScript": "issue.priority?.name == 'High'", "name": "Condition", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.JiraCommonCompileContexts.CONDITION_AND_ADDITIONAL_CONTEXT"}, {"scriptFile": "", "description": "Enter any customisations to the target issue, e.g. hard-coding specific field values.", "scriptCompileCtxOptions": null, "inlineScript": "null", "name": "Additional issue actions", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.AbstractCloneIssue.CLONE_ISSUE_ADDITIONAL_CODE_CONTEXT"}], "description": "<PERSON>lones this issue to another issue, optionally another project and issue type, and creates a link.\n        ", "clazz": null, "name": "Clones an issue, and links"}]}, {"href": "http://localhost:8080/jira/secure/admin/workflows/ViewWorkflowTransition.jspa?workflowMode=live&workflowName=QSBLRT&workflowTransition=301", "name": "Stop Progress", "configuredItems": [{"scripts": [{"scriptFile": "", "description": "Enter the condition for which this function will fire. <PERSON><PERSON> will evaluate to \"true\".\n                You can click one of the examples below, or see the wiki page for further examples.", "scriptCompileCtxOptions": null, "inlineScript": "issue.priority?.name == 'High'", "name": "Condition", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.JiraCommonCompileContexts.CONDITION_AND_ADDITIONAL_CONTEXT"}, {"scriptFile": "", "description": "Enter any customisations to the target issue, e.g. hard-coding specific field values.", "scriptCompileCtxOptions": null, "inlineScript": "null", "name": "Additional issue actions", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.AbstractCloneIssue.CLONE_ISSUE_ADDITIONAL_CODE_CONTEXT"}], "description": "<PERSON>lones this issue to another issue, optionally another project and issue type, and creates a link.\n        ", "clazz": null, "name": "Clones an issue, and links"}]}, {"href": "http://localhost:8080/jira/secure/admin/workflows/ViewWorkflowTransition.jspa?workflowMode=live&workflowName=QSBLRT&workflowTransition=701", "name": "Close Issue", "configuredItems": [{"scripts": [{"scriptFile": "", "description": "Enter the condition for which this function will fire. <PERSON><PERSON> will evaluate to \"true\".\n                You can click one of the examples below, or see the wiki page for further examples.", "scriptCompileCtxOptions": null, "inlineScript": "issue.priority?.name == 'High'", "name": "Condition", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.JiraCommonCompileContexts.CONDITION_AND_ADDITIONAL_CONTEXT"}, {"scriptFile": "", "description": "Enter any customisations to the target issue, e.g. hard-coding specific field values.", "scriptCompileCtxOptions": null, "inlineScript": "null", "name": "Additional issue actions", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.AbstractCloneIssue.CLONE_ISSUE_ADDITIONAL_CODE_CONTEXT"}], "description": "<PERSON>lones this issue to another issue, optionally another project and issue type, and creates a link.\n        ", "clazz": null, "name": "Clones an issue, and links"}]}], "draft": false, "name": "QSBLRT"}, {"active": true, "transitions": [{"href": "http://localhost:8080/jira/secure/admin/workflows/ViewWorkflowTransition.jspa?workflowMode=live&workflowName=SAZCVX&workflowTransition=1", "name": "Create Issue", "configuredItems": [{"scripts": [{"scriptFile": "", "description": "Enter the condition for which this function will fire. <PERSON><PERSON> will evaluate to \"true\".\n                You can click one of the examples below, or see the wiki page for further examples.", "scriptCompileCtxOptions": null, "inlineScript": "issue.priority?.name == 'High'", "name": "Condition", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.JiraCommonCompileContexts.CONDITION_AND_ADDITIONAL_CONTEXT"}, {"scriptFile": "", "description": "Enter any customisations to the target issue, e.g. hard-coding specific field values.", "scriptCompileCtxOptions": null, "inlineScript": "null", "name": "Additional issue actions", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.AbstractCloneIssue.CLONE_ISSUE_ADDITIONAL_CODE_CONTEXT"}], "description": "<PERSON>lones this issue to another issue, optionally another project and issue type, and creates a link.\n        ", "clazz": null, "name": "Clones an issue, and links"}]}, {"href": "http://localhost:8080/jira/secure/admin/workflows/ViewWorkflowTransition.jspa?workflowMode=live&workflowName=SAZCVX&workflowTransition=2&workflowStep=1", "name": "Close Issue", "configuredItems": [{"scripts": [{"scriptFile": "", "description": "Enter the condition for which this function will fire. <PERSON><PERSON> will evaluate to \"true\".\n                You can click one of the examples below, or see the wiki page for further examples.", "scriptCompileCtxOptions": null, "inlineScript": "issue.priority?.name == 'High'", "name": "Condition", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.JiraCommonCompileContexts.CONDITION_AND_ADDITIONAL_CONTEXT"}, {"scriptFile": "", "description": "Enter any customisations to the target issue, e.g. hard-coding specific field values.", "scriptCompileCtxOptions": null, "inlineScript": "null", "name": "Additional issue actions", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.AbstractCloneIssue.CLONE_ISSUE_ADDITIONAL_CODE_CONTEXT"}], "description": "<PERSON>lones this issue to another issue, optionally another project and issue type, and creates a link.\n        ", "clazz": null, "name": "Clones an issue, and links"}]}, {"href": "http://localhost:8080/jira/secure/admin/workflows/ViewWorkflowTransition.jspa?workflowMode=live&workflowName=SAZCVX&workflowTransition=3&workflowStep=4", "name": "Reopen Issue", "configuredItems": [{"scripts": [{"scriptFile": "", "description": "Enter the condition for which this function will fire. <PERSON><PERSON> will evaluate to \"true\".\n                You can click one of the examples below, or see the wiki page for further examples.", "scriptCompileCtxOptions": null, "inlineScript": "issue.priority?.name == 'High'", "name": "Condition", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.JiraCommonCompileContexts.CONDITION_AND_ADDITIONAL_CONTEXT"}, {"scriptFile": "", "description": "Enter any customisations to the target issue, e.g. hard-coding specific field values.", "scriptCompileCtxOptions": null, "inlineScript": "null", "name": "Additional issue actions", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.AbstractCloneIssue.CLONE_ISSUE_ADDITIONAL_CODE_CONTEXT"}], "description": "<PERSON>lones this issue to another issue, optionally another project and issue type, and creates a link.\n        ", "clazz": null, "name": "Clones an issue, and links"}]}, {"href": "http://localhost:8080/jira/secure/admin/workflows/ViewWorkflowTransition.jspa?workflowMode=live&workflowName=SAZCVX&workflowTransition=4&workflowStep=1", "name": "Start Progress", "configuredItems": [{"scripts": [{"scriptFile": "", "description": "Enter the condition for which this function will fire. <PERSON><PERSON> will evaluate to \"true\".\n                You can click one of the examples below, or see the wiki page for further examples.", "scriptCompileCtxOptions": null, "inlineScript": "issue.priority?.name == 'High'", "name": "Condition", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.JiraCommonCompileContexts.CONDITION_AND_ADDITIONAL_CONTEXT"}, {"scriptFile": "", "description": "Enter any customisations to the target issue, e.g. hard-coding specific field values.", "scriptCompileCtxOptions": null, "inlineScript": "null", "name": "Additional issue actions", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.AbstractCloneIssue.CLONE_ISSUE_ADDITIONAL_CODE_CONTEXT"}], "description": "<PERSON>lones this issue to another issue, optionally another project and issue type, and creates a link.\n        ", "clazz": null, "name": "Clones an issue, and links"}]}, {"href": "http://localhost:8080/jira/secure/admin/workflows/ViewWorkflowTransition.jspa?workflowMode=live&workflowName=SAZCVX&workflowTransition=5&workflowStep=1", "name": "Resolve Issue", "configuredItems": [{"scripts": [{"scriptFile": "", "description": "Enter the condition for which this function will fire. <PERSON><PERSON> will evaluate to \"true\".\n                You can click one of the examples below, or see the wiki page for further examples.", "scriptCompileCtxOptions": null, "inlineScript": "issue.priority?.name == 'High'", "name": "Condition", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.JiraCommonCompileContexts.CONDITION_AND_ADDITIONAL_CONTEXT"}, {"scriptFile": "", "description": "Enter any customisations to the target issue, e.g. hard-coding specific field values.", "scriptCompileCtxOptions": null, "inlineScript": "null", "name": "Additional issue actions", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.AbstractCloneIssue.CLONE_ISSUE_ADDITIONAL_CODE_CONTEXT"}], "description": "<PERSON>lones this issue to another issue, optionally another project and issue type, and creates a link.\n        ", "clazz": null, "name": "Clones an issue, and links"}]}, {"href": "http://localhost:8080/jira/secure/admin/workflows/ViewWorkflowTransition.jspa?workflowMode=live&workflowName=SAZCVX&workflowTransition=301", "name": "Stop Progress", "configuredItems": [{"scripts": [{"scriptFile": "", "description": "Enter the condition for which this function will fire. <PERSON><PERSON> will evaluate to \"true\".\n                You can click one of the examples below, or see the wiki page for further examples.", "scriptCompileCtxOptions": null, "inlineScript": "issue.priority?.name == 'High'", "name": "Condition", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.JiraCommonCompileContexts.CONDITION_AND_ADDITIONAL_CONTEXT"}, {"scriptFile": "", "description": "Enter any customisations to the target issue, e.g. hard-coding specific field values.", "scriptCompileCtxOptions": null, "inlineScript": "null", "name": "Additional issue actions", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.AbstractCloneIssue.CLONE_ISSUE_ADDITIONAL_CODE_CONTEXT"}], "description": "<PERSON>lones this issue to another issue, optionally another project and issue type, and creates a link.\n        ", "clazz": null, "name": "Clones an issue, and links"}]}, {"href": "http://localhost:8080/jira/secure/admin/workflows/ViewWorkflowTransition.jspa?workflowMode=live&workflowName=SAZCVX&workflowTransition=701", "name": "Close Issue", "configuredItems": [{"scripts": [{"scriptFile": "", "description": "Enter the condition for which this function will fire. <PERSON><PERSON> will evaluate to \"true\".\n                You can click one of the examples below, or see the wiki page for further examples.", "scriptCompileCtxOptions": null, "inlineScript": "issue.priority?.name == 'High'", "name": "Condition", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.JiraCommonCompileContexts.CONDITION_AND_ADDITIONAL_CONTEXT"}, {"scriptFile": "", "description": "Enter any customisations to the target issue, e.g. hard-coding specific field values.", "scriptCompileCtxOptions": null, "inlineScript": "null", "name": "Additional issue actions", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.AbstractCloneIssue.CLONE_ISSUE_ADDITIONAL_CODE_CONTEXT"}], "description": "<PERSON>lones this issue to another issue, optionally another project and issue type, and creates a link.\n        ", "clazz": null, "name": "Clones an issue, and links"}]}], "draft": false, "name": "SAZCVX"}, {"active": true, "transitions": [{"href": "http://localhost:8080/jira/secure/admin/workflows/ViewWorkflowTransition.jspa?workflowMode=live&workflowName=SHPODM&workflowTransition=1", "name": "Create Issue", "configuredItems": [{"scripts": [{"scriptFile": "", "description": "Enter the condition for which this function will fire. <PERSON><PERSON> will evaluate to \"true\".\n                You can click one of the examples below, or see the wiki page for further examples.", "scriptCompileCtxOptions": null, "inlineScript": "issue.priority?.name == 'High'", "name": "Condition", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.JiraCommonCompileContexts.CONDITION_AND_ADDITIONAL_CONTEXT"}, {"scriptFile": "", "description": "Enter any customisations to the target issue, e.g. hard-coding specific field values.", "scriptCompileCtxOptions": null, "inlineScript": "null", "name": "Additional issue actions", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.AbstractCloneIssue.CLONE_ISSUE_ADDITIONAL_CODE_CONTEXT"}], "description": "<PERSON>lones this issue to another issue, optionally another project and issue type, and creates a link.\n        ", "clazz": null, "name": "Clones an issue, and links"}]}, {"href": "http://localhost:8080/jira/secure/admin/workflows/ViewWorkflowTransition.jspa?workflowMode=live&workflowName=SHPODM&workflowTransition=2&workflowStep=1", "name": "Close Issue", "configuredItems": [{"scripts": [{"scriptFile": "", "description": "Enter the condition for which this function will fire. <PERSON><PERSON> will evaluate to \"true\".\n                You can click one of the examples below, or see the wiki page for further examples.", "scriptCompileCtxOptions": null, "inlineScript": "issue.priority?.name == 'High'", "name": "Condition", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.JiraCommonCompileContexts.CONDITION_AND_ADDITIONAL_CONTEXT"}, {"scriptFile": "", "description": "Enter any customisations to the target issue, e.g. hard-coding specific field values.", "scriptCompileCtxOptions": null, "inlineScript": "null", "name": "Additional issue actions", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.AbstractCloneIssue.CLONE_ISSUE_ADDITIONAL_CODE_CONTEXT"}], "description": "<PERSON>lones this issue to another issue, optionally another project and issue type, and creates a link.\n        ", "clazz": null, "name": "Clones an issue, and links"}]}, {"href": "http://localhost:8080/jira/secure/admin/workflows/ViewWorkflowTransition.jspa?workflowMode=live&workflowName=SHPODM&workflowTransition=3&workflowStep=4", "name": "Reopen Issue", "configuredItems": [{"scripts": [{"scriptFile": "", "description": "Enter the condition for which this function will fire. <PERSON><PERSON> will evaluate to \"true\".\n                You can click one of the examples below, or see the wiki page for further examples.", "scriptCompileCtxOptions": null, "inlineScript": "issue.priority?.name == 'High'", "name": "Condition", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.JiraCommonCompileContexts.CONDITION_AND_ADDITIONAL_CONTEXT"}, {"scriptFile": "", "description": "Enter any customisations to the target issue, e.g. hard-coding specific field values.", "scriptCompileCtxOptions": null, "inlineScript": "null", "name": "Additional issue actions", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.AbstractCloneIssue.CLONE_ISSUE_ADDITIONAL_CODE_CONTEXT"}], "description": "<PERSON>lones this issue to another issue, optionally another project and issue type, and creates a link.\n        ", "clazz": null, "name": "Clones an issue, and links"}]}, {"href": "http://localhost:8080/jira/secure/admin/workflows/ViewWorkflowTransition.jspa?workflowMode=live&workflowName=SHPODM&workflowTransition=4&workflowStep=1", "name": "Start Progress", "configuredItems": [{"scripts": [{"scriptFile": "", "description": "Enter the condition for which this function will fire. <PERSON><PERSON> will evaluate to \"true\".\n                You can click one of the examples below, or see the wiki page for further examples.", "scriptCompileCtxOptions": null, "inlineScript": "issue.priority?.name == 'High'", "name": "Condition", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.JiraCommonCompileContexts.CONDITION_AND_ADDITIONAL_CONTEXT"}, {"scriptFile": "", "description": "Enter any customisations to the target issue, e.g. hard-coding specific field values.", "scriptCompileCtxOptions": null, "inlineScript": "null", "name": "Additional issue actions", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.AbstractCloneIssue.CLONE_ISSUE_ADDITIONAL_CODE_CONTEXT"}], "description": "<PERSON>lones this issue to another issue, optionally another project and issue type, and creates a link.\n        ", "clazz": null, "name": "Clones an issue, and links"}]}, {"href": "http://localhost:8080/jira/secure/admin/workflows/ViewWorkflowTransition.jspa?workflowMode=live&workflowName=SHPODM&workflowTransition=5&workflowStep=1", "name": "Resolve Issue", "configuredItems": [{"scripts": [{"scriptFile": "", "description": "Enter the condition for which this function will fire. <PERSON><PERSON> will evaluate to \"true\".\n                You can click one of the examples below, or see the wiki page for further examples.", "scriptCompileCtxOptions": null, "inlineScript": "issue.priority?.name == 'High'", "name": "Condition", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.JiraCommonCompileContexts.CONDITION_AND_ADDITIONAL_CONTEXT"}, {"scriptFile": "", "description": "Enter any customisations to the target issue, e.g. hard-coding specific field values.", "scriptCompileCtxOptions": null, "inlineScript": "null", "name": "Additional issue actions", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.AbstractCloneIssue.CLONE_ISSUE_ADDITIONAL_CODE_CONTEXT"}], "description": "<PERSON>lones this issue to another issue, optionally another project and issue type, and creates a link.\n        ", "clazz": null, "name": "Clones an issue, and links"}]}, {"href": "http://localhost:8080/jira/secure/admin/workflows/ViewWorkflowTransition.jspa?workflowMode=live&workflowName=SHPODM&workflowTransition=301", "name": "Stop Progress", "configuredItems": [{"scripts": [{"scriptFile": "", "description": "Enter the condition for which this function will fire. <PERSON><PERSON> will evaluate to \"true\".\n                You can click one of the examples below, or see the wiki page for further examples.", "scriptCompileCtxOptions": null, "inlineScript": "issue.priority?.name == 'High'", "name": "Condition", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.JiraCommonCompileContexts.CONDITION_AND_ADDITIONAL_CONTEXT"}, {"scriptFile": "", "description": "Enter any customisations to the target issue, e.g. hard-coding specific field values.", "scriptCompileCtxOptions": null, "inlineScript": "null", "name": "Additional issue actions", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.AbstractCloneIssue.CLONE_ISSUE_ADDITIONAL_CODE_CONTEXT"}], "description": "<PERSON>lones this issue to another issue, optionally another project and issue type, and creates a link.\n        ", "clazz": null, "name": "Clones an issue, and links"}]}, {"href": "http://localhost:8080/jira/secure/admin/workflows/ViewWorkflowTransition.jspa?workflowMode=live&workflowName=SHPODM&workflowTransition=701", "name": "Close Issue", "configuredItems": [{"scripts": [{"scriptFile": "", "description": "Enter the condition for which this function will fire. <PERSON><PERSON> will evaluate to \"true\".\n                You can click one of the examples below, or see the wiki page for further examples.", "scriptCompileCtxOptions": null, "inlineScript": "issue.priority?.name == 'High'", "name": "Condition", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.JiraCommonCompileContexts.CONDITION_AND_ADDITIONAL_CONTEXT"}, {"scriptFile": "", "description": "Enter any customisations to the target issue, e.g. hard-coding specific field values.", "scriptCompileCtxOptions": null, "inlineScript": "null", "name": "Additional issue actions", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.AbstractCloneIssue.CLONE_ISSUE_ADDITIONAL_CODE_CONTEXT"}], "description": "<PERSON>lones this issue to another issue, optionally another project and issue type, and creates a link.\n        ", "clazz": null, "name": "Clones an issue, and links"}]}], "draft": false, "name": "SHPODM"}, {"active": true, "transitions": [{"href": "http://localhost:8080/jira/secure/admin/workflows/ViewWorkflowTransition.jspa?workflowMode=live&workflowName=TFRGGG&workflowTransition=1", "name": "Create Issue", "configuredItems": [{"scripts": [{"scriptFile": "", "description": "Enter the condition for which this function will fire. <PERSON><PERSON> will evaluate to \"true\".\n                You can click one of the examples below, or see the wiki page for further examples.", "scriptCompileCtxOptions": null, "inlineScript": "issue.priority?.name == 'High'", "name": "Condition", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.JiraCommonCompileContexts.CONDITION_AND_ADDITIONAL_CONTEXT"}, {"scriptFile": "", "description": "Enter any customisations to the target issue, e.g. hard-coding specific field values.", "scriptCompileCtxOptions": null, "inlineScript": "null", "name": "Additional issue actions", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.AbstractCloneIssue.CLONE_ISSUE_ADDITIONAL_CODE_CONTEXT"}], "description": "<PERSON>lones this issue to another issue, optionally another project and issue type, and creates a link.\n        ", "clazz": null, "name": "Clones an issue, and links"}]}, {"href": "http://localhost:8080/jira/secure/admin/workflows/ViewWorkflowTransition.jspa?workflowMode=live&workflowName=TFRGGG&workflowTransition=2&workflowStep=1", "name": "Close Issue", "configuredItems": [{"scripts": [{"scriptFile": "", "description": "Enter the condition for which this function will fire. <PERSON><PERSON> will evaluate to \"true\".\n                You can click one of the examples below, or see the wiki page for further examples.", "scriptCompileCtxOptions": null, "inlineScript": "issue.priority?.name == 'High'", "name": "Condition", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.JiraCommonCompileContexts.CONDITION_AND_ADDITIONAL_CONTEXT"}, {"scriptFile": "", "description": "Enter any customisations to the target issue, e.g. hard-coding specific field values.", "scriptCompileCtxOptions": null, "inlineScript": "null", "name": "Additional issue actions", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.AbstractCloneIssue.CLONE_ISSUE_ADDITIONAL_CODE_CONTEXT"}], "description": "<PERSON>lones this issue to another issue, optionally another project and issue type, and creates a link.\n        ", "clazz": null, "name": "Clones an issue, and links"}]}, {"href": "http://localhost:8080/jira/secure/admin/workflows/ViewWorkflowTransition.jspa?workflowMode=live&workflowName=TFRGGG&workflowTransition=3&workflowStep=4", "name": "Reopen Issue", "configuredItems": [{"scripts": [{"scriptFile": "", "description": "Enter the condition for which this function will fire. <PERSON><PERSON> will evaluate to \"true\".\n                You can click one of the examples below, or see the wiki page for further examples.", "scriptCompileCtxOptions": null, "inlineScript": "issue.priority?.name == 'High'", "name": "Condition", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.JiraCommonCompileContexts.CONDITION_AND_ADDITIONAL_CONTEXT"}, {"scriptFile": "", "description": "Enter any customisations to the target issue, e.g. hard-coding specific field values.", "scriptCompileCtxOptions": null, "inlineScript": "null", "name": "Additional issue actions", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.AbstractCloneIssue.CLONE_ISSUE_ADDITIONAL_CODE_CONTEXT"}], "description": "<PERSON>lones this issue to another issue, optionally another project and issue type, and creates a link.\n        ", "clazz": null, "name": "Clones an issue, and links"}]}, {"href": "http://localhost:8080/jira/secure/admin/workflows/ViewWorkflowTransition.jspa?workflowMode=live&workflowName=TFRGGG&workflowTransition=4&workflowStep=1", "name": "Start Progress", "configuredItems": [{"scripts": [{"scriptFile": "", "description": "Enter the condition for which this function will fire. <PERSON><PERSON> will evaluate to \"true\".\n                You can click one of the examples below, or see the wiki page for further examples.", "scriptCompileCtxOptions": null, "inlineScript": "issue.priority?.name == 'High'", "name": "Condition", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.JiraCommonCompileContexts.CONDITION_AND_ADDITIONAL_CONTEXT"}, {"scriptFile": "", "description": "Enter any customisations to the target issue, e.g. hard-coding specific field values.", "scriptCompileCtxOptions": null, "inlineScript": "null", "name": "Additional issue actions", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.AbstractCloneIssue.CLONE_ISSUE_ADDITIONAL_CODE_CONTEXT"}], "description": "<PERSON>lones this issue to another issue, optionally another project and issue type, and creates a link.\n        ", "clazz": null, "name": "Clones an issue, and links"}]}, {"href": "http://localhost:8080/jira/secure/admin/workflows/ViewWorkflowTransition.jspa?workflowMode=live&workflowName=TFRGGG&workflowTransition=5&workflowStep=1", "name": "Resolve Issue", "configuredItems": [{"scripts": [{"scriptFile": "", "description": "Enter the condition for which this function will fire. <PERSON><PERSON> will evaluate to \"true\".\n                You can click one of the examples below, or see the wiki page for further examples.", "scriptCompileCtxOptions": null, "inlineScript": "issue.priority?.name == 'High'", "name": "Condition", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.JiraCommonCompileContexts.CONDITION_AND_ADDITIONAL_CONTEXT"}, {"scriptFile": "", "description": "Enter any customisations to the target issue, e.g. hard-coding specific field values.", "scriptCompileCtxOptions": null, "inlineScript": "null", "name": "Additional issue actions", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.AbstractCloneIssue.CLONE_ISSUE_ADDITIONAL_CODE_CONTEXT"}], "description": "<PERSON>lones this issue to another issue, optionally another project and issue type, and creates a link.\n        ", "clazz": null, "name": "Clones an issue, and links"}]}, {"href": "http://localhost:8080/jira/secure/admin/workflows/ViewWorkflowTransition.jspa?workflowMode=live&workflowName=TFRGGG&workflowTransition=301", "name": "Stop Progress", "configuredItems": [{"scripts": [{"scriptFile": "", "description": "Enter the condition for which this function will fire. <PERSON><PERSON> will evaluate to \"true\".\n                You can click one of the examples below, or see the wiki page for further examples.", "scriptCompileCtxOptions": null, "inlineScript": "issue.priority?.name == 'High'", "name": "Condition", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.JiraCommonCompileContexts.CONDITION_AND_ADDITIONAL_CONTEXT"}, {"scriptFile": "", "description": "Enter any customisations to the target issue, e.g. hard-coding specific field values.", "scriptCompileCtxOptions": null, "inlineScript": "null", "name": "Additional issue actions", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.AbstractCloneIssue.CLONE_ISSUE_ADDITIONAL_CODE_CONTEXT"}], "description": "<PERSON>lones this issue to another issue, optionally another project and issue type, and creates a link.\n        ", "clazz": null, "name": "Clones an issue, and links"}]}, {"href": "http://localhost:8080/jira/secure/admin/workflows/ViewWorkflowTransition.jspa?workflowMode=live&workflowName=TFRGGG&workflowTransition=701", "name": "Close Issue", "configuredItems": [{"scripts": [{"scriptFile": "", "description": "Enter the condition for which this function will fire. <PERSON><PERSON> will evaluate to \"true\".\n                You can click one of the examples below, or see the wiki page for further examples.", "scriptCompileCtxOptions": null, "inlineScript": "issue.priority?.name == 'High'", "name": "Condition", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.JiraCommonCompileContexts.CONDITION_AND_ADDITIONAL_CONTEXT"}, {"scriptFile": "", "description": "Enter any customisations to the target issue, e.g. hard-coding specific field values.", "scriptCompileCtxOptions": null, "inlineScript": "null", "name": "Additional issue actions", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.AbstractCloneIssue.CLONE_ISSUE_ADDITIONAL_CODE_CONTEXT"}], "description": "<PERSON>lones this issue to another issue, optionally another project and issue type, and creates a link.\n        ", "clazz": null, "name": "Clones an issue, and links"}]}], "draft": false, "name": "TFRGGG"}, {"active": true, "transitions": [{"href": "http://localhost:8080/jira/secure/admin/workflows/ViewWorkflowTransition.jspa?workflowMode=live&workflowName=XHNNMX&workflowTransition=1", "name": "Create Issue", "configuredItems": [{"scripts": [{"scriptFile": "", "description": "Enter the condition for which this function will fire. <PERSON><PERSON> will evaluate to \"true\".\n                You can click one of the examples below, or see the wiki page for further examples.", "scriptCompileCtxOptions": null, "inlineScript": "issue.priority?.name == 'High'", "name": "Condition", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.JiraCommonCompileContexts.CONDITION_AND_ADDITIONAL_CONTEXT"}, {"scriptFile": "", "description": "Enter any customisations to the target issue, e.g. hard-coding specific field values.", "scriptCompileCtxOptions": null, "inlineScript": "null", "name": "Additional issue actions", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.AbstractCloneIssue.CLONE_ISSUE_ADDITIONAL_CODE_CONTEXT"}], "description": "<PERSON>lones this issue to another issue, optionally another project and issue type, and creates a link.\n        ", "clazz": null, "name": "Clones an issue, and links"}]}, {"href": "http://localhost:8080/jira/secure/admin/workflows/ViewWorkflowTransition.jspa?workflowMode=live&workflowName=XHNNMX&workflowTransition=2&workflowStep=1", "name": "Close Issue", "configuredItems": [{"scripts": [{"scriptFile": "", "description": "Enter the condition for which this function will fire. <PERSON><PERSON> will evaluate to \"true\".\n                You can click one of the examples below, or see the wiki page for further examples.", "scriptCompileCtxOptions": null, "inlineScript": "issue.priority?.name == 'High'", "name": "Condition", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.JiraCommonCompileContexts.CONDITION_AND_ADDITIONAL_CONTEXT"}, {"scriptFile": "", "description": "Enter any customisations to the target issue, e.g. hard-coding specific field values.", "scriptCompileCtxOptions": null, "inlineScript": "null", "name": "Additional issue actions", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.AbstractCloneIssue.CLONE_ISSUE_ADDITIONAL_CODE_CONTEXT"}], "description": "<PERSON>lones this issue to another issue, optionally another project and issue type, and creates a link.\n        ", "clazz": null, "name": "Clones an issue, and links"}]}, {"href": "http://localhost:8080/jira/secure/admin/workflows/ViewWorkflowTransition.jspa?workflowMode=live&workflowName=XHNNMX&workflowTransition=3&workflowStep=4", "name": "Reopen Issue", "configuredItems": [{"scripts": [{"scriptFile": "", "description": "Enter the condition for which this function will fire. <PERSON><PERSON> will evaluate to \"true\".\n                You can click one of the examples below, or see the wiki page for further examples.", "scriptCompileCtxOptions": null, "inlineScript": "issue.priority?.name == 'High'", "name": "Condition", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.JiraCommonCompileContexts.CONDITION_AND_ADDITIONAL_CONTEXT"}, {"scriptFile": "", "description": "Enter any customisations to the target issue, e.g. hard-coding specific field values.", "scriptCompileCtxOptions": null, "inlineScript": "null", "name": "Additional issue actions", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.AbstractCloneIssue.CLONE_ISSUE_ADDITIONAL_CODE_CONTEXT"}], "description": "<PERSON>lones this issue to another issue, optionally another project and issue type, and creates a link.\n        ", "clazz": null, "name": "Clones an issue, and links"}]}, {"href": "http://localhost:8080/jira/secure/admin/workflows/ViewWorkflowTransition.jspa?workflowMode=live&workflowName=XHNNMX&workflowTransition=4&workflowStep=1", "name": "Start Progress", "configuredItems": [{"scripts": [{"scriptFile": "", "description": "Enter the condition for which this function will fire. <PERSON><PERSON> will evaluate to \"true\".\n                You can click one of the examples below, or see the wiki page for further examples.", "scriptCompileCtxOptions": null, "inlineScript": "issue.priority?.name == 'High'", "name": "Condition", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.JiraCommonCompileContexts.CONDITION_AND_ADDITIONAL_CONTEXT"}, {"scriptFile": "", "description": "Enter any customisations to the target issue, e.g. hard-coding specific field values.", "scriptCompileCtxOptions": null, "inlineScript": "null", "name": "Additional issue actions", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.AbstractCloneIssue.CLONE_ISSUE_ADDITIONAL_CODE_CONTEXT"}], "description": "<PERSON>lones this issue to another issue, optionally another project and issue type, and creates a link.\n        ", "clazz": null, "name": "Clones an issue, and links"}]}, {"href": "http://localhost:8080/jira/secure/admin/workflows/ViewWorkflowTransition.jspa?workflowMode=live&workflowName=XHNNMX&workflowTransition=5&workflowStep=1", "name": "Resolve Issue", "configuredItems": [{"scripts": [{"scriptFile": "", "description": "Enter the condition for which this function will fire. <PERSON><PERSON> will evaluate to \"true\".\n                You can click one of the examples below, or see the wiki page for further examples.", "scriptCompileCtxOptions": null, "inlineScript": "issue.priority?.name == 'High'", "name": "Condition", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.JiraCommonCompileContexts.CONDITION_AND_ADDITIONAL_CONTEXT"}, {"scriptFile": "", "description": "Enter any customisations to the target issue, e.g. hard-coding specific field values.", "scriptCompileCtxOptions": null, "inlineScript": "null", "name": "Additional issue actions", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.AbstractCloneIssue.CLONE_ISSUE_ADDITIONAL_CODE_CONTEXT"}], "description": "<PERSON>lones this issue to another issue, optionally another project and issue type, and creates a link.\n        ", "clazz": null, "name": "Clones an issue, and links"}]}, {"href": "http://localhost:8080/jira/secure/admin/workflows/ViewWorkflowTransition.jspa?workflowMode=live&workflowName=XHNNMX&workflowTransition=301", "name": "Stop Progress", "configuredItems": [{"scripts": [{"scriptFile": "", "description": "Enter the condition for which this function will fire. <PERSON><PERSON> will evaluate to \"true\".\n                You can click one of the examples below, or see the wiki page for further examples.", "scriptCompileCtxOptions": null, "inlineScript": "issue.priority?.name == 'High'", "name": "Condition", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.JiraCommonCompileContexts.CONDITION_AND_ADDITIONAL_CONTEXT"}, {"scriptFile": "", "description": "Enter any customisations to the target issue, e.g. hard-coding specific field values.", "scriptCompileCtxOptions": null, "inlineScript": "null", "name": "Additional issue actions", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.AbstractCloneIssue.CLONE_ISSUE_ADDITIONAL_CODE_CONTEXT"}], "description": "<PERSON>lones this issue to another issue, optionally another project and issue type, and creates a link.\n        ", "clazz": null, "name": "Clones an issue, and links"}]}, {"href": "http://localhost:8080/jira/secure/admin/workflows/ViewWorkflowTransition.jspa?workflowMode=live&workflowName=XHNNMX&workflowTransition=701", "name": "Close Issue", "configuredItems": [{"scripts": [{"scriptFile": "", "description": "Enter the condition for which this function will fire. <PERSON><PERSON> will evaluate to \"true\".\n                You can click one of the examples below, or see the wiki page for further examples.", "scriptCompileCtxOptions": null, "inlineScript": "issue.priority?.name == 'High'", "name": "Condition", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.JiraCommonCompileContexts.CONDITION_AND_ADDITIONAL_CONTEXT"}, {"scriptFile": "", "description": "Enter any customisations to the target issue, e.g. hard-coding specific field values.", "scriptCompileCtxOptions": null, "inlineScript": "null", "name": "Additional issue actions", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.AbstractCloneIssue.CLONE_ISSUE_ADDITIONAL_CODE_CONTEXT"}], "description": "<PERSON>lones this issue to another issue, optionally another project and issue type, and creates a link.\n        ", "clazz": null, "name": "Clones an issue, and links"}]}], "draft": false, "name": "XHNNMX"}, {"active": true, "transitions": [{"href": "http://localhost:8080/jira/secure/admin/workflows/ViewWorkflowTransition.jspa?workflowMode=live&workflowName=YJIOLU&workflowTransition=1", "name": "Create Issue", "configuredItems": [{"scripts": [{"scriptFile": "", "description": "Enter the condition for which this function will fire. <PERSON><PERSON> will evaluate to \"true\".\n                You can click one of the examples below, or see the wiki page for further examples.", "scriptCompileCtxOptions": null, "inlineScript": "issue.priority?.name == 'High'", "name": "Condition", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.JiraCommonCompileContexts.CONDITION_AND_ADDITIONAL_CONTEXT"}, {"scriptFile": "", "description": "Enter any customisations to the target issue, e.g. hard-coding specific field values.", "scriptCompileCtxOptions": null, "inlineScript": "null", "name": "Additional issue actions", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.AbstractCloneIssue.CLONE_ISSUE_ADDITIONAL_CODE_CONTEXT"}], "description": "<PERSON>lones this issue to another issue, optionally another project and issue type, and creates a link.\n        ", "clazz": null, "name": "Clones an issue, and links"}]}, {"href": "http://localhost:8080/jira/secure/admin/workflows/ViewWorkflowTransition.jspa?workflowMode=live&workflowName=YJIOLU&workflowTransition=2&workflowStep=1", "name": "Close Issue", "configuredItems": [{"scripts": [{"scriptFile": "", "description": "Enter the condition for which this function will fire. <PERSON><PERSON> will evaluate to \"true\".\n                You can click one of the examples below, or see the wiki page for further examples.", "scriptCompileCtxOptions": null, "inlineScript": "issue.priority?.name == 'High'", "name": "Condition", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.JiraCommonCompileContexts.CONDITION_AND_ADDITIONAL_CONTEXT"}, {"scriptFile": "", "description": "Enter any customisations to the target issue, e.g. hard-coding specific field values.", "scriptCompileCtxOptions": null, "inlineScript": "null", "name": "Additional issue actions", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.AbstractCloneIssue.CLONE_ISSUE_ADDITIONAL_CODE_CONTEXT"}], "description": "<PERSON>lones this issue to another issue, optionally another project and issue type, and creates a link.\n        ", "clazz": null, "name": "Clones an issue, and links"}]}, {"href": "http://localhost:8080/jira/secure/admin/workflows/ViewWorkflowTransition.jspa?workflowMode=live&workflowName=YJIOLU&workflowTransition=3&workflowStep=4", "name": "Reopen Issue", "configuredItems": [{"scripts": [{"scriptFile": "", "description": "Enter the condition for which this function will fire. <PERSON><PERSON> will evaluate to \"true\".\n                You can click one of the examples below, or see the wiki page for further examples.", "scriptCompileCtxOptions": null, "inlineScript": "issue.priority?.name == 'High'", "name": "Condition", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.JiraCommonCompileContexts.CONDITION_AND_ADDITIONAL_CONTEXT"}, {"scriptFile": "", "description": "Enter any customisations to the target issue, e.g. hard-coding specific field values.", "scriptCompileCtxOptions": null, "inlineScript": "null", "name": "Additional issue actions", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.AbstractCloneIssue.CLONE_ISSUE_ADDITIONAL_CODE_CONTEXT"}], "description": "<PERSON>lones this issue to another issue, optionally another project and issue type, and creates a link.\n        ", "clazz": null, "name": "Clones an issue, and links"}]}, {"href": "http://localhost:8080/jira/secure/admin/workflows/ViewWorkflowTransition.jspa?workflowMode=live&workflowName=YJIOLU&workflowTransition=4&workflowStep=1", "name": "Start Progress", "configuredItems": [{"scripts": [{"scriptFile": "", "description": "Enter the condition for which this function will fire. <PERSON><PERSON> will evaluate to \"true\".\n                You can click one of the examples below, or see the wiki page for further examples.", "scriptCompileCtxOptions": null, "inlineScript": "issue.priority?.name == 'High'", "name": "Condition", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.JiraCommonCompileContexts.CONDITION_AND_ADDITIONAL_CONTEXT"}, {"scriptFile": "", "description": "Enter any customisations to the target issue, e.g. hard-coding specific field values.", "scriptCompileCtxOptions": null, "inlineScript": "null", "name": "Additional issue actions", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.AbstractCloneIssue.CLONE_ISSUE_ADDITIONAL_CODE_CONTEXT"}], "description": "<PERSON>lones this issue to another issue, optionally another project and issue type, and creates a link.\n        ", "clazz": null, "name": "Clones an issue, and links"}]}, {"href": "http://localhost:8080/jira/secure/admin/workflows/ViewWorkflowTransition.jspa?workflowMode=live&workflowName=YJIOLU&workflowTransition=5&workflowStep=1", "name": "Resolve Issue", "configuredItems": [{"scripts": [{"scriptFile": "", "description": "Enter the condition for which this function will fire. <PERSON><PERSON> will evaluate to \"true\".\n                You can click one of the examples below, or see the wiki page for further examples.", "scriptCompileCtxOptions": null, "inlineScript": "issue.priority?.name == 'High'", "name": "Condition", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.JiraCommonCompileContexts.CONDITION_AND_ADDITIONAL_CONTEXT"}, {"scriptFile": "", "description": "Enter any customisations to the target issue, e.g. hard-coding specific field values.", "scriptCompileCtxOptions": null, "inlineScript": "null", "name": "Additional issue actions", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.AbstractCloneIssue.CLONE_ISSUE_ADDITIONAL_CODE_CONTEXT"}], "description": "<PERSON>lones this issue to another issue, optionally another project and issue type, and creates a link.\n        ", "clazz": null, "name": "Clones an issue, and links"}]}, {"href": "http://localhost:8080/jira/secure/admin/workflows/ViewWorkflowTransition.jspa?workflowMode=live&workflowName=YJIOLU&workflowTransition=301", "name": "Stop Progress", "configuredItems": [{"scripts": [{"scriptFile": "", "description": "Enter the condition for which this function will fire. <PERSON><PERSON> will evaluate to \"true\".\n                You can click one of the examples below, or see the wiki page for further examples.", "scriptCompileCtxOptions": null, "inlineScript": "issue.priority?.name == 'High'", "name": "Condition", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.JiraCommonCompileContexts.CONDITION_AND_ADDITIONAL_CONTEXT"}, {"scriptFile": "", "description": "Enter any customisations to the target issue, e.g. hard-coding specific field values.", "scriptCompileCtxOptions": null, "inlineScript": "null", "name": "Additional issue actions", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.AbstractCloneIssue.CLONE_ISSUE_ADDITIONAL_CODE_CONTEXT"}], "description": "<PERSON>lones this issue to another issue, optionally another project and issue type, and creates a link.\n        ", "clazz": null, "name": "Clones an issue, and links"}]}, {"href": "http://localhost:8080/jira/secure/admin/workflows/ViewWorkflowTransition.jspa?workflowMode=live&workflowName=YJIOLU&workflowTransition=701", "name": "Close Issue", "configuredItems": [{"scripts": [{"scriptFile": "", "description": "Enter the condition for which this function will fire. <PERSON><PERSON> will evaluate to \"true\".\n                You can click one of the examples below, or see the wiki page for further examples.", "scriptCompileCtxOptions": null, "inlineScript": "issue.priority?.name == 'High'", "name": "Condition", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.JiraCommonCompileContexts.CONDITION_AND_ADDITIONAL_CONTEXT"}, {"scriptFile": "", "description": "Enter any customisations to the target issue, e.g. hard-coding specific field values.", "scriptCompileCtxOptions": null, "inlineScript": "null", "name": "Additional issue actions", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.AbstractCloneIssue.CLONE_ISSUE_ADDITIONAL_CODE_CONTEXT"}], "description": "<PERSON>lones this issue to another issue, optionally another project and issue type, and creates a link.\n        ", "clazz": null, "name": "Clones an issue, and links"}]}], "draft": false, "name": "YJIOLU"}, {"active": true, "transitions": [{"href": "http://localhost:8080/jira/secure/admin/workflows/ViewWorkflowTransition.jspa?workflowMode=live&workflowName=ZFQLYZ&workflowTransition=1", "name": "Create Issue", "configuredItems": [{"scripts": [{"scriptFile": "", "description": "Enter the condition for which this function will fire. <PERSON><PERSON> will evaluate to \"true\".\n                You can click one of the examples below, or see the wiki page for further examples.", "scriptCompileCtxOptions": null, "inlineScript": "issue.priority?.name == 'High'", "name": "Condition", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.JiraCommonCompileContexts.CONDITION_AND_ADDITIONAL_CONTEXT"}, {"scriptFile": "", "description": "Enter any customisations to the target issue, e.g. hard-coding specific field values.", "scriptCompileCtxOptions": null, "inlineScript": "null", "name": "Additional issue actions", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.AbstractCloneIssue.CLONE_ISSUE_ADDITIONAL_CODE_CONTEXT"}], "description": "<PERSON>lones this issue to another issue, optionally another project and issue type, and creates a link.\n        ", "clazz": null, "name": "Clones an issue, and links"}]}, {"href": "http://localhost:8080/jira/secure/admin/workflows/ViewWorkflowTransition.jspa?workflowMode=live&workflowName=ZFQLYZ&workflowTransition=2&workflowStep=1", "name": "Close Issue", "configuredItems": [{"scripts": [{"scriptFile": "", "description": "Enter the condition for which this function will fire. <PERSON><PERSON> will evaluate to \"true\".\n                You can click one of the examples below, or see the wiki page for further examples.", "scriptCompileCtxOptions": null, "inlineScript": "issue.priority?.name == 'High'", "name": "Condition", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.JiraCommonCompileContexts.CONDITION_AND_ADDITIONAL_CONTEXT"}, {"scriptFile": "", "description": "Enter any customisations to the target issue, e.g. hard-coding specific field values.", "scriptCompileCtxOptions": null, "inlineScript": "null", "name": "Additional issue actions", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.AbstractCloneIssue.CLONE_ISSUE_ADDITIONAL_CODE_CONTEXT"}], "description": "<PERSON>lones this issue to another issue, optionally another project and issue type, and creates a link.\n        ", "clazz": null, "name": "Clones an issue, and links"}]}, {"href": "http://localhost:8080/jira/secure/admin/workflows/ViewWorkflowTransition.jspa?workflowMode=live&workflowName=ZFQLYZ&workflowTransition=3&workflowStep=4", "name": "Reopen Issue", "configuredItems": [{"scripts": [{"scriptFile": "", "description": "Enter the condition for which this function will fire. <PERSON><PERSON> will evaluate to \"true\".\n                You can click one of the examples below, or see the wiki page for further examples.", "scriptCompileCtxOptions": null, "inlineScript": "issue.priority?.name == 'High'", "name": "Condition", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.JiraCommonCompileContexts.CONDITION_AND_ADDITIONAL_CONTEXT"}, {"scriptFile": "", "description": "Enter any customisations to the target issue, e.g. hard-coding specific field values.", "scriptCompileCtxOptions": null, "inlineScript": "null", "name": "Additional issue actions", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.AbstractCloneIssue.CLONE_ISSUE_ADDITIONAL_CODE_CONTEXT"}], "description": "<PERSON>lones this issue to another issue, optionally another project and issue type, and creates a link.\n        ", "clazz": null, "name": "Clones an issue, and links"}]}, {"href": "http://localhost:8080/jira/secure/admin/workflows/ViewWorkflowTransition.jspa?workflowMode=live&workflowName=ZFQLYZ&workflowTransition=4&workflowStep=1", "name": "Start Progress", "configuredItems": [{"scripts": [{"scriptFile": "", "description": "Enter the condition for which this function will fire. <PERSON><PERSON> will evaluate to \"true\".\n                You can click one of the examples below, or see the wiki page for further examples.", "scriptCompileCtxOptions": null, "inlineScript": "issue.priority?.name == 'High'", "name": "Condition", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.JiraCommonCompileContexts.CONDITION_AND_ADDITIONAL_CONTEXT"}, {"scriptFile": "", "description": "Enter any customisations to the target issue, e.g. hard-coding specific field values.", "scriptCompileCtxOptions": null, "inlineScript": "null", "name": "Additional issue actions", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.AbstractCloneIssue.CLONE_ISSUE_ADDITIONAL_CODE_CONTEXT"}], "description": "<PERSON>lones this issue to another issue, optionally another project and issue type, and creates a link.\n        ", "clazz": null, "name": "Clones an issue, and links"}]}, {"href": "http://localhost:8080/jira/secure/admin/workflows/ViewWorkflowTransition.jspa?workflowMode=live&workflowName=ZFQLYZ&workflowTransition=5&workflowStep=1", "name": "Resolve Issue", "configuredItems": [{"scripts": [{"scriptFile": "", "description": "Enter the condition for which this function will fire. <PERSON><PERSON> will evaluate to \"true\".\n                You can click one of the examples below, or see the wiki page for further examples.", "scriptCompileCtxOptions": null, "inlineScript": "issue.priority?.name == 'High'", "name": "Condition", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.JiraCommonCompileContexts.CONDITION_AND_ADDITIONAL_CONTEXT"}, {"scriptFile": "", "description": "Enter any customisations to the target issue, e.g. hard-coding specific field values.", "scriptCompileCtxOptions": null, "inlineScript": "null", "name": "Additional issue actions", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.AbstractCloneIssue.CLONE_ISSUE_ADDITIONAL_CODE_CONTEXT"}], "description": "<PERSON>lones this issue to another issue, optionally another project and issue type, and creates a link.\n        ", "clazz": null, "name": "Clones an issue, and links"}]}, {"href": "http://localhost:8080/jira/secure/admin/workflows/ViewWorkflowTransition.jspa?workflowMode=live&workflowName=ZFQLYZ&workflowTransition=301", "name": "Stop Progress", "configuredItems": [{"scripts": [{"scriptFile": "", "description": "Enter the condition for which this function will fire. <PERSON><PERSON> will evaluate to \"true\".\n                You can click one of the examples below, or see the wiki page for further examples.", "scriptCompileCtxOptions": null, "inlineScript": "issue.priority?.name == 'High'", "name": "Condition", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.JiraCommonCompileContexts.CONDITION_AND_ADDITIONAL_CONTEXT"}, {"scriptFile": "", "description": "Enter any customisations to the target issue, e.g. hard-coding specific field values.", "scriptCompileCtxOptions": null, "inlineScript": "null", "name": "Additional issue actions", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.AbstractCloneIssue.CLONE_ISSUE_ADDITIONAL_CODE_CONTEXT"}], "description": "<PERSON>lones this issue to another issue, optionally another project and issue type, and creates a link.\n        ", "clazz": null, "name": "Clones an issue, and links"}]}, {"href": "http://localhost:8080/jira/secure/admin/workflows/ViewWorkflowTransition.jspa?workflowMode=live&workflowName=ZFQLYZ&workflowTransition=701", "name": "Close Issue", "configuredItems": [{"scripts": [{"scriptFile": "", "description": "Enter the condition for which this function will fire. <PERSON><PERSON> will evaluate to \"true\".\n                You can click one of the examples below, or see the wiki page for further examples.", "scriptCompileCtxOptions": null, "inlineScript": "issue.priority?.name == 'High'", "name": "Condition", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.JiraCommonCompileContexts.CONDITION_AND_ADDITIONAL_CONTEXT"}, {"scriptFile": "", "description": "Enter any customisations to the target issue, e.g. hard-coding specific field values.", "scriptCompileCtxOptions": null, "inlineScript": "null", "name": "Additional issue actions", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.AbstractCloneIssue.CLONE_ISSUE_ADDITIONAL_CODE_CONTEXT"}], "description": "<PERSON>lones this issue to another issue, optionally another project and issue type, and creates a link.\n        ", "clazz": null, "name": "Clones an issue, and links"}]}], "draft": false, "name": "ZFQLYZ"}], "listeners": [], "fields": [], "endpoints": [], "behaviours": [], "scriptErrors": [], "fragments": [], "jobs": []}}