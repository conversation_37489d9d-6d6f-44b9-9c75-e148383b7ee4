{"output": {"workflows": [{"active": false, "transitions": [{"href": "http://localhost:8080/jira/secure/admin/workflows/ViewWorkflowTransition.jspa?workflowMode=live&workflowName=Cypress-workflow&workflowTransition=4&workflowStep=1", "name": "Start Progress", "configuredItems": [{"scripts": [{"scriptFile": null, "description": "Enter your script here", "scriptCompileCtxOptions": null, "inlineScript": "789", "name": "Inline script", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.workflow.conditions.CustomScriptCondition.CUSTOM_SCRIPT_CONDITION_CONTEXT"}], "description": "Run your own groovy script from a file or entered into JIRA.", "clazz": null, "name": "Custom script condition"}, {"scripts": [{"scriptFile": "", "description": "Path to the script file accessible on the server", "scriptCompileCtxOptions": null, "inlineScript": null, "name": "Script file", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.workflow.conditions.CustomScriptCondition.CUSTOM_SCRIPT_CONDITION_CONTEXT"}], "description": "Run your own groovy script from a file or entered into JIRA.", "clazz": null, "name": "Custom script condition"}, {"scripts": [{"scriptFile": "", "description": "Enter the condition for which this function will fire. <PERSON><PERSON> will evaluate to \"true\".\n                You can click one of the examples below, or see the wiki page for further examples.", "scriptCompileCtxOptions": null, "inlineScript": "true", "name": "Condition", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.JiraCommonCompileContexts.CONDITION_AND_ADDITIONAL_CONTEXT"}], "description": "Runs a simple embedded script to find out whether to show the action or not", "clazz": null, "name": "Simple scripted condition"}]}], "draft": false, "name": "Cypress-workflow"}, {"active": true, "transitions": [{"href": "http://localhost:8080/jira/secure/admin/workflows/ViewWorkflowTransition.jspa?workflowMode=live&workflowName=Software+Simplified+Workflow+for+Project+SSPA&workflowTransition=21", "name": "In Progress", "configuredItems": [{"scripts": [{"scriptFile": null, "description": "Enter your script here", "scriptCompileCtxOptions": null, "inlineScript": "import com.onresolve.scriptrunner.runner.util.UserMessageUtil\n                        UserMessageUtil.success('Transitioned!')", "name": "Inline script", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.JiraCommonCompileContexts.ADDITIONAL_CODE_CONTEXT"}], "description": "Run your own groovy script from a file or entered into JIRA.", "clazz": null, "name": "Custom script post-function"}]}], "draft": false, "name": "Software Simplified Workflow for Project SSPA"}, {"active": true, "transitions": [{"href": "http://localhost:8080/jira/secure/admin/workflows/ViewWorkflowTransition.jspa?workflowMode=draft&workflowName=SR_TEST__wf&workflowTransition=4&workflowStep=1", "name": "Start Progress", "configuredItems": [{"scripts": [{"scriptFile": "", "description": "Enter the condition for which this function will fire. <PERSON><PERSON> will evaluate to \"true\".\n                You can click one of the examples below, or see the wiki page for further examples.", "scriptCompileCtxOptions": null, "inlineScript": "123", "name": "Condition", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.JiraCommonCompileContexts.CONDITION_AND_ADDITIONAL_CONTEXT"}, {"scriptFile": "", "description": "Enter any customisations to the target issue, e.g. hard-coding specific field values.", "scriptCompileCtxOptions": null, "inlineScript": "456", "name": "Additional issue actions", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.AbstractCloneIssue.CLONE_ISSUE_ADDITIONAL_CODE_CONTEXT"}], "description": "Create a sub-task. Will optionally reopen a matching sub-task.", "clazz": null, "name": "Create a sub-task"}]}], "draft": true, "name": "SR_TEST__wf"}], "listeners": [{"scripts": [{"scriptFile": null, "description": "Enter your script here", "scriptCompileCtxOptions": null, "inlineScript": "log.debug \"issue created: ${event.issue}\"", "name": "Inline script", "scriptCompileContext": "com.onresolve.scriptrunner.runner.events.JiraEventsCompileContextProvider.SCRIPT_CUSTOMLISTENER_CONTEXT"}], "description": "Write your own groovy class as a custom listener.", "clazz": null, "name": "Custom listener"}], "fields": [{"scripts": [{"scriptFile": null, "description": "Enter your script here", "scriptCompileCtxOptions": "{\n    \"customFieldId\": \"10200\"\n}", "inlineScript": "123", "name": "Inline script", "scriptCompileContext": "com.onresolve.scriptrunner.customfield.GroovyCustomField.SCRIPT_FIELD_CONTEXT"}], "description": "Create your own custom scripted field.", "clazz": null, "name": "Aaaa - Custom Script Field"}], "endpoints": [{"scripts": [{"scriptFile": null, "description": "Enter your script here", "scriptCompileCtxOptions": null, "inlineScript": "import com.onresolve.scriptrunner.runner.rest.common.CustomEndpointDelegate\nimport groovy.json.JsonBuilder\nimport groovy.transform.BaseScript\n\nimport javax.ws.rs.core.MultivaluedMap\nimport javax.ws.rs.core.Response\n\n@BaseScript CustomEndpointDelegate delegate\n\ndoSomething(httpMethod: \"GET\", groups: [\"jira-administrators\"]) { MultivaluedMap queryParams, String body ->\n    return Response.ok(new JsonBuilder([abc: 42]).toString()).build();\n}\n", "name": "Inline script", "scriptCompileContext": "com.onresolve.scriptrunner.runner.stc.ScriptCompileContext.SCRIPT_CONSOLE_CONTEXT"}], "description": "Define a REST endpoint in a script", "clazz": null, "name": "Custom endpoint"}], "behaviours": [{"scripts": [{"scriptFile": "", "description": null, "scriptCompileCtxOptions": null, "inlineScript": "// initialiser", "name": "Initialiser", "scriptCompileContext": "com.onresolve.jira.behaviours.BehaviourManagerImpl.BEHAVIOURS_SCRIPT_CONTEXT"}, {"scriptFile": "", "description": null, "scriptCompileCtxOptions": null, "inlineScript": "123\ngetFieldById(getFieldChanged())\n// summary", "name": "Summary", "scriptCompileContext": "com.onresolve.jira.behaviours.BehaviourManagerImpl.BEHAVIOURS_SCRIPT_CONTEXT"}, {"scriptFile": "examples/Scratch.groovy", "description": null, "scriptCompileCtxOptions": null, "inlineScript": "", "name": "Approvals", "scriptCompileContext": "com.onresolve.jira.behaviours.BehaviourManagerImpl.BEHAVIOURS_SCRIPT_CONTEXT"}], "description": "my thing", "clazz": null, "name": "Behaviour: test"}], "scriptErrors": [], "fragments": [{"scripts": [{"scriptFile": "", "description": "Under what circumstances should this link be displayed. Must be either a plain script or an implementation of com.atlassian.plugin.web.Condition", "scriptCompileCtxOptions": null, "inlineScript": "true", "name": "Condition", "scriptCompileContext": "com.onresolve.scriptrunner.fragments.FragmentsCannedScriptUtils.SCRIPT_FRAG_CONDITION_CONTEXT"}], "description": "Creates a web item (a button or link) at the location you specify", "clazz": null, "name": "Custom web item"}, {"scripts": [{"scriptFile": "examples/Scratch.groovy", "description": "Under what circumstances should this link be displayed. Must be either a plain script or an implementation of com.atlassian.plugin.web.Condition", "scriptCompileCtxOptions": null, "inlineScript": "", "name": "Condition", "scriptCompileContext": "com.onresolve.scriptrunner.fragments.FragmentsCannedScriptUtils.SCRIPT_FRAG_CONDITION_CONTEXT"}], "description": "Creates a web item (a button or link) at the location you specify", "clazz": null, "name": "Custom web item"}, {"scripts": [{"scriptFile": "", "description": "Under what circumstances should this link be displayed. Must be either a plain script or an implementation of com.atlassian.plugin.web.Condition", "scriptCompileCtxOptions": null, "inlineScript": "123", "name": "Condition", "scriptCompileContext": "com.onresolve.scriptrunner.fragments.FragmentsCannedScriptUtils.SCRIPT_FRAG_CONDITION_CONTEXT"}, {"scriptFile": "", "description": "This class will generate the contents of the panel, if present. Write to the provided <em>writer</em>. See the examples.", "scriptCompileCtxOptions": null, "inlineScript": "writer.write(\"Hello world!\")", "name": "Provider class/script", "scriptCompileContext": "com.onresolve.scriptrunner.fragments.AbstractCustomWebPanel.SCRIPT_FRAG_PANEL_CONTEXT"}], "description": "Create a custom web panel to display additional information", "clazz": null, "name": "Show a web panel"}], "jobs": [{"scripts": [{"scriptFile": "", "description": "Enter any customisations to the target issue, e.g. hard-coding specific field values.", "scriptCompileCtxOptions": null, "inlineScript": "log.debug (\"foooo\")", "name": "Additional issue actions", "scriptCompileContext": "com.onresolve.scriptrunner.canned.jira.utils.JiraCommonCompileContexts.ADDITIONAL_CODE_CONTEXT"}], "description": "Can periodically modify issues based on a JQL query, for instance: to change the state of issues if they have been inactive for 2 weeks.", "clazz": null, "name": "Escalation service"}]}}