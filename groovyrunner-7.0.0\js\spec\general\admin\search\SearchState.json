{"/restendpoints": {"available": [{"class": "com.onresolve.scriptrunner.canned.common.rest.CustomRestEndpoint", "name": "Custom endpoint", "description": "Define a REST endpoint in a script", "homePlugin": null, "hideInitially": false}], "configured": [{"FIELD_INLINE_SCRIPT": "import com.onresolve.scriptrunner.runner.rest.common.CustomEndpointDelegate\nimport groovy.json.JsonBuilder\nimport groovy.transform.BaseScript\n\nimport javax.ws.rs.core.MultivaluedMap\nimport javax.ws.rs.core.Response\n\n@BaseScript CustomEndpointDelegate delegate\n\ndoSomething(httpMethod: \"GET\", groups: [\"jira-administrators\"]) { MultivaluedMap queryParams, String body ->\n    return Response.ok(new JsonBuilder([abc: 42]).toString()).build();\n}\n", "FIELD_NOTES": "my rest endpoint", "canned-script": "com.onresolve.scriptrunner.canned.common.rest.CustomRestEndpoint", "endpoints": [{"verb": "GET", "name": "doSomething", "groups": ["jira-administrators"], "resourcePath": "Script12.groovy"}], "id": "-1813792304", "resourcePath": "Inline script"}, {"resourcePath": "com/onresolve/base/test/rest/jstestutils/cypress/CreateEmptyWorkflow.groovy", "endpoints": [{"verb": "GET", "name": "createEmptyWorkflow", "groups": null, "resourcePath": "com/onresolve/base/test/rest/jstestutils/cypress/CreateEmptyWorkflow.groovy"}]}, {"resourcePath": "com/onresolve/base/test/rest/jstestutils/cypress/GetJSDProjectForCypressTests.groovy", "endpoints": [{"verb": "GET", "name": "getJSDProjectForCypressTests", "groups": null, "resourcePath": "com/onresolve/base/test/rest/jstestutils/cypress/GetJSDProjectForCypressTests.groovy"}, {"verb": "GET", "name": "cypressTestsEnableTestPluginModule", "groups": null, "resourcePath": "com/onresolve/base/test/rest/jstestutils/cypress/GetJSDProjectForCypressTests.groovy"}, {"verb": "GET", "name": "cypressTestsDisableTestPluginModule", "groups": null, "resourcePath": "com/onresolve/base/test/rest/jstestutils/cypress/GetJSDProjectForCypressTests.groovy"}, {"verb": "GET", "name": "isSupportedJsdVersion", "groups": null, "resourcePath": "com/onresolve/base/test/rest/jstestutils/cypress/GetJSDProjectForCypressTests.groovy"}]}, {"resourcePath": "com/onresolve/base/test/rest/jstestutils/DbEventTypesEndpoint.groovy", "endpoints": [{"verb": "GET", "name": "eventTypes", "groups": null, "resourcePath": "com/onresolve/base/test/rest/jstestutils/DbEventTypesEndpoint.groovy"}]}, {"resourcePath": "com/onresolve/base/test/rest/jstestutils/GetEditHtml.groovy", "endpoints": [{"verb": "GET", "name": "viewHtml", "groups": null, "resourcePath": "com/onresolve/base/test/rest/jstestutils/GetEditHtml.groovy"}, {"verb": "GET", "name": "editHtml", "groups": null, "resourcePath": "com/onresolve/base/test/rest/jstestutils/GetEditHtml.groovy"}]}, {"resourcePath": "com/onresolve/base/test/rest/jstestutils/GithubReposEndpoint.groovy", "endpoints": [{"verb": "GET", "name": "githubRepoQuery", "groups": null, "resourcePath": "com/onresolve/base/test/rest/jstestutils/GithubReposEndpoint.groovy"}]}, {"resourcePath": "com/onresolve/base/test/rest/jstestutils/IssueApproval.groovy", "endpoints": [{"verb": "GET", "name": "approve", "groups": null, "resourcePath": "com/onresolve/base/test/rest/jstestutils/IssueApproval.groovy"}]}, {"resourcePath": "com/onresolve/base/test/rest/jstestutils/LabelAndRedirect.groovy", "endpoints": [{"verb": "GET", "name": "labelIssue", "groups": null, "resourcePath": "com/onresolve/base/test/rest/jstestutils/LabelAndRedirect.groovy"}]}, {"resourcePath": "com/onresolve/base/test/rest/jstestutils/ListConfluenceSpacesEndpoint.groovy", "endpoints": [{"verb": "GET", "name": "listConfluenceSpaces", "groups": null, "resourcePath": "com/onresolve/base/test/rest/jstestutils/ListConfluenceSpacesEndpoint.groovy"}, {"verb": "GET", "name": "listConfluenceSpacesDemo", "groups": null, "resourcePath": "com/onresolve/base/test/rest/jstestutils/ListConfluenceSpacesEndpoint.groovy"}]}, {"resourcePath": "com/onresolve/base/test/rest/jstestutils/PickFromRemoteJira.groovy", "endpoints": [{"verb": "GET", "name": "pickRemoteIssue", "groups": null, "resourcePath": "com/onresolve/base/test/rest/jstestutils/PickFromRemoteJira.groovy"}]}, {"resourcePath": "com/onresolve/base/test/rest/jstestutils/SelectConversionExamples.groovy", "endpoints": [{"verb": "GET", "name": "issueLinkTypes", "groups": null, "resourcePath": "com/onresolve/base/test/rest/jstestutils/SelectConversionExamples.groovy"}]}, {"resourcePath": "com/onresolve/base/test/rest/jstestutils/WebItemResponses.groovy", "endpoints": [{"verb": "GET", "name": "showDialog", "groups": null, "resourcePath": "com/onresolve/base/test/rest/jstestutils/WebItemResponses.groovy"}, {"verb": "GET", "name": "labelPage", "groups": null, "resourcePath": "com/onresolve/base/test/rest/jstestutils/WebItemResponses.groovy"}]}, {"resourcePath": "com/onresolve/base/test/rest/jstestutils/ToggleNewEditor.groovy", "endpoints": [{"verb": "GET", "name": "toggleEditor", "groups": null, "resourcePath": "com/onresolve/base/test/rest/jstestutils/ToggleNewEditor.groovy"}]}, {"resourcePath": "com/onresolve/base/test/rest/jstestutils/ToggleDarkFeature.groovy", "endpoints": [{"verb": "POST", "name": "enableDarkFeature", "groups": null, "resourcePath": "com/onresolve/base/test/rest/jstestutils/ToggleDarkFeature.groovy"}, {"verb": "POST", "name": "disableDarkFeature", "groups": null, "resourcePath": "com/onresolve/base/test/rest/jstestutils/ToggleDarkFeature.groovy"}]}]}, "/scriptfields": {"configured": [], "available": [{"class": "com.onresolve.scriptrunner.canned.jira.fields.CustomCannedScriptField", "name": "Custom Script Field", "description": "Create your own custom scripted field.", "homePlugin": null, "hideInitially": false, "helpUrl": "http://localhost:4000/jira/scripted-fields.html"}, {"class": "com.onresolve.scriptrunner.canned.jira.fields.DateOfFirstTransitionScriptField", "name": "Date of First Transition", "description": "Shows the date when the field first transitioned into a required state.", "homePlugin": null, "hideInitially": false, "helpUrl": "http://localhost:4000/jira/script-fields/date-of-first-transition.html"}, {"class": "com.onresolve.scriptrunner.canned.jira.fields.NoOfTimesInStatusScriptField", "name": "No. of Times In Status", "description": "This field is calibrated to show the number of times a given issue has been in selected status", "homePlugin": null, "hideInitially": false, "helpUrl": "http://localhost:4000/jira/script-fields/no-of-times-in-status.html"}, {"class": "com.onresolve.scriptrunner.canned.jira.fields.ParentIssueScriptField", "name": "Show parent issue in hierarchy", "description": "Show parent issue in hierarchy, following Portfolio parent field or links", "homePlugin": null, "hideInitially": false, "helpUrl": "http://localhost:4000/jira/script-fields/parent-issue.html"}, {"class": "com.onresolve.scriptrunner.canned.jira.fields.TimeOfLastStatusChangeScriptField", "name": "Time of Last Status Change", "description": "Displays a timestamp of when the issue last had its status changed", "homePlugin": null, "hideInitially": false, "helpUrl": "http://localhost:4000/jira/script-fields/time-of-last-status-change.html"}]}, "/listeners": {"configured": [{"FIELD_ADDITIONAL_SCRIPT": [], "FIELD_CONDITION": ["aas", ""], "FIELD_FUNCTION_ID": "8616c1d555dcc6e5ec0e780114cb8250740e2204", "FIELD_LISTENER_NOTES": "Create subbie", "FIELD_TARGET_ISSUE_TYPE": "10102", "canned-script": "com.onresolve.scriptrunner.canned.jira.workflow.postfunctions.CreateSubTask", "events": [1], "friendlyEventNames": "Issue Created", "id": "1322924060", "name": "Create a sub-task", "projects": ["JRA"]}], "available": [{"class": "com.onresolve.scriptrunner.canned.jira.workflow.listeners.CustomListener", "name": "Custom listener", "description": "Write your own groovy class as a custom listener.", "homePlugin": null, "hideInitially": false}, {"class": "com.onresolve.scriptrunner.canned.jira.workflow.listeners.VersionSynchroniseListener", "name": "Version synchroniser", "description": "Synchronises versions across multiple projects", "homePlugin": null, "hideInitially": false}, {"class": "com.onresolve.scriptrunner.canned.jira.workflow.postfunctions.AddWatcher", "name": "Adds the current user as a watcher", "description": "Adds the user performing the action as a watcher, if condition applies", "homePlugin": null, "hideInitially": false, "helpUrl": "http://localhost:4000/jira/listeners.html#_add_watcher"}, {"class": "com.onresolve.scriptrunner.canned.jira.workflow.postfunctions.CloneIssue", "name": "Clones an issue, and links", "description": "<PERSON>lones this issue to another issue, optionally another project and issue type, and creates a link.\n        ", "homePlugin": null, "hideInitially": false, "helpUrl": "http://localhost:4000/jira/builtin-scripts.html#_clones_an_issue_and_links"}, {"class": "com.onresolve.scriptrunner.canned.jira.workflow.postfunctions.CreateSubTask", "name": "Create a sub-task", "description": "Create a sub-task. Will optionally reopen a matching sub-task.", "homePlugin": null, "hideInitially": false, "helpUrl": "http://localhost:4000/jira/builtin-scripts.html#_create_a_sub_task"}, {"class": "com.onresolve.scriptrunner.canned.jira.workflow.postfunctions.FasttrackTransition", "name": "Fast-track transition an issue", "description": "If the condition is met, automatically transition this issue to another status\n        ", "homePlugin": null, "hideInitially": false, "helpUrl": "http://localhost:4000/jira/builtin-scripts.html#_fast_track_transition_an_issue"}, {"class": "com.onresolve.scriptrunner.canned.jira.workflow.postfunctions.FireEventWhen", "name": "Fires an event when condition is true", "description": "Fires an event that can be picked up by a notification scheme, in order to send mail only under certain conditions, eg Priority is Blocker", "homePlugin": null, "hideInitially": false, "helpUrl": "http://localhost:4000/jira/builtin-scripts.html#_fires_an_event_when_condition_is_true"}, {"class": "com.onresolve.scriptrunner.canned.jira.workflow.postfunctions.SendCustomEmail", "name": "Send a custom email", "description": "Send an email based on the provided template if conditions are met", "homePlugin": null, "hideInitially": false, "helpUrl": "http://localhost:4000/jira/builtin-scripts.html#_send_a_custom_email"}, {"class": "com.onresolve.scriptrunner.canned.jira.workflow.postfunctions.TransformationsCannedScriptListener", "name": "Sam<PERSON> showing transforms", "description": "Sam<PERSON> showing transforms", "homePlugin": null, "hideInitially": false}]}, "/fragments": {"configured": [], "available": [{"class": "com.onresolve.scriptrunner.canned.common.fragments.CustomRawXmlDescriptorScript", "name": "Raw xml module", "description": "Raw xml module", "homePlugin": null, "hideInitially": false, "helpUrl": "http://localhost:4000/jira/fragments/XmlModuleItem.html"}, {"class": "com.onresolve.scriptrunner.canned.common.fragments.CustomWebResource", "name": "Install web resource", "description": "Install web resource", "homePlugin": null, "hideInitially": false, "helpUrl": "http://localhost:4000/jira/fragments/WebResource.html"}, {"class": "com.onresolve.scriptrunner.canned.common.fragments.CustomWebSection", "name": "Create a custom web section", "description": "Creates a new web section, which you can add web-items to", "homePlugin": null, "hideInitially": false, "helpUrl": "http://localhost:4000/jira/fragments/WebSection.html"}, {"class": "com.onresolve.scriptrunner.canned.jira.fragments.CreateConstrainedIssueWebItem", "name": "Constrained create issue dialog", "description": "Opens the Create Issue dialog with project, issue type, and a behaviour set", "homePlugin": null, "hideInitially": false, "helpUrl": "http://localhost:4000/jira/fragments/CreateConstrainedIssue.html"}, {"class": "com.onresolve.scriptrunner.canned.jira.fragments.CustomWebItem", "name": "Custom web item", "description": "Creates a web item (a button or link) at the location you specify", "homePlugin": null, "hideInitially": false, "helpUrl": "http://localhost:4000/jira/fragments/WebItem.html"}, {"class": "com.onresolve.scriptrunner.canned.jira.fragments.CustomWebItemProvider", "name": "Custom web item provider", "description": "Custom web item provider", "homePlugin": null, "hideInitially": false, "helpUrl": "http://localhost:4000/jira/fragments/WebItemProvider.html"}, {"class": "com.onresolve.scriptrunner.canned.jira.fragments.CustomWebPanel", "name": "Show a web panel", "description": "Create a custom web panel to display additional information", "homePlugin": null, "hideInitially": false, "helpUrl": "http://localhost:4000/jira/fragments/WebPanel.html"}, {"class": "com.onresolve.scriptrunner.canned.jira.fragments.HideUIElement", "name": "Hide system or plugin UI element", "description": "Conditionally hides (or displays) a system web item or panel", "homePlugin": null, "hideInitially": false, "helpUrl": "http://localhost:4000/jira/fragments/HideUIElement.html"}]}, "/builtin": {"available": [{"class": "com.onresolve.scriptrunner.canned.jira.admin.BulkFixResolutions", "name": "Bulk Fix Resolutions", "description": "Modify resolution field in bulk, without creating a change entry. Useful after a bad import.\n        ", "homePlugin": null, "hideInitially": false, "helpUrl": "http://localhost:4000/jira/builtin-scripts.html#_bulk_fix_resolutions"}, {"class": "com.onresolve.scriptrunner.canned.jira.admin.BulkImportCustomFieldValues", "name": "Bulk import custom field values", "description": "Bulk import custom field values", "homePlugin": null, "hideInitially": false, "helpUrl": "http://localhost:4000/jira/builtin-scripts.html#_bulk_import_custom_field_values"}, {"class": "com.onresolve.scriptrunner.canned.jira.admin.ChangeSharedEntityOwnership", "name": "Change dashboard or filter ownership", "description": "This will change the ownership of the selected dashboards/filters, and for dashboards any filters that they use", "homePlugin": null, "hideInitially": false, "helpUrl": "http://localhost:4000/jira/builtin-scripts.html#_change_dashboard_or_filter_ownership"}, {"class": "com.onresolve.scriptrunner.canned.jira.admin.ClearCaches", "name": "Clear classloader or jira internal caches", "description": "Clear the groovy internal caches if this is not working automatically, or the jira caches if you have changed something in the database.<br>Expect a delay after executing this.", "homePlugin": null, "hideInitially": false, "helpUrl": "http://localhost:4000/jira/builtin-scripts.html#_clear_jira_or_groovy_caches"}, {"class": "com.onresolve.scriptrunner.canned.jira.admin.CopyCustomField", "name": "Copy field values", "description": "Copy field values from one field to another in bulk, to support changing a field type without\n            using SQL or restarting.\n        ", "homePlugin": null, "hideInitially": false, "helpUrl": "http://localhost:4000/jira/builtin-scripts.html#_copy_custom_field_values"}, {"class": "com.onresolve.scriptrunner.canned.jira.admin.CopyProject", "name": "Copy project", "description": "This tool will create a new project, from the configuration of another project.\n           <br>This includes: Schemes, Role memberships, Custom field configurations.\n           <br>Optionally: Issues, Versions, Components, Request types, Queues, Organizations, SLAs\n        ", "homePlugin": null, "hideInitially": false, "helpUrl": "http://localhost:4000/jira/builtin-scripts.html#_copy_project"}, {"class": "com.onresolve.scriptrunner.canned.jira.admin.IssueEventGenerator", "name": "Generate events", "description": "Generate Issue Created events to be consumed by listeners.\n        ", "homePlugin": null, "hideInitially": false, "helpUrl": "http://localhost:4000/"}, {"class": "com.onresolve.scriptrunner.canned.jira.admin.ReindexIssues", "name": "Reindex issues", "description": "Reindex issues for a project or corresponding to a filter, perhaps after an indexing problem or editing the database.\n        ", "homePlugin": null, "hideInitially": false}, {"class": "com.onresolve.scriptrunner.canned.jira.admin.ScriptRegistry", "name": "Script registry", "description": "Use the Script Registry to search your ScriptRunner custom scripts, and view any type-checking errors or deprecation warnings. Note that type-checking errors do not mean your code will not run - as we compile it statically and groovy is a dynamic language, there may be false positives.", "homePlugin": null, "hideInitially": false}, {"class": "com.onresolve.scriptrunner.canned.jira.admin.ServiceDeskBulkCopySLA", "name": "Bulk Copy SLA Configuration", "description": "Apply one SLA Configuration from one project onto a list of projects.", "homePlugin": null, "hideInitially": false}, {"class": "com.onresolve.scriptrunner.canned.jira.admin.SplitCustomFieldContext", "name": "Split custom field contexts", "description": "This script will split projects out a custom field context, duplicating option values and updating issues with the new option values.", "homePlugin": null, "hideInitially": false, "helpUrl": "http://localhost:4000/jira/builtin-scripts.html#_split_custom_field_context"}, {"class": "com.onresolve.scriptrunner.canned.jira.admin.SwitchUser", "name": "Switch to a different user", "description": "Switch to another user to deal with support problems and so on\n        ", "homePlugin": null, "hideInitially": false, "helpUrl": "http://localhost:4000/jira/builtin-scripts.html#_switch_user"}, {"class": "com.onresolve.scriptrunner.canned.common.admin.ConfigurationExporter", "name": "Configuration exporter", "description": "Exports extension configuration information to a descriptor file for using in a scripts plugin", "homePlugin": null, "hideInitially": false, "helpUrl": "http://localhost:4000/jira/builtin-scripts.html#todo..."}, {"class": "com.onresolve.scriptrunner.canned.common.admin.ListScheduledJobs", "name": "List scheduled jobs", "description": "List all scheduled jobs", "homePlugin": null, "hideInitially": false}, {"class": "com.onresolve.scriptrunner.canned.common.admin.LogFileViewer", "name": "View server log files", "description": "Shows the last N lines of application log files", "homePlugin": null, "hideInitially": false, "helpUrl": "http://localhost:4000/jira/builtin-scripts.html#_view_server_log_files"}, {"class": "com.onresolve.scriptrunner.canned.common.admin.RunUnitTests", "name": "Test runner", "description": "Runs JUnit and Spock tests, for use in a development instance only", "homePlugin": null, "hideInitially": false, "helpUrl": "http://localhost:4000/jira/testing.html"}, {"class": "com.onresolve.scriptrunner.canned.common.admin.SampleItemsCannedScript", "name": "Sample list of items", "description": "Sample list of items", "homePlugin": null, "hideInitially": false}, {"class": "com.onresolve.scriptrunner.canned.common.admin.SampleItemsConcreteType", "name": "Sample list of objects", "description": "Sample list of objects", "homePlugin": null, "hideInitially": false}, {"class": "com.onresolve.scriptrunner.canned.common.admin.TransformationsCannedScript", "name": "Sam<PERSON> showing transforms", "description": "Sam<PERSON> showing transforms", "homePlugin": null, "hideInitially": false}]}}