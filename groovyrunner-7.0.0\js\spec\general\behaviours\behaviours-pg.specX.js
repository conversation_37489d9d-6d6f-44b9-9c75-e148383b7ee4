/* eslint-disable no-unused-vars,func-style */
'use strict';

import * as actions from 'js/behaviours/action-creators';
import store from 'js/behaviours/create-store'
import initialiseBehaviours from 'js/behaviours/initialise-fields'
import {retainJasmineFixtures} from "../../issue-redux/test-utils";
import 'wr-dependency!js-test-resources'

require('css/client-val.css');

describe('behaviours playground', () => {
    beforeEach((done) => {
        retainJasmineFixtures()

        AJS.$('#jasmine-fixtures').append(require('html-loader!../fixtures/behaviours-fixture.html'));

        JIRA.trigger(JIRA.Events.NEW_CONTENT_ADDED, [$(document), JIRA.CONTENT_ADDED_REASON.pageLoad]);
        setTimeout(() => {
            console.log('TRIGGERING NEW CONTENT EVENT AND WAITING');
            done();
        }, 200);
    });

    it('something', () => {

        const initialState = {
            'summary'          : {
                readonly: true
            },
            reporter           : {
                disabled  : true,
                validation: '/PATHA/t/scripto.groovy'
            },
            'customfield_12345': {}
        };


        // store.subscribe(function () {
        //     console.log('store has been updated. Latest store state:', store.getState());
        //     // Update your views here
        // });

        const behaviours = [
            {
                id       : 'summary',
                field    : $('#summary'),
                fieldId  : 'summary',
                fieldType: 'com.atlassian.jira.issue.fields.SummarySystemField',
            },
            {
                id       : 'reporter',
                field    : $('#reporter'),
                fieldId  : 'reporter',
                fieldType: 'com.atlassian.jira.issue.fields.ReporterSystemField',
            },
            {
                id       : 'assignee',
                field    : $('#assignee'),
                fieldId  : 'assignee',
                fieldType: 'com.atlassian.jira.issue.fields.AssigneeSystemField',
            },
        ];

        initialiseBehaviours(store, behaviours);

        const $field = $('#summary');

        expect(store.getState().reporter.readonly).not.toBeDefined();
        expect($field).not.toHaveAttr('readonly');

        store.dispatch(actions.setReadOnly('summary', true));
        expect(store.getState().summary.readonly).toBe(true);
        expect($field).toHaveAttr('readonly');

        store.dispatch(actions.setReadOnly('summary', false));
        expect($field).not.toHaveAttr('readonly');

        store.dispatch(actions.setValue('summary', 'my summary'));
        expect($field.val()).toEqual('my summary');

        store.dispatch(actions.setReadOnly('reporter', true));
        expect(store.getState().reporter.readonly).toBe(true);

        store.dispatch(actions.setReadOnly('reporter', false));

        store.dispatch(actions.setReadOnly('assignee', true));

        store.dispatch(actions.setRequired('summary', true));
        store.dispatch(actions.setRequired('summary', false));

        store.dispatch(actions.setReadOnly('assignee', false));

    });

    it("verifies subscriptions are initially called", () => {
        const behaviours = [
            {
                field    : $('#summary'),
                fieldId  : 'summary',
                fieldType: 'com.atlassian.jira.issue.fields.SummarySystemField',
            }
        ];

        initialiseBehaviours(store, behaviours);

        store.dispatch(actions.addField({
            'summary': {
                readonly: true,
                value   : 'some summary',
            }
        }));

        const $field = $("#summary");
        expect($field.val()).toEqual('some summary');
        expect($field).toHaveAttr('readonly');

    });

    it("change events are triggered", () => {
        const behaviours = [
            {
                id       : 'summary',
                field    : $('#summary'),
                fieldId  : 'summary',
                fieldType: 'com.atlassian.jira.issue.fields.SummarySystemField',
            }
        ];

        initialiseBehaviours(store, behaviours);

        store.dispatch(actions.addField({
            'summary': {
                validator: 'server',
            }
        }));

        const $field = $("#summary");
    });

    it("considers merging values", () => {

        initialiseBehaviours(store, [
            {
                id       : 'summary',
                field    : $('#summary'),
                fieldId  : 'summary',
                fieldType: 'com.atlassian.jira.issue.fields.SummarySystemField',
            }
        ]);


        store.dispatch(actions.addField({
            'summary' : {
                readonly: true,
            }
        }));
    });

    it('checks immutable state', () => {

        const initialState = {
            items: {
                'reporter': {
                    readonly: false,
                },
                'summary' : {
                    readonly: false
                }
            }
        };


        initialState.items.summary.readonly = false;

        const updated = {
            items: {
                ...initialState.items,
                'summary': {
                    readonly: true
                }
            }
        };

        expect(initialState.items.summary.readonly).toBe(false);
        expect(updated.items.summary.readonly).toBe(true);
        expect(updated.items.reporter.readonly).toBe(false);

        const updatedAgain = {
            items: {
                ...initialState.items,
                'summary': {
                    readonly: false
                }
            }
        };

        expect(initialState).toEqual(updatedAgain);
        expect(initialState).not.toBe(updatedAgain);
    });
});
