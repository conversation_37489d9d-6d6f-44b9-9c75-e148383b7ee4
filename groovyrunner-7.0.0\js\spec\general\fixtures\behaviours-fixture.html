<form class="aui ajs-dirty-warning-exempt">

    <div class="field-group">
        <tr class="fieldArea" id="reporterFieldArea">
            <td class="fieldLabelArea">
                <label for="reporter">Reporter</label>
            </td>
            <td class="fieldValueArea">

                <select id="reporter"
                        name="reporter"
                        class="single-user-picker js-default-user-picker"
                        data-container-class="long-field"
                >

                    <optgroup id="reporter-group-suggested" label="$i18n.getText('user.picker.group.suggested')"
                              data-weight="0">

                        <option selected="selected"
                                class="current-user"
                                data-field-text="Mr Admin"
                                data-icon="http://localhost:8080/jira/secure/useravatar?size=xsmall&amp;avatarId=10340"
                                value="admin">Mr Admin
                        </option>

                    </optgroup>

                    <optgroup id="reporter-group-search" label="Search for user"
                              data-weight="1"></optgroup>

                </select>
                <div class="description">Search for user</div>
            </td>
        </tr>
    </div>

    <div class="field-group">
        <label for="summary">Summary</label>
        <input class="text long-field" id="summary" name="summary" type="text" value="">
    </div>


    <div class="field-group">
        <tr class="fieldArea" id="assigneeFieldArea">
            <td class="fieldLabelArea">
                <label for="assignee">
                    Assignee:
                </label>
            </td>
            <td class="fieldValueArea">

                <select id="assignee" name="assignee" class="single-user-picker js-assignee-picker"
                        data-show-dropdown-button="true" data-user-type="assignee" data-container-class="long-field">
                    <optgroup id="assignee-group-suggested" label="Suggestions" data-weight="0">
                        <option class="current-user" value="admin" data-field-text="Mr Admin"
                                data-field-label="Mr Admin - <EMAIL> (admin)"
                                data-icon="http://www.gravatar.com/avatar/64e1b8d34f425d19e1ee2ea7236d3028?d=mm&amp;s=16">
                            Mr
                            Admin
                        </option>
                        <option value="" data-field-text="Unassigned" data-field-label="Unassigned"
                                data-icon="http://localhost:8080/jira/secure/useravatar?size=xsmall&amp;avatarId=10123">
                            Unassigned
                        </option>
                        <option selected="selected" value="-1" data-field-text="Automatic" data-field-label="Automatic"
                                data-icon="http://localhost:8080/jira/secure/useravatar?size=xsmall&amp;avatarId=10123">
                            Automatic
                        </option>
                    </optgroup>
                </select><a href="#assignee" id="assign-to-me-trigger">Assign to me</a>
                <fieldset class="hidden parameters"><input type="hidden" title="projectKeys" value="JRA"/></fieldset>
            </td>
        </tr>
    </div>
</form>

