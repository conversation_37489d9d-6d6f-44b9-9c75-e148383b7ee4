<div id="my-fixture">

    <form class="aui">

        <div class="field-group aui-field-componentspicker frother-control-renderer">
            <label for="components">Component/s</label>

            <div class="ajs-multi-select-placeholder textarea long-field"></div>
            <select class="select hidden" id="components" multiple="multiple" name="components"
                    size="4"
                    data-remove-null-options="true" data-submit-input-val="true" data-input-text=""
                    data-create-permission="true">
                <option value="-1">
                    Unknown
                </option>
                <option title="Comp1  - a component for Comp1" value="10000">
                    Comp1
                </option>
                <option title="Comp2  - a component for Comp2" value="10001">
                    Comp2
                </option>
                <option title="Comp3  - a component for Comp3" value="10002">
                    Comp3
                </option>
            </select>

            <div class="description">Start typing to get a list of possible matches or press down to select.</div>
        </div>

        <div class="field-group">
            <label for="customfield_1">SelectListA</label>
            <select class="select cf-select" name="customfield_1" id="customfield_1">
                <option value="-1">None</option>
                <option value="1">AAA</option>
                <option value="2">BBB</option>
                <option value="3">CCC</option>
            </select>

            <div class="description">an eg custom field</div>
        </div>
    </form>
</div>
