<form class="aui">
    <tr class="fieldArea" id="reporterFieldArea">
        <td class="fieldLabelArea">
            &nbsp;
        </td>
        <td class="fieldValueArea">

            <select id="reporter"
                    name="reporter"
                    class="single-user-picker js-default-user-picker"
                    data-container-class="long-field"
            >

                <optgroup id="reporter-group-suggested" label="$i18n.getText('user.picker.group.suggested')"
                          data-weight="0">

                    <option selected="selected"
                            class="current-user"
                            data-field-text="Mr Admin"
                            data-icon="http://localhost:8080/jira/secure/useravatar?size=xsmall&amp;avatarId=10340"
                            value="admin">Mr Admin
                    </option>

                </optgroup>

                <optgroup id="reporter-group-search" label="$i18n.getText('user.picker.group.search')"
                          data-weight="1"></optgroup>

            </select>


            <div class="description">${i18n.getText('user.picker.ajax.desc')}</div>


        </td>
    </tr>

</form>

