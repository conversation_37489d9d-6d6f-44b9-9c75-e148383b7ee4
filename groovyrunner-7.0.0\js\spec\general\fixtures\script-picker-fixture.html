<form class="aui">
    <tr class="fieldArea" id="scriptPickerFieldArea">
        <td class="fieldLabelArea">
            &nbsp;
        </td>
        <td class="fieldValueArea">
            <div class="script-picker">
                <input style="max-width: 100%" type="text" class="text long-field scriptFileSource typeahead"
                       name="testScriptFile" id="testScriptFile"
                       data-sr-context="com.onresolve.scriptrunner.runner.stc.ScriptCompileContext.SCRIPT_CONSOLE_CONTEXT">
            </div>
        </td>
    </tr>
</form>
