<link type="text/css" , rel="stylesheet"
      href="http://localhost:8080/jira/download/contextbatch/css/_super/batch.css">

<form class="aui" id="#issue-create">
    <div class="field-group">
        <label for="summary">Summary<span class="aui-icon icon-required">Required</span></label>
        <input class="text long-field" id="summary" name="summary" type="text" value=""/>
    </div>

    <div class="field-group">
        <label for="customfield_1">SelectListA</label>
        <select class="select cf-select" name="customfield_1" id="customfield_1">
            <option value="-1">None</option>
            <option value="1">AAA</option>
            <option value="2">BBB</option>
            <option value="3">CCC</option>
        </select>

        <div class="description">an eg custom field</div>
    </div>


    <div class="field-group">
        <label for="customfield_2">MultiSelectA</label>
        <select class="select cf-select" id="customfield_2" multiple="multiple" name="customfield_2" size="5"
                data-remove-null-options="true" data-submit-input-val="true" data-input-text="">
            <option value="-1">None</option>
            <option value="1">AAA</option>
            <option value="2">BBB</option>
            <option value="3">CCC</option>
        </select>

        <div class="description">an eg custom field</div>
    </div>

    <div class="buttons-container form-footer">
        <div class="buttons">
            <input accesskey="s" class="aui-button" id="issue-create-submit" name="Create" title="Press Alt+s to submit this form"
                    type="submit" value="Create" />

            <a accesskey="`" class="aui-button aui-button-link cancel" href="default.jsp" id="issue-create-cancel"
                    title="Press Alt+` to cancel">Cancel</a>
        </div>
    </div>

</form>

