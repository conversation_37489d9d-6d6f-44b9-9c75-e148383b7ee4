describe("running the script console", function () {

    beforeEach(function (done) {
        spyOn(AJS.$, "ajax").and.callFake(function (params) {

            if (/Checker$/.test(params.url)) {

                return $.when({
                    compilationResult: {
                        "syntaxExceptions": [
                            {
                                "startColumn": 1,
                                "sourceLocator": "Script114.groovy",
                                "suppressed": [],
                                "message": "[Static type checking] - No such property: estimy for class: com.atlassian.jira.issue.Issue\n @ line 1, column 1.",
                                "endLine": 1,
                                "stackTraceDepth": 247,
                                "cause": null,
                                "originalMessage": "[Static type checking] - No such property: estimy for class: com.atlassian.jira.issue.Issue\n",
                                "fatal": false,
                                "localizedMessage": "[Static type checking] - No such property: estimy for class: com.atlassian.jira.issue.Issue\n @ line 1, column 1.",
                                "endColumn": 14,
                                "line": 1,
                                "startLine": 1
                            }
                        ],
                        "warnings": []
                    }
                }, "some text status", {status: 200});

            }
            else if (params.dataType !== "script") {
                var data = {"output": "4"};
                return $.when(data, "some text status", {status: 200});
            }
            else {
                return $.ajax(params)
            }
        });

        spyOn($, "ajax").and.callFake(function (params) {

            if (params.dataType !== "script") {
                var data = {"output": "4"};
                return $.when(data, "some text status", {status: 200});
            }
            else {
                return $.ajax(params)
            }
        });

        ScriptRunner.Base.isScriptConsole = function () { return true; };

        $("body").append((plugin.com.onresolve.scriptrunner.project({
            baseUrl: "/",
            isBuiltin: false,
            section: "script_console",
            title: "scripts"
        })));

        done();
    });
});

