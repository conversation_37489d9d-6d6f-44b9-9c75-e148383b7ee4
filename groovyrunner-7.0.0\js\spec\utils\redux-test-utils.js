import store from 'js/behaviours/create-store'

export function expectFieldToHaveValue(fieldId, callback) {
    return waitForSliceChange(`[${fieldId}][fulfillment]`, callback);
}

export function waitForSliceChange(slice, callback) {
    return new Promise(resolve => {
        const unsubscribe = store.whenever(slice, () => {
            return true
        }, (curState, prevState) => {
            callback();
            unsubscribe();
            resolve()
        });
    })
}

