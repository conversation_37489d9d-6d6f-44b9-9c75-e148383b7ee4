"use strict";(self.webpackJsonpScriptRunner=self.webpackJsonpScriptRunner||[]).push([["src_main_resources_js_monaco_MonacoEditorLoader_tsx"],{71237:(n,e,t)=>{t.r(e),t.d(e,{_forTests:()=>v,default:()=>g});var o=t(63844),r=t(72142),c=t(59482),i=t(66661),a=t(8163),u=t(36416),s=function(){return"".concat(u.V.staticResourcePrefix,"/../").concat(u.V.pluginVersion,"/_/download/resources/").concat(u.V.appKey,":")},d=t(6173),p=t(74703),l=function(){return l=Object.assign||function(n){for(var e,t=1,o=arguments.length;t<o;t++)for(var r in e=arguments[t])Object.prototype.hasOwnProperty.call(e,r)&&(n[r]=e[r]);return n},l.apply(this,arguments)},f=window.require,w=window.define;const g=function(n){var e=(0,r.I0)(),t=(0,r.v9)((0,c.Ie)(n.editorId));return o.createElement(i.MonacoEditor,l({},n,{serviceUrl:"".concat(AJS.contextPath(),"/rest/scriptrunner/latest/codeinsight"),loaderUrl:"".concat(s(),"vs/vs"),fullScreen:t,onRagStatusDidChange:function(t){return e((0,c.jn)({id:n.editorId,value:t}))},onParametersDidChange:function(t){return e((0,a.Z)({editorId:n.editorId,annotations:t}))},onToggleFullScreen:function(n){return e((0,c.ri)(n))},onigPath:"".concat(s(),"vscode-oniguruma/vscode-oniguruma/onig.wasm"),onMount:function(t,o){return(0,d.fu)(n.editorId,t,o,e)},afterMount:function(){window.require=f,window.define=w},tracking:new m}))};var m=function(){function n(){}return n.prototype.completionsOpen=function(n,e){(0,p.w)("trackMonaco",l({action:n},e))},n.prototype.completionSelected=function(n,e,t){(0,p.w)("trackMonaco",l(l({action:"completionSelected"},n),{snippet:e.kind===i.CompletionItemKind.Snippet,selectedIdx:t}))},n}(),v={OnPremCodeEditorTracking:m}}}]);