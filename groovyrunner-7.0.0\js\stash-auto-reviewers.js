require('./jquery.livequery');
require('../css/auto-reviewers.css');
require('wr-dependency!adminSettingsWebResources');
require('wr-dependency!com.atlassian.auiplugin:aui-labels');
require('wr-dependency!com.atlassian.bitbucket.bitbucket-web:global');

function mapUser(userList, isMandatory) {
    return _.map(userList, function (element) {
        return {
            id    : element.name,
            item  : element,
            text  : element.displayName,
            locked: isMandatory
        };
    });
}

function addRevPRCreate(userSelectElement, source, dest) {
    $.ajax(AJS.contextPath() + '/rest/scriptrunner-bitbucket/latest/auto-reviewers/byRef',
        {
            data   : {
                fromRepoId: source.repo,
                from      : source.branch,
                toRepoId  : dest.repo,
                to        : dest.branch
            },
            error  : function (xhr, status, errorMsg) {
                console.error(status + ' ' + errorMsg);
            },
            success: function (data) {
                let users = mapUser(data.mandatory, true).concat(mapUser(data.suggested, false));

                if (typeof users !== 'undefined' && users.length > 0) {
                    let userSelect = userSelectElement.data('select2');
                    users = users.concat(userSelect.data());
                    userSelect.data(users);
                    userSelectElement.addClass("sr-auto-reviewers-added");
                }
            }
        }
    );
}

const init = function () {

    // Wait for Bitbucket's built in default reviewers to be set, and then apply ours
    $(document).ajaxComplete(function(event, xhr, settings) {
        if(settings.url.includes("/rest/default-reviewers")) {
            $('div.pull-request-create-form #s2id_reviewers:visible').livequery(function () {
                addUsersToReviewers();
            });
        }
    })
};

const addUsersToReviewers = function () {
    const sourceRepo = $('input[name=fromRepoId]').val();
    const sourceBranch = $('input[name=fromBranch]').val();

    const destRepo = $('input[name=toRepoId]').val();
    const destBranch = $('input[name=toBranch]').val();

    const userSelectElement = $('.field-group.pull-request-reviewers #reviewers');
    if (userSelectElement.parent().className?.includes('adaptavist-sr') !== true) {
        userSelectElement.parent().addClass('adaptavist-sr')
    }

    if (sourceRepo && sourceBranch && destRepo && destBranch) {
        addRevPRCreate(userSelectElement, {
            repo  : sourceRepo,
            branch: sourceBranch
        }, {
            repo  : destRepo,
            branch: destBranch
        });
    }
};

AJS.$(document).ready(init);
