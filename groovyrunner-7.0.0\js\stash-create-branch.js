
define('feature/branch-creation/formXX', [
    'jquery',
    'underscore',
    'aui',
    'util/events',
    'util/ajax',
    'util/navbuilder',
    'util/dom-event',
    'model/page-state',
    'model/revision-reference',
    'model/repository',
    'feature/repository/branch-diagram',
    'feature/repository/branch-selector',
    'feature/repository/global-repository-selector',
    'widget/submit-spinner',
    'widget/simple-select'
], function (
    $,
    _,
    AJS,
    events,
    ajax,
    nav,
    domEvent,
    pageState,
    RevisionReference,
    Repository,
    BranchDiagram,
    BranchSelector,
    GlobalRepositorySelector,
    SubmitSpinner,
    SimpleSelect
    ) {

    console.log("hey ho");
    console.log(BranchSelector);

    $(function () {
        console.log("inside start");
    });
});

(function ($) {
    AJS.toInit(function () {


        console.log("bitbucket create branch start up");

        console.log("selected project: ", AJS.$("#repository-selector span:first").attr("data-id"));

        AJS.$("#branch-from-selector").click();

        AJS.$("a[data-id='refs/heads/develop']").click();



    });
}) (AJS.$);

require(['jquery', 'feature/repository/branch-selector'], function($, BranchSelector, BranchCreationForm) {
    console.log("sss", BranchSelector);

    // new BranchSelector();

    // console.log(BranchCreationForm);

});