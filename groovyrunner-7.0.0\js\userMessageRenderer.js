(function ($) {

    /**
     * Note... this should be called only enough, not more. Which is once per issue.
     *
     * The code below is the best thing I can come up with at the moment, bearing in mind the pageLoad event
     * is currently triggered twice:
     *
     * https://jira.atlassian.com/browse/JRA-42752
     *
     */
    AJS.toInit(function () {

        var displayMessages = function (event, $context, reason) {
            try {

                // Check for messages after inline edit or other panel refresh. #attachmentmodule was used because
                // it's the one panel that is refreshed after doing an inline edit in both the planning board
                // and the view issue screen.
                if (!!$context && typeof $context.is === "function" && !$context.is('div#addcomment, #attachmentmodule')) {
                    return
                }

                $.get(AJS.contextPath() + "/rest/scriptrunner/1.0/message",
                    {},
                    function (data, textStatus, jqXHR) {
                        if (jqXHR.status === 200 && jqXHR.getResponseHeader("content-type").indexOf("application/json") > -1) {
                            data = data || {};
                            $.each(data, function (i, j) {
                                require(['aui/flag'], function (flag) {
                                    flag(j);
                                });
                            });
                        } else if (jqXHR.status !== 204) {
                            console.log(jqXHR.status + " code returned. Error getting messages!");
                        }
                    }
                );
            }
            catch (e) {
                console.log("Error retrieving user messages via ScriptRunner");
                console.error(e);
            }
        };

        JIRA.bind(JIRA.Events.NEW_CONTENT_ADDED, displayMessages);
        JIRA.bind(JIRA.Events.ISSUE_REFRESHED, displayMessages);
        displayMessages(null, null, null);
    });
})(AJS.$);
