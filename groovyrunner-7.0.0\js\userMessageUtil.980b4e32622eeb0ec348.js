/*! For license information please see userMessageUtil.980b4e32622eeb0ec348.js.LICENSE.txt */
(self.webpackJsonpScriptRunner=self.webpackJsonpScriptRunner||[]).push([["userMessageUtil"],{88433:(e,t,n)=>{"use strict";n.d(t,{NU:()=>p,X_:()=>o,Xs:()=>d,bj:()=>a,or:()=>f,sd:()=>s,zM:()=>c});var r=n(91478),u="adaptavist-scriptrunner-behaviour-context-id",i="adaptavist-scriptrunner-context-issue-id",o=function(){return document.head.querySelector("meta[name=".concat(u,"]"))},s=function(e){return $(document.head).append("<meta name=".concat(u," content=").concat(e,">"))},c=function(){var e=o();e&&e.remove()},a=function(){return document.head.querySelector("meta[name=".concat(i,"]"))},d=function(e){return $(document.head).append("<meta name=".concat(i," content=").concat(e,">"))},p=function(){var e=a();e&&e.remove()},f=function(e){return e.startsWith(r.UI)?e.substring(r.UI.length):e}},91478:(e,t,n)=>{"use strict";n.d(t,{UI:()=>r});n(5667),jQuery;var r="sr-"},46086:(e,t,n)=>{"use strict";var r,u,i;u=[n(65311)],r=function(e,t){function n(e,t,n,r){return!(e.selector!=t.selector||e.context!=t.context||n&&n.$lqguid!=t.fn.$lqguid||r&&r.$lqguid!=t.fn2.$lqguid)}e.extend(e.fn,{livequery:function(t,u){var i,o=this;return e.each(r.queries,(function(e,r){if(n(o,r,t,u))return(i=r)&&!1})),(i=i||new r(o.selector,o.context,t,u)).stopped=!1,i.run(),o},expire:function(t,u){var i=this;return e.each(r.queries,(function(e,o){n(i,o,t,u)&&!i.stopped&&r.stop(o.id)})),i}});var r=e.livequery=function(t,n,u,i){var o=this;return o.selector=t,o.context=n,o.fn=u,o.fn2=i,o.elements=e([]),o.stopped=!1,o.id=r.queries.push(o)-1,u.$lqguid=u.$lqguid||r.guid++,i&&(i.$lqguid=i.$lqguid||r.guid++),o};r.prototype={stop:function(){var t=this;t.stopped||(t.fn2&&t.elements.each(t.fn2),t.elements=e([]),t.stopped=!0)},run:function(){var t=this;if(!t.stopped){var n=t.elements,r=e(t.selector,t.context),u=r.not(n),i=n.not(r);t.elements=r,u.each(t.fn),t.fn2&&i.each(t.fn2)}}},e.extend(r,{guid:0,queries:[],queue:[],running:!1,timeout:null,registered:[],checkQueue:function(){if(r.running&&r.queue.length)for(var e=r.queue.length;e--;)r.queries[r.queue.shift()].run()},pause:function(){r.running=!1},play:function(){r.running=!0,r.run()},registerPlugin:function(){e.each(arguments,(function(t,n){if(e.fn[n]&&!(e.inArray(n,r.registered)>0)){var u=e.fn[n];e.fn[n]=function(){var e=u.apply(this,arguments);return r.run(),e},r.registered.push(n)}}))},run:function(n){n!==t?e.inArray(n,r.queue)<0&&r.queue.push(n):e.each(r.queries,(function(t){e.inArray(t,r.queue)<0&&r.queue.push(t)})),r.timeout&&clearTimeout(r.timeout),r.timeout=setTimeout(r.checkQueue,20)},stop:function(n){n!==t?r.queries[n].stop():e.each(r.queries,r.prototype.stop)}}),r.registerPlugin("append","prepend","after","before","wrap","attr","removeAttr","addClass","removeClass","toggleClass","empty","remove","html","prop","removeProp"),e((function(){r.play()}))},void 0===(i="function"==typeof r?r.apply(t,u):r)||(e.exports=i)},64562:(e,t,n)=>{"use strict";var r=n(88433);n(46086),function(e){function t(){e(".sr-create-bound-issue").off("click").on("click",(function(t){function n(e,t){return decodeURIComponent((new RegExp("[?|&]"+t+"=([^&;]+?)(&|#|;|$)").exec(e)||[,""])[1].replace(/\+/g,"%20"))||null}var u=e(this);t.preventDefault();var i=JIRA.Forms.createCreateIssueForm({issueType:n(u.attr("href"),"issuetype"),pid:n(u.attr("href"),"pid")}),o=i.asDialog({id:"create-constrained-issue-dialog"});i.bind("contentRefreshed",function(e){(0,r.X_)()||(0,r.sd)((0,r.or)(e.attr("id"))),(0,r.bj)()||(0,r.Xs)(JIRA.Issue.getIssueId())}(u)),i.bind("sessionComplete",(function(){location.reload()})),o.bind("Dialog.hide",(function(){(0,r.zM)(),(0,r.NU)(),AJS.$(document).unbind("QuickCreateIssue.validationError")})),o.show()}))}JIRA.bind(JIRA.Events.NEW_CONTENT_ADDED,(function(n,r,u){window.location.href.indexOf("RapidBoard.jspa?")>-1&&e("#ghx-detail-issue").livequery((function(){t()})),u!==JIRA.CONTENT_ADDED_REASON.pageLoad&&u!==JIRA.CONTENT_ADDED_REASON.panelRefreshed||t()}))}(AJS.$)},40517:(e,t,n)=>{"use strict";!function(e){AJS.toInit((function(){var t=function(t,r,u){try{if(r&&"function"==typeof r.is&&!r.is("div#addcomment, #attachmentmodule"))return;e.get(AJS.contextPath()+"/rest/scriptrunner/1.0/message",{},(function(t,r,u){200===u.status&&u.getResponseHeader("content-type").indexOf("application/json")>-1?(t=t||{},e.each(t,(function(e,t){Promise.resolve().then((function(){var e=[n(38860)];(function(e){e(t)}).apply(null,e)})).catch(n.oe)}))):204!==u.status&&console.log(u.status+" code returned. Error getting messages!")}))}catch(e){console.log("Error retrieving user messages via ScriptRunner"),console.error(e)}};JIRA.bind(JIRA.Events.NEW_CONTENT_ADDED,t),JIRA.bind(JIRA.Events.ISSUE_REFRESHED,t),t(0,null)}))}(AJS.$)},5667:(e,t,n)=>{e.exports=n},65311:e=>{"use strict";e.exports=jQuery},38860:e=>{"use strict";e.exports=require("aui/flag")}},e=>{var t=t=>e(e.s=t);t(40517),t(64562)}]);