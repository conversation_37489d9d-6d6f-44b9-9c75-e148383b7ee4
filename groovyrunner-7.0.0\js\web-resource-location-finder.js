if (typeof LOCATION_FINDER === "undefined") {
    LOCATION_FINDER = {};
    LOCATION_FINDER.contexts = new Set();
}

(function ($, _) {
    AJS.toInit(function () {

        // frag finder
        // currently this is missing fullpage-editor and things that are loaded async, probably pattern doesn't match
        // use setInterval to run this several times...
        _.each($("link"), function (link) {
            var $link = $(link);

            var href = $link.attr("href");
            if (href.indexOf("download/contextbatch/css") > -1) {

                var myRegexp = /.*download\/contextbatch\/css\/(.*?)\/batch.css.*/g;
                var match = myRegexp.exec(href);
                var cssBatch = match[1];

                var contexts =  cssBatch.split(",");
                _.each(contexts, function (context) {
                    LOCATION_FINDER.contexts.add(context);

                });

                var finderElement = "<div id='web-resource-fragment-item' class='fragment-item'>Web resource: locations:" + Array.from(LOCATION_FINDER.contexts.values()).join(", ") + "</div>";

                if ($("#web-resource-fragment-item").length > 0) {
                    $("#web-resource-fragment-item").replaceWith(finderElement);
                }
                else {
                    $("body").prepend(finderElement);
                }
            }
        });
    });
})(AJS.$, _);
