import 'wr-dependency!com.atlassian.auiplugin:ajs';
import 'wr-dependency!com.atlassian.auiplugin:dialog2';
import 'wr-dependency!web-item-response-renderer';
import 'wr-dependency!jira.webresources:util';
import {platformSpecific} from "./admin/platform";
import {findElementAsync} from './admin/util/DomUtils'
import {srLinkIdPrefix} from "./fragmentHelpers/web-fragment-helper-shared";

(function ($) {
    $(function () {
        const webItemsKey = `com.onresolve.${platformSpecific({
            bitbucket: 'stash',
            default  : PLATFORM_NAME
        })}.groovy.groovyrunner:web-item-response-renderer.web-item-actions-data-provider`;
        const webItems = WRM.data.claim(webItemsKey);

        const initialise = function (event, $context, reason) {

            if (reason === "panelRefreshed" || !reason) {

                $.each(webItems, (i, item) => register(item.action, item.id));

                // this needs to be added to a static resource, unless we automatically wire Close buttons to close the dialog
                AJS.dialog2.off('show').on("show", function (e) {
                    var targetId = e.target.id;
                    if (targetId === "sr-dialog") {
                        var srDialog = AJS.dialog2(e.target);
                        $(e.target).find("#dialog-close-button").click(function (e) {
                            e.preventDefault();
                            srDialog.hide();
                            srDialog.remove();
                        });
                    }
                });
            }
        };

        $(document).bind("newContentAdded", initialise);
        initialise(null, $(document), null);

        function renderDialog(anchor) {
            const postActionFn = anchor.data("postActionFn");
            AJS.$.ajax({
                type    : "GET",
                dataType: "html",
                url     : anchor.attr("href")
            }).done(function (data) {
                AJS.dialog2($(data)).show();
            }).fail(function (jqXHR, textStatus, errorThrown) {
                console.warn("Failed to execute remote:", errorThrown);
            }).always(() => {
                if (typeof postActionFn === 'function') {
                    postActionFn()
                }
            });
        }

        function renderFlag(anchor) {
            const postActionFn = anchor.data("postActionFn");
            require(['aui/flag'], function (flag) {
                AJS.$.ajax({
                    type    : "GET",
                    dataType: "json",
                    url     : anchor.attr("href")
                }).done(function (data) {
                    flag(data);
                }).fail(function (jqXHR, textStatus, errorThrown) {
                    console.warn("Failed to execute remote:", errorThrown);
                }).always(() => {
                    if (typeof postActionFn === 'function') {
                        postActionFn()
                    }
                });
            });
        }

        async function register(action, id) {
            const frontendIdentifier = srLinkIdPrefix + id
            const selector = `.${frontendIdentifier},#${frontendIdentifier}`
            await findElementAsync(selector, {subtree: true})

            if (action === "RUN_CODE_SHOW_DIALOG") {
                bindEventHandlerTo(selector, renderDialog);
            }
            if (action === "RUN_CODE_SHOW_FLAG") {
                bindEventHandlerTo(selector, renderFlag);
            }
        }

        function bindEventHandlerTo(selector, callback) {
            var element = $(document);
            element.off('click.sr-fragments', selector).on("click.sr-fragments", selector, clickHandler(callback));

            element.children().off('click.sr-fragments', selector).on("click.sr-fragments", selector, clickHandler(callback));
        }

        function clickHandler(callback) {
            return function (e) {
                e.preventDefault();
                // the following code allows us to find the element with the closest href w.r.t
                // as in some locations (for example dropdowns and repo actions in BBS)
                // the target element that is clicked can either be the <a href="link"> or <li> element
                const $targetElement = $(e.target);
                const ancestorAnchor = $targetElement.closest("[href]");
                const childAnchor = $targetElement.find("[href]").andSelf();

                callback(ancestorAnchor.length !== 0 ? ancestorAnchor : childAnchor);

                // this fires twice otherwise
                return false;
            }
        }
    });
})(AJS.$);
