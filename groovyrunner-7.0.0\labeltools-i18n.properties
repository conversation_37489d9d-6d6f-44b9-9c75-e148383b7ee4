com.adaptavist.confluence.labeltools.add-label.label=Add Label
com.adaptavist.confluence.labeltools.add-label.desc=Automatically add labels to a page
com.adaptavist.confluence.labeltools.add-label.param.labels.label=Labels
com.adaptavist.confluence.labeltools.add-label.param.labels.desc=A comma separated list of labels to apply
com.adaptavist.confluence.labeltools.choose-label.label=Choose Label
com.adaptavist.confluence.labeltools.choose-label.desc=Add labels to a page by choosing from a set of labels
com.adaptavist.confluence.labeltools.choose-label.param.title.label=Title
com.adaptavist.confluence.labeltools.choose-label.param.title.desc=The title to show above the drop-down list of labels
com.adaptavist.confluence.labeltools.choose-label.param.labels.label=Labels
com.adaptavist.confluence.labeltools.choose-label.param.labels.desc=A comma separated list of labels that can be applied
com.adaptavist.confluence.labeltools.choose-label.param.descriptions.label=Descriptions
com.adaptavist.confluence.labeltools.choose-label.param.descriptions.desc=A comma separated list of descriptions that can be applied to labels. If left empty, the description of each label will be the same as the label
com.adaptavist.confluence.labeltools.choose-label.param.multiselect.label=Multiselect
com.adaptavist.confluence.labeltools.choose-label.param.multiselect.desc=Allow multiple labels to be added at the same time
com.adaptavist.confluence.labeltools.choose-label.param.labelsuggest.label=Show suggested labels on the macro
com.adaptavist.confluence.labeltools.choose-label.param.labelsuggest.desc=Allow the system to suggests labels on the macro based on the content of the page
com.adaptavist.confluence.labeltools.choose-label.param.buttonLabel.label=Button text
com.adaptavist.confluence.labeltools.choose-label.param.buttonLabel.desc=Add custom text to the add button. Default is "Add Label"
com.adaptavist.confluence.labeltools.choose-label.param.showSuggestedLabelsOnDialog.label=Show suggested labels on the label dialog
com.adaptavist.confluence.labeltools.choose-label.param.showSuggestedLabelsOnDialog.desc=Allow the system to suggests labels on the label dialog based on the content of the page
com.adaptavist.confluence.labeltools.error.msgBoxtitle=Label Tools for ScriptRunner Confluence error
com.adaptavist.confluence.labeltools.preview.message=Preview not available
com.adaptavist.confluence.labeltools.error.descriptions=The number of labels and descriptions must match

com.adaptavist.confluence.labeltools.label.dialog.heading=with Suggestions
com.adaptavist.confluence.labeltools.choose-label.suggested-labels.group.heading=Suggested Labels
