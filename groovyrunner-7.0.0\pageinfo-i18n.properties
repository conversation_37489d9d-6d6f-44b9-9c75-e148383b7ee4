# com.adaptavist.confluence.pageinfo.PageInfoMacro
com.adaptavist.confluence.pageinfo.page-info.label=Page Info
com.adaptavist.confluence.pageinfo.page-info.desc=Displays information about pages and blog posts.
com.adaptavist.confluence.pageinfo.page-info.param.infoType.label=Information Type:
com.adaptavist.confluence.pageinfo.page-info.param.infoType.desc=Specify the type of information to be displayed.
com.adaptavist.confluence.pageinfo.page-info.param.page.label=Page:
com.adaptavist.confluence.pageinfo.page-info.param.page.desc=The name of the page to display information for. Also accepts $self and $parent.
com.adaptavist.confluence.pageinfo.page-info.param.dateFormat.label=Date Format:
com.adaptavist.confluence.pageinfo.page-info.param.dateFormat.desc=Allows to override default date display format, eg: dd MMMM yyyy - hh:mm aa.
com.adaptavist.confluence.pageinfo.page-info.param.type.label=Type:
com.adaptavist.confluence.pageinfo.page-info.param.type.desc=For those display types that produce a list of information.
com.adaptavist.confluence.pageinfo.page-info.param.prefix.label=Prefix:
com.adaptavist.confluence.pageinfo.page-info.param.prefix.desc=When displaying labels, dates, or version numbers, a prefix can be inserted beforehand.
com.adaptavist.confluence.pageinfo.page-info.param.reverse.label=Reverse Order:
com.adaptavist.confluence.pageinfo.page-info.param.reverse.desc=When displaying a list of items, the order can be reversed by setting the parameter.
com.adaptavist.confluence.pageinfo.page-info.param.count.label=Count:
com.adaptavist.confluence.pageinfo.page-info.param.count.desc=Limits the number of items shown in a list.
com.adaptavist.confluence.pageinfo.page-info.param.showComments.label=Show Comments:
com.adaptavist.confluence.pageinfo.page-info.param.showComments.desc=Displays the comments that accompany an update.
com.adaptavist.confluence.pageinfo.page-info.param.showVersions.label=Show Versions:
com.adaptavist.confluence.pageinfo.page-info.param.showVersions.desc=Displays the version numbers.
# Error messages
com.adaptavist.confluence.pageinfo.page-info.error.msgBoxtitle=Page Info for ScriptRunner Confluence error
com.adaptavist.confluence.pageinfo.page-info.error.msgBox=Page Info requires a license for <a href="https://marketplace.atlassian.com/plugins/com.onresolve.confluence.groovy.groovyrunner/server/overview">ScriptRunner for Confluence</a>
# com.adaptavist.confluence.pageinfo.VersionHistoryMacro
com.adaptavist.confluence.pageinfo.version-history.label=Version History
com.adaptavist.confluence.pageinfo.version-history.desc=Tabular display of page versions showing date, author and update comment.
com.adaptavist.confluence.pageinfo.version-history.param.page.label=Page:
com.adaptavist.confluence.pageinfo.version-history.param.page.desc=The name of the page you wish to display information for. Also accepts $self and $parent
com.adaptavist.confluence.pageinfo.version-history.param.dateFormat.label=Date Format:
com.adaptavist.confluence.pageinfo.version-history.param.dateFormat.desc=Allows you to override the format used to display dates. By default, dates and times are shown using the format defined in Confluences General Configuration. This can be altered however by using the date formats syntax, eg: dd MMMM yyyy - hh:mm aa
com.adaptavist.confluence.pageinfo.version-history.param.reverse.label=Reverse Order:
com.adaptavist.confluence.pageinfo.version-history.param.reverse.desc=When displaying a list of items, you can set this parameter to true to reverse the order of those items. Defaults to false
com.adaptavist.confluence.pageinfo.version-history.param.first.label=First:
com.adaptavist.confluence.pageinfo.version-history.param.first.desc=Specify how many results are displayed. Display only the first X entries
adaptavist.version-history.table.summary=Version History
adaptavist.version-history.table.heading.version=Version
adaptavist.version-history.table.heading.date=Date
adaptavist.version-history.table.heading.author=Author
adaptavist.version-history.table.heading.comment=Comment
com.onresolve.confluence.groovy.groovyrunner.page-info.param.infoType.Commenters.desc=Commenters
com.onresolve.confluence.groovy.groovyrunner.page-info.param.infoType.Created\u0020by.desc=Created by
com.onresolve.confluence.groovy.groovyrunner.page-info.param.infoType.Create\u0020date.desc=Create date
com.onresolve.confluence.groovy.groovyrunner.page-info.param.infoType.Current\u0020version.desc=Current version
com.onresolve.confluence.groovy.groovyrunner.page-info.param.infoType.Diffs.desc=Diffs
com.onresolve.confluence.groovy.groovyrunner.page-info.param.infoType.Labels.desc=Labels
com.onresolve.confluence.groovy.groovyrunner.page-info.param.infoType.Modified\u0020by.desc=Modified by
com.onresolve.confluence.groovy.groovyrunner.page-info.param.infoType.Modified\u0020date.desc=Modified date
com.onresolve.confluence.groovy.groovyrunner.page-info.param.infoType.Modified\u0020users.desc=Modified users
com.onresolve.confluence.groovy.groovyrunner.page-info.param.infoType.Page\u0020id.desc=Page id
com.onresolve.confluence.groovy.groovyrunner.page-info.param.infoType.Participants.desc=Participants
com.onresolve.confluence.groovy.groovyrunner.page-info.param.infoType.Title.desc=Title
com.onresolve.confluence.groovy.groovyrunner.page-info.param.infoType.Tiny\u0020url.desc=Tiny url
com.onresolve.confluence.groovy.groovyrunner.page-info.param.infoType.Versions.desc=Versions
com.onresolve.confluence.groovy.groovyrunner.page-info.param.type.Flat.desc=Flat
com.onresolve.confluence.groovy.groovyrunner.page-info.param.type.List.desc=List

