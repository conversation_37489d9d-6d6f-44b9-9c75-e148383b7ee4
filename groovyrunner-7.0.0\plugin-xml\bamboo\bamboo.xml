<atlassian-plugin plugins-version="2">

    <web-panel key="exitSwitchUser" location="header.global.banner">
        <description>ScriptRunner Switch User - Exit Banner</description>
        <condition class="com.onresolve.scriptrunner.fragments.SwitchUserExitBannerCondition" />
        <context-provider class="com.onresolve.scriptrunner.fragments.SwitchUserExitBannerContextProvider" />
        <resource type="velocity" name="view" location="templates/switchuser/exit-switch-user.vm" />
    </web-panel>

    <web-section key="scriptrunner_section_conf" name="ScriptRunner Admin Section" location="system.admin"
                 i18n-name-key="item.groovy.runner.label"  weight="150" >
        <label key="item.groovy.runner.websection.label" />
    </web-section>

    <web-item key="script_console_conf" name="Display Script Console Web Item" section="system.admin/scriptrunner_section_conf"
              weight="15" >
        <label key="item.groovy.runner.label"/>
        <link linkId="script_console_conf">/plugins/servlet/scriptrunner/admin/console</link>
    </web-item>

    <web-item key="builtin_scripts_conf" name="Display Built-in Scripts Console Web Item" section="system.admin/scriptrunner_section_conf"
              weight="15" >
        <label key="item.builtin.scripts.label"/>
        <link linkId="builtin_scripts_conf">/plugins/servlet/scriptrunner/admin/builtin</link>
    </web-item>

    <web-item key="bamboo_events_conf" name="Display Event Handlers" section="system.admin/scriptrunner_section_conf"
              weight="15" >
        <label key="item.stashevents.label"/>
        <link linkId="confluence_events_conf">/plugins/servlet/scriptrunner/admin/events</link>
        <description>Manage script event handlers</description>
        <tooltip key="bitbucket.events.tooltip"/>
    </web-item>

    <web-item key="fragments_conf" name="Display Fragments" section="system.admin/scriptrunner_section_conf"
              weight="15" >
        <label key="item.fragments.label"/>
        <link linkId="fragments_conf">/plugins/servlet/scriptrunner/admin/fragments</link>
        <description>Manage fragments</description>
    </web-item>

    <web-item key="scheduled_jobs_conf" name="Display Scheduled Jobs" section="system.admin/scriptrunner_section_conf"
              weight="15" >
        <label key="item.script_jobs.label"/>
        <link linkId="scheduled_jobs_conf">/plugins/servlet/scriptrunner/admin/jobs</link>
        <description>Manage scheduled jobs</description>
    </web-item>

    <web-item key="script_endpoints_conf" name="Display Endpoints Box" section="system.admin/scriptrunner_section_conf" weight="20">
        <label key="item.script.endpoints.label"/>
        <link linkId="script_endpoints_conf">/plugins/servlet/scriptrunner/admin/restendpoints</link>
    </web-item>

    <web-item key="scripteditor" name="Display Script Editor Web Item" section="system.admin/scriptrunner_section_conf" weight="22">
        <label key="item.script.runner.editor.label"/>
        <link linkId="scriptEditor">/plugins/servlet/scriptrunner/admin/scriptEditor</link>
        <description>Create and edit scripts</description>
    </web-item>

    <web-item key="scriptrunnersettings" name="ScriptRunner Settings" section="system.admin/scriptrunner_section_conf" weight="30" >
        <label key="item.script.runner.settings.label"/>
        <link linkId="scriptrunnersettings_link">/plugins/servlet/scriptrunner/admin/settings</link>
        <condition class="com.onresolve.scriptrunner.webfragment.conditions.HasSystemAdminPermissionCondition"/>
    </web-item>

    <rest name="ScriptRunner REST Resource - Bamboo"
          key="scriptrunner-rest-resource-bamboo"
          path="/scriptrunner-bamboo"
          version="1.0">
        <description>ScriptRunner REST resource - Bamboo</description>
        <package>com.onresolve.scriptrunner.filters</package>
        <package>com.onresolve.scriptrunner.runner.rest.common.error</package>
        <package>com.onresolve.scriptrunner.runner.rest.bamboo</package>
        <package>com.onresolve.scriptrunner.runner.rest.common.providers.writer</package>
    </rest>

    <preBuildQueuedAction key="filteringPreBuildQueuedAction" name="SR Filtering PreBuild Queued Action"
                          class="com.onresolve.scriptrunner.bamboo.tasks.SrFilteringPreBuildQueuedAction">
        <description>Filters build definitions when queued</description>
    </preBuildQueuedAction>

    <taskType key="task.configuration" name="ScriptRunner Condition Tasks" class="com.onresolve.scriptrunner.bamboo.tasks.ScriptRunnerAgentInternalTask">
        <description>A range of configurable tasks for conditionally executing tasks or jobs</description>
        <configuration class="com.onresolve.scriptrunner.bamboo.tasks.config.ConditionalExecutionTaskConfigurator"/>
        <param name="runtimeClass">com.onresolve.bamboo.stub.SkipTask.SkipTaskTask</param>
        <resource type="freemarker" name="edit" location="templates/editSkipTaskTask.ftl"/>

        <!--todo: icon wrong size and generic-->
        <resource type="download" name="icon" location="images/SR-Task.png"/>
    </taskType>

    <taskType key="task.builder.task" name="ScriptRunner Scriptable Tasks" class="com.onresolve.scriptrunner.bamboo.tasks.ScriptRunnerAgentInternalTask">
        <description>Tasks for creating and manipulating other tasks at runtime</description>
        <configuration class="com.onresolve.scriptrunner.bamboo.tasks.config.ScriptableTaskConfigurator"/>
        <param name="runtimeClass">com.onresolve.bamboo.stub.SkipTask.SkipTaskTask</param>
        <resource type="freemarker" name="edit" location="templates/editSkipTaskTask.ftl"/>
        <resource type="download" name="icon" location="images/SR-Task.png"/>
    </taskType>

    <!--Matrix tasks disabled-->
    <!--<taskType key="task.configuration.matrix" name="ScriptRunner Matrix Build Task" class="com.onresolve.scriptrunner.bamboo.tasks.ScriptRunnerAgentInternalTask">
        <description>Execute a build multiple times using a matrix of parameters</description>
        <configuration class="com.onresolve.scriptrunner.bamboo.tasks.config.MatrixTaskConfigurator"/>
        <param name="runtimeClass">com.onresolve.bamboo.stub.SkipTask.SkipTaskTask</param>
        <resource type="freemarker" name="edit" location="templates/editSkipTaskTask.ftl"/>
        <resource type="download" name="icon" location="images/SR-Enterprise.png"/>
    </taskType>-->

</atlassian-plugin>
