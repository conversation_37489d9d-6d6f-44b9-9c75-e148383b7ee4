<atlassian-plugin plugins-version="2">

    <web-section key="scriptrunner_section_stash" name="ScriptRunner Admin Section" location="atl.admin"
                 i18n-name-key="item.groovy.runner.label" weight="150" application="bitbucket">
        <label key="item.groovy.runner.websection.label"/>
    </web-section>

    <web-panel key="exitSwitchUser" location="bitbucket.head">
        <description>ScriptRunner Switch User - Exit Banner</description>
        <condition class="com.onresolve.scriptrunner.fragments.SwitchUserExitBannerCondition" />
        <context-provider class="com.onresolve.scriptrunner.fragments.SwitchUserExitBannerContextProvider" />
        <resource type="velocity" name="view" location="templates/switchuser/exit-switch-user.vm" />
    </web-panel>

    <web-item key="scriptrunnerbrowse" name="ScriptRunner Browse" section="atl.admin/scriptrunner_section_stash"
              weight="10" >
        <label key="item.script.runner.browse.label"/>
        <link linkId="scriptrunnerbrowse_link">/plugins/servlet/scriptrunner/admin/browse</link>
        <description>Browse ScriptRunner scripts</description>
        <tooltip key="item.script.runner.browse.tooltip"/>
    </web-item>

    <web-item key="scriptconsole_stash" name="Display Script Console Web Item"
              section="atl.admin/scriptrunner_section_stash"
              weight="15" application="bitbucket">
        <label key="item.groovy.runner.label"/>
        <link linkId="script_console">/plugins/servlet/scriptrunner/admin/console</link>
        <description>Run scripts in your application instance</description>
        <tooltip key="item.groovy.runner.tooltip"/>
        <condition class="com.onresolve.scriptrunner.permissions.ScriptRunnerUseCondition" />
    </web-item>

    <web-item key="builtinscripts_stash" name="Display Built-in Scripts Console Web Item"
              section="atl.admin/scriptrunner_section_stash"
              weight="20" application="bitbucket">
        <label key="item.builtin.scripts.label"/>
        <link linkId="builtin_scripts">/plugins/servlet/scriptrunner/admin/builtin</link>
        <tooltip key="item.builtin.scripts.tooltip"/>
        <description key="item.builtin.scripts.tooltip"/>
    </web-item>

    <web-item key="switch_user_shortcut" name="Switch User" section="atl.admin/scriptrunner_section_stash"
              weight="80">
        <label key="item.script.runner.shortcut.switch.user.label"/>
        <link linkId="switch_user_shortcut_link">
            /plugins/servlet/scriptrunner/admin/builtin/exec/com.onresolve.scriptrunner.canned.bitbucket.admin.SwitchUser
        </link>
        <condition class="com.onresolve.scriptrunner.switchuser.SwitchUserShortcutCondition"/>
    </web-item>

    <web-item key="bitbucket_scheduled_jobs" name="Display Scheduled Jobs" section="atl.admin/scriptrunner_section_stash"
              weight="25">
        <label key="item.script_jobs.label"/>
        <link linkId="bitbucket_scheduled_jobs">/plugins/servlet/scriptrunner/admin/jobs</link>
        <description>Manage scheduled jobs</description>
        <tooltip key="item.script_jobs.tooltip"/>
    </web-item>

    <web-item key="stash_events" name="Display Event Handlers" section="atl.admin/scriptrunner_section_stash"
              weight="30" application="bitbucket">
        <label key="item.stashevents.label"/>
        <link linkId="stash_events">/plugins/servlet/scriptrunner/admin/listeners</link>
        <description>Manage listeners</description>
        <tooltip key="bitbucket.events.tooltip"/>
    </web-item>

    <web-item key="pre_hooks_stash" name="Display Script Hooks" section="atl.admin/scriptrunner_section_stash"
              weight="35" application="bitbucket">
        <label key="item.prehooks.label"/>
        <link linkId="prehooks">/plugins/servlet/scriptrunner/admin/prehooks</link>
        <tooltip key="bitbucket.prehooks.tooltip"/>
        <description key="bitbucket.prehooks.tooltip"/>
    </web-item>

    <web-item key="post_hooks_stash" name="Display Script Hooks" section="atl.admin/scriptrunner_section_stash"
              weight="40" application="bitbucket">
        <label key="item.posthooks.label"/>
        <link linkId="posthooks">/plugins/servlet/scriptrunner/admin/posthooks</link>
        <tooltip key="bitbucket.posthooks.tooltip"/>
        <description key="bitbucket.posthooks.tooltip"/>
    </web-item>

    <web-item key="mergechecks_stash" name="Display Script Hooks" section="atl.admin/scriptrunner_section_stash"
              weight="45" application="bitbucket">
        <label key="item.mergecheck.label"/>
        <link linkId="mergechecks">/plugins/servlet/scriptrunner/admin/mergechecks</link>
        <tooltip key="bitbucket.mergechecks.tooltip"/>
        <description key="bitbucket.mergechecks.tooltip"/>
    </web-item>

    <web-item key="bitbucket_fragments" name="Display Fragments" section="atl.admin/scriptrunner_section_stash"
              weight="50" >
        <label key="item.fragments.label"/>
        <link linkId="fragments">/plugins/servlet/scriptrunner/admin/fragments</link>
        <description>Manage fragments</description>
        <tooltip key="item.fragments.tooltip"/>
    </web-item>

    <!-- Disable scheduled jobs feature until all items in SRBITB-202 are complete -->


    <web-item key="bitbucket_rest_endpoints" name="Display Endpoints Box" section="atl.admin/scriptrunner_section_stash"
              weight="55">
        <label key="item.script.endpoints.label"/>
        <link linkId="script_endpoints">/plugins/servlet/scriptrunner/admin/restendpoints</link>
        <tooltip key="item.script.endpoints.tooltip"/>
    </web-item>

    <web-item key="resources" name="Display Resources" section="atl.admin/scriptrunner_section_stash" weight="60">
        <label key="item.script.resources.label"/>
        <link linkId="resources">/plugins/servlet/scriptrunner/admin/resources</link>
        <tooltip key="item.script.resources.tooltip"/>
        <description>Manage connections to databases</description>
    </web-item>

    <web-item key="scriptEditor" name="Script Editor" section="atl.admin/scriptrunner_section_stash" weight="65">
        <label key="item.script.runner.editor.label"/>
        <link linkId="scriptEditor">/plugins/servlet/scriptrunner/admin/scriptEditor</link>
        <tooltip key="item.script.editor.tooltip"/>
        <description>Create and edit scripts</description>
        <condition class="com.onresolve.scriptrunner.permissions.ScriptRunnerUseCondition" />
    </web-item>

    <web-item key="scriptrunnersettings" name="ScriptRunner Settings" section="atl.admin/scriptrunner_section_stash"
              weight="70" >
        <label key="item.script.runner.settings.label"/>
        <link linkId="scriptrunnersettings_link">/plugins/servlet/scriptrunner/admin/settings</link>
        <condition class="com.atlassian.bitbucket.web.conditions.HasGlobalPermissionCondition">
            <param name="permission">SYS_ADMIN</param>
        </condition>
        <tooltip key="item.script.runner.settings.label"/>
    </web-item>

    <!--These two are per-repo hooks that can be enabled and disabled by project admins, are not currently in use-->
    <!--
        <repository-hook name="Configurable Script Pre-receive hook"
                         key="script-receive-repository-hook"
                         class="com.onresolve.scriptrunner.bitbucketcket.ScriptedPreReceiveRepoHook">
            <description key="script-receive-repository-hook.description">
                Scriptable pre-receive hook
            </description>
            <icon>icon-example.png</icon>
            <config-form name="Ref Hook Config" key="refHook-config">
                <view>com.atlassian.bitbucket.repository.hook.ref.formContents</view>
                <directory location="/com/onresolve/scriptrunner/templates/soy/sections/bitbucket/"/>
            </config-form>
            &lt;!&ndash; Validators can be declared separately &ndash;&gt;
            <validator>com.onresolve.scriptrunner.bitbucketcket.ScriptedPreReceiveRepoHook</validator>
        </repository-hook>

    <repository-hook key="examplehook" name="Enforce Reviewers" class="com.onresolve.scriptrunner.bitbucketcket.RepoMergeCheck">
        <description>Enforces that pull requests must have a minimum number of acceptances before they can be merged.</description>
        <icon>icons/example.png</icon>
        <config-form name="Simple Hook Config" key="simpleHook-config">
            <view>com.atlassian.bitbucket.repository.hook.ref.mergeCheckformContents</view>
            <directory location="/com/onresolve/scriptrunner/templates/soy/sections/bitbucket/"/>
        </config-form>
        &lt;!&ndash; Validators can be declared separately &ndash;&gt;
        <validator>com.onresolve.scriptrunner.bitbucketcket.RepoMergeCheck</validator>
    </repository-hook>
    -->

    <web-section key="scriptrunner_repo_section_stash" name="ScriptRunner Repo Admin Section" location="bitbucket.repository.settings.panel"
                 i18n-name-key="item.groovy.runner.label" weight="150" >
        <label key="item.groovy.runner.websection.label"/>

        <condition class="com.onresolve.scriptrunner.settings.condition.RestrictRepositoryOrProjectAccessCondition" />
    </web-section>

    <web-item key="repository-pre-receive-hooks" name="Pre receive link" weight="40" section="bitbucket.repository.settings.panel/scriptrunner_repo_section_stash">
        <label key="item.prehooks.label"/>
        <tooltip key="bitbucket.prehooks.tooltip"/>
        <description key="bitbucket.prehooks.tooltip"/>
        <!--using all these path params is because you cannot use withParam twice, it encodes the ampersand-->
        <link linkId="prehooks">/plugins/servlet/scriptrunner/repoadmin/$repository.project.key/$repository.slug/prehooks</link>
    </web-item>

    <web-item key="repository-post-receive-hooks" name="Post receive link" weight="40" section="bitbucket.repository.settings.panel/scriptrunner_repo_section_stash">
        <label key="item.posthooks.label"/>
        <tooltip key="bitbucket.posthooks.tooltip"/>
        <description key="bitbucket.posthooks.tooltip"/>
        <link linkId="posthooks">/plugins/servlet/scriptrunner/repoadmin/$repository.project.key/$repository.slug/posthooks</link>
    </web-item>

    <web-item key="repository-mergecheck-hooks" name="Mergecheck link" weight="40" section="bitbucket.repository.settings.panel/scriptrunner_repo_section_stash">
        <label key="item.mergecheck.label"/>
        <tooltip key="bitbucket.mergechecks.tooltip"/>
        <description key="bitbucket.mergechecks.tooltip"/>
        <link linkId="mergechecks">/plugins/servlet/scriptrunner/repoadmin/$repository.project.key/$repository.slug/mergechecks</link>
    </web-item>

    <web-item key="repository-event-handlers" name="Event Handlers link" weight="40" section="bitbucket.repository.settings.panel/scriptrunner_repo_section_stash">
        <label>Listeners</label>
        <tooltip>Listeners</tooltip>
        <link linkId="stash_events">/plugins/servlet/scriptrunner/repoadmin/$repository.project.key/$repository.slug/listeners</link>
    </web-item>

    <servlet name="Redirect Servlet" key="redirect-servlet" class="com.onresolve.scriptrunner.runner.RedirectServlet">
        <description>Redirect servlet</description>
        <url-pattern>/scriptrunner/redirect/**</url-pattern>
    </servlet>

    <web-section key="scriptrunner_project_section_stash" name="ScriptRunner Project Admin Section" location="bitbucket.project.settings.panel"
                 i18n-name-key="item.groovy.runner.label" weight="150" application="bitbucket">
        <label key="item.groovy.runner.websection.label"/>

        <condition class="com.onresolve.scriptrunner.settings.condition.RestrictRepositoryOrProjectAccessCondition" />
    </web-section>

    <web-item key="builtin-scripts-project-level" name="Built-in Scripts" weight="40"
              section="bitbucket.project.settings.panel/scriptrunner_project_section_stash" application="bitbucket">
        <label key="item.project.builtin.scripts"/>
        <tooltip key="tooltip.project.builtin.scripts"/>
        <link linkId="project_builtin_scripts">/plugins/servlet/scriptrunner/projectadmin/$project.key/projectbuiltin</link>
    </web-item>

    <web-item key="project-pre-receive-hooks" name="Pre receive link" weight="50"
              section="bitbucket.project.settings.panel/scriptrunner_project_section_stash">
    <label key="item.prehooks.label"/>
        <tooltip key="bitbucket.prehooks.tooltip"/>
        <description key="bitbucket.prehooks.tooltip"/>
        <link linkId="prehooks">/plugins/servlet/scriptrunner/projectadmin/$project.key/prehooks</link>
    </web-item>

    <web-item key="project-post-receive-hooks" name="Post receive link" weight="60"
              section="bitbucket.project.settings.panel/scriptrunner_project_section_stash">
        <label key="item.posthooks.label"/>
        <tooltip key="bitbucket.posthooks.tooltip"/>
        <description key="bitbucket.posthooks.tooltip"/>
        <link linkId="posthooks">/plugins/servlet/scriptrunner/projectadmin/$project.key/posthooks</link>
    </web-item>

    <web-item key="project-mergecheck-hooks" name="Mergecheck link" weight="70"
              section="bitbucket.project.settings.panel/scriptrunner_project_section_stash">
        <label key="item.mergecheck.label"/>
        <tooltip key="bitbucket.mergechecks.tooltip"/>
        <description key="bitbucket.mergechecks.tooltip"/>
        <link linkId="mergechecks">/plugins/servlet/scriptrunner/projectadmin/$project.key/mergechecks</link>
    </web-item>

    <web-item key="project-event-handlers" name="Event Handlers link" weight="80"
              section="bitbucket.project.settings.panel/scriptrunner_project_section_stash">
        <label>Listeners</label>
        <tooltip>Listeners</tooltip>
        <link linkId="stash_events">/plugins/servlet/scriptrunner/projectadmin/$project.key/listeners</link>
    </web-item>

    <repository-hook key="script-post-receive-hook" name="Scriptable post-receive global hook" configurable="false"
                     class="com.onresolve.scriptrunner.bitbucket.hooks.SynchronousScriptRunnerPostRepositoryHook"
                     application="bitbucket">
        <description>ScriptRunner post-receive hook</description>
    </repository-hook>

    <repository-hook key="script-post-receive-hook-async" name="Async scriptable post-receive global hook" configurable="false"
                     class="com.onresolve.scriptrunner.bitbucket.hooks.AsyncScriptRunnerPostRepositoryHook"
                     application="bitbucket">
        <description>Async ScriptRunner post-receive hook</description>
    </repository-hook>

    <web-resource key="bitbucket-user-picker" name="Resources for user pickers" application="bitbucket">
        <dependency>com.atlassian.bitbucket.server.bitbucket-web:user-multi-selector</dependency>
        <dependency>com.atlassian.bitbucket.server.bitbucket-web:group-multi-selector</dependency>
    </web-resource>

    <rest name="ScriptRunner REST Resource - Bitbucket"
          key="scriptrunner-rest-resource-bitbucket"
          path="/scriptrunner-bitbucket"
          version="1.0"
          application="bitbucket">
        <description>ScriptRunner REST resource - Bitbucket</description>
        <package>com.onresolve.scriptrunner.filters</package>
        <package>com.onresolve.scriptrunner.runner.rest.common.error</package>
        <package>com.onresolve.scriptrunner.runner.rest.common.providers.writer</package>
        <package>com.onresolve.scriptrunner.runner.rest.bitbucket</package>
    </rest>

    <!--This is here so existing remote github triggers can continue to work-->
    <rest name="ScriptRunner REST Resource - Bitbucket/Stash Compat"
          key="scriptrunner-rest-resource-bitbucket-stash"
          path="/scriptrunner-stash"
          version="1.0"
          application="bitbucket">
        <description>ScriptRunner REST resource - Bitbucket/Stash Compat</description>
        <package>com.onresolve.scriptrunner.runner.rest.bitbucket</package>
    </rest>

    <migration-handler key="ScriptRunnerRepoHookMigrationHandler" weight="101">
        <exporter class="com.onresolve.scriptrunner.bitbucket.migration.hooks.repo.RepoHookDataCenterExporter"/>
        <importer class="com.onresolve.scriptrunner.bitbucket.migration.hooks.repo.RepoHookDataCenterImporter"/>
    </migration-handler>

    <migration-handler key="ScriptRunnerProjectHookMigrationHandler" weight="102">
        <exporter class="com.onresolve.scriptrunner.bitbucket.migration.hooks.project.ProjectHookDataCenterExporter"/>
        <importer class="com.onresolve.scriptrunner.bitbucket.migration.hooks.project.ProjectHookDataCenterImporter"/>
    </migration-handler>

</atlassian-plugin>