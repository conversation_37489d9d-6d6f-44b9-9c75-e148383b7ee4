<atlassian-plugin plugins-version="2">

    <servlet name="Project Servlet" key="project-servlet" class="com.onresolve.scriptrunner.runner.ScriptRunnerServlet">
        <description key="project-servlet.description">The Project Servlet Plugin</description>
        <url-pattern>/scriptrunner/admin/*</url-pattern>
        <url-pattern>/scriptrunner/projectadmin/*</url-pattern>
        <url-pattern>/scriptrunner/repoadmin/*</url-pattern>
    </servlet>

    <servlet name="Legacy Redirect Servlet" key="legacy-redirect-servlet" class="com.onresolve.scriptrunner.runner.LegacyRedirectServlet">
        <description key="project-servlet.description">Legacy Servlet Redirect</description>
        <url-pattern>/scriptrunner/builtin</url-pattern>
    </servlet>

    <servlet name="Custom Web item Links Servlet" key="custom-webitems-servlet" class="com.onresolve.scriptrunner.runner.servlets.WebItemLinkServlet">
        <description key="custom-webitems-servlet.description">Custom Web item Links Servlet</description>
        <url-pattern>/scriptrunner/webitemredirect</url-pattern>
    </servlet>

    <rest name="ScriptRunner REST Resource - Common"
          key="scriptrunner-rest-resource-common"
          path="/scriptrunner"
          version="1.0">
        <description>ScriptRunner REST resource - Common</description>
        <package>com.onresolve.scriptrunner.filters</package>
        <package>com.onresolve.scriptrunner.runner.rest.common</package>
        <package>com.onresolve.scriptrunner.user.properties.rest</package>
        <package>com.onresolve.scriptrunner.features.rest</package>
        <package>com.onresolve.scriptrunner.runner.rest.common.providers.writer</package>
    </rest>

    <web-resource key="hideWebItemsResource">
        <context>atl.admin</context>
        <resource type="download" name="hide-web-items.css" location="css/hide-web-items.css" />
    </web-resource>

</atlassian-plugin>
