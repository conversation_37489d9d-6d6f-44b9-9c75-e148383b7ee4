<atlassian-plugin plugins-version="2">

    <servlet name="Enhanced Search" key="enhanced-search-servlet" class="com.onresolve.scriptrunner.confluence.EnhancedSearchServlet">
        <description>Enhanced Search</description>
        <url-pattern>/scriptrunner/enhancedsearch/*</url-pattern>
    </servlet>

    <web-section key="scriptrunner-space-section"
                 i18n-name-key="item.groovy.runner.label"
                 location="system.space.tools" weight="200">
        <label key="scriptrunner.confluence.space.tool.section"/>
        <condition class="com.atlassian.confluence.plugin.descriptor.web.conditions.SpaceSidebarCondition"/>
        <condition class="com.atlassian.confluence.plugin.descriptor.web.conditions.SpacePermissionCondition">
            <param name="permission">administer</param>
        </condition>
    </web-section>

    <web-item key="space_script_endpoints_conf" name="Space-Tools Notifications Web-Item"
              section="system.space.tools/scriptrunner-space-section"
              weight="200">
        <condition class="com.onresolve.scriptrunner.confluence.SpaceFunctionalityCondition"/>
        <condition class="com.atlassian.confluence.plugin.descriptor.web.conditions.ReadWriteAccessModeCondition"/>
        <label key="scriptrunner.confluence.space.tool.item"/>
        <link linkId="space_script_endpoints_conf">
            /scriptrunner-confluence/space_admin/items.action?section=space_admin_builtin_scripts&amp;key=$generalUtil.urlEncode($helper.spaceKey)#/builtin
        </link>
    </web-item>

    <xwork name="SR Confluence Space Admin Action" key="space-admin-action">
        <description>Action for SR Confluence Space Admin</description>
        <package name="sr-conf-space-admin" extends="default" namespace="/scriptrunner-confluence/space_admin">
            <default-interceptor-ref name="validatingStack"/>
            <action name="items"
                    class="com.onresolve.scriptrunner.confluence.action.ConfluenceSpaceAdminAction"
                    method="fetchItems">
                <result name="success" type="velocity">/com/onresolve/scriptrunner/templates/vm/space-admin-items.vm
                </result>
                <result name="error" type="velocity">/com/onresolve/scriptrunner/templates/vm/space-admin-error.vm
                </result>
            </action>
        </package>
    </xwork>

    <web-section key="scriptrunner_section_conf" name="ScriptRunner Admin Section" location="system.admin"
                 i18n-name-key="item.groovy.runner.label" weight="150">
        <label key="item.groovy.runner.websection.label"/>
    </web-section>

    <!-- Switch user -->
    <web-panel key="exitSwitchUser" location="atl.general">
        <description>ScriptRunner Switch User - Exit Banner</description>
        <condition class="com.onresolve.scriptrunner.fragments.SwitchUserExitBannerCondition"/>
        <context-provider class="com.onresolve.scriptrunner.fragments.SwitchUserExitBannerContextProvider"/>
        <resource type="velocity" name="view" location="templates/switchuser/exit-switch-user.vm"/>
    </web-panel>

    <!-- Enable browse page menu item-->
    <web-item key="scriptrunnerhome" name="ScriptRunner Home" section="system.admin/scriptrunner_section_conf"
              weight="10">
        <label key="item.script.runner.home.label"/>
        <link linkId="scriptrunnerhome_link">/plugins/servlet/scriptrunner/admin/home</link>
    </web-item>

    <web-item key="scriptrunnerbrowse" name="ScriptRunner Browse" section="system.admin/scriptrunner_section_conf"
              weight="15">
        <label key="item.script.runner.browse.label"/>
        <link linkId="scriptrunnerbrowse_link">/plugins/servlet/scriptrunner/admin/browse</link>
    </web-item>

    <web-item key="script_console_conf" name="Display Script Console Web Item"
              section="system.admin/scriptrunner_section_conf"
              weight="20">
        <label key="item.groovy.runner.label"/>
        <link linkId="script_console_conf">/plugins/servlet/scriptrunner/admin/console</link>
        <condition class="com.onresolve.scriptrunner.permissions.ScriptRunnerUseCondition"/>
    </web-item>

    <web-item key="builtin_scripts_conf" name="Display Built-in Scripts Console Web Item"
              section="system.admin/scriptrunner_section_conf"
              weight="25">
        <label key="item.builtin.scripts.label"/>
        <link linkId="builtin_scripts_conf">/plugins/servlet/scriptrunner/admin/builtin</link>
    </web-item>

    <web-item key="switch_user_shortcut" name="Switch User" section="system.admin/scriptrunner_section_conf"
              weight="160" >
        <label key="item.script.runner.shortcut.switch.user.label"/>
        <link linkId="switch_user_shortcut_link">
            /plugins/servlet/scriptrunner/admin/builtin/exec/com.onresolve.scriptrunner.canned.confluence.admin.SwitchUser
        </link>
        <condition class="com.onresolve.scriptrunner.switchuser.SwitchUserShortcutCondition"/>
    </web-item>

    <web-item key="scheduled_jobs_conf" name="Display Scheduled Jobs" section="system.admin/scriptrunner_section_conf"
              weight="30">
        <label key="item.script_jobs.label"/>
        <link linkId="scheduled_jobs_conf">/plugins/servlet/scriptrunner/admin/jobs</link>
        <description>Manage scheduled jobs</description>
    </web-item>

    <web-item key="confluence_events_conf" name="Display Event Handlers"
              section="system.admin/scriptrunner_section_conf"
              weight="35">
        <label key="item.confluence.events.label"/>
        <link linkId="confluence_events_conf">/plugins/servlet/scriptrunner/admin/listeners</link>
        <description>Manage script event handlers</description>
    </web-item>

    <web-item key="confluence_macros_conf" name="Display Macros" section="system.admin/scriptrunner_section_conf"
              weight="40">
        <label key="item.confluence.macros.label"/>
        <link linkId="confluence_macros_conf">/plugins/servlet/scriptrunner/admin/macros</link>
        <description>Manage script event handlers</description>
    </web-item>

    <web-item key="fragments_conf" name="Display Fragments" section="system.admin/scriptrunner_section_conf"
              weight="45">
        <label key="item.fragments.label"/>
        <link linkId="fragments_conf">/plugins/servlet/scriptrunner/admin/fragments</link>
        <description>Manage fragments</description>
    </web-item>

    <web-item key="confluence_cql_functions_conf" name="Display CQL Functions"
              section="system.admin/scriptrunner_section_conf"
              weight="50">
        <label key="scriptrunner.confluence.cql.functions.label"/>
        <link linkId="confluence_cql_functions_conf">/plugins/servlet/scriptrunner/admin/cqlfunctions</link>
        <description>Manage script cql functions</description>
    </web-item>

    <web-item key="confluence_extractors_conf" name="Display Extractors"
              section="system.admin/scriptrunner_section_conf"
              weight="55">
        <label key="item.confluence.extractors.label"/>
        <link linkId="confluence_extractors_conf">/plugins/servlet/scriptrunner/admin/extractors</link>
        <description>Manage script extractors</description>
    </web-item>

    <web-item key="script_endpoints_conf" name="Display Endpoints Box" section="system.admin/scriptrunner_section_conf"
              weight="60">
        <label key="item.script.endpoints.label"/>
        <link linkId="script_endpoints_conf">/plugins/servlet/scriptrunner/admin/restendpoints</link>
    </web-item>

    <web-item key="resources" name="Display Resources" section="system.admin/scriptrunner_section_conf" weight="65">
        <label key="item.script.resources.label"/>
        <link linkId="resources">/plugins/servlet/scriptrunner/admin/resources</link>
        <description>Manage connections to databases</description>
    </web-item>

    <web-item key="scriptEditor" name="Script Editor" section="system.admin/scriptrunner_section_conf" weight="70">
        <label key="item.script.runner.editor.label"/>
        <link linkId="scriptEditor">/plugins/servlet/scriptrunner/admin/scriptEditor</link>
    </web-item>

    <!--
        <web-item key="scripteditor" name="Display Script Editor Web Item" section="system.admin/scriptrunner_section_conf"
                  weight="25" >
            <label key="item.script.runner.editor.label"/>
            <link linkId="scriptEditor">/plugins/servlet/scriptrunner/admin/scriptEditor</link>
            <condition class="com.onresolve.scriptrunner.permissions.ScriptRunnerUseCondition" />
        </web-item>
    -->

    <web-item key="scriptrunnersettings" name="ScriptRunner Settings" section="system.admin/scriptrunner_section_conf"
              weight="80">
        <label key="item.script.runner.settings.label"/>
        <link linkId="scriptrunnersettings_link">/plugins/servlet/scriptrunner/admin/settings</link>
        <condition class="com.atlassian.confluence.plugin.descriptor.web.conditions.SystemAdministratorCondition"/>
    </web-item>

    <!-- part of path after "rest" in /confluence/rest/scriptrunner-confluence/latest/exec -->
    <rest name="ScriptRunner REST Resource - Confluence"
          key="scriptrunner-rest-resource-confluence"
          path="/scriptrunner-confluence"
          version="1.0">
        <description>ScriptRunner REST resource - Confluence</description>
        <package>com.onresolve.scriptrunner.filters</package>
        <package>com.onresolve.scriptrunner.confluence.filter</package>
        <package>com.onresolve.scriptrunner.runner.rest.common.error</package>
        <package>com.onresolve.scriptrunner.runner.rest.confluence</package>
        <package>com.onresolve.scriptrunner.runner.rest.common.providers.writer</package>
    </rest>

    <rest name="ScriptRunner REST Resource - Confluence - Descendants"
          key="scriptrunner-rest-resource-confluence-descendants"
          path="/scriptrunner-confluence/descendants"
          version="1.0">
        <package>com.onresolve.scriptrunner.confluence.descendants</package>
    </rest>

    <extractor name="Script Extractor"
               key="scriptExtractor"
               class="com.onresolve.scriptrunner.canned.confluence.extractors.ScriptExtractor"
               priority="900">
        <description>Extractor for ScriptRunner for Confluence</description>
    </extractor>

    <web-resource key="images-resources">
        <context>editor</context>
        <dependency>com.atlassian.confluence.tinymceplugin:editor-resources</dependency>
        <resource type="download" name="images/" location="images/"/>
    </web-resource>

    <web-resource key="sr-cofluence-permissions-resource">
        <dependency>com.atlassian.auiplugin:aui-select2</dependency>
        <resource type="download" name="sr-confluence-permissions.css"
                  location="css/confluence/sr-confluence-permissions.css"/>
        <context>admin</context>
    </web-resource>

    <!-- Create Page -->
    <rest name="Create Page HTTP Resource"
          key="create-page-http"
          path="/create-page"
          version="1.0">
        <description>Create page REST service</description>
        <package>com.adaptavist.confluence.createpage.rest</package>
    </rest>

    <xwork name="createandviewpageaction" key="createandviewpageaction">
        <package name="createandviewpageaction" extends="default" namespace="/plugins/createpage">
            <default-interceptor-ref name="defaultStack"/>
            <action name="createandviewpage" class="com.adaptavist.confluence.createpage.CreateAndViewPageAction">
                <result name="View" type="redirect">/pages/viewpage.action?pageId=${pageId}</result>
                <result name="Edit" type="redirect">/pages/editpage.action?pageId=${pageId}</result>
                <result name="error" type="redirect">/pages/viewpage.action?pageId=${parentId}</result>
                <result name="pageAlreadySaved"
                        type="redirect">/pages/viewpage.action?pageId=${pageId}&amp;pageAlreadySaved=true
                </result>
                <result name="openedInANewTab"
                        type="redirect">/pages/viewpage.action?pageId=${pageId}&amp;openedInANewTab=true
                </result>
                <result name="noPermissionsOnFromPage"
                        type="redirect">/pages/viewpage.action?pageId=${parentId}&amp;noPermissionsOnFromPage=true
                </result>
            </action>
        </package>

        <package name="createpagetemplatevariables" extends="default" namespace="/pages">
            <default-interceptor-ref name="defaultStack"/>
            <action name="createpagetemplatevariables"
                    class="com.adaptavist.confluence.createpage.CreatePageTemplateVariablesAction">
                <result name="error" type="velocity">
                    /com/onresolve/scriptrunner/templates/vm/createpage-templatevariables.vm
                </result>
                <result name="input" type="velocity">
                    /com/onresolve/scriptrunner/templates/vm/createpage-templatevariables.vm
                </result>
                <result name="success" type="velocity">
                    /com/onresolve/scriptrunner/templates/vm/createpage-templatevariables.vm
                </result>
                <result name="novariables" type="chain">
                    <param name="actionName">createandviewpage</param>
                    <param name="namespace">/plugins/createpage</param>
                </result>
            </action>

            <action name="createpageentervariables"
                    class="com.adaptavist.confluence.createpage.CreatePageTemplateVariablesAction" method="doEnter">
                <default-interceptor-ref name="defaultStack">
                    <param name="chain.includes">templateEditorFormat</param>
                </default-interceptor-ref>
                <result name="error" type="velocity">
                    /com/onresolve/scriptrunner/templates/vm/createpage-templatevariables.vm
                </result>
                <result name="input" type="velocity">
                    /com/onresolve/scriptrunner/templates/vm/createpage-templatevariables.vm
                </result>
                <result name="success" type="chain">
                    <param name="actionName">createandviewpage</param>
                    <param name="namespace">/plugins/createpage</param>
                </result>
            </action>
        </package>
    </xwork>
    <!-- /Create Page -->

</atlassian-plugin>
