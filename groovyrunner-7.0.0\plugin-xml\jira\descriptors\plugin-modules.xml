<bundles>
  <web-resource key="split_auditLogResource">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <dependency>com.atlassian.plugins.jquery:jquery</dependency>
    <resource type="download" name="auditLogResource.css" location="js/auditLogResource.css"/>
    <resource type="download" name="auditLogResource.351e4c0993f2153ccd63.js" location="js/auditLogResource.351e4c0993f2153ccd63.js"/>
  </web-resource>
  <web-resource key="split_bhResources">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="bhResources.c21ef7a559a4b747876f.js" location="js/bhResources.c21ef7a559a4b747876f.js"/>
  </web-resource>
  <web-resource key="split_fragmentResources">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="fragmentResources.4b50f1e16b9a7ba89ee6.js" location="js/fragmentResources.4b50f1e16b9a7ba89ee6.js"/>
  </web-resource>
  <web-resource key="split_jsdCannedCommentResources">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="jsdCannedCommentResources.284c1005d7c1ae56db8d.js" location="js/jsdCannedCommentResources.284c1005d7c1ae56db8d.js"/>
  </web-resource>
  <web-resource key="split_jqlQueryResources">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="jqlQueryResources.ecdf5971669ae4bff8af.js" location="js/jqlQueryResources.ecdf5971669ae4bff8af.js"/>
  </web-resource>
  <web-resource key="split_issueFunctionSearcherResources">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="issueFunctionSearcherResources.7cf062829a5b98127069.js" location="js/issueFunctionSearcherResources.7cf062829a5b98127069.js"/>
  </web-resource>
  <web-resource key="split_sdCustomFieldsResources">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="sdCustomFieldsResources.7b839b780cb644b32d2e.js" location="js/sdCustomFieldsResources.7b839b780cb644b32d2e.js"/>
  </web-resource>
  <web-resource key="split_mailHandlerResources">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="mailHandlerResources.fe104947b3b0b715b04b.js" location="js/mailHandlerResources.fe104947b3b0b715b04b.js"/>
  </web-resource>
  <web-resource key="split_vendor">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="vendor.css" location="js/vendor.css"/>
    <resource type="download" name="vendor.cc951dd397a936d8a1fe.js" location="js/vendor.cc951dd397a936d8a1fe.js"/>
  </web-resource>
  <web-resource key="split_default-src_main_resources_js_behaviours_serialiseQueryString_ts-src_main_resources_js_compon-2a076c">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="default-src_main_resources_js_behaviours_serialiseQueryString_ts-src_main_resources_js_compon-2a076c.7c62e5c90a4064c19585.js" location="js/default-src_main_resources_js_behaviours_serialiseQueryString_ts-src_main_resources_js_compon-2a076c.7c62e5c90a4064c19585.js"/>
  </web-resource>
  <web-resource key="split_default-frontend-components_packages_loading-spinner_dist_index_js-src_main_resources_js_admi-afd5fc">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <dependency>com.onresolve.jira.groovy.groovyrunner:pluginInfoSettingsWebResources</dependency>
    <resource type="download" name="default-frontend-components_packages_loading-spinner_dist_index_js-src_main_resources_js_admi-afd5fc.c44d8450a44e39f6ab03.js" location="js/default-frontend-components_packages_loading-spinner_dist_index_js-src_main_resources_js_admi-afd5fc.c44d8450a44e39f6ab03.js"/>
  </web-resource>
  <web-resource key="split_default-frontend-components_node_modules_vscode-textmate_release_sync_recursive-src_main_reso-d6f95a">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <dependency>com.atlassian.plugins.jquery:jquery</dependency>
    <dependency>com.onresolve.jira.groovy.groovyrunner:adminSettingsWebResources</dependency>
    <resource type="download" name="default-frontend-components_node_modules_vscode-textmate_release_sync_recursive-src_main_reso-d6f95a.e1c112093b00e244ca22.js" location="js/default-frontend-components_node_modules_vscode-textmate_release_sync_recursive-src_main_reso-d6f95a.e1c112093b00e244ca22.js"/>
    <resource type="download" name="intelligentCodeEditor1-f34fd25a..svg" location="js/intelligentCodeEditor1-f34fd25a..svg">
      <param name="content-type" value="image/svg+xml"/>
    </resource>
    <resource type="download" name="intelligentCodeEditor2-d3208dbb..png" location="js/intelligentCodeEditor2-d3208dbb..png"/>
    <resource type="download" name="intelligentCodeEditor3-f7986567..svg" location="js/intelligentCodeEditor3-f7986567..svg">
      <param name="content-type" value="image/svg+xml"/>
    </resource>
  </web-resource>
  <web-resource key="split_default-src_main_resources_js_behaviours_index_ts">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <dependency>jira.webresources:util</dependency>
    <dependency>jira.webresources:list</dependency>
    <dependency>com.onresolve.jira.groovy.groovyrunner:behaviours-translations</dependency>
    <resource type="download" name="default-src_main_resources_js_behaviours_index_ts.ec28af8593357101efdb.js" location="js/default-src_main_resources_js_behaviours_index_ts.ec28af8593357101efdb.js"/>
  </web-resource>
  <web-resource key="split_default-src_main_resources_js_admin_params_JQLQueryParam_tsx-src_main_resources_js_components-f807ef">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="default-src_main_resources_js_admin_params_JQLQueryParam_tsx-src_main_resources_js_components-f807ef.bae658366df44c8f40ec.js" location="js/default-src_main_resources_js_admin_params_JQLQueryParam_tsx-src_main_resources_js_components-f807ef.bae658366df44c8f40ec.js"/>
  </web-resource>
  <web-resource key="split_default-src_main_resources_js_admin_StandaloneParameterisedScriptOrFile_tsx">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="default-src_main_resources_js_admin_StandaloneParameterisedScriptOrFile_tsx.fad48c679a205bc90a1f.js" location="js/default-src_main_resources_js_admin_StandaloneParameterisedScriptOrFile_tsx.fad48c679a205bc90a1f.js"/>
  </web-resource>
  <web-resource key="split_default-src_main_resources_js_admin_TopLevelErrorPage_tsx-src_main_resources_js_admin_common_-ee9508">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="default-src_main_resources_js_admin_TopLevelErrorPage_tsx-src_main_resources_js_admin_common_-ee9508.2bab94240e0b45c28f7a.js" location="js/default-src_main_resources_js_admin_TopLevelErrorPage_tsx-src_main_resources_js_admin_common_-ee9508.2bab94240e0b45c28f7a.js"/>
  </web-resource>
  <web-resource key="split_default-src_main_resources_js_admin_index_tsx-src_main_resources_images_SR-add_svg-src_main_r-a1b842">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <dependency>com.atlassian.auiplugin:dialog2</dependency>
    <resource type="download" name="default-src_main_resources_js_admin_index_tsx-src_main_resources_images_SR-add_svg-src_main_r-a1b842.css" location="js/default-src_main_resources_js_admin_index_tsx-src_main_resources_images_SR-add_svg-src_main_r-a1b842.css"/>
    <resource type="download" name="default-src_main_resources_js_admin_index_tsx-src_main_resources_images_SR-add_svg-src_main_r-a1b842.cd5ebdb866e88a3c41cd.js" location="js/default-src_main_resources_js_admin_index_tsx-src_main_resources_images_SR-add_svg-src_main_r-a1b842.cd5ebdb866e88a3c41cd.js"/>
    <resource type="download" name="SR-add-94385558..svg" location="js/SR-add-94385558..svg">
      <param name="content-type" value="image/svg+xml"/>
    </resource>
    <resource type="download" name="card1-e04284b0..png" location="js/card1-e04284b0..png"/>
    <resource type="download" name="card2-5d104a74..png" location="js/card2-5d104a74..png"/>
    <resource type="download" name="card3-3ad920ea..png" location="js/card3-3ad920ea..png"/>
    <resource type="download" name="card4-76980558..png" location="js/card4-76980558..png"/>
    <resource type="download" name="welcome-8fd1e8ec..png" location="js/welcome-8fd1e8ec..png"/>
    <resource type="download" name="console_svg2-10a8c37d..svg" location="js/console_svg2-10a8c37d..svg">
      <param name="content-type" value="image/svg+xml"/>
    </resource>
    <resource type="download" name="fragments_svg1-cd5ec066..svg" location="js/fragments_svg1-cd5ec066..svg">
      <param name="content-type" value="image/svg+xml"/>
    </resource>
    <resource type="download" name="fragments_svg2-5855267d..svg" location="js/fragments_svg2-5855267d..svg">
      <param name="content-type" value="image/svg+xml"/>
    </resource>
    <resource type="download" name="jobs_svg2-cda5448d..svg" location="js/jobs_svg2-cda5448d..svg">
      <param name="content-type" value="image/svg+xml"/>
    </resource>
    <resource type="download" name="macros_svg1-8575d281..svg" location="js/macros_svg1-8575d281..svg">
      <param name="content-type" value="image/svg+xml"/>
    </resource>
    <resource type="download" name="macros_svg2-2a3bea21..svg" location="js/macros_svg2-2a3bea21..svg">
      <param name="content-type" value="image/svg+xml"/>
    </resource>
    <resource type="download" name="macros_svg3-a7a19e57..svg" location="js/macros_svg3-a7a19e57..svg">
      <param name="content-type" value="image/svg+xml"/>
    </resource>
    <resource type="download" name="resources_svg1-3b334e34..svg" location="js/resources_svg1-3b334e34..svg">
      <param name="content-type" value="image/svg+xml"/>
    </resource>
  </web-resource>
  <web-resource key="split_combinedAdminSection">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="combinedAdminSection.2cab7f53b15437d57db2.js" location="js/combinedAdminSection.2cab7f53b15437d57db2.js"/>
  </web-resource>
  <web-resource key="split_webItemResponseRenderer">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <dependency>com.atlassian.plugins.jquery:jquery</dependency>
    <dependency>com.atlassian.auiplugin:dialog2</dependency>
    <dependency>jira.webresources:util</dependency>
    <dependency>com.atlassian.auiplugin:ajs</dependency>
    <dependency>com.onresolve.jira.groovy.groovyrunner:web-item-response-renderer</dependency>
    <resource type="download" name="webItemResponseRenderer.4703556adaf7adeb00e8.js" location="js/webItemResponseRenderer.4703556adaf7adeb00e8.js"/>
  </web-resource>
  <web-resource key="split_fragmentResource">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="fragmentResource.css" location="js/fragmentResource.css"/>
    <resource type="download" name="fragmentResource.86798d0743b82fed6bdc.js" location="js/fragmentResource.86798d0743b82fed6bdc.js"/>
  </web-resource>
  <web-resource key="split_modifyNavItemNames">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="modifyNavItemNames.5fe8df2e52987f6d7623.js" location="js/modifyNavItemNames.5fe8df2e52987f6d7623.js"/>
  </web-resource>
  <web-resource key="split_automation">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="automation.a81deae0c358b1ea51d9.js" location="js/automation.a81deae0c358b1ea51d9.js"/>
  </web-resource>
  <web-resource key="split_bhInlineEditPluginResources">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="bhInlineEditPluginResources.ee4d7089babd8223fb74.js" location="js/bhInlineEditPluginResources.ee4d7089babd8223fb74.js"/>
  </web-resource>
  <web-resource key="split_default-src_main_resources_js_admin_util_generalUtils_ts-src_main_resources_js_behaviours_deb-a911b4">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <dependency>com.atlassian.plugins.jquery:jquery</dependency>
    <dependency>com.atlassian.auiplugin:aui-select2</dependency>
    <resource type="download" name="default-src_main_resources_js_admin_util_generalUtils_ts-src_main_resources_js_behaviours_deb-a911b4.30d9fece4d17348cf7b7.js" location="js/default-src_main_resources_js_admin_util_generalUtils_ts-src_main_resources_js_behaviours_deb-a911b4.30d9fece4d17348cf7b7.js"/>
  </web-resource>
  <web-resource key="split_bhIssuePluginResources">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="bhIssuePluginResources.css" location="js/bhIssuePluginResources.css"/>
    <resource type="download" name="bhIssuePluginResources.8dfbb9c73ddf7eba3595.js" location="js/bhIssuePluginResources.8dfbb9c73ddf7eba3595.js"/>
  </web-resource>
  <web-resource key="split_bhPortal">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <dependency>com.atlassian.auiplugin:ajs</dependency>
    <resource type="download" name="bhPortal.css" location="js/bhPortal.css"/>
    <resource type="download" name="bhPortal.336bffc69014a40a937a.js" location="js/bhPortal.336bffc69014a40a937a.js"/>
  </web-resource>
  <web-resource key="split_jqlQueryDoc">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="jqlQueryDoc.css" location="js/jqlQueryDoc.css"/>
    <resource type="download" name="jqlQueryDoc.0420f40f9994f5467c63.js" location="js/jqlQueryDoc.0420f40f9994f5467c63.js"/>
  </web-resource>
  <web-resource key="split_ghShowInNavigator">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="ghShowInNavigator.cef6ee547a26a33b38fe.js" location="js/ghShowInNavigator.cef6ee547a26a33b38fe.js"/>
  </web-resource>
  <web-resource key="split_userMessageUtil">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <dependency>com.atlassian.plugins.jquery:jquery</dependency>
    <resource type="download" name="userMessageUtil.980b4e32622eeb0ec348.js" location="js/userMessageUtil.980b4e32622eeb0ec348.js"/>
  </web-resource>
  <web-resource key="split_ghContextMenuItems">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="ghContextMenuItems.d9b93939f533a71b2192.js" location="js/ghContextMenuItems.d9b93939f533a71b2192.js"/>
  </web-resource>
  <web-resource key="split_jsdCannedCommentLoader">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <dependency>jira.webresources:util</dependency>
    <resource type="download" name="jsdCannedCommentLoader.c7b7cbffca84accae7dd.js" location="js/jsdCannedCommentLoader.c7b7cbffca84accae7dd.js"/>
  </web-resource>
  <web-resource key="split_default-frontend-components_packages_fetch-util_dist_index_js-src_main_resources_js_behaviour-9652e8">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="default-frontend-components_packages_fetch-util_dist_index_js-src_main_resources_js_behaviour-9652e8.f3e95f9584be96df899f.js" location="js/default-frontend-components_packages_fetch-util_dist_index_js-src_main_resources_js_behaviour-9652e8.f3e95f9584be96df899f.js"/>
  </web-resource>
  <web-resource key="split_mailHandler">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="mailHandler.css" location="js/mailHandler.css"/>
    <resource type="download" name="mailHandler.9c6cade41c4b2503fd73.js" location="js/mailHandler.9c6cade41c4b2503fd73.js"/>
  </web-resource>
  <web-resource key="split_sdCustomFields">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <dependency>com.atlassian.plugins.jquery:jquery</dependency>
    <dependency>jira.webresources:util</dependency>
    <resource type="download" name="sdCustomFields.29cae4a169a286a22425.js" location="js/sdCustomFields.29cae4a169a286a22425.js"/>
  </web-resource>
  <web-resource key="split_issueFunctionSearcher">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="issueFunctionSearcher.css" location="js/issueFunctionSearcher.css"/>
    <resource type="download" name="issueFunctionSearcher.67fd6cd45d5393cf3f26.js" location="js/issueFunctionSearcher.67fd6cd45d5393cf3f26.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter~~refractor-core-import">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter~~refractor-core-import.a869c74fde24ae4f4724.js" location="js/react-syntax-highlighter~~refractor-core-import.a869c74fde24ae4f4724.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_sqf">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_sqf.05d6265134e0dec682e7.js" location="js/react-syntax-highlighter_languages_refractor_sqf.05d6265134e0dec682e7.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_factor">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_factor.14995ff48f0a6511534a.js" location="js/react-syntax-highlighter_languages_refractor_factor.14995ff48f0a6511534a.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_phpdoc">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_phpdoc.95d4a2dc08f27d6077d9.js" location="js/react-syntax-highlighter_languages_refractor_phpdoc.95d4a2dc08f27d6077d9.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_t4Cs">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_t4Cs.f117a4d5839adbcce1b9.js" location="js/react-syntax-highlighter_languages_refractor_t4Cs.f117a4d5839adbcce1b9.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter~~refractor-import">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter~~refractor-import.e68b8db88be2fc532758.js" location="js/react-syntax-highlighter~~refractor-import.e68b8db88be2fc532758.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_abap">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_abap.b0b060d11b395e02f3e9.js" location="js/react-syntax-highlighter_languages_refractor_abap.b0b060d11b395e02f3e9.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_abnf">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_abnf.dea880412280ba64bae7.js" location="js/react-syntax-highlighter_languages_refractor_abnf.dea880412280ba64bae7.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_actionscript">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_actionscript.c5543055b2913b965d52.js" location="js/react-syntax-highlighter_languages_refractor_actionscript.c5543055b2913b965d52.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_ada">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_ada.785b071d2e17b6ecdb17.js" location="js/react-syntax-highlighter_languages_refractor_ada.785b071d2e17b6ecdb17.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_agda">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_agda.bf9d8d0967f0f94d8d86.js" location="js/react-syntax-highlighter_languages_refractor_agda.bf9d8d0967f0f94d8d86.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_al">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_al.b24072e3f53a1f1c13b8.js" location="js/react-syntax-highlighter_languages_refractor_al.b24072e3f53a1f1c13b8.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_antlr4">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_antlr4.1cdb9ddf65b686055f24.js" location="js/react-syntax-highlighter_languages_refractor_antlr4.1cdb9ddf65b686055f24.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_apacheconf">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_apacheconf.0aa293d9525d5fa1d656.js" location="js/react-syntax-highlighter_languages_refractor_apacheconf.0aa293d9525d5fa1d656.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_apl">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_apl.a399dfecae26e3372422.js" location="js/react-syntax-highlighter_languages_refractor_apl.a399dfecae26e3372422.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_applescript">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_applescript.05b24e346ad7f82dccf3.js" location="js/react-syntax-highlighter_languages_refractor_applescript.05b24e346ad7f82dccf3.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_aql">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_aql.0d68b2ecbba7619a65e7.js" location="js/react-syntax-highlighter_languages_refractor_aql.0d68b2ecbba7619a65e7.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_arduino">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_arduino.7b6c5c9e64152f504dff.js" location="js/react-syntax-highlighter_languages_refractor_arduino.7b6c5c9e64152f504dff.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_arff">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_arff.f815b98e78d6d96738e5.js" location="js/react-syntax-highlighter_languages_refractor_arff.f815b98e78d6d96738e5.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_asciidoc">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_asciidoc.bd921588d1e6a5811720.js" location="js/react-syntax-highlighter_languages_refractor_asciidoc.bd921588d1e6a5811720.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_asm6502">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_asm6502.a84f168b7f345c4ed1f2.js" location="js/react-syntax-highlighter_languages_refractor_asm6502.a84f168b7f345c4ed1f2.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_aspnet">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_aspnet.2306c2eabe4d536f1af1.js" location="js/react-syntax-highlighter_languages_refractor_aspnet.2306c2eabe4d536f1af1.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_autohotkey">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_autohotkey.1bda2f0f11d82b1c0204.js" location="js/react-syntax-highlighter_languages_refractor_autohotkey.1bda2f0f11d82b1c0204.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_autoit">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_autoit.98beb4865cec73194313.js" location="js/react-syntax-highlighter_languages_refractor_autoit.98beb4865cec73194313.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_bash">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_bash.f70c6b0dac50f8febd47.js" location="js/react-syntax-highlighter_languages_refractor_bash.f70c6b0dac50f8febd47.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_basic">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_basic.a0f5edbe9f6d8a9a1392.js" location="js/react-syntax-highlighter_languages_refractor_basic.a0f5edbe9f6d8a9a1392.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_batch">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_batch.d0818509741bedff9464.js" location="js/react-syntax-highlighter_languages_refractor_batch.d0818509741bedff9464.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_bbcode">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_bbcode.54b9ea6154d326986326.js" location="js/react-syntax-highlighter_languages_refractor_bbcode.54b9ea6154d326986326.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_birb">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_birb.352eee6d4dfa49051b8b.js" location="js/react-syntax-highlighter_languages_refractor_birb.352eee6d4dfa49051b8b.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_bison">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_bison.519aff14c7e2c22515e6.js" location="js/react-syntax-highlighter_languages_refractor_bison.519aff14c7e2c22515e6.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_bnf">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_bnf.fb67b8b5181d4082fe0c.js" location="js/react-syntax-highlighter_languages_refractor_bnf.fb67b8b5181d4082fe0c.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_brainfuck">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_brainfuck.974843f8163d0c0f5b93.js" location="js/react-syntax-highlighter_languages_refractor_brainfuck.974843f8163d0c0f5b93.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_brightscript">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_brightscript.e7d0de98e493b0c1fbf9.js" location="js/react-syntax-highlighter_languages_refractor_brightscript.e7d0de98e493b0c1fbf9.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_bro">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_bro.99b38ec94b66ba7ef5d0.js" location="js/react-syntax-highlighter_languages_refractor_bro.99b38ec94b66ba7ef5d0.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_bsl">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_bsl.a30a9321b7270753878c.js" location="js/react-syntax-highlighter_languages_refractor_bsl.a30a9321b7270753878c.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_c">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_c.47693997509217a17bf4.js" location="js/react-syntax-highlighter_languages_refractor_c.47693997509217a17bf4.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_cil">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_cil.7c29480113c0971cca15.js" location="js/react-syntax-highlighter_languages_refractor_cil.7c29480113c0971cca15.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_clike">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_clike.6e16e2ebfb73f98502d5.js" location="js/react-syntax-highlighter_languages_refractor_clike.6e16e2ebfb73f98502d5.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_clojure">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_clojure.38a74c7ca69916015660.js" location="js/react-syntax-highlighter_languages_refractor_clojure.38a74c7ca69916015660.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_cmake">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_cmake.6cfbfebe4d120a6368e9.js" location="js/react-syntax-highlighter_languages_refractor_cmake.6cfbfebe4d120a6368e9.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_coffeescript">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_coffeescript.a5560a24cfcb5528719d.js" location="js/react-syntax-highlighter_languages_refractor_coffeescript.a5560a24cfcb5528719d.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_concurnas">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_concurnas.d719d31011aba984d3fb.js" location="js/react-syntax-highlighter_languages_refractor_concurnas.d719d31011aba984d3fb.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_cpp">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_cpp.311c01a61954e5cde50f.js" location="js/react-syntax-highlighter_languages_refractor_cpp.311c01a61954e5cde50f.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_crystal">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_crystal.829e323f5a15128cdfc2.js" location="js/react-syntax-highlighter_languages_refractor_crystal.829e323f5a15128cdfc2.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_csharp">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_csharp.761be32f143e2e0de833.js" location="js/react-syntax-highlighter_languages_refractor_csharp.761be32f143e2e0de833.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_csp">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_csp.f1e033ad30065a8dcde9.js" location="js/react-syntax-highlighter_languages_refractor_csp.f1e033ad30065a8dcde9.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_cssExtras">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_cssExtras.08a7a16f67b5aff2d6fa.js" location="js/react-syntax-highlighter_languages_refractor_cssExtras.08a7a16f67b5aff2d6fa.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_css">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_css.ce6d7e7f996b9dd8721e.js" location="js/react-syntax-highlighter_languages_refractor_css.ce6d7e7f996b9dd8721e.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_cypher">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_cypher.c80ee7c450f2fbe955c2.js" location="js/react-syntax-highlighter_languages_refractor_cypher.c80ee7c450f2fbe955c2.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_d">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_d.3451ce12841460299af4.js" location="js/react-syntax-highlighter_languages_refractor_d.3451ce12841460299af4.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_dart">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_dart.f261468b931ad0c44ff8.js" location="js/react-syntax-highlighter_languages_refractor_dart.f261468b931ad0c44ff8.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_dax">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_dax.753dce03897c9c0dce3c.js" location="js/react-syntax-highlighter_languages_refractor_dax.753dce03897c9c0dce3c.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_dhall">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_dhall.9be123b7aa10d3c65fda.js" location="js/react-syntax-highlighter_languages_refractor_dhall.9be123b7aa10d3c65fda.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_diff">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_diff.963cea0595dd8f35a8f4.js" location="js/react-syntax-highlighter_languages_refractor_diff.963cea0595dd8f35a8f4.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_django">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_django.669936cc02cd4c837ee0.js" location="js/react-syntax-highlighter_languages_refractor_django.669936cc02cd4c837ee0.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_dnsZoneFile">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_dnsZoneFile.daec69b0288df5a2cbec.js" location="js/react-syntax-highlighter_languages_refractor_dnsZoneFile.daec69b0288df5a2cbec.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_docker">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_docker.b9072d0518c04e40b6b5.js" location="js/react-syntax-highlighter_languages_refractor_docker.b9072d0518c04e40b6b5.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_ebnf">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_ebnf.0f3b7ea851f308d9afdf.js" location="js/react-syntax-highlighter_languages_refractor_ebnf.0f3b7ea851f308d9afdf.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_editorconfig">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_editorconfig.49fda2a6a2812bf831b9.js" location="js/react-syntax-highlighter_languages_refractor_editorconfig.49fda2a6a2812bf831b9.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_eiffel">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_eiffel.6c4b746c8c4c6297fa7a.js" location="js/react-syntax-highlighter_languages_refractor_eiffel.6c4b746c8c4c6297fa7a.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_ejs">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_ejs.455d00aad69b761344d6.js" location="js/react-syntax-highlighter_languages_refractor_ejs.455d00aad69b761344d6.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_elixir">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_elixir.cedcdc102a0bf992a384.js" location="js/react-syntax-highlighter_languages_refractor_elixir.cedcdc102a0bf992a384.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_elm">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_elm.095c2de864ef514220eb.js" location="js/react-syntax-highlighter_languages_refractor_elm.095c2de864ef514220eb.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_erb">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_erb.4b26d1e39bd41b80b6e3.js" location="js/react-syntax-highlighter_languages_refractor_erb.4b26d1e39bd41b80b6e3.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_erlang">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_erlang.c78d0753cfae9b2de780.js" location="js/react-syntax-highlighter_languages_refractor_erlang.c78d0753cfae9b2de780.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_etlua">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_etlua.62c60179d775b81227cf.js" location="js/react-syntax-highlighter_languages_refractor_etlua.62c60179d775b81227cf.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_excelFormula">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_excelFormula.a2bf638349999a3ba267.js" location="js/react-syntax-highlighter_languages_refractor_excelFormula.a2bf638349999a3ba267.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_firestoreSecurityRules">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_firestoreSecurityRules.06d175b66b0f7cfb6684.js" location="js/react-syntax-highlighter_languages_refractor_firestoreSecurityRules.06d175b66b0f7cfb6684.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_flow">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_flow.3e48cd88fe0edf294745.js" location="js/react-syntax-highlighter_languages_refractor_flow.3e48cd88fe0edf294745.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_fortran">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_fortran.f6760d5707e78fd0653d.js" location="js/react-syntax-highlighter_languages_refractor_fortran.f6760d5707e78fd0653d.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_fsharp">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_fsharp.9fc833356606d8f70078.js" location="js/react-syntax-highlighter_languages_refractor_fsharp.9fc833356606d8f70078.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_ftl">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_ftl.c780d53c38ab520dde5e.js" location="js/react-syntax-highlighter_languages_refractor_ftl.c780d53c38ab520dde5e.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_gcode">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_gcode.9d199f2b7d1caa6eee49.js" location="js/react-syntax-highlighter_languages_refractor_gcode.9d199f2b7d1caa6eee49.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_gdscript">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_gdscript.7f58a2782bf14c797a96.js" location="js/react-syntax-highlighter_languages_refractor_gdscript.7f58a2782bf14c797a96.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_gedcom">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_gedcom.a741e8afb693d7802837.js" location="js/react-syntax-highlighter_languages_refractor_gedcom.a741e8afb693d7802837.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_gherkin">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_gherkin.3228926535e1725e3183.js" location="js/react-syntax-highlighter_languages_refractor_gherkin.3228926535e1725e3183.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_git">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_git.17223206d2bc34152a46.js" location="js/react-syntax-highlighter_languages_refractor_git.17223206d2bc34152a46.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_glsl">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_glsl.1cd54e6799377cdf403e.js" location="js/react-syntax-highlighter_languages_refractor_glsl.1cd54e6799377cdf403e.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_gml">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_gml.04c8abac09a2b5794e34.js" location="js/react-syntax-highlighter_languages_refractor_gml.04c8abac09a2b5794e34.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_go">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_go.d7263ed0b16ad351368b.js" location="js/react-syntax-highlighter_languages_refractor_go.d7263ed0b16ad351368b.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_graphql">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_graphql.68bcb5a3d502080049c1.js" location="js/react-syntax-highlighter_languages_refractor_graphql.68bcb5a3d502080049c1.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_groovy">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_groovy.53268daf233b8fd5a934.js" location="js/react-syntax-highlighter_languages_refractor_groovy.53268daf233b8fd5a934.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_haml">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_haml.960dadd4db6e07be336f.js" location="js/react-syntax-highlighter_languages_refractor_haml.960dadd4db6e07be336f.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_handlebars">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_handlebars.9cba2c3650583ccdde96.js" location="js/react-syntax-highlighter_languages_refractor_handlebars.9cba2c3650583ccdde96.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_haskell">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_haskell.3cd883b663746d54ebce.js" location="js/react-syntax-highlighter_languages_refractor_haskell.3cd883b663746d54ebce.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_haxe">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_haxe.574b976f2f200703438d.js" location="js/react-syntax-highlighter_languages_refractor_haxe.574b976f2f200703438d.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_hcl">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_hcl.439a80fec4053191e572.js" location="js/react-syntax-highlighter_languages_refractor_hcl.439a80fec4053191e572.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_hlsl">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_hlsl.aef5fab1bf6e46b7ecdc.js" location="js/react-syntax-highlighter_languages_refractor_hlsl.aef5fab1bf6e46b7ecdc.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_hpkp">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_hpkp.8149630729aeb25043bc.js" location="js/react-syntax-highlighter_languages_refractor_hpkp.8149630729aeb25043bc.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_hsts">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_hsts.4dffc6406dc1ef143aa5.js" location="js/react-syntax-highlighter_languages_refractor_hsts.4dffc6406dc1ef143aa5.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_http">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_http.d4fbb01e517fac192ddf.js" location="js/react-syntax-highlighter_languages_refractor_http.d4fbb01e517fac192ddf.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_ichigojam">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_ichigojam.7ef04294d0ba68554980.js" location="js/react-syntax-highlighter_languages_refractor_ichigojam.7ef04294d0ba68554980.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_icon">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_icon.8f173d7949c9b9144051.js" location="js/react-syntax-highlighter_languages_refractor_icon.8f173d7949c9b9144051.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_iecst">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_iecst.041d1a0f41dfbb5309b5.js" location="js/react-syntax-highlighter_languages_refractor_iecst.041d1a0f41dfbb5309b5.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_ignore">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_ignore.3057f4355041421b94df.js" location="js/react-syntax-highlighter_languages_refractor_ignore.3057f4355041421b94df.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_inform7">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_inform7.f351ddee548bc9b13995.js" location="js/react-syntax-highlighter_languages_refractor_inform7.f351ddee548bc9b13995.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_ini">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_ini.773a4d0535dc5980f249.js" location="js/react-syntax-highlighter_languages_refractor_ini.773a4d0535dc5980f249.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_io">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_io.703d4a177b3efc4eacd5.js" location="js/react-syntax-highlighter_languages_refractor_io.703d4a177b3efc4eacd5.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_j">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_j.e8a92211f4dc00acb1e2.js" location="js/react-syntax-highlighter_languages_refractor_j.e8a92211f4dc00acb1e2.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_java">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_java.9e8d6028a854704a5ec9.js" location="js/react-syntax-highlighter_languages_refractor_java.9e8d6028a854704a5ec9.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_javadoc">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_javadoc.cc5a01ce6d938586d993.js" location="js/react-syntax-highlighter_languages_refractor_javadoc.cc5a01ce6d938586d993.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_javadoclike">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_javadoclike.876c59cf6d2d96d57e37.js" location="js/react-syntax-highlighter_languages_refractor_javadoclike.876c59cf6d2d96d57e37.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_javascript">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_javascript.dc5d437032de2b74abbb.js" location="js/react-syntax-highlighter_languages_refractor_javascript.dc5d437032de2b74abbb.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_javastacktrace">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_javastacktrace.3e6884b89282e96229e0.js" location="js/react-syntax-highlighter_languages_refractor_javastacktrace.3e6884b89282e96229e0.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_jolie">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_jolie.d90260ed142e3b86164a.js" location="js/react-syntax-highlighter_languages_refractor_jolie.d90260ed142e3b86164a.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_jq">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_jq.b67b594407706bf4dd4d.js" location="js/react-syntax-highlighter_languages_refractor_jq.b67b594407706bf4dd4d.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_jsExtras">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_jsExtras.cef98f9073258b41980b.js" location="js/react-syntax-highlighter_languages_refractor_jsExtras.cef98f9073258b41980b.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_jsTemplates">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_jsTemplates.7bc34fc33622aea9e5d2.js" location="js/react-syntax-highlighter_languages_refractor_jsTemplates.7bc34fc33622aea9e5d2.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_jsdoc">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_jsdoc.0bec8d2234e760583f6b.js" location="js/react-syntax-highlighter_languages_refractor_jsdoc.0bec8d2234e760583f6b.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_json">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_json.957386f632220b7dd9f0.js" location="js/react-syntax-highlighter_languages_refractor_json.957386f632220b7dd9f0.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_json5">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_json5.6497305476fca4070485.js" location="js/react-syntax-highlighter_languages_refractor_json5.6497305476fca4070485.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_jsonp">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_jsonp.ee17f26c251f49b7e629.js" location="js/react-syntax-highlighter_languages_refractor_jsonp.ee17f26c251f49b7e629.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_jsstacktrace">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_jsstacktrace.70075032c7ce23dbda88.js" location="js/react-syntax-highlighter_languages_refractor_jsstacktrace.70075032c7ce23dbda88.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_jsx">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_jsx.140747d252a4afa0e6c3.js" location="js/react-syntax-highlighter_languages_refractor_jsx.140747d252a4afa0e6c3.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_julia">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_julia.96e2a0ee92e17282e502.js" location="js/react-syntax-highlighter_languages_refractor_julia.96e2a0ee92e17282e502.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_keyman">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_keyman.1979f76570cf9f7cfa39.js" location="js/react-syntax-highlighter_languages_refractor_keyman.1979f76570cf9f7cfa39.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_kotlin">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_kotlin.0d671830f82534cad62a.js" location="js/react-syntax-highlighter_languages_refractor_kotlin.0d671830f82534cad62a.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_latex">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_latex.3765831d0be36cf5bc00.js" location="js/react-syntax-highlighter_languages_refractor_latex.3765831d0be36cf5bc00.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_latte">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_latte.faf4f6557c26ae073a5c.js" location="js/react-syntax-highlighter_languages_refractor_latte.faf4f6557c26ae073a5c.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_less">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_less.38fbfff8dc0651989185.js" location="js/react-syntax-highlighter_languages_refractor_less.38fbfff8dc0651989185.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_lilypond">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_lilypond.f35ed65ac825f7f9c211.js" location="js/react-syntax-highlighter_languages_refractor_lilypond.f35ed65ac825f7f9c211.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_liquid">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_liquid.dc8d517ef9f3756334dd.js" location="js/react-syntax-highlighter_languages_refractor_liquid.dc8d517ef9f3756334dd.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_lisp">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_lisp.34cc9384e2dd3c4913ad.js" location="js/react-syntax-highlighter_languages_refractor_lisp.34cc9384e2dd3c4913ad.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_livescript">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_livescript.0d58fb2aeeb1309b2e86.js" location="js/react-syntax-highlighter_languages_refractor_livescript.0d58fb2aeeb1309b2e86.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_llvm">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_llvm.dab0c470093b368bd121.js" location="js/react-syntax-highlighter_languages_refractor_llvm.dab0c470093b368bd121.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_lolcode">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_lolcode.6673456fc09d1bdf6fb3.js" location="js/react-syntax-highlighter_languages_refractor_lolcode.6673456fc09d1bdf6fb3.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_lua">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_lua.520798f8f97ac5c04ef7.js" location="js/react-syntax-highlighter_languages_refractor_lua.520798f8f97ac5c04ef7.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_makefile">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_makefile.9187e7cfb8df245174e7.js" location="js/react-syntax-highlighter_languages_refractor_makefile.9187e7cfb8df245174e7.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_markdown">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_markdown.1057e8d3972293b5044a.js" location="js/react-syntax-highlighter_languages_refractor_markdown.1057e8d3972293b5044a.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_markupTemplating">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_markupTemplating.acd228ea6abb084743dc.js" location="js/react-syntax-highlighter_languages_refractor_markupTemplating.acd228ea6abb084743dc.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_markup">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_markup.36c6cba1405b008d3fba.js" location="js/react-syntax-highlighter_languages_refractor_markup.36c6cba1405b008d3fba.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_matlab">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_matlab.e1515f873ecc6ac3faa3.js" location="js/react-syntax-highlighter_languages_refractor_matlab.e1515f873ecc6ac3faa3.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_mel">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_mel.dc370df82e9025834765.js" location="js/react-syntax-highlighter_languages_refractor_mel.dc370df82e9025834765.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_mizar">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_mizar.ee7d5ad8388dc82ae21f.js" location="js/react-syntax-highlighter_languages_refractor_mizar.ee7d5ad8388dc82ae21f.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_mongodb">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_mongodb.2fe31f9eca0773752dda.js" location="js/react-syntax-highlighter_languages_refractor_mongodb.2fe31f9eca0773752dda.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_monkey">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_monkey.321ee29a4f67e127b78e.js" location="js/react-syntax-highlighter_languages_refractor_monkey.321ee29a4f67e127b78e.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_moonscript">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_moonscript.01ac7a30d1fb2a8a443b.js" location="js/react-syntax-highlighter_languages_refractor_moonscript.01ac7a30d1fb2a8a443b.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_n1ql">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_n1ql.33df4f3c876243699b0f.js" location="js/react-syntax-highlighter_languages_refractor_n1ql.33df4f3c876243699b0f.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_n4js">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_n4js.7d73e14ac33c5a469e83.js" location="js/react-syntax-highlighter_languages_refractor_n4js.7d73e14ac33c5a469e83.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_nand2tetrisHdl">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_nand2tetrisHdl.4939f9d0f0ffa3a26bc5.js" location="js/react-syntax-highlighter_languages_refractor_nand2tetrisHdl.4939f9d0f0ffa3a26bc5.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_naniscript">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_naniscript.feb8e137dafa232ff9b5.js" location="js/react-syntax-highlighter_languages_refractor_naniscript.feb8e137dafa232ff9b5.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_nasm">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_nasm.fac1a1f8d0fc29595cb3.js" location="js/react-syntax-highlighter_languages_refractor_nasm.fac1a1f8d0fc29595cb3.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_neon">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_neon.44e4f888652683ec372b.js" location="js/react-syntax-highlighter_languages_refractor_neon.44e4f888652683ec372b.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_nginx">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_nginx.867583f93f4d89aa99d2.js" location="js/react-syntax-highlighter_languages_refractor_nginx.867583f93f4d89aa99d2.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_nim">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_nim.45d256da6e4fcdc1d24d.js" location="js/react-syntax-highlighter_languages_refractor_nim.45d256da6e4fcdc1d24d.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_nix">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_nix.c52097f3bf1358d775af.js" location="js/react-syntax-highlighter_languages_refractor_nix.c52097f3bf1358d775af.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_nsis">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_nsis.a4c7c3c7e11405fb70bd.js" location="js/react-syntax-highlighter_languages_refractor_nsis.a4c7c3c7e11405fb70bd.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_objectivec">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_objectivec.5188a8f218ce23684c34.js" location="js/react-syntax-highlighter_languages_refractor_objectivec.5188a8f218ce23684c34.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_ocaml">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_ocaml.f13e4a90b41dc0283a00.js" location="js/react-syntax-highlighter_languages_refractor_ocaml.f13e4a90b41dc0283a00.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_opencl">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_opencl.82781a893034341e6157.js" location="js/react-syntax-highlighter_languages_refractor_opencl.82781a893034341e6157.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_oz">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_oz.3d5db1c12019a2e4eb55.js" location="js/react-syntax-highlighter_languages_refractor_oz.3d5db1c12019a2e4eb55.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_parigp">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_parigp.888d9e3877b5df0dc2f4.js" location="js/react-syntax-highlighter_languages_refractor_parigp.888d9e3877b5df0dc2f4.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_parser">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_parser.a136a6290b7f5761345b.js" location="js/react-syntax-highlighter_languages_refractor_parser.a136a6290b7f5761345b.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_pascal">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_pascal.7d19694fb6dc5f4b6526.js" location="js/react-syntax-highlighter_languages_refractor_pascal.7d19694fb6dc5f4b6526.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_pascaligo">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_pascaligo.a7ff842f32d7084af2da.js" location="js/react-syntax-highlighter_languages_refractor_pascaligo.a7ff842f32d7084af2da.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_pcaxis">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_pcaxis.cc7a645db1af44ab6bae.js" location="js/react-syntax-highlighter_languages_refractor_pcaxis.cc7a645db1af44ab6bae.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_peoplecode">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_peoplecode.f65c2b4e1e358ad8fa99.js" location="js/react-syntax-highlighter_languages_refractor_peoplecode.f65c2b4e1e358ad8fa99.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_perl">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_perl.433a49f11b48a8894a47.js" location="js/react-syntax-highlighter_languages_refractor_perl.433a49f11b48a8894a47.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_phpExtras">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_phpExtras.ba6495bb4469430fdcdb.js" location="js/react-syntax-highlighter_languages_refractor_phpExtras.ba6495bb4469430fdcdb.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_php">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_php.8642e7c25cda4c5493bb.js" location="js/react-syntax-highlighter_languages_refractor_php.8642e7c25cda4c5493bb.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_plsql">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_plsql.e372ee08db0fc5bcf618.js" location="js/react-syntax-highlighter_languages_refractor_plsql.e372ee08db0fc5bcf618.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_powerquery">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_powerquery.9fa5ab80e842efe46d9c.js" location="js/react-syntax-highlighter_languages_refractor_powerquery.9fa5ab80e842efe46d9c.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_powershell">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_powershell.b493408b38f7d859ff29.js" location="js/react-syntax-highlighter_languages_refractor_powershell.b493408b38f7d859ff29.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_processing">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_processing.821849874fc3140950ff.js" location="js/react-syntax-highlighter_languages_refractor_processing.821849874fc3140950ff.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_prolog">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_prolog.97efe880ea86cb161b11.js" location="js/react-syntax-highlighter_languages_refractor_prolog.97efe880ea86cb161b11.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_properties">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_properties.d83b07b1c50f86425521.js" location="js/react-syntax-highlighter_languages_refractor_properties.d83b07b1c50f86425521.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_protobuf">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_protobuf.99fae2669554f3144437.js" location="js/react-syntax-highlighter_languages_refractor_protobuf.99fae2669554f3144437.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_pug">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_pug.f7a0603ea0936d655667.js" location="js/react-syntax-highlighter_languages_refractor_pug.f7a0603ea0936d655667.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_puppet">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_puppet.1d02e2cd06eba222cd35.js" location="js/react-syntax-highlighter_languages_refractor_puppet.1d02e2cd06eba222cd35.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_pure">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_pure.2db5601424c5d4f2793e.js" location="js/react-syntax-highlighter_languages_refractor_pure.2db5601424c5d4f2793e.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_purebasic">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_purebasic.ce302040473fa950dd6c.js" location="js/react-syntax-highlighter_languages_refractor_purebasic.ce302040473fa950dd6c.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_purescript">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_purescript.4ff34e9abe13c95aeccf.js" location="js/react-syntax-highlighter_languages_refractor_purescript.4ff34e9abe13c95aeccf.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_python">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_python.d6a1ab7fd7ecbcd3e014.js" location="js/react-syntax-highlighter_languages_refractor_python.d6a1ab7fd7ecbcd3e014.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_q">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_q.e0f87df625a09b109d61.js" location="js/react-syntax-highlighter_languages_refractor_q.e0f87df625a09b109d61.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_qml">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_qml.659e256e79445238024a.js" location="js/react-syntax-highlighter_languages_refractor_qml.659e256e79445238024a.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_qore">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_qore.75abd8b8f06843a4aee3.js" location="js/react-syntax-highlighter_languages_refractor_qore.75abd8b8f06843a4aee3.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_r">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_r.5d60cfb0eda5d41086fb.js" location="js/react-syntax-highlighter_languages_refractor_r.5d60cfb0eda5d41086fb.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_racket">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_racket.5fa5f3ca401b9d67e730.js" location="js/react-syntax-highlighter_languages_refractor_racket.5fa5f3ca401b9d67e730.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_reason">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_reason.463734e8933d65267cf6.js" location="js/react-syntax-highlighter_languages_refractor_reason.463734e8933d65267cf6.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_regex">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_regex.81a73b47a8433abeb227.js" location="js/react-syntax-highlighter_languages_refractor_regex.81a73b47a8433abeb227.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_renpy">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_renpy.ff6533840e1547e732a5.js" location="js/react-syntax-highlighter_languages_refractor_renpy.ff6533840e1547e732a5.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_rest">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_rest.866de569a631b767f222.js" location="js/react-syntax-highlighter_languages_refractor_rest.866de569a631b767f222.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_rip">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_rip.84a7f94f53d3881b1391.js" location="js/react-syntax-highlighter_languages_refractor_rip.84a7f94f53d3881b1391.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_roboconf">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_roboconf.56f7c96363a31d2bb2bb.js" location="js/react-syntax-highlighter_languages_refractor_roboconf.56f7c96363a31d2bb2bb.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_robotframework">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_robotframework.9d7312f398e19802e2dd.js" location="js/react-syntax-highlighter_languages_refractor_robotframework.9d7312f398e19802e2dd.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_ruby">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_ruby.95d3115ec9c843e55df4.js" location="js/react-syntax-highlighter_languages_refractor_ruby.95d3115ec9c843e55df4.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_rust">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_rust.a78d83bcfcd33a079b9f.js" location="js/react-syntax-highlighter_languages_refractor_rust.a78d83bcfcd33a079b9f.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_sas">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_sas.eed8a3a6390ff82bd24f.js" location="js/react-syntax-highlighter_languages_refractor_sas.eed8a3a6390ff82bd24f.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_sass">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_sass.db96bb9f4789a1b44f8f.js" location="js/react-syntax-highlighter_languages_refractor_sass.db96bb9f4789a1b44f8f.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_scala">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_scala.a0ea35463df88f2c7732.js" location="js/react-syntax-highlighter_languages_refractor_scala.a0ea35463df88f2c7732.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_scheme">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_scheme.5047557ae381c6375092.js" location="js/react-syntax-highlighter_languages_refractor_scheme.5047557ae381c6375092.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_scss">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_scss.149a0a94e26e452e6713.js" location="js/react-syntax-highlighter_languages_refractor_scss.149a0a94e26e452e6713.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_shellSession">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_shellSession.e64e9b552307cc93da00.js" location="js/react-syntax-highlighter_languages_refractor_shellSession.e64e9b552307cc93da00.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_smali">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_smali.24b864ae5d45928321d7.js" location="js/react-syntax-highlighter_languages_refractor_smali.24b864ae5d45928321d7.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_smalltalk">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_smalltalk.3e249dbba0e7affe831c.js" location="js/react-syntax-highlighter_languages_refractor_smalltalk.3e249dbba0e7affe831c.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_smarty">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_smarty.5bc7aa03d367e7e20bbf.js" location="js/react-syntax-highlighter_languages_refractor_smarty.5bc7aa03d367e7e20bbf.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_sml">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_sml.06a7a5a0adfb830e58b9.js" location="js/react-syntax-highlighter_languages_refractor_sml.06a7a5a0adfb830e58b9.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_solidity">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_solidity.bd7cdb159fd5f6e44556.js" location="js/react-syntax-highlighter_languages_refractor_solidity.bd7cdb159fd5f6e44556.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_solutionFile">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_solutionFile.dbc005e8970e55c5e892.js" location="js/react-syntax-highlighter_languages_refractor_solutionFile.dbc005e8970e55c5e892.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_soy">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_soy.43d990b88e8b94d6aa36.js" location="js/react-syntax-highlighter_languages_refractor_soy.43d990b88e8b94d6aa36.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_sparql">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_sparql.0d882bcbe32fd1bbdd32.js" location="js/react-syntax-highlighter_languages_refractor_sparql.0d882bcbe32fd1bbdd32.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_splunkSpl">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_splunkSpl.f095b403d8b40db5b3ed.js" location="js/react-syntax-highlighter_languages_refractor_splunkSpl.f095b403d8b40db5b3ed.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_sql">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_sql.0376d04f35b847fd065f.js" location="js/react-syntax-highlighter_languages_refractor_sql.0376d04f35b847fd065f.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_stan">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_stan.67eb22f61ef07c769424.js" location="js/react-syntax-highlighter_languages_refractor_stan.67eb22f61ef07c769424.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_stylus">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_stylus.9519a323a8e173bab67b.js" location="js/react-syntax-highlighter_languages_refractor_stylus.9519a323a8e173bab67b.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_swift">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_swift.825b60b05ea09d1ddf17.js" location="js/react-syntax-highlighter_languages_refractor_swift.825b60b05ea09d1ddf17.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_t4Templating">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_t4Templating.8f67561c401c35412c1e.js" location="js/react-syntax-highlighter_languages_refractor_t4Templating.8f67561c401c35412c1e.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_t4Vb">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_t4Vb.d5dd62f5216df94a1dd1.js" location="js/react-syntax-highlighter_languages_refractor_t4Vb.d5dd62f5216df94a1dd1.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_tap">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_tap.a11f354112021bca8dea.js" location="js/react-syntax-highlighter_languages_refractor_tap.a11f354112021bca8dea.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_tcl">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_tcl.2e8dd4b128db9cd13203.js" location="js/react-syntax-highlighter_languages_refractor_tcl.2e8dd4b128db9cd13203.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_textile">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_textile.8f39b6225dfc00b9b2b2.js" location="js/react-syntax-highlighter_languages_refractor_textile.8f39b6225dfc00b9b2b2.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_toml">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_toml.6f1e3699c9244c6e8d5b.js" location="js/react-syntax-highlighter_languages_refractor_toml.6f1e3699c9244c6e8d5b.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_tsx">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_tsx.16686480e30b7fd7e676.js" location="js/react-syntax-highlighter_languages_refractor_tsx.16686480e30b7fd7e676.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_tt2">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_tt2.e0af941b08b27f782b7c.js" location="js/react-syntax-highlighter_languages_refractor_tt2.e0af941b08b27f782b7c.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_turtle">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_turtle.1ddb02e97037e24ab21b.js" location="js/react-syntax-highlighter_languages_refractor_turtle.1ddb02e97037e24ab21b.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_twig">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_twig.7395fece452d66a37ce7.js" location="js/react-syntax-highlighter_languages_refractor_twig.7395fece452d66a37ce7.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_typescript">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_typescript.5edf864be24478b6ae92.js" location="js/react-syntax-highlighter_languages_refractor_typescript.5edf864be24478b6ae92.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_typoscript">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_typoscript.cff312ade448a7ec6ccf.js" location="js/react-syntax-highlighter_languages_refractor_typoscript.cff312ade448a7ec6ccf.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_unrealscript">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_unrealscript.dd361dc347e2ebc7ef18.js" location="js/react-syntax-highlighter_languages_refractor_unrealscript.dd361dc347e2ebc7ef18.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_vala">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_vala.3c915f8803cc3fa18a69.js" location="js/react-syntax-highlighter_languages_refractor_vala.3c915f8803cc3fa18a69.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_vbnet">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_vbnet.c6270e40dac7ae799994.js" location="js/react-syntax-highlighter_languages_refractor_vbnet.c6270e40dac7ae799994.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_velocity">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_velocity.2161415c2af9e99a907f.js" location="js/react-syntax-highlighter_languages_refractor_velocity.2161415c2af9e99a907f.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_verilog">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_verilog.d1502fbfd0ae09cbfbc2.js" location="js/react-syntax-highlighter_languages_refractor_verilog.d1502fbfd0ae09cbfbc2.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_vhdl">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_vhdl.e61f5a65df32b5ef9881.js" location="js/react-syntax-highlighter_languages_refractor_vhdl.e61f5a65df32b5ef9881.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_vim">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_vim.c50e9ce1923a42482f5d.js" location="js/react-syntax-highlighter_languages_refractor_vim.c50e9ce1923a42482f5d.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_visualBasic">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_visualBasic.e965fddd3888d6233685.js" location="js/react-syntax-highlighter_languages_refractor_visualBasic.e965fddd3888d6233685.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_warpscript">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_warpscript.d5ae7c8b92fe591b721f.js" location="js/react-syntax-highlighter_languages_refractor_warpscript.d5ae7c8b92fe591b721f.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_wasm">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_wasm.2610e5bad52ec4f702d9.js" location="js/react-syntax-highlighter_languages_refractor_wasm.2610e5bad52ec4f702d9.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_wiki">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_wiki.eb7caa4176c18f9cb55c.js" location="js/react-syntax-highlighter_languages_refractor_wiki.eb7caa4176c18f9cb55c.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_xeora">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_xeora.11ebc45302572d655ffe.js" location="js/react-syntax-highlighter_languages_refractor_xeora.11ebc45302572d655ffe.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_xmlDoc">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_xmlDoc.9a55df7d2d47fbcad18a.js" location="js/react-syntax-highlighter_languages_refractor_xmlDoc.9a55df7d2d47fbcad18a.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_xojo">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_xojo.4a4cc5f42c7a6ece4cb0.js" location="js/react-syntax-highlighter_languages_refractor_xojo.4a4cc5f42c7a6ece4cb0.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_xquery">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_xquery.a8137581b91bbf45376d.js" location="js/react-syntax-highlighter_languages_refractor_xquery.a8137581b91bbf45376d.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_yaml">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_yaml.79620ae72455bae0e899.js" location="js/react-syntax-highlighter_languages_refractor_yaml.79620ae72455bae0e899.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_yang">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_yang.e5c01eb2b2ba8479eb50.js" location="js/react-syntax-highlighter_languages_refractor_yang.e5c01eb2b2ba8479eb50.js"/>
  </web-resource>
  <web-resource key="react-syntax-highlighter_languages_refractor_zig">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="react-syntax-highlighter_languages_refractor_zig.586e7ce8eea50f7aa95e.js" location="js/react-syntax-highlighter_languages_refractor_zig.586e7ce8eea50f7aa95e.js"/>
  </web-resource>
  <web-resource key="src_main_resources_js_monaco_MonacoEditorLoader_tsx">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="src_main_resources_js_monaco_MonacoEditorLoader_tsx.d1cbff86325d74d367b6.js" location="js/src_main_resources_js_monaco_MonacoEditorLoader_tsx.d1cbff86325d74d367b6.js"/>
  </web-resource>
  <web-resource key="default-src_main_resources_js_behaviours_serialiseQueryString_ts-src_main_resources_js_compon-2a076c">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="default-src_main_resources_js_behaviours_serialiseQueryString_ts-src_main_resources_js_compon-2a076c.7c62e5c90a4064c19585.js" location="js/default-src_main_resources_js_behaviours_serialiseQueryString_ts-src_main_resources_js_compon-2a076c.7c62e5c90a4064c19585.js"/>
  </web-resource>
  <web-resource key="default-frontend-components_packages_loading-spinner_dist_index_js-src_main_resources_js_admi-afd5fc">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <dependency>com.onresolve.jira.groovy.groovyrunner:pluginInfoSettingsWebResources</dependency>
    <resource type="download" name="default-frontend-components_packages_loading-spinner_dist_index_js-src_main_resources_js_admi-afd5fc.c44d8450a44e39f6ab03.js" location="js/default-frontend-components_packages_loading-spinner_dist_index_js-src_main_resources_js_admi-afd5fc.c44d8450a44e39f6ab03.js"/>
  </web-resource>
  <web-resource key="default-frontend-components_node_modules_vscode-textmate_release_sync_recursive-src_main_reso-d6f95a">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <dependency>com.atlassian.plugins.jquery:jquery</dependency>
    <dependency>com.onresolve.jira.groovy.groovyrunner:adminSettingsWebResources</dependency>
    <resource type="download" name="default-frontend-components_node_modules_vscode-textmate_release_sync_recursive-src_main_reso-d6f95a.e1c112093b00e244ca22.js" location="js/default-frontend-components_node_modules_vscode-textmate_release_sync_recursive-src_main_reso-d6f95a.e1c112093b00e244ca22.js"/>
    <resource type="download" name="intelligentCodeEditor1-f34fd25a..svg" location="js/intelligentCodeEditor1-f34fd25a..svg">
      <param name="content-type" value="image/svg+xml"/>
    </resource>
    <resource type="download" name="intelligentCodeEditor2-d3208dbb..png" location="js/intelligentCodeEditor2-d3208dbb..png"/>
    <resource type="download" name="intelligentCodeEditor3-f7986567..svg" location="js/intelligentCodeEditor3-f7986567..svg">
      <param name="content-type" value="image/svg+xml"/>
    </resource>
  </web-resource>
  <web-resource key="default-src_main_resources_js_admin_params_JQLQueryParam_tsx-src_main_resources_js_components-f807ef">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="default-src_main_resources_js_admin_params_JQLQueryParam_tsx-src_main_resources_js_components-f807ef.bae658366df44c8f40ec.js" location="js/default-src_main_resources_js_admin_params_JQLQueryParam_tsx-src_main_resources_js_components-f807ef.bae658366df44c8f40ec.js"/>
  </web-resource>
  <web-resource key="default-src_main_resources_js_admin_StandaloneParameterisedScriptOrFile_tsx">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="default-src_main_resources_js_admin_StandaloneParameterisedScriptOrFile_tsx.fad48c679a205bc90a1f.js" location="js/default-src_main_resources_js_admin_StandaloneParameterisedScriptOrFile_tsx.fad48c679a205bc90a1f.js"/>
  </web-resource>
  <web-resource key="default-node_modules_jquery_fancytree_dist_skin-win7_ui_fancytree_css-frontend-components_nod-6dc64f">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="default-node_modules_jquery_fancytree_dist_skin-win7_ui_fancytree_css-frontend-components_nod-6dc64f.css" location="js/default-node_modules_jquery_fancytree_dist_skin-win7_ui_fancytree_css-frontend-components_nod-6dc64f.css"/>
    <resource type="download" name="default-node_modules_jquery_fancytree_dist_skin-win7_ui_fancytree_css-frontend-components_nod-6dc64f.06ac1e1ad7b85c69d5d3.js" location="js/default-node_modules_jquery_fancytree_dist_skin-win7_ui_fancytree_css-frontend-components_nod-6dc64f.06ac1e1ad7b85c69d5d3.js"/>
  </web-resource>
  <web-resource key="audit-log-resource" name="Resource for ScriptRunner audit log">
    <context>atl.admin</context>
    <dependency>com.onresolve.jira.groovy.groovyrunner:common-runtime</dependency>
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-rest:web-resource-manager</dependency>
    <dependency>com.onresolve.jira.groovy.groovyrunner:split_auditLogResource</dependency>
  </web-resource>
  <web-resource key="combinedAdminSection" name="Admin UI resources">
    <dependency>com.onresolve.jira.groovy.groovyrunner:common-runtime</dependency>
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-rest:web-resource-manager</dependency>
    <dependency>com.onresolve.jira.groovy.groovyrunner:split_bhResources</dependency>
    <dependency>com.onresolve.jira.groovy.groovyrunner:split_fragmentResources</dependency>
    <dependency>com.onresolve.jira.groovy.groovyrunner:split_jsdCannedCommentResources</dependency>
    <dependency>com.onresolve.jira.groovy.groovyrunner:split_jqlQueryResources</dependency>
    <dependency>com.onresolve.jira.groovy.groovyrunner:split_issueFunctionSearcherResources</dependency>
    <dependency>com.onresolve.jira.groovy.groovyrunner:split_sdCustomFieldsResources</dependency>
    <dependency>com.onresolve.jira.groovy.groovyrunner:split_mailHandlerResources</dependency>
    <dependency>com.onresolve.jira.groovy.groovyrunner:split_vendor</dependency>
    <dependency>com.onresolve.jira.groovy.groovyrunner:split_default-src_main_resources_js_behaviours_serialiseQueryString_ts-src_main_resources_js_compon-2a076c</dependency>
    <dependency>com.onresolve.jira.groovy.groovyrunner:split_default-frontend-components_packages_loading-spinner_dist_index_js-src_main_resources_js_admi-afd5fc</dependency>
    <dependency>com.onresolve.jira.groovy.groovyrunner:split_default-frontend-components_node_modules_vscode-textmate_release_sync_recursive-src_main_reso-d6f95a</dependency>
    <dependency>com.onresolve.jira.groovy.groovyrunner:split_default-src_main_resources_js_behaviours_index_ts</dependency>
    <dependency>com.onresolve.jira.groovy.groovyrunner:split_default-src_main_resources_js_admin_params_JQLQueryParam_tsx-src_main_resources_js_components-f807ef</dependency>
    <dependency>com.onresolve.jira.groovy.groovyrunner:split_default-src_main_resources_js_admin_StandaloneParameterisedScriptOrFile_tsx</dependency>
    <dependency>com.onresolve.jira.groovy.groovyrunner:split_default-src_main_resources_js_admin_TopLevelErrorPage_tsx-src_main_resources_js_admin_common_-ee9508</dependency>
    <dependency>com.onresolve.jira.groovy.groovyrunner:split_default-src_main_resources_js_admin_index_tsx-src_main_resources_images_SR-add_svg-src_main_r-a1b842</dependency>
    <dependency>com.onresolve.jira.groovy.groovyrunner:split_combinedAdminSection</dependency>
    <resource type="download" name="intelligentCodeEditor1-f34fd25a..svg" location="js/intelligentCodeEditor1-f34fd25a..svg">
      <param name="content-type" value="image/svg+xml"/>
    </resource>
    <resource type="download" name="intelligentCodeEditor2-d3208dbb..png" location="js/intelligentCodeEditor2-d3208dbb..png"/>
    <resource type="download" name="intelligentCodeEditor3-f7986567..svg" location="js/intelligentCodeEditor3-f7986567..svg">
      <param name="content-type" value="image/svg+xml"/>
    </resource>
    <resource type="download" name="SR-add-94385558..svg" location="js/SR-add-94385558..svg">
      <param name="content-type" value="image/svg+xml"/>
    </resource>
    <resource type="download" name="card1-e04284b0..png" location="js/card1-e04284b0..png"/>
    <resource type="download" name="card2-5d104a74..png" location="js/card2-5d104a74..png"/>
    <resource type="download" name="card3-3ad920ea..png" location="js/card3-3ad920ea..png"/>
    <resource type="download" name="card4-76980558..png" location="js/card4-76980558..png"/>
    <resource type="download" name="welcome-8fd1e8ec..png" location="js/welcome-8fd1e8ec..png"/>
    <resource type="download" name="console_svg2-10a8c37d..svg" location="js/console_svg2-10a8c37d..svg">
      <param name="content-type" value="image/svg+xml"/>
    </resource>
    <resource type="download" name="fragments_svg1-cd5ec066..svg" location="js/fragments_svg1-cd5ec066..svg">
      <param name="content-type" value="image/svg+xml"/>
    </resource>
    <resource type="download" name="fragments_svg2-5855267d..svg" location="js/fragments_svg2-5855267d..svg">
      <param name="content-type" value="image/svg+xml"/>
    </resource>
    <resource type="download" name="jobs_svg2-cda5448d..svg" location="js/jobs_svg2-cda5448d..svg">
      <param name="content-type" value="image/svg+xml"/>
    </resource>
    <resource type="download" name="macros_svg1-8575d281..svg" location="js/macros_svg1-8575d281..svg">
      <param name="content-type" value="image/svg+xml"/>
    </resource>
    <resource type="download" name="macros_svg2-2a3bea21..svg" location="js/macros_svg2-2a3bea21..svg">
      <param name="content-type" value="image/svg+xml"/>
    </resource>
    <resource type="download" name="macros_svg3-a7a19e57..svg" location="js/macros_svg3-a7a19e57..svg">
      <param name="content-type" value="image/svg+xml"/>
    </resource>
    <resource type="download" name="resources_svg1-3b334e34..svg" location="js/resources_svg1-3b334e34..svg">
      <param name="content-type" value="image/svg+xml"/>
    </resource>
  </web-resource>
  <web-resource key="web-item-response-renderer-resources" name="Formats web-item REST responses">
    <context>atl.general</context>
    <context>atl.admin</context>
    <context>main</context>
    <context>admin</context>
    <context>customerportal</context>
    <dependency>com.onresolve.jira.groovy.groovyrunner:common-runtime</dependency>
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-rest:web-resource-manager</dependency>
    <dependency>com.onresolve.jira.groovy.groovyrunner:split_webItemResponseRenderer</dependency>
  </web-resource>
  <web-resource key="fragment-resource" state="disabled" name="Resource for fragment popup helper">
    <context>atl.general</context>
    <context>atl.admin</context>
    <context>main</context>
    <context>admin</context>
    <dependency>com.onresolve.jira.groovy.groovyrunner:common-runtime</dependency>
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-rest:web-resource-manager</dependency>
    <dependency>com.onresolve.jira.groovy.groovyrunner:split_bhResources</dependency>
    <dependency>com.onresolve.jira.groovy.groovyrunner:split_fragmentResources</dependency>
    <dependency>com.onresolve.jira.groovy.groovyrunner:split_fragmentResource</dependency>
  </web-resource>
  <web-resource key="modify-nav-item-names" name="Standardize navigatrion item names">
    <context>atl.admin</context>
    <context>admin</context>
    <dependency>com.onresolve.jira.groovy.groovyrunner:common-runtime</dependency>
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-rest:web-resource-manager</dependency>
    <dependency>com.onresolve.jira.groovy.groovyrunner:split_modifyNavItemNames</dependency>
  </web-resource>
  <web-resource key="execute-script-action-resources" name="Execute Script Automation Action Resources">
    <context>automation-addons-context</context>
    <dependency>com.onresolve.jira.groovy.groovyrunner:common-runtime</dependency>
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-rest:web-resource-manager</dependency>
    <dependency>com.onresolve.jira.groovy.groovyrunner:split_automation</dependency>
  </web-resource>
  <web-resource key="bh-inlineEditPluginResources" name="Behaviour inline edit issue assets">
    <context>jira.view.issue</context>
    <context>gh-rapid-plan</context>
    <context>gh-rapid-work</context>
    <dependency>com.onresolve.jira.groovy.groovyrunner:common-runtime</dependency>
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-rest:web-resource-manager</dependency>
    <dependency>com.onresolve.jira.groovy.groovyrunner:split_bhResources</dependency>
    <dependency>com.onresolve.jira.groovy.groovyrunner:split_bhInlineEditPluginResources</dependency>
  </web-resource>
  <web-resource key="bh-issuePluginResources" name="Behaviour issue assets">
    <context>jira.general</context>
    <dependency>com.onresolve.jira.groovy.groovyrunner:common-runtime</dependency>
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-rest:web-resource-manager</dependency>
    <dependency>com.onresolve.jira.groovy.groovyrunner:split_bhResources</dependency>
    <dependency>com.onresolve.jira.groovy.groovyrunner:split_default-src_main_resources_js_behaviours_serialiseQueryString_ts-src_main_resources_js_compon-2a076c</dependency>
    <dependency>com.onresolve.jira.groovy.groovyrunner:split_default-src_main_resources_js_behaviours_index_ts</dependency>
    <dependency>com.onresolve.jira.groovy.groovyrunner:split_default-src_main_resources_js_admin_util_generalUtils_ts-src_main_resources_js_behaviours_deb-a911b4</dependency>
    <dependency>com.onresolve.jira.groovy.groovyrunner:split_bhIssuePluginResources</dependency>
  </web-resource>
  <web-resource key="bh-issuePluginResourcesPortal" name="Behaviour issue assets for portal">
    <context>customerportal</context>
    <dependency>com.onresolve.jira.groovy.groovyrunner:common-runtime</dependency>
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-rest:web-resource-manager</dependency>
    <dependency>com.onresolve.jira.groovy.groovyrunner:split_bhResources</dependency>
    <dependency>com.onresolve.jira.groovy.groovyrunner:split_default-src_main_resources_js_behaviours_serialiseQueryString_ts-src_main_resources_js_compon-2a076c</dependency>
    <dependency>com.onresolve.jira.groovy.groovyrunner:split_default-src_main_resources_js_behaviours_index_ts</dependency>
    <dependency>com.onresolve.jira.groovy.groovyrunner:split_default-src_main_resources_js_admin_util_generalUtils_ts-src_main_resources_js_behaviours_deb-a911b4</dependency>
    <dependency>com.onresolve.jira.groovy.groovyrunner:split_bhPortal</dependency>
  </web-resource>
  <web-resource key="jqlQueryDoc" name="Script Query advanced navigator documentation">
    <context>jira.navigator.advanced</context>
    <dependency>com.onresolve.jira.groovy.groovyrunner:common-runtime</dependency>
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-rest:web-resource-manager</dependency>
    <dependency>com.onresolve.jira.groovy.groovyrunner:split_bhResources</dependency>
    <dependency>com.onresolve.jira.groovy.groovyrunner:split_jqlQueryResources</dependency>
    <dependency>com.onresolve.jira.groovy.groovyrunner:split_jqlQueryDoc</dependency>
  </web-resource>
  <web-resource key="ghShowInNavigator" name="Resource to add View in Navigator to sprints on the rapid board">
    <context>gh-rapid-plan</context>
    <dependency>com.onresolve.jira.groovy.groovyrunner:common-runtime</dependency>
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-rest:web-resource-manager</dependency>
    <dependency>com.onresolve.jira.groovy.groovyrunner:split_ghShowInNavigator</dependency>
  </web-resource>
  <web-resource key="userMessageUtil" name="Displays messages to the user generated from script workflow functions">
    <context>jira.general</context>
    <dependency>com.onresolve.jira.groovy.groovyrunner:common-runtime</dependency>
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-rest:web-resource-manager</dependency>
    <dependency>com.onresolve.jira.groovy.groovyrunner:split_userMessageUtil</dependency>
  </web-resource>
  <web-resource key="gh-context-menu-items" name="Context menu additions">
    <context>gh-rapid-plan</context>
    <context>gh-rapid-work</context>
    <dependency>com.onresolve.jira.groovy.groovyrunner:common-runtime</dependency>
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-rest:web-resource-manager</dependency>
    <dependency>com.onresolve.jira.groovy.groovyrunner:split_bhResources</dependency>
    <dependency>com.onresolve.jira.groovy.groovyrunner:split_ghContextMenuItems</dependency>
  </web-resource>
  <web-resource key="jsd-canned-comment-loader" state="disabled" name="Canned comments resource">
    <context>jira.view.issue</context>
    <context>jira.edit.issue</context>
    <dependency>com.onresolve.jira.groovy.groovyrunner:common-runtime</dependency>
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-rest:web-resource-manager</dependency>
    <dependency>com.onresolve.jira.groovy.groovyrunner:split_bhResources</dependency>
    <dependency>com.onresolve.jira.groovy.groovyrunner:split_fragmentResources</dependency>
    <dependency>com.onresolve.jira.groovy.groovyrunner:split_jsdCannedCommentResources</dependency>
    <dependency>com.onresolve.jira.groovy.groovyrunner:split_jsdCannedCommentLoader</dependency>
  </web-resource>
  <web-resource key="mail-handler" name="ScriptRunner mail handler">
    <context>jira.admin</context>
    <dependency>com.onresolve.jira.groovy.groovyrunner:common-runtime</dependency>
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-rest:web-resource-manager</dependency>
    <dependency>com.onresolve.jira.groovy.groovyrunner:split_bhResources</dependency>
    <dependency>com.onresolve.jira.groovy.groovyrunner:split_fragmentResources</dependency>
    <dependency>com.onresolve.jira.groovy.groovyrunner:split_jsdCannedCommentResources</dependency>
    <dependency>com.onresolve.jira.groovy.groovyrunner:split_jqlQueryResources</dependency>
    <dependency>com.onresolve.jira.groovy.groovyrunner:split_issueFunctionSearcherResources</dependency>
    <dependency>com.onresolve.jira.groovy.groovyrunner:split_sdCustomFieldsResources</dependency>
    <dependency>com.onresolve.jira.groovy.groovyrunner:split_mailHandlerResources</dependency>
    <dependency>com.onresolve.jira.groovy.groovyrunner:split_default-frontend-components_packages_loading-spinner_dist_index_js-src_main_resources_js_admi-afd5fc</dependency>
    <dependency>com.onresolve.jira.groovy.groovyrunner:split_default-frontend-components_node_modules_vscode-textmate_release_sync_recursive-src_main_reso-d6f95a</dependency>
    <dependency>com.onresolve.jira.groovy.groovyrunner:split_default-src_main_resources_js_admin_TopLevelErrorPage_tsx-src_main_resources_js_admin_common_-ee9508</dependency>
    <dependency>com.onresolve.jira.groovy.groovyrunner:split_default-frontend-components_packages_fetch-util_dist_index_js-src_main_resources_js_behaviour-9652e8</dependency>
    <dependency>com.onresolve.jira.groovy.groovyrunner:split_mailHandler</dependency>
    <resource type="download" name="intelligentCodeEditor1-f34fd25a..svg" location="js/intelligentCodeEditor1-f34fd25a..svg">
      <param name="content-type" value="image/svg+xml"/>
    </resource>
    <resource type="download" name="intelligentCodeEditor2-d3208dbb..png" location="js/intelligentCodeEditor2-d3208dbb..png"/>
    <resource type="download" name="intelligentCodeEditor3-f7986567..svg" location="js/intelligentCodeEditor3-f7986567..svg">
      <param name="content-type" value="image/svg+xml"/>
    </resource>
  </web-resource>
  <web-resource key="sdCustomFieldsResources" name="Resources for Service Desk Edit Request Type screen">
    <context>jira.admin</context>
    <dependency>com.onresolve.jira.groovy.groovyrunner:common-runtime</dependency>
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-rest:web-resource-manager</dependency>
    <dependency>com.onresolve.jira.groovy.groovyrunner:split_bhResources</dependency>
    <dependency>com.onresolve.jira.groovy.groovyrunner:split_sdCustomFieldsResources</dependency>
    <dependency>com.onresolve.jira.groovy.groovyrunner:split_sdCustomFields</dependency>
  </web-resource>
  <web-resource key="entrypoint-issueFunctionSearcher">
    <context>jira.navigator.simple</context>
    <dependency>com.onresolve.jira.groovy.groovyrunner:common-runtime</dependency>
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-rest:web-resource-manager</dependency>
    <dependency>com.onresolve.jira.groovy.groovyrunner:split_bhResources</dependency>
    <dependency>com.onresolve.jira.groovy.groovyrunner:split_fragmentResources</dependency>
    <dependency>com.onresolve.jira.groovy.groovyrunner:split_jsdCannedCommentResources</dependency>
    <dependency>com.onresolve.jira.groovy.groovyrunner:split_jqlQueryResources</dependency>
    <dependency>com.onresolve.jira.groovy.groovyrunner:split_issueFunctionSearcherResources</dependency>
    <dependency>com.onresolve.jira.groovy.groovyrunner:split_default-src_main_resources_js_behaviours_serialiseQueryString_ts-src_main_resources_js_compon-2a076c</dependency>
    <dependency>com.onresolve.jira.groovy.groovyrunner:split_default-frontend-components_packages_loading-spinner_dist_index_js-src_main_resources_js_admi-afd5fc</dependency>
    <dependency>com.onresolve.jira.groovy.groovyrunner:split_default-src_main_resources_js_admin_params_JQLQueryParam_tsx-src_main_resources_js_components-f807ef</dependency>
    <dependency>com.onresolve.jira.groovy.groovyrunner:split_issueFunctionSearcher</dependency>
  </web-resource>
  <web-resource key="common-runtime">
    <dependency>com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path</dependency>
    <resource type="download" name="runtime.353034cb18d4da3c909f.js" location="js/runtime.353034cb18d4da3c909f.js"/>
  </web-resource>
  <web-resource key="assets-ca373968-7a96-4741-b4cd-05540b5ee2c7">
    <resource type="download" name="intelligentCodeEditor1-f34fd25a..svg" location="js/intelligentCodeEditor1-f34fd25a..svg">
      <param name="content-type" value="image/svg+xml"/>
    </resource>
    <resource type="download" name="intelligentCodeEditor2-d3208dbb..png" location="js/intelligentCodeEditor2-d3208dbb..png"/>
    <resource type="download" name="intelligentCodeEditor3-f7986567..svg" location="js/intelligentCodeEditor3-f7986567..svg">
      <param name="content-type" value="image/svg+xml"/>
    </resource>
    <resource type="download" name="SR-add-94385558..svg" location="js/SR-add-94385558..svg">
      <param name="content-type" value="image/svg+xml"/>
    </resource>
    <resource type="download" name="card1-e04284b0..png" location="js/card1-e04284b0..png"/>
    <resource type="download" name="card2-5d104a74..png" location="js/card2-5d104a74..png"/>
    <resource type="download" name="card3-3ad920ea..png" location="js/card3-3ad920ea..png"/>
    <resource type="download" name="card4-76980558..png" location="js/card4-76980558..png"/>
    <resource type="download" name="welcome-8fd1e8ec..png" location="js/welcome-8fd1e8ec..png"/>
    <resource type="download" name="console_svg2-10a8c37d..svg" location="js/console_svg2-10a8c37d..svg">
      <param name="content-type" value="image/svg+xml"/>
    </resource>
    <resource type="download" name="fragments_svg1-cd5ec066..svg" location="js/fragments_svg1-cd5ec066..svg">
      <param name="content-type" value="image/svg+xml"/>
    </resource>
    <resource type="download" name="fragments_svg2-5855267d..svg" location="js/fragments_svg2-5855267d..svg">
      <param name="content-type" value="image/svg+xml"/>
    </resource>
    <resource type="download" name="jobs_svg2-cda5448d..svg" location="js/jobs_svg2-cda5448d..svg">
      <param name="content-type" value="image/svg+xml"/>
    </resource>
    <resource type="download" name="macros_svg1-8575d281..svg" location="js/macros_svg1-8575d281..svg">
      <param name="content-type" value="image/svg+xml"/>
    </resource>
    <resource type="download" name="macros_svg2-2a3bea21..svg" location="js/macros_svg2-2a3bea21..svg">
      <param name="content-type" value="image/svg+xml"/>
    </resource>
    <resource type="download" name="macros_svg3-a7a19e57..svg" location="js/macros_svg3-a7a19e57..svg">
      <param name="content-type" value="image/svg+xml"/>
    </resource>
    <resource type="download" name="resources_svg1-3b334e34..svg" location="js/resources_svg1-3b334e34..svg">
      <param name="content-type" value="image/svg+xml"/>
    </resource>
    <resource type="download" name="bhInlineEditPluginResources.ee4d7089babd8223fb74.js.LICENSE.txt" location="js/bhInlineEditPluginResources.ee4d7089babd8223fb74.js.LICENSE.txt"/>
    <resource type="download" name="bhResources.c21ef7a559a4b747876f.js.LICENSE.txt" location="js/bhResources.c21ef7a559a4b747876f.js.LICENSE.txt"/>
    <resource type="download" name="default-node_modules_jquery_fancytree_dist_skin-win7_ui_fancytree_css-frontend-components_nod-6dc64f.06ac1e1ad7b85c69d5d3.js.LICENSE.txt" location="js/default-node_modules_jquery_fancytree_dist_skin-win7_ui_fancytree_css-frontend-components_nod-6dc64f.06ac1e1ad7b85c69d5d3.js.LICENSE.txt"/>
    <resource type="download" name="default-src_main_resources_js_admin_StandaloneParameterisedScriptOrFile_tsx.fad48c679a205bc90a1f.js.LICENSE.txt" location="js/default-src_main_resources_js_admin_StandaloneParameterisedScriptOrFile_tsx.fad48c679a205bc90a1f.js.LICENSE.txt"/>
    <resource type="download" name="fragmentResources.4b50f1e16b9a7ba89ee6.js.LICENSE.txt" location="js/fragmentResources.4b50f1e16b9a7ba89ee6.js.LICENSE.txt"/>
    <resource type="download" name="ghShowInNavigator.cef6ee547a26a33b38fe.js.LICENSE.txt" location="js/ghShowInNavigator.cef6ee547a26a33b38fe.js.LICENSE.txt"/>
    <resource type="download" name="issueFunctionSearcherResources.7cf062829a5b98127069.js.LICENSE.txt" location="js/issueFunctionSearcherResources.7cf062829a5b98127069.js.LICENSE.txt"/>
    <resource type="download" name="jsdCannedCommentResources.284c1005d7c1ae56db8d.js.LICENSE.txt" location="js/jsdCannedCommentResources.284c1005d7c1ae56db8d.js.LICENSE.txt"/>
    <resource type="download" name="mailHandlerResources.fe104947b3b0b715b04b.js.LICENSE.txt" location="js/mailHandlerResources.fe104947b3b0b715b04b.js.LICENSE.txt"/>
    <resource type="download" name="react-syntax-highlighter~~refractor-core-import.a869c74fde24ae4f4724.js.LICENSE.txt" location="js/react-syntax-highlighter~~refractor-core-import.a869c74fde24ae4f4724.js.LICENSE.txt"/>
    <resource type="download" name="userMessageUtil.980b4e32622eeb0ec348.js.LICENSE.txt" location="js/userMessageUtil.980b4e32622eeb0ec348.js.LICENSE.txt"/>
    <resource type="download" name="vendor.cc951dd397a936d8a1fe.js.LICENSE.txt" location="js/vendor.cc951dd397a936d8a1fe.js.LICENSE.txt"/>
  </web-resource>
</bundles>