<atlassian-plugin plugins-version="2">

    <!--new ui section-->
    <web-section key="scriptrunner_section" name="ScriptRunner Admin Section" location="system.admin.top.navigation.bar"
                 i18n-name-key="item.groovy.runner.label" >
        <label key="item.groovy.runner.websection.label" />

        <condition class="com.atlassian.jira.plugin.webfragment.conditions.JiraGlobalPermissionCondition">
            <param name="permission">administer</param>
        </condition>
    </web-section>

    <web-section key="scriptrunner_items" name="ScriptRunner Items" location="scriptrunner_section" />

    <web-item key="scriptrunnerbrowse" name="ScriptRunner Browse" section="scriptrunner_section/scriptrunner_items"
              weight="10" >
        <label key="item.script.runner.browse.label"/>
        <link linkId="scriptrunnerbrowse_link">/plugins/servlet/scriptrunner/admin/browse</link>
    </web-item>

    <web-item key="scriptconsole" name="Display Script Console Web Item" section="scriptrunner_section/scriptrunner_items"
              weight="15" >
        <label key="item.groovy.runner.label"/>
        <link linkId="console">/plugins/servlet/scriptrunner/admin/console</link>
        <condition class="com.onresolve.scriptrunner.permissions.ScriptRunnerUseCondition" />
    </web-item>

    <web-item key="builtinscripts" name="Display Built-in Scripts Console Web Item" section="scriptrunner_section/scriptrunner_items"
              weight="20" >
        <label key="item.builtin.scripts.label"/>
        <link linkId="builtin">/plugins/servlet/scriptrunner/admin/builtin</link>
    </web-item>

    <web-item key="switch_user_shortcut" name="Switch User" section="scriptrunner_section/scriptrunner_items" weight="80" >
        <label key="item.script.runner.shortcut.switch.user.label"/>
        <link linkId="switch_user_shortcut_link">
            /plugins/servlet/scriptrunner/admin/builtin/exec/com.onresolve.scriptrunner.canned.jira.admin.SwitchUser
        </link>
        <condition class="com.onresolve.scriptrunner.switchuser.SwitchUserShortcutCondition"/>
    </web-item>

    <web-item key="scheduled_jobs_jira" name="Display Scheduled Jobs" section="scriptrunner_section/scriptrunner_items"
              weight="25">
        <label key="item.script_jobs.label"/>
        <link linkId="scheduled_jobs_jira">/plugins/servlet/scriptrunner/admin/jobs</link>
        <description>Manage scheduled jobs</description>
    </web-item>

    <web-item key="script_listeners" name="Display Listener Box" section="scriptrunner_section/scriptrunner_items" weight="30">
        <label key="item.script.listeners.label"/>
        <link linkId="script_listeners">/plugins/servlet/scriptrunner/admin/listeners</link>
    </web-item>

    <web-item key="script_fields" name="Display Scripted Fields Box" section="scriptrunner_section/scriptrunner_items"
              weight="31" >
        <label key="item.groovy.fields.label"/>
        <link linkId="scriptfields">/plugins/servlet/scriptrunner/admin/scriptfields</link>
    </web-item>

    <web-item key="behaviours_section" name="Behaviours Admin Section" section="scriptrunner_section/scriptrunner_items" i18n-name-key="item.behaviours.label"  weight="35" >
        <label key="item.behaviours.label" />
        <link linkId="behaviours">/plugins/servlet/scriptrunner/admin/behaviours</link>
    </web-item>

    <web-item key="script_workflows" name="Display Scripted Workflows" section="scriptrunner_section/scriptrunner_items"
              weight="36" >
        <label key="item.groovy.workflows.label"/>
        <link linkId="scriptworkflows">/plugins/servlet/scriptrunner/admin/workflows</link>
    </web-item>

    <web-item key="jira_fragments" name="Display Fragments" section="scriptrunner_section/scriptrunner_items"
              weight="40" >
        <label key="item.fragments.label"/>
        <link linkId="fragments">/plugins/servlet/scriptrunner/admin/fragments</link>
    </web-item>

    <web-item key="jql_functions" name="Display Functions Box" section="scriptrunner_section/scriptrunner_items" weight="45"
    >
        <label key="item.script.functions.label"/>
        <link linkId="jqlfunctions">/plugins/servlet/scriptrunner/admin/jqlfunctions</link>
    </web-item>

    <web-item key="script_endpoints" name="Display Endpoints Box" section="scriptrunner_section/scriptrunner_items" weight="50">
        <label key="item.script.endpoints.label"/>
        <link linkId="restendpoints">/plugins/servlet/scriptrunner/admin/restendpoints</link>
    </web-item>

    <web-item key="resources" name="Display Resources" section="scriptrunner_section/scriptrunner_items" weight="55">
        <label key="item.script.resources.label"/>
        <link linkId="resources">/plugins/servlet/scriptrunner/admin/resources</link>
        <description>Manage connections to databases</description>
    </web-item>

    <web-item key="script_mail_handler" name="Mail Handler" section="scriptrunner_section/scriptrunner_items"
              weight="60" >
        <label key="item.groovy.mailhandler.label"/>
        <link linkId="mailhandler">/plugins/servlet/scriptrunner/admin/mailhandler</link>
    </web-item>

    <web-item key="scripteditor" name="Display Script Editor Web Item" section="scriptrunner_section/scriptrunner_items"
              weight="65" >
        <label key="item.script.runner.editor.label"/>
        <link linkId="scriptEditor">/plugins/servlet/scriptrunner/admin/scriptEditor</link>
        <condition class="com.onresolve.scriptrunner.permissions.ScriptRunnerUseCondition" />
    </web-item>

    <web-item key="scriptrunnersettings" name="ScriptRunner Settings" section="scriptrunner_section/scriptrunner_items"
              weight="70" >
        <label key="item.script.runner.settings.label"/>
        <link linkId="scriptrunnersettings_link">/plugins/servlet/scriptrunner/admin/settings</link>
        <condition class="com.atlassian.jira.plugin.webfragment.conditions.JiraGlobalPermissionCondition">
            <param name="permission">sysadmin</param>
        </condition>
    </web-item>
    <!--end new ui section-->

    <!--REST - JIRA specific-->
    <rest name="ScriptRunner REST Resource - JIRA"
          key="scriptrunner-rest-resource-jira"
          path="/scriptrunner-jira"
          version="1.0"
    >
        <description>ScriptRunner REST resource - JIRA</description>
        <package>com.onresolve.scriptrunner.filters</package>
        <package>com.onresolve.scriptrunner.runner.rest.common.error</package>
        <package>com.onresolve.scriptrunner.runner.rest.jira</package>
        <package>com.onresolve.scriptrunner.runner.rest.common.providers.writer</package>
    </rest>

    <!--SCRIPT FIELD-->
    <customfield-type key="scripted-field" name="Scripted Field"
                      class="com.onresolve.scriptrunner.customfield.GroovyCustomField" >
        <description>A calculated field whose value is calculated by running a groovy script. Search admin for "Script
            Fields" after adding to configure.
        </description>

        <resource type="download" name="customfieldpreview.png" location="images/SR-Enterprise-240x120.png" />

        <resource type="velocity" name="view" location="templates/customfield/view-scriptedfield.vm"/>
        <resource type="velocity" name="xml" location="templates/customfield/xml-scriptedfield.vm"/>
    </customfield-type>

    <!--SWITCH USER-->
    <web-panel key="exitSwitchUser" location="jira-banner" weight="0">
        <description>ScriptRunner Switch User - Exit Banner</description>
        <condition class="com.onresolve.scriptrunner.fragments.SwitchUserExitBannerCondition" />
        <context-provider class="com.onresolve.scriptrunner.fragments.SwitchUserExitBannerContextProvider" />
        <resource type="velocity" name="view" location="templates/switchuser/exit-switch-user.vm" />
    </web-panel>

    <rest key="rest-service-resources" path="/scriptrunner/behaviours" version="1.0" >
        <description>ScriptRunner REST Resource - Behaviours</description>
        <package>com.onresolve.jira.behaviours.restservice</package>
        <package>com.onresolve.scriptrunner.runner.rest.common.error</package>
        <package>com.onresolve.scriptrunner.filters</package>
    </rest>

    <web-resource key="execute-script-action-static-resources" name="Execute Script Automation Action Static Resources">
        <context>automation-addons-context</context>
        <resource type="download" name="icons/" location="images/"/>
    </web-resource>

    <automation-action key="execute-script-issue-action"
                       class="com.onresolve.scriptrunner.automation.ExecuteScriptIssueAction"
                       name="Execute a ScriptRunner script action for Automation for Jira (deprecated)">
        <!--
         This allows this rule component to be added in project specific rules in the project admin section
         Only allow this if your rule component is safe to be used by project administrators and cannot be used by them
         to escalate their permissions.  This generally means:
         * Don't use velocity rendering (velocity allows access to any underlying objects and methods which could be used to get around permission checks)
         * Perform appropriate permission checks in your server-side validation if your rule action can perform actions that not all users
           should be able to do.

         If in doubt, omit this parameter.
        -->
        <param name="projectSafe">false</param>
    </automation-action>

    <automation-rule-component
            key="execute-script-issue-action-v2"
            class="com.onresolve.scriptrunner.automation.ExecuteScriptIssueActionV2"
            name="Execute a ScriptRunner script action for Automation for Jira"
            type="ACTION">
        <param name="projectSafe">false</param>
    </automation-rule-component>

    <!-- PICKER CUSTOM FIELDS -->
    <customfield-type key="multiple-issue-picker-cf" name="Multiple Issue Picker"
                        application="jira"
                        class="com.onresolve.scriptrunner.canned.jira.fields.editable.issue.MultiIssuePickerField"
    >
        <description>Pick from a list of issues from a JQL query. Tip: Is it easiest to create these from Admin -> Script Fields.</description>

        <resource type="download" name="customfieldpreview.png" location="images/multi-issue-picker-preview.png"/>

        <resource type="velocity" name="view" location="templates/customfield/view-custompicker.vm"/>
        <resource type="velocity" name="edit" location="templates/customfield/edit-custompicker.vm"/>
        <resource type="velocity" name="xml" location="templates/customfield/xml-custompicker.vm"/>
        <category>STANDARD</category>
    </customfield-type>

    <customfield-type key="single-issue-picker-cf" name="Single Issue Picker"
                        application="jira"
                        class="com.onresolve.scriptrunner.canned.jira.fields.editable.issue.SingleIssuePickerField"
    >
        <description>Pick from a list of issues from a JQL query. Tip: Is it easiest to create these from Admin -> Script Fields.</description>

        <resource type="download" name="customfieldpreview.png" location="images/single-issue-picker-preview.png"/>

        <resource type="velocity" name="view" location="templates/customfield/view-custompicker.vm"/>
        <resource type="velocity" name="edit" location="templates/customfield/edit-custompicker.vm"/>
        <resource type="velocity" name="xml" location="templates/customfield/xml-custompicker.vm"/>
        <category>STANDARD</category>
    </customfield-type>

    <customfield-type key="single-database-picker-cf" name="Single Database Values Picker"
                        application="jira"
                        class="com.onresolve.scriptrunner.canned.jira.fields.editable.database.SingleValuePicker"
    >
        <description>Pick a record from a linked database</description>
        <resource type="velocity" name="view" location="templates/customfield/view-custompicker.vm"/>
        <resource type="velocity" name="edit" location="templates/customfield/edit-custompicker.vm"/>
        <resource type="velocity" name="xml" location="templates/customfield/xml-custompicker.vm"/>
        <category>STANDARD</category>
    </customfield-type>

    <customfield-type key="multiple-database-picker-cf" name="Database Picker Custom Field"
                        application="jira"
                        class="com.onresolve.scriptrunner.canned.jira.fields.editable.database.MultiValuePicker"
    >
        <description>Pick multiple records from a linked database</description>
        <resource type="velocity" name="view" location="templates/customfield/view-custompicker.vm"/>
        <resource type="velocity" name="edit" location="templates/customfield/edit-custompicker.vm"/>
        <resource type="velocity" name="xml" location="templates/customfield/xml-custompicker.vm"/>
        <category>STANDARD</category>
    </customfield-type>

    <customfield-type key="single-ldap-picker-cf" name="Single LDAP Picker"
                        application="jira"
                        class="com.onresolve.scriptrunner.canned.jira.fields.editable.database.SingleValuePicker"
    >
        <description>Pick a single record returned by an LDAP query</description>
        <resource type="velocity" name="view" location="templates/customfield/view-custompicker.vm"/>
        <resource type="velocity" name="edit" location="templates/customfield/edit-custompicker.vm"/>
        <resource type="velocity" name="xml" location="templates/customfield/xml-custompicker.vm"/>
        <category>STANDARD</category>
    </customfield-type>

    <customfield-type key="multiple-ldap-picker-cf" name="Multiple LDAP Picker"
                        application="jira"
                        class="com.onresolve.scriptrunner.canned.jira.fields.editable.database.MultiValuePicker"
    >
        <description>Pick multiple records returned by an LDAP query</description>
        <resource type="velocity" name="view" location="templates/customfield/view-custompicker.vm"/>
        <resource type="velocity" name="edit" location="templates/customfield/edit-custompicker.vm"/>
        <resource type="velocity" name="xml" location="templates/customfield/xml-custompicker.vm"/>
        <category>STANDARD</category>
    </customfield-type>

    <customfield-type key="single-remote-issue-picker-cf" name="Single Remote Issue Picker"
                        application="jira"
                        class="com.onresolve.scriptrunner.canned.jira.fields.editable.remoteissue.SingleRemoteIssuePickerField"
    >
        <description>Pick from a list of issues from a JQL query. Tip: Is it easiest to create these from Admin -> Script Fields.</description>

        <resource type="download" name="customfieldpreview.png" location="images/single-issue-picker-preview.png"/>

        <resource type="velocity" name="view" location="templates/customfield/view-custompicker.vm"/>
        <resource type="velocity" name="edit" location="templates/customfield/edit-custompicker.vm"/>
        <resource type="velocity" name="xml" location="templates/plugins/fields/xml/xml-basictext.vm"/>
        <category>STANDARD</category>
    </customfield-type>

    <customfield-type key="multiple-remote-issue-picker-cf" name="Multiple Remote Issue Picker"
                        application="jira"
                        class="com.onresolve.scriptrunner.canned.jira.fields.editable.remoteissue.MultipleRemoteIssuePickerField"
    >
        <description>Pick from a list of issues from a JQL query. Tip: Is it easiest to create these from Admin -> Script Fields.</description>

        <resource type="download" name="customfieldpreview.png" location="images/multi-issue-picker-preview.png"/>

        <resource type="velocity" name="view" location="templates/customfield/view-custompicker.vm"/>
        <resource type="velocity" name="edit" location="templates/customfield/edit-custompicker.vm"/>
        <resource type="velocity" name="xml" location="templates/plugins/fields/xml/xml-basictext.vm"/>
        <category>STANDARD</category>
    </customfield-type>

    <customfield-type key="single-custom-picker-cf" name="Single Custom Picker"
                      application="jira"
                      class="com.onresolve.scriptrunner.canned.jira.fields.editable.database.SingleValuePicker"
    >
        <description>todo</description>
        <resource type="velocity" name="view" location="templates/customfield/view-custompicker.vm"/>
        <resource type="velocity" name="edit" location="templates/customfield/edit-custompicker.vm"/>
        <resource type="velocity" name="xml" location="templates/customfield/xml-custompicker.vm"/>
        <category>STANDARD</category>
    </customfield-type>

    <customfield-type key="multiple-custom-picker-cf" name="Multiple Custom Picker"
                      application="jira"
                      class="com.onresolve.scriptrunner.canned.jira.fields.editable.database.MultiValuePicker"
    >
        <description>todo</description>
        <resource type="velocity" name="view" location="templates/customfield/view-custompicker.vm"/>
        <resource type="velocity" name="edit" location="templates/customfield/edit-custompicker.vm"/>
        <resource type="velocity" name="xml" location="templates/customfield/xml-custompicker.vm"/>
        <category>STANDARD</category>
    </customfield-type>


    <!-- PICKER CUSTOM SEARCHERS -->
    <customfield-searcher key="configurableDbPickerSearcher" name="Database Picker Searcher"
                            class="com.onresolve.scriptrunner.canned.jira.fields.editable.search.PickerSearcher"
                            application="jira">
        <description >Search for database picker</description>

        <resource type="velocity" name="search" location="templates/customfield/search-custompicker.vm"/>
        <resource type="velocity" name="view" location="templates/customfield/view-searcher-picker.vm"/>
        <resource type="velocity" name="label" location="templates/customfield/label-picker-searcher.vm"/>

        <valid-customfield-type package="com.onresolve.jira.groovy.groovyrunner" key="single-database-picker-cf"/>
        <valid-customfield-type package="com.onresolve.jira.groovy.groovyrunner" key="multiple-database-picker-cf"/>
        <valid-customfield-type package="com.onresolve.jira.groovy.groovyrunner" key="single-ldap-picker-cf"/>
        <valid-customfield-type package="com.onresolve.jira.groovy.groovyrunner" key="multiple-ldap-picker-cf"/>
        <valid-customfield-type package="com.onresolve.jira.groovy.groovyrunner" key="single-custom-picker-cf"/>
        <valid-customfield-type package="com.onresolve.jira.groovy.groovyrunner" key="multiple-custom-picker-cf"/>
    </customfield-searcher>

    <customfield-searcher key="configurablePickerSearcher" name="Issue Picker Searcher"
                            class="com.onresolve.scriptrunner.canned.jira.fields.editable.issue.IssuePickerSearcher"
                            application="jira">
        <description key="admin.customfield.searcher.textsearcher.desc">Search for issue picker.
        </description>

        <resource type="velocity" name="search" location="templates/customfield/search-custompicker.vm"/>
        <resource type="velocity" name="view" location="templates/customfield/view-searcher-picker.vm"/>
        <resource type="velocity" name="label" location="templates/customfield/label-picker-searcher.vm"/>

        <valid-customfield-type package="com.onresolve.jira.groovy.groovyrunner" key="multiple-issue-picker-cf"/>
        <valid-customfield-type package="com.onresolve.jira.groovy.groovyrunner" key="single-issue-picker-cf"/>
    </customfield-searcher>

    <customfield-searcher key="remoteIssuePickerSearcher" name="Remote Issue Picker Searcher"
                            class="com.onresolve.scriptrunner.canned.jira.fields.editable.remoteissue.RemoteIssuePickerSearcher"
                            application="jira">
        <description key="admin.customfield.searcher.textsearcher.desc">Search for remote issue picker.
        </description>

        <resource type="velocity" name="search" location="templates/customfield/search-custompicker.vm"/>
        <resource type="velocity" name="view" location="templates/plugins/fields/view-searcher/view-searcher-basictext.vm"/>
        <valid-customfield-type package="com.onresolve.jira.groovy.groovyrunner" key="single-remote-issue-picker-cf"/>
        <valid-customfield-type package="com.onresolve.jira.groovy.groovyrunner" key="multiple-remote-issue-picker-cf"/>
    </customfield-searcher>

    <rest name="Canned comments rest"
          key="canned-comments-rest"
          path="/jsdcanned"
          version="1.0">
        <description>JSD Comments</description>
        <package>com.adaptavist.jsdcc.rest</package>
    </rest>

    <message-handler i18n-name-key="item.script.runner.mail.label" key="sr-mail-handler"
                     class="com.onresolve.scriptrunner.mail.MailHandler"
                     add-edit-url="/secure/admin/SrMailHandlerDetails!default.jspa" weight="10"/>

    <webwork1 key="mail-handler-val" name="ScriptRunner Mail Handler">
        <actions>
            <action name="com.onresolve.scriptrunner.mail.MailHandlerAction" alias="SrMailHandlerDetails" roles-required="admin">
                <view name="input">/templates/mail/mailHandler.vm</view>
                <view name="securitybreach">/secure/views/securitybreach.jsp</view>
            </action>
        </actions>
    </webwork1>

    <web-resource key="behaviours-translations" name="Behaviours Translations">
        <data key="behaviours-translations-data-provider" class="com.onresolve.jira.behaviours.TranslationsDataProvider"/>
        <context>jira.general</context>
    </web-resource>

</atlassian-plugin>
