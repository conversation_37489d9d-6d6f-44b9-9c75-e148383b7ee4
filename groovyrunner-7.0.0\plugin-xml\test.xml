<atlassian-plugin plugins-version="2">

    <servlet name="Tests Servlet" key="tests-servlet" class="com.onresolve.scriptrunner.test.TestsServlet">
        <description key="unlicensed-servlet.description">Tests</description>
        <url-pattern>/scriptrunner/tests</url-pattern>
    </servlet>

    <servlet name="Remote Control servlet" key="remote-control-servlet" class="com.onresolve.scriptrunner.test.RemoteControlServlet">
        <description>Remote Control servlet</description>
        <url-pattern>/scriptrunner/remote-control</url-pattern>
    </servlet>

    <web-resource key="js-test-resources-ss-template" name="JS testing resources server-side template" >
        <resource type="soy" name="tests" location="/com/onresolve/scriptrunner/templates/soy/tests.soy"/>
    </web-resource>

    <servlet-filter name="Tests Servlet Filter" key="tests-servlet-filter"
                    class="com.onresolve.scriptrunner.test.TestsServletFilter" location="before-dispatch" weight="200">
        <description>Injects resources.</description>
        <url-pattern>/</url-pattern>
        <dispatcher>REQUEST</dispatcher>
        <dispatcher>FORWARD</dispatcher>
    </servlet-filter>

</atlassian-plugin>
