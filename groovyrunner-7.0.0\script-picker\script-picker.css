div.script-picker-checked {
    display: inline-block;
    width: 500px;
}

div.script-picker .twitter-typeahead {
    display: inline !important;
}

.tt-script-picker-input.typeahead {
    vertical-align: baseline !important;
}

.tt-script-picker-menu {
    width: 99.9%;
    margin-top: 6.5px;
    padding-right: -1px;
    background-color: #fff;
    border: 1px solid rgba(0, 0, 0, 0.2);

    overflow-y: auto;
    max-height: 200px;
}

.tt-script-picker-suggestion {
    color: #575757; /* Colour for the directory */
    font-size: 12px;
    height: 28px;

    padding: 3px 20px;
    line-height: 28px;
    overflow-x: auto;
    overflow-y: hidden;
    cursor: pointer;
}

.tt-script-picker-suggestion .script-picker-suggestion-line{
    width: 100%;
    display: inline-block;

    overflow: hidden;
    text-overflow: ellipsis;
    direction: rtl;

    white-space: nowrap;

    text-align:right;
}

.script-picker-suggestion-filename {
    /*display: inline-block;*/
    vertical-align: center;
    height: 100%;
    margin-right: 0.5em;

    color: black;
    background: white;
    font-size: 14px;
    direction: ltr;

    overflow: hidden;
    float: left;
    position: relative;
}

.script-picker-directory-suggestion .script-picker-suggestion-line .script-picker-suggestion-filename {
    background-color: #F5F5F5;
}

.tt-script-picker-suggestion p {
    margin: 0;
}

.script-picker-directory-suggestion {
    background-color: #F5F5F5;
    color: #363636;
}

.script-picker-file-icon {
    margin-right: 5px;
    float: left;

    padding: 5px 0;
}

.script-picker-directory-icon {
    margin-right: 5px;
    float: left;

    padding: 6px 0;
}

.tt-script-picker-cursor {
    color: #FFFFFF;
    background-color: #3874AA;
}

.tt-script-picker-cursor .script-picker-suggestion-line .script-picker-suggestion-filename {
    color: #FFFFFF;
    background-color: #3874AA;
}

.tt-script-picker-suggestion.tt-script-picker-cursor .script-picker-suggestion-path {
    color: #FFFFFF;
}

/* Bamboo - Override the default width to display the field to the correct length */
#panel-editor-config form.aui .script-picker .long-field {
    width: 98.5%;
}
