# Web items
item.confluence.events.label=Event Listeners
item.confluence.macros.label=Script Macros
item.confluence.editor.label=Script Editor
item.confluence.extractors.label=Search Extractors
scriptrunner.confluence.cql.functions.label=Script CQL Functions
scriptrunner.confluence.permissions.label=Permissions
# CQL Functions
scriptrunner.confluence.cql.functions.param.name.label=Name 
scriptrunner.confluence.cql.functions.param.name.description=CQL Function display name, for instance <b>myFavouritePages</b> (mandatory)<br>The name should respect the \
  <a target=\"_blank\" href=\"https://developer.atlassian.com/confdev/confluence-server-rest-api/advanced-searching-using-cql/cql-function-reference#CQLFunctionReference-charactersReservedCharacters\">list of reserved words and characters</a>.
scriptrunner.confluence.cql.functions.param.number.of.parameters.label=Number of parameters
scriptrunner.confluence.cql.functions.param.number.of.parameters.description=Specifies the number of arguments accepted by the Query Function (mandatory)
scriptrunner.confluence.cql.functions.param.type.label=Type
scriptrunner.confluence.cql.functions.param.type.description=There are two types of CQL functions which can be implemented:\
  <p><b>Single value query functions</b> return a single String value<br>\
  <b>Multi value query functions</b> return a number of String values<br>More details can be found \
  <a target=\"_blank\" href=\"https://developer.atlassian.com/confdev/confluence-plugin-guide/confluence-plugin-module-types/cql-function-module\">here</a>.
scriptrunner.confluence.cql.functions.validation.name.error=Please specify the name of the CQL Function
scriptrunner.confluence.cql.functions.validation.number.of.parameters.error=Please specify the number of parameters of the CQL Function
scriptrunner.confluence.cql.functions.validation.number.of.parameters.positive.error=The number of parameters of the CQL Function should be between 0 and 10
scriptrunner.confluence.cql.functions.validation.same.name.error=A CQL Function with the name ''{0}'' already exists.
scriptrunner.confluence.cql.functions.validation.db.error=Item has been deleted or updated by another user, please refresh
scriptrunner.confluence.cql.functions.validation.preference.error=CQL User preference ''{0}'' does not exist
scriptrunner.confluence.cql.functions.example.name.first=Pages with a certain label
scriptrunner.confluence.cql.functions.example.name.second=Linked pages for a space and page
scriptrunner.confluence.cql.functions.example.name.third=Pages containing macros from a certain add-on
scriptrunner.confluence.scipt.macro.validation.same.key.error=A macro with the key ''{0}'' already exists.
scriptrunner.confluence.space.tool.section=Advanced Space Functionality
scriptrunner.confluence.space.tool.item=Built-in Scripts