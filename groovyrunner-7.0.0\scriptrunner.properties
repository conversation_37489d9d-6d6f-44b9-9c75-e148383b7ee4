item.groovy.runner.websection.label=ScriptRunner
item.groovy.runner.label=Script Console
item.script.runner.home.label=ScriptRunner Home
item.script.runner.browse.label=Browse ScriptRunner
item.script.runner.editor.label=Script Editor
item.script.runner.shortcut.switch.user.label=Switch User
item.builtin.scripts.label=Built-in Scripts
item.script.listeners.label=Script Listeners
item.script.functions.label=Script JQL Functions
item.script.resources.label=Resources
item.script.endpoints.label=REST Endpoints
item.script.runner.settings.label=ScriptRunner Settings
item.script.runner.jql.field.description=This field is managed by <PERSON><PERSON>t<PERSON>unner for use with JQL functions
item.groovy.fields.label=Script Fields
item.groovy.workflows.label=Script Workflows
item.groovy.mailhandler.label=Script Mail Handler

item.prehooks.label=Pre Hooks
item.posthooks.label=Post Hooks
item.mergecheck.label=Merge Checks
item.stashevents.label=Listeners

item.fragments.label=Script Fragments
item.project.builtin.scripts=Built-in Scripts
tooltip.project.builtin.scripts=Built-in Scripts
item.script_jobs.label=Jobs
item.script_jobs.tooltip=Manage scheduled script jobs
item.fragments.tooltip=Modify the user interface using web fragments
item.script.endpoints.tooltip=Allow external systems to integrate with the application via REST
item.script.resources.tooltip=Manage connections to databases
item.script.editor.tooltip=Create and edit scripts

# Behaviours

item.behaviours.label=Behaviours
field.title.missing.value=You must enter a value for this field
submit.error.message=The following validation errors were found:
submit.error.message.correct=Please correct them.
no.permission.to.edit.field=You do not have permission to edit this field
behaviours.field.access.denied=ACCESS DENIED

# Service
admin.service.groovy.input=Input File

# tooltip descriptions
item.script.runner.browse.tooltip=Browse ScriptRunner scripts
item.groovy.runner.tooltip=Run your own scripts in your application instance
item.builtin.scripts.tooltip=Run the provided administration scripts

bitbucket.prehooks.tooltip=Administer script pre-receive hooks
bitbucket.posthooks.tooltip=Administer asynchronous script post-receive hooks
bitbucket.mergechecks.tooltip=Administer merge checks
bitbucket.events.tooltip=Listeners allow you to run code on events

item.script.runner.configurable-cf.field.description=This picker field is managed by ScriptRunner - configure it at Admin -> Script Fields
item.script.runner.mail.label=ScriptRunner Mail Handler

com.codebarrel.thirdparty.scriptrunner.exception=Script function failed on Automation for Jira rule:
com.codebarrel.thirdparty.scriptrunner.error=ERROR:
com.codebarrel.thirdparty.scriptrunner.info=INFO:
