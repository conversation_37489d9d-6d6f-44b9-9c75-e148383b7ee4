{namespace plugin.com.onresolve.scriptrunner}

/**
* Show the enablement dialog
*/
{template .enableFinderDialog}
<section role="dialog" id="enable-finder-dialog" class="adaptavist-sr aui-layer aui-dialog2 aui-dialog2-medium" aria-hidden="true">
    <header class="aui-dialog2-header">
        <h2 class="aui-dialog2-header-main">Web Location Finder</h2>
        <a class="aui-dialog2-header-close">
            <span class="aui-icon aui-icon-small aui-iconfont-close-dialog">Close</span>
        </a>
    </header>
    <div class="aui-dialog2-content">
        <p>
        <div class="aui-message aui-message-warning">
            <p class="title">
                <strong>Fragment Locator</strong>
            </p>
            <p>Enabling the fragment locator will change the appearance of every page for every single user, so should not be
            enabled in a production system. If this is not a production system then proceed to click the Enable button.</p>
        </div>

        </p>
    </div>
    <footer class="aui-dialog2-footer">
        <div class="aui-dialog2-footer-actions">
            <button id="dialog-submit-button" class="aui-button aui-button-primary">Enable</button>
            <span style="margin-left: 5px; display: none;" class="aui-icon aui-icon-wait">Loading...</span>
            <button id="dialog-close-button" class="aui-button aui-button-link">Close</button>
        </div>
    </footer>
</section>
{/template}