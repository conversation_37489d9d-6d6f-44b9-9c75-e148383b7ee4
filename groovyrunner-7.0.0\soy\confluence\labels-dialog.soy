{namespace plugin.com.onresolve.scriptrunner.confluence}

/**
 * Add one label to the suggested labels on the label dialog
 * @param label
 */
{template .label}
    <li class="adaptavist-sr aui-label aui-label-split" data-label-id="{$label.text}">
        <a class="aui-label-split-main">{$label.text}</a>
    </li>
{/template}

/**
 * Populate labels dialog with suggested labels
 * @param labels
 */
{template .labels}
    <ul class="adaptavist-sr label-list editable suggested-labels-dialog">
    {foreach $label in $labels}
        <li class="aui-label aui-label-split" data-label-id="{$label.id}">
            <a class="aui-label-split-main">{$label.text}</a>
        </li>
    {/foreach}
    </ul>
{/template}