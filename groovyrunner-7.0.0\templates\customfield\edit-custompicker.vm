$webResourceManager.requireResource("com.onresolve.jira.groovy.groovyrunner:bh-issuePluginResources")

#disable_html_escaping()
#customControlHeader ($action $customField.id $customField.name $fieldLayoutItem.required $displayParameters $auiparams)

<select id="$customField.id" name="$customField.id" multiple="multiple" style="display: none">
    #foreach ($option in $!value)
        <option value="$textutils.htmlEncode($option)" selected="selected">$textutils.htmlEncode($option)</option>
    #end
</select>

<div class="sr-custompicker" data-customfieldid="$customField.id" data-fieldconfigid="$fieldConfigId"
     data-fieldparams="$textutils.htmlEncode($!params)"></div>

#customControlFooter ($action $customField.id $fieldLayoutItem.fieldDescription $displayParameters $auiparams)