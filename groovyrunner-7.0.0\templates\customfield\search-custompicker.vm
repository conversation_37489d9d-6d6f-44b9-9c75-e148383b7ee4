#disable_html_escaping()
$!{auiparams.put("controlHeaderClass", "aui-field-text")}
#searcherEditHeader (${customField.id} ${customField.name})

<div>
    <select id="$customField.id" name="$customField.id" multiple="multiple" style="display: none">
        #foreach ($option in $!valueObject)
            <option value="$textutils.htmlEncode($option)" selected="selected">$textutils.htmlEncode($option)</option>
        #end
    </select>
    <div class="sr-custompicker" data-customfieldid="$customField.id" data-fieldconfigid="$!fieldConfigId" data-fieldparams="$textutils.htmlEncode($!params)"></div>
</div>
#searcherEditFooter (${customField.id} ${customField.descriptionProperty.viewHtml})
$!{auiparams.clear()}