##<pre>
##    cannedScript: $cannedScript
##    displayParameters: ${displayParameters}
##    textOnly: $textOnly
##    excelView: $excelView
##    value: $!value
##    value class: $!value.class
##    fieldLayoutItem: $fieldLayoutItem
##</pre>
###stop

#disable_html_escaping()
#if ($value)
    ## Other export options are handled by implementing com.atlassian.jira.issue.export.customfield.ExportableCustomFieldType
    #if (${displayParameters.excel_view})
        ## Excel view, removed from Jira but still available via config option
        $textutils.br($textutils.htmlEncode($!value.toString(), false))
    #elseif(${displayParameters.textOnly})
        ## Export to Word, Printable
        $cannedScript.getTextOnlyHtml($issue, $customField, $fieldLayoutItem, $!value)
    #elseif(${displayParameters.navigator_view})
        ## Issue navigator list view
        $cannedScript.getColumnViewHtml($issue, $customField, $fieldLayoutItem, $!value)
    #else
        ## View-issue screen
        $cannedScript.getViewHtml($issue, $customField, $fieldLayoutItem, $!value)
    #end
#end
