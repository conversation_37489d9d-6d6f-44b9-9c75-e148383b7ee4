#disable_html_escaping()
#set ($modifierKey = $action.browserUtils.getModifierKey())
#set ($submitAccessKey = $i18n.getText('AUI.form.submit.button.accesskey'))
#set ($submitTitle = $i18n.getText('AUI.form.submit.button.tooltip', [$submitAccessKey, $modifierKey]))
#set ($cancelAccessKey = $i18n.getText('AUI.form.cancel.link.accesskey'))
#set ($cancelTitle = $i18n.getText('AUI.form.cancel.link.tooltip', [$cancelAccessKey, $modifierKey]))
<html>
<head>
    <title>$action.handlerName</title>
    <meta name="decorator" content="atl.admin">
    <meta name="admin.active.section" content="admin_plugins_menu/scriptrunner_section">
    <meta name="admin.active.tab" content="mailHandler">
    <meta name="activeTab" content="mailHandler">
</head>
<body>
<h1>ScriptRunner Mail Handler</h1>
<form class="aui top-label ajs-dirty-warning-exempt " action="SrMailHandlerDetails.jspa" method="POST" name="srMailHandlerForm" id="srMailHandlerForm">
    <div class="form-body" id="mail-handler-target">
        <p>Loading ScriptRunner MailHandler form...</p>
    </div>
    <script>
        function initializeMailHandler() {
            if (!window['ScriptRunner']) {
                console.warn('ScriptRunner extension is not installed');
                return false;
            }
            const init = ScriptRunner.initMailHandlerForm;
            if (init) {
                init();
                return true;
            } else {
                console.warn('ScriptRunner mail handler form initialization function does not exist');
            }
            return false;
        }

        if (!initializeMailHandler()) {
            window.addEventListener('message', function(e) {
                if (e.data !== 'sr-MailHandlerLoaded') {
                    return;
                }

                initializeMailHandler();
            });
        }
    </script>
    #if ($action.editing)
        #set ($addButtonLabel = $i18n.getText('common.words.save'))
    #else
        #set ($addButtonLabel = $i18n.getText('common.forms.add'))
    #end

    <div class="buttons-container form-footer" >
        <div class="buttons" id="mail-handler-actions" style="display: flex; align-items: center" data-add="$!addButtonLabel" data-cancel="$i18n.getText("AUI.form.cancel.link.text")">

        </div>
    </div>
    <input type="hidden" name="atl_token" value="$atl_token">
    <input type="hidden" name="bulk" id="bulk" value="$!textutils.htmlEncode($bulk)">
    <input type="hidden" name="scriptText" id="script-text" value="$!textutils.htmlEncode($scriptText)">
    <input type="hidden" name="scriptPath" id="script-path" value="$!textutils.htmlEncode($scriptPath)">
    <input type="hidden" name="scriptStatus" id="script-status" value="$!textutils.htmlEncode($scriptStatus)">
    <input type="hidden" name="forwardEmail" id="forward-email" value="$!textutils.htmlEncode($forwardEmail)">
    <input type="hidden" name="catchEmail" id="catch-email" value="$!textutils.htmlEncode($catchEmail)">
    <input type="hidden" name="stripQuote" id="strip-quote" value="$!textutils.htmlEncode($stripQuote)">
    <input type="hidden" name="createUser" id="create-user" value="$!textutils.htmlEncode($createUser)">
    <input type="hidden" name="validationResult" id="validation-result" value="$!textutils.htmlEncode($action.getErrors().get("error"))">
</form>
</body>
</html>
