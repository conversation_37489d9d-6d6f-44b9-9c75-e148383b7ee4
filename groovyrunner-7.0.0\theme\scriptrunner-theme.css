.cm-s-scriptrunner-theme { z-index: 0 }
.cm-s-scriptrunner-theme.CodeMirror {
    height: 100%;
    background: #0E253B;
    color: #E6F0FA;
    box-shadow: unset;
    border-radius: 6px;
    border: 2px solid #020C15;
}
.cm-s-scriptrunner-theme div.CodeMirror-selected { background: #b36539 !important; }
.cm-s-scriptrunner-theme .CodeMirror-gutters { background: #48596A; border-right: 2px solid #566879; z-index: 2; }
.cm-s-scriptrunner-theme .CodeMirror-linenumber { color: #E6F0FA;   }
.cm-s-scriptrunner-theme .CodeMirror-linenumbers { padding-right: 8px; }
.cm-s-scriptrunner-theme .CodeMirror-cursor { border-left: 1px solid white !important; }

.cm-s-scriptrunner-theme span.cm-comment { color: #08f; }
.cm-s-scriptrunner-theme span.cm-atom { color: #845dc4; }
.cm-s-scriptrunner-theme span.cm-number, .cm-s-scriptrunner-theme span.cm-attribute { color: #ff80e1; }
.cm-s-scriptrunner-theme span.cm-keyword { color: #ffee80; }
.cm-s-scriptrunner-theme span.cm-string { color: #3ad900; }
.cm-s-scriptrunner-theme span.cm-meta { color: #ff9d00; }
.cm-s-scriptrunner-theme span.cm-variable-2, .cm-s-scriptrunner-theme span.cm-tag { color: #9effff; }
.cm-s-scriptrunner-theme span.cm-variable-3, .cm-s-scriptrunner-theme span.cm-def { color: white; }
.cm-s-scriptrunner-theme span.cm-bracket { color: #d8d8d8; }
.cm-s-scriptrunner-theme span.cm-builtin, .cm-s-scriptrunner-theme span.cm-special { color: #ff9e59; }
.cm-s-scriptrunner-theme span.cm-link { color: #845dc4; }
.cm-s-scriptrunner-theme span.cm-error { color: #9d1e15; }
.cm-s-scriptrunner-theme span.cm-quote {color: #090;}

.cm-s-scriptrunner-theme .CodeMirror-matchingbracket {outline:1px solid grey;color:white !important}

.CodeMirror-hint { z-index: 10 !important }
.CodeMirror-hint b { color:purple !important }
.CodeMirror-hint.CodeMirror-hint-active b {color:white !important}

.CodeMirror-Tern-tooltip {
    border: 1px solid silver;
    border-radius: 3px;
    color: #eeeeee;
    padding: 2px 5px;
    font-size: 90%;
    font-family: monospace;
    background-color: #4B4D4D;
    white-space: pre-wrap;

    max-width: 40em;
    position: absolute;
    z-index: 10;
    /*-webkit-box-shadow: 2px 3px 5px rgba(0,0,0,.2);*/
    /*-moz-box-shadow: 2px 3px 5px rgba(0,0,0,.2);*/
    /*box-shadow: 2px 3px 5px rgba(0,0,0,.2);*/

    transition: opacity 1s;
    -moz-transition: opacity 1s;
    -webkit-transition: opacity 1s;
    -o-transition: opacity 1s;
    -ms-transition: opacity 1s;
    font-weight: lighter;
}

.CodeMirror-Tern-tooltip .disabled {
    color: #aaaaaa;
}

.CodeMirror-Tern-tooltip b {
    font-weight: 900;
    color: white;
}

.CodeMirror-lint-tooltip {
    z-index: 1000000;
}
