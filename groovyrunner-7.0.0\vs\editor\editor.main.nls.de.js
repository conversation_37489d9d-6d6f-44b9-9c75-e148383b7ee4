/*!-----------------------------------------------------------
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Version: 0.33.0(c722ca6c7eed3d7987c0d5c3df5c45f6b15e77d1)
 * Released under the MIT license
 * https://github.com/microsoft/vscode/blob/main/LICENSE.txt
 *-----------------------------------------------------------*/define("vs/editor/editor.main.nls.de",{"vs/base/browser/ui/actionbar/actionViewItems":["{0} ({1})"],"vs/base/browser/ui/findinput/findInput":["Eingabe"],"vs/base/browser/ui/findinput/findInputCheckboxes":["Gro\xDF-/Kleinschreibung beachten","<PERSON>ur ganzes Wort suchen","Regul\xE4ren Ausdruck verwenden"],"vs/base/browser/ui/findinput/replaceInput":["Eingabe","Gro\xDF-/Kleinschreibung beibehalten"],"vs/base/browser/ui/iconLabel/iconLabelHover":["Wird geladen..."],"vs/base/browser/ui/inputbox/inputBox":["Fehler: {0}","Warnung: {0}","Info: {0}","f\xFCr Verlauf"],"vs/base/browser/ui/keybindingLabel/keybindingLabel":["Ungebunden"],"vs/base/browser/ui/tree/abstractTree":["L\xF6schen","Typfilter deaktivieren","Typfilter aktivieren","Keine Elemente gefunden","{0} von {1} Elementen stimmen \xFCberein"],"vs/base/common/actions":["(leer)"],"vs/base/common/errorMessage":["{0}: {1}","Ein Systemfehler ist aufgetreten ({0}).","Ein unbekannter Fehler ist aufgetreten. Weitere Details dazu finden Sie im Protokoll.","Ein unbekannter Fehler ist aufgetreten. Weitere Details dazu finden Sie im Protokoll.","{0} ({1} Fehler gesamt)","Ein unbekannter Fehler ist aufgetreten. Weitere Details dazu finden Sie im Protokoll."],"vs/base/common/keybindingLabels":["STRG","UMSCHALTTASTE","ALT","Windows","STRG","UMSCHALTTASTE","ALT","Super","Steuern","UMSCHALTTASTE","Option","Befehl","Steuern","UMSCHALTTASTE","ALT","Windows","Steuern","UMSCHALTTASTE","ALT","Super"],"vs/base/parts/quickinput/browser/quickInput":["Zur\xFCck","Dr\xFCcken Sie die EINGABETASTE, um Ihre Eingabe zu best\xE4tigen, oder ESC, um den Vorgang abzubrechen.","{0}/{1}","Nehmen Sie eine Eingabe vor, um die Ergebnisse einzugrenzen.","{0} Ergebnisse","{0} ausgew\xE4hlt","OK","Benutzerdefiniert","Zur\xFCck ({0})","Zur\xFCck"],"vs/base/parts/quickinput/browser/quickInputList":["Schnelleingabe"],"vs/editor/browser/controller/textAreaHandler":["Editor","Auf den Editor kann derzeit nicht zugegriffen werden. Dr\xFCcken Sie {0}, um die Optionen anzuzeigen."],"vs/editor/browser/coreCommands":["Auch bei l\xE4ngeren Zeilen am Ende bleiben","Auch bei l\xE4ngeren Zeilen am Ende bleiben","Sekund\xE4re Cursor entfernt"],"vs/editor/browser/editorExtensions":["&&R\xFCckg\xE4ngig","R\xFCckg\xE4ngig","&&Wiederholen","Wiederholen","&&Alles ausw\xE4hlen","Alle ausw\xE4hlen"],"vs/editor/browser/widget/codeEditorWidget":["Die Anzahl der Cursors wurde auf {0} beschr\xE4nkt."],"vs/editor/browser/widget/diffEditorWidget":["Zeilenformatierung f\xFCr Einf\xFCgungen im Diff-Editor","Zeilenformatierung f\xFCr Entfernungen im Diff-Editor","Kann die Dateien nicht vergleichen, da eine Datei zu gro\xDF ist."],"vs/editor/browser/widget/diffReview":['Symbol f\xFCr "Einf\xFCgen" in der Diff-\xDCberpr\xFCfung.','Symbol f\xFCr "Entfernen" in der Diff-\xDCberpr\xFCfung.','Symbol f\xFCr "Schlie\xDFen" in der Diff-\xDCberpr\xFCfung.',"Schlie\xDFen","keine ge\xE4nderten Zeilen","1 Zeile ge\xE4ndert","{0} Zeilen ge\xE4ndert","Unterschied {0} von {1}: urspr\xFCngliche Zeile {2}, {3}, ge\xE4nderte Zeile {4}, {5}","leer","{0}: unver\xE4nderte Zeile {1}","{0} urspr\xFCngliche Zeile {1} ge\xE4nderte Zeile {2}","+ {0} ge\xE4nderte Zeile(n) {1}","\u2013 {0} Originalzeile {1}","Zum n\xE4chsten Unterschied wechseln","Zum vorherigen Unterschied wechseln"],"vs/editor/browser/widget/inlineDiffMargin":["Gel\xF6schte Zeilen kopieren","Gel\xF6schte Zeile kopieren","Ge\xE4nderte Zeilen kopieren","Ge\xE4nderte Zeile kopieren","Gel\xF6schte Zeile kopieren ({0})","Ge\xE4nderte Zeile ({0}) kopieren","Diese \xC4nderung r\xFCckg\xE4ngig machen","Gel\xF6schte Zeile kopieren ({0})","Ge\xE4nderte Zeile ({0}) kopieren"],"vs/editor/common/config/editorConfigurationSchema":["Editor",'Die Anzahl der Leerzeichen, denen ein Tabstopp entspricht. Diese Einstellung wird basierend auf dem Inhalt der Datei \xFCberschrieben, wenn "#editor.detectIndentation#" aktiviert ist.','F\xFCgt beim Dr\xFCcken der TAB-Taste Leerzeichen ein. Diese Einstellung wird basierend auf dem Inhalt der Datei \xFCberschrieben, wenn "#editor.detectIndentation#" aktiviert ist.','Steuert, ob "#editor.tabSize#" und "#editor.insertSpaces#" automatisch erkannt werden, wenn eine Datei basierend auf dem Dateiinhalt ge\xF6ffnet wird.',"Nachfolgende automatisch eingef\xFCgte Leerzeichen entfernen","Spezielle Behandlung f\xFCr gro\xDFe Dateien zum Deaktivieren bestimmter speicherintensiver Funktionen.","Steuert, ob Vervollst\xE4ndigungen auf Grundlage der W\xF6rter im Dokument berechnet werden sollen.","Nur W\xF6rter aus dem aktiven Dokument vorschlagen","W\xF6rter aus allen ge\xF6ffneten Dokumenten derselben Sprache vorschlagen","W\xF6rter aus allen ge\xF6ffneten Dokumenten vorschlagen","Steuert, aus welchen Dokumenten wortbasierte Vervollst\xE4ndigungen berechnet werden.","Die semantische Hervorhebung ist f\xFCr alle Farbdesigns aktiviert.","Die semantische Hervorhebung ist f\xFCr alle Farbdesigns deaktiviert.",'Die semantische Hervorhebung wird durch die Einstellung "semanticHighlighting" des aktuellen Farbdesigns konfiguriert.',"Steuert, ob die semantische Hervorhebung f\xFCr die Sprachen angezeigt wird, die sie unterst\xFCtzen.","Peek-Editoren ge\xF6ffnet lassen, auch wenn auf den Inhalt doppelgeklickt oder die ESC-TASTE gedr\xFCckt wird.","Zeilen, die diese L\xE4nge \xFCberschreiten, werden aus Leistungsgr\xFCnden nicht tokenisiert","Definiert die Klammersymbole, die den Einzug vergr\xF6\xDFern oder verkleinern.","Das \xF6ffnende Klammerzeichen oder die Zeichenfolgensequenz.","Das schlie\xDFende Klammerzeichen oder die Zeichenfolgensequenz.","Definiert die Klammerpaare, die durch ihre Schachtelungsebene farbig formatiert werden, wenn die Farbgebung f\xFCr das Klammerpaar aktiviert ist.","Das \xF6ffnende Klammerzeichen oder die Zeichenfolgensequenz.","Das schlie\xDFende Klammerzeichen oder die Zeichenfolgensequenz.","Timeout in Millisekunden, nach dem die Diff-Berechnung abgebrochen wird. Bei 0 wird kein Timeout verwendet.","Maximale Dateigr\xF6\xDFe in MB, f\xFCr die Diffs berechnet werden sollen. Verwenden Sie 0, um keinen Grenzwert zu setzen.","Steuert, ob der Diff-Editor die Unterschiede nebeneinander oder im Text anzeigt.","Wenn aktiviert, ignoriert der Diff-Editor \xC4nderungen an voran- oder nachgestellten Leerzeichen.",'Steuert, ob der Diff-Editor die Indikatoren "+" und "-" f\xFCr hinzugef\xFCgte/entfernte \xC4nderungen anzeigt.',"Steuert, ob der Editor CodeLens anzeigt.","Zeilenumbr\xFCche erfolgen nie.","Der Zeilenumbruch erfolgt an der Breite des Anzeigebereichs.",'Zeilen werden entsprechend der Einstellung "#editor.wordWrap#" umbrochen.'],"vs/editor/common/config/editorOptions":["Der Editor verwendet Plattform-APIs, um zu erkennen, wenn eine Sprachausgabe angef\xFCgt wird.","Der Editor wird dauerhaft f\xFCr die Verwendung mit einer Sprachausgabe optimiert. Zeilenumbr\xFCche werden deaktiviert.","Der Editor wird nie f\xFCr die Verwendung mit einer Sprachausgabe optimiert.",'Steuert, ob der Editor in einem f\xFCr die Sprachausgabe optimierten Modus ausgef\xFChrt werden soll. Durch Festlegen auf "Ein" werden Zeilenumbr\xFCche deaktiviert.',"Steuert, ob beim Kommentieren ein Leerzeichen eingef\xFCgt wird.","Steuert, ob leere Zeilen bei Umschalt-, Hinzuf\xFCgungs- oder Entfernungsaktionen f\xFCr Zeilenkommentare ignoriert werden sollen.","Steuert, ob ein Kopiervorgang ohne Auswahl die aktuelle Zeile kopiert.","Steuert, ob der Cursor bei der Suche nach \xDCbereinstimmungen w\xE4hrend der Eingabe springt.","Suchzeichenfolge niemals aus der Editorauswahl seeden.","Suchzeichenfolge immer aus der Editorauswahl seeden, einschlie\xDFlich Wort an Cursorposition.","Suchzeichenfolge nur aus der Editorauswahl seeden.",'Steuert, ob f\xFCr die Suchzeichenfolge im Widget "Suche" ein Seeding aus der Auswahl des Editors ausgef\xFChrt wird.','"In Auswahl suchen" niemals automatisch aktivieren (Standard).','"In Auswahl suchen" immer automatisch aktivieren.','"In Auswahl suchen" automatisch aktivieren, wenn mehrere Inhaltszeilen ausgew\xE4hlt sind.','Steuert die Bedingung zum automatischen Aktivieren von "In Auswahl suchen".','Steuert, ob das Widget "Suche" die freigegebene Suchzwischenablage unter macOS lesen oder bearbeiten soll.','Steuert, ob das Suchwidget zus\xE4tzliche Zeilen im oberen Bereich des Editors hinzuf\xFCgen soll. Wenn die Option auf "true" festgelegt ist, k\xF6nnen Sie \xFCber die erste Zeile hinaus scrollen, wenn das Suchwidget angezeigt wird.',"Steuert, ob die Suche automatisch am Anfang (oder am Ende) neu gestartet wird, wenn keine weiteren \xDCbereinstimmungen gefunden werden.",'Hiermit werden Schriftligaturen (Schriftartfeatures "calt" und "liga") aktiviert/deaktiviert. \xC4ndern Sie diesen Wert in eine Zeichenfolge, um die CSS-Eigenschaft "font-feature-settings" detailliert zu steuern.','Explizite CSS-Eigenschaft "font-feature-settings". Stattdessen kann ein boolescher Wert \xFCbergeben werden, wenn nur Ligaturen aktiviert/deaktiviert werden m\xFCssen.','Hiermit werden Schriftligaturen oder Schriftartfeatures konfiguriert. Hierbei kann es sich entweder um einen booleschen Wert zum Aktivieren oder Deaktivieren von Ligaturen oder um eine Zeichenfolge f\xFCr den Wert der CSS-Eigenschaft "font-feature-settings" handeln.',"Legt die Schriftgr\xF6\xDFe in Pixeln fest.",'Es sind nur die Schl\xFCsselw\xF6rter "normal" und "bold" sowie Zahlen zwischen 1 und 1000 zul\xE4ssig.','Steuert die Schriftbreite. Akzeptiert die Schl\xFCsselw\xF6rter "normal" und "bold" sowie Zahlen zwischen 1 und 1000.',"Vorschauansicht der Ergebnisse anzeigen (Standardeinstellung)","Zum Hauptergebnis gehen und Vorschauansicht anzeigen","Wechseln Sie zum prim\xE4ren Ergebnis, und aktivieren Sie die Navigation ohne Vorschau zu anderen Ergebnissen.",'Diese Einstellung ist veraltet. Verwenden Sie stattdessen separate Einstellungen wie "editor.editor.gotoLocation.multipleDefinitions" oder "editor.editor.gotoLocation.multipleImplementations".','Legt das Verhalten des Befehls "Gehe zu Definition" fest, wenn mehrere Zielpositionen vorhanden sind','Legt das Verhalten des Befehls "Gehe zur Typdefinition" fest, wenn mehrere Zielpositionen vorhanden sind.','Legt das Verhalten des Befehls "Gehe zu Deklaration" fest, wenn mehrere Zielpositionen vorhanden sind.','Legt das Verhalten des Befehls "Gehe zu Implementierungen", wenn mehrere Zielspeicherorte vorhanden sind','Legt das Verhalten des Befehls "Gehe zu Verweisen" fest, wenn mehrere Zielpositionen vorhanden sind','Die alternative Befehls-ID, die ausgef\xFChrt wird, wenn das Ergebnis von "Gehe zu Definition" die aktuelle Position ist.','Die alternative Befehls-ID, die ausgef\xFChrt wird, wenn das Ergebnis von "Gehe zu Typdefinition" die aktuelle Position ist.','Die alternative Befehls-ID, die ausgef\xFChrt wird, wenn das Ergebnis von "Gehe zu Deklaration" der aktuelle Speicherort ist.','Die alternative Befehls-ID, die ausgef\xFChrt wird, wenn das Ergebnis von "Gehe zu Implementatierung" der aktuelle Speicherort ist.','Die alternative Befehls-ID, die ausgef\xFChrt wird, wenn das Ergebnis von "Gehe zu Verweis" die aktuelle Position ist.',"Steuert, ob die Hovermarkierung angezeigt wird.","Steuert die Verz\xF6gerung in Millisekunden, nach der die Hovermarkierung angezeigt wird.","Steuert, ob die Hovermarkierung sichtbar bleiben soll, wenn der Mauszeiger dar\xFCber bewegt wird.","Zeigen Sie den Mauszeiger lieber \xFCber der Linie an, wenn Platz vorhanden ist.","Aktiviert das Gl\xFChbirnensymbol f\xFCr Codeaktionen im Editor.","Aktiviert die Inlay-Hinweise im Editor.",'Steuert den Schriftgrad von Inlayhinweisen im Editor. Ein Standardwert von 90 % der "#editor.fontSize#" wird verwendet, wenn der konfigurierte Wert kleiner als \u201E5\u201C oder gr\xF6\xDFer als der Schriftgrad des Editors ist.',"Steuert die Schriftfamilie f\xFCr Inlay-Hinweise im Editor. Wenn der Wert \u201Eleer\u201C festgelegt wird, wird die \u201E#editor.fontFamily#\u201C verwendet.",`Steuert die Zeilenh\xF6he. \r
 \u2013 Verwenden Sie 0, um die Zeilenh\xF6he automatisch anhand des Schriftgrads zu berechnen.\r
 \u2013 Werte zwischen 0 und 8 werden als Multiplikator mit dem Schriftgrad verwendet.\r
 \u2013 Werte gr\xF6\xDFer oder gleich 8 werden als effektive Werte verwendet.`,"Steuert, ob die Minimap angezeigt wird.","Die Minimap hat die gleiche Gr\xF6\xDFe wie der Editor-Inhalt (und kann scrollen).","Die Minimap wird bei Bedarf vergr\xF6\xDFert oder verkleinert, um die H\xF6he des Editors zu f\xFCllen (kein Scrollen).","Die Minimap wird bei Bedarf verkleinert, damit sie nicht gr\xF6\xDFer als der Editor ist (kein Scrollen).","Legt die Gr\xF6\xDFe der Minimap fest.","Steuert die Seite, wo die Minimap gerendert wird.","Steuert, wann der Schieberegler f\xFCr die Minimap angezeigt wird.","Ma\xDFstab des in der Minimap gezeichneten Inhalts: 1, 2 oder 3.","Die tats\xE4chlichen Zeichen in einer Zeile rendern im Gegensatz zu Farbbl\xF6cken.","Begrenzen Sie die Breite der Minimap, um nur eine bestimmte Anzahl von Spalten zu rendern.","Steuert den Abstand zwischen dem oberen Rand des Editors und der ersten Zeile.","Steuert den Abstand zwischen dem unteren Rand des Editors und der letzten Zeile.","Aktiviert ein Pop-up, das Dokumentation und Typ eines Parameters anzeigt w\xE4hrend Sie tippen.","Steuert, ob das Men\xFC mit Parameterhinweisen zyklisch ist oder sich am Ende der Liste schlie\xDFt.","Schnellvorschl\xE4ge innerhalb von Zeichenfolgen aktivieren.","Schnellvorschl\xE4ge innerhalb von Kommentaren aktivieren.","Schnellvorschl\xE4ge au\xDFerhalb von Zeichenfolgen und Kommentaren aktivieren.","Steuert, ob Vorschl\xE4ge automatisch w\xE4hrend der Eingabe angezeigt werden sollen.","Zeilennummern werden nicht dargestellt.","Zeilennummern werden als absolute Zahl dargestellt.","Zeilennummern werden als Abstand in Zeilen an Cursorposition dargestellt.","Zeilennummern werden alle 10 Zeilen dargestellt.","Steuert die Anzeige von Zeilennummern.","Anzahl der Zeichen aus Festbreitenschriftarten, ab der dieses Editor-Lineal gerendert wird.","Farbe dieses Editor-Lineals.","Vertikale Linien nach einer bestimmten Anzahl von Monospacezeichen rendern. Verwenden Sie mehrere Werte f\xFCr mehrere Linien. Wenn das Array leer ist, werden keine Linien gerendert.","Die vertikale Bildlaufleiste wird nur bei Bedarf angezeigt.","Die vertikale Bildlaufleiste ist immer sichtbar.","Die vertikale Bildlaufleiste wird immer ausgeblendet.","Steuert die Sichtbarkeit der vertikalen Bildlaufleiste.","Die horizontale Bildlaufleiste wird nur bei Bedarf angezeigt.","Die horizontale Bildlaufleiste ist immer sichtbar.","Die horizontale Bildlaufleiste wird immer ausgeblendet.","Steuert die Sichtbarkeit der horizontalen Bildlaufleiste.","Die Breite der vertikalen Bildlaufleiste.","Die H\xF6he der horizontalen Bildlaufleiste.","Steuert, ob Klicks nach Seite scrollen oder zur Klickposition springen.","Legt fest, ob alle nicht einfachen ASCII-Zeichen hervorgehoben werden. Nur Zeichen zwischen U+0020 und U+007E, Tabulator, Zeilenvorschub und Wagenr\xFCcklauf gelten als einfache ASCII-Zeichen.","Legt fest, ob Zeichen, die nur als Platzhalter dienen oder \xFCberhaupt keine Breite haben, hervorgehoben werden.","Legt fest, ob Zeichen hervorgehoben werden, die mit einfachen ASCII-Zeichen verwechselt werden k\xF6nnen, mit Ausnahme derjenigen, die im aktuellen Gebietsschema des Benutzers \xFCblich sind.","Legt fest, ob Zeichen in Kommentaren auch mit Unicode-Hervorhebung versehen werden sollen.","Legt fest, ob Zeichen in Zeichenfolgen auch mit Unicode-Hervorhebung versehen werden sollen.","Definiert zul\xE4ssige Zeichen, die nicht hervorgehoben werden.","Unicodezeichen, die in zul\xE4ssigen Gebietsschemas \xFCblich sind, werden nicht hervorgehoben.","Steuert, ob Inline-Vorschl\xE4ge automatisch im Editor angezeigt werden.","Steuert, ob die Farbgebung f\xFCr das Klammerpaar aktiviert ist. Verwenden Sie \u201Eworkbench.colorCustomizations\u201C, um die Hervorhebungsfarben der Klammer au\xDFer Kraft zu setzen.","Aktiviert Klammernpaarf\xFChrungslinien.","Aktiviert Klammernpaarf\xFChrungslinien nur f\xFCr das aktive Klammerpaar.","Deaktiviert Klammernpaarf\xFChrungslinien.","Steuert, ob F\xFChrungslinien f\xFCr Klammerpaare aktiviert sind oder nicht.","Aktiviert horizontale F\xFChrungslinien als Erg\xE4nzung zu vertikalen Klammernpaarf\xFChrungslinien.","Aktiviert horizontale F\xFChrungslinien nur f\xFCr das aktive Klammerpaar.","Deaktiviert horizontale F\xFChrungslinien f\xFCr Klammernpaare.","Steuert, ob horizontale F\xFChrungslinien f\xFCr Klammernpaare aktiviert sind oder nicht.","Steuert, ob der Editor das aktive Klammerpaar hervorheben soll.","Steuert, ob der Editor Einzugsf\xFChrungslinien rendern soll.","Steuert, ob der Editor die aktive Einzugsf\xFChrungslinie hevorheben soll.","Vorschlag einf\xFCgen, ohne den Text auf der rechten Seite des Cursors zu \xFCberschreiben","Vorschlag einf\xFCgen und Text auf der rechten Seite des Cursors \xFCberschreiben","Legt fest, ob W\xF6rter beim Akzeptieren von Vervollst\xE4ndigungen \xFCberschrieben werden. Beachten Sie, dass dies von Erweiterungen abh\xE4ngt, die f\xFCr dieses Features aktiviert sind.","Steuert, ob Filter- und Suchvorschl\xE4ge geringf\xFCgige Tippfehler ber\xFCcksichtigen.","Steuert, ob bei der Sortierung W\xF6rter priorisiert werden, die in der N\xE4he des Cursors stehen.",'Steuert, ob gespeicherte Vorschlagauswahlen in verschiedenen Arbeitsbereichen und Fenstern gemeinsam verwendet werden (daf\xFCr ist "#editor.suggestSelection#" erforderlich).','Steuert, ob ein aktiver Schnipsel verhindert, dass der Bereich "Schnelle Vorschl\xE4ge" angezeigt wird.',"Steuert, ob Symbole in Vorschl\xE4gen ein- oder ausgeblendet werden.","Steuert die Sichtbarkeit der Statusleiste unten im Vorschlagswidget.","Steuert, ob das Ergebnis des Vorschlags im Editor in der Vorschau angezeigt werden soll.","Steuert, ob Vorschlagsdetails inline mit der Bezeichnung oder nur im Detailwidget angezeigt werden.","Diese Einstellung ist veraltet. Die Gr\xF6\xDFe des Vorschlagswidgets kann jetzt ge\xE4ndert werden.",'Diese Einstellung ist veraltet. Verwenden Sie stattdessen separate Einstellungen wie "editor.suggest.showKeywords" oder "editor.suggest.showSnippets".','Wenn aktiviert, zeigt IntelliSense "method"-Vorschl\xE4ge an.','Wenn aktiviert, zeigt IntelliSense "funktions"-Vorschl\xE4ge an.','Wenn aktiviert, zeigt IntelliSense "constructor"-Vorschl\xE4ge an.',"Wenn IntelliSense aktiviert ist, werden \u201Everaltete\u201C Vorschl\xE4ge angezeigt.",'Wenn aktiviert, zeigt IntelliSense "field"-Vorschl\xE4ge an.','Wenn aktiviert, zeigt IntelliSense "variable"-Vorschl\xE4ge an.','Wenn aktiviert, zeigt IntelliSense "class"-Vorschl\xE4ge an.','Wenn aktiviert, zeigt IntelliSense "struct"-Vorschl\xE4ge an.','Wenn aktiviert, zeigt IntelliSense "interface"-Vorschl\xE4ge an.','Wenn aktiviert, zeigt IntelliSense "module"-Vorschl\xE4ge an.','Wenn aktiviert, zeigt IntelliSense "property"-Vorschl\xE4ge an.','Wenn aktiviert, zeigt IntelliSense "event"-Vorschl\xE4ge an.','Wenn aktiviert, zeigt IntelliSense "operator"-Vorschl\xE4ge an.','Wenn aktiviert, zeigt IntelliSense "unit"-Vorschl\xE4ge an.','Wenn aktiviert, zeigt IntelliSense "value"-Vorschl\xE4ge an.','Wenn aktiviert, zeigt IntelliSense "constant"-Vorschl\xE4ge an.','Wenn aktiviert, zeigt IntelliSense "enum"-Vorschl\xE4ge an.','Wenn aktiviert, zeigt IntelliSense "enumMember"-Vorschl\xE4ge an.','Wenn aktiviert, zeigt IntelliSense "keyword"-Vorschl\xE4ge an.','Wenn aktiviert, zeigt IntelliSense "text"-Vorschl\xE4ge an.','Wenn aktiviert, zeigt IntelliSense "color"-Vorschl\xE4ge an.','Wenn aktiviert, zeigt IntelliSense "file"-Vorschl\xE4ge an.','Wenn aktiviert, zeigt IntelliSense "reference"-Vorschl\xE4ge an.','Wenn aktiviert, zeigt IntelliSense "customcolor"-Vorschl\xE4ge an.','Wenn aktiviert, zeigt IntelliSense "folder"-Vorschl\xE4ge an.','Wenn aktiviert, zeigt IntelliSense "typeParameter"-Vorschl\xE4ge an.','Wenn aktiviert, zeigt IntelliSense "snippet"-Vorschl\xE4ge an.',"Wenn aktiviert, zeigt IntelliSense user-Vorschl\xE4ge an.","Wenn aktiviert, zeigt IntelliSense issues-Vorschl\xE4ge an.","Gibt an, ob f\xFChrende und nachstehende Leerzeichen immer ausgew\xE4hlt werden sollen.",'Steuert, ob Vorschl\xE4ge \xFCber Commitzeichen angenommen werden sollen. In JavaScript kann ein Semikolon (";") beispielsweise ein Commitzeichen sein, das einen Vorschlag annimmt und dieses Zeichen eingibt.',"Einen Vorschlag nur mit der EINGABETASTE akzeptieren, wenn dieser eine \xC4nderung am Text vornimmt.","Steuert, ob Vorschl\xE4ge mit der EINGABETASTE (zus\xE4tzlich zur TAB-Taste) akzeptiert werden sollen. Vermeidet Mehrdeutigkeit zwischen dem Einf\xFCgen neuer Zeilen oder dem Annehmen von Vorschl\xE4gen.","Steuert die Anzahl von Zeilen im Editor, die von einer Sprachausgabe in einem Arbeitsschritt gelesen werden k\xF6nnen. Wenn eine Sprachausgabe erkannt wird, wird der Standardwert automatisch auf 500 festgelegt. Warnung: Ein Wert h\xF6her als der Standardwert, kann sich auf die Leistung auswirken.","Editor-Inhalt","Verwenden Sie Sprachkonfigurationen, um zu bestimmen, wann Klammern automatisch geschlossen werden sollen.","Schlie\xDFe Klammern nur automatisch, wenn der Cursor sich links von einem Leerzeichen befindet.","Steuert, ob der Editor automatisch Klammern schlie\xDFen soll, nachdem der Benutzer eine \xF6ffnende Klammer hinzugef\xFCgt hat.","Angrenzende schlie\xDFende Anf\xFChrungszeichen oder Klammern werden nur \xFCberschrieben, wenn sie automatisch eingef\xFCgt wurden.","Steuert, ob der Editor angrenzende schlie\xDFende Anf\xFChrungszeichen oder Klammern beim L\xF6schen entfernen soll.","Schlie\xDFende Anf\xFChrungszeichen oder Klammern werden nur \xFCberschrieben, wenn sie automatisch eingef\xFCgt wurden.","Steuert, ob der Editor schlie\xDFende Anf\xFChrungszeichen oder Klammern \xFCberschreiben soll.","Verwende die Sprachkonfiguration, um zu ermitteln, wann Anf\xFChrungsstriche automatisch geschlossen werden.","Schlie\xDFende Anf\xFChrungszeichen nur dann automatisch erg\xE4nzen, wenn der Cursor sich links von einem Leerzeichen befindet.","Steuert, ob der Editor Anf\xFChrungszeichen automatisch schlie\xDFen soll, nachdem der Benutzer ein \xF6ffnendes Anf\xFChrungszeichen hinzugef\xFCgt hat.","Der Editor f\xFCgt den Einzug nicht automatisch ein.","Der Editor beh\xE4lt den Einzug der aktuellen Zeile bei.","Der Editor beh\xE4lt den in der aktuellen Zeile definierten Einzug bei und beachtet f\xFCr Sprachen definierte Klammern.","Der Editor beh\xE4lt den Einzug der aktuellen Zeile bei, beachtet von Sprachen definierte Klammern und ruft spezielle onEnterRules-Regeln auf, die von Sprachen definiert wurden.","Der Editor beh\xE4lt den Einzug der aktuellen Zeile bei, beachtet die von Sprachen definierten Klammern, ruft von Sprachen definierte spezielle onEnterRules-Regeln auf und beachtet von Sprachen definierte indentationRules-Regeln.","Legt fest, ob der Editor den Einzug automatisch anpassen soll, wenn Benutzer Zeilen eingeben, einf\xFCgen, verschieben oder einr\xFCcken","Sprachkonfigurationen verwenden, um zu bestimmen, wann eine Auswahl automatisch umschlossen werden soll.","Mit Anf\xFChrungszeichen, nicht mit Klammern umschlie\xDFen.","Mit Klammern, nicht mit Anf\xFChrungszeichen umschlie\xDFen.","Steuert, ob der Editor die Auswahl beim Eingeben von Anf\xFChrungszeichen oder Klammern automatisch umschlie\xDFt.","Emuliert das Auswahlverhalten von Tabstoppzeichen, wenn Leerzeichen f\xFCr den Einzug verwendet werden. Die Auswahl wird an Tabstopps ausgerichtet.","Steuert, ob der Editor CodeLens anzeigt.","Steuert die Schriftfamilie f\xFCr CodeLens.","Steuert den Schriftgrad in Pixeln f\xFCr CodeLens. Bei Festlegung auf \u201E0\u201C werden 90\xA0% von \u201E#editor.fontSize#\u201C verwendet.","Steuert, ob der Editor die Inline-Farbdecorators und die Farbauswahl rendern soll.","Zulassen, dass die Auswahl per Maus und Tasten die Spaltenauswahl durchf\xFChrt.","Steuert, ob Syntax-Highlighting in die Zwischenablage kopiert wird.","Steuert den Cursoranimationsstil.","Steuert, ob die weiche Cursoranimation aktiviert werden soll.","Steuert den Cursor-Stil.",'Steuert die Mindestanzahl sichtbarer f\xFChrender und nachfolgender Zeilen um den Cursor. Dies wird in einigen anderen Editoren als "scrollOff" oder "scrollOffset" bezeichnet.','"cursorSurroundingLines" wird nur erzwungen, wenn die Ausl\xF6sung \xFCber die Tastatur oder API erfolgt.','"cursorSurroundingLines" wird immer erzwungen.',"Legt fest, wann cursorSurroundingLines erzwungen werden soll","Steuert die Breite des Cursors, wenn `#editor.cursorStyle#` auf `line` festgelegt ist.","Steuert, ob der Editor das Verschieben einer Auswahl per Drag and Drop zul\xE4sst.","Multiplikator f\xFCr Scrollgeschwindigkeit bei Dr\xFCcken von ALT.","Steuert, ob Codefaltung im Editor aktiviert ist.","Verwenden Sie eine sprachspezifische Faltstrategie, falls verf\xFCgbar. Andernfalls wird eine einzugsbasierte verwendet.","Einzugsbasierte Faltstrategie verwenden.","Steuert die Strategie f\xFCr die Berechnung von Faltbereichen.","Steuert, ob der Editor eingefaltete Bereiche hervorheben soll.","Steuert, ob der Editor Importbereiche automatisch reduziert.","Die maximale Anzahl von faltbaren Regionen. Eine Erh\xF6hung dieses Werts kann dazu f\xFChren, dass der Editor weniger reaktionsf\xE4hig wird, wenn die aktuelle Quelle eine gro\xDFe Anzahl von faltbaren Regionen aufweist.","Steuert, ob eine Zeile aufgefaltet wird, wenn nach einer gefalteten Zeile auf den leeren Inhalt geklickt wird.","Steuert die Schriftfamilie.","Steuert, ob der Editor den eingef\xFCgten Inhalt automatisch formatieren soll. Es muss ein Formatierer vorhanden sein, der in der Lage ist, auch Dokumentbereiche zu formatieren.","Steuert, ob der Editor die Zeile nach der Eingabe automatisch formatieren soll.","Steuert, ob der Editor den vertikalen Glyphenrand rendert. Der Glyphenrand wird haupts\xE4chlich zum Debuggen verwendet.","Steuert, ob der Cursor im \xDCbersichtslineal ausgeblendet werden soll.","Legt den Abstand der Buchstaben in Pixeln fest.","Steuert, ob die verkn\xFCpfte Bearbeitung im Editor aktiviert ist. Abh\xE4ngig von der Sprache werden zugeh\xF6rige Symbole, z.\xA0B. HTML-Tags, w\xE4hrend der Bearbeitung aktualisiert.","Steuert, ob der Editor Links erkennen und anklickbar machen soll.","Passende Klammern hervorheben",'Ein Multiplikator, der f\xFCr die Mausrad-Bildlaufereignisse "deltaX" und "deltaY" verwendet werden soll.',"Schriftart des Editors vergr\xF6\xDFern, wenn das Mausrad verwendet und die STRG-TASTE gedr\xFCckt wird.","Mehrere Cursor zusammenf\xFChren, wenn sie sich \xFCberlappen.","Ist unter Windows und Linux der STRG-Taste und unter macOS der Befehlstaste zugeordnet.","Ist unter Windows und Linux der ALT-Taste und unter macOS der Wahltaste zugeordnet.",'Der Modifizierer, der zum Hinzuf\xFCgen mehrerer Cursor mit der Maus verwendet wird. Die Mausbewegungen "Gehe zu Definition" und "Link \xF6ffnen" werden so angepasst, dass kein Konflikt mit dem Multi-Cursor-Modifizierer entsteht. [Weitere Informationen](https://code.visualstudio.com/docs/editor/codebasics#_multicursor-modifier).',"Jeder Cursor f\xFCgt eine Textzeile ein.","Jeder Cursor f\xFCgt den vollst\xE4ndigen Text ein.","Steuert das Einf\xFCgen, wenn die Zeilenanzahl des Einf\xFCgetexts der Cursor-Anzahl entspricht.","Steuert, ob der Editor das Vorkommen semantischer Symbole hervorheben soll.","Steuert, ob um das \xDCbersichtslineal ein Rahmen gezeichnet werden soll.","Struktur beim \xD6ffnen des Peek-Editors fokussieren","Editor fokussieren, wenn Sie den Peek-Editor \xF6ffnen","Steuert, ob der Inline-Editor oder die Struktur im Peek-Widget fokussiert werden soll.",'Steuert, ob die Mausgeste "Gehe zu Definition" immer das Vorschauwidget \xF6ffnet.',"Steuert die Verz\xF6gerung in Millisekunden nach der Schnellvorschl\xE4ge angezeigt werden.","Steuert, ob der Editor bei Eingabe automatisch eine Umbenennung vornimmt.",'Veraltet. Verwenden Sie stattdessen "editor.linkedEditing".',"Steuert, ob der Editor Steuerzeichen rendern soll.","Letzte Zeilennummer rendern, wenn die Datei mit einem Zeilenumbruch endet.","Hebt den Bundsteg und die aktuelle Zeile hervor.","Steuert, wie der Editor die aktuelle Zeilenhervorhebung rendern soll.","Steuert, ob der Editor die aktuelle Zeilenhervorhebung nur dann rendern soll, wenn der Fokus auf dem Editor liegt.","Leerraumzeichen werden gerendert mit Ausnahme der einzelnen Leerzeichen zwischen W\xF6rtern.","Hiermit werden Leerraumzeichen nur f\xFCr ausgew\xE4hlten Text gerendert.","Nur nachstehende Leerzeichen rendern","Steuert, wie der Editor Leerzeichen rendern soll.","Steuert, ob eine Auswahl abgerundete Ecken aufweisen soll.","Steuert die Anzahl der zus\xE4tzlichen Zeichen, nach denen der Editor horizontal scrollt.","Steuert, ob der Editor jenseits der letzten Zeile scrollen wird.","Nur entlang der vorherrschenden Achse scrollen, wenn gleichzeitig vertikal und horizontal gescrollt wird. Dadurch wird ein horizontaler Versatz beim vertikalen Scrollen auf einem Trackpad verhindert.","Steuert, ob die prim\xE4re Linux-Zwischenablage unterst\xFCtzt werden soll.","Steuert, ob der Editor \xDCbereinstimmungen hervorheben soll, die der Auswahl \xE4hneln.","Steuerelemente f\xFCr die Codefaltung immer anzeigen.","Steuerelemente f\xFCr die Codefaltung nur anzeigen, wenn sich die Maus \xFCber dem Bundsteg befindet.","Steuert, wann die Steuerungselemente f\xFCr die Codefaltung am Bundsteg angezeigt werden.","Steuert das Ausblenden von nicht verwendetem Code.","Steuert durchgestrichene veraltete Variablen.","Zeige Schnipselvorschl\xE4ge \xFCber den anderen Vorschl\xE4gen.","Schnipselvorschl\xE4ge unter anderen Vorschl\xE4gen anzeigen.","Zeige Schnipselvorschl\xE4ge mit anderen Vorschl\xE4gen.","Keine Schnipselvorschl\xE4ge anzeigen.","Steuert, ob Codeschnipsel mit anderen Vorschl\xE4gen angezeigt und wie diese sortiert werden.","Legt fest, ob der Editor Bildl\xE4ufe animiert ausf\xFChrt.",'Schriftgr\xF6\xDFe f\xFCr das vorgeschlagene Widget. Bei Festlegung auf 0 wird der Wert von "#editor.fontSize#" verwendet.','Zeilenh\xF6he f\xFCr das vorgeschlagene Widget. Bei Festlegung auf 0 wird der Wert von "#editor.lineHeight#" verwendet. Der Mindestwert ist 8.',"Steuert, ob Vorschl\xE4ge automatisch angezeigt werden sollen, wenn Triggerzeichen eingegeben werden.","Immer den ersten Vorschlag ausw\xE4hlen.",'W\xE4hlen Sie die aktuellsten Vorschl\xE4ge aus, es sei denn, es wird ein Vorschlag durch eine weitere Eingabe ausgew\xE4hlt, z.B. "console.| -> console.log", weil "log" vor Kurzem abgeschlossen wurde.','W\xE4hlen Sie Vorschl\xE4ge basierend auf fr\xFCheren Pr\xE4fixen aus, die diese Vorschl\xE4ge abgeschlossen haben, z.B. "co -> console" und "con ->" const".',"Steuert, wie Vorschl\xE4ge bei Anzeige der Vorschlagsliste vorab ausgew\xE4hlt werden.","Die Tab-Vervollst\xE4ndigung f\xFCgt den passendsten Vorschlag ein, wenn auf Tab gedr\xFCckt wird.","Tab-Vervollst\xE4ndigungen deaktivieren.",'Codeschnipsel per Tab vervollst\xE4ndigen, wenn die Pr\xE4fixe \xFCbereinstimmen. Funktioniert am besten, wenn "quickSuggestions" deaktiviert sind.',"Tab-Vervollst\xE4ndigungen aktivieren.","Ungew\xF6hnliche Zeilenabschlusszeichen werden automatisch entfernt.","Ungew\xF6hnliche Zeilenabschlusszeichen werden ignoriert.","Zum Entfernen ungew\xF6hnlicher Zeilenabschlusszeichen wird eine Eingabeaufforderung angezeigt.","Entfernen Sie un\xFCbliche Zeilenabschlusszeichen, die Probleme verursachen k\xF6nnen.","Das Einf\xFCgen und L\xF6schen von Leerzeichen erfolgt nach Tabstopps.","Zeichen, die als Worttrennzeichen verwendet werden, wenn wortbezogene Navigationen oder Vorg\xE4nge ausgef\xFChrt werden.","Zeilenumbr\xFCche erfolgen nie.","Der Zeilenumbruch erfolgt an der Breite des Anzeigebereichs.",'Der Zeilenumbruch erfolgt bei "#editor.wordWrapColumn#".','Der Zeilenumbruch erfolgt beim Mindestanzeigebereich und "#editor.wordWrapColumn".',"Steuert, wie der Zeilenumbruch durchgef\xFChrt werden soll.",'Steuert die umschlie\xDFende Spalte des Editors, wenn "#editor.wordWrap#" den Wert "wordWrapColumn" oder "bounded" aufweist.',"Kein Einzug. Umbrochene Zeilen beginnen bei Spalte 1.","Umbrochene Zeilen erhalten den gleichen Einzug wie das \xFCbergeordnete Element.","Umbrochene Zeilen erhalten + 1 Einzug auf das \xFCbergeordnete Element.","Umgebrochene Zeilen werden im Vergleich zum \xFCbergeordneten Element +2 einger\xFCckt.","Steuert die Einr\xFCckung der umbrochenen Zeilen.","Es wird angenommen, dass alle Zeichen gleich breit sind. Dies ist ein schneller Algorithmus, der f\xFCr Festbreitenschriftarten und bestimmte Alphabete (wie dem lateinischen), bei denen die Glyphen gleich breit sind, korrekt funktioniert.","Delegiert die Berechnung von Umbruchpunkten an den Browser. Dies ist ein langsamer Algorithmus, der bei gro\xDFen Dateien Code Freezes verursachen kann, aber in allen F\xE4llen korrekt funktioniert.","Steuert den Algorithmus, der Umbruchpunkte berechnet."],"vs/editor/common/core/editorColorRegistry":["Hintergrundfarbe zur Hervorhebung der Zeile an der Cursorposition.","Hintergrundfarbe f\xFCr den Rahmen um die Zeile an der Cursorposition.","Hintergrundfarbe der markierten Bereiche, wie z.B. Quick Open oder die Suche. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.","Hintergrundfarbe f\xFCr den Rahmen um hervorgehobene Bereiche.",'Hintergrundfarbe des hervorgehobenen Symbols, z. B. "Gehe zu Definition" oder "Gehe zu n\xE4chster/vorheriger". Die Farbe darf nicht undurchsichtig sein, um zugrunde liegende Dekorationen nicht zu verbergen.',"Hintergrundfarbe des Rahmens um hervorgehobene Symbole","Farbe des Cursors im Editor.","Hintergrundfarbe vom Editor-Cursor. Erlaubt die Anpassung der Farbe von einem Zeichen, welches von einem Block-Cursor \xFCberdeckt wird.","Farbe der Leerzeichen im Editor.","Farbe der F\xFChrungslinien f\xFCr Einz\xFCge im Editor.","Farbe der F\xFChrungslinien f\xFCr Einz\xFCge im aktiven Editor.","Zeilennummernfarbe im Editor.","Zeilennummernfarbe der aktiven Editorzeile.",'Die ID ist veraltet. Verwenden Sie stattdessen "editorLineNumber.activeForeground".',"Zeilennummernfarbe der aktiven Editorzeile.","Farbe des Editor-Lineals.","Vordergrundfarbe der CodeLens-Links im Editor","Hintergrundfarbe f\xFCr zusammengeh\xF6rige Klammern","Farbe f\xFCr zusammengeh\xF6rige Klammern","Farbe des Rahmens f\xFCr das \xDCbersicht-Lineal.","Hintergrundfarbe des \xDCbersichtslineals im Editor. Wird nur verwendet, wenn die Minimap aktiviert ist und auf der rechten Seite des Editors platziert wird.","Hintergrundfarbe der Editorleiste. Die Leiste enth\xE4lt die Glyphenr\xE4nder und die Zeilennummern.","Rahmenfarbe unn\xF6tigen (nicht genutzten) Quellcodes im Editor.",'Deckkraft des unn\xF6tigen (nicht genutzten) Quellcodes im Editor. "#000000c0" rendert z.B. den Code mit einer Deckkraft von 75%. Verwenden Sie f\xFCr Designs mit hohem Kontrast das Farbdesign "editorUnnecessaryCode.border", um unn\xF6tigen Code zu unterstreichen statt ihn abzublenden.',"Rahmenfarbe des Ghost-Texts im Editor.","Vordergrundfarbe des Ghost-Texts im Editor.","Hintergrundfarbe des Ghost-Texts im Editor.","\xDCbersichtslinealmarkerfarbe f\xFCr das Hervorheben von Bereichen. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.","\xDCbersichtslineal-Markierungsfarbe f\xFCr Fehler.","\xDCbersichtslineal-Markierungsfarbe f\xFCr Warnungen.","\xDCbersichtslineal-Markierungsfarbe f\xFCr Informationen.","Vordergrundfarbe der Klammern (1). Erfordert die Aktivierung der Farbgebung des Klammerpaars.","Vordergrundfarbe der Klammern (2). Erfordert die Aktivierung der Farbgebung des Klammerpaars.","Vordergrundfarbe der Klammern (3). Erfordert die Aktivierung der Farbgebung des Klammerpaars.","Vordergrundfarbe der Klammern (4). Erfordert die Aktivierung der Farbgebung des Klammerpaars.","Vordergrundfarbe der Klammern (5). Erfordert die Aktivierung der Farbgebung des Klammerpaars.","Vordergrundfarbe der Klammern (6). Erfordert die Aktivierung der Farbgebung des Klammerpaars.","Vordergrundfarbe der unerwarteten Klammern.","Hintergrundfarbe der inaktiven Klammerpaar-Hilfslinien (1). Erfordert das Aktivieren von Klammerpaar-Hilfslinien.","Hintergrundfarbe der inaktiven Klammerpaar-Hilfslinien (2). Erfordert das Aktivieren von Klammerpaar-Hilfslinien.","Hintergrundfarbe der inaktiven Klammerpaar-Hilfslinien (3). Erfordert das Aktivieren von Klammerpaar-Hilfslinien.","Hintergrundfarbe der inaktiven Klammerpaar-Hilfslinien (4). Erfordert das Aktivieren von Klammerpaar-Hilfslinien.","Hintergrundfarbe der inaktiven Klammerpaar-Hilfslinien (5). Erfordert das Aktivieren von Klammerpaar-Hilfslinien.","Hintergrundfarbe der inaktiven Klammerpaar-Hilfslinien (6). Erfordert das Aktivieren von Klammerpaar-Hilfslinien.","Hintergrundfarbe der aktiven Klammerpaar-Hilfslinien (1). Erfordert das Aktivieren von Klammerpaar-Hilfslinien.","Hintergrundfarbe der aktiven Klammerpaar-Hilfslinien (2). Erfordert das Aktivieren von Klammerpaar-Hilfslinien.","Hintergrundfarbe der aktiven Klammerpaar-Hilfslinien (3). Erfordert das Aktivieren von Klammerpaar-Hilfslinien.","Hintergrundfarbe der aktiven Klammerpaar-Hilfslinien (4). Erfordert das Aktivieren von Klammerpaar-Hilfslinien.","Hintergrundfarbe der aktiven Klammerpaar-Hilfslinien (5). Erfordert das Aktivieren von Klammerpaar-Hilfslinien.","Hintergrundfarbe der aktiven Klammerpaar-Hilfslinien (6). Erfordert das Aktivieren von Klammerpaar-Hilfslinien.","Rahmenfarbe, die zum Hervorheben von Unicode-Zeichen verwendet wird."],"vs/editor/common/editorContextKeys":["Gibt an, ob der Editor-Text den Fokus besitzt (Cursor blinkt).","Gibt an, ob der Editor oder ein Editor-Widget den Fokus besitzt (z.\xA0B. ob der Fokus sich im Suchwidget befindet).","Gibt an, ob ein Editor oder eine Rich-Text-Eingabe den Fokus besitzt (Cursor blinkt).","Gibt an, ob der Editor schreibgesch\xFCtzt ist.","Gibt an, ob der Kontext ein Diff-Editor ist.",'Gibt an, ob "editor.columnSelection" aktiviert ist.',"Gibt an, ob im Editor Text ausgew\xE4hlt ist.","Gibt an, ob der Editor \xFCber Mehrfachauswahl verf\xFCgt.","Gibt an, ob die TAB-TASTE den Fokus aus dem Editor verschiebt.","Gibt an, ob Hover im Editor sichtbar ist.","Gibt an, ob der Editor Bestandteil eines gr\xF6\xDFeren Editors ist (z.\xA0B. Notebooks).","Der Sprachbezeichner des Editors.","Gibt an, ob der Editor \xFCber einen Vervollst\xE4ndigungselementanbieter verf\xFCgt.","Gibt an, ob der Editor \xFCber einen Codeaktionsanbieter verf\xFCgt.","Gibt an, ob der Editor \xFCber einen CodeLens-Anbieter verf\xFCgt.","Gibt an, ob der Editor \xFCber einen Definitionsanbieter verf\xFCgt.","Gibt an, ob der Editor \xFCber einen Deklarationsanbieter verf\xFCgt.","Gibt an, ob der Editor \xFCber einen Implementierungsanbieter verf\xFCgt.","Gibt an, ob der Editor \xFCber einen Typdefinitionsanbieter verf\xFCgt.","Gibt an, ob der Editor \xFCber einen Hoveranbieter verf\xFCgt.","Gibt an, ob der Editor \xFCber einen Dokumenthervorhebungsanbieter verf\xFCgt.","Gibt an, ob der Editor \xFCber einen Dokumentsymbolanbieter verf\xFCgt.","Gibt an, ob der Editor \xFCber einen Verweisanbieter verf\xFCgt.","Gibt an, ob der Editor \xFCber einen Umbenennungsanbieter verf\xFCgt.","Gibt an, ob der Editor \xFCber einen Signaturhilfeanbieter verf\xFCgt.","Gibt an, ob der Editor \xFCber einen Inlinehinweisanbieter verf\xFCgt.","Gibt an, ob der Editor \xFCber einen Dokumentformatierungsanbieter verf\xFCgt.","Gibt an, ob der Editor \xFCber einen Anbieter f\xFCr Dokumentauswahlformatierung verf\xFCgt.","Gibt an, ob der Editor \xFCber mehrere Dokumentformatierungsanbieter verf\xFCgt.","Gibt an, ob der Editor \xFCber mehrere Anbieter f\xFCr Dokumentauswahlformatierung verf\xFCgt."],"vs/editor/common/languages/modesRegistry":["Nur-Text"],"vs/editor/common/model/editStack":["Eingabe"],"vs/editor/common/standaloneStrings":["Keine Auswahl","Zeile {0}, Spalte {1} ({2} ausgew\xE4hlt)","Zeile {0}, Spalte {1}","{0} Auswahlen ({1} Zeichen ausgew\xE4hlt)","{0} Auswahlen",'Die Einstellung "accessibilitySupport" wird jetzt in "on" ge\xE4ndert.',"Die Dokumentationsseite zur Barrierefreiheit des Editors wird ge\xF6ffnet."," in einem schreibgesch\xFCtzten Bereich eines Diff-Editors."," in einem Bereich eines Diff-Editors."," in einem schreibgesch\xFCtzten Code-Editor"," in einem Code-Editor","Dr\xFCcken Sie BEFEHLSTASTE + E, um den Editor f\xFCr eine optimierte Verwendung mit Sprachausgabe zu konfigurieren.","Dr\xFCcken Sie STRG + E, um den Editor f\xFCr eine optimierte Verwendung mit Sprachausgabe zu konfigurieren.","Der Editor ist auf eine optimale Verwendung mit Sprachausgabe konfiguriert.","Der Editor ist so konfiguriert, dass er nie auf die Verwendung mit Sprachausgabe hin optimiert wird. Dies ist zu diesem Zeitpunkt nicht der Fall.","Durch Dr\xFCcken der TAB-TASTE im aktuellen Editor wird der Fokus in das n\xE4chste Element verschoben, das den Fokus erhalten kann. Schalten Sie dieses Verhalten um, indem Sie {0} dr\xFCcken.","Durch Dr\xFCcken der TAB-TASTE im aktuellen Editor wird der Fokus in das n\xE4chste Element verschoben, das den Fokus erhalten kann. Der {0}-Befehl kann zurzeit nicht durch eine Tastenzuordnung ausgel\xF6st werden.","Durch Dr\xFCcken der TAB-TASTE im aktuellen Editor wird das Tabstoppzeichen eingef\xFCgt. Schalten Sie dieses Verhalten um, indem Sie {0} dr\xFCcken.","Durch Dr\xFCcken der TAB-TASTE im aktuellen Editor wird das Tabstoppzeichen eingef\xFCgt. Der {0}-Befehl kann zurzeit nicht durch eine Tastenzuordnung ausgel\xF6st werden.","Dr\xFCcken Sie BEFEHLSTASTE + H, um ein Browserfenster mit weiteren Informationen zur Barrierefreiheit des Editors zu \xF6ffnen.","Dr\xFCcken Sie STRG + H, um ein Browserfenster mit weiteren Informationen zur Barrierefreiheit des Editors zu \xF6ffnen.","Sie k\xF6nnen diese QuickInfo schlie\xDFen und durch Dr\xFCcken von ESC oder UMSCHALT+ESC zum Editor zur\xFCckkehren.","Hilfe zur Barrierefreiheit anzeigen","Entwickler: Token \xFCberpr\xFCfen","Gehe zu Zeile/Spalte...","Alle Anbieter f\xFCr den Schnellzugriff anzeigen","Befehlspalette","Befehle anzeigen und ausf\xFChren","Gehe zu Symbol...","Gehe zu Symbol nach Kategorie...","Editor-Inhalt","Dr\xFCcken Sie ALT + F1, um die Barrierefreiheitsoptionen aufzurufen.","Zu Design mit hohem Kontrast umschalten","{0} Bearbeitungen in {1} Dateien durchgef\xFChrt"],"vs/editor/contrib/anchorSelect/browser/anchorSelect":["Auswahlanker",'Anker festgelegt bei "{0}:{1}"',"Auswahlanker festlegen","Zu Auswahlanker wechseln","Auswahl von Anker zu Cursor","Auswahlanker abbrechen"],"vs/editor/contrib/bracketMatching/browser/bracketMatching":["\xDCbersichtslineal-Markierungsfarbe f\xFCr zusammengeh\xF6rige Klammern.","Gehe zu Klammer","Ausw\xE4hlen bis Klammer","Gehe zu &&Klammer"],"vs/editor/contrib/caretOperations/browser/caretOperations":["Ausgew\xE4hlten Text nach links verschieben","Ausgew\xE4hlten Text nach rechts verschieben"],"vs/editor/contrib/caretOperations/browser/transpose":["Buchstaben austauschen"],"vs/editor/contrib/clipboard/browser/clipboard":["&&Ausschneiden","Ausschneiden","Ausschneiden","Ausschneiden","&&Kopieren","Kopieren","Kopieren","Kopieren","Kopieren als","Kopieren als","&&Einf\xFCgen","Einf\xFCgen","Einf\xFCgen","Einf\xFCgen","Mit Syntaxhervorhebung kopieren"],"vs/editor/contrib/codeAction/browser/codeActionCommands":["Art der auszuf\xFChrenden Codeaktion","Legt fest, wann die zur\xFCckgegebenen Aktionen angewendet werden","Die erste zur\xFCckgegebene Codeaktion immer anwenden","Die erste zur\xFCckgegebene Codeaktion anwenden, wenn nur eine vorhanden ist","Zur\xFCckgegebene Codeaktionen nicht anwenden","Legt fest, ob nur bevorzugte Codeaktionen zur\xFCckgegeben werden sollen","Beim Anwenden der Code-Aktion ist ein unbekannter Fehler aufgetreten","Schnelle Problembehebung ...","Keine Codeaktionen verf\xFCgbar",'Keine bevorzugten Codeaktionen f\xFCr "{0}" verf\xFCgbar','Keine Codeaktionen f\xFCr "{0}" verf\xFCgbar',"Keine bevorzugten Codeaktionen verf\xFCgbar","Keine Codeaktionen verf\xFCgbar","Refactoring durchf\xFChren...",'Keine bevorzugten Refactorings f\xFCr "{0}" verf\xFCgbar','Keine Refactorings f\xFCr "{0}" verf\xFCgbar',"Keine bevorzugten Refactorings verf\xFCgbar","Keine Refactorings verf\xFCgbar","Quellaktion...",'Keine bevorzugten Quellaktionen f\xFCr "{0}" verf\xFCgbar','Keine Quellaktionen f\xFCr "{0}" verf\xFCgbar',"Keine bevorzugten Quellaktionen verf\xFCgbar","Keine Quellaktionen verf\xFCgbar","Importe organisieren","Keine Aktion zum Organisieren von Importen verf\xFCgbar","Alle korrigieren",'Aktion "Alle korrigieren" nicht verf\xFCgbar',"Automatisch korrigieren...","Keine automatischen Korrekturen verf\xFCgbar"],"vs/editor/contrib/codeAction/browser/lightBulbWidget":["Zeigt Codeaktionen an. Bevorzugte Schnellkorrektur verf\xFCgbar ({0})","Codeaktionen anzeigen ({0})","Codeaktionen anzeigen"],"vs/editor/contrib/codelens/browser/codelensController":["CodeLens-Befehle f\xFCr aktuelle Zeile anzeigen"],"vs/editor/contrib/colorPicker/browser/colorPickerWidget":["Zum Umschalten zwischen Farboptionen (rgb/hsl/hex) klicken"],"vs/editor/contrib/comment/browser/comment":["Zeilenkommentar umschalten","Zeilenkommen&&tar umschalten","Zeilenkommentar hinzuf\xFCgen","Zeilenkommentar entfernen","Blockkommentar umschalten","&&Blockkommentar umschalten"],"vs/editor/contrib/contextmenu/browser/contextmenu":["Editor-Kontextmen\xFC anzeigen"],"vs/editor/contrib/cursorUndo/browser/cursorUndo":["Mit Cursor r\xFCckg\xE4ngig machen","Wiederholen mit Cursor"],"vs/editor/contrib/editorState/browser/keybindingCancellation":['Gibt an, ob der Editor einen abbrechbaren Vorgang ausf\xFChrt, z.\xA0B. "Verweisvorschau".'],"vs/editor/contrib/find/browser/findController":["Suchen","&&Suchen",`\xDCberschreibt das Flag \u201EUse Regular Expression\u201C.\r
Das Flag wird f\xFCr die Zukunft nicht gespeichert.\r
0:\xA0Nichts unternehmen\r
1:\xA0TRUE\r
2:\xA0FALSE`,`\xDCberschreibt das Flag \u201EMatch Whole Word\u201C.\r
Das Flag wird f\xFCr die Zukunft nicht gespeichert.\r
0:\xA0Nichts unternehmen\r
1:\xA0TRUE\r
2:\xA0FALSE`,`\xDCberschreibt das Flag \u201EMath Case\u201C.\r
Das Flag wird f\xFCr die Zukunft nicht gespeichert.\r
0:\xA0Nichts unternehmen\r
1:\xA0TRUE\r
2:\xA0FALSE`,`\xDCberschreibt das Flag \u201EPreserve Case\u201C.\r
Das Flag wird f\xFCr die Zukunft nicht gespeichert.\r
0:\xA0Nichts unternehmen\r
1:\xA0TRUE\r
2:\xA0FALSE`,"Mit Argumenten suchen","Mit Auswahl suchen","Weitersuchen","Vorheriges Element suchen","N\xE4chste Auswahl suchen","Vorherige Auswahl suchen","Ersetzen","&&Ersetzen"],"vs/editor/contrib/find/browser/findWidget":['Symbol f\xFCr "In Auswahl suchen" im Editor-Such-Widget.',"Symbol f\xFCr die Anzeige, dass das Editor-Such-Widget zugeklappt wurde.","Symbol f\xFCr die Anzeige, dass das Editor-Such-Widget aufgeklappt wurde.",'Symbol f\xFCr "Ersetzen" im Editor-Such-Widget.','Symbol f\xFCr "Alle ersetzen" im Editor-Such-Widget.','Symbol f\xFCr "Vorheriges Element suchen" im Editor-Such-Widget.','Symbol f\xFCr "N\xE4chstes Element suchen" im Editor-Such-Widget.',"Suchen","Suchen","Vorherige \xDCbereinstimmung","N\xE4chste \xDCbereinstimmung","In Auswahl suchen","Schlie\xDFen","Ersetzen","Ersetzen","Ersetzen","Alle ersetzen","Ersetzen umschalten","Nur die ersten {0} Ergebnisse wurden hervorgehoben, aber alle Suchoperationen werden auf dem gesamten Text durchgef\xFChrt.","{0} von {1}","Keine Ergebnisse","{0} gefunden",'{0} f\xFCr "{1}" gefunden','{0} f\xFCr "{1}" gefunden, bei {2}','{0} f\xFCr "{1}" gefunden','STRG+EINGABE f\xFCgt jetzt einen Zeilenumbruch ein, statt alles zu ersetzen. Sie k\xF6nnen die Tastenzuordnung f\xFCr "editor.action.replaceAll" \xE4ndern, um dieses Verhalten au\xDFer Kraft zu setzen.'],"vs/editor/contrib/folding/browser/folding":["Die Anzahl der faltbaren Regionen ist auf maximal {0} beschr\xE4nkt. Erh\xF6hen Sie die Konfigurationsoption [\u201CMaximale faltbare Regionen\u201C](command:workbench.action.openSettings?[\u201Ceditor.foldingMaximumRegions\u201C]) um weitere zu erm\xF6glichen.","Auffalten","Faltung rekursiv aufheben","Falten","Einklappung umschalten","Rekursiv falten","Alle Blockkommentare falten","Alle Regionen falten","Alle Regionen auffalten","Alle Regionen mit Ausnahme der ausgew\xE4hlten zuklappen","Alle Regionen mit Ausnahme der ausgew\xE4hlten auffalten","Alle falten","Alle auffalten","Zur \xFCbergeordneten Reduzierung wechseln","Zum vorherigen Faltbereich wechseln","Zum n\xE4chsten Faltbereich wechseln","Faltebene {0}","Hintergrundfarbe hinter gefalteten Bereichen. Die Farbe darf nicht deckend sein, sodass zugrunde liegende Dekorationen nicht ausgeblendet werden.","Farbe des Faltsteuerelements im Editor-Bundsteg."],"vs/editor/contrib/folding/browser/foldingDecorations":["Symbol f\xFCr aufgeklappte Bereiche im Editor-Glyphenrand.","Symbol f\xFCr zugeklappte Bereiche im Editor-Glyphenrand."],"vs/editor/contrib/fontZoom/browser/fontZoom":["Editorschriftart vergr\xF6\xDFern","Editorschriftart verkleinern","Editor Schriftart Vergr\xF6\xDFerung zur\xFCcksetzen"],"vs/editor/contrib/format/browser/format":["1 Formatierung in Zeile {0} vorgenommen","{0} Formatierungen in Zeile {1} vorgenommen","1 Formatierung zwischen Zeilen {0} und {1} vorgenommen","{0} Formatierungen zwischen Zeilen {1} und {2} vorgenommen"],"vs/editor/contrib/format/browser/formatActions":["Dokument formatieren","Auswahl formatieren"],"vs/editor/contrib/gotoError/browser/gotoError":["Gehe zu n\xE4chstem Problem (Fehler, Warnung, Information)","Symbol f\xFCr den Marker zum Wechseln zum n\xE4chsten Element.","Gehe zu vorigem Problem (Fehler, Warnung, Information)","Symbol f\xFCr den Marker zum Wechseln zum vorherigen Element.","Gehe zu dem n\xE4chsten Problem in den Dateien (Fehler, Warnung, Info)","N\xE4chstes &&Problem","Gehe zu dem vorherigen Problem in den Dateien (Fehler, Warnung, Info)","Vorheriges &&Problem"],"vs/editor/contrib/gotoError/browser/gotoErrorWidget":["Fehler","Warnung","Info","Hinweis","{0} bei {1}. ","{0} von {1} Problemen","{0} von {1} Problemen","Editormarkierung: Farbe bei Fehler des Navigationswidgets.","Hintergrund der Fehler\xFCberschrift des Markernavigationswidgets im Editor.","Editormarkierung: Farbe bei Warnung des Navigationswidgets.","Hintergrund der Warnungs\xFCberschrift des Markernavigationswidgets im Editor.","Editormarkierung: Farbe bei Information des Navigationswidgets.","Hintergrund der Informations\xFCberschrift des Markernavigationswidgets im Editor.","Editormarkierung: Hintergrund des Navigationswidgets."],"vs/editor/contrib/gotoSymbol/browser/goToCommands":["Vorschau","Definitionen",'Keine Definition gefunden f\xFCr "{0}".',"Keine Definition gefunden","Gehe zu Definition","Definition an der Seite \xF6ffnen","Definition einsehen","Deklarationen",'Keine Deklaration f\xFCr "{0}" gefunden.',"Keine Deklaration gefunden.","Zur Deklaration wechseln",'Keine Deklaration f\xFCr "{0}" gefunden.',"Keine Deklaration gefunden.","Vorschau f\xFCr Deklaration anzeigen","Typdefinitionen",'Keine Typendefinition gefunden f\xFCr "{0}"',"Keine Typendefinition gefunden","Zur Typdefinition wechseln","Vorschau der Typdefinition anzeigen","Implementierungen",'Keine Implementierung gefunden f\xFCr "{0}"',"Keine Implementierung gefunden","Gehe zu Implementierungen","Vorschau f\xFCr Implementierungen anzeigen",'F\xFCr "{0}" wurden keine Verweise gefunden.',"Keine Referenzen gefunden","Gehe zu Verweisen","Verweise","Vorschau f\xFCr Verweise anzeigen","Verweise","Zum beliebigem Symbol wechseln","Speicherorte",'Keine Ergebnisse f\xFCr "{0}"',"Verweise","Gehe &&zu Definition","Gehe zu &&Deklaration","Zur &&Typdefinition wechseln","Gehe zu &&Implementierungen","Gehe zu &&Verweisen"],"vs/editor/contrib/gotoSymbol/browser/link/goToDefinitionAtPosition":["Klicken Sie, um {0} Definitionen anzuzeigen."],"vs/editor/contrib/gotoSymbol/browser/peek/referencesController":['Gibt an, ob die Verweisvorschau sichtbar ist, z.\xA0B. "Verweisvorschau" oder "Definition einsehen".',"Wird geladen...","{0} ({1})"],"vs/editor/contrib/gotoSymbol/browser/peek/referencesTree":["{0} Verweise","{0} Verweis","Verweise"],"vs/editor/contrib/gotoSymbol/browser/peek/referencesWidget":["Keine Vorschau verf\xFCgbar.","Keine Ergebnisse","Verweise"],"vs/editor/contrib/gotoSymbol/browser/referencesModel":["Symbol in {0} in Zeile {1}, Spalte {2}",'Symbol in "{0}" in Zeile {1}, Spalte {2}, {3}',"1 Symbol in {0}, vollst\xE4ndiger Pfad {1}","{0} Symbole in {1}, vollst\xE4ndiger Pfad {2}","Es wurden keine Ergebnisse gefunden.","1 Symbol in {0} gefunden","{0} Symbole in {1} gefunden","{0} Symbole in {1} Dateien gefunden"],"vs/editor/contrib/gotoSymbol/browser/symbolNavigation":["Gibt an, ob Symbolpositionen vorliegen, bei denen die Navigation nur \xFCber die Tastatur m\xF6glich ist.","Symbol {0} von {1}, {2} f\xFCr n\xE4chstes","Symbol {0} von {1}"],"vs/editor/contrib/hover/browser/hover":["Hovern anzeigen","Definitionsvorschauhover anzeigen"],"vs/editor/contrib/hover/browser/markdownHoverParticipant":["Wird geladen...","Die Tokenisierung wird bei langen Zeilen aus Leistungsgr\xFCnden \xFCbersprungen. Dies kann \xFCber \u201Eeditor.maxTokenizationLineLength\u201C konfiguriert werden."],"vs/editor/contrib/hover/browser/markerHoverParticipant":["Problem anzeigen","Keine Schnellkorrekturen verf\xFCgbar","Es wird nach Schnellkorrekturen gesucht...","Keine Schnellkorrekturen verf\xFCgbar","Schnelle Problembehebung ..."],"vs/editor/contrib/inPlaceReplace/browser/inPlaceReplace":["Durch vorherigen Wert ersetzen","Durch n\xE4chsten Wert ersetzen"],"vs/editor/contrib/indentation/browser/indentation":["Einzug in Leerzeichen konvertieren","Einzug in Tabstopps konvertieren","Konfigurierte Tabulatorgr\xF6\xDFe","Tabulatorgr\xF6\xDFe f\xFCr aktuelle Datei ausw\xE4hlen","Einzug mithilfe von Tabstopps","Einzug mithilfe von Leerzeichen","Einzug aus Inhalt erkennen","Neuen Einzug f\xFCr Zeilen festlegen","Gew\xE4hlte Zeilen zur\xFCckziehen"],"vs/editor/contrib/inlayHints/browser/inlayHintsHover":["BEFEHL + Klicken","STRG + Klicken","OPTION + Klicken","ALT + Klicken","Wechseln Sie zu Definition ({0}), klicken Sie mit der rechten Maustaste, um weitere Informationen zu finden.","Gehe zu Definition ({0})","Befehl ausf\xFChren"],"vs/editor/contrib/inlineCompletions/browser/ghostTextController":["Gibt an, ob ein Inline-Vorschlag sichtbar ist.","Gibt an, ob der Inline-Vorschlag mit Leerzeichen beginnt.","Ob der Inline-Vorschlag mit Leerzeichen beginnt, das kleiner ist als das, was durch die Tabulatortaste eingef\xFCgt werden w\xFCrde","N\xE4chsten Inline-Vorschlag anzeigen","Vorherigen Inline-Vorschlag anzeigen","Inline-Vorschlag ausl\xF6sen"],"vs/editor/contrib/inlineCompletions/browser/inlineCompletionsHoverParticipant":["Weiter","Zur\xFCck","Annehmen","Vorschlag:"],"vs/editor/contrib/lineSelection/browser/lineSelection":["Zeilenauswahl erweitern"],"vs/editor/contrib/linesOperations/browser/linesOperations":["Zeile nach oben kopieren","Zeile nach oben &&kopieren","Zeile nach unten kopieren","Zeile nach unten ko&&pieren","Auswahl duplizieren","&&Auswahl duplizieren","Zeile nach oben verschieben","Zeile nach oben &&verschieben","Zeile nach unten verschieben","Zeile nach &&unten verschieben","Zeilen aufsteigend sortieren","Zeilen absteigend sortieren","Doppelte Zeilen l\xF6schen","Nachgestelltes Leerzeichen k\xFCrzen","Zeile l\xF6schen","Zeileneinzug","Zeile ausr\xFCcken","Zeile oben einf\xFCgen","Zeile unten einf\xFCgen","Alle \xFCbrigen l\xF6schen","Alle rechts l\xF6schen","Zeilen verkn\xFCpfen","Zeichen um den Cursor herum transponieren","In Gro\xDFbuchstaben umwandeln","In Kleinbuchstaben umwandeln","In gro\xDFe Anfangsbuchstaben umwandeln","In Snake Case umwandeln"],"vs/editor/contrib/linkedEditing/browser/linkedEditing":["Verkn\xFCpfte Bearbeitung starten","Hintergrundfarbe, wenn der Editor automatisch nach Typ umbenennt."],"vs/editor/contrib/links/browser/links":["Fehler beim \xD6ffnen dieses Links, weil er nicht wohlgeformt ist: {0}","Fehler beim \xD6ffnen dieses Links, weil das Ziel fehlt.","Befehl ausf\xFChren","Link folgen","BEFEHL + Klicken","STRG + Klicken","OPTION + Klicken","alt + klicken",'F\xFChren Sie den Befehl "{0}" aus.',"Link \xF6ffnen"],"vs/editor/contrib/message/browser/messageController":["Gibt an, ob der Editor zurzeit eine Inlinenachricht anzeigt.","Ein Bearbeiten ist im schreibgesch\xFCtzten Editor nicht m\xF6glich"],"vs/editor/contrib/multicursor/browser/multicursor":["Hinzugef\xFCgter Cursor: {0}","Hinzugef\xFCgte Cursor: {0}","Cursor oberhalb hinzuf\xFCgen","Cursor oberh&&alb hinzuf\xFCgen","Cursor unterhalb hinzuf\xFCgen","Cursor unterhal&&b hinzuf\xFCgen","Cursor an Zeilenenden hinzuf\xFCgen","C&&ursor an Zeilenenden hinzuf\xFCgen","Cursor am Ende hinzuf\xFCgen","Cursor am Anfang hinzuf\xFCgen","Auswahl zur n\xE4chsten \xDCbereinstimmungssuche hinzuf\xFCgen","&&N\xE4chstes Vorkommen hinzuf\xFCgen","Letzte Auswahl zu vorheriger \xDCbereinstimmungssuche hinzuf\xFCgen","Vo&&rheriges Vorkommen hinzuf\xFCgen","Letzte Auswahl in n\xE4chste \xDCbereinstimmungssuche verschieben","Letzte Auswahl in vorherige \xDCbereinstimmungssuche verschieben","Alle Vorkommen ausw\xE4hlen und \xDCbereinstimmung suchen","Alle V&&orkommen ausw\xE4hlen","Alle Vorkommen \xE4ndern"],"vs/editor/contrib/parameterHints/browser/parameterHints":["Parameterhinweise ausl\xF6sen"],"vs/editor/contrib/parameterHints/browser/parameterHintsWidget":["Symbol f\xFCr die Anzeige des n\xE4chsten Parameterhinweises.","Symbol f\xFCr die Anzeige des vorherigen Parameterhinweises.","{0}, Hinweis","Vordergrundfarbe des aktiven Elements im Parameterhinweis."],"vs/editor/contrib/peekView/browser/peekView":["Gibt an, ob der aktuelle Code-Editor in der Vorschau eingebettet ist.","Schlie\xDFen","Hintergrundfarbe des Titelbereichs der Peek-Ansicht.","Farbe des Titels in der Peek-Ansicht.","Farbe der Titelinformationen in der Peek-Ansicht.","Farbe der Peek-Ansichtsr\xE4nder und des Pfeils.","Hintergrundfarbe der Ergebnisliste in der Peek-Ansicht.","Vordergrundfarbe f\xFCr Zeilenknoten in der Ergebnisliste der Peek-Ansicht.","Vordergrundfarbe f\xFCr Dateiknoten in der Ergebnisliste der Peek-Ansicht.","Hintergrundfarbe des ausgew\xE4hlten Eintrags in der Ergebnisliste der Peek-Ansicht.","Vordergrundfarbe des ausgew\xE4hlten Eintrags in der Ergebnisliste der Peek-Ansicht.","Hintergrundfarbe des Peek-Editors.","Hintergrundfarbe der Leiste im Peek-Editor.","Farbe f\xFCr \xDCbereinstimmungsmarkierungen in der Ergebnisliste der Peek-Ansicht.","Farbe f\xFCr \xDCbereinstimmungsmarkierungen im Peek-Editor.","Rahmen f\xFCr \xDCbereinstimmungsmarkierungen im Peek-Editor."],"vs/editor/contrib/quickAccess/browser/gotoLineQuickAccess":["\xD6ffnen Sie zuerst einen Text-Editor, um zu einer Zeile zu wechseln.","Wechseln Sie zu Zeile {0} und Zeichen {1}.","Zu Zeile {0} wechseln.","Aktuelle Zeile: {0}, Zeichen: {1}. Geben Sie eine Zeilennummer zwischen 1 und {2} ein, zu der Sie navigieren m\xF6chten.","Aktuelle Zeile: {0}, Zeichen: {1}. Geben Sie eine Zeilennummer ein, zu der Sie navigieren m\xF6chten."],"vs/editor/contrib/quickAccess/browser/gotoSymbolQuickAccess":["\xD6ffnen Sie zun\xE4chst einen Text-Editor mit Symbolinformationen, um zu einem Symbol zu navigieren.","Der aktive Text-Editor stellt keine Symbolinformationen bereit.","Keine \xFCbereinstimmenden Editorsymbole.","Keine Editorsymbole.","An der Seite \xF6ffnen","Unten \xF6ffnen","Symbole ({0})","Eigenschaften ({0})","Methoden ({0})","Funktionen ({0})","Konstruktoren ({0})","Variablen ({0})","Klassen ({0})","Strukturen ({0})","Ereignisse ({0})","Operatoren ({0})","Schnittstellen ({0})","Namespaces ({0})","Pakete ({0})","Typparameter ({0})","Module ({0})","Eigenschaften ({0})","Enumerationen ({0})","Enumerationsmember ({0})","Zeichenfolgen ({0})","Dateien ({0})","Arrays ({0})","Zahlen ({0})","Boolesche Werte ({0})","Objekte ({0})","Schl\xFCssel ({0})","Felder ({0})","Konstanten ({0})"],"vs/editor/contrib/rename/browser/rename":["Kein Ergebnis.","Ein unbekannter Fehler ist beim Aufl\xF6sen der Umbenennung eines Ortes aufgetreten.",'"{0}" wird umbenannt.',"{0} wird umbenannt.",'"{0}" erfolgreich in "{1}" umbenannt. Zusammenfassung: {2}',"Die rename-Funktion konnte die \xC4nderungen nicht anwenden.","Die rename-Funktion konnte die \xC4nderungen nicht berechnen.","Symbol umbenennen","M\xF6glichkeit aktivieren/deaktivieren, \xC4nderungen vor dem Umbenennen als Vorschau anzeigen zu lassen"],"vs/editor/contrib/rename/browser/renameInputField":["Gibt an, ob das Widget zum Umbenennen der Eingabe sichtbar ist.","Benennen Sie die Eingabe um. Geben Sie einen neuen Namen ein, und dr\xFCcken Sie die EINGABETASTE, um den Commit auszuf\xFChren.","{0} zur Umbenennung, {1} zur Vorschau"],"vs/editor/contrib/smartSelect/browser/smartSelect":["Auswahl aufklappen","Auswahl &&erweitern","Markierung verkleinern","Au&&swahl verkleinern"],"vs/editor/contrib/snippet/browser/snippetController2":["Gibt an, ob der Editor sich zurzeit im Schnipselmodus befindet.","Gibt an, ob ein n\xE4chster Tabstopp im Schnipselmodus vorhanden ist.","Gibt an, ob ein vorheriger Tabstopp im Schnipselmodus vorhanden ist."],"vs/editor/contrib/snippet/browser/snippetVariables":["Sonntag","Montag","Dienstag","Mittwoch","Donnerstag","Freitag","Samstag","So","Mo","Di","Mi","Do","Fr","Sa","Januar","Februar","M\xE4rz","April","Mai","Juni","Juli","August","September","Oktober","November","Dezember","Jan","Feb","M\xE4r","Apr","Mai","Jun","Jul","Aug","Sep","Okt","Nov","Dez"],"vs/editor/contrib/suggest/browser/suggest":["Gibt an, ob Vorschlagsdetails sichtbar sind.","Gibt an, ob mehrere Vorschl\xE4ge zur Auswahl stehen.","Gibt an, ob das Einf\xFCgen des aktuellen Vorschlags zu einer \xC4nderung f\xFChrt oder ob bereits alles eingegeben wurde.","Gibt an, ob Vorschl\xE4ge durch Dr\xFCcken der EINGABETASTE eingef\xFCgt werden.","Gibt an, ob der aktuelle Vorschlag Verhalten zum Einf\xFCgen und Ersetzen aufweist.","Gibt an, ob Einf\xFCgen oder Ersetzen als Standardverhalten verwendet wird.","Gibt an, ob der aktuelle Vorschlag die Aufl\xF6sung weiterer Details unterst\xFCtzt."],"vs/editor/contrib/suggest/browser/suggestController":['Das Akzeptieren von "{0}" ergab {1} zus\xE4tzliche Bearbeitungen.',"Vorschlag ausl\xF6sen","Einf\xFCgen","Einf\xFCgen","Ersetzen","Ersetzen","Einf\xFCgen","weniger anzeigen","mehr anzeigen","Gr\xF6\xDFe des Vorschlagswidgets zur\xFCcksetzen"],"vs/editor/contrib/suggest/browser/suggestWidget":["Hintergrundfarbe des Vorschlagswidgets.","Rahmenfarbe des Vorschlagswidgets.","Vordergrundfarbe des Vorschlagswidgets.","Die Vordergrundfarbe des ausgew\xE4hlten Eintrags im Vorschlagswidget.","Die Vordergrundfarbe des Symbols des ausgew\xE4hlten Eintrags im Vorschlagswidget.","Hintergrundfarbe des ausgew\xE4hlten Eintrags im Vorschlagswidget.","Farbe der Trefferhervorhebung im Vorschlagswidget.","Die Farbe des Treffers wird im Vorschlagswidget hervorgehoben, wenn ein Element fokussiert wird.","Vordergrundfarbe des Status des Vorschlagswidgets.","Wird geladen...","Keine Vorschl\xE4ge.","Vorschlagen","{0}{1}, {2}","{0}{1}","{0}, {1}","{0}, Dokumente: {1}"],"vs/editor/contrib/suggest/browser/suggestWidgetDetails":["Schlie\xDFen","Wird geladen..."],"vs/editor/contrib/suggest/browser/suggestWidgetRenderer":["Symbol f\xFCr weitere Informationen im Vorschlags-Widget.","Weitere Informationen"],"vs/editor/contrib/suggest/browser/suggestWidgetStatus":["{0} ({1})"],"vs/editor/contrib/symbolIcons/browser/symbolIcons":["Die Vordergrundfarbe f\xFCr Arraysymbole. Diese Symbole werden in den Widgets f\xFCr Gliederung, Breadcrumbs und Vorschl\xE4ge angezeigt.","Die Vordergrundfarbe f\xFCr boolesche Symbole. Diese Symbole werden in den Widgets f\xFCr Gliederung, Breadcrumbs und Vorschl\xE4ge angezeigt.","Die Vordergrundfarbe f\xFCr Klassensymbole. Diese Symbole werden in den Widgets f\xFCr Gliederung, Breadcrumbs und Vorschl\xE4ge angezeigt.","Die Vordergrundfarbe f\xFCr Farbsymbole. Diese Symbole werden in den Widgets f\xFCr Gliederung, Breadcrumbs und Vorschl\xE4ge angezeigt.","Die Vordergrundfarbe f\xFCr konstante Symbole. Diese Symbole werden in den Widgets f\xFCr Gliederung, Breadcrumbs und Vorschl\xE4ge angezeigt.","Die Vordergrundfarbe f\xFCr Konstruktorsymbole. Diese Symbole werden in den Widgets f\xFCr Gliederung, Breadcrumbs und Vorschl\xE4ge angezeigt.","Die Vordergrundfarbe f\xFCr Enumeratorsymbole. Diese Symbole werden in den Widgets f\xFCr Gliederung, Breadcrumbs und Vorschl\xE4ge angezeigt.","Die Vordergrundfarbe f\xFCr Enumeratormembersymbole. Diese Symbole werden in den Widgets f\xFCr Gliederung, Breadcrumbs und Vorschl\xE4ge angezeigt.","Die Vordergrundfarbe f\xFCr Ereignissymbole. Diese Symbole werden in den Widgets f\xFCr Gliederung, Breadcrumbs und Vorschl\xE4ge angezeigt.","Die Vordergrundfarbe f\xFCr Feldsymbole. Diese Symbole werden in den Widgets f\xFCr Gliederung, Breadcrumbs und Vorschl\xE4ge angezeigt.","Die Vordergrundfarbe f\xFCr Dateisymbole. Diese Symbole werden in den Widgets f\xFCr Gliederung, Breadcrumbs und Vorschl\xE4ge angezeigt.","Die Vordergrundfarbe f\xFCr Ordnersymbole. Diese Symbole werden in den Widgets f\xFCr Gliederung, Breadcrumbs und Vorschl\xE4ge angezeigt.","Die Vordergrundfarbe f\xFCr Funktionssymbole. Diese Symbole werden in den Widgets f\xFCr Gliederung, Breadcrumbs und Vorschl\xE4ge angezeigt.","Die Vordergrundfarbe f\xFCr Schnittstellensymbole. Diese Symbole werden in den Widgets f\xFCr Gliederung, Breadcrumbs und Vorschl\xE4ge angezeigt.","Die Vordergrundfarbe f\xFCr Schl\xFCsselsymbole. Diese Symbole werden in den Widgets f\xFCr Gliederung, Breadcrumbs und Vorschl\xE4ge angezeigt.","Die Vordergrundfarbe f\xFCr Schl\xFCsselwortsymbole. Diese Symbole werden in den Widgets f\xFCr Gliederung, Breadcrumbs und Vorschl\xE4ge angezeigt.","Die Vordergrundfarbe f\xFCr Methodensymbole. Diese Symbole werden in den Widgets f\xFCr Gliederung, Breadcrumbs und Vorschl\xE4ge angezeigt.","Die Vordergrundfarbe f\xFCr Modulsymbole. Diese Symbole werden in den Widgets f\xFCr Gliederung, Breadcrumbs und Vorschl\xE4ge angezeigt.","Die Vordergrundfarbe f\xFCr Namespacesymbole. Diese Symbole werden in den Widgets f\xFCr Gliederung, Breadcrumbs und Vorschl\xE4ge angezeigt.","Die Vordergrundfarbe f\xFCr NULL-Symbole. Diese Symbole werden in den Widgets f\xFCr Gliederung, Breadcrumbs und Vorschl\xE4ge angezeigt.","Die Vordergrundfarbe f\xFCr Zahlensymbole. Diese Symbole werden in den Widgets f\xFCr Gliederung, Breadcrumbs und Vorschl\xE4ge angezeigt.","Die Vordergrundfarbe f\xFCr Objektsymbole. Diese Symbole werden in den Widgets f\xFCr Gliederung, Breadcrumbs und Vorschl\xE4ge angezeigt.","Die Vordergrundfarbe f\xFCr Operatorsymbole. Diese Symbole werden in den Widgets f\xFCr Gliederung, Breadcrumbs und Vorschl\xE4ge angezeigt.","Die Vordergrundfarbe f\xFCr Paketsymbole. Diese Symbole werden in den Widgets f\xFCr Gliederung, Breadcrumbs und Vorschl\xE4ge angezeigt.","Die Vordergrundfarbe f\xFCr Eigenschaftensymbole. Diese Symbole werden in den Widgets f\xFCr Gliederung, Breadcrumbs und Vorschl\xE4ge angezeigt.","Die Vordergrundfarbe f\xFCr Referenzsymbole. Diese Symbole werden in den Widgets f\xFCr Gliederung, Breadcrumbs und Vorschl\xE4ge angezeigt.","Die Vordergrundfarbe f\xFCr Codeschnipselsymbole. Diese Symbole werden in den Widgets f\xFCr Gliederung, Breadcrumbs und Vorschl\xE4ge angezeigt.","Die Vordergrundfarbe f\xFCr Zeichenfolgensymbole. Diese Symbole werden in den Widgets f\xFCr Gliederung, Breadcrumbs und Vorschl\xE4ge angezeigt.","Die Vordergrundfarbe f\xFCr Struktursymbole. Diese Symbole werden in den Widgets f\xFCr Gliederung, Breadcrumbs und Vorschl\xE4ge angezeigt.","Die Vordergrundfarbe f\xFCr Textsymbole. Diese Symbole werden in den Widgets f\xFCr Gliederung, Breadcrumbs und Vorschl\xE4ge angezeigt.","Die Vordergrundfarbe f\xFCr Typparametersymbole. Diese Symbole werden in den Widgets f\xFCr Gliederung, Breadcrumbs und Vorschl\xE4ge angezeigt.","Die Vordergrundfarbe f\xFCr Einheitensymbole. Diese Symbole werden in den Widgets f\xFCr Gliederung, Breadcrumbs und Vorschl\xE4ge angezeigt.","Die Vordergrundfarbe f\xFCr variable Symbole. Diese Symbole werden in den Widgets f\xFCr Gliederung, Breadcrumbs und Vorschl\xE4ge angezeigt."],"vs/editor/contrib/toggleTabFocusMode/browser/toggleTabFocusMode":["TAB-Umschalttaste verschiebt Fokus","Beim Dr\xFCcken auf Tab wird der Fokus jetzt auf das n\xE4chste fokussierbare Element verschoben","Beim Dr\xFCcken von Tab wird jetzt das Tabulator-Zeichen eingef\xFCgt"],"vs/editor/contrib/tokenization/browser/tokenization":["Entwickler: Force Retokenize"],"vs/editor/contrib/unicodeHighlighter/browser/unicodeHighlighter":["Symbol, das mit einer Warnmeldung im Erweiterungs-Editor angezeigt wird.","Dieses Dokument enth\xE4lt viele nicht einfache ASCII-Unicode-Zeichen.","Dieses Dokument enth\xE4lt viele mehrdeutige Unicode-Zeichen.","Dieses Dokument enth\xE4lt viele unsichtbare Unicode-Zeichen.","Das Zeichen {0} kann mit dem Zeichen {1} verwechselt werden, was im Quellcode h\xE4ufiger vorkommt.","Das Zeichen {0} ist nicht sichtbar.","Das Zeichen {0} ist kein einfaches ASCII-Zeichen.","Einstellungen anpassen","Hervorhebung in Kommentaren deaktivieren","Deaktivieren der Hervorhebung von Zeichen in Kommentaren","Hervorhebung in Zeichenfolgen deaktivieren","Deaktivieren der Hervorhebung von Zeichen in Zeichenfolgen","Mehrdeutige Hervorhebung deaktivieren","Deaktivieren der Hervorhebung von mehrdeutigen Zeichen","Unsichtbare Hervorhebung deaktivieren","Deaktivieren der Hervorhebung unsichtbarer Zeichen","Nicht-ASCII-Hervorhebung deaktivieren","Deaktivieren der Hervorhebung von nicht einfachen ASCII-Zeichen","Ausschlussoptionen anzeigen","{0} (unsichtbares Zeichen) von der Hervorhebung ausschlie\xDFen","{0} nicht hervorheben","Unicodezeichen zulassen, die in der Sprache \u201E{0}\u201C h\xE4ufiger vorkommen.","Konfigurieren der Optionen f\xFCr die Unicode-Hervorhebung"],"vs/editor/contrib/unusualLineTerminators/browser/unusualLineTerminators":["Ungew\xF6hnliche Zeilentrennzeichen","Ungew\xF6hnliche Zeilentrennzeichen erkannt",`Die Datei "{0}" enth\xE4lt mindestens ein ungew\xF6hnliches Zeilenabschlusszeichen, z. B. Zeilentrennzeichen (LS) oder Absatztrennzeichen (PS).\r
\r
Es wird empfohlen, sie aus der Datei zu entfernen. Dies kann \xFCber "editor.unusualLineTerminators" konfiguriert werden.`,"Entfernen ungew\xF6hnlicher Zeilenabschlusszeichen","Ignorieren"],"vs/editor/contrib/wordHighlighter/browser/wordHighlighter":["Hintergrundfarbe eines Symbols beim Lesezugriff, z.B. beim Lesen einer Variablen. Die Farbe darf nicht deckend sein, damit sie nicht die zugrunde liegenden Dekorationen verdeckt.","Hintergrundfarbe eines Symbols bei Schreibzugriff, z.B. beim Schreiben in eine Variable. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.","Randfarbe eines Symbols beim Lesezugriff, wie etwa beim Lesen einer Variablen.","Randfarbe eines Symbols beim Schreibzugriff, wie etwa beim Schreiben einer Variablen.","\xDCbersichtslinealmarkerfarbd f\xFCr das Hervorheben von Symbolen. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.","\xDCbersichtslinealmarkerfarbe f\xFCr Symbolhervorhebungen bei Schreibzugriff. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.","Gehe zur n\xE4chsten Symbolhervorhebungen","Gehe zur vorherigen Symbolhervorhebungen","Symbol-Hervorhebung ein-/ausschalten"],"vs/editor/contrib/wordOperations/browser/wordOperations":["Wort l\xF6schen"],"vs/platform/actions/browser/menuEntryActionViewItem":["{0} ({1})","{0} ({1})"],"vs/platform/configuration/common/configurationRegistry":["Au\xDFerkraftsetzungen f\xFCr die Standardsprachkonfiguration",'Hiermit wird die Au\xDFerkraftsetzung von Einstellungen f\xFCr die Sprache "{0}" konfiguriert.',"Zu \xFCberschreibende Editor-Einstellungen f\xFCr eine Sprache konfigurieren.","Diese Einstellung unterst\xFCtzt keine sprachspezifische Konfiguration.","Zu \xFCberschreibende Editor-Einstellungen f\xFCr eine Sprache konfigurieren.","Diese Einstellung unterst\xFCtzt keine sprachspezifische Konfiguration.","Eine leere Eigenschaft kann nicht registriert werden.",'"{0}" kann nicht registriert werden. Stimmt mit dem Eigenschaftsmuster "\\\\[.*\\\\]$" zum Beschreiben sprachspezifischer Editor-Einstellungen \xFCberein. Verwenden Sie den Beitrag "configurationDefaults".','{0}" kann nicht registriert werden. Diese Eigenschaft ist bereits registriert.'],"vs/platform/contextkey/browser/contextKeyService":["Ein Befehl, der Informationen zu Kontextschl\xFCsseln zur\xFCckgibt"],"vs/platform/contextkey/common/contextkeys":["Gibt an, ob macOS als Betriebssystem verwendet wird.","Gibt an, ob Linux als Betriebssystem verwendet wird.","Gibt an, ob Windows als Betriebssystem verwendet wird.","Gibt an, ob es sich bei der Plattform um einen Webbrowser handelt.","Gibt an, ob macOS auf einer Nicht-Browser-Plattform als Betriebssystem verwendet wird.","Gibt an, ob iOS als Betriebssystem verwendet wird.","Gibt an, ob sich der Tastaturfokus in einem Eingabefeld befindet."],"vs/platform/history/browser/contextScopedHistoryWidget":["Gibt an, ob Vorschl\xE4ge sichtbar sind."],"vs/platform/keybinding/common/abstractKeybindingService":["({0}) wurde gedr\xFCckt. Es wird auf die zweite Taste in der Kombination gewartet...","Die Tastenkombination ({0}, {1}) ist kein Befehl."],"vs/platform/list/browser/listService":["Workbench","Ist unter Windows und Linux der STRG-Taste und unter macOS der Befehlstaste zugeordnet.","Ist unter Windows und Linux der ALT-Taste und unter macOS der Wahltaste zugeordnet.",'Der Modifizierer zum Hinzuf\xFCgen eines Elements in B\xE4umen und Listen zu einer Mehrfachauswahl mit der Maus (zum Beispiel im Explorer, in ge\xF6ffneten Editoren und in der SCM-Ansicht). Die Mausbewegung "Seitlich \xF6ffnen" wird \u2013 sofern unterst\xFCtzt \u2013 so angepasst, dass kein Konflikt mit dem Modifizierer f\xFCr Mehrfachauswahl entsteht.',"Steuert, wie Elemente in Strukturen und Listen mithilfe der Maus ge\xF6ffnet werden (sofern unterst\xFCtzt). Bei \xFCbergeordneten Elementen, deren untergeordnete Elemente sich in Strukturen befinden, steuert diese Einstellung, ob ein Einfachklick oder ein Doppelklick das \xFCbergeordnete Elemente erweitert. Beachten Sie, dass einige Strukturen und Listen diese Einstellung ggf. ignorieren, wenn sie nicht zutrifft.","Steuert, ob Listen und Strukturen ein horizontales Scrollen in der Workbench unterst\xFCtzen. Warnung: Das Aktivieren dieser Einstellung kann sich auf die Leistung auswirken.","Steuert den Struktureinzug in Pixeln.","Steuert, ob die Struktur Einzugsf\xFChrungslinien rendern soll.","Steuert, ob Listen und Strukturen einen optimierten Bildlauf verwenden.",'Ein Multiplikator, der f\xFCr die Mausrad-Bildlaufereignisse "deltaX" und "deltaY" verwendet werden soll.',"Multiplikator f\xFCr Scrollgeschwindigkeit bei Dr\xFCcken von ALT.","Bei der einfachen Tastaturnavigation werden Elemente in den Fokus genommen, die mit der Tastatureingabe \xFCbereinstimmen. Die \xDCbereinstimmungen gelten nur f\xFCr Pr\xE4fixe.","Hervorheben von Tastaturnavigationshervorgebungselemente, die mit der Tastatureingabe \xFCbereinstimmen. Beim nach oben und nach unten Navigieren werden nur die hervorgehobenen Elemente durchlaufen.","Durch das Filtern der Tastaturnavigation werden alle Elemente herausgefiltert und ausgeblendet, die nicht mit der Tastatureingabe \xFCbereinstimmen.",'Steuert die Tastaturnavigation in Listen und Strukturen in der Workbench. Kann "simple" (einfach), "highlight" (hervorheben) und "filter" (filtern) sein.','Legt fest, ob die Tastaturnavigation in Listen und Strukturen automatisch durch Eingaben ausgel\xF6st wird. Wenn der Wert auf "false" festgelegt ist, wird die Tastaturnavigation nur ausgel\xF6st, wenn der Befehl "list.toggleKeyboardNavigation" ausgef\xFChrt wird. Diesem Befehl k\xF6nnen Sie eine Tastenkombination zuweisen.',"Steuert, wie Strukturordner beim Klicken auf die Ordnernamen erweitert werden. Beachten Sie, dass einige Strukturen und Listen diese Einstellung ggf. ignorieren, wenn sie nicht zutrifft."],"vs/platform/markers/common/markers":["Fehler","Warnung","Info"],"vs/platform/quickinput/browser/commandsQuickAccess":["{0}, {1}","zuletzt verwendet","andere Befehle","Der Befehl {0} hat einen Fehler ausgel\xF6st ({1})."],"vs/platform/quickinput/browser/helpQuickAccess":["Globale Befehle","Editor-Befehle","{0}, {1}"],"vs/platform/theme/common/colorRegistry":["Allgemeine Vordergrundfarbe. Diese Farbe wird nur verwendet, wenn sie nicht durch eine Komponente \xFCberschrieben wird.","Allgemeine Vordergrundfarbe f\xFCr Fehlermeldungen. Diese Farbe wird nur verwendet, wenn sie nicht durch eine Komponente \xFCberschrieben wird.","Vordergrundfarbe f\xFCr Beschreibungstexte, die weitere Informationen anzeigen, z.B. f\xFCr eine Beschriftung.","Die f\xFCr Symbole in der Workbench verwendete Standardfarbe.","Allgemeine Rahmenfarbe f\xFCr fokussierte Elemente. Diese Farbe wird nur verwendet, wenn sie nicht durch eine Komponente \xFCberschrieben wird.","Ein zus\xE4tzlicher Rahmen um Elemente, mit dem diese von anderen getrennt werden, um einen gr\xF6\xDFeren Kontrast zu erreichen.","Ein zus\xE4tzlicher Rahmen um aktive Elemente, mit dem diese von anderen getrennt werden, um einen gr\xF6\xDFeren Kontrast zu erreichen.","Hintergrundfarbe der Textauswahl in der Workbench (z.B. f\xFCr Eingabefelder oder Textbereiche). Diese Farbe gilt nicht f\xFCr die Auswahl im Editor.","Farbe f\xFCr Text-Trennzeichen.","Vordergrundfarbe f\xFCr Links im Text.","Vordergrundfarbe f\xFCr angeklickte Links im Text und beim Zeigen darauf mit der Maus.","Vordergrundfarbe f\xFCr vorformatierte Textsegmente.","Hintergrundfarbe f\xFCr Blockzitate im Text.","Rahmenfarbe f\xFCr blockquote-Elemente im Text.","Hintergrundfarbe f\xFCr Codebl\xF6cke im Text.","Schattenfarbe von Widgets wie zum Beispiel Suchen/Ersetzen innerhalb des Editors.","Hintergrund f\xFCr Eingabefeld.","Vordergrund f\xFCr Eingabefeld.","Rahmen f\xFCr Eingabefeld.","Rahmenfarbe f\xFCr aktivierte Optionen in Eingabefeldern.","Hintergrundfarbe f\xFCr aktivierte Optionen in Eingabefeldern.","Hintergrundfarbe beim Daraufzeigen f\xFCr Optionen in Eingabefeldern.","Vordergrundfarbe f\xFCr aktivierte Optionen in Eingabefeldern.","Eingabefeld-Vordergrundfarbe f\xFCr Platzhaltertext.","Hintergrundfarbe bei der Eingabevalidierung f\xFCr den Schweregrad der Information.","Vordergrundfarbe bei der Eingabevalidierung f\xFCr den Schweregrad der Information.","Rahmenfarbe bei der Eingabevalidierung f\xFCr den Schweregrad der Information.","Hintergrundfarbe bei der Eingabevalidierung f\xFCr den Schweregrad der Warnung.","Vordergrundfarbe bei der Eingabevalidierung f\xFCr den Schweregrad der Warnung.","Rahmenfarbe bei der Eingabevalidierung f\xFCr den Schweregrad der Warnung.","Hintergrundfarbe bei der Eingabevalidierung f\xFCr den Schweregrad des Fehlers.","Vordergrundfarbe bei der Eingabevalidierung f\xFCr den Schweregrad des Fehlers.","Rahmenfarbe bei der Eingabevalidierung f\xFCr den Schweregrad des Fehlers.","Hintergrund f\xFCr Dropdown.","Hintergrund f\xFCr Dropdownliste.","Vordergrund f\xFCr Dropdown.","Rahmen f\xFCr Dropdown.","Hintergrundfarbe von Kontrollk\xE4stchenwidget.","Vordergrundfarbe von Kontrollk\xE4stchenwidget.","Rahmenfarbe von Kontrollk\xE4stchenwidget.","Vordergrundfarbe der Schaltfl\xE4che.","Hintergrundfarbe der Schaltfl\xE4che.","Hintergrundfarbe der Schaltfl\xE4che, wenn darauf gezeigt wird.","Rahmenfarbe der Schaltfl\xE4che.","Sekund\xE4re Vordergrundfarbe der Schaltfl\xE4che.","Hintergrundfarbe der sekund\xE4ren Schaltfl\xE4che.","Hintergrundfarbe der sekund\xE4ren Schaltfl\xE4che beim Daraufzeigen.","Hintergrundfarbe f\xFCr Badge. Badges sind kurze Info-Texte, z.B. f\xFCr Anzahl Suchergebnisse.","Vordergrundfarbe f\xFCr Badge. Badges sind kurze Info-Texte, z.B. f\xFCr Anzahl Suchergebnisse.","Schatten der Scrollleiste, um anzuzeigen, dass die Ansicht gescrollt wird.","Hintergrundfarbe vom Scrollbar-Schieber","Hintergrundfarbe des Schiebereglers, wenn darauf gezeigt wird.","Hintergrundfarbe des Schiebereglers, wenn darauf geklickt wird.","Hintergrundfarbe des Fortschrittbalkens, der f\xFCr zeitintensive Vorg\xE4nge angezeigt werden kann.","Hintergrundfarbe f\xFCr Fehlertext im Editor. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.","Vordergrundfarbe von Fehlerunterstreichungen im Editor.","Randfarbe von Fehlerfeldern im Editor.","Hintergrundfarbe f\xFCr Warnungstext im Editor. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.","Vordergrundfarbe von Warnungsunterstreichungen im Editor.","Randfarbe der Warnfelder im Editor.","Hintergrundfarbe f\xFCr Infotext im Editor. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.","Vordergrundfarbe von Informationsunterstreichungen im Editor.","Randfarbe der Infofelder im Editor.","Vordergrundfarbe der Hinweisunterstreichungen im Editor.","Randfarbe der Hinweisfelder im Editor.","Rahmenfarbe aktiver Trennleisten.","Hintergrundfarbe des Editors.","Standardvordergrundfarbe des Editors.","Hintergrundfarbe von Editor-Widgets wie zum Beispiel Suchen/Ersetzen.","Vordergrundfarbe f\xFCr Editorwidgets wie Suchen/Ersetzen.","Rahmenfarbe von Editorwigdets. Die Farbe wird nur verwendet, wenn f\xFCr das Widget ein Rahmen verwendet wird und die Farbe nicht von einem Widget \xFCberschrieben wird.","Rahmenfarbe der Gr\xF6\xDFenanpassungsleiste von Editorwigdets. Die Farbe wird nur verwendet, wenn f\xFCr das Widget ein Gr\xF6\xDFenanpassungsrahmen verwendet wird und die Farbe nicht von einem Widget au\xDFer Kraft gesetzt wird.","Schnellauswahl der Hintergrundfarbe. Im Widget f\xFCr die Schnellauswahl sind Auswahlelemente wie die Befehlspalette enthalten.","Vordergrundfarbe der Schnellauswahl. Im Widget f\xFCr die Schnellauswahl sind Auswahlelemente wie die Befehlspalette enthalten.","Hintergrundfarbe f\xFCr den Titel der Schnellauswahl. Im Widget f\xFCr die Schnellauswahl sind Auswahlelemente wie die Befehlspalette enthalten.","Schnellauswahlfarbe f\xFCr das Gruppieren von Bezeichnungen.","Schnellauswahlfarbe f\xFCr das Gruppieren von Rahmen.","Die Hintergrundfarbe der Tastenbindungsbeschriftung. Die Tastenbindungsbeschriftung wird verwendet, um eine Tastenkombination darzustellen.","Die Vordergrundfarbe der Tastenbindungsbeschriftung. Die Tastenbindungsbeschriftung wird verwendet, um eine Tastenkombination darzustellen.","Die Rahmenfarbe der Tastenbindungsbeschriftung. Die Tastenbindungsbeschriftung wird verwendet, um eine Tastenkombination darzustellen.","Die Rahmenfarbe der Schaltfl\xE4che der Tastenbindungsbeschriftung. Die Tastenbindungsbeschriftung wird verwendet, um eine Tastenkombination darzustellen.","Farbe der Editor-Auswahl.","Farbe des gew\xE4hlten Text f\xFCr einen hohen Kontrast","Die Farbe der Auswahl befindet sich in einem inaktiven Editor. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegende Dekorationen verdeckt.","Farbe f\xFCr Bereiche mit dem gleichen Inhalt wie die Auswahl. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.","Randfarbe f\xFCr Bereiche, deren Inhalt der Auswahl entspricht.","Farbe des aktuellen Suchergebnisses.","Farbe der anderen Suchergebnisse. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.","Farbe des Bereichs, der die Suche eingrenzt. Die Farbe darf nicht deckend sein, damit sie nicht die zugrunde liegenden Dekorationen verdeckt.","Randfarbe des aktuellen Suchergebnisses.","Randfarbe der anderen Suchtreffer.","Rahmenfarbe des Bereichs, der die Suche eingrenzt. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.","Farbe der Abfrage\xFCbereinstimmungen des Such-Editors","Rahmenfarbe der Abfrage\xFCbereinstimmungen des Such-Editors","Hervorhebung unterhalb des Worts, f\xFCr das ein Hoverelement angezeigt wird. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.","Hintergrundfarbe des Editor-Mauszeigers.","Vordergrundfarbe des Editor-Mauszeigers","Rahmenfarbe des Editor-Mauszeigers.","Hintergrundfarbe der Hoverstatusleiste des Editors.","Farbe der aktiven Links.","Vordergrundfarbe f\xFCr Inlinehinweise","Hintergrundfarbe f\xFCr Inlinehinweise","Vordergrundfarbe von Inlinehinweisen f\xFCr Typen","Hintergrundfarbe von Inlinehinweisen f\xFCr Typen","Vordergrundfarbe von Inlinehinweisen f\xFCr Parameter","Hintergrundfarbe von Inlinehinweisen f\xFCr Parameter",'Die f\xFCr das Aktionssymbol "Gl\xFChbirne" verwendete Farbe.','Die f\xFCr das Aktionssymbol "Automatische Gl\xFChbirnenkorrektur" verwendete Farbe.',"Hintergrundfarbe f\xFCr eingef\xFCgten Text. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.","Hintergrundfarbe f\xFCr Text, der entfernt wurde. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.","Hintergrundfarbe f\xFCr eingef\xFCgte Linien. Die Farbe darf nicht deckend sein, um zugrunde liegende Dekorationen nicht auszublenden.","Hintergrundfarbe f\xFCr Linien, die entfernt wurden. Die Farbe darf nicht deckend sein, um zugrunde liegende Dekorationen nicht auszublenden.","Hintergrundfarbe f\xFCr den Rand, an dem Zeilen eingef\xFCgt wurden.","Hintergrundfarbe f\xFCr den Rand, an dem die Linien entfernt wurden.","Vordergrund des Diff-\xDCbersichtslineals f\xFCr eingef\xFCgten Inhalt.","Vordergrund des Diff-\xDCbersichtslineals f\xFCr entfernten Inhalt.","Konturfarbe f\xFCr eingef\xFCgten Text.","Konturfarbe f\xFCr entfernten Text.","Die Rahmenfarbe zwischen zwei Text-Editoren.","Farbe der diagonalen F\xFCllung des Vergleichs-Editors. Die diagonale F\xFCllung wird in Ansichten mit parallelem Vergleich verwendet.","Hintergrundfarbe der Liste/Struktur f\xFCr das fokussierte Element, wenn die Liste/Struktur aktiv ist. Eine aktive Liste/Struktur hat Tastaturfokus, eine inaktive hingegen nicht.","Vordergrundfarbe der Liste/Struktur f\xFCr das fokussierte Element, wenn die Liste/Struktur aktiv ist. Eine aktive Liste/Struktur hat Tastaturfokus, eine inaktive hingegen nicht.","Konturfarbe der Liste/Struktur f\xFCr das fokussierte Element, wenn die Liste/Struktur aktiv ist. Eine aktive Liste/Struktur hat Tastaturfokus, eine inaktive hingegen nicht.","Hintergrundfarbe der Liste/Struktur f\xFCr das ausgew\xE4hlte Element, wenn die Liste/Struktur aktiv ist. Eine aktive Liste/Struktur hat Tastaturfokus, eine inaktive hingegen nicht.","Vordergrundfarbe der Liste/Struktur f\xFCr das ausgew\xE4hlte Element, wenn die Liste/Struktur aktiv ist. Eine aktive Liste/Struktur hat Tastaturfokus, eine inaktive hingegen nicht.","Vordergrundfarbe des Symbols der Liste/Struktur f\xFCr das ausgew\xE4hlte Element, wenn die Liste/Struktur aktiv ist. Eine aktive Liste/Struktur hat Tastaturfokus, eine inaktive hingegen nicht.","Hintergrundfarbe der Liste/Struktur f\xFCr das ausgew\xE4hlte Element, wenn die Liste/Struktur inaktiv ist. Eine aktive Liste/Struktur hat Tastaturfokus, eine inaktive hingegen nicht.","Vordergrundfarbe der Liste/Struktur f\xFCr das ausgew\xE4hlte Element, wenn die Liste/Baumstruktur inaktiv ist. Eine aktive Liste/Baumstruktur hat Tastaturfokus, eine inaktive hingegen nicht.","Vordergrundfarbe des Symbols der Liste/Struktur f\xFCr das ausgew\xE4hlte Element, wenn die Liste/Struktur inaktiv ist. Eine aktive Liste/Struktur hat Tastaturfokus, eine inaktive hingegen nicht.","Hintergrundfarbe der Liste/Struktur f\xFCr das fokussierte Element, wenn die Liste/Struktur inaktiv ist. Eine aktive Liste/Struktur hat Tastaturfokus, eine inaktive hingegen nicht.","Konturfarbe der Liste/Struktur f\xFCr das fokussierte Element, wenn die Liste/Struktur inaktiv ist. Eine aktive Liste/Struktur hat Tastaturfokus, eine inaktive hingegen nicht.","Hintergrund der Liste/Struktur, wenn mit der Maus auf Elemente gezeigt wird.","Vordergrund der Liste/Struktur, wenn mit der Maus auf Elemente gezeigt wird.","Drag & Drop-Hintergrund der Liste/Struktur, wenn Elemente mithilfe der Maus verschoben werden.","Vordergrundfarbe der Liste/Struktur zur Trefferhervorhebung beim Suchen innerhalb der Liste/Struktur.","Die Vordergrundfarbe der Liste/Struktur des Treffers hebt aktiv fokussierte Elemente hervor, wenn innerhalb der Liste / der Struktur gesucht wird.","Vordergrundfarbe einer Liste/Struktur f\xFCr ung\xFCltige Elemente, z.B. ein nicht ausgel\xF6ster Stamm im Explorer.","Vordergrundfarbe f\xFCr Listenelemente, die Fehler enthalten.","Vordergrundfarbe f\xFCr Listenelemente, die Warnungen enthalten.","Hintergrundfarbe des Typfilterwidgets in Listen und Strukturen.","Konturfarbe des Typfilterwidgets in Listen und Strukturen.","Konturfarbe des Typfilterwidgets in Listen und Strukturen, wenn es keine \xDCbereinstimmungen gibt.","Hintergrundfarbe der gefilterten \xDCbereinstimmung","Rahmenfarbe der gefilterten \xDCbereinstimmung","Strukturstrichfarbe f\xFCr die Einzugsf\xFChrungslinien.","Tabellenrahmenfarbe zwischen Spalten.","Hintergrundfarbe f\xFCr ungerade Tabellenzeilen.","Hintergrundfarbe f\xFCr nicht hervorgehobene Listen-/Strukturelemente.",'Verwenden Sie stattdessen "quickInputList.focusBackground".',"Die Hintergrundfarbe der Schnellauswahl f\xFCr das fokussierte Element.","Die Vordergrundfarbe des Symbols der Schnellauswahl f\xFCr das fokussierte Element.","Die Hintergrundfarbe der Schnellauswahl f\xFCr das fokussierte Element.","Rahmenfarbe von Men\xFCs.","Vordergrundfarbe von Men\xFCelementen.","Hintergrundfarbe von Men\xFCelementen.","Vordergrundfarbe des ausgew\xE4hlten Men\xFCelements im Men\xFC.","Hintergrundfarbe des ausgew\xE4hlten Men\xFCelements im Men\xFC.","Rahmenfarbe des ausgew\xE4hlten Men\xFCelements im Men\xFC.","Farbe eines Trenner-Men\xFCelements in Men\xFCs.","Symbolleistenhintergrund beim Bewegen der Maus \xFCber Aktionen","Symbolleistengliederung beim Bewegen der Maus \xFCber Aktionen","Symbolleistenhintergrund beim Halten der Maus \xFCber Aktionen","Hervorhebungs-Hintergrundfarbe eines Codeschnipsel-Tabstopps.","Hervorhebungs-Rahmenfarbe eines Codeschnipsel-Tabstopps.","Hervorhebungs-Hintergrundfarbe des letzten Tabstopps eines Codeschnipsels.","Rahmenfarbe zur Hervorhebung des letzten Tabstopps eines Codeschnipsels.","Farbe der Breadcrumb-Elemente, die den Fokus haben.","Hintergrundfarbe der Breadcrumb-Elemente.","Farbe der Breadcrumb-Elemente, die den Fokus haben.","Die Farbe der ausgew\xE4hlten Breadcrumb-Elemente.","Hintergrundfarbe des Breadcrumb-Auswahltools.","Hintergrund des aktuellen Headers in Inlinezusammenf\xFChrungskonflikten. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.","Hintergrund f\xFCr den aktuellen Inhalt in Inlinezusammenf\xFChrungskonflikten. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.","Hintergrund f\xFCr eingehende Header in Inlinezusammenf\xFChrungskonflikten. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.","Hintergrund f\xFCr eingehenden Inhalt in Inlinezusammenf\xFChrungskonflikten. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.","Headerhintergrund f\xFCr gemeinsame Vorg\xE4ngerelemente in Inlinezusammenf\xFChrungskonflikten. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.","Hintergrund des Inhalts gemeinsamer Vorg\xE4ngerelemente in Inlinezusammenf\xFChrungskonflikt. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.","Rahmenfarbe f\xFCr Kopfzeilen und die Aufteilung in Inline-Mergingkonflikten.","Aktueller \xDCbersichtslineal-Vordergrund f\xFCr Inline-Mergingkonflikte.","Eingehender \xDCbersichtslineal-Vordergrund f\xFCr Inline-Mergingkonflikte.","Hintergrund des \xDCbersichtslineals des gemeinsamen \xFCbergeordneten Elements bei Inlinezusammenf\xFChrungskonflikten.","\xDCbersichtslinealmarkerfarbe f\xFCr das Suchen von \xDCbereinstimmungen. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.","\xDCbersichtslinealmarkerfarbe f\xFCr das Hervorheben der Auswahl. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.","Minimap-Markerfarbe f\xFCr gefundene \xDCbereinstimmungen.","Minimap-Markerfarbe f\xFCr wiederholte Editorauswahlen.","Minimap-Markerfarbe f\xFCr die Editorauswahl.","Minimapmarkerfarbe f\xFCr Fehler","Minimapmarkerfarbe f\xFCr Warnungen","Hintergrundfarbe der Minimap.","Deckkraft von Vordergrundelementen, die in der Minimap gerendert werden. Beispiel: \u201E#000000c0\u201C wird die Elemente mit einer Deckkraft von 75 % rendern.","Hintergrundfarbe des Minimap-Schiebereglers.","Hintergrundfarbe des Minimap-Schiebereglers beim Daraufzeigen.","Hintergrundfarbe des Minimap-Schiebereglers, wenn darauf geklickt wird.","Die Farbe, die f\xFCr das Problemfehlersymbol verwendet wird.","Die Farbe, die f\xFCr das Problemwarnsymbol verwendet wird.","Die Farbe, die f\xFCr das Probleminfosymbol verwendet wird.","Die in Diagrammen verwendete Vordergrundfarbe.","Die f\xFCr horizontale Linien in Diagrammen verwendete Farbe.","Die in Diagrammvisualisierungen verwendete Farbe Rot.","Die in Diagrammvisualisierungen verwendete Farbe Blau.","Die in Diagrammvisualisierungen verwendete Farbe Gelb.","Die in Diagrammvisualisierungen verwendete Farbe Orange.","Die in Diagrammvisualisierungen verwendete Farbe Gr\xFCn.","Die in Diagrammvisualisierungen verwendete Farbe Violett."],"vs/platform/theme/common/iconRegistry":["Die ID der zu verwendenden Schriftart. Sofern nicht festgelegt, wird die zuerst definierte Schriftart verwendet.","Das der Symboldefinition zugeordnete Schriftzeichen.","Symbol f\xFCr Aktion zum Schlie\xDFen in Widgets","Symbol f\xFCr den Wechsel zur vorherigen Editor-Position.","Symbol f\xFCr den Wechsel zur n\xE4chsten Editor-Position."],"vs/platform/undoRedo/common/undoRedoService":["Die folgenden Dateien wurden geschlossen und auf dem Datentr\xE4ger ge\xE4ndert: {0}.","Die folgenden Dateien wurden auf inkompatible Weise ge\xE4ndert: {0}.",'"{0}" konnte nicht f\xFCr alle Dateien r\xFCckg\xE4ngig gemacht werden. {1}','"{0}" konnte nicht f\xFCr alle Dateien r\xFCckg\xE4ngig gemacht werden. {1}','"{0}" konnte nicht f\xFCr alle Dateien r\xFCckg\xE4ngig gemacht werden, da \xC4nderungen an {1} vorgenommen wurden.','"{0}" konnte nicht f\xFCr alle Dateien r\xFCckg\xE4ngig gemacht werden, weil bereits ein Vorgang zum R\xFCckg\xE4ngigmachen oder Wiederholen f\xFCr "{1}" durchgef\xFChrt wird.','"{0}" konnte nicht f\xFCr alle Dateien r\xFCckg\xE4ngig gemacht werden, weil in der Zwischenzeit bereits ein Vorgang zum R\xFCckg\xE4ngigmachen oder Wiederholen durchgef\xFChrt wurde.','M\xF6chten Sie "{0}" f\xFCr alle Dateien r\xFCckg\xE4ngig machen?',"In {0} Dateien r\xFCckg\xE4ngig machen","Datei r\xFCckg\xE4ngig machen","Abbrechen",'"{0}" konnte nicht r\xFCckg\xE4ngig gemacht werden, weil bereits ein Vorgang zum R\xFCckg\xE4ngigmachen oder Wiederholen durchgef\xFChrt wird.','M\xF6chten Sie "{0}" r\xFCckg\xE4ngig machen?',"Ja","Nein",'"{0}" konnte nicht in allen Dateien wiederholt werden. {1}','"{0}" konnte nicht in allen Dateien wiederholt werden. {1}','"{0}" konnte nicht in allen Dateien wiederholt werden, da \xC4nderungen an {1} vorgenommen wurden.','"{0}" konnte nicht f\xFCr alle Dateien wiederholt werden, weil bereits ein Vorgang zum R\xFCckg\xE4ngigmachen oder Wiederholen f\xFCr "{1}" durchgef\xFChrt wird.','"{0}" konnte nicht f\xFCr alle Dateien wiederholt werden, weil in der Zwischenzeit bereits ein Vorgang zum R\xFCckg\xE4ngigmachen oder Wiederholen durchgef\xFChrt wurde.','"{0}" konnte nicht wiederholt werden, weil bereits ein Vorgang zum R\xFCckg\xE4ngigmachen oder Wiederholen durchgef\xFChrt wird.'],"vs/platform/workspace/common/workspace":["Codearbeitsbereich"]});

//# sourceMappingURL=../../../min-maps/vs/editor/editor.main.nls.de.js.map