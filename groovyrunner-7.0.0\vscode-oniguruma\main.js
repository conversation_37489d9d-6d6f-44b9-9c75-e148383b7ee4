!function(n,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.onig=t():n.onig=t()}(this,(function(){return function(n){var t={};function e(r){if(t[r])return t[r].exports;var i=t[r]={i:r,l:!1,exports:{}};return n[r].call(i.exports,i,i.exports,e),i.l=!0,i.exports}return e.m=n,e.c=t,e.d=function(n,t,r){e.o(n,t)||Object.defineProperty(n,t,{enumerable:!0,get:r})},e.r=function(n){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(n,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(n,"__esModule",{value:!0})},e.t=function(n,t){if(1&t&&(n=e(n)),8&t)return n;if(4&t&&"object"==typeof n&&n&&n.__esModule)return n;var r=Object.create(null);if(e.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:n}),2&t&&"string"!=typeof n)for(var i in n)e.d(r,i,function(t){return n[t]}.bind(null,i));return r},e.n=function(n){var t=n&&n.__esModule?function(){return n.default}:function(){return n};return e.d(t,"a",t),t},e.o=function(n,t){return Object.prototype.hasOwnProperty.call(n,t)},e.p="",e(e.s=0)}([function(n,t,e){"use strict";var r=this&&this.__importDefault||function(n){return n&&n.__esModule?n:{default:n}};Object.defineProperty(t,"__esModule",{value:!0});const i=r(e(1));let o=null,a=!1;class f{constructor(n){const t=n.length,e=f._utf8ByteLength(n),r=e!==t,i=r?new Uint32Array(t+1):null;r&&(i[t]=e);const o=r?new Uint32Array(e+1):null;r&&(o[e]=t);const a=new Uint8Array(e);let u=0;for(let e=0;e<t;e++){const f=n.charCodeAt(e);let s=f,c=!1;if(f>=55296&&f<=56319&&e+1<t){const t=n.charCodeAt(e+1);t>=56320&&t<=57343&&(s=65536+(f-55296<<10)|t-56320,c=!0)}r&&(i[e]=u,c&&(i[e+1]=u),s<=127?o[u+0]=e:s<=2047?(o[u+0]=e,o[u+1]=e):s<=65535?(o[u+0]=e,o[u+1]=e,o[u+2]=e):(o[u+0]=e,o[u+1]=e,o[u+2]=e,o[u+3]=e)),s<=127?a[u++]=s:s<=2047?(a[u++]=192|(1984&s)>>>6,a[u++]=128|(63&s)>>>0):s<=65535?(a[u++]=224|(61440&s)>>>12,a[u++]=128|(4032&s)>>>6,a[u++]=128|(63&s)>>>0):(a[u++]=240|(1835008&s)>>>18,a[u++]=128|(258048&s)>>>12,a[u++]=128|(4032&s)>>>6,a[u++]=128|(63&s)>>>0),c&&e++}this.utf16Length=t,this.utf8Length=e,this.utf16Value=n,this.utf8Value=a,this.utf16OffsetToUtf8=i,this.utf8OffsetToUtf16=o}static _utf8ByteLength(n){let t=0;for(let e=0,r=n.length;e<r;e++){const i=n.charCodeAt(e);let o=i,a=!1;if(i>=55296&&i<=56319&&e+1<r){const t=n.charCodeAt(e+1);t>=56320&&t<=57343&&(o=65536+(i-55296<<10)|t-56320,a=!0)}t+=o<=127?1:o<=2047?2:o<=65535?3:4,a&&e++}return t}createString(n){const t=n._malloc(this.utf8Length);return n.HEAPU8.set(this.utf8Value,t),t}}class u{constructor(n){if(this.id=++u.LAST_ID,!o)throw new Error("Must invoke loadWASM first.");this._onigBinding=o,this.content=n;const t=new f(n);this.utf16Length=t.utf16Length,this.utf8Length=t.utf8Length,this.utf16OffsetToUtf8=t.utf16OffsetToUtf8,this.utf8OffsetToUtf16=t.utf8OffsetToUtf16,this.utf8Length<1e4&&!u._sharedPtrInUse?(u._sharedPtr||(u._sharedPtr=o._malloc(1e4)),u._sharedPtrInUse=!0,o.HEAPU8.set(t.utf8Value,u._sharedPtr),this.ptr=u._sharedPtr):this.ptr=t.createString(o)}convertUtf8OffsetToUtf16(n){return this.utf8OffsetToUtf16?n<0?0:n>this.utf8Length?this.utf16Length:this.utf8OffsetToUtf16[n]:n}convertUtf16OffsetToUtf8(n){return this.utf16OffsetToUtf8?n<0?0:n>this.utf16Length?this.utf8Length:this.utf16OffsetToUtf8[n]:n}dispose(){this.ptr===u._sharedPtr?u._sharedPtrInUse=!1:this._onigBinding._free(this.ptr)}}t.OnigString=u,u.LAST_ID=0,u._sharedPtr=0,u._sharedPtrInUse=!1;class s{constructor(n){if(!o)throw new Error("Must invoke loadWASM first.");const t=[],e=[];for(let r=0,i=n.length;r<i;r++){const i=new f(n[r]);t[r]=i.createString(o),e[r]=i.utf8Length}const r=o._malloc(4*n.length);o.HEAPU32.set(t,r/4);const i=o._malloc(4*n.length);o.HEAPU32.set(e,i/4);const a=o._createOnigScanner(r,i,n.length);for(let e=0,r=n.length;e<r;e++)o._free(t[e]);o._free(i),o._free(r),0===a&&function(n){throw new Error(n.UTF8ToString(n._getLastOnigError()))}(o),this._onigBinding=o,this._ptr=a}dispose(){this._onigBinding._freeOnigScanner(this._ptr)}findNextMatchSync(n,t,e=a){if("string"==typeof n){n=new u(n);const r=this._findNextMatchSync(n,t,e);return n.dispose(),r}return this._findNextMatchSync(n,t,e)}_findNextMatchSync(n,t,e){const r=this._onigBinding;let i;if(i=e?r._findNextOnigScannerMatchDbg(this._ptr,n.id,n.ptr,n.utf8Length,n.convertUtf16OffsetToUtf8(t)):r._findNextOnigScannerMatch(this._ptr,n.id,n.ptr,n.utf8Length,n.convertUtf16OffsetToUtf8(t)),0===i)return null;const o=r.HEAPU32;let a=i/4;const f=o[a++],u=o[a++];let s=[];for(let t=0;t<u;t++){const e=n.convertUtf8OffsetToUtf16(o[a++]),r=n.convertUtf8OffsetToUtf16(o[a++]);s[t]={start:e,end:r,length:r-e}}return{index:f,captureIndices:s}}}t.OnigScanner=s;let c=!1;t.loadWASM=function(n){if(c)throw new Error("Cannot invoke loadWASM more than once.");let t,e,r,a;c=!0,n instanceof ArrayBuffer||n instanceof Response?t=n:(t=n.data,e=n.print);const f=new Promise((n,t)=>{r=n,a=t});let u;return u=t instanceof ArrayBuffer?function(n){return t=>WebAssembly.instantiate(n,t)}(t):t instanceof Response&&"function"==typeof WebAssembly.instantiateStreaming?function(n){return t=>WebAssembly.instantiateStreaming(n,t)}(t):function(n){return async t=>{const e=await n.arrayBuffer();return WebAssembly.instantiate(e,t)}}(t),function(n,t,e,r){i.default({print:t,instantiateWasm:(t,e)=>{if("undefined"==typeof performance){const n=()=>Date.now();t.env.emscripten_get_now=n,t.wasi_snapshot_preview1.emscripten_get_now=n}return n(t).then(n=>e(n.instance),r),{}}}).then(n=>{o=n,e()})}(u,e,r,a),f},t.createOnigString=function(n){return new u(n)},t.createOnigScanner=function(n){return new s(n)},t.setDefaultDebugCall=function(n){a=n}},function(n,t,e){var r=function(){"undefined"!=typeof document&&document.currentScript&&document.currentScript.src;return function(n){var t,e=void 0!==(n=n||{})?n:{},r={};for(t in e)e.hasOwnProperty(t)&&(r[t]=e[t]);var i,o=[],a=!1,f=!1,u=!0,s="";function c(n){return e.locateFile?e.locateFile(n,s):s+n}u&&("undefined"!=typeof read&&function(n){return read(n)},i=function(n){var t;return"function"==typeof readbuffer?new Uint8Array(readbuffer(n)):(_("object"==typeof(t=read(n,"binary"))),t)},"undefined"!=typeof scriptArgs?o=scriptArgs:void 0!==arguments&&(o=arguments),"function"==typeof quit&&function(n){quit(n)},"undefined"!=typeof onig_print&&("undefined"==typeof console&&(console={}),console.log=onig_print,console.warn=console.error="undefined"!=typeof printErr?printErr:onig_print));var l=e.print||console.log.bind(console),d=e.printErr||console.warn.bind(console);for(t in r)r.hasOwnProperty(t)&&(e[t]=r[t]);r=null,e.arguments&&(o=e.arguments),e.thisProgram&&e.thisProgram,e.quit&&e.quit;var p,h,y=function(n){n};e.wasmBinary&&(p=e.wasmBinary),e.noExitRuntime&&e.noExitRuntime,"object"!=typeof WebAssembly&&d("no native wasm support detected");var m=new WebAssembly.Table({initial:51,maximum:51,element:"anyfunc"}),g=!1;function _(n,t){n||G("Assertion failed: "+t)}var v="undefined"!=typeof TextDecoder?new TextDecoder("utf8"):void 0;function b(n,t,e){for(var r=t+e,i=t;n[i]&&!(i>=r);)++i;if(i-t>16&&n.subarray&&v)return v.decode(n.subarray(t,i));for(var o="";t<i;){var a=n[t++];if(128&a){var f=63&n[t++];if(192!=(224&a)){var u=63&n[t++];if((a=224==(240&a)?(15&a)<<12|f<<6|u:(7&a)<<18|f<<12|u<<6|63&n[t++])<65536)o+=String.fromCharCode(a);else{var s=a-65536;o+=String.fromCharCode(55296|s>>10,56320|1023&s)}}else o+=String.fromCharCode((31&a)<<6|f)}else o+=String.fromCharCode(a)}return o}function w(n,t){return n?b(S,n,t):""}"undefined"!=typeof TextDecoder&&new TextDecoder("utf-16le");var A,S,O,C=65536;function U(n,t){return n%t>0&&(n+=t-n%t),n}function M(n){A=n,e.HEAP8=new Int8Array(n),e.HEAP16=new Int16Array(n),e.HEAP32=O=new Int32Array(n),e.HEAPU8=S=new Uint8Array(n),e.HEAPU16=new Uint16Array(n),e.HEAPU32=new Uint32Array(n),e.HEAPF32=new Float32Array(n),e.HEAPF64=new Float64Array(n)}var P=5547200,x=304160,T=e.INITIAL_MEMORY||16777216;function R(n){for(;n.length>0;){var t=n.shift();if("function"!=typeof t){var r=t.func;"number"==typeof r?void 0===t.arg?e.dynCall_v(r):e.dynCall_vi(r,t.arg):r(void 0===t.arg?null:t.arg)}else t(e)}}(h=e.wasmMemory?e.wasmMemory:new WebAssembly.Memory({initial:T/C,maximum:2147483648/C}))&&(A=h.buffer),T=A.byteLength,M(A),O[x>>2]=P;var E=[],I=[],L=[],W=[];function j(){if(e.preRun)for("function"==typeof e.preRun&&(e.preRun=[e.preRun]);e.preRun.length;)B(e.preRun.shift());R(E)}function D(){!0,R(I)}function k(){R(L)}function N(){if(e.postRun)for("function"==typeof e.postRun&&(e.postRun=[e.postRun]);e.postRun.length;)H(e.postRun.shift());R(W)}function B(n){E.unshift(n)}function H(n){W.unshift(n)}Math.abs,Math.ceil,Math.floor,Math.min;var F=0,z=null,q=null;function V(n){F++,e.monitorRunDependencies&&e.monitorRunDependencies(F)}function Y(n){if(F--,e.monitorRunDependencies&&e.monitorRunDependencies(F),0==F&&(null!==z&&(clearInterval(z),z=null),q)){var t=q;q=null,t()}}function G(n){throw e.onAbort&&e.onAbort(n),l(n+=""),d(n),g=!0,1,n="abort("+n+"). Build with -s ASSERTIONS=1 for more info.",new WebAssembly.RuntimeError(n)}e.preloadedImages={},e.preloadedAudios={};var J="data:application/octet-stream;base64,";function K(n){return String.prototype.startsWith?n.startsWith(J):0===n.indexOf(J)}var Q,X="onig.wasm";function Z(){try{if(p)return new Uint8Array(p);if(i)return i(X);throw"both async and sync fetching of the wasm failed"}catch(n){G(n)}}function $(){return p||!a&&!f||"function"!=typeof fetch?new Promise((function(n,t){n(Z())})):fetch(X,{credentials:"same-origin"}).then((function(n){if(!n.ok)throw"failed to load wasm binary file at '"+X+"'";return n.arrayBuffer()})).catch((function(){return Z()}))}function nn(){var n={env:cn,wasi_snapshot_preview1:cn};function t(n,t){var r=n.exports;e.asm=r,Y()}function r(n){t(n.instance)}function i(t){return $().then((function(t){return WebAssembly.instantiate(t,n)})).then(t,(function(n){d("failed to asynchronously prepare wasm: "+n),G(n)}))}if(V(),e.instantiateWasm)try{return e.instantiateWasm(n,t)}catch(n){return d("Module.instantiateWasm callback failed with error: "+n),!1}return function(){if(p||"function"!=typeof WebAssembly.instantiateStreaming||K(X)||"function"!=typeof fetch)return i(r);fetch(X,{credentials:"same-origin"}).then((function(t){return WebAssembly.instantiateStreaming(t,n).then(r,(function(n){d("wasm streaming compile failed: "+n),d("falling back to ArrayBuffer instantiation"),i(r)}))}))}(),{}}function tn(){return 304160}function en(n,t,e){S.copyWithin(n,t,t+e)}function rn(){return S.length}function on(n){try{return h.grow(n-A.byteLength+65535>>>16),M(h.buffer),1}catch(n){}}function an(n){var t=rn();if(n>2147483648)return!1;for(var e=1;e<=4;e*=2){var r=t*(1+.2/e);if(r=Math.min(r,n+100663296),on(Math.min(2147483648,U(Math.max(16777216,n,r),65536))))return!0}return!1}K(X)||(X=c(X)),I.push({func:function(){pn()}}),Q="undefined"!=typeof dateNow?dateNow:function(){return performance.now()};var fn={mappings:{},buffers:[null,[],[]],printChar:function(n,t){var e=fn.buffers[n];0===t||10===t?((1===n?l:d)(b(e,0)),e.length=0):e.push(t)},varargs:void 0,get:function(){return fn.varargs+=4,O[fn.varargs-4>>2]},getStr:function(n){return w(n)},get64:function(n,t){return n}};function un(n,t,e,r){for(var i=0,o=0;o<e;o++){for(var a=O[t+8*o>>2],f=O[t+(8*o+4)>>2],u=0;u<f;u++)fn.printChar(n,S[a+u]);i+=f}return O[r>>2]=i,0}function sn(n){y(0|n)}var cn={emscripten_get_now:Q,emscripten_get_sbrk_ptr:tn,emscripten_memcpy_big:en,emscripten_resize_heap:an,fd_write:un,memory:h,setTempRet0:sn,table:m},ln=nn();e.asm=ln;var dn,pn=e.___wasm_call_ctors=function(){return(pn=e.___wasm_call_ctors=e.asm.__wasm_call_ctors).apply(null,arguments)};e._malloc=function(){return(e._malloc=e.asm.malloc).apply(null,arguments)},e._free=function(){return(e._free=e.asm.free).apply(null,arguments)},e._getLastOnigError=function(){return(e._getLastOnigError=e.asm.getLastOnigError).apply(null,arguments)},e._createOnigScanner=function(){return(e._createOnigScanner=e.asm.createOnigScanner).apply(null,arguments)},e._freeOnigScanner=function(){return(e._freeOnigScanner=e.asm.freeOnigScanner).apply(null,arguments)},e._findNextOnigScannerMatch=function(){return(e._findNextOnigScannerMatch=e.asm.findNextOnigScannerMatch).apply(null,arguments)},e._findNextOnigScannerMatchDbg=function(){return(e._findNextOnigScannerMatchDbg=e.asm.findNextOnigScannerMatchDbg).apply(null,arguments)},e.stackSave=function(){return(e.stackSave=e.asm.stackSave).apply(null,arguments)},e.stackAlloc=function(){return(e.stackAlloc=e.asm.stackAlloc).apply(null,arguments)},e.stackRestore=function(){return(e.stackRestore=e.asm.stackRestore).apply(null,arguments)},e.__growWasmMemory=function(){return(e.__growWasmMemory=e.asm.__growWasmMemory).apply(null,arguments)},e.dynCall_vi=function(){return(e.dynCall_vi=e.asm.dynCall_vi).apply(null,arguments)},e.dynCall_iiii=function(){return(e.dynCall_iiii=e.asm.dynCall_iiii).apply(null,arguments)},e.dynCall_iiiii=function(){return(e.dynCall_iiiii=e.asm.dynCall_iiiii).apply(null,arguments)},e.dynCall_iii=function(){return(e.dynCall_iii=e.asm.dynCall_iii).apply(null,arguments)},e.dynCall_iidiiii=function(){return(e.dynCall_iidiiii=e.asm.dynCall_iidiiii).apply(null,arguments)},e.dynCall_vii=function(){return(e.dynCall_vii=e.asm.dynCall_vii).apply(null,arguments)},e.dynCall_ii=function(){return(e.dynCall_ii=e.asm.dynCall_ii).apply(null,arguments)},e.dynCall_i=function(){return(e.dynCall_i=e.asm.dynCall_i).apply(null,arguments)},e.dynCall_jiji=function(){return(e.dynCall_jiji=e.asm.dynCall_jiji).apply(null,arguments)};function hn(n){function t(){dn||(dn=!0,e.calledRun=!0,g||(D(),k(),e.onRuntimeInitialized&&e.onRuntimeInitialized(),N()))}n=n||o,F>0||(j(),F>0||(e.setStatus?(e.setStatus("Running..."),setTimeout((function(){setTimeout((function(){e.setStatus("")}),1),t()}),1)):t()))}if(e.asm=ln,e.UTF8ToString=w,e.then=function(n){if(dn)n(e);else{var t=e.onRuntimeInitialized;e.onRuntimeInitialized=function(){t&&t(),n(e)}}return e},q=function n(){dn||hn(),dn||(q=n)},e.run=hn,e.preInit)for("function"==typeof e.preInit&&(e.preInit=[e.preInit]);e.preInit.length>0;)e.preInit.pop()();return!0,hn(),n}}();n.exports=r}])}));