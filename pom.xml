<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.eve</groupId>
    <artifactId>eve-jira</artifactId>
    <version>1.0.5</version>
    <organization>
        <name>Example Company</name>
        <url>http://www.example.com/</url>
    </organization>
    <name>eve-jira</name>
    <description>This is the com.eve:eve-jira plugin for Atlassian JIRA.</description>
    <packaging>atlassian-plugin</packaging>
    <dependencies>
        <dependency>
            <groupId>com.atlassian.activeobjects</groupId>
            <artifactId>activeobjects-plugin</artifactId>
            <version>${ao.version}</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>com.atlassian.cache</groupId>
            <artifactId>atlassian-cache-api</artifactId>
            <version>2.0.2</version>
        </dependency>


        <dependency>
            <groupId>com.atlassian.jira</groupId>
            <artifactId>jira-api</artifactId>
            <version>${jira.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.atlassian.jira</groupId>
            <artifactId>jira-core</artifactId>
            <version>${jira.version}</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.10</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.atlassian.plugin</groupId>
            <artifactId>atlassian-spring-scanner-annotation</artifactId>
            <version>${atlassian.spring.scanner.version}</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>com.atlassian.plugin</groupId>
            <artifactId>atlassian-spring-scanner-runtime</artifactId>
            <version>${atlassian.spring.scanner.version}</version>
            <scope>runtime</scope>
        </dependency>

        <dependency>
            <groupId>javax.inject</groupId>
            <artifactId>javax.inject</artifactId>
            <version>1</version>
            <scope>provided</scope>
        </dependency>

        <!-- WIRED TEST RUNNER DEPENDENCIES -->
        <dependency>
            <groupId>com.atlassian.plugins</groupId>
            <artifactId>atlassian-plugins-osgi-testrunner</artifactId>
            <version>${plugin.testrunner.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>javax.ws.rs</groupId>
            <artifactId>jsr311-api</artifactId>
            <version>1.1.1</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
            <version>2.2.2-atlassian-1</version>
            <scope>compile</scope>
        </dependency>



        <dependency>
            <groupId>com.atlassian.upm</groupId>
            <artifactId>licensing-api</artifactId>
            <version>2.21.4</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.atlassian.upm</groupId>
            <artifactId>upm-api</artifactId>
            <version>2.21</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>javax.servlet-api</artifactId>
            <version>3.0.1</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>com.atlassian.upm</groupId>
            <artifactId>plugin-license-storage-lib</artifactId>
            <version>${upm.license.compatibility.version}</version>
        </dependency>

        <dependency>
            <groupId>com.atlassian.plugins.rest</groupId>
            <artifactId>atlassian-rest-doclet</artifactId>
            <version>2.9.2</version>
            <scope>test</scope>
        </dependency>

        <!-- https://mvnrepository.com/artifact/com.alibaba/fastjson -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.76</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
            <version>2.2.2-atlassian-1</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>5.7.7</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.jetbrains</groupId>
            <artifactId>annotations</artifactId>
            <version>RELEASE</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt</artifactId>
            <version>0.9.1</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>RELEASE</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.atlassian.plugins.rest</groupId>
            <artifactId>atlassian-rest-common</artifactId>
            <version>2.9.2</version>
            <scope>provided</scope>
        </dependency>
        <!--  poi -->
<!--        <dependency>-->
<!--            <groupId>org.apache.poi</groupId>-->
<!--            <artifactId>poi</artifactId>-->
<!--            <version>4.1.2</version>-->
<!--        </dependency>-->

<!--        <dependency>-->
<!--            <groupId>org.apache.poi</groupId>-->
<!--            <artifactId>poi-ooxml</artifactId>-->
<!--            <version>4.1.2</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>org.apache.poi</groupId>-->
<!--            <artifactId>poi-scratchpad</artifactId>-->
<!--            <version>4.1.2</version>-->
<!--        </dependency>-->

        <!-- itextpdf -->
<!--        <dependency>-->
<!--            <groupId>com.itextpdf</groupId>-->
<!--            <artifactId>itextpdf</artifactId>-->
<!--            <version>5.4.3</version>-->
<!--        </dependency>-->

<!--        <dependency>-->
<!--            <groupId>com.itextpdf</groupId>-->
<!--            <artifactId>itext-asian</artifactId>-->
<!--            <version>5.2.0</version>-->
<!--        </dependency>-->
        <!--        <dependency>-->
        <!--            <groupId>com.vladsch.flexmark</groupId>-->
        <!--            <artifactId>flexmark-all</artifactId>-->
        <!--            <version>0.62.2</version>-->
        <!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.yahoo.platform.yui</groupId>-->
<!--            <artifactId>yuicompressor</artifactId>-->
<!--            <version>2.4.8</version>-->
<!--        </dependency>-->
        <dependency>
            <groupId>org.apache.pdfbox</groupId>
            <artifactId>pdfbox</artifactId>
            <version>2.0.27</version>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>com.atlassian.maven.plugins</groupId>
                <artifactId>jira-maven-plugin</artifactId>
                <version>${amps.version}</version>
                <extensions>true</extensions>

                <configuration>
                    <productVersion>${jira.version}</productVersion>
                    <productDataVersion>${jira.version}</productDataVersion>

                    <skipRestDocGeneration>true</skipRestDocGeneration>
                    <allowGoogleTracking>false</allowGoogleTracking>
                    <enableQuickReload>true</enableQuickReload>
                    <encoding>UTF-8</encoding>
                    <applications>
                        <application>
                            <applicationKey>jira-software</applicationKey>
                            <version>${jira.version}</version>
                        </application>
                    </applications>

                    <pluginArtifacts>
                        <pluginArtifact>
                            <groupId>com.atlassian.activeobjects</groupId>
                            <artifactId>activeobjects-plugin</artifactId>
                            <version>${ao.spi.version}</version>
                        </pluginArtifact>
                        <pluginArtifact>
                            <groupId>com.atlassian.activeobjects</groupId>
                            <artifactId>activeobjects-jira-spi</artifactId>
                            <version>${ao.version}</version>
                        </pluginArtifact>
                    </pluginArtifacts>

                </configuration>



            </plugin>



            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>

        </plugins>
    </build>
    <properties>
        <ao.version>1.2.2</ao.version>
        <ao.spi.version>1.2.2</ao.spi.version>

        <jira.version>8.15.0</jira.version>
        <jira-rest-plugin>8.15.0</jira-rest-plugin>

        <!--                <jira.version>7.13.0</jira.version>-->
        <!--                <jira-rest-plugin>7.13.0</jira-rest-plugin>-->

        <!--        <ao.version>1.1.5</ao.version>-->
        <!--        <ao.spi.version>1.1.5</ao.spi.version>-->
        <template.renderer.version>1.4.2.2</template.renderer.version>
        <fastjson.version>1.1.35</fastjson.version>

        <!--<jira.version>7.2.3</jira.version>-->
        <!--<jira.version>7.1.2</jira.version>-->
        <!--<jira.version>7.0.0</jira.version>-->

        <!--        <jira.version>8.0.1</jira.version>-->
        <!--        <amps.version>6.3.15</amps.version>-->

        <!--        <jira-rest-plugin>7.13.1</jira-rest-plugin>-->
        <amps.version>8.0.0</amps.version>


        <plugin.testrunner.version>1.2.3</plugin.testrunner.version>
        <atlassian.spring.scanner.version>1.2.13</atlassian.spring.scanner.version>
        <upm.license.compatibility.version>2.8</upm.license.compatibility.version>

        <!-- This key is used to keep the consistency between the key in atlassian-plugin.xml and the key to generate bundle. -->
        <atlassian.plugin.key>${project.groupId}.${project.artifactId}</atlassian.plugin.key>
        <!-- TestKit version 6.x for JIRA 6.x -->
        <testkit.version>6.3.11</testkit.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>
</project>
