package com.eve.actions;

import com.atlassian.jira.web.action.JiraWebActionSupport;
import com.atlassian.sal.api.websudo.WebSudoRequired;
import com.eve.services.ApprovalMsgOpenConfigService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2022/12/1
 */
@WebSudoRequired
public class ApprovalMsgOpenConfigAction extends JiraWebActionSupport {
    private String tabId;
    private String projectKey;
    private boolean openStatus;

    @Autowired
    private ApprovalMsgOpenConfigService approvalMsgOpenConfigService;

    public ApprovalMsgOpenConfigAction(ApprovalMsgOpenConfigService approvalMsgOpenConfigService) {
        this.approvalMsgOpenConfigService = approvalMsgOpenConfigService;
    }

    public String doMainpage() throws Exception {

        if (StringUtils.isEmpty(tabId)) {
            tabId = "1";
        }
        if ("1".equals(tabId)) {
            openStatus = approvalMsgOpenConfigService.queryProjectApprovalMsgStatus(projectKey);
        }

        if ("2".equals(tabId)) {
            //todo 2
        }

        if ("3".equals(tabId)) {
            //todo 3
        }
        return "mainpage";
    }

    public String getTabId() {
        return tabId;
    }

    public void setTabId(String tabId) {
        this.tabId = tabId;
    }

    public String getProjectKey() {
        return projectKey;
    }

    public void setProjectKey(String projectKey) {
        this.projectKey = projectKey;
    }

    public boolean isOpenStatus() {
        return openStatus;
    }

    public void setOpenStatus(boolean openStatus) {
        this.openStatus = openStatus;
    }
}
