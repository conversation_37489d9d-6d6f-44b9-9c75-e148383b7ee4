package com.eve.actions;

import com.atlassian.sal.api.websudo.WebSudoRequired;
import com.eve.beans.CategoryBean;
import com.eve.beans.ProjectBean;
import com.eve.services.ProjectService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

@WebSudoRequired
public class CatesProjectsAction extends JiraWebBaseAction{
    
    private List<CategoryBean> categoryBeans;
    private List<ProjectBean> projectBeans;
    private String cateId;
    
    @Autowired
    private ProjectService projectService;

    public CatesProjectsAction(ProjectService projectService) {
        this.projectService = projectService;
    }

    public List<CategoryBean> getCategoryBeans() {
        return categoryBeans;
    }

    public void setCategoryBeans(List<CategoryBean> categoryBeans) {
        this.categoryBeans = categoryBeans;
    }

    public List<ProjectBean> getProjectBeans() {
        return projectBeans;
    }

    public void setProjectBeans(List<ProjectBean> projectBeans) {
        this.projectBeans = projectBeans;
    }

    public String getCateId() {
        return cateId;
    }

    public void setCateId(String cateId) {
        this.cateId = cateId;
    }

    public String doMainpage() throws Exception {
        
        setCategoryBeans(projectService.getCategorys());
        
        if (0 == categoryBeans.size()) {
            cateId = "0";
        }
        
        if (StringUtils.isEmpty(cateId)) {
            cateId = String.valueOf(categoryBeans.get(0).getCategoryId());
        }
        
        setProjectBeans(projectService.getProdectsByCate(Long.valueOf(cateId)));
        
        return "mainpage";
    }
}
