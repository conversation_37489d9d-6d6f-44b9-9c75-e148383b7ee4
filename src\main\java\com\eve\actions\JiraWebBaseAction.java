package com.eve.actions;

import com.atlassian.jira.component.ComponentAccessor;
import com.atlassian.jira.config.properties.APKeys;
import com.atlassian.jira.issue.customfields.OperationContext;
import com.atlassian.jira.issue.operation.IssueOperation;
import com.atlassian.jira.issue.operation.IssueOperations;
import com.atlassian.jira.web.action.JiraWebActionSupport;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * Created with IntelliJ IDEA.
 * User: hehongqi
 * Date: 13-3-21
 * Time: 下午1:49
 * To change this template use File | Settings | File Templates.
 */
public class JiraWebBaseAction extends JiraWebActionSupport implements OperationContext {

    protected String currentUserShowName; //当前登录用户显示的名称
    protected String currentUser;         //当前登录用户名称的ID

    protected String baseURL;             //当前系统的根路径
    protected List<?> list;              //列表页面需要显示的数据信息
    protected String atl_token;
    protected String oper;

    private int pageSize = 20;
    private int currentPage = 1;


    @Override
    public Collection<String> getErrorMessages() {
        if (errorMessages == null) {
            errorMessages = new ArrayList<String>();
        }
        return super.errorMessages;
    }

    @Override
    public Map<String, String> getErrors() {
        return super.getErrors();
    }

    public String getCurrentUser() {
        if (this.getLoggedInUser() != null) {
            currentUser = this.getLoggedInUser().getName();
        }
        return currentUser;
    }

    public void setCurrentUser(String currentUser) {
        this.currentUser = currentUser;
    }

    public String getBaseURL() {
        //JiraUrl.constructBaseUrl(request);
        //baseURL = getHttpRequest().getContextPath();//ComponentAccessor.getApplicationProperties().getString("gears.baseurl");
        // baseURL = ComponentAccessor.getWebResourceUrlProvider().getBaseUrl();
        baseURL = ComponentAccessor.getApplicationProperties().getString(APKeys.JIRA_BASEURL);
        return baseURL;
    }

    public void setBaseURL(String baseURL) {
        this.baseURL = baseURL;
    }

    public String getCurrentUserShowName() {
        currentUserShowName = this.getLoggedInUser().getDisplayName();
        return currentUserShowName;
    }

    public void setCurrentUserShowName(String currentUserShowName) {
        this.currentUserShowName = currentUserShowName;
    }


    public List<?> getList() {
        return list;
    }

    public void setList(List<?> list) {
        this.list = list;
    }

    public String getOper() {
        return oper;
    }

    public void setOper(String oper) {
        this.oper = oper;
    }

    public String getAtl_token() {
        return atl_token;
    }

    public void setAtl_token(String atl_token) {
        this.atl_token = atl_token;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public int getCurrentPage() {
        return currentPage;
    }

    public void setCurrentPage(int currentPage) {
        this.currentPage = currentPage;
    }


    @Override
    public String execute() throws Exception {

        return super.execute();
    }


    @Override
    public Map<String, Object> getFieldValuesHolder() {
        return null;
    }

    @Override
    public IssueOperation getIssueOperation() {
        return IssueOperations.VIEW_ISSUE_OPERATION;
    }
}
