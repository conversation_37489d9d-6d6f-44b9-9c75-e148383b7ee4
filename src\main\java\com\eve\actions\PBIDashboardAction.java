package com.eve.actions;

import com.atlassian.jira.web.action.JiraWebActionSupport;
import com.atlassian.sal.api.websudo.WebSudoRequired;
import com.eve.beans.DeptProjectBean;
import com.eve.beans.DeptsBean;
import com.eve.services.DeptProjectService;
import com.eve.services.DeptsService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/6/14
 */
//@WebSudoRequired
public class PBIDashboardAction extends JiraWebBaseAction {

    private List<DeptProjectBean> deptProjectBeans;
    private List<DeptsBean> deptsBeans;
    private String deptName;
    private String nameMatch;
    private String tabId;
    private String projectKey;

    @Autowired
    private DeptProjectService deptProjectService;
    @Autowired
    private DeptsService deptsService;

    public PBIDashboardAction(DeptProjectService deptProjectService, DeptsService deptsService) {
        this.deptProjectService = deptProjectService;
        this.deptsService = deptsService;
    }

    public String doMainpage() throws Exception {

        if (StringUtils.isEmpty(tabId)) {
            tabId = "1";
        }
        if ("1".equals(tabId)) {
            deptProjectBeans = deptProjectService.list(deptName);
        }

        if ("2".equals(tabId)) {
            deptsBeans = deptsService.list(nameMatch);
        }
        return "mainPage";
    }

    public List<DeptsBean> getDeptsBeans() {
        return deptsBeans;
    }

    public void setDeptsBeans(List<DeptsBean> deptsBeans) {
        this.deptsBeans = deptsBeans;
    }

    public String getNameMatch() {
        return nameMatch;
    }

    public void setNameMatch(String nameMatch) {
        this.nameMatch = nameMatch;
    }

    public String getTabId() {
        return tabId;
    }

    public void setTabId(String tabId) {
        this.tabId = tabId;
    }

    public List<DeptProjectBean> getDeptProjectBeans() {
        return deptProjectBeans;
    }

    public void setDeptProjectBeans(List<DeptProjectBean> deptProjectBeans) {
        this.deptProjectBeans = deptProjectBeans;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public String getProjectKey() {
        return projectKey;
    }

    public void setProjectKey(String projectKey) {
        this.projectKey = projectKey;
    }
}
