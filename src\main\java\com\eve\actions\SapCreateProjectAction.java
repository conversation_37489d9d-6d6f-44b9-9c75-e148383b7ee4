package com.eve.actions;

import com.atlassian.sal.api.websudo.WebSudoRequired;
import com.eve.beans.CostCenterBean;
import com.eve.beans.InnerOrderTypeBean;
import com.eve.beans.ResultBean;
import com.eve.services.SapCreateProjectService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/8/22
 */
@WebSudoRequired
public class SapCreateProjectAction extends JiraWebBaseAction {
    private ResultBean resultBean = new ResultBean();
    private List<CostCenterBean> costCenterBeanList = new ArrayList<>();
    private List<InnerOrderTypeBean> innerOrderTypeBeanList = new ArrayList<>();
    private String orderTypeName;
    private String companyCode;
    private String costCenterName;
    private String tabId;

    @Autowired
    private SapCreateProjectService sapCreateProjectService;

    public SapCreateProjectAction(SapCreateProjectService sapCreateProjectService) {
        this.sapCreateProjectService = sapCreateProjectService;
    }

    public String doMainpage() throws Exception {

        if (StringUtils.isEmpty(tabId)) {
            tabId = "1";
        }
        if ("1".equals(tabId)) {
            innerOrderTypeBeanList = sapCreateProjectService.listOrderType(orderTypeName);
        }
        if ("2".equals(tabId)) {
//            resultBean = sapCreateProjectService.listCostCenter(0);
        }
        return "mainpage";
    }

    public ResultBean getResultBean() {
        return resultBean;
    }

    public void setResultBean(ResultBean resultBean) {
        this.resultBean = resultBean;
    }

    public String getOrderTypeName() {
        return orderTypeName;
    }

    public void setOrderTypeName(String orderTypeName) {
        this.orderTypeName = orderTypeName;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getCostCenterName() {
        return costCenterName;
    }

    public void setCostCenterName(String costCenterName) {
        this.costCenterName = costCenterName;
    }

    public List<CostCenterBean> getCostCenterBeanList() {
        return costCenterBeanList;
    }

    public void setCostCenterBeanList(List<CostCenterBean> costCenterBeanList) {
        this.costCenterBeanList = costCenterBeanList;
    }

    public List<InnerOrderTypeBean> getInnerOrderTypeBeanList() {
        return innerOrderTypeBeanList;
    }

    public void setInnerOrderTypeBeanList(List<InnerOrderTypeBean> innerOrderTypeBeanList) {
        this.innerOrderTypeBeanList = innerOrderTypeBeanList;
    }

    public String getTabId() {
        return tabId;
    }

    public void setTabId(String tabId) {
        this.tabId = tabId;
    }
}
