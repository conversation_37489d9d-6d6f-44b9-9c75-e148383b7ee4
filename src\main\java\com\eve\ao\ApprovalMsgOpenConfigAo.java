package com.eve.ao;

import net.java.ao.schema.StringLength;
import net.java.ao.schema.Table;

/**
 * <AUTHOR>
 * @date 2022/12/1
 */
@Table("approval_open")
public interface ApprovalMsgOpenConfigAo extends Entity {

    @StringLength(255)
    public String getProjectKey();
    @StringLength(255)
    public void setProjectKey(String projectKey);

    public boolean isOpenStatus();
    public void setOpenStatus(boolean openStatus);
}
