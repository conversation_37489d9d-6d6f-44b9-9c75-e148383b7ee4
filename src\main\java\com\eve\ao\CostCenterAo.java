package com.eve.ao;

import net.java.ao.schema.StringLength;
import net.java.ao.schema.Table;

/**
 * <AUTHOR>
 * @date 2022/5/31
 */
@Table("cost_center")
public interface CostCenterAo extends Entity {

    @StringLength(255)
    public String getBUKRS();

    public void setBUKRS(String BUKRS);

    @StringLength(255)
    public String getKOSTL();

    public void setKOSTL(String KOSTL);

    @StringLength(255)
    public String getKTEXT();

    public void setKTEXT(String KTEXT);

    @StringLength(255)
    public String getCBLX();

    public void setCBLX(String CBLX);

    @StringLength(255)
    public String getLRZXDM();

    public void setLRZXDM(String LRZXDM);

    @StringLength(255)
    public String getLRZXMC();

    public void setLRZXMC(String profitCenterName);

    public boolean isDisableFlag();

    public void setDisableFlag(boolean disableFlag);

    public boolean isActiveInProjectFlag();

    public void setActiveInProjectFlag(boolean activeInProjectFlag);
}
