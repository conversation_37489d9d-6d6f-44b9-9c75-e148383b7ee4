package com.eve.ao;

import net.java.ao.schema.Indexed;
import net.java.ao.schema.StringLength;
import net.java.ao.schema.Table;

@Table("dept_project")
public interface DeptProjectAo extends Entity{

    @StringLength(255)
    public String getDeptName();
    public void setDeptName(String deptName);

    @Indexed
    public Long getDeptId();
    public void setDeptId(Long deptId);

    @StringLength(15)
    public String getExecutor();
    public void setExecutor(String executor);

    @Indexed
    public Long getProjectId();
    public void setProjectId(Long projectId);

    @Indexed
    public Long getIssueTypeId();
    public void setIssueTypeId(Long issueTypeId);
}
