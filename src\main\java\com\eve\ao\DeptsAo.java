package com.eve.ao;

import net.java.ao.schema.Indexed;
import net.java.ao.schema.StringLength;

public interface DeptsAo extends Entity {

    @StringLength(15)
    public String getNameMatch();
    public void setNameMatch(String nameMatch);

    @Indexed
    public Long getDeptId();
    public void setDeptId(Long deptId);

    @Indexed
    public Long getSubDeptId();
    public void setSubDeptId(Long subDeptId);
}
