package com.eve.ao;

import net.java.ao.schema.StringLength;
import net.java.ao.schema.Table;

/**
 * <AUTHOR>
 * @date 2022/8/22
 */
@Table("inner_order_type")
public interface InnerOrderTypeAo extends Entity {

    @StringLength(255)
    public String getOrderTypeCode();
    public void setOrderTypeCode(String orderTypeCode);

    @StringLength(255)
    public String getOrderTypeName();
    public void setOrderTypeName(String orderTypeName);

    @StringLength(255)
    public String getCompanyCode();
    public void setCompanyCode(String companyCode);
}
