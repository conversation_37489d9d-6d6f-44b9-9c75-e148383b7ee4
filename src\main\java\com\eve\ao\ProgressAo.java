package com.eve.ao;

import net.java.ao.schema.Indexed;
import net.java.ao.schema.StringLength;
import net.java.ao.schema.Table;

import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @date 2022/4/6
 */

@Table("Progress")
public interface ProgressAo extends Entity{
    @Indexed
    public Long getIssueId();

    public void setIssueId(Long issueId);

    @StringLength(255)
    public String getStage();

    public void setStage(String stage);

    @StringLength(255)
    public String getNextStep();

    public void setNextStep(String nextStep);

    @StringLength(255)
    public String getRisk();

    public void setRisk(String risk);

    @StringLength(255)
    public String getStrategy();

    public void setStrategy(String strategy);

    @StringLength(255)
    public String getComment();

    public void setComment(String comment);

    public Timestamp getCreateDate();
    public void setCreateDate(Timestamp createDate);
}
