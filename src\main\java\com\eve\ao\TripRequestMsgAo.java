package com.eve.ao;

import net.java.ao.schema.Table;

import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @date 2022/9/15
 */
@Table("trip_request_msg")
public interface TripRequestMsgAo extends Entity {
    public String getDept();
    public void setDept(String dept);

    public String getApplicant();
    public void setApplicant(String applicant);

    public String getBoss();
    public void setBoss(String boss);

    public String getTripReason();
    public void setTripReason(String tripReason);

    public String getTripAddress();
    public void setTripAddress(String tripAddress);

    public String getTripStartDate();
    public void setTripStartDate(String tripStartDate);

    public String getTripEndDate();
    public void setTripEndDate(String tripEndDate);

    public String getTripDays();
    public void setTripDays(String tripDays);

    public String getIsOnline();
    public void setIsOnline(String isOnline);

    public String getIssueId();
    public void setIssueId(String issueId);

    public String getBusinessCode();
    public void setBusinessCode(String businessCode);

    public String getCreateResult();
    public void setCreateResult(String createResult);

    public Timestamp getReceiveDate();
    public void setReceiveDate(Timestamp receiveDate);
}
