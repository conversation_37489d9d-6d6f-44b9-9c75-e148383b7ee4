package com.eve.beans;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/12/2
 */
@XmlRootElement
public class ApprovalFlowProgressConfigBean {
    @XmlElement
    private Long id;
    @XmlElement
    private String order;
    @XmlElement
    private String projectKey;
    @XmlElement
    private String projectId;
    @XmlElement
    private String workflowId;
    @XmlElement
    private String statusName;
    @XmlElement
    private String statusId;
    @XmlElement
    private List<ActionBean> actionBeanList;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getOrder() {
        return order;
    }

    public void setOrder(String order) {
        this.order = order;
    }

    public String getProjectKey() {
        return projectKey;
    }

    public void setProjectKey(String projectKey) {
        this.projectKey = projectKey;
    }

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public String getStatusName() {
        return statusName;
    }

    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }

    public String getStatusId() {
        return statusId;
    }

    public void setStatusId(String statusId) {
        this.statusId = statusId;
    }
}