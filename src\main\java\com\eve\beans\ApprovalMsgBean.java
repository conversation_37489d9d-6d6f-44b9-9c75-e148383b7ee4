package com.eve.beans;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * <AUTHOR>
 * @date 2022/12/1
 */
@XmlRootElement
public class ApprovalMsgBean {
    @XmlElement
    private Long id;
    @XmlElement
    private Long no;
    @XmlElement
    private String statusName;
    @XmlElement
    private String operator;
    @XmlElement
    private String opera;
    //yyyy-MM-dd
    @XmlElement
    private String operaTime;

    public ApprovalMsgBean() {
    }

    public ApprovalMsgBean(Long no, String statusName, String operator, String opera, String operaTime) {
        this.no = no;
        this.statusName = statusName;
        this.operator = operator;
        this.opera = opera;
        this.operaTime = operaTime;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getNo() {
        return no;
    }

    public void setNo(Long no) {
        this.no = no;
    }

    public String getStatusName() {
        return statusName;
    }

    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getOperaTime() {
        return operaTime;
    }

    public void setOperaTime(String operaTime) {
        this.operaTime = operaTime;
    }

    public String getOpera() {
        return opera;
    }

    public void setOpera(String opera) {
        this.opera = opera;
    }
}
