package com.eve.beans;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2021/10/19 16:04
 */
@XmlRootElement
public class AttachmentBean implements Serializable {

    @XmlElement
    private Long attachmentId;
    @XmlElement
    private String attachmentName;
    @XmlElement
    private String attachmentType;
    @XmlElement
    private String attachmentUrl;
    @XmlElement
    private String attachmentAuthor;
    @XmlElement
    private String attachmentAuthorAccount;
    @XmlElement
    private String attachmentCate;
    @XmlElement
    private String attachmentPath;
    @XmlElement
    private String attachmentSize;
    @XmlElement
    private String createDate;

    public AttachmentBean() {
    }

    public AttachmentBean(Long attachmentId, String attachmentName, String attachmentType, String attachmentUrl) {
        this.attachmentId = attachmentId;
        this.attachmentName = attachmentName;
        this.attachmentType = attachmentType;
        this.attachmentUrl = attachmentUrl;
    }

    private AttachmentBean(Builder builder) {
        setAttachmentId(builder.attachmentId);
        setAttachmentName(builder.attachmentName);
        setAttachmentType(builder.attachmentType);
        setAttachmentUrl(builder.attachmentUrl);
        setAttachmentAuthor(builder.attachmentAuthor);
        setAttachmentAuthorAccount(builder.attachmentAuthorAccount);
        setAttachmentCate(builder.attachmentCate);
        setAttachmentPath(builder.attachmentPath);
        setAttachmentSize(builder.attachmentSize);
        setCreateDate(builder.createDate);
    }

    public String getAttachmentPath() {
        return attachmentPath;
    }

    public void setAttachmentPath(String attachmentPath) {
        this.attachmentPath = attachmentPath;
    }

    public String getAttachmentCate() {
        return attachmentCate;
    }

    public void setAttachmentCate(String attachmentCate) {
        this.attachmentCate = attachmentCate;
    }

    public String getAttachmentSize() {
        return attachmentSize;
    }

    public void setAttachmentSize(String attachmentSize) {
        this.attachmentSize = attachmentSize;
    }

    public String getAttachmentAuthor() {
        return attachmentAuthor;
    }

    public void setAttachmentAuthor(String attachmentAuthor) {
        this.attachmentAuthor = attachmentAuthor;
    }

    public String getAttachmentAuthorAccount() {
        return attachmentAuthorAccount;
    }

    public void setAttachmentAuthorAccount(String attachmentAuthorAccount) {
        this.attachmentAuthorAccount = attachmentAuthorAccount;
    }

    public Long getAttachmentId() {
        return attachmentId;
    }

    public void setAttachmentId(Long attachmentId) {
        this.attachmentId = attachmentId;
    }

    public String getAttachmentName() {
        return attachmentName;
    }

    public void setAttachmentName(String attachmentName) {
        this.attachmentName = attachmentName;
    }

    public String getAttachmentType() {
        return attachmentType;
    }

    public void setAttachmentType(String attachmentType) {
        this.attachmentType = attachmentType;
    }

    public String getAttachmentUrl() {
        return attachmentUrl;
    }

    public void setAttachmentUrl(String attachmentUrl) {
        this.attachmentUrl = attachmentUrl;
    }

    public String getCreateDate() {
        return createDate;
    }

    public void setCreateDate(String createDate) {
        this.createDate = createDate;
    }

    @Override
    public String toString() {
        return "AttachmentBean{" +
                "attachmentId=" + attachmentId +
                ", attachmentName='" + attachmentName + '\'' +
                ", attachmentType='" + attachmentType + '\'' +
                ", attachmentUrl='" + attachmentUrl + '\'' +
                ", createDate='" + createDate + '\'' +
                '}';
    }

    public static final class Builder {
        private Long attachmentId;
        private String attachmentName;
        private String attachmentType;
        private String attachmentUrl;
        private String attachmentAuthor;
        private String attachmentAuthorAccount;
        private String attachmentCate;
        private String attachmentPath;
        private String attachmentSize;
        private String createDate;

        public Builder() {
        }

        public Builder attachmentId(Long attachmentId) {
            this.attachmentId = attachmentId;
            return this;
        }

        public Builder attachmentName(String attachmentName) {
            this.attachmentName = attachmentName;
            return this;
        }

        public Builder attachmentType(String attachmentType) {
            this.attachmentType = attachmentType;
            return this;
        }

        public Builder attachmentUrl(String attachmentUrl) {
            this.attachmentUrl = attachmentUrl;
            return this;
        }

        public Builder attachmentAuthor(String attachmentAuthor) {
            this.attachmentAuthor = attachmentAuthor;
            return this;
        }

        public Builder attachmentAuthorAccount(String attachmentAuthorAccount) {
            this.attachmentAuthorAccount = attachmentAuthorAccount;
            return this;
        }

        public Builder attachmentCate(String attachmentCate) {
            this.attachmentCate = attachmentCate;
            return this;
        }

        public Builder attachmentPath(String attachmentPath) {
            this.attachmentPath = attachmentPath;
            return this;
        }

        public Builder attachmentSize(String attachmentSize) {
            this.attachmentSize = attachmentSize;
            return this;
        }

        public Builder createDate(String createDate) {
            this.createDate = createDate;
            return this;
        }

        public AttachmentBean build() {
            return new AttachmentBean(this);
        }
    }
}
