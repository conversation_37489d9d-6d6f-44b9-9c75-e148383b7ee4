package com.eve.beans;

import javax.xml.bind.annotation.XmlElement;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/2/22
 */
public class AttachmentCateBean {
    @XmlElement
    private Long cateId;
    @XmlElement
    private String cateName;
    @XmlElement
    private String hideEmpty;
    @XmlElement
    private String allowWrite;
    @XmlElement
    private List<AttachmentBean> attachmentList;

    private AttachmentCateBean(Builder builder) {
        setCateId(builder.cateId);
        setCateName(builder.cateName);
        setHideEmpty(builder.hideEmpty);
        setAllowWrite(builder.allowWrite);
        setAttachmentList(builder.attachmentList);
    }

    public Long getCateId() {
        return cateId;
    }

    public void setCateId(Long cateId) {
        this.cateId = cateId;
    }

    public String getCateName() {
        return cateName;
    }

    public void setCateName(String cateName) {
        this.cateName = cateName;
    }

    public String getHideEmpty() {
        return hideEmpty;
    }

    public void setHideEmpty(String hideEmpty) {
        this.hideEmpty = hideEmpty;
    }

    public String getAllowWrite() {
        return allowWrite;
    }

    public void setAllowWrite(String allowWrite) {
        this.allowWrite = allowWrite;
    }

    public List<AttachmentBean> getAttachmentList() {
        return attachmentList;
    }

    public void setAttachmentList(List<AttachmentBean> attachmentList) {
        this.attachmentList = attachmentList;
    }

    public static final class Builder {
        private Long cateId;
        private String cateName;
        private String hideEmpty;
        private String allowWrite;
        private List<AttachmentBean> attachmentList;

        public Builder() {
        }

        public Builder cateId(Long cateId) {
            this.cateId = cateId;
            return this;
        }

        public Builder cateName(String cateName) {
            this.cateName = cateName;
            return this;
        }

        public Builder hideEmpty(String hideEmpty) {
            this.hideEmpty = hideEmpty;
            return this;
        }

        public Builder allowWrite(String allowWrite) {
            this.allowWrite = allowWrite;
            return this;
        }

        public Builder attachmentList(List<AttachmentBean> attachmentList) {
            this.attachmentList = attachmentList;
            return this;
        }

        public AttachmentCateBean build() {
            return new AttachmentCateBean(this);
        }
    }
}
