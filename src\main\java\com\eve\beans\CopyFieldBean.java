package com.eve.beans;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/5/9
 */
@XmlRootElement
public class CopyFieldBean implements Serializable {
    @XmlElement
    private String id;
    @XmlElement
    private String name;

    public CopyFieldBean() {
    }

    public CopyFieldBean(String id, String name) {
        this.id = id;
        this.name = name;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
