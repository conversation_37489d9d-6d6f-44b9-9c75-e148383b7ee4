package com.eve.beans;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/5/30
 */
@XmlRootElement
public class CostCenterBean implements Serializable {
    @XmlElement
    private Long id;
    @XmlElement
    private String BUKRS;//公司代码
    @XmlElement
    private String KOSTL;//成本中心代码
    @XmlElement
    private String KTEXT;//成本中心名称
    @XmlElement
    private String CBLX;//成本
    @XmlElement
    private String LRZXDM;//利润中心代码
    @XmlElement
    private String LRZXMC;//利润中心名称
    @XmlElement
    private boolean disableFlag;//禁用标志位
    @XmlElement
    private boolean activeInProjectFlag;//在项目管理中启用

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getBUKRS() {
        return BUKRS;
    }

    public void setBUKRS(String BUKRS) {
        this.BUKRS = BUKRS;
    }

    public String getKOSTL() {
        return KOSTL;
    }

    public void setKOSTL(String KOSTL) {
        this.KOSTL = KOSTL;
    }

    public String getKTEXT() {
        return KTEXT;
    }

    public void setKTEXT(String KTEXT) {
        this.KTEXT = KTEXT;
    }

    public String getCBLX() {
        return CBLX;
    }

    public void setCBLX(String CBLX) {
        this.CBLX = CBLX;
    }

    public String getLRZXDM() {
        return LRZXDM;
    }

    public void setLRZXDM(String LRZXDM) {
        this.LRZXDM = LRZXDM;
    }

    public String getLRZXMC() {
        return LRZXMC;
    }

    public void setLRZXMC(String LRZXMC) {
        this.LRZXMC = LRZXMC;
    }

    public boolean isDisableFlag() {
        return disableFlag;
    }

    public void setDisableFlag(boolean disableFlag) {
        this.disableFlag = disableFlag;
    }

    public boolean isActiveInProjectFlag() {
        return activeInProjectFlag;
    }

    public void setActiveInProjectFlag(boolean activeInProjectFlag) {
        this.activeInProjectFlag = activeInProjectFlag;
    }

    @Override
    public String toString() {
        return "CostCenterBean{" +
                "BUKRS='" + BUKRS + '\'' +
                ", KOSTL='" + KOSTL + '\'' +
                ", KTEXT='" + KTEXT + '\'' +
                ", CBLX='" + CBLX + '\'' +
                ", LRZXDM='" + LRZXDM + '\'' +
                ", LRZXMC='" + LRZXMC + '\'' +
                '}';
    }
}
