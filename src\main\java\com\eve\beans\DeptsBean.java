package com.eve.beans;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

@XmlRootElement
public class DeptsBean {
    @XmlElement
    private Long id;
    @XmlElement
    private Long deptId;
    @XmlElement
    private String deptName;
    @XmlElement
    private Long subDeptId;
    @XmlElement
    private String subDeptName;
    @XmlElement
    private String nameMatch;

    public DeptsBean(){
        
    }

    public DeptsBean(Long id, Long deptId, String deptName, Long subDeptId, String subDeptName, String nameMatch) {
        this.id = id;
        this.deptId = deptId;
        this.deptName = deptName;
        this.subDeptId = subDeptId;
        this.subDeptName = subDeptName;
        this.nameMatch = nameMatch;
    }

    public Long getSubDeptId() {
        return subDeptId;
    }

    public void setSubDeptId(Long subDeptId) {
        this.subDeptId = subDeptId;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public String getSubDeptName() {
        return subDeptName;
    }

    public void setSubDeptName(String subDeptName) {
        this.subDeptName = subDeptName;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getNameMatch() {
        return nameMatch;
    }

    public void setNameMatch(String nameMatch) {
        this.nameMatch = nameMatch;
    }

    public Long getDeptId() {
        return deptId;
    }

    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }
}
