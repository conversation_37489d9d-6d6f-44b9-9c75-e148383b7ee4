package com.eve.beans;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/12/1
 */
@XmlRootElement
public class FailureDescriptionPictureBean implements Serializable {
    @XmlElement
    private String uid;
    @XmlElement
    private Long id;
    @XmlElement
    private String name;
    @XmlElement
    private Long size;
    @XmlElement
    private String type;
    @XmlElement
    private String url;
    @XmlElement
    private String status;

    public FailureDescriptionPictureBean() {
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getSize() {
        return size;
    }

    public void setSize(Long size) {
        this.size = size;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}
