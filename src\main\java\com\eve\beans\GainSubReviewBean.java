package com.eve.beans;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * <AUTHOR>
 * @date 2022/12/1
 */
@XmlRootElement
public class GainSubReviewBean {
    @XmlElement
    private Long issueId;
    @XmlElement
    private Long parentId;
    @XmlElement
    private String issueKey;
    @XmlElement
    private String reviewUserName;
    @XmlElement
    private String reviewScore;
    @XmlElement
    private String reviewOpinion;

    public GainSubReviewBean() {
    }

    public GainSubReviewBean(Long issueId, Long parentId, String issueKey, String reviewUserName, String reviewScore, String reviewOpinion) {
        this.issueId = issueId;
        this.parentId = parentId;
        this.issueKey = issueKey;
        this.reviewUserName = reviewUserName;
        this.reviewScore = reviewScore;
        this.reviewOpinion = reviewOpinion;
    }

    private GainSubReviewBean(Builder builder) {
        setIssueId(builder.issueId);
        setParentId(builder.parentId);
        setIssueKey(builder.issueKey);
        setReviewUserName(builder.reviewUserName);
        setReviewScore(builder.reviewScore);
        setReviewOpinion(builder.reviewOpinion);
    }

    public Long getIssueId() {
        return issueId;
    }

    public void setIssueId(Long issueId) {
        this.issueId = issueId;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public String getIssueKey() {
        return issueKey;
    }

    public void setIssueKey(String issueKey) {
        this.issueKey = issueKey;
    }

    public String getReviewUserName() {
        return reviewUserName;
    }

    public void setReviewUserName(String reviewUserName) {
        this.reviewUserName = reviewUserName;
    }

    public String getReviewScore() {
        return reviewScore;
    }

    public void setReviewScore(String reviewScore) {
        this.reviewScore = reviewScore;
    }

    public String getReviewOpinion() {
        return reviewOpinion;
    }

    public void setReviewOpinion(String reviewOpinion) {
        this.reviewOpinion = reviewOpinion;
    }

    public static final class Builder {
        private Long issueId;
        private Long parentId;
        private String issueKey;
        private String reviewUserName;
        private String reviewScore;
        private String reviewOpinion;

        public Builder() {
        }

        public Builder issueId(Long issueId) {
            this.issueId = issueId;
            return this;
        }

        public Builder parentId(Long parentId) {
            this.parentId = parentId;
            return this;
        }

        public Builder issueKey(String issueKey) {
            this.issueKey = issueKey;
            return this;
        }

        public Builder reviewUserName(String reviewUserName) {
            this.reviewUserName = reviewUserName;
            return this;
        }

        public Builder reviewScore(String reviewScore) {
            this.reviewScore = reviewScore;
            return this;
        }

        public Builder reviewOpinion(String reviewOpinion) {
            this.reviewOpinion = reviewOpinion;
            return this;
        }

        public GainSubReviewBean build() {
            return new GainSubReviewBean(this);
        }
    }
}
