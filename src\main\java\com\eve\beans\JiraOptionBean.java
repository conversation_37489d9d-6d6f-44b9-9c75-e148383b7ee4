package com.eve.beans;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * <AUTHOR>
 * @date 2022/11/16
 */
@XmlRootElement
public class JiraOptionBean {
    @XmlElement
    private Long id;
    @XmlElement
    private Long pid;
    @XmlElement
    private String value;
    @XmlElement
    private String projectCount;
    @XmlElement
    private Long sequence;//选项顺序

    public JiraOptionBean() {
    }

    public JiraOptionBean(Long id, Long pid, String value, String projectCount, Long sequence) {
        this.id = id;
        this.pid = pid;
        this.value = value;
        this.projectCount = projectCount;
        this.sequence = sequence;
    }

    private JiraOptionBean(Builder builder) {
        setId(builder.id);
        setPid(builder.pid);
        setValue(builder.value);
        setProjectCount(builder.projectCount);
        setSequence(builder.sequence);
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getPid() {
        return pid;
    }

    public void setPid(Long pid) {
        this.pid = pid;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getProjectCount() {
        return projectCount;
    }

    public void setProjectCount(String projectCount) {
        this.projectCount = projectCount;
    }

    public Long getSequence() {
        return sequence;
    }

    public void setSequence(Long sequence) {
        this.sequence = sequence;
    }

    public static final class Builder {
        private Long id;
        private Long pid;
        private String value;
        private String projectCount;
        private Long sequence;

        public Builder() {
        }

        public Builder id(Long id) {
            this.id = id;
            return this;
        }

        public Builder pid(Long pid) {
            this.pid = pid;
            return this;
        }

        public Builder value(String value) {
            this.value = value;
            return this;
        }

        public Builder projectCount(String projectCount) {
            this.projectCount = projectCount;
            return this;
        }

        public Builder sequence(Long sequence) {
            this.sequence = sequence;
            return this;
        }

        public JiraOptionBean build() {
            return new JiraOptionBean(this);
        }
    }
}
