package com.eve.beans;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;

/**
 * @<PERSON> wanghao
 * @Date 2021/9/7 15:32
 */
@XmlRootElement
public class OptionBean implements Serializable {

    /**
     * 选项名称
     */
    @XmlElement
    private String name;

    /**
     * 选项id
     */
    @XmlElement
    private Long id;

    /**
     * 选项类型
     */
    @XmlElement
    private String type;

    @XmlElement
    private String value;

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
}
