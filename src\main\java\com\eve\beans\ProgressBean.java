package com.eve.beans;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @date 2022/4/6
 */
@XmlRootElement
public class ProgressBean implements Serializable {
    @XmlElement
    private Long id;
    @XmlElement
    private Long issueId;
    @XmlElement
    private String stage;
    @XmlElement
    private String nextStep;
    @XmlElement
    private String risk;
    @XmlElement
    private String strategy;
    @XmlElement
    private String comment;

    @XmlElement
    private Timestamp createDate;

    public ProgressBean() {
    }

    public ProgressBean(Long id, Long issueId, String stage, String nextStep, String risk, String strategy, String comment, Timestamp createDate) {
        this.id = id;
        this.issueId = issueId;
        this.stage = stage;
        this.nextStep = nextStep;
        this.risk = risk;
        this.strategy = strategy;
        this.comment = comment;
        this.createDate = createDate;
    }

    public Timestamp getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Timestamp createDate) {
        this.createDate = createDate;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getIssueId() {
        return issueId;
    }

    public void setIssueId(Long issueId) {
        this.issueId = issueId;
    }

    public String getStage() {
        return stage;
    }

    public void setStage(String stage) {
        this.stage = stage;
    }

    public String getNextStep() {
        return nextStep;
    }

    public void setNextStep(String nextStep) {
        this.nextStep = nextStep;
    }

    public String getRisk() {
        return risk;
    }

    public void setRisk(String risk) {
        this.risk = risk;
    }

    public String getStrategy() {
        return strategy;
    }

    public void setStrategy(String strategy) {
        this.strategy = strategy;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }
}
