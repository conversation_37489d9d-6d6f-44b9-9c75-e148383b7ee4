package com.eve.beans;


import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

@XmlRootElement
public class ProjectBean {
    @XmlElement
    private Long projectId;
    @XmlElement
    private String projectName;
    @XmlElement
    private String projectKey;
    @XmlElement
    private String projectUrl;

    public ProjectBean(Long projectId, String projectName, String projectKey, String projectUrl) {
        this.projectId = projectId;
        this.projectName = projectName;
        this.projectKey = projectKey;
        this.projectUrl = projectUrl;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getProjectKey() {
        return projectKey;
    }

    public void setProjectKey(String projectKey) {
        this.projectKey = projectKey;
    }

    public String getProjectUrl() {
        return projectUrl;
    }

    public void setProjectUrl(String projectUrl) {
        this.projectUrl = projectUrl;
    }
}
