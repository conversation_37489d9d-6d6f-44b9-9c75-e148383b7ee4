package com.eve.beans;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * <AUTHOR>
 * @date 2022/10/24
 */
@XmlRootElement
public class ProjectQueryParamBean {
    @XmlElement
    private Long issueId;
    @XmlElement
    private String userName;
    /**
     * projectType topic-课题项目 platform-平台项目
     */
    @XmlElement
    private String projectType;

    public Long getIssueId() {
        return issueId;
    }

    public void setIssueId(Long issueId) {
        this.issueId = issueId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getProjectType() {
        return projectType;
    }

    public void setProjectType(String projectType) {
        this.projectType = projectType;
    }
}
