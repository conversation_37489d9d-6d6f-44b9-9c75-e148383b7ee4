package com.eve.beans;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/24
 */
@XmlRootElement
public class ProjectQueryResponseBean implements Serializable {
    @XmlElement
    private Long issueId;
    @XmlElement
    private String issueKey;

    @XmlElement
    private List<Long> attachments;
    @XmlElement
    private String status;
    @XmlElement
    private String startDate;
    @XmlElement
    private String endDate;
    @XmlElement
    private String description;
    @XmlElement
    private String summary;

    @XmlElement
    private String code;
    @XmlElement
    private String name;
    @XmlElement
    private String cate;

    @XmlElement
    private String backGround;
    @XmlElement
    private String purpose;
    @XmlElement
    private String target;


    //产品
    @XmlElement
    private String stage;
    @XmlElement
    private String model;

    //出差
    @XmlElement
    private String reason;
    @XmlElement
    private String addr;
    @XmlElement
    private String days;
    @XmlElement
    private String leaderScore;
    @XmlElement
    private String leaderEvaluation;
    @XmlElement
    private String manageScore;

    //成果
    @XmlElement
    private List<String> modality;
    @XmlElement
    private String content;
    @XmlElement
    private String directorScore;
    @XmlElement
    private String directorEvaluation;
    @XmlElement
    private String presidentScore;
    @XmlElement
    private String presidentEvaluation;
    @XmlElement
    private String averageScore;
    @XmlElement
    private List<String> mainCompletePerson;


    public ProjectQueryResponseBean() {
    }

    /**
     * 项目管理构造函数
     * @param issueId   问题ID
     * @param issueKey  问题关键字
     * @param code  代号
     * @param name  名称
     * @param backGround    背景
     * @param purpose   目的
     * @param target    目标
     * @param attachments   附件ID列表
     * @param status    状态
     * @param startDate 立项日期
     * @param endDate   结束日期
     */
    public ProjectQueryResponseBean(Long issueId, String issueKey, String code, String name, String backGround, String purpose, String target, List<Long> attachments, String status, String startDate, String endDate,String summary) {
        this.issueId = issueId;
        this.issueKey = issueKey;
        this.code = code;
        this.name = name;
        this.backGround = backGround;
        this.purpose = purpose;
        this.target = target;
        this.attachments = attachments;
        this.status = status;
        this.startDate = startDate;
        this.endDate = endDate;
        this.summary = summary;
    }

    /**
     * 实验报告构造函数
     * @param issueId   问题ID
     * @param issueKey  问题关键字
     * @param code  单号
     * @param name  名称
     * @param purpose   目的
     * @param attachments   附件ID列表
     * @param status    状态
     * @param startDate 立项日期
     * @param endDate   结束日期
     */
    public ProjectQueryResponseBean(Long issueId, String issueKey, String code, String name, String purpose, List<Long> attachments, String status, String startDate, String endDate,String summary) {
        this.issueId = issueId;
        this.issueKey = issueKey;
        this.code = code;
        this.name = name;
        this.purpose = purpose;
        this.attachments = attachments;
        this.status = status;
        this.startDate = startDate;
        this.endDate = endDate;
        this.summary = summary;
    }

    private ProjectQueryResponseBean(Builder builder) {
        setIssueId(builder.issueId);
        setIssueKey(builder.issueKey);
        setAttachments(builder.attachments);
        setStatus(builder.status);
        setStartDate(builder.startDate);
        setEndDate(builder.endDate);
        setDescription(builder.description);
        setSummary(builder.summary);
        setCode(builder.code);
        setName(builder.name);
        setCate(builder.cate);
        setBackGround(builder.backGround);
        setPurpose(builder.purpose);
        setTarget(builder.target);
        setStage(builder.stage);
        setModel(builder.model);
        setReason(builder.reason);
        setAddr(builder.addr);
        setDays(builder.days);
        setLeaderScore(builder.leaderScore);
        setLeaderEvaluation(builder.leaderEvaluation);
        setManageScore(builder.manageScore);
        setModality(builder.modality);
        setContent(builder.content);
        setDirectorScore(builder.directorScore);
        setDirectorEvaluation(builder.directorEvaluation);
        setPresidentScore(builder.presidentScore);
        setPresidentEvaluation(builder.presidentEvaluation);
        setAverageScore(builder.averageScore);
        setMainCompletePerson(builder.mainCompletePerson);
    }

    public Long getIssueId() {
        return issueId;
    }

    public void setIssueId(Long issueId) {
        this.issueId = issueId;
    }

    public String getIssueKey() {
        return issueKey;
    }

    public void setIssueKey(String issueKey) {
        this.issueKey = issueKey;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getBackGround() {
        return backGround;
    }

    public void setBackGround(String backGround) {
        this.backGround = backGround;
    }

    public String getPurpose() {
        return purpose;
    }

    public void setPurpose(String purpose) {
        this.purpose = purpose;
    }

    public String getTarget() {
        return target;
    }

    public void setTarget(String target) {
        this.target = target;
    }

    public List<Long> getAttachments() {
        return attachments;
    }

    public void setAttachments(List<Long> attachments) {
        this.attachments = attachments;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public String getSummary() {
        return summary;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }

    public String getStage() {
        return stage;
    }

    public void setStage(String stage) {
        this.stage = stage;
    }

    public String getCate() {
        return cate;
    }

    public void setCate(String cate) {
        this.cate = cate;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getAddr() {
        return addr;
    }

    public void setAddr(String addr) {
        this.addr = addr;
    }

    public String getDays() {
        return days;
    }

    public void setDays(String days) {
        this.days = days;
    }

    public String getLeaderScore() {
        return leaderScore;
    }

    public void setLeaderScore(String leaderScore) {
        this.leaderScore = leaderScore;
    }

    public String getManageScore() {
        return manageScore;
    }

    public void setManageScore(String manageScore) {
        this.manageScore = manageScore;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public List<String> getModality() {
        return modality;
    }

    public void setModality(List<String> modality) {
        this.modality = modality;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getDirectorScore() {
        return directorScore;
    }

    public void setDirectorScore(String directorScore) {
        this.directorScore = directorScore;
    }

    public String getDirectorEvaluation() {
        return directorEvaluation;
    }

    public void setDirectorEvaluation(String directorEvaluation) {
        this.directorEvaluation = directorEvaluation;
    }

    public String getPresidentScore() {
        return presidentScore;
    }

    public void setPresidentScore(String presidentScore) {
        this.presidentScore = presidentScore;
    }

    public String getPresidentEvaluation() {
        return presidentEvaluation;
    }

    public void setPresidentEvaluation(String presidentEvaluation) {
        this.presidentEvaluation = presidentEvaluation;
    }

    public String getAverageScore() {
        return averageScore;
    }

    public void setAverageScore(String averageScore) {
        this.averageScore = averageScore;
    }

    public String getLeaderEvaluation() {
        return leaderEvaluation;
    }

    public void setLeaderEvaluation(String leaderEvaluation) {
        this.leaderEvaluation = leaderEvaluation;
    }

    public List<String> getMainCompletePerson() {
        return mainCompletePerson;
    }

    public void setMainCompletePerson(List<String> mainCompletePerson) {
        this.mainCompletePerson = mainCompletePerson;
    }

    public static final class Builder {
        private Long issueId;
        private String issueKey;
        private List<Long> attachments;
        private String status;
        private String startDate;
        private String endDate;
        private String description;
        private String summary;
        private String code;
        private String name;
        private String backGround;
        private String purpose;
        private String target;
        private String stage;
        private String cate;
        private String model;
        private String reason;
        private String addr;
        private String days;
        private String leaderScore;
        private String leaderEvaluation;
        private String manageScore;
        private List<String> modality;
        private String content;
        private String directorScore;
        private String directorEvaluation;
        private String presidentScore;
        private String presidentEvaluation;
        private String averageScore;
        private List<String> mainCompletePerson;

        public Builder() {
        }

        public Builder issueId(Long issueId) {
            this.issueId = issueId;
            return this;
        }

        public Builder issueKey(String issueKey) {
            this.issueKey = issueKey;
            return this;
        }

        public Builder attachments(List<Long> attachments) {
            this.attachments = attachments;
            return this;
        }

        public Builder status(String status) {
            this.status = status;
            return this;
        }

        public Builder startDate(String startDate) {
            this.startDate = startDate;
            return this;
        }

        public Builder endDate(String endDate) {
            this.endDate = endDate;
            return this;
        }

        public Builder description(String description) {
            this.description = description;
            return this;
        }

        public Builder summary(String summary) {
            this.summary = summary;
            return this;
        }

        public Builder code(String code) {
            this.code = code;
            return this;
        }

        public Builder name(String name) {
            this.name = name;
            return this;
        }

        public Builder backGround(String backGround) {
            this.backGround = backGround;
            return this;
        }

        public Builder purpose(String purpose) {
            this.purpose = purpose;
            return this;
        }

        public Builder target(String target) {
            this.target = target;
            return this;
        }

        public Builder stage(String stage) {
            this.stage = stage;
            return this;
        }

        public Builder cate(String cate) {
            this.cate = cate;
            return this;
        }

        public Builder model(String model) {
            this.model = model;
            return this;
        }

        public Builder reason(String reason) {
            this.reason = reason;
            return this;
        }

        public Builder addr(String addr) {
            this.addr = addr;
            return this;
        }

        public Builder days(String days) {
            this.days = days;
            return this;
        }

        public Builder leaderScore(String leaderScore) {
            this.leaderScore = leaderScore;
            return this;
        }

        public Builder leaderEvaluation(String leaderEvaluation) {
            this.leaderEvaluation = leaderEvaluation;
            return this;
        }

        public Builder manageScore(String manageScore) {
            this.manageScore = manageScore;
            return this;
        }

        public Builder modality(List<String> modality) {
            this.modality = modality;
            return this;
        }

        public Builder content(String content) {
            this.content = content;
            return this;
        }

        public Builder directorScore(String directorScore) {
            this.directorScore = directorScore;
            return this;
        }

        public Builder directorEvaluation(String directorEvaluation) {
            this.directorEvaluation = directorEvaluation;
            return this;
        }

        public Builder presidentScore(String presidentScore) {
            this.presidentScore = presidentScore;
            return this;
        }

        public Builder presidentEvaluation(String presidentEvaluation) {
            this.presidentEvaluation = presidentEvaluation;
            return this;
        }

        public Builder averageScore(String averageScore) {
            this.averageScore = averageScore;
            return this;
        }

        public Builder mainCompletePerson(List<String> mainCompletePerson) {
            this.mainCompletePerson = mainCompletePerson;
            return this;
        }

        public ProjectQueryResponseBean build() {
            return new ProjectQueryResponseBean(this);
        }
    }
}
