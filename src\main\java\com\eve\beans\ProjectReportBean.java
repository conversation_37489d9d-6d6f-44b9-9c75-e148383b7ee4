package com.eve.beans;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/11/16
 */
@XmlRootElement
public class ProjectReportBean {
    @XmlElement
    private List<JiraOptionBean> departmentCateList;
    @XmlElement
    private List<JiraOptionBean> jmDepartmentList;
    @XmlElement
    private List<JiraOptionBean> projectCateList;
    @XmlElement
    private List<JiraOptionBean> resultCateList;
    @XmlElement
    private List<JiraOptionBean> resultTypeList;
    @XmlElement
    private List<JiraOptionBean> affiliatedPlatformList;
    @XmlElement
    private List<JiraOptionBean> platformCateList;
    @XmlElement
    private Long issueId;
    @XmlElement
    private String issueKey;
    @XmlElement
    private String projectType;
    @XmlElement
    private String projectName;
    @XmlElement
    private String projectCode;
    @XmlElement
    private String projectBackground;
    @XmlElement
    private String projectPurpose;
    @XmlElement
    private String projectTarget;
    @XmlElement
    private String projectLevel;
    @XmlElement
    private String developmentBudget;
    @XmlElement
    private String statusName;
    @XmlElement
    private String statusId;
    @XmlElement
    private String progress;
    @XmlElement
    private String problemAndRisk;
    @XmlElement
    private String strategy;
    @XmlElement
    private String nextPlan;
    @XmlElement
    private Long isInitiationPass;//立项是否通过，1-通过、2-不通过、3-再确认
    @XmlElement
    private String projectLeader;
    @XmlElement
    private String planInitiationDate;
    @XmlElement
    private String initiationPassDate;
    @XmlElement
    private String planSchemeReviewDate;
    @XmlElement
    private String schemeReviewPassDate;
    @XmlElement
    private String planTechnicalSummaryDate;
    @XmlElement
    private String technicalSummaryPassDate;
    @XmlElement
    private Long delayDay;
    @XmlElement
    private String resultName;
    @XmlElement
    private String summary;
    @XmlElement
    private String resultContent;
    @XmlElement
    private String resultPassDate;
    @XmlElement
    private String directorScore;
    @XmlElement
    private String directorEvaluation;
    @XmlElement
    private String presidentScore;
    @XmlElement
    private String presidentEvaluation;
    @XmlElement
    private String averageScore;
    @XmlElement
    private List<String> mainCompletePerson;
    @XmlElement
    private String resultLeader;
    @XmlElement
    private Long isCompleteProject;//0-未结题项目 1-已结题项目
    @XmlElement
    private Long departmentSequence;//按部门架构排序
    @XmlElement
    private String initiationDate;
    @XmlElement
    private String projectStatus;//1-正常、2-暂停、3-延期、4-停止、5-结项
    @XmlElement
    private String requestReporterIds;//1-正常、2-暂停、3-延期、4-停止、5-结项


    public ProjectReportBean() {
    }

    public ProjectReportBean(List<JiraOptionBean> departmentCateList, List<JiraOptionBean> projectCateList, String projectType, String projectName, String projectCode, String projectBackground, String projectPurpose, String projectTarget, String projectLevel, String developmentBudget, String statusName, String progress, String problemAndRisk, String strategy, String nextPlan, String planInitiationDate, String initiationPassDate, String planTechnicalSummaryDate, String technicalSummaryPassDate) {
        this.departmentCateList = departmentCateList;
        this.projectCateList = projectCateList;
        this.projectType = projectType;
        this.projectName = projectName;
        this.projectCode = projectCode;
        this.projectBackground = projectBackground;
        this.projectPurpose = projectPurpose;
        this.projectTarget = projectTarget;
        this.projectLevel = projectLevel;
        this.developmentBudget = developmentBudget;
        this.statusName = statusName;
        this.progress = progress;
        this.problemAndRisk = problemAndRisk;
        this.strategy = strategy;
        this.nextPlan = nextPlan;
        this.planInitiationDate = planInitiationDate;
        this.initiationPassDate = initiationPassDate;
        this.planTechnicalSummaryDate = planTechnicalSummaryDate;
        this.technicalSummaryPassDate = technicalSummaryPassDate;
    }

    private ProjectReportBean(Builder builder) {
        setDepartmentCateList(builder.departmentCateList);
        setJmDepartmentList(builder.jmDepartmentList);
        setProjectCateList(builder.projectCateList);
        setResultCateList(builder.resultCateList);
        setResultTypeList(builder.resultTypeList);
        setAffiliatedPlatformList(builder.affiliatedPlatformList);
        setPlatformCateList(builder.platformCateList);
        setIssueId(builder.issueId);
        setIssueKey(builder.issueKey);
        setProjectType(builder.projectType);
        setProjectName(builder.projectName);
        setProjectCode(builder.projectCode);
        setProjectBackground(builder.projectBackground);
        setProjectPurpose(builder.projectPurpose);
        setProjectTarget(builder.projectTarget);
        setProjectLevel(builder.projectLevel);
        setDevelopmentBudget(builder.developmentBudget);
        setStatusName(builder.statusName);
        setStatusId(builder.statusId);
        setProgress(builder.progress);
        setProblemAndRisk(builder.problemAndRisk);
        setStrategy(builder.strategy);
        setNextPlan(builder.nextPlan);
        setIsInitiationPass(builder.isInitiationPass);
        setProjectLeader(builder.projectLeader);
        setPlanInitiationDate(builder.planInitiationDate);
        setInitiationPassDate(builder.initiationPassDate);
        setPlanSchemeReviewDate(builder.planSchemeReviewDate);
        setSchemeReviewPassDate(builder.schemeReviewPassDate);
        setPlanTechnicalSummaryDate(builder.planTechnicalSummaryDate);
        setTechnicalSummaryPassDate(builder.technicalSummaryPassDate);
        setDelayDay(builder.delayDay);
        setResultName(builder.resultName);
        setSummary(builder.summary);
        setResultContent(builder.resultContent);
        setResultPassDate(builder.resultPassDate);
        setDirectorScore(builder.directorScore);
        setDirectorEvaluation(builder.directorEvaluation);
        setPresidentScore(builder.presidentScore);
        setPresidentEvaluation(builder.presidentEvaluation);
        setAverageScore(builder.averageScore);
        setMainCompletePerson(builder.mainCompletePerson);
        setResultLeader(builder.resultLeader);
        setIsCompleteProject(builder.isCompleteProject);
        setDepartmentSequence(builder.departmentSequence);
        setInitiationDate(builder.initiationDate);
        setProjectStatus(builder.projectStatus);
        setRequestReporterIds(builder.requestReporterIds);
    }

    public List<JiraOptionBean> getPlatformCateList() {
        return platformCateList;
    }

    public void setPlatformCateList(List<JiraOptionBean> platformCateList) {
        this.platformCateList = platformCateList;
    }

    public String getPresidentScore() {
        return presidentScore;
    }

    public void setPresidentScore(String presidentScore) {
        this.presidentScore = presidentScore;
    }

    public String getPresidentEvaluation() {
        return presidentEvaluation;
    }

    public void setPresidentEvaluation(String presidentEvaluation) {
        this.presidentEvaluation = presidentEvaluation;
    }

    public List<JiraOptionBean> getJmDepartmentList() {
        return jmDepartmentList;
    }

    public void setJmDepartmentList(List<JiraOptionBean> jmDepartmentList) {
        this.jmDepartmentList = jmDepartmentList;
    }

    public String getRequestReporterIds() {
        return requestReporterIds;
    }

    public void setRequestReporterIds(String requestReporterIds) {
        this.requestReporterIds = requestReporterIds;
    }

    public List<JiraOptionBean> getAffiliatedPlatformList() {
        return affiliatedPlatformList;
    }

    public void setAffiliatedPlatformList(List<JiraOptionBean> affiliatedPlatformList) {
        this.affiliatedPlatformList = affiliatedPlatformList;
    }

    public String getResultLeader() {
        return resultLeader;
    }

    public void setResultLeader(String resultLeader) {
        this.resultLeader = resultLeader;
    }

    public String getProjectStatus() {
        return projectStatus;
    }

    public void setProjectStatus(String projectStatus) {
        this.projectStatus = projectStatus;
    }

    public String getInitiationDate() {
        return initiationDate;
    }

    public void setInitiationDate(String initiationDate) {
        this.initiationDate = initiationDate;
    }

    public String getStatusId() {
        return statusId;
    }

    public void setStatusId(String statusId) {
        this.statusId = statusId;
    }

    public Long getIsCompleteProject() {
        return isCompleteProject;
    }

    public void setIsCompleteProject(Long isCompleteProject) {
        this.isCompleteProject = isCompleteProject;
    }

    public String getResultPassDate() {
        return resultPassDate;
    }

    public void setResultPassDate(String resultPassDate) {
        this.resultPassDate = resultPassDate;
    }

    public String getSummary() {
        return summary;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }

    public String getResultContent() {
        return resultContent;
    }

    public void setResultContent(String resultContent) {
        this.resultContent = resultContent;
    }

    public String getDirectorScore() {
        return directorScore;
    }

    public void setDirectorScore(String directorScore) {
        this.directorScore = directorScore;
    }

    public String getDirectorEvaluation() {
        return directorEvaluation;
    }

    public void setDirectorEvaluation(String directorEvaluation) {
        this.directorEvaluation = directorEvaluation;
    }

    public String getAverageScore() {
        return averageScore;
    }

    public void setAverageScore(String averageScore) {
        this.averageScore = averageScore;
    }

    public List<String> getMainCompletePerson() {
        return mainCompletePerson;
    }

    public void setMainCompletePerson(List<String> mainCompletePerson) {
        this.mainCompletePerson = mainCompletePerson;
    }

    public List<JiraOptionBean> getResultTypeList() {
        return resultTypeList;
    }

    public void setResultTypeList(List<JiraOptionBean> resultTypeList) {
        this.resultTypeList = resultTypeList;
    }

    public List<JiraOptionBean> getDepartmentCateList() {
        return departmentCateList;
    }

    public void setDepartmentCateList(List<JiraOptionBean> departmentCateList) {
        this.departmentCateList = departmentCateList;
    }

    public List<JiraOptionBean> getProjectCateList() {
        return projectCateList;
    }

    public void setProjectCateList(List<JiraOptionBean> projectCateList) {
        this.projectCateList = projectCateList;
    }

    public String getProjectType() {
        return projectType;
    }

    public void setProjectType(String projectType) {
        this.projectType = projectType;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode;
    }

    public String getProjectBackground() {
        return projectBackground;
    }

    public void setProjectBackground(String projectBackground) {
        this.projectBackground = projectBackground;
    }

    public String getProjectPurpose() {
        return projectPurpose;
    }

    public void setProjectPurpose(String projectPurpose) {
        this.projectPurpose = projectPurpose;
    }

    public String getProjectTarget() {
        return projectTarget;
    }

    public void setProjectTarget(String projectTarget) {
        this.projectTarget = projectTarget;
    }

    public String getProjectLevel() {
        return projectLevel;
    }

    public void setProjectLevel(String projectLevel) {
        this.projectLevel = projectLevel;
    }

    public String getDevelopmentBudget() {
        return developmentBudget;
    }

    public void setDevelopmentBudget(String developmentBudget) {
        this.developmentBudget = developmentBudget;
    }

    public String getStatusName() {
        return statusName;
    }

    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }

    public String getProgress() {
        return progress;
    }

    public void setProgress(String progress) {
        this.progress = progress;
    }

    public String getProblemAndRisk() {
        return problemAndRisk;
    }

    public void setProblemAndRisk(String problemAndRisk) {
        this.problemAndRisk = problemAndRisk;
    }

    public String getStrategy() {
        return strategy;
    }

    public void setStrategy(String strategy) {
        this.strategy = strategy;
    }

    public String getNextPlan() {
        return nextPlan;
    }

    public void setNextPlan(String nextPlan) {
        this.nextPlan = nextPlan;
    }

    public Long getIsInitiationPass() {
        return isInitiationPass;
    }

    public void setIsInitiationPass(Long isInitiationPass) {
        this.isInitiationPass = isInitiationPass;
    }

    public String getProjectLeader() {
        return projectLeader;
    }

    public void setProjectLeader(String projectLeader) {
        this.projectLeader = projectLeader;
    }

    public String getPlanInitiationDate() {
        return planInitiationDate;
    }

    public void setPlanInitiationDate(String planInitiationDate) {
        this.planInitiationDate = planInitiationDate;
    }

    public String getInitiationPassDate() {
        return initiationPassDate;
    }

    public void setInitiationPassDate(String initiationPassDate) {
        this.initiationPassDate = initiationPassDate;
    }

    public String getPlanTechnicalSummaryDate() {
        return planTechnicalSummaryDate;
    }

    public String getPlanSchemeReviewDate() {
        return planSchemeReviewDate;
    }

    public void setPlanSchemeReviewDate(String planSchemeReviewDate) {
        this.planSchemeReviewDate = planSchemeReviewDate;
    }

    public String getSchemeReviewPassDate() {
        return schemeReviewPassDate;
    }

    public void setSchemeReviewPassDate(String schemeReviewPassDate) {
        this.schemeReviewPassDate = schemeReviewPassDate;
    }

    public void setPlanTechnicalSummaryDate(String planTechnicalSummaryDate) {
        this.planTechnicalSummaryDate = planTechnicalSummaryDate;
    }

    public String getTechnicalSummaryPassDate() {
        return technicalSummaryPassDate;
    }

    public void setTechnicalSummaryPassDate(String technicalSummaryPassDate) {
        this.technicalSummaryPassDate = technicalSummaryPassDate;
    }

    public Long getDelayDay() {
        return delayDay;
    }

    public void setDelayDay(Long delayDay) {
        this.delayDay = delayDay;
    }

    public List<JiraOptionBean> getResultCateList() {
        return resultCateList;
    }

    public void setResultCateList(List<JiraOptionBean> resultCateList) {
        this.resultCateList = resultCateList;
    }

    public Long getIssueId() {
        return issueId;
    }

    public void setIssueId(Long issueId) {
        this.issueId = issueId;
    }

    public String getIssueKey() {
        return issueKey;
    }

    public void setIssueKey(String issueKey) {
        this.issueKey = issueKey;
    }

    public String getResultName() {
        return resultName;
    }

    public void setResultName(String resultName) {
        this.resultName = resultName;
    }

    public Long getDepartmentSequence() {
        return departmentSequence;
    }

    public void setDepartmentSequence(Long departmentSequence) {
        this.departmentSequence = departmentSequence;
    }

    public static final class Builder {
        private List<JiraOptionBean> departmentCateList;
        private List<JiraOptionBean> jmDepartmentList;
        private List<JiraOptionBean> projectCateList;
        private List<JiraOptionBean> resultCateList;
        private List<JiraOptionBean> resultTypeList;
        private List<JiraOptionBean> affiliatedPlatformList;
        private List<JiraOptionBean> platformCateList;
        private Long issueId;
        private String issueKey;
        private String projectType;
        private String projectName;
        private String projectCode;
        private String projectBackground;
        private String projectPurpose;
        private String projectTarget;
        private String projectLevel;
        private String developmentBudget;
        private String statusName;
        private String statusId;
        private String progress;
        private String problemAndRisk;
        private String strategy;
        private String nextPlan;
        private Long isInitiationPass;
        private String projectLeader;
        private String planInitiationDate;
        private String initiationPassDate;
        private String planSchemeReviewDate;
        private String schemeReviewPassDate;
        private String planTechnicalSummaryDate;
        private String technicalSummaryPassDate;
        private Long delayDay;
        private String resultName;
        private String summary;
        private String resultContent;
        private String resultPassDate;
        private String directorScore;
        private String directorEvaluation;
        private String presidentScore;
        private String presidentEvaluation;
        private String averageScore;
        private List<String> mainCompletePerson;
        private String resultLeader;
        private Long isCompleteProject;
        private Long departmentSequence;
        private String initiationDate;
        private String projectStatus;
        private String requestReporterIds;

        public Builder() {
        }

        public Builder departmentCateList(List<JiraOptionBean> departmentCateList) {
            this.departmentCateList = departmentCateList;
            return this;
        }

        public Builder jmDepartmentList(List<JiraOptionBean> jmDepartmentList) {
            this.jmDepartmentList = jmDepartmentList;
            return this;
        }

        public Builder projectCateList(List<JiraOptionBean> projectCateList) {
            this.projectCateList = projectCateList;
            return this;
        }

        public Builder resultCateList(List<JiraOptionBean> resultCateList) {
            this.resultCateList = resultCateList;
            return this;
        }

        public Builder resultTypeList(List<JiraOptionBean> resultTypeList) {
            this.resultTypeList = resultTypeList;
            return this;
        }

        public Builder affiliatedPlatformList(List<JiraOptionBean> affiliatedPlatformList) {
            this.affiliatedPlatformList = affiliatedPlatformList;
            return this;
        }

        public Builder platformCateList(List<JiraOptionBean> platformCateList) {
            this.platformCateList = platformCateList;
            return this;
        }

        public Builder issueId(Long issueId) {
            this.issueId = issueId;
            return this;
        }

        public Builder issueKey(String issueKey) {
            this.issueKey = issueKey;
            return this;
        }

        public Builder projectType(String projectType) {
            this.projectType = projectType;
            return this;
        }

        public Builder projectName(String projectName) {
            this.projectName = projectName;
            return this;
        }

        public Builder projectCode(String projectCode) {
            this.projectCode = projectCode;
            return this;
        }

        public Builder projectBackground(String projectBackground) {
            this.projectBackground = projectBackground;
            return this;
        }

        public Builder projectPurpose(String projectPurpose) {
            this.projectPurpose = projectPurpose;
            return this;
        }

        public Builder projectTarget(String projectTarget) {
            this.projectTarget = projectTarget;
            return this;
        }

        public Builder projectLevel(String projectLevel) {
            this.projectLevel = projectLevel;
            return this;
        }

        public Builder developmentBudget(String developmentBudget) {
            this.developmentBudget = developmentBudget;
            return this;
        }

        public Builder statusName(String statusName) {
            this.statusName = statusName;
            return this;
        }

        public Builder statusId(String statusId) {
            this.statusId = statusId;
            return this;
        }

        public Builder progress(String progress) {
            this.progress = progress;
            return this;
        }

        public Builder problemAndRisk(String problemAndRisk) {
            this.problemAndRisk = problemAndRisk;
            return this;
        }

        public Builder strategy(String strategy) {
            this.strategy = strategy;
            return this;
        }

        public Builder nextPlan(String nextPlan) {
            this.nextPlan = nextPlan;
            return this;
        }

        public Builder isInitiationPass(Long isInitiationPass) {
            this.isInitiationPass = isInitiationPass;
            return this;
        }

        public Builder projectLeader(String projectLeader) {
            this.projectLeader = projectLeader;
            return this;
        }

        public Builder planInitiationDate(String planInitiationDate) {
            this.planInitiationDate = planInitiationDate;
            return this;
        }

        public Builder initiationPassDate(String initiationPassDate) {
            this.initiationPassDate = initiationPassDate;
            return this;
        }

        public Builder planSchemeReviewDate(String planSchemeReviewDate) {
            this.planSchemeReviewDate = planSchemeReviewDate;
            return this;
        }

        public Builder schemeReviewPassDate(String schemeReviewPassDate) {
            this.schemeReviewPassDate = schemeReviewPassDate;
            return this;
        }

        public Builder planTechnicalSummaryDate(String planTechnicalSummaryDate) {
            this.planTechnicalSummaryDate = planTechnicalSummaryDate;
            return this;
        }

        public Builder technicalSummaryPassDate(String technicalSummaryPassDate) {
            this.technicalSummaryPassDate = technicalSummaryPassDate;
            return this;
        }

        public Builder delayDay(Long delayDay) {
            this.delayDay = delayDay;
            return this;
        }

        public Builder resultName(String resultName) {
            this.resultName = resultName;
            return this;
        }

        public Builder summary(String summary) {
            this.summary = summary;
            return this;
        }

        public Builder resultContent(String resultContent) {
            this.resultContent = resultContent;
            return this;
        }

        public Builder resultPassDate(String resultPassDate) {
            this.resultPassDate = resultPassDate;
            return this;
        }

        public Builder directorScore(String directorScore) {
            this.directorScore = directorScore;
            return this;
        }

        public Builder directorEvaluation(String directorEvaluation) {
            this.directorEvaluation = directorEvaluation;
            return this;
        }

        public Builder presidentScore(String presidentScore) {
            this.presidentScore = presidentScore;
            return this;
        }

        public Builder presidentEvaluation(String presidentEvaluation) {
            this.presidentEvaluation = presidentEvaluation;
            return this;
        }

        public Builder averageScore(String averageScore) {
            this.averageScore = averageScore;
            return this;
        }

        public Builder mainCompletePerson(List<String> mainCompletePerson) {
            this.mainCompletePerson = mainCompletePerson;
            return this;
        }

        public Builder resultLeader(String resultLeader) {
            this.resultLeader = resultLeader;
            return this;
        }

        public Builder isCompleteProject(Long isCompleteProject) {
            this.isCompleteProject = isCompleteProject;
            return this;
        }

        public Builder departmentSequence(Long departmentSequence) {
            this.departmentSequence = departmentSequence;
            return this;
        }

        public Builder initiationDate(String initiationDate) {
            this.initiationDate = initiationDate;
            return this;
        }

        public Builder projectStatus(String projectStatus) {
            this.projectStatus = projectStatus;
            return this;
        }

        public Builder requestReporterIds(String requestReporterIds) {
            this.requestReporterIds = requestReporterIds;
            return this;
        }

        public ProjectReportBean build() {
            return new ProjectReportBean(this);
        }
    }
}
