package com.eve.beans;


import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

@XmlRootElement
public class ProjectReviewListBean {
    @XmlElement
    private Long issueId;
    @XmlElement
    private String issueKey;
    @XmlElement
    private Long statusId;
    @XmlElement
    private String statusName;
    @XmlElement
    private Long secondReview;
    @XmlElement
    private Long no;
    @XmlElement
    private String cate1;
    @XmlElement
    private String cate2;
    @XmlElement
    private String cate3;
    @XmlElement
    private Long projectCate;
    @XmlElement
    private String projectName;
    @XmlElement
    private String projectPurpose;
    @XmlElement
    private String projectTarget;
    @XmlElement
    private String projectBackGround;
    @XmlElement
    private String researchContent;
    @XmlElement
    private Long platformAndTopic;
    @XmlElement
    private Long projectLevel;
    @XmlElement
    private String projectLeader;
    @XmlElement
    private String projectLeaderId;
    @XmlElement
    private String department2;
    @XmlElement
    private String department3;
    @XmlElement
    private Long departmentCateParent;
    @XmlElement
    private Long departmentCate;
    @XmlElement
    private Long parentJmDepartment;
    @XmlElement
    private String parentJmDepartmentName;
    @XmlElement
    private Long childJmDepartment;
    @XmlElement
    private String childJmDepartmentName;
    @XmlElement
    private String area;
    @XmlElement
    private String directLeader;//直属领导
    @XmlElement
    private String directLeaderId;
    @XmlElement
    private String inspectorGeneral;//总监
    @XmlElement
    private String inspectorGeneralId;
    @XmlElement
    private String deputyHeadOfTheInstitute;//副所长
    @XmlElement
    private String deputyHeadOfTheInstituteId;
    @XmlElement
    private String headOfTheInstitute;//所长
    @XmlElement
    private String headOfTheInstituteId;
    @XmlElement
    private String president;//院长
    @XmlElement
    private String presidentId;
    @XmlElement
    private String projectManager;//项目管理员
    @XmlElement
    private String projectManagerId;
    @XmlElement
    private Long reviewResult1;
    @XmlElement
    private String reviewOpinion1;
    @XmlElement
    private Long reviewResult2;
    @XmlElement
    private String reviewOpinion2;
    @XmlElement
    private String createDate;
    @XmlElement
    private Long affiliatedPlatform;//归属平台
    @XmlElement
    private String affiliatedPlatform1;//归属平台一级
    @XmlElement
    private String affiliatedPlatform2;//归属平台二级
    @XmlElement
    private String platformLeader;//平台负责人
    @XmlElement
    private String platformLeaderId;//平台负责人工号
    @XmlElement
    private Long isCompleteProject;//0-未结题项目 1-已结题项目
    @XmlElement
    private Long isInitiationPass;//立项是否通过，1-通过、2-不通过、3-再确认
    @XmlElement
    private String projectStatus;//1-正常、2-暂停、3-延期、4-停止、5-结项
    @XmlElement
    private Long topicType;//课题来源

    public ProjectReviewListBean(Long issueId, String issueKey, Long statusId, String statusName, Long secondReview, Long no, String cate1, String cate2, String cate3, Long projectCate, String projectName, String projectPurpose, String projectTarget, String projectBackGround, String researchContent, Long platformAndTopic, Long projectLevel, String projectLeader, String projectLeaderId, String department2, String department3, Long departmentCateParent, Long departmentCate, String directLeader, String directLeaderId, String inspectorGeneral, String inspectorGeneralId, String deputyHeadOfTheInstitute, String deputyHeadOfTheInstituteId, String headOfTheInstitute, String headOfTheInstituteId, String president, String presidentId, String projectManager, String projectManagerId, Long reviewResult1, String reviewOpinion1, Long reviewResult2, String reviewOpinion2) {
        this.issueId = issueId;
        this.issueKey = issueKey;
        this.statusId = statusId;
        this.statusName = statusName;
        this.secondReview = secondReview;
        this.no = no;
        this.cate1 = cate1;
        this.cate2 = cate2;
        this.cate3 = cate3;
        this.projectCate = projectCate;
        this.projectName = projectName;
        this.projectPurpose = projectPurpose;
        this.projectTarget = projectTarget;
        this.projectBackGround = projectBackGround;
        this.researchContent = researchContent;
        this.platformAndTopic = platformAndTopic;
        this.projectLevel = projectLevel;
        this.projectLeader = projectLeader;
        this.projectLeaderId = projectLeaderId;
        this.department2 = department2;
        this.department3 = department3;
        this.departmentCateParent = departmentCateParent;
        this.departmentCate = departmentCate;
        this.directLeader = directLeader;
        this.directLeaderId = directLeaderId;
        this.inspectorGeneral = inspectorGeneral;
        this.inspectorGeneralId = inspectorGeneralId;
        this.deputyHeadOfTheInstitute = deputyHeadOfTheInstitute;
        this.deputyHeadOfTheInstituteId = deputyHeadOfTheInstituteId;
        this.headOfTheInstitute = headOfTheInstitute;
        this.headOfTheInstituteId = headOfTheInstituteId;
        this.president = president;
        this.presidentId = presidentId;
        this.projectManager = projectManager;
        this.projectManagerId = projectManagerId;
        this.reviewResult1 = reviewResult1;
        this.reviewOpinion1 = reviewOpinion1;
        this.reviewResult2 = reviewResult2;
        this.reviewOpinion2 = reviewOpinion2;
    }

    public Long getTopicType() {
        return topicType;
    }

    public void setTopicType(Long topicType) {
        this.topicType = topicType;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public String getParentJmDepartmentName() {
        return parentJmDepartmentName;
    }

    public void setParentJmDepartmentName(String parentJmDepartmentName) {
        this.parentJmDepartmentName = parentJmDepartmentName;
    }

    public String getChildJmDepartmentName() {
        return childJmDepartmentName;
    }

    public void setChildJmDepartmentName(String childJmDepartmentName) {
        this.childJmDepartmentName = childJmDepartmentName;
    }

    public Long getParentJmDepartment() {
        return parentJmDepartment;
    }

    public void setParentJmDepartment(Long parentJmDepartment) {
        this.parentJmDepartment = parentJmDepartment;
    }

    public Long getChildJmDepartment() {
        return childJmDepartment;
    }

    public void setChildJmDepartment(Long childJmDepartment) {
        this.childJmDepartment = childJmDepartment;
    }

    public Long getDepartmentCateParent() {
        return departmentCateParent;
    }

    public void setDepartmentCateParent(Long departmentCateParent) {
        this.departmentCateParent = departmentCateParent;
    }

    public String getProjectStatus() {
        return projectStatus;
    }

    public void setProjectStatus(String projectStatus) {
        this.projectStatus = projectStatus;
    }

    public Long getIsCompleteProject() {
        return isCompleteProject;
    }

    public void setIsCompleteProject(Long isCompleteProject) {
        this.isCompleteProject = isCompleteProject;
    }

    public Long getIsInitiationPass() {
        return isInitiationPass;
    }

    public void setIsInitiationPass(Long isInitiationPass) {
        this.isInitiationPass = isInitiationPass;
    }

    public String getCreateDate() {
        return createDate;
    }

    public void setCreateDate(String createDate) {
        this.createDate = createDate;
    }

    public String getProjectPurpose() {
        return projectPurpose;
    }

    public void setProjectPurpose(String projectPurpose) {
        this.projectPurpose = projectPurpose;
    }

    public String getProjectTarget() {
        return projectTarget;
    }

    public void setProjectTarget(String projectTarget) {
        this.projectTarget = projectTarget;
    }

    public String getProjectBackGround() {
        return projectBackGround;
    }

    public void setProjectBackGround(String projectBackGround) {
        this.projectBackGround = projectBackGround;
    }

    public String getDirectLeader() {
        return directLeader;
    }

    public void setDirectLeader(String directLeader) {
        this.directLeader = directLeader;
    }

    public String getDirectLeaderId() {
        return directLeaderId;
    }

    public void setDirectLeaderId(String directLeaderId) {
        this.directLeaderId = directLeaderId;
    }

    public String getInspectorGeneral() {
        return inspectorGeneral;
    }

    public void setInspectorGeneral(String inspectorGeneral) {
        this.inspectorGeneral = inspectorGeneral;
    }

    public String getInspectorGeneralId() {
        return inspectorGeneralId;
    }

    public void setInspectorGeneralId(String inspectorGeneralId) {
        this.inspectorGeneralId = inspectorGeneralId;
    }

    public String getDeputyHeadOfTheInstitute() {
        return deputyHeadOfTheInstitute;
    }

    public void setDeputyHeadOfTheInstitute(String deputyHeadOfTheInstitute) {
        this.deputyHeadOfTheInstitute = deputyHeadOfTheInstitute;
    }

    public String getDeputyHeadOfTheInstituteId() {
        return deputyHeadOfTheInstituteId;
    }

    public void setDeputyHeadOfTheInstituteId(String deputyHeadOfTheInstituteId) {
        this.deputyHeadOfTheInstituteId = deputyHeadOfTheInstituteId;
    }

    public String getHeadOfTheInstitute() {
        return headOfTheInstitute;
    }

    public void setHeadOfTheInstitute(String headOfTheInstitute) {
        this.headOfTheInstitute = headOfTheInstitute;
    }

    public String getHeadOfTheInstituteId() {
        return headOfTheInstituteId;
    }

    public void setHeadOfTheInstituteId(String headOfTheInstituteId) {
        this.headOfTheInstituteId = headOfTheInstituteId;
    }

    public String getPresident() {
        return president;
    }

    public void setPresident(String president) {
        this.president = president;
    }

    public String getPresidentId() {
        return presidentId;
    }

    public void setPresidentId(String presidentId) {
        this.presidentId = presidentId;
    }

    public String getProjectManager() {
        return projectManager;
    }

    public void setProjectManager(String projectManager) {
        this.projectManager = projectManager;
    }

    public String getProjectManagerId() {
        return projectManagerId;
    }

    public void setProjectManagerId(String projectManagerId) {
        this.projectManagerId = projectManagerId;
    }

    public Long getIssueId() {
        return issueId;
    }

    public void setIssueId(Long issueId) {
        this.issueId = issueId;
    }

    public String getIssueKey() {
        return issueKey;
    }

    public void setIssueKey(String issueKey) {
        this.issueKey = issueKey;
    }

    public Long getStatusId() {
        return statusId;
    }

    public void setStatusId(Long statusId) {
        this.statusId = statusId;
    }

    public String getStatusName() {
        return statusName;
    }

    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }

    public Long getSecondReview() {
        return secondReview;
    }

    public void setSecondReview(Long secondReview) {
        this.secondReview = secondReview;
    }

    public Long getNo() {
        return no;
    }

    public void setNo(Long no) {
        this.no = no;
    }

    public String getCate1() {
        return cate1;
    }

    public void setCate1(String cate1) {
        this.cate1 = cate1;
    }

    public String getCate2() {
        return cate2;
    }

    public void setCate2(String cate2) {
        this.cate2 = cate2;
    }

    public String getCate3() {
        return cate3;
    }

    public void setCate3(String cate3) {
        this.cate3 = cate3;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getResearchContent() {
        return researchContent;
    }

    public void setResearchContent(String researchContent) {
        this.researchContent = researchContent;
    }

    public Long getPlatformAndTopic() {
        return platformAndTopic;
    }

    public void setPlatformAndTopic(Long platformAndTopic) {
        this.platformAndTopic = platformAndTopic;
    }

    public Long getProjectLevel() {
        return projectLevel;
    }

    public void setProjectLevel(Long projectLevel) {
        this.projectLevel = projectLevel;
    }

    public String getProjectLeader() {
        return projectLeader;
    }

    public void setProjectLeader(String projectLeader) {
        this.projectLeader = projectLeader;
    }

    public String getDepartment2() {
        return department2;
    }

    public void setDepartment2(String department2) {
        this.department2 = department2;
    }

    public String getDepartment3() {
        return department3;
    }

    public void setDepartment3(String department3) {
        this.department3 = department3;
    }

    public Long getReviewResult1() {
        return reviewResult1;
    }

    public void setReviewResult1(Long reviewResult1) {
        this.reviewResult1 = reviewResult1;
    }

    public String getReviewOpinion1() {
        return reviewOpinion1;
    }

    public void setReviewOpinion1(String reviewOpinion1) {
        this.reviewOpinion1 = reviewOpinion1;
    }

    public Long getReviewResult2() {
        return reviewResult2;
    }

    public void setReviewResult2(Long reviewResult2) {
        this.reviewResult2 = reviewResult2;
    }

    public String getReviewOpinion2() {
        return reviewOpinion2;
    }

    public void setReviewOpinion2(String reviewOpinion2) {
        this.reviewOpinion2 = reviewOpinion2;
    }

    public Long getProjectCate() {
        return projectCate;
    }

    public void setProjectCate(Long projectCate) {
        this.projectCate = projectCate;
    }

    public String getProjectLeaderId() {
        return projectLeaderId;
    }

    public void setProjectLeaderId(String projectLeaderId) {
        this.projectLeaderId = projectLeaderId;
    }

    public Long getDepartmentCate() {
        return departmentCate;
    }

    public void setDepartmentCate(Long departmentCate) {
        this.departmentCate = departmentCate;
    }

    public Long getAffiliatedPlatform() {
        return affiliatedPlatform;
    }

    public void setAffiliatedPlatform(Long affiliatedPlatform) {
        this.affiliatedPlatform = affiliatedPlatform;
    }

    public String getAffiliatedPlatform1() {
        return affiliatedPlatform1;
    }

    public void setAffiliatedPlatform1(String affiliatedPlatform1) {
        this.affiliatedPlatform1 = affiliatedPlatform1;
    }

    public String getAffiliatedPlatform2() {
        return affiliatedPlatform2;
    }

    public void setAffiliatedPlatform2(String affiliatedPlatform2) {
        this.affiliatedPlatform2 = affiliatedPlatform2;
    }

    public String getPlatformLeader() {
        return platformLeader;
    }

    public void setPlatformLeader(String platformLeader) {
        this.platformLeader = platformLeader;
    }

    public String getPlatformLeaderId() {
        return platformLeaderId;
    }

    public void setPlatformLeaderId(String platformLeaderId) {
        this.platformLeaderId = platformLeaderId;
    }
}
