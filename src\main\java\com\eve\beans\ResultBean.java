package com.eve.beans;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.util.Map;

/**
 * Created by seven on 2017/12/19.
 */
@XmlRootElement
public class ResultBean implements Serializable {

    public int length;
    @XmlElement
    private boolean result = true;
    @XmlElement
    private String message;
    /*@XmlElement
    private boolean cannext = true;*/
    @XmlElement
    private Object value;
    /*@XmlElement
    private String strValue;*/
    /*@XmlElement
    private Integer code;*/
    @XmlElement
    private Map map;

    @XmlElement
    private Integer pageNum;

    @XmlElement
    private Integer cur;

    public Map getMap() {
        return map;
    }

    public void setMap(Map map) {
        this.map = map;
    }
    

    public Object getValue() {
        return value;
    }


    public void setValue(Object value) {
        this.value = value;
    }


    public void setMessage(String message) {
        this.message = message;
        this.result = false;
    }


    public boolean isResult() {
        return result;
    }


    public void setResult(boolean result) {
        this.result = result;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getCur() {
        return cur;
    }

    public void setCur(Integer cur) {
        this.cur = cur;
    }

    public void addMessage(String msg) {
        if (message == null) {
            message = msg;
        } else {
            message = message + " ; " + msg;
        }
        this.result = false;
    }

    public String getMessage() {
        return message;
    }

    @Override
    public String toString() {
        return "ResultBean{" +
                "result=" + result +
                ", message='" + message + '\'' +
                ", value=" + value +
                ", map=" + map +
                '}';
    }
}
