package com.eve.beans;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;

/**
 * @<PERSON> wang<PERSON>
 * @Date 2021/10/19 16:04
 */
@XmlRootElement
public class ResultReviewBean implements Serializable {

    @XmlElement
    private String reviewType;//评审人、所长、院长
    @XmlElement
    private String reviewUser;//评审人
    @XmlElement
    private String reviewScore;//评分
    @XmlElement
    private String reviewOpinion;//评价

    public ResultReviewBean() {
    }

    public ResultReviewBean(String reviewType, String reviewUser, String reviewScore, String reviewOpinion) {
        this.reviewType = reviewType;
        this.reviewUser = reviewUser;
        this.reviewScore = reviewScore;
        this.reviewOpinion = reviewOpinion;
    }

    private ResultReviewBean(Builder builder) {
        setReviewType(builder.reviewType);
        setReviewUser(builder.reviewUser);
        setReviewScore(builder.reviewScore);
        setReviewOpinion(builder.reviewOpinion);
    }

    public String getReviewType() {
        return reviewType;
    }

    public void setReviewType(String reviewType) {
        this.reviewType = reviewType;
    }

    public String getReviewUser() {
        return reviewUser;
    }

    public void setReviewUser(String reviewUser) {
        this.reviewUser = reviewUser;
    }

    public String getReviewScore() {
        return reviewScore;
    }

    public void setReviewScore(String reviewScore) {
        this.reviewScore = reviewScore;
    }

    public String getReviewOpinion() {
        return reviewOpinion;
    }

    public void setReviewOpinion(String reviewOpinion) {
        this.reviewOpinion = reviewOpinion;
    }

    public static final class Builder {
        private String reviewType;
        private String reviewUser;
        private String reviewScore;
        private String reviewOpinion;

        public Builder() {
        }

        public Builder reviewType(String reviewType) {
            this.reviewType = reviewType;
            return this;
        }

        public Builder reviewUser(String reviewUser) {
            this.reviewUser = reviewUser;
            return this;
        }

        public Builder reviewScore(String reviewScore) {
            this.reviewScore = reviewScore;
            return this;
        }

        public Builder reviewOpinion(String reviewOpinion) {
            this.reviewOpinion = reviewOpinion;
            return this;
        }

        public ResultReviewBean build() {
            return new ResultReviewBean(this);
        }
    }
}
