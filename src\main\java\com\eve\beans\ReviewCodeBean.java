package com.eve.beans;

import com.atlassian.jira.issue.history.ChangeItemBean;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * <AUTHOR>
 * @date 2022/10/25
 */
@XmlRootElement
public class ReviewCodeBean {
    @XmlElement
    private String operatorCode;
    @XmlElement
    private String operatorName;
    @XmlElement
    private String oldAssignCode;
    @XmlElement
    private String oldAssignName;
    @XmlElement
    private String newAssignCode;
    @XmlElement
    private String newAssignName;
    @XmlElement
    private String transitionName;
    @XmlElement
    private ChangeItemBean changeItemBean;

    public ReviewCodeBean(String operatorCode, String operatorName, String oldAssignCode, String oldAssignName, String newAssignCode, String newAssignName, String transitionName, ChangeItemBean changeItemBean) {
        this.operatorCode = operatorCode;
        this.operatorName = operatorName;
        this.oldAssignCode = oldAssignCode;
        this.oldAssignName = oldAssignName;
        this.newAssignCode = newAssignCode;
        this.newAssignName = newAssignName;
        this.transitionName = transitionName;
        this.changeItemBean = changeItemBean;
    }

    public String getOperatorCode() {
        return operatorCode;
    }

    public void setOperatorCode(String operatorCode) {
        this.operatorCode = operatorCode;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    public String getOldAssignCode() {
        return oldAssignCode;
    }

    public void setOldAssignCode(String oldAssignCode) {
        this.oldAssignCode = oldAssignCode;
    }

    public String getOldAssignName() {
        return oldAssignName;
    }

    public void setOldAssignName(String oldAssignName) {
        this.oldAssignName = oldAssignName;
    }

    public String getNewAssignCode() {
        return newAssignCode;
    }

    public void setNewAssignCode(String newAssignCode) {
        this.newAssignCode = newAssignCode;
    }

    public String getNewAssignName() {
        return newAssignName;
    }

    public void setNewAssignName(String newAssignName) {
        this.newAssignName = newAssignName;
    }

    public String getTransitionName() {
        return transitionName;
    }

    public void setTransitionName(String transitionName) {
        this.transitionName = transitionName;
    }

    public ChangeItemBean getChangeItemBean() {
        return changeItemBean;
    }

    public void setChangeItemBean(ChangeItemBean changeItemBean) {
        this.changeItemBean = changeItemBean;
    }
}
