package com.eve.beans;

import javax.xml.bind.annotation.XmlElement;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/2/22
 */
public class SmartAttachmentCateBean {
    @XmlElement
    private Long id;
    @XmlElement
    private String name;
    @XmlElement
    private List<Long> attachmentIds;
    @XmlElement
    private List<AttachmentBean> attachmentList;
    @XmlElement
    private List<SmartAttachmentCateBean> documents;
    @XmlElement
    private Boolean hideEmpty;
    @XmlElement
    private Boolean allowWrite;

    public SmartAttachmentCateBean() {
    }

    private SmartAttachmentCateBean(Builder builder) {
        setId(builder.id);
        setName(builder.name);
        setAttachmentIds(builder.attachmentIds);
        setAttachmentList(builder.attachmentList);
        setDocuments(builder.documents);
        setHideEmpty(builder.hideEmpty);
        setAllowWrite(builder.allowWrite);
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public List<Long> getAttachmentIds() {
        return attachmentIds;
    }

    public void setAttachmentIds(List<Long> attachmentIds) {
        this.attachmentIds = attachmentIds;
    }

    public List<AttachmentBean> getAttachmentList() {
        return attachmentList;
    }

    public void setAttachmentList(List<AttachmentBean> attachmentList) {
        this.attachmentList = attachmentList;
    }

    public List<SmartAttachmentCateBean> getDocuments() {
        return documents;
    }

    public void setDocuments(List<SmartAttachmentCateBean> documents) {
        this.documents = documents;
    }

    public Boolean getHideEmpty() {
        return hideEmpty;
    }

    public void setHideEmpty(Boolean hideEmpty) {
        this.hideEmpty = hideEmpty;
    }

    public Boolean getAllowWrite() {
        return allowWrite;
    }

    public void setAllowWrite(Boolean allowWrite) {
        this.allowWrite = allowWrite;
    }

    public static final class Builder {
        private Long id;
        private String name;
        private List<Long> attachmentIds;
        private List<AttachmentBean> attachmentList;
        private List<SmartAttachmentCateBean> documents;
        private Boolean hideEmpty;
        private Boolean allowWrite;

        public Builder() {
        }

        public Builder id(Long id) {
            this.id = id;
            return this;
        }

        public Builder name(String name) {
            this.name = name;
            return this;
        }

        public Builder attachmentIds(List<Long> attachmentIds) {
            this.attachmentIds = attachmentIds;
            return this;
        }

        public Builder attachmentList(List<AttachmentBean> attachmentList) {
            this.attachmentList = attachmentList;
            return this;
        }

        public Builder documents(List<SmartAttachmentCateBean> documents) {
            this.documents = documents;
            return this;
        }

        public Builder hideEmpty(Boolean hideEmpty) {
            this.hideEmpty = hideEmpty;
            return this;
        }

        public Builder allowWrite(Boolean allowWrite) {
            this.allowWrite = allowWrite;
            return this;
        }

        public SmartAttachmentCateBean build() {
            return new SmartAttachmentCateBean(this);
        }
    }

    @Override
    public String toString() {
        return "SmartAttachmentCateBean{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", attachmentIds=" + attachmentIds +
                ", attachmentList=" + attachmentList +
                ", documents=" + documents +
                ", hideEmpty=" + hideEmpty +
                ", allowWrite=" + allowWrite +
                '}';
    }
}
