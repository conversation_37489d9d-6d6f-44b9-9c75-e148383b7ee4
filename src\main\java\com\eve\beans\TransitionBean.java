package com.eve.beans;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/12/27
 */
@XmlRootElement
public class TransitionBean implements Serializable {
    @XmlElement
    private String transitionId;
    @XmlElement
    private String transitionName;
    @XmlElement
    private List<TransitionScreenFieldBean> transitionScreenFieldBeanList;

    public TransitionBean(String transitionId, String transitionName) {
        this.transitionId = transitionId;
        this.transitionName = transitionName;
    }

    public String getTransitionId() {
        return transitionId;
    }

    public void setTransitionId(String transitionId) {
        this.transitionId = transitionId;
    }

    public String getTransitionName() {
        return transitionName;
    }

    public void setTransitionName(String transitionName) {
        this.transitionName = transitionName;
    }

    public List<TransitionScreenFieldBean> getTransitionScreenFieldBeanList() {
        return transitionScreenFieldBeanList;
    }

    public void setTransitionScreenFieldBeanList(List<TransitionScreenFieldBean> transitionScreenFieldBeanList) {
        this.transitionScreenFieldBeanList = transitionScreenFieldBeanList;
    }
}
