package com.eve.beans;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/10/10
 */
@XmlRootElement
public class TransitionScreenFieldBean {
    @XmlElement
    private Integer position;
    @XmlElement
    private Long fieldId;
    @XmlElement
    private String fieldKey;
    @XmlElement
    private String fieldName;
    @XmlElement
    private String fieldType;
    @XmlElement
    private List<JiraOptionBean> optionBeanList;

    public TransitionScreenFieldBean(Integer position, Long fieldId, String fieldKey, String fieldName, String fieldType) {
        this.position = position;
        this.fieldId = fieldId;
        this.fieldKey = fieldKey;
        this.fieldName = fieldName;
        this.fieldType = fieldType;
    }

    public Integer getPosition() {
        return position;
    }

    public void setPosition(Integer position) {
        this.position = position;
    }

    public Long getFieldId() {
        return fieldId;
    }

    public void setFieldId(Long fieldId) {
        this.fieldId = fieldId;
    }

    public String getFieldKey() {
        return fieldKey;
    }

    public void setFieldKey(String fieldKey) {
        this.fieldKey = fieldKey;
    }

    public String getFieldName() {
        return fieldName;
    }

    public void setFieldName(String fieldName) {
        this.fieldName = fieldName;
    }

    public String getFieldType() {
        return fieldType;
    }

    public void setFieldType(String fieldType) {
        this.fieldType = fieldType;
    }

    public List<JiraOptionBean> getOptionBeanList() {
        return optionBeanList;
    }

    public void setOptionBeanList(List<JiraOptionBean> optionBeanList) {
        this.optionBeanList = optionBeanList;
    }
}
