package com.eve.beans;

public class TripRequestBean {
    //申请人
    private String applicant;
    //部门
    private String dept;
    //上司
    private String boss;
    //出差理由
    private String tripReason;
    //出差地点
    private String tripAddress;
    //出差开始日期
    private String tripStartDate;
    //出差结束日期
    private String tripEndDate;
    //出差天数
    private String tripDays;

    //问题ID
    private String issueId;

    //费控系统单号
    private String businessCode;
    
    public String getDept() {
        return dept;
    }

    public void setDept(String dept) {
        this.dept = dept;
    }

    public String getApplicant() {
        return applicant;
    }

    public void setApplicant(String applicant) {
        this.applicant = applicant;
    }

    public String getBoss() {
        return boss;
    }

    public void setBoss(String boss) {
        this.boss = boss;
    }

    public String getTripReason() {
        return tripReason;
    }

    public void setTripReason(String tripReason) {
        this.tripReason = tripReason;
    }

    public String getTripAddress() {
        return tripAddress;
    }

    public void setTripAddress(String tripAddress) {
        this.tripAddress = tripAddress;
    }

    public String getTripStartDate() {
        return tripStartDate;
    }

    public void setTripStartDate(String tripStartDate) {
        this.tripStartDate = tripStartDate;
    }

    public String getTripEndDate() {
        return tripEndDate;
    }

    public void setTripEndDate(String tripEndDate) {
        this.tripEndDate = tripEndDate;
    }

    public String getTripDays() {
        return tripDays;
    }

    public void setTripDays(String tripDays) {
        this.tripDays = tripDays;
    }

    public String getIssueId() {
        return issueId;
    }

    public void setIssueId(String issueId) {
        this.issueId = issueId;
    }

    public String getBusinessCode() {
        return businessCode;
    }

    public void setBusinessCode(String businessCode) {
        this.businessCode = businessCode;
    }
}
