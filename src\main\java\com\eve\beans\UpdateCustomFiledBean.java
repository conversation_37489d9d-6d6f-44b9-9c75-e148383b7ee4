package com.eve.beans;

import com.alibaba.fastjson.JSON;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/1/9
 */
@XmlRootElement
public class UpdateCustomFiledBean {
    @XmlElement
    private Long projectId;
    @XmlElement
    private Long issueId;
    @XmlElement
    private String issueKey;
    @XmlElement
    private Long parentIssueId;
    @XmlElement
    private String userName;
    @XmlElement
    private String issueType;
    @XmlElement
    private Long transitionId;
    @XmlElement
    private String transitionName;
    @XmlElement
    private Map<String, String> map;//更新的信息

    private UpdateCustomFiledBean(Builder builder) {
        setProjectId(builder.projectId);
        setIssueId(builder.issueId);
        setIssueKey(builder.issueKey);
        setUserName(builder.userName);
        setIssueType(builder.issueType);
        setTransitionId(builder.transitionId);
        setTransitionName(builder.transitionName);
        setMap(builder.map);
    }

    public Long getParentIssueId() {
        return parentIssueId;
    }

    public void setParentIssueId(Long parentIssueId) {
        this.parentIssueId = parentIssueId;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public Long getIssueId() {
        return issueId;
    }

    public void setIssueId(Long issueId) {
        this.issueId = issueId;
    }

    public String getIssueKey() {
        return issueKey;
    }

    public void setIssueKey(String issueKey) {
        this.issueKey = issueKey;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getIssueType() {
        return issueType;
    }

    public void setIssueType(String issueType) {
        this.issueType = issueType;
    }

    public Long getTransitionId() {
        return transitionId;
    }

    public void setTransitionId(Long transitionId) {
        this.transitionId = transitionId;
    }

    public String getTransitionName() {
        return transitionName;
    }

    public void setTransitionName(String transitionName) {
        this.transitionName = transitionName;
    }

    public Map<String, String> getMap() {
        return map;
    }

    public void setMap(Map<String, String> map) {
        this.map = map;
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }

    public static final class Builder {
        private Long projectId;
        private Long issueId;
        private String issueKey;
        private String userName;
        private String issueType;
        private Long transitionId;
        private String transitionName;
        private Map<String, String> map;

        public Builder() {
        }

        public Builder projectId(Long projectId) {
            this.projectId = projectId;
            return this;
        }

        public Builder issueId(Long issueId) {
            this.issueId = issueId;
            return this;
        }

        public Builder issueKey(String issueKey) {
            this.issueKey = issueKey;
            return this;
        }

        public Builder userName(String userName) {
            this.userName = userName;
            return this;
        }

        public Builder issueType(String issueType) {
            this.issueType = issueType;
            return this;
        }

        public Builder transitionId(Long transitionId) {
            this.transitionId = transitionId;
            return this;
        }

        public Builder transitionName(String transitionName) {
            this.transitionName = transitionName;
            return this;
        }

        public Builder map(Map<String, String> map) {
            this.map = map;
            return this;
        }

        public UpdateCustomFiledBean build() {
            return new UpdateCustomFiledBean(this);
        }
    }
}
