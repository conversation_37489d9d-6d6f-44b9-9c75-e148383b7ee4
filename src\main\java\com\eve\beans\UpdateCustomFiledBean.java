package com.eve.beans;

import com.alibaba.fastjson.JSON;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/1/9
 */
@XmlRootElement
public class UpdateCustomFiledBean {
    @XmlElement
    private Long issueId;
    @XmlElement
    private String issueKey;
    @XmlElement
    private String userName;
    @XmlElement
    private String issueType;
    @XmlElement
    private Long transitionId;
    @XmlElement
    private String transitionName;
    @XmlElement
    private Map<String, String> map;//更新的信息

    public Long getIssueId() {
        return issueId;
    }

    public void setIssueId(Long issueId) {
        this.issueId = issueId;
    }

    public String getIssueKey() {
        return issueKey;
    }

    public void setIssueKey(String issueKey) {
        this.issueKey = issueKey;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getIssueType() {
        return issueType;
    }

    public void setIssueType(String issueType) {
        this.issueType = issueType;
    }

    public Long getTransitionId() {
        return transitionId;
    }

    public void setTransitionId(Long transitionId) {
        this.transitionId = transitionId;
    }

    public String getTransitionName() {
        return transitionName;
    }

    public void setTransitionName(String transitionName) {
        this.transitionName = transitionName;
    }

    public Map<String, String> getMap() {
        return map;
    }

    public void setMap(Map<String, String> map) {
        this.map = map;
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
