package com.eve.beans;

import com.atlassian.jira.transition.TransitionEntry;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/12/26
 */
public class WorkflowBean {
    private String workflowStep;
    private String workflowName;
    private String workflowMode;
    private String workflowTransition;

    private List<TransitionEntry> transitionEntryList;

    public WorkflowBean() {
    }

    public WorkflowBean(String workflowName, List<TransitionEntry> transitionEntryList) {
        this.workflowName = workflowName;
        this.transitionEntryList = transitionEntryList;
    }

    public String getWorkflowStep() {
        return workflowStep;
    }

    public void setWorkflowStep(String workflowStep) {
        this.workflowStep = workflowStep;
    }

    public String getWorkflowMode() {
        return workflowMode;
    }

    public void setWorkflowMode(String workflowMode) {
        this.workflowMode = workflowMode;
    }

    public String getWorkflowTransition() {
        return workflowTransition;
    }

    public void setWorkflowTransition(String workflowTransition) {
        this.workflowTransition = workflowTransition;
    }

    public String getWorkflowName() {
        return workflowName;
    }

    public void setWorkflowName(String workflowName) {
        this.workflowName = workflowName;
    }

    public List<TransitionEntry> getTransitionEntryList() {
        return transitionEntryList;
    }

    public void setTransitionEntryList(List<TransitionEntry> transitionEntryList) {
        this.transitionEntryList = transitionEntryList;
    }
}
