package com.eve.customfield;


import com.atlassian.jira.issue.IssueManager;
import com.atlassian.jira.issue.customfields.impl.AbstractMultiCFType;
import com.atlassian.jira.issue.customfields.impl.FieldValidationException;
import com.atlassian.jira.issue.customfields.manager.GenericConfigManager;
import com.atlassian.jira.issue.customfields.manager.OptionsManager;
import com.atlassian.jira.issue.customfields.persistence.CustomFieldValuePersister;
import com.atlassian.jira.issue.customfields.persistence.PersistenceFieldType;
import com.atlassian.jira.issue.customfields.view.CustomFieldParams;
import com.atlassian.jira.issue.fields.config.FieldConfig;
import com.atlassian.jira.issue.fields.config.FieldConfigItemType;
import com.atlassian.jira.issue.fields.config.manager.FieldConfigSchemeManager;
import com.atlassian.jira.project.ProjectManager;
import com.atlassian.jira.security.JiraAuthenticationContext;
import com.atlassian.jira.util.ErrorCollection;
import com.atlassian.plugin.spring.scanner.annotation.imports.ComponentImport;
import com.eve.beans.InnerOrderTypeBean;
import com.eve.services.SapCreateProjectService;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import javax.inject.Inject;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Comparator;
import java.util.List;

public class InnerOrderTypeCustomField extends AbstractMultiCFType<InnerOrderTypeBean> {

    @ComponentImport
    private OptionsManager optionsManager;
    @ComponentImport
    private JiraAuthenticationContext jiraAuthenticationContext;
    @ComponentImport
    private SapCreateProjectService sapCreateProjectService;
    @ComponentImport
    private CustomFieldValuePersister customFieldValuePersister;
    @ComponentImport
    private GenericConfigManager genericConfigManager;
    @ComponentImport
    private ProjectManager projectManager;
    @ComponentImport
    private FieldConfigSchemeManager fieldConfigSchemeManager;
    @ComponentImport
    private IssueManager issueManager;


    public InnerOrderTypeCustomField(CustomFieldValuePersister customFieldValuePersister, GenericConfigManager genericConfigManager, OptionsManager optionsManager, JiraAuthenticationContext jiraAuthenticationContext, SapCreateProjectService sapCreateProjectService, CustomFieldValuePersister customFieldValuePersister1, GenericConfigManager genericConfigManager1, ProjectManager projectManager, FieldConfigSchemeManager fieldConfigSchemeManager, IssueManager issueManager) {
        super(customFieldValuePersister, genericConfigManager);
        this.optionsManager = optionsManager;
        this.jiraAuthenticationContext = jiraAuthenticationContext;
        this.sapCreateProjectService = sapCreateProjectService;
        this.customFieldValuePersister = customFieldValuePersister1;
        this.genericConfigManager = genericConfigManager1;
        this.projectManager = projectManager;
        this.fieldConfigSchemeManager = fieldConfigSchemeManager;
        this.issueManager = issueManager;
    }

    @Inject
    @Nonnull
    @Override
    public List<FieldConfigItemType> getConfigurationItemTypes() {
        List<FieldConfigItemType> list = super.getConfigurationItemTypes();
        list.add(new InnerOrderTypeCustomFieldConfigItemType(issueManager, jiraAuthenticationContext,
                this.fieldConfigSchemeManager, this.projectManager, this.optionsManager,this.sapCreateProjectService));
        return list;
    }

    @Nullable
    @Override
    protected Comparator<InnerOrderTypeBean> getTypeComparator() {
        return null;
    }

    @Nullable
    @Override
    protected Object convertTypeToDbValue(@Nullable InnerOrderTypeBean option) {
        return option == null ? "" : option.getId()+"";
    }

    @Nullable
    @Override
    protected InnerOrderTypeBean convertDbValueToType(@Nullable Object o) {
//        List<InnerOrderTypeBean> cateOptions = sapCreateProjectService.listOrderType();
        return sapCreateProjectService.getOrderTypeById(String.valueOf(o));
    }

    @Nonnull
    @Override
    protected PersistenceFieldType getDatabaseType() {
        return PersistenceFieldType.TYPE_LIMITED_TEXT;
    }

    @Override
    public String getStringFromSingularObject(InnerOrderTypeBean option) {
        return option == null ? "" : option.getId()+"";
    }

    @Override
    public InnerOrderTypeBean getSingularObjectFromString(String s) throws FieldValidationException {
//        List<InnerOrderTypeBean> cateOptions = sapCreateProjectService.listOrderType();
        return sapCreateProjectService.getOrderTypeById(s);
    }

    @Override
    public void validateFromParams(CustomFieldParams customFieldParams, ErrorCollection errorCollection, FieldConfig fieldConfig) {

    }

    @Override
    public Collection<InnerOrderTypeBean> getValueFromCustomFieldParams(CustomFieldParams customFieldParams) throws FieldValidationException {
        Collection<InnerOrderTypeBean> innerOrderTypeBeanCollection = new ArrayList<>();
        List<String> ids = new ArrayList<>(customFieldParams.getAllValues());
//        List<InnerOrderTypeBean> innerOrderTypeBeanList = sapCreateProjectService.listOrderType();

        for (String id :ids) {
            if ("".equals(id)){
                continue;
            }
            if ("null".equals(id)){
                continue;
            }
            innerOrderTypeBeanCollection.add(sapCreateProjectService.getOrderTypeById(id));
        }
        return innerOrderTypeBeanCollection;
    }

    @Override
    public Object getStringValueFromCustomFieldParams(CustomFieldParams customFieldParams) {
        
        return customFieldParams.getAllValues();
    }
}
