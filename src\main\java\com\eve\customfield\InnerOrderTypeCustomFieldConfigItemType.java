package com.eve.customfield;

import com.atlassian.jira.issue.Issue;
import com.atlassian.jira.issue.IssueManager;
import com.atlassian.jira.issue.customfields.manager.OptionsManager;
import com.atlassian.jira.issue.customfields.option.GenericImmutableOptions;
import com.atlassian.jira.issue.fields.config.FieldConfig;
import com.atlassian.jira.issue.fields.config.FieldConfigItemType;
import com.atlassian.jira.issue.fields.config.manager.FieldConfigSchemeManager;
import com.atlassian.jira.issue.fields.layout.field.FieldLayoutItem;
import com.atlassian.jira.project.ProjectManager;
import com.atlassian.jira.security.JiraAuthenticationContext;
import com.atlassian.plugin.spring.scanner.annotation.imports.ComponentImport;
import com.eve.beans.InnerOrderTypeBean;
import com.eve.services.SapCreateProjectService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

public class InnerOrderTypeCustomFieldConfigItemType implements FieldConfigItemType {

    private static final Logger log = LoggerFactory.getLogger(InnerOrderTypeCustomFieldConfigItemType.class);

    private IssueManager issueManager;
    private JiraAuthenticationContext jiraAuthenticationContext;
    private FieldConfigSchemeManager fieldConfigSchemeManager;
    private ProjectManager projectManager;
    private OptionsManager optionsManager;
    @ComponentImport
    private SapCreateProjectService sapCreateProjectService;

    public InnerOrderTypeCustomFieldConfigItemType(IssueManager issueManager, JiraAuthenticationContext jiraAuthenticationContext,
                                                   FieldConfigSchemeManager fieldConfigSchemeManager, ProjectManager projectManager,
                                                   OptionsManager optionsManager,
                                                   SapCreateProjectService sapCreateProjectService) {
        this.issueManager = issueManager;
        this.jiraAuthenticationContext = jiraAuthenticationContext;
        this.fieldConfigSchemeManager = fieldConfigSchemeManager;
        this.projectManager = projectManager;
        this.optionsManager = optionsManager;
        this.sapCreateProjectService = sapCreateProjectService;
    }

    @Override
    public String getDisplayName() {
        return "InnerOrderTypeCustomField";
    }

    @Override
    public String getDisplayNameKey() {
        return "";
    }

    @Override
    public String getViewHtml(FieldConfig fieldConfig, FieldLayoutItem fieldLayoutItem) {
        return fieldConfig.getCustomField().getCustomFieldType().getDescriptor().getDefaultViewHtml(fieldConfig, fieldLayoutItem);
    }

    @Override
    public String getObjectKey() {
        return "options";
    }

    @Override
    public Object getConfigurationObject(Issue issue, FieldConfig fieldConfig) {
        List<InnerOrderTypeBean> innerOrderTypeBeanList = sapCreateProjectService.listOrderType("");
        return new GenericImmutableOptions(innerOrderTypeBeanList, fieldConfig);
    }

    @Override
    public String getBaseEditUrl() {
        return "";
    }
}
