package com.eve.jql;

import com.atlassian.jira.JiraDataType;
import com.atlassian.jira.JiraDataTypes;
import com.atlassian.jira.bc.project.ProjectService;
import com.atlassian.jira.jql.operand.QueryLiteral;
import com.atlassian.jira.jql.query.QueryCreationContext;
import com.atlassian.jira.permission.ProjectPermissions;
import com.atlassian.jira.plugin.jql.function.AbstractJqlFunction;
import com.atlassian.jira.plugin.jql.function.ClauseSanitisingJqlFunction;
import com.atlassian.jira.project.Project;
import com.atlassian.jira.project.ProjectManager;
import com.atlassian.jira.security.PermissionManager;
import com.atlassian.jira.user.ApplicationUser;
import com.atlassian.jira.util.MessageSet;
import com.atlassian.jira.util.MessageSetImpl;
import com.atlassian.plugin.spring.scanner.annotation.component.Scanned;
import com.atlassian.plugin.spring.scanner.annotation.imports.JiraImport;
import com.atlassian.query.clause.TerminalClause;
import com.atlassian.query.operand.FunctionOperand;

import javax.annotation.Nonnull;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/26
 */
@Scanned
public class ValidProject extends AbstractJqlFunction //implements ClauseSanitisingJqlFunction
{
    @JiraImport
    private final ProjectService projectService;
    @JiraImport
    private final ProjectManager projectManager;
    @JiraImport
    private final PermissionManager permissionManager;

    List<String> queryProjectList = new ArrayList<>();

    public ValidProject(ProjectService projectService, ProjectManager projectManager, PermissionManager permissionManager) {
        this.projectService = projectService;
        this.projectManager = projectManager;
        this.permissionManager = permissionManager;
    }

    @Nonnull
    @Override
    public MessageSet validate(ApplicationUser applicationUser,
                               @Nonnull FunctionOperand functionOperand,
                               @Nonnull TerminalClause terminalClause) {
        queryProjectList.clear();
        MessageSet messages = new MessageSetImpl();
        final List<String> projectList = functionOperand.getArgs();
        //参数输入确认
        if (projectList.isEmpty()) {
            messages.addErrorMessage(getI18n().getText("rolefunc.bad.num.arguments", functionOperand.getName()));
            return messages;
        }
        //验证项目有效性
        for (String project : projectList) {
            ProjectService.GetProjectResult result = getProjectResult(project);
            if (!result.isValid()) {
//                result.getErrorCollection().getErrorMessages().forEach(messages::addErrorMessage);
//                return messages;
            } else {
                queryProjectList.add(project);
            }
        }
        return messages;
    }

    @Nonnull
    @Override
    public List<QueryLiteral> getValues(@Nonnull QueryCreationContext queryCreationContext,
                                        @Nonnull FunctionOperand functionOperand,
                                        @Nonnull TerminalClause terminalClause) {
        final List<String> arguments = functionOperand.getArgs();
        //参数检查
        if (queryProjectList.isEmpty()) {
            return Collections.emptyList();
        }
        //将结果转换为查询文本返回
        final List<QueryLiteral> literals = new ArrayList<>();
        for (String projectKey : queryProjectList) {
            literals.add(new QueryLiteral(functionOperand, projectKey));
        }
        return literals;
    }

    private ProjectService.GetProjectResult getProjectResult(String project){
        ProjectService.GetProjectResult result = null;
        try {
            result = projectService.getProjectById(Long.parseLong(project));
        } catch (NumberFormatException e){
            result = projectService.getProjectByKey(project);
        }
        return result;
    }

    /**
     * 最少需要接收的参数，最少需要一个项目
     * @return
     */
    @Override
    public int getMinimumNumberOfExpectedArguments() {
        return 1;
    }

    /**
     * 返回的数据类型
     * @return
     */
    @Nonnull
    @Override
    public JiraDataType getDataType() {
        return JiraDataTypes.TEXT;
    }

//    @Nonnull
//    @Override
//    public FunctionOperand sanitiseOperand(final ApplicationUser user, @Nonnull final FunctionOperand functionOperand) {
//        final List<String> projectList = functionOperand.getArgs();
//
//        //项目列表为空，返回原始参数
//        if (projectList.size() <= 0) {
//            return functionOperand;
//        }
//
//        boolean argChanged = false;
//        final List<String> newFunctionOperand = new ArrayList<>(projectList.size());
//        for (final String projectKey : projectList) {
//            final Project project = projectManager.getProjectObjByKey(projectKey);
//            if (project != null && !permissionManager.hasPermission(ProjectPermissions.BROWSE_PROJECTS, project, user)) {
//                //newFunctionOperand.add(project.getId().toString());//将无权限项目替换为ID
//                //无效或无权限项目，过滤
//                argChanged = true;
//            } else {
//                newFunctionOperand.add(projectKey);
//            }
//        }
//
//        if (argChanged) {
//            //存在无效或无权限项目，过滤
//            return new FunctionOperand(functionOperand.getName(), newFunctionOperand);
//        } else {
//            //不存在无效或无权限项目，原参数返回
//            return functionOperand;
//        }
//    }
}
