package com.eve.rest;

import com.eve.beans.ApprovalMsgOpenConfigBean;
import com.eve.beans.ResultBean;
import com.eve.services.ApprovalMsgOpenConfigService;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

/**
 * <AUTHOR>
 * @date 2022/12/1
 */
@Path("approval/msg")
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
public class ApprovalMsgOpenConfigRest {

    private ApprovalMsgOpenConfigService approvalMsgOpenConfigService;

    public ApprovalMsgOpenConfigRest(ApprovalMsgOpenConfigService approvalMsgOpenConfigService) {
        this.approvalMsgOpenConfigService = approvalMsgOpenConfigService;
    }

    @Path("update/project/open")
    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response updateProjectApprovalMsgStatus(@QueryParam("projectKey") String projectKey) {
        ResultBean resultBean = new ResultBean();
        try {
            resultBean = approvalMsgOpenConfigService.updateProjectApprovalMsgStatus(projectKey);
        } catch (Exception e) {
            e.printStackTrace();
            resultBean.setMessage(e.getMessage());
        }
        return Response.ok(resultBean).build();
    }

    @Path("get/currentStatus")
    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response getProjectApprovalMsgStatus(@QueryParam("issueId") String issueId) {
        ResultBean resultBean = new ResultBean();
        try {
            resultBean = approvalMsgOpenConfigService.updateProjectApprovalMsgStatus(issueId);
        } catch (Exception e) {
            e.printStackTrace();
            resultBean.setMessage(e.getMessage());
        }
        return Response.ok(resultBean).build();
    }
    @Path("get/msg")
    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response getIssueApprovalMsg(@QueryParam("issueId") String issueId) {
        ResultBean resultBean = new ResultBean();
        try {
            resultBean = approvalMsgOpenConfigService.getIssueApprovalMsg(issueId);
        } catch (Exception e) {
            e.printStackTrace();
            resultBean.setMessage(e.getMessage());
        }
        return Response.ok(resultBean).build();
    }
}
