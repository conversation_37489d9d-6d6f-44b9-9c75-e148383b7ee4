package com.eve.rest;

import com.atlassian.plugins.rest.common.security.AnonymousAllowed;
import com.eve.beans.ResultBean;
import com.eve.services.AttachmentService;
import org.springframework.beans.factory.annotation.Autowired;

import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.*;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

/**
 * <AUTHOR>
 * @date 2022/4/18
 */
@Path("attachment")
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
public class AttachmentRest {
    @Autowired
    private AttachmentService attachmentService;
    public AttachmentRest(AttachmentService attachmentService) {
        this.attachmentService = attachmentService;
    }

    @Path("get/unsigned/{issueKey}")
    @GET
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response queryAttachments(@PathParam("issueKey") String issueKey) {
        ResultBean resultBean = new ResultBean();
        try {
            resultBean.setValue(attachmentService.getAttachments(issueKey));
        } catch (Exception e) {
            e.printStackTrace();
            resultBean.setMessage(e.getMessage());
        }
        return Response.ok(resultBean).build();
    }

    @Path("preview")
    @GET
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @AnonymousAllowed
    public Response previewAttachment(@QueryParam("attachmentId") Long attachmentId, @Context HttpServletResponse response) {
        ResultBean resultBean = new ResultBean();
        try {
            attachmentService.previewAttachment(attachmentId,response);
            resultBean.setValue("success");
        } catch (Exception e) {
            e.printStackTrace();
            resultBean.setMessage(e.getMessage());
        }
        return Response.ok(resultBean).build();
    }

    @Path("get/cate/attachments")
    @GET
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response getCateAndAttachment(
            @QueryParam("token") String token,
            @QueryParam("issueId") Long issueId,
            @QueryParam("allAttachment") int allAttachment
    ) {
        ResultBean resultBean = new ResultBean();
        try {
            resultBean = attachmentService.getCateAndAttachment(token,issueId,allAttachment);
        } catch (Exception e) {
            e.printStackTrace();
            resultBean.setMessage(e.getMessage());
        }
        return Response.ok(resultBean).build();
    }
}
