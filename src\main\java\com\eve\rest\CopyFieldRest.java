package com.eve.rest;

import com.eve.beans.CopyFieldBean;
import com.eve.beans.ResultBean;
import com.eve.services.CopyFieldService;
import org.springframework.beans.factory.annotation.Autowired;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/9
 */
@Path("field/copy")
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
public class CopyFieldRest {

    CopyFieldService copyFieldService;
    public CopyFieldRest(CopyFieldService copyFieldService) {
        this.copyFieldService = copyFieldService;
    }

    @Path("query/{fieldType}")
    @GET
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response queryFieldList(@PathParam("fieldType") String fieldType) {
        ResultBean resultBean = new ResultBean();
        try {
            //调用service获取该类型的所有字段用于展示
            resultBean.setValue(copyFieldService.queryFieldList(fieldType));
        } catch (Exception e) {
            e.printStackTrace();
            resultBean.setMessage(e.getMessage());
        }
        return Response.ok(resultBean).build();
    }

    @Path("query/Transition/{WorkflowName}")
    @GET
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response queryTransitionByWorkflowName(@PathParam("WorkflowName") String WorkflowName) {
        ResultBean resultBean = new ResultBean();
        try {
            //调用service获取该类型的所有字段用于展示
            resultBean.setValue(copyFieldService.queryTransitionByWorkflowName(WorkflowName));
        } catch (Exception e) {
            e.printStackTrace();
            resultBean.setMessage(e.getMessage());
        }
        return Response.ok(resultBean).build();
    }
}
