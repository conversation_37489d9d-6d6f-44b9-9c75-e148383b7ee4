package com.eve.rest;

import com.atlassian.plugins.rest.common.security.AnonymousAllowed;
import com.eve.beans.ResultBean;
import com.eve.beans.UpdateCustomFiledBean;
import com.eve.beans.WorkflowBean;
import com.eve.services.CustomToolService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;

import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.*;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/2/10
 */
@Path("tool")
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
public class CustomToolRest {
    @Autowired
    CustomToolService customToolService;

    @Path("update/process")
    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response updateIssueProcess(Map<String,String> map) {
        ResultBean resultBean;
        try {
            resultBean = customToolService.updateIssueProcess(map);
        } catch (Exception e) {
            e.printStackTrace();
            resultBean = new ResultBean();
            resultBean.setMessage(e.getMessage());
        }
        return Response.ok(resultBean).build();
    }

    @Path("/issue/create")
    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @AnonymousAllowed
    public Response createIssue(
            @QueryParam("isOnline") int isOnline,
            UpdateCustomFiledBean updateCustomFiledBean
    ) {
        ResultBean resultBean = new ResultBean();
        try {
            resultBean = customToolService.createIssue(isOnline,updateCustomFiledBean);
        } catch (Exception e) {
            e.printStackTrace();
            resultBean.setMessage(e.getMessage());
        }
        return Response.ok(resultBean).build();
    }

    @Path("/subIssue/create")
    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @AnonymousAllowed
    public Response createSubIssue(
            @QueryParam("isOnline") int isOnline,
            UpdateCustomFiledBean updateCustomFiledBean
    ) {
        ResultBean resultBean = new ResultBean();
        try {
            resultBean = customToolService.createSubIssue(isOnline,updateCustomFiledBean);
        } catch (Exception e) {
            e.printStackTrace();
            resultBean.setMessage(e.getMessage());
        }
        return Response.ok(resultBean).build();
    }

    @Path("get/transition")
    @GET
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response getTransitionByIssue(
            @QueryParam("userName") String userName,
            @QueryParam("issueId") Long issueId) {
        ResultBean resultBean;
        try {
            resultBean = customToolService.getTransitionByIssue(userName, issueId);
        } catch (Exception e) {
            e.printStackTrace();
            resultBean = new ResultBean();
            resultBean.setMessage(e.getMessage());
        }
        return Response.ok(resultBean).build();
    }

    @Path("transition/id")
    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @AnonymousAllowed
    public Response runTransitionById(
            @QueryParam("isOnline") int isOnline,
            UpdateCustomFiledBean updateCustomFiledBean
    ) {
        ResultBean resultBean = new ResultBean();
        try {
            resultBean = customToolService.runTransitionById(isOnline,updateCustomFiledBean);
        } catch (Exception e) {
            e.printStackTrace();
            resultBean.setMessage(e.getMessage());
        }
        return Response.ok(resultBean).build();
    }

    @Path("transition/name")
    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
//    @AnonymousAllowed
    public Response runTransitionByName(
            @QueryParam("isOnline") int isOnline,
            @QueryParam("token") String token,
            UpdateCustomFiledBean updateCustomFiledBean
    ) {
        ResultBean resultBean = new ResultBean();
        try {
            resultBean = customToolService.runTransitionByName(isOnline,token, updateCustomFiledBean);
        } catch (Exception e) {
            e.printStackTrace();
            resultBean.setMessage(e.getMessage());
        }
        return Response.ok(resultBean).build();
    }


    @Path("get/selectOption")
    @GET
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response getJiraSelectOption(@QueryParam("fieldName") String fieldName) {
        ResultBean resultBean;
        try {
            resultBean = customToolService.getJiraSelectOption(fieldName);
        } catch (Exception e) {
            e.printStackTrace();
            resultBean = new ResultBean();
            resultBean.setMessage(e.getMessage());
        }
        return Response.ok(resultBean).build();
    }

    @Path("check/user")
    @GET
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response checkUser(@QueryParam("userName") String userName) {
        ResultBean resultBean;
        try {
            resultBean = customToolService.checkUser(userName);
        } catch (Exception e) {
            e.printStackTrace();
            resultBean = new ResultBean();
            resultBean.setMessage(e.getMessage());
        }
        return Response.ok(resultBean).build();
    }

    @Path("download/attachment")
    @GET
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @AnonymousAllowed
    public Response downloadAttachment(@QueryParam("id") Long attachmentId, @Context HttpServletResponse response) {
        ResultBean resultBean = new ResultBean();
        try {
            Assert.notNull(attachmentId,"请输入附件id");
            customToolService.downloadAttachment(attachmentId,response);
            resultBean.setValue("success");
        } catch (Exception e) {
            e.printStackTrace();
            resultBean.setMessage(e.getMessage());
        }
        return Response.ok(resultBean).build();
    }



    @Path("webhook/issueCreate/{issueId}")
    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @AnonymousAllowed
    public Response issueCreateWebhook(@PathParam("issueId") String issueId) {
        ResultBean resultBean;
        try {
            resultBean = customToolService.issueCreateWebhook(issueId);
        } catch (Exception e) {
            e.printStackTrace();
            resultBean = new ResultBean();
            resultBean.setMessage(e.getMessage());
        }
        return Response.ok(resultBean).build();
    }

    @Path("webhook/issueUpdate/{issueId}")
    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @AnonymousAllowed
    public synchronized Response issueUpdateWebhook(@PathParam("issueId") String issueId) {
        ResultBean resultBean;
        try {
            resultBean = customToolService.issueUpdateWebhook(issueId);
        } catch (Exception e) {
            e.printStackTrace();
            resultBean = new ResultBean();
            resultBean.setMessage(e.getMessage());
        }
        return Response.ok(resultBean).build();
    }

    @Path("webhook/repeatedApprovalJump/{issueId}")
    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @AnonymousAllowed
    public synchronized Response repeatedApprovalJumpWebhook(@PathParam("issueId") String issueId) {
        ResultBean resultBean;
        try {
            resultBean = customToolService.repeatedApprovalJumpWebhook(issueId);
        } catch (Exception e) {
            e.printStackTrace();
            resultBean = new ResultBean();
            resultBean.setMessage(e.getMessage());
        }
        return Response.ok(resultBean).build();
    }

    @Path("workFlow/getTransition")
    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @AnonymousAllowed
    public synchronized Response getTransitionByWorkFlow(WorkflowBean workflowBean) {
        ResultBean resultBean;
        try {
            resultBean = customToolService.getTransitionByWorkFlow(workflowBean);
        } catch (Exception e) {
            e.printStackTrace();
            resultBean = new ResultBean();
            resultBean.setMessage(e.getMessage());
        }
        return Response.ok(resultBean).build();
    }

}
