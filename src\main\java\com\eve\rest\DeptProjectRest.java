package com.eve.rest;


import com.eve.beans.DeptProjectBean;
import com.eve.beans.ResultBean;
import com.eve.services.DeptProjectService;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

@Path("dept/project")
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
public class DeptProjectRest {

    private DeptProjectService deptProjectService;
    public DeptProjectRest(DeptProjectService deptProjectService) {
        this.deptProjectService = deptProjectService;
    }
    
    @Path("insert")
    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response insert(DeptProjectBean deptProjectBean) {
        ResultBean resultBean = new ResultBean();
        try {
            resultBean = deptProjectService.insert(deptProjectBean);
        } catch (Exception e) {
            e.printStackTrace();
            resultBean.setMessage(e.getMessage());
        }
        return Response.ok(resultBean).build();
    }

    @Path("delete/{deptprojectid}")
    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response delete(@PathParam("deptprojectid")Long deptProjectId) {
        ResultBean resultBean = new ResultBean();
        try {
            resultBean = deptProjectService.delete(deptProjectId);
        } catch (Exception e) {
            e.printStackTrace();
            resultBean.setMessage(e.getMessage());
        }
        return Response.ok(resultBean).build();
    }

    @Path("get/{deptprojectid}")
    @GET
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response getById(@PathParam("deptprojectid") Long deptProjectId) {
        ResultBean resultBean = new ResultBean();
        try {
            resultBean = deptProjectService.getById(deptProjectId);
        } catch (Exception e) {
            e.printStackTrace();
            resultBean.setMessage(e.getMessage());
        }
        return Response.ok(resultBean).build();
    }
}
