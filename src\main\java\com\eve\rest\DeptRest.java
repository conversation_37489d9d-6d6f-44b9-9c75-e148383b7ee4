package com.eve.rest;

import com.atlassian.jira.component.ComponentAccessor;
import com.atlassian.jira.issue.context.JiraContextNode;
import com.atlassian.jira.issue.context.ProjectContext;
import com.atlassian.jira.issue.customfields.option.Option;
import com.atlassian.jira.issue.customfields.option.Options;
import com.atlassian.jira.issue.fields.CustomField;
import com.eve.beans.DeptBean;

import com.eve.beans.ResultBean;
import com.eve.utils.Constant;
import com.eve.utils.Utils;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.util.ArrayList;
import java.util.List;

@Path("dept")
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
public class DeptRest {

    @Path("parents")
    @GET
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response getParents() {
        ResultBean resultBean = new ResultBean();
        try {
            CustomField testCustfield = Utils.getCustomFieldByID(Constant.subDepartCustID);
            JiraContextNode jiraContextNode = new ProjectContext(Constant.travelReportProjectId, ComponentAccessor.getProjectManager());
            Options options = testCustfield.getOptions("", jiraContextNode);
            List<DeptBean> deptBeans = new ArrayList();
            for (Option item : options) {
                deptBeans.add(new DeptBean(item.getValue(), item.getOptionId()));
            }
            resultBean.setValue(deptBeans);

        } catch (Exception e) {
            e.printStackTrace();
            resultBean.setMessage(e.getMessage());
        }
        return Response.ok(resultBean).build();
    }

    @Path("childs/{parent}")
    @GET
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response getChilds(@PathParam("parent") Long parent) {
        ResultBean resultBean = new ResultBean();
        try {
            CustomField testCustfield = Utils.getCustomFieldByID(Constant.subDepartCustID);
            JiraContextNode jiraContextNode = new ProjectContext(Constant.travelReportProjectId, ComponentAccessor.getProjectManager());

            Options options = testCustfield.getOptions("", jiraContextNode);
            List<DeptBean> deptBeans = new ArrayList();
            List<Option> childOptions = new ArrayList<Option>();
            for (Option item : options) {
                if (parent.equals(item.getOptionId())) {
                    childOptions.addAll(item.getChildOptions());
                    break;
                }
            }
            for (Option item : childOptions) {
                deptBeans.add(new DeptBean(item.getValue(), item.getOptionId()));
            }
            resultBean.setValue(deptBeans);

        } catch (Exception e) {
            e.printStackTrace();
            resultBean.setMessage(e.getMessage());
        }
        return Response.ok(resultBean).build();
    }
}
