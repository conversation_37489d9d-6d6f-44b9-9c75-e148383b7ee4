package com.eve.rest;

import com.eve.beans.DeptProjectBean;
import com.eve.beans.DeptsBean;
import com.eve.beans.ResultBean;
import com.eve.services.DeptProjectService;
import com.eve.services.DeptsService;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

@Path("depts")
public class DeptsRest {

    private DeptsService deptsService;
    public DeptsRest(DeptsService deptsService) {
        this.deptsService = deptsService;
    }

    @Path("insert")
    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response insert(DeptsBean deptsBean) {
        ResultBean resultBean = new ResultBean();
        try {
            resultBean = deptsService.insert(deptsBean);
        } catch (Exception e) {
            e.printStackTrace();
            resultBean.setMessage(e.getMessage());
        }
        return Response.ok(resultBean).build();
    }

    @Path("delete/{deptid}")
    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response delete(@PathParam("deptid")Long deptId) {
        ResultBean resultBean = new ResultBean();
        try {
            resultBean = deptsService.delete(deptId);
        } catch (Exception e) {
            e.printStackTrace();
            resultBean.setMessage(e.getMessage());
        }
        return Response.ok(resultBean).build();
    }

    @Path("get/{deptid}")
    @GET
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response getById(@PathParam("deptid") Long deptId) {
        ResultBean resultBean = new ResultBean();
        try {
            resultBean = deptsService.getById(deptId);
        } catch (Exception e) {
            e.printStackTrace();
            resultBean.setMessage(e.getMessage());
        }
        return Response.ok(resultBean).build();
    }
    
}
