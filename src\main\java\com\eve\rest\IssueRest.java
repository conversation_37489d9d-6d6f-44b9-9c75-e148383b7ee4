package com.eve.rest;

import com.eve.beans.ResultBean;
import com.eve.beans.TripRequestBean;
import com.eve.services.DeptProjectService;
import com.eve.services.IssueService;
import org.springframework.beans.factory.annotation.Autowired;

import javax.ws.rs.Consumes;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

@Path("issue")
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
public class IssueRest {
    @Autowired
    private IssueService issueService;

    public IssueRest(IssueService issueService) {
        this.issueService = issueService;
    }

    @POST
    @Path("create")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response createIssue(TripRequestBean tripRequestBean) {
        ResultBean resultBean;
        try {
            resultBean = issueService.createIssue(tripRequestBean);
        } catch (Exception e) {
            e.printStackTrace();
            resultBean = new ResultBean();
            resultBean.setMessage(e.getMessage());
        }
        return Response.ok(resultBean).build();
    }

}
