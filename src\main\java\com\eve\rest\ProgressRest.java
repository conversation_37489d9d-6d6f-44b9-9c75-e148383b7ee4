package com.eve.rest;

import com.eve.beans.ProgressBean;
import com.eve.beans.ResultBean;
import com.eve.services.ProgressService;
import org.springframework.beans.factory.annotation.Autowired;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

/**
 * <AUTHOR>
 * @date 2022/4/6
 */
@Path("progress")
public class ProgressRest {
    @Autowired
    private ProgressService progressService;

    public ProgressRest(ProgressService progressService) {
        this.progressService = progressService;
    }

    @Path("list/{issueid}/p/{num}")
    @GET
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response getList(@PathParam("issueid") Long issueId, @PathParam("num") int num) {
        ResultBean resultBean = new ResultBean();
        try {
            resultBean.setValue(progressService.list(issueId,num));
            resultBean.setPageNum(progressService.getCount(issueId));
            resultBean.setCur(num);

        } catch (Exception e) {
            e.printStackTrace();
            resultBean.setMessage(e.getMessage());
        }
        return Response.ok(resultBean).build();
    }

    @Path("listbystage/{issueid}/{key}")
    @GET
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response getListByStage(@PathParam("issueid") Long issueId,@PathParam("key") String key) {
        ResultBean resultBean = new ResultBean();
        try {
            resultBean.setValue(progressService.getByName(issueId,key));
        } catch (Exception e) {
            e.printStackTrace();
            resultBean.setMessage(e.getMessage());
        }
        return Response.ok(resultBean).build();
    }

    @Path("insert")
    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response insert(ProgressBean progressBean) {
        ResultBean resultBean = new ResultBean();
        try {
            resultBean = progressService.insert(progressBean);
        } catch (Exception e) {
            e.printStackTrace();
            resultBean.setMessage(e.getMessage());
        }
        return Response.ok(resultBean).build();
    }

    @Path("delete/{processid}")
    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response delete(@PathParam("processid")Long processid) {
        ResultBean resultBean = new ResultBean();
        try {
            resultBean = progressService.delete(processid);
        } catch (Exception e) {
            e.printStackTrace();
            resultBean.setMessage(e.getMessage());
        }
        return Response.ok(resultBean).build();
    }

    @Path("get/{processid}")
    @GET
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response getById(@PathParam("processid") Long processId) {
        ResultBean resultBean = new ResultBean();
        try {
            resultBean = progressService.getById(processId);
        } catch (Exception e) {
            e.printStackTrace();
            resultBean.setMessage(e.getMessage());
        }
        return Response.ok(resultBean).build();
    }
}
