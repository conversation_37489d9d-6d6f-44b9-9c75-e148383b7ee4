package com.eve.rest;

import com.eve.beans.ProjectQueryParamBean;
import com.eve.beans.ResultBean;
import com.eve.services.ProjectQueryService;
import org.springframework.beans.factory.annotation.Autowired;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

/**
 * <AUTHOR>
 * @date 2022/10/24
 */
@Path("project/info")
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
public class ProjectQueryRest {
    @Autowired
    ProjectQueryService projectQueryService;

    @Path("query/project")
    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response queryProject(ProjectQueryParamBean projectQueryParamBean) {
        ResultBean resultBean = new ResultBean();
        try {
            resultBean = projectQueryService.queryProject(projectQueryParamBean);
        } catch (Exception e) {
            e.printStackTrace();
            resultBean.setMessage(e.getMessage());
        }
        return Response.ok(resultBean).build();
    }

    @Path("query/testReport")
    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response queryTestReport(ProjectQueryParamBean projectQueryParamBean) {
        ResultBean resultBean = new ResultBean();
        try {
            resultBean = projectQueryService.queryTestReport(projectQueryParamBean);
        } catch (Exception e) {
            e.printStackTrace();
            resultBean.setMessage(e.getMessage());
        }
        return Response.ok(resultBean).build();
    }

    @Path("query/product")
    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response queryProductManage(ProjectQueryParamBean projectQueryParamBean) {
        ResultBean resultBean = new ResultBean();
        try {
            resultBean = projectQueryService.queryProductManage(projectQueryParamBean);
        } catch (Exception e) {
            e.printStackTrace();
            resultBean.setMessage(e.getMessage());
        }
        return Response.ok(resultBean).build();
    }
    @Path("query/travelReport")
    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response queryTravelReport(ProjectQueryParamBean projectQueryParamBean) {
        ResultBean resultBean = new ResultBean();
        try {
            resultBean = projectQueryService.queryTravelReport(projectQueryParamBean);
        } catch (Exception e) {
            e.printStackTrace();
            resultBean.setMessage(e.getMessage());
        }
        return Response.ok(resultBean).build();
    }
    @Path("query/achievementManage")
    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response queryAchievementManage(ProjectQueryParamBean projectQueryParamBean) {
        ResultBean resultBean = new ResultBean();
        try {
            resultBean = projectQueryService.queryAchievementManage(projectQueryParamBean);
        } catch (Exception e) {
            e.printStackTrace();
            resultBean.setMessage(e.getMessage());
        }
        return Response.ok(resultBean).build();
    }
}
