package com.eve.rest;

import com.atlassian.plugins.rest.common.security.AnonymousAllowed;
import com.eve.beans.ResultBean;
import com.eve.beans.UpdateCustomFiledBean;
import com.eve.beans.UpdateCustomFiledListBean;
import com.eve.services.ProjectReportService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/11/16
 */
@Path("project/report")
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
public class ProjectReportRest {
    @Autowired
    ProjectReportService projectReportService;

    @Path("projectcates")
    @GET
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @AnonymousAllowed
    @Deprecated
    public Response getProjectCates() {
        ResultBean resultBean = new ResultBean();
        try {
            resultBean = projectReportService.getProjectCates();
        } catch (Exception e) {
            e.printStackTrace();
            resultBean.setMessage(e.getMessage());
        }
        return Response.ok(resultBean).build();
    }
    @Path("get/cascadingselect")
    @GET
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @AnonymousAllowed
    public Response getJiraCascadingSelect(@QueryParam("fieldName") String fieldName) {
        ResultBean resultBean = new ResultBean();
        try {
            resultBean = projectReportService.getJiraCascadingSelect(fieldName);
        } catch (Exception e) {
            e.printStackTrace();
            resultBean.setMessage(e.getMessage());
        }
        return Response.ok(resultBean).build();
    }

    @Path("projects")
    @GET
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @AnonymousAllowed
    public Response getProjects(
            @QueryParam("token") String token,
            @QueryParam("startDate") String startDate,
            @QueryParam("endDate") String endDate,
            @DefaultValue("ALL")@QueryParam("area") String area,
            @QueryParam("isOnline") Integer isOnline
    ) {
        ResultBean resultBean = new ResultBean();
        try {
            resultBean = projectReportService.getProjectByCate(token, null, null, startDate, endDate, area, isOnline);
        } catch (Exception e) {
            e.printStackTrace();
            resultBean.setMessage(e.getMessage());
        }
        return Response.ok(resultBean).build();
    }
    @Path("delay")
    @GET
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @AnonymousAllowed
    public Response getDelayProjects(@QueryParam("token") String token, @QueryParam("isOnline") int isOnline) {
        ResultBean resultBean = new ResultBean();
        try {
            resultBean = projectReportService.getDelayProjects(token, isOnline);
        } catch (Exception e) {
            e.printStackTrace();
            resultBean.setMessage(e.getMessage());
        }
        return Response.ok(resultBean).build();
    }

    @Path("projectsbycates")
    @GET
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @AnonymousAllowed
    public Response getProjectByCate(
            @QueryParam("token") String token,
            @QueryParam("parentid") Long parentId,
            @QueryParam("cateid") Long cateId,
            @DefaultValue("ALL")@QueryParam("area") String area,
            @QueryParam("isOnline") int isOnline
    ) {
        ResultBean resultBean = new ResultBean();
        try {
            resultBean = projectReportService.getProjectByCate(token, parentId, cateId, null, null, area, isOnline);
        } catch (Exception e) {
            e.printStackTrace();
            resultBean.setMessage(e.getMessage());
        }
        return Response.ok(resultBean).build();
    }

    @Path("reviewlist")
    @GET
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @AnonymousAllowed
    public Response getProjectReviewList(
            @QueryParam("token") String token,
            @QueryParam("reviewListType") Long reviewListType,
            @QueryParam("reviewMonth") String reviewMonth,
            @QueryParam("startCreateDate") String startCreateDate,
            @QueryParam("endCreateDate") String endCreateDate,
            @QueryParam("isOnline") int isOnline,
            @DefaultValue("ALL")@QueryParam("area") String area
    ) {
        ResultBean resultBean = new ResultBean();
        try {
            resultBean = projectReportService.getProjectReviewList(token, reviewListType, reviewMonth, startCreateDate,endCreateDate,area,isOnline);
        } catch (Exception e) {
            e.printStackTrace();
            resultBean.setMessage(e.getMessage());
        }
        return Response.ok(resultBean).build();
    }

    //根据传递的条件查询评审清单
    @Path("reviewlist/condition")
    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @AnonymousAllowed
    public Response getProjectReviewList(
            @QueryParam("token") String token,
            @QueryParam("isOnline") int isOnline,
            UpdateCustomFiledBean updateCustomFiledBean
    ) {
        ResultBean resultBean = new ResultBean();
        try {
            resultBean = projectReportService.getProjectReviewList(token, isOnline,updateCustomFiledBean);
        } catch (Exception e) {
            e.printStackTrace();
            resultBean.setMessage(e.getMessage());
        }
        return Response.ok(resultBean).build();
    }

    @Path("transition/name")
    @GET
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @AnonymousAllowed
    public Response runTransitionByName(
            @QueryParam("token") String token,
            @QueryParam("issueId") Long issueId,
            @QueryParam("transitionName") String transitionName,
            @QueryParam("isSystemCall") String isSystemCall
    ) {
        ResultBean resultBean = new ResultBean();
        try {
            UpdateCustomFiledBean updateCustomFiledBean = new UpdateCustomFiledBean();
            updateCustomFiledBean.setIssueId(issueId);
            if ("1".equals(isSystemCall)) {
                Map<String, String> map = updateCustomFiledBean.getMap();
                if (ObjectUtils.isEmpty(map)) {
                    map = new HashMap<>();
                }
                map.put("isSystemCall", isSystemCall);
                updateCustomFiledBean.setMap(map);
            }
            if ("再确认".equals(transitionName)) {
                transitionName = "驳回修改";
            }
            resultBean = projectReportService.runTransitionByName(token, transitionName, updateCustomFiledBean);
        } catch (Exception e) {
            e.printStackTrace();
            resultBean.setMessage(e.getMessage());
        }
        return Response.ok(resultBean).build();
    }

    @Path("transition/name/list")
    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @AnonymousAllowed
    public Response runTransitionByNameList(
            @QueryParam("token") String token,
            UpdateCustomFiledListBean updateCustomFiledListBean
    ) {
        ResultBean resultBean = new ResultBean();
        try {
            List<UpdateCustomFiledBean> updateCustomFiledBeanList = updateCustomFiledListBean.getUpdateCustomFiledBeanList();
            updateCustomFiledBeanList.forEach(updateCustomFiledBean -> {
                if ("再确认".equals(updateCustomFiledBean.getTransitionName())) {
                    updateCustomFiledBean.setTransitionName("驳回修改");
                }
            });
            resultBean = projectReportService.runTransitionByNameList(token, updateCustomFiledBeanList);
        } catch (Exception e) {
            e.printStackTrace();
            resultBean.setMessage(e.getMessage());
        }
        return Response.ok(resultBean).build();
    }

    @Path("update")
    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @AnonymousAllowed
    public Response updateCustomFiled(
            @QueryParam("token") String token,
            @QueryParam("isOnline") int isOnline,
            UpdateCustomFiledBean updateCustomFiledBean
    ) {

        ResultBean resultBean = new ResultBean();
        try {
            resultBean = projectReportService.updateCustomFiled(token, isOnline, updateCustomFiledBean);
        } catch (Exception e) {
            e.printStackTrace();
            resultBean.setMessage(e.getMessage());
        }
        return Response.ok(resultBean).build();
    }

    @Path("create")
    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @AnonymousAllowed
    public Response createProject(
            @QueryParam("token") String token,
            @QueryParam("isOnline") int isOnline,
            UpdateCustomFiledBean updateCustomFiledBean
    ) {
        return updateCustomFiled(token, isOnline,updateCustomFiledBean);
    }

    @Path("review/pass")
    @GET
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @AnonymousAllowed
    public Response queryInitiationPassRate(
            @QueryParam("token") String token,
            @QueryParam("startDate") String startDate,
            @QueryParam("endDate") String endDate,
            @DefaultValue("ALL")@QueryParam("area") String area
    ) {
        ResultBean resultBean = new ResultBean();
        try {
            resultBean = projectReportService.queryInitiationPassRate(token, startDate,endDate,area);
        } catch (Exception e) {
            e.printStackTrace();
            resultBean.setMessage(e.getMessage());
        }
        return Response.ok(resultBean).build();
    }
}
