package com.eve.rest;

import com.atlassian.plugins.rest.common.security.AnonymousAllowed;
import com.eve.beans.ResultBean;
import com.eve.beans.UpdateCustomFiledBean;
import com.eve.beans.UpdateCustomFiledListBean;
import com.eve.services.ProjectReportService;
import com.eve.services.ResultService;
import org.springframework.beans.factory.annotation.Autowired;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/1/11
 */
@Path("result/report")
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
public class ResultRest {
    @Autowired
    ResultService resultService;
    @Autowired
    private ProjectReportService projectReportService;

    @Path("topicResult")
    @GET
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @AnonymousAllowed
    public Response getAllResult(@DefaultValue("ALL")@QueryParam("area") String area,@QueryParam("isOnline") Integer isOnline) {
        ResultBean resultBean = new ResultBean();
        try {
            resultBean = resultService.getAllResult(area,isOnline);
        } catch (Exception e) {
            e.printStackTrace();
            resultBean.setMessage(e.getMessage());
        }
        return Response.ok(resultBean).build();
    }

    @Path("resultReviewRequest/check")
    @GET
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response getReviewResult(@QueryParam("issueId") Long issueId) {
        ResultBean resultBean = new ResultBean();
        try {
            resultBean = resultService.getResultReviewRequest(issueId);
        } catch (Exception e) {
            e.printStackTrace();
            resultBean.setMessage(e.getMessage());
        }
        return Response.ok(resultBean).build();
    }

    @Path("reviewResult")
    @GET
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response getResultReviewRequest(@QueryParam("issueId") Long issueId) {
        ResultBean resultBean = new ResultBean();
        try {
            resultBean = resultService.getReviewResult(issueId);
        } catch (Exception e) {
            e.printStackTrace();
            resultBean.setMessage(e.getMessage());
        }
        return Response.ok(resultBean).build();
    }

    @Path("resultReviewRequest/create")
    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @AnonymousAllowed
    public Response resultReviewRequestCreate(
            @QueryParam("isOnline") int isOnline,
            UpdateCustomFiledBean updateCustomFiledBean
    ) {
        ResultBean resultBean = new ResultBean();
        try {
            resultBean = resultService.resultReviewRequestCreate(isOnline,updateCustomFiledBean);
        } catch (Exception e) {
            e.printStackTrace();
            resultBean.setMessage(e.getMessage());
        }
        return Response.ok(resultBean).build();
    }

    @Path("transition/name/list")
    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @AnonymousAllowed
    public Response runTransitionByNameList(
            @QueryParam("token") String token,
            UpdateCustomFiledListBean updateCustomFiledListBean
    ) {
        ResultBean resultBean = new ResultBean();
        try {
            List<UpdateCustomFiledBean> updateCustomFiledBeanList = updateCustomFiledListBean.getUpdateCustomFiledBeanList();
            updateCustomFiledBeanList.forEach(updateCustomFiledBean -> {
                if ("通过".equals(updateCustomFiledBean.getTransitionName())) {
                    updateCustomFiledBean.setTransitionName("批准");
                }
            });
            resultBean = projectReportService.runTransitionByNameList(token, updateCustomFiledBeanList);
        } catch (Exception e) {
            e.printStackTrace();
            resultBean.setMessage(e.getMessage());
        }
        return Response.ok(resultBean).build();
    }

    @Path("subReview")
    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @AnonymousAllowed
    public Response getGainSubReviewIssue(
            @QueryParam("token") String token,
            UpdateCustomFiledBean updateCustomFiledBean
    ) {
        ResultBean resultBean = new ResultBean();
        try {
            resultBean = resultService.getGainSubReviewIssue(token, updateCustomFiledBean);
        } catch (Exception e) {
            e.printStackTrace();
            resultBean.setMessage(e.getMessage());
        }
        return Response.ok(resultBean).build();
    }
}
