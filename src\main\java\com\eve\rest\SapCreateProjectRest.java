package com.eve.rest;

import com.eve.beans.InnerOrderTypeBean;
import com.eve.beans.ResultBean;
import com.eve.services.SapCreateProjectService;
import org.springframework.beans.factory.annotation.Autowired;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

/**
 * <AUTHOR>
 * @date 2022/5/31
 */
@Path("create/project")
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
public class SapCreateProjectRest {
    @Autowired
    SapCreateProjectService sapCreateProjectService;

    @Path("update/cost")
    @GET
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response updateCostCenter() {
        ResultBean resultBean = new ResultBean();
        try {
            resultBean = sapCreateProjectService.updateCostCenter();
        } catch (Exception e) {
            e.printStackTrace();
            resultBean.setMessage(e.getMessage());
        }
        return Response.ok(resultBean).build();
    }
    @Path("list/cost/{num}")
    @GET
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response listCostCenter(@PathParam("num") int curNum) {
        ResultBean resultBean = new ResultBean();
        try {
            resultBean = sapCreateProjectService.listCostCenter(curNum,"");
        } catch (Exception e) {
            e.printStackTrace();
            resultBean.setMessage(e.getMessage());
        }
        return Response.ok(resultBean).build();
    }
    @Path("list/cost/{num}/{costCenterName}")
    @GET
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response listCostCenterByName(@PathParam("num") int curNum,@PathParam("costCenterName") String costCenterName) {
        ResultBean resultBean = new ResultBean();
        try {
            resultBean = sapCreateProjectService.listCostCenter(curNum,costCenterName);
        } catch (Exception e) {
            e.printStackTrace();
            resultBean.setMessage(e.getMessage());
        }
        return Response.ok(resultBean).build();
    }

    @Path("orderType/update")
    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response updateOrderType(InnerOrderTypeBean innerOrderTypeBean) {
        ResultBean resultBean = new ResultBean();
        try {
            resultBean = sapCreateProjectService.updateOrderType(innerOrderTypeBean);
        } catch (Exception e) {
            e.printStackTrace();
            resultBean.setMessage(e.getMessage());
        }
        return Response.ok(resultBean).build();
    }
    @Path("orderType/delete/{id}")
    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response deleteOrderType(@PathParam("id") Long id) {
        ResultBean resultBean = new ResultBean();
        try {
            resultBean = sapCreateProjectService.deleteOrderType(id);
        } catch (Exception e) {
            e.printStackTrace();
            resultBean.setMessage(e.getMessage());
        }
        return Response.ok(resultBean).build();
    }
    @Path("orderType/get/{id}")
    @GET
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response getOrderType(@PathParam("id") Long id) {
        ResultBean resultBean = new ResultBean();
        try {
            resultBean = sapCreateProjectService.getOrderType(id);
        } catch (Exception e) {
            e.printStackTrace();
            resultBean.setMessage(e.getMessage());
        }
        return Response.ok(resultBean).build();
    }
}
