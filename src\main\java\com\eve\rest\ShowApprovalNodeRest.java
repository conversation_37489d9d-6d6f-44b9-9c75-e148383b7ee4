package com.eve.rest;

import com.eve.beans.ResultBean;
import com.eve.services.ShowApprovalNodeService;
import org.springframework.beans.factory.annotation.Autowired;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

/**
 * <AUTHOR>
 * @date 2022/10/26
 */
@Path("approval/node")
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
public class ShowApprovalNodeRest {
    @Autowired
    private ShowApprovalNodeService showApprovalNodeService;

    @Path("get/msg")
    @GET
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response getNodeMsg(@QueryParam("issueId") Long issueId) {
        ResultBean resultBean = new ResultBean();
        try {
            resultBean = showApprovalNodeService.getNodeMsg(issueId);
        } catch (Exception e) {
            e.printStackTrace();
            resultBean.setMessage(e.getMessage());
        }
        return Response.ok(resultBean).build();
    }
}
