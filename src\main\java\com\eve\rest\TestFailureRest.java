package com.eve.rest;

import com.atlassian.plugins.rest.common.security.AnonymousAllowed;
import com.eve.beans.ResultBean;
import com.eve.beans.TripRequestBean;
import com.eve.beans.UpdateCustomFiledBean;
import com.eve.services.TestFailureService;
import com.eve.services.TripReportService;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

/**
 * <AUTHOR>
 * @since 2023/9/13
 */
@Path("/testFailure")
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
public class TestFailureRest {
//    @Autowired
    private TestFailureService testFailureService;

    public TestFailureRest(TestFailureService testFailureService) {
        this.testFailureService = testFailureService;
    }

    @POST
    @Path("/create")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response createTestFailure(@QueryParam("isOnline") int isOnline, UpdateCustomFiledBean updateCustomFiledBean) {
        ResultBean resultBean;
        try {
            resultBean = testFailureService.createTestFailure(isOnline,updateCustomFiledBean);
        } catch (Exception e) {
            e.printStackTrace();
            resultBean = new ResultBean();
            resultBean.setMessage(e.getMessage());
        }
        return Response.ok(resultBean).build();
    }

//    @POST
//    @Path("report/edit")
//    @Consumes(MediaType.APPLICATION_JSON)
//    @Produces(MediaType.APPLICATION_JSON)
//    @AnonymousAllowed
//    public Response editTripReport(TripRequestBean tripRequestBean) {
//        ResultBean resultBean;
//        try {
//            resultBean = testFailureService.editTripReport(tripRequestBean);
//        } catch (Exception e) {
//            e.printStackTrace();
//            resultBean = new ResultBean();
//            resultBean.setMessage(e.getMessage());
//        }
//        return Response.ok(resultBean).build();
//    }

}
