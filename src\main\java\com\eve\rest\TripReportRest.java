package com.eve.rest;

import com.atlassian.plugins.rest.common.security.AnonymousAllowed;
import com.eve.beans.ResultBean;
import com.eve.beans.TripRequestBean;
import com.eve.services.IssueService;
import com.eve.services.TripReportService;
import org.springframework.beans.factory.annotation.Autowired;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

/**
 * <AUTHOR>
 * @since 2023/9/13
 */
@Path("trip")
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
public class TripReportRest {
//    @Autowired
    private TripReportService tripReportService;

    public TripReportRest(TripReportService tripReportService) {
        this.tripReportService = tripReportService;
    }

    @POST
    @Path("report/create")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @AnonymousAllowed
    public Response createTripReport(@QueryParam("isOnline") int isOnline,TripRequestBean tripRequestBean) {
        ResultBean resultBean;
        try {
            resultBean = tripReportService.createTripReport(isOnline,tripRequestBean);
        } catch (Exception e) {
            e.printStackTrace();
            resultBean = new ResultBean();
            resultBean.setMessage(e.getMessage());
        }
        return Response.ok(resultBean).build();
    }

    @POST
    @Path("report/edit")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @AnonymousAllowed
    public Response editTripReport(TripRequestBean tripRequestBean) {
        ResultBean resultBean;
        try {
            resultBean = tripReportService.editTripReport(tripRequestBean);
        } catch (Exception e) {
            e.printStackTrace();
            resultBean = new ResultBean();
            resultBean.setMessage(e.getMessage());
        }
        return Response.ok(resultBean).build();
    }

}
