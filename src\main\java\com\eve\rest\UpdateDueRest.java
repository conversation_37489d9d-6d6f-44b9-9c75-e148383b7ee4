package com.eve.rest;

import com.eve.beans.ResultBean;
import com.eve.services.ServiceUpdateDue;
import org.springframework.beans.factory.annotation.Autowired;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

/**
 * <AUTHOR>
 * @Date 2021/11/4 16:24
 */
@Path("field/sign")
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
public class UpdateDueRest {
@Autowired
    private ServiceUpdateDue serviceUpdateDue;

    @Path("query/field/{fieldType}")
    @GET
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response queryFieldList(@PathParam("fieldType") String fieldType) {
        ResultBean resultBean = new ResultBean();
        try {
            resultBean = serviceUpdateDue.queryFieldList(fieldType);
        } catch (Exception e) {
            e.printStackTrace();
            resultBean.setMessage(e.getMessage());
        }
        return Response.ok(resultBean).build();
    }

}
