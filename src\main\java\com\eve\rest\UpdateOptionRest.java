package com.eve.rest;

import com.eve.beans.ResultBean;
import com.eve.services.ServiceUpdateDue;
import com.eve.services.ServiceUpdateOption;
import org.springframework.beans.factory.annotation.Autowired;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

/**
 * <AUTHOR>
 * @Date 2021/2/23 16:24
 */
@Path("field/option")
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)

public class UpdateOptionRest {
     @Path("query/field/{field_field}")
    @GET
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response queryFieldList(@PathParam("field_field") String field_field) {
        ResultBean resultBean = new ResultBean();
        try {
            //取选项的值
            long field_fieldl = Long.parseLong(field_field);
            resultBean = ServiceUpdateOption.queryOptionsList(field_fieldl);
        } catch (Exception e) {
            e.printStackTrace();
            resultBean.setMessage(e.getMessage());
        }
        return Response.ok(resultBean).build();
    }

}
//rest拦截VM的请求，rest ，rest调用service处理逻辑，接口return 处理结果到vm的 response