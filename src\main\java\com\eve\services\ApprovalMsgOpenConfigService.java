package com.eve.services;

import com.atlassian.activeobjects.external.ActiveObjects;
import com.atlassian.jira.component.ComponentAccessor;
import com.atlassian.jira.issue.MutableIssue;
import com.atlassian.jira.issue.changehistory.ChangeHistory;
import com.atlassian.jira.issue.changehistory.ChangeHistoryItem;
import com.atlassian.jira.issue.history.ChangeItemBean;
import com.eve.ao.ApprovalMsgOpenConfigAo;
import com.eve.beans.ApprovalMsgBean;
import com.eve.beans.ResultBean;
import com.eve.utils.Utils;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/12/1
 */
public class ApprovalMsgOpenConfigService {

    private ActiveObjects ao;

    public ApprovalMsgOpenConfigService(ActiveObjects ao) {
        this.ao = ao;
    }

    public Boolean queryProjectApprovalMsgStatus(String projectKey) {
        try {
            ApprovalMsgOpenConfigAo[] approvalMsgOpenConfigAos = ao.find(ApprovalMsgOpenConfigAo.class, "PROJECT_KEY = '" + projectKey + "'");
            if (ObjectUtils.isEmpty(approvalMsgOpenConfigAos)) {
                return false;
            } else {
                return approvalMsgOpenConfigAos[0].isOpenStatus();
            }
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    public ResultBean updateProjectApprovalMsgStatus(String projectKey) {
        ResultBean resultBean = new ResultBean();
        try {
            if (ObjectUtils.isEmpty(projectKey)) {
                resultBean.setMessage("请传递projectKey");
                return resultBean;
            }
            ApprovalMsgOpenConfigAo[] approvalMsgOpenConfigAos = ao.find(ApprovalMsgOpenConfigAo.class, "PROJECT_KEY = '" + projectKey + "'");
            if (ObjectUtils.isEmpty(approvalMsgOpenConfigAos)) {
                ApprovalMsgOpenConfigAo approvalMsgOpenConfigAo = ao.create(ApprovalMsgOpenConfigAo.class);
                approvalMsgOpenConfigAo.setProjectKey(projectKey);
                approvalMsgOpenConfigAo.setOpenStatus(true);
                approvalMsgOpenConfigAo.save();
            } else {
                for (ApprovalMsgOpenConfigAo approvalMsgOpenConfigAo : approvalMsgOpenConfigAos) {
                    approvalMsgOpenConfigAo.setOpenStatus(!approvalMsgOpenConfigAo.isOpenStatus());
                    approvalMsgOpenConfigAo.save();
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            resultBean.setMessage(e.getMessage());
        }
        return resultBean;
    }

    public ResultBean getIssueApprovalMsg(String issueId) {
        ResultBean resultBean = new ResultBean();
        try {
            if (ObjectUtils.isEmpty(issueId)) {
                resultBean.setMessage("issueId错误");
                return resultBean;
            }
            MutableIssue mutableIssue = ComponentAccessor.getIssueManager().getIssueObject(Long.parseLong(issueId));
            Long workflowId = mutableIssue.getWorkflowId();
            mutableIssue.getAssignee();
            //按变更组获取
            List<ChangeHistory> changeHistories = ComponentAccessor.getChangeHistoryManager().getChangeHistories(mutableIssue);
            List<ChangeHistory> changeHistoryList = changeHistories.stream().filter(e -> {
                List<ChangeItemBean> changeItemBeans = e.getChangeItemBeans();
                AtomicInteger i = new AtomicInteger();
                changeItemBeans.forEach(changeItemBean -> {
                    if ("status".equals(changeItemBean.getField()) || "assignee".equals(changeItemBean.getField())) {
                        i.getAndIncrement();
                    }
                });
                return i.get() == 2;
            }).collect(Collectors.toList());
            List<ApprovalMsgBean> approvalMsgBeanList = new ArrayList<>();
            for (Integer i = 0; i < changeHistoryList.size(); i++) {
                ChangeHistory changeHistory = changeHistoryList.get(i);

                ApprovalMsgBean approvalMsgBean = new ApprovalMsgBean();
                approvalMsgBean.setNo(i.longValue());
                approvalMsgBean.setOperator(changeHistory.getAuthorDisplayName());
                approvalMsgBean.setOperaTime(Utils.getDateFormat(changeHistory.getTimePerformed()));
                List<ChangeItemBean> changeItemBeanList = changeHistory.getChangeItemBeans();
                for (ChangeItemBean changeItemBean : changeItemBeanList) {
                    if ("status".equals(changeItemBean.getField())) {
                        approvalMsgBean.setStatusName(changeItemBean.getFromString());
                    }
                    if ("assignee".equals(changeItemBean.getField())) {
                        approvalMsgBean.setOpera(changeItemBean.getFromString() + "提交给了" + changeItemBean.getToString());
                    }
                }
                approvalMsgBeanList.add(approvalMsgBean);
            }
//            for (ChangeHistory changeHistory : changeHistories) {
//                for (ChangeItemBean changeItemBean : changeHistory.getChangeItemBeans()) {
//                    if (changeItemBean.getField().equals("status")) {
//                        //todo
//                    }
//                }
//            }
//            //全部获取
//            List<ChangeHistoryItem> allChangeItems = ComponentAccessor.getChangeHistoryManager().getAllChangeItems(mutableIssue);
            resultBean.setValue(approvalMsgBeanList);
        } catch (Exception e) {
            e.printStackTrace();
            resultBean.setMessage(e.getMessage());
        }
        return resultBean;
    }
}
