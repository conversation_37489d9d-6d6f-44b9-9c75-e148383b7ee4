package com.eve.services;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.atlassian.jira.component.ComponentAccessor;
import com.atlassian.jira.config.properties.APKeys;
import com.atlassian.jira.issue.Issue;
import com.atlassian.jira.issue.MutableIssue;
import com.atlassian.jira.issue.attachment.Attachment;
import com.atlassian.jira.issue.fields.CustomField;
import com.atlassian.jira.security.JiraAuthenticationContext;
import com.atlassian.jira.user.ApplicationUser;
import com.atlassian.jira.util.AttachmentUtils;
import com.eve.beans.AttachmentBean;
import com.eve.beans.ResultBean;
import com.eve.beans.SmartAttachmentCateBean;
import com.eve.utils.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

public class AttachmentService {
    private static final Logger log = LoggerFactory.getLogger(AttachmentService.class);

    @Resource
    private JiraCustomTool jiraCustomTool;

    public List<AttachmentBean> getAttachments(String issueKey) throws Exception {

        List<AttachmentBean> attachmentBeanList = new ArrayList<>();

        Issue issue = ComponentAccessor.getIssueManager().getIssueByCurrentKey(issueKey);
        String baseUrl = ComponentAccessor.getApplicationProperties().getString(APKeys.JIRA_BASEURL);
        CustomField customField = Utils.getCustomFieldByID(Constant.attachmentSignCateCustomFieldId);
        String attachCategory = String.valueOf(issue.getCustomFieldValue(customField));
        // 获取issue中的全部附件信息
        Collection<Attachment> attachments = issue.getAttachments();

        // 获取类别列表信息
        Map<String, String> headers = new HashMap<>();
        headers.put("atlas-authorization", ComponentAccessor.getComponent(JiraAuthenticationContext.class).getLoggedInUser().getUsername());
        String callback = HttpUtils.doGet(
                baseUrl+ "/rest/attach-cat/1.0/attachments?issueKey=" + issueKey,
                headers
        );
//        log.error("文件类别获取结果：{}", callback);
        List<Long> attachmentIds = new ArrayList<>();
        JSONObject jsonObject = JSON.parseObject(callback);

        List<SmartAttachmentCateBean> smartAttachmentCateBeanList = JSONObject.parseArray(jsonObject.getString("categories"), SmartAttachmentCateBean.class);

        Optional<SmartAttachmentCateBean> faBreakReportFirst = smartAttachmentCateBeanList.stream().filter(e -> attachCategory.contains(e.getName())).findFirst();
        if (faBreakReportFirst.isPresent()) {
            SmartAttachmentCateBean smartAttachmentCateBean = faBreakReportFirst.get();
            attachmentIds.addAll(smartAttachmentCateBean.getAttachmentIds());

            List<SmartAttachmentCateBean> documents = smartAttachmentCateBean.getDocuments();
            if (!ObjectUtils.isEmpty(documents)) {//有成组的同名文件，取最新的文件(文件id最大)
                documents.stream().map(SmartAttachmentCateBean::getAttachmentIds)
                        .filter(e -> !ObjectUtils.isEmpty(e))
                        .map(e -> e.stream().max(Comparator.comparing(Long::longValue)).orElse(0L))
                        .forEach(attachmentIds::add);
            }
        }


//        Map<String, Object> callbackMap = (Map<String, Object>) new Gson().fromJson(callback, Object.class);
//
//        Object callbackObject = callbackMap.get("categories");
//
//        List<Map<String, Object>> dataMapList = null;
//
//
//
//        if (callbackObject instanceof ArrayList) {
//            dataMapList = (List<Map<String, Object>>) callbackObject;
//        } else {
//            dataMapList = new ArrayList<>();
//            Map<String, Object> dataMap = (Map<String, Object>) callbackObject;
//            dataMapList.add(dataMap);
//        }
//
//        // 获取附件类别自定义字段值
//
//        log.error("文件类别字段值：{}", attachCategory);
//        Optional<Map<String, Object>> datamapOptional = dataMapList.stream().filter(e -> attachCategory.contains((String) e.get("name"))).findFirst();
//
//
//        if (datamapOptional.isPresent()) {
//            Map<String, Object> map = datamapOptional.get();
//            log.error("过滤后文件类别获取结果：{}", map);
//            attachmentIds.addAll((ArrayList<Double>) map.get("attachmentIds"));
//        }

//        if (ObjectUtil.isEmpty(attachmentBeanList)) {
//            return attachmentBeanList;
//        }
        
        attachments = attachments.stream().filter(e -> attachmentIds.contains(e.getId())).collect(Collectors.toList());
        for (Attachment e : attachments) {
            String attachmentUrl = baseUrl + "/secure/attachment/" + e.getId() + "/" + e.getFilename();
            ApplicationUser applicationUser = e.getAuthorObject();
            DateTimeFormatter formatter = DateTimeFormatter
                    .ofPattern("yyyy-MM-dd HH:mm")
                    .withZone(ZoneId.systemDefault());

            AttachmentBean attachmentBean = new AttachmentBean();
            attachmentBean.setAttachmentType(e.getMimetype());
            attachmentBean.setAttachmentUrl(attachmentUrl);
            attachmentBean.setAttachmentId(e.getId());
            attachmentBean.setAttachmentName(e.getFilename());
            attachmentBean.setAttachmentAuthor(applicationUser.getDisplayName());
            attachmentBean.setAttachmentAuthorAccount(applicationUser.getUsername());
            attachmentBean.setAttachmentCate(attachCategory);
            attachmentBean.setCreateDate(formatter.format(e.getCreated().toInstant()));
            attachmentBeanList.add(attachmentBean);
        }
        return attachmentBeanList;
    }

    public void previewAttachment(Long attachmentId, HttpServletResponse response) {
        try {
            byte[] fileBytes;
            Attachment attachment = ComponentAccessor.getAttachmentManager().getAttachment(attachmentId);
            File attachmentFile = AttachmentUtils.getAttachmentFile(attachment);
            String mimetype = attachment.getMimetype();
            String filename = attachment.getFilename();
            String attachmentFileName = attachmentFile.getName();

            if (!FileUtil.exist(attachmentFile)) {
                throw new IllegalArgumentException("附件不存在: " + attachmentId);
            } else {
                fileBytes = FileUtil.readBytes(attachmentFile);
            }

            //设置contentType
//            response.setContentType(MediaType.APPLICATION_PDF_VALUE);
            response.setContentType(mimetype);

            //获取outputStream
            ServletOutputStream outputStream = response.getOutputStream();
            //输出
            IoUtil.write(outputStream, true, fileBytes);
            log.error("附件预览接口调用完成，附件ID：" + attachmentId + "附件名称：" + attachmentFileName + "#" + filename);
        } catch (Exception e) {
            e.printStackTrace();
            log.error(">>> 预览文件异常：%s", e.getMessage());
            throw new RuntimeException(e);
        }
    }

    public ResultBean getCateAndAttachment(String token, Long issueId, int allAttachment) {
        ResultBean resultBean = new ResultBean();
        try {
            ApplicationUser applicationUser = JwtTokenUtil.getApplicationUserByToken(token);
            MutableIssue mutableIssue = ComponentAccessor.getIssueManager().getIssueObject(issueId);
            List<Issue> issueList = jiraCustomTool.getIssueListByJql(applicationUser, "issueKey=" + mutableIssue.getKey());
            if (ObjectUtils.isEmpty(issueList) && allAttachment != 1) {
                resultBean.setValue(new ArrayList<>());
                return resultBean;
            }
            String baseUrl = ComponentAccessor.getApplicationProperties().getString(APKeys.JIRA_BASEURL);
            // 获取类别列表信息
            Map<String, String> headers = new HashMap<>();
//            headers.put("atlas-authorization", ComponentAccessor.getComponent(JiraAuthenticationContext.class).getLoggedInUser().getUsername());
            headers.put("atlas-authorization", applicationUser.getUsername());
            String callback = HttpUtils.doGet(
                    baseUrl + "/rest/attach-cat/1.0/attachments?issueKey=" + mutableIssue.getKey(),
                    headers
            );
            log.error("获取附件分类响应：" + callback);
            JSONObject jsonObject = JSON.parseObject(callback);

            List<SmartAttachmentCateBean> smartAttachmentCateBeanList = JSONObject.parseArray(jsonObject.getString("categories"), SmartAttachmentCateBean.class);
            smartAttachmentCateBeanList = smartAttachmentCateBeanList.stream().filter(e -> !"驳回文件区".equals(e.getName())).collect(Collectors.toList());
            smartAttachmentCateBeanList.forEach(smartAttachmentCateBean -> {
                List<Long> attachmentIdList = smartAttachmentCateBean.getAttachmentIds();

                List<AttachmentBean> attachmentList = new ArrayList<>();
                List<SmartAttachmentCateBean> documents = smartAttachmentCateBean.getDocuments();
                if (!ObjectUtils.isEmpty(documents)) {//有成组的同名文件，取最新的文件(文件id最大)
                    documents.stream().map(SmartAttachmentCateBean::getAttachmentIds)
                            .filter(attachmentIds -> !ObjectUtils.isEmpty(attachmentIds))
                            .map(attachmentIds -> attachmentIds.stream().max(Comparator.comparing(Long::longValue)).orElse(0L))
                            .forEach(attachmentIdList::add);
                }
                attachmentIdList.forEach(attachmentId -> {
                    Attachment attachment = ComponentAccessor.getAttachmentManager().getAttachment(attachmentId);
                    if (!ObjectUtils.isEmpty(attachment)) {
                        String attachmentUrl = baseUrl + "/secure/attachment/" + attachment.getId() + "/" + attachment.getFilename();
                        attachmentList.add(
                                new AttachmentBean.Builder()
                                        .attachmentId(attachment.getId())
                                        .attachmentName(attachment.getFilename())
                                        .attachmentType(attachment.getMimetype())
                                        .attachmentUrl(attachmentUrl)
                                        .createDate(Utils.getDateFormat(attachment.getCreated()))
                                        .build()
                        );
                    }
                });
                smartAttachmentCateBean.setAttachmentList(attachmentList);
            });
            log.debug("获取附件分类处理结果：" + smartAttachmentCateBeanList);
/*            Map<String, Object> callbackMap = (Map<String, Object>) new Gson().fromJson(callback, Object.class);

            Object callbackObject = callbackMap.get("categories");
            List<Map<String, Object>> dataMapList = null;

            if (callbackObject instanceof ArrayList) {
                dataMapList = (List<Map<String, Object>>) callbackObject;
            } else {
                dataMapList = new ArrayList<>();
                Map<String, Object> dataMap = (Map<String, Object>) callbackObject;
                dataMapList.add(dataMap);
            }
            List<AttachmentCateBean> attachmentCateBeanList = new ArrayList<>();
            for (Map<String, Object> stringObjectMap : dataMapList) {
                List<Long> attachmentIds = (List<Long>) stringObjectMap.get("attachmentIds");
                List<Map<String, Object>> documentList = (List<Map<String, Object>>) stringObjectMap.get("documents");

                if (!ObjectUtils.isEmpty(documentList)) {
                    attachmentIds.addAll(documentList.stream().map(e -> {
                        List<Long> attachmentIdList = ((List<Long>) e.get("attachmentIds"));
                        return attachmentIdList.stream().max(Comparator.comparing(Long::longValue)).orElse(0L);
                    }).collect(Collectors.toList()));
                }


                List<AttachmentBean> attachmentBeanList = attachmentIds.stream().map(e -> {
                    Attachment attachment = ComponentAccessor.getAttachmentManager().getAttachment(e);
                    if (!ObjectUtils.isEmpty(attachment)) {
                        String attachmentUrl = baseUrl + "/secure/attachment/" + attachment.getId() + "/" + attachment.getFilename();
                        return new AttachmentBean(attachment.getId(), attachment.getFilename(), attachment.getMimetype(), attachmentUrl);
                    }
                    return new AttachmentBean();
                }).collect(Collectors.toList());


                AttachmentCateBean attachmentCateBean = new AttachmentCateBean.Builder()
                        .cateId((Long) stringObjectMap.get("id"))
                        .cateName((String) stringObjectMap.get("name"))
                        .attachmentList(attachmentBeanList)
                        .build();
            }*/

            resultBean.setValue(smartAttachmentCateBeanList);
        } catch (Exception e) {
            log.error(Utils.errInfo(e));
            resultBean.setMessage(e.getMessage());
        }
        return resultBean;
    }
}
