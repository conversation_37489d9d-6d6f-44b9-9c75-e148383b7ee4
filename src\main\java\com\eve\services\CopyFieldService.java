package com.eve.services;

import com.atlassian.jira.component.ComponentAccessor;
import com.atlassian.jira.issue.MutableIssue;
import com.atlassian.jira.issue.customfields.CustomFieldType;
import com.atlassian.jira.issue.fields.CustomField;
import com.atlassian.jira.issue.fields.screen.FieldScreen;
import com.atlassian.jira.issue.fields.screen.FieldScreenLayoutItem;
import com.atlassian.jira.issue.fields.screen.FieldScreenTab;
import com.atlassian.jira.security.xsrf.XsrfTokenGenerator;
import com.atlassian.jira.transition.TransitionEntry;
import com.atlassian.jira.transition.TransitionManager;
import com.atlassian.jira.transition.WorkflowTransitionEntry;
import com.atlassian.jira.workflow.JiraWorkflow;
import com.atlassian.jira.workflow.WorkflowManager;
import com.eve.beans.CopyFieldBean;
import com.eve.beans.ResultBean;
import com.eve.beans.TransitionBean;
import com.eve.beans.WorkflowBean;
import com.eve.utils.Constant;
import com.opensymphony.workflow.loader.ActionDescriptor;
import com.opensymphony.workflow.loader.WorkflowDescriptor;
import org.ofbiz.core.entity.GenericValue;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/5/9
 */
public class CopyFieldService {

    public List<CopyFieldBean> queryFieldList(String fieldType) {
        List<CopyFieldBean> resultCustomFieldList = new ArrayList<>();
        try {
//            -获取转换同时获取转换屏幕上的字段ID
//            MutableIssue mutableIssue = ComponentAccessor.getIssueManager().getIssueObject(10000L);
//            JiraWorkflow workflow = ComponentAccessor.getWorkflowManager().getWorkflow(mutableIssue);
//            WorkflowDescriptor descriptor = workflow.getDescriptor();
//            ActionDescriptor actionDescriptor = descriptor.getAction(21);
//            Map<String, Object> metaAttributes = actionDescriptor.getMetaAttributes();
//            String fieldScreenId = (String) metaAttributes.get("jira.fieldscreen.id");
//            FieldScreen fieldScreen = ComponentAccessor.getFieldScreenManager().getFieldScreen(Long.parseLong(fieldScreenId));
//            List<FieldScreenTab> tabs = fieldScreen.getTabs();
//            for (FieldScreenTab fieldScreenTab : tabs) {
//                List<FieldScreenLayoutItem> fieldScreenLayoutItems = fieldScreenTab.getFieldScreenLayoutItems();
//                for (FieldScreenLayoutItem fieldScreenLayoutItem : fieldScreenLayoutItems) {
//                    String fieldId = fieldScreenLayoutItem.getFieldId();
//                }
//                GenericValue genericValue = fieldScreenTab.getGenericValue();
//            }
//            String view = actionDescriptor.getView();
//            XsrfTokenGenerator xsrfTokenGenerator = ComponentAccessor.getComponent(XsrfTokenGenerator.class);
//            String token = xsrfTokenGenerator.generateToken();
//            -

            // 获取所有的自定义字段
            List<CustomField> customFieldList = ComponentAccessor.getCustomFieldManager().getCustomFieldObjects();

            //获取所有的自定义字段类型-测试
//            List<CustomFieldType<?, ?>> customFieldTypeList = ComponentAccessor.getCustomFieldManager().getCustomFieldTypes();
            // 获取指定的自定义字段的类型
//            CustomFieldType customFieldType = ComponentAccessor.getCustomFieldManager().getCustomFieldType(fieldType);
            //用户字段，添加经办人，报告人
            if (fieldType.equals(Constant.userPickerFieldType)) {
                resultCustomFieldList.addAll(Constant.assigneeAndReporter);
            }
            //日期时间字段，添加到期日
            if (fieldType.equals(Constant.dateFieldType) || fieldType.equals(Constant.dateTimeFieldType)) {
                resultCustomFieldList.add(new CopyFieldBean("dueDate", "到期日"));
            }

            for (CustomField customField : customFieldList) {
                if (fieldType.equals(customField.getCustomFieldType().getKey()) || "all".equals(fieldType)) {
                    CopyFieldBean copyFieldBean = new CopyFieldBean();
                    copyFieldBean.setId(customField.getId());
                    copyFieldBean.setName(customField.getName());
                    resultCustomFieldList.add(copyFieldBean);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        }
        return resultCustomFieldList;
    }

    public List<TransitionBean> queryTransitionByWorkflowName(String workflowName) {
        List<TransitionBean> resultCustomFieldList = new ArrayList<>();
        try {
            WorkflowManager workflowManager = ComponentAccessor.getWorkflowManager();
            TransitionManager transitionManager = ComponentAccessor.getComponentOfType(TransitionManager.class);
            Collection<JiraWorkflow> jiraWorkflows = workflowManager.getWorkflows();
            Collection<WorkflowTransitionEntry> workflowTransitionEntries = transitionManager.getTransitions(jiraWorkflows);
//            List<WorkflowBean> workflowBeanList = new ArrayList<>();
            for (WorkflowTransitionEntry workflowTransitionEntry : workflowTransitionEntries) {
                JiraWorkflow workflow = workflowTransitionEntry.getWorkflow();
                if (workflow.getName().equals(workflowName)) {
                    Collection<TransitionEntry> transitions = workflowTransitionEntry.getTransitions();
                    List<TransitionBean> collect = transitions.stream().map(e -> new TransitionBean(e.getTransitionId() + "", e.getName())).collect(Collectors.toList());
                    resultCustomFieldList.addAll(collect);
                    break;
                }
//                workflowBeanList.add(new WorkflowBean(workflow.getName(), (List<TransitionEntry>) workflowTransitionEntry.getTransitions()));
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        }
        return resultCustomFieldList;
    }
}
