package com.eve.services;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.atlassian.jira.avatar.AvatarManager;
import com.atlassian.jira.avatar.AvatarService;
import com.atlassian.jira.bc.ServiceResultImpl;
import com.atlassian.jira.bc.issue.IssueService;
import com.atlassian.jira.bc.issue.search.SearchService;
import com.atlassian.jira.component.ComponentAccessor;
import com.atlassian.jira.config.SubTaskManager;
import com.atlassian.jira.event.type.EventDispatchOption;
import com.atlassian.jira.exception.CreateException;
import com.atlassian.jira.issue.*;
import com.atlassian.jira.issue.attachment.Attachment;
import com.atlassian.jira.issue.attachment.CreateAttachmentParamsBean;
import com.atlassian.jira.issue.context.JiraContextNode;
import com.atlassian.jira.issue.context.ProjectContext;
import com.atlassian.jira.issue.customfields.manager.OptionsManager;
import com.atlassian.jira.issue.customfields.option.Option;
import com.atlassian.jira.issue.fields.CustomField;
import com.atlassian.jira.issue.fields.config.FieldConfigScheme;
import com.atlassian.jira.issue.fields.screen.FieldScreen;
import com.atlassian.jira.issue.fields.screen.FieldScreenLayoutItem;
import com.atlassian.jira.issue.fields.screen.FieldScreenManager;
import com.atlassian.jira.issue.fields.screen.FieldScreenTab;
import com.atlassian.jira.issue.history.ChangeItemBean;
import com.atlassian.jira.issue.search.SearchException;
import com.atlassian.jira.issue.search.SearchResults;
import com.atlassian.jira.issue.status.Status;
import com.atlassian.jira.security.JiraAuthenticationContext;
import com.atlassian.jira.transition.TransitionEntry;
import com.atlassian.jira.transition.TransitionManager;
import com.atlassian.jira.transition.WorkflowTransitionEntry;
import com.atlassian.jira.user.ApplicationUser;
import com.atlassian.jira.user.util.UserManager;
import com.atlassian.jira.util.AttachmentUtils;
import com.atlassian.jira.web.bean.PagerFilter;
import com.atlassian.jira.workflow.JiraWorkflow;
import com.atlassian.query.Query;
import com.eve.beans.*;
import com.eve.utils.Constant;
import com.eve.utils.JiraCustomTool;
import com.eve.utils.JwtTokenUtil;
import com.eve.utils.Utils;
import com.opensymphony.workflow.loader.ActionDescriptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/2/10
 */
public class CustomToolService {
    private static final Logger log = LoggerFactory.getLogger(ProjectReportService.class);
    public final Set<String> stringSet = ConcurrentHashMap.newKeySet();
    // 使用 ConcurrentHashMap 缓存字段ID
    private final Map<String, Long> fieldIdCache = new ConcurrentHashMap<>();
    // 记录最后加载时间
    private final AtomicLong lastLoadTime = new AtomicLong(0);
    // 缓存过期时间（毫秒）
    private static final long CACHE_EXPIRY = 60 * 60 * 1000; // 60分钟

    private JiraCustomTool jiraCustomTool;
    private CustomFieldManager customFieldManager;
    private TransitionManager transitionManager;
    private FieldScreenManager fieldScreenManager;
    private IssueService issueService;
    private OptionsManager optionsManager;
    @Resource
    private AttachmentManager attachmentManager;
    @Resource
    private UserManager userManager;
    @Resource
    private AvatarManager avatarManager;
    @Resource
    private AvatarService avatarService;

    public CustomToolService(JiraCustomTool jiraCustomTool, CustomFieldManager customFieldManager, TransitionManager transitionManager, FieldScreenManager fieldScreenManager, IssueService issueService, OptionsManager optionsManager) {
        this.jiraCustomTool = jiraCustomTool;
        this.customFieldManager = customFieldManager;
        this.transitionManager = transitionManager;
        this.fieldScreenManager = fieldScreenManager;
        this.issueService = issueService;
        this.optionsManager = optionsManager;
    }

    public ResultBean updateIssueProcess(Map<String, String> map) {
        ResultBean resultBean = new ResultBean();
        log.error("参数" + map);
        try {
            MutableIssue mutableIssue = ComponentAccessor.getIssueManager().getIssueObject(Long.parseLong(map.get("issueId")));
            ApplicationUser applicationUser = ComponentAccessor.getUserManager().getUserByName(map.get("userName"));
            String projectProcess = map.get("projectProcess");
            String projectNextStep = map.get("projectNextStep");
            String projectRisk = map.get("projectRisk");
            String projectStrategy = map.get("projectStrategy");
            CustomField projectProcessCustField = customFieldManager.getCustomFieldObject(Constant.progressCustId);
            CustomField projectNextStepCustField = customFieldManager.getCustomFieldObject(Constant.nextStepCustId);
            CustomField projectRiskCustField = customFieldManager.getCustomFieldObject(Constant.riskCustId);
            CustomField projectStrategyCustField = customFieldManager.getCustomFieldObject(Constant.strageCustId);
            mutableIssue.setCustomFieldValue(projectProcessCustField, projectProcess);
            mutableIssue.setCustomFieldValue(projectNextStepCustField, projectNextStep);
            mutableIssue.setCustomFieldValue(projectRiskCustField, projectRisk);
            mutableIssue.setCustomFieldValue(projectStrategyCustField, projectStrategy);

            String comment = "进度：" + projectProcess + "\n下一步计划：" + projectNextStep + "\n风险：" + projectRisk + "\n对策：" + projectStrategy;
            ComponentAccessor.getCommentManager().create(mutableIssue, applicationUser, comment, true);

            CustomField processUpdateDateCustField = customFieldManager.getCustomFieldObject(Constant.processUpdateDateCustomFieldId);
            mutableIssue.setCustomFieldValue(processUpdateDateCustField, Utils.o2sqlDate(new Date()));

            ComponentAccessor.getIssueManager().updateIssue(applicationUser, mutableIssue, EventDispatchOption.ISSUE_UPDATED, false);
//            IssueManager issueManager = ComponentAccessor.getIssueManager();
//            IssueService issueService = new DefaultIssueService();
            resultBean.setValue(mutableIssue.getKey());
        } catch (Exception e) {
            log.error(Utils.errInfo(e));
            resultBean.setMessage(e.getMessage());
        }
        return resultBean;
    }

    public ResultBean getTransitionByIssue(String userName, Long issueId) {
        ResultBean resultBean = new ResultBean();
        try {
            List<TransitionBean> transitionBeanList = new ArrayList<>();
            ApplicationUser applicationUser = ComponentAccessor.getUserManager().getUserByName(userName);
            if (applicationUser == null) {
                throw new IllegalArgumentException("Jira不存在该用户：" + userName);
            }
            MutableIssue issue = ComponentAccessor.getIssueManager().getIssueObject(issueId);

            if (issue == null) {
                issue = ComponentAccessor.getIssueManager().getIssueObject("CPGL-"+issueId);
                if (issue == null) {
                    throw new IllegalArgumentException("该问题不存在：" + issueId);

                }
                issueId = issue.getId();
            }
            Long statusId = Long.valueOf(issue.getStatusId());
            Status status = issue.getStatus();
            //根据问题获取工作流
            JiraWorkflow workflow = ComponentAccessor.getWorkflowManager().getWorkflow(issue);
//            log.error("issueKey：{},工作流名称：{},statusId：{},statusName：{}", issue.getKey(),workflow.getName(),statusId,status.getName());
//            for (ActionDescriptor actionDescriptor : workflow.getAllActions()) {
//                if (actionDescriptor.getMetaAttributes().containsKey(JiraWorkflow.ACTION_SCREEN_ATTRIBUTE)) {
//                    Long fieldScreenId = new Long((String) actionDescriptor.getMetaAttributes().get(JiraWorkflow.ACTION_SCREEN_ATTRIBUTE));
//                }
//                int actionDescriptorId = actionDescriptor.getId();
//                String name = actionDescriptor.getName();
//                //校验用户是否具有执行该工作流的权限
//                IssueService.TransitionValidationResult validationResult = issueService.validateTransition(applicationUser, issue.getId(), actionDescriptorId, issueService.newIssueInputParameters());
//                if (validationResult.isValid()) {
//                    transitionBeanList.add(new TransitionBean(actionDescriptorId + "", name));
//                }
//            }
            List<String> unPutTransitionList = new ArrayList<>(Arrays.asList("填写进展情况", "暂停", "停止"));
            //获取转换入口，可能存在多个工作流，每个工作流有一组转换
            List<String> allTransitionList = new ArrayList<>();
            Collection<WorkflowTransitionEntry> workflowTransitionEntryCollection = transitionManager.getTransitions(Collections.singletonList(workflow));
            for (WorkflowTransitionEntry workflowTransitionEntry : workflowTransitionEntryCollection) {
                for (TransitionEntry transition : workflowTransitionEntry.getTransitions()) {
                    String transitionName = transition.getName();
                    allTransitionList.add(transition.getFromStatusId()+";"+transitionName+";"+transition.getTransitionId());
//                    && (!"暂停".equals(transitionName))
                    if ((transition.getIsGlobal() || (statusId.equals(transition.getFromStatusId()))) && (!unPutTransitionList.contains(transitionName))) {
                        log.debug("当前状态的转换：{}", transitionName);
                        //校验用户是否具有执行该工作流的权限
                        IssueService.TransitionValidationResult validationResult = issueService.validateTransition(applicationUser, issue.getId(), transition.getTransitionId(), issueService.newIssueInputParameters());
                        if (validationResult.isValid()) {
                            TransitionBean transitionBean = new TransitionBean(transition.getTransitionId() + "", transitionName);
                            List<TransitionScreenFieldBean> transitionScreenFieldBeanList = new ArrayList<>();
                            //获取转换界面，用于获取转换时需填写的字段
                            if (transition.getHasScreen()) {
                                Collection<ActionDescriptor> allActions = workflow.getAllActions();
                                ActionDescriptor actionDescriptor = allActions.stream().filter(e -> e.getId() == transition.getTransitionId()).findFirst().orElse(null);
                                if (actionDescriptor != null && actionDescriptor.getMetaAttributes().containsKey(JiraWorkflow.ACTION_SCREEN_ATTRIBUTE)) {
                                    Long fieldScreenId = new Long((String) actionDescriptor.getMetaAttributes().get(JiraWorkflow.ACTION_SCREEN_ATTRIBUTE));
                                    //根据界面id获取界面字段
                                    FieldScreen fieldScreen = fieldScreenManager.getFieldScreen(fieldScreenId);
                                    List<FieldScreenTab> fieldScreenTabs = fieldScreenManager.getFieldScreenTabs(fieldScreen);
                                    int fieldPosition = 1;
                                    for (FieldScreenTab fieldScreenTab : fieldScreenTabs) {
                                        List<FieldScreenLayoutItem> fieldScreenLayoutItems = fieldScreenTab.getFieldScreenLayoutItems();
                                        for (FieldScreenLayoutItem fieldScreenLayoutItem : fieldScreenLayoutItems) {
                                            int position = fieldScreenLayoutItem.getPosition();
                                            String fieldId = fieldScreenLayoutItem.getFieldId();
                                            CustomField customFieldObject = customFieldManager.getCustomFieldObject(fieldId);
                                            String customFieldTypekey = customFieldObject.getCustomFieldType().getKey();
                                            TransitionScreenFieldBean transitionScreenFieldBean = new TransitionScreenFieldBean(fieldPosition++, customFieldObject.getIdAsLong(), fieldId, customFieldObject.getName(), customFieldTypekey);
                                            //option添加进去
                                            List<String> fieldTypeList = Arrays.asList(
                                                    "com.atlassian.jira.plugin.system.customfieldtypes:cascadingselect",
                                                    "com.atlassian.jira.plugin.system.customfieldtypes:select",
                                                    "com.atlassian.jira.plugin.system.customfieldtypes:multiselect",
                                                    "com.atlassian.jira.plugin.system.customfieldtypes:radiobuttons",
                                                    "com.atlassian.jira.plugin.system.customfieldtypes:multicheckboxes");
                                            if (fieldTypeList.contains(customFieldTypekey)) {
                                                transitionScreenFieldBean.setOptionBeanList(queryOptionsListByIssue(customFieldObject.getIdAsLong(), issue));
                                            }
                                            transitionScreenFieldBeanList.add(transitionScreenFieldBean);
                                        }
                                    }
                                    transitionScreenFieldBeanList.add(new TransitionScreenFieldBean(fieldPosition++, 0L,"comment","评论","comment"));

                                }
                            }
                            transitionBean.setTransitionScreenFieldBeanList(transitionScreenFieldBeanList);
                            transitionBeanList.add(transitionBean);
                        }
                    }
                    else {

                    }
                }
                log.debug("全部转换：{}", allTransitionList);
            }
            String jsonString = JSON.toJSONString(transitionBeanList);
            log.debug("获取转换完成：{}", jsonString);
            resultBean.setValue(transitionBeanList);
        } catch (Exception e) {
            log.error("获取转换异常：{}", Utils.errInfo(e));
            resultBean.setMessage(e.getMessage());
        }
        return resultBean;
    }

    public static List<JiraOptionBean> queryOptionsListByIssue(Long fieldId, Issue issue) {
        // 获取选项自定义字段的选项列表
        Long projectId = issue.getProjectId();
        CustomField customField = Utils.getCustomFieldByID(fieldId);
        List<JiraOptionBean> jiraOptionBeanList = new ArrayList<>();
        for (Option option : customField.getOptions("", new ProjectContext(projectId, ComponentAccessor.getProjectManager()))) {
            if (option.getDisabled()) {
                continue;
            }
            jiraOptionBeanList.add(new JiraOptionBean.Builder().id(option.getOptionId()).pid(1L).value(option.getValue()).sequence(option.getSequence()).build());
            List<Option> childOptions = option.getChildOptions();
            if (!ObjectUtils.isEmpty(childOptions)) {
                for (Option childOption : childOptions) {
                    if (childOption.getDisabled()) {
                        continue;
                    }
                    jiraOptionBeanList.add(new JiraOptionBean.Builder().id(childOption.getOptionId()).pid(option.getOptionId()).value(childOption.getValue()).sequence(childOption.getSequence()).build());
                }
            }
        }
        return jiraOptionBeanList;
    }
    public ResultBean runTransitionByName(int isOnline,String token,UpdateCustomFiledBean updateCustomFiledBean) {
        ResultBean resultBean = new ResultBean();
        try {
            Long issueId = updateCustomFiledBean.getIssueId();
//            log.error("进入转换执行接口:Token:"+token+" issue:" + issueId + "转换名称:" + updateCustomFiledBean.getTransitionName());
            log.error("进入转换执行接口:Token:{} issueId:{} updateCustomFiledBean:{}", token,issueId, updateCustomFiledBean);
            ApplicationUser applicationUser = JwtTokenUtil.getApplicationUserByToken(token);
            MutableIssue mutableIssue = ComponentAccessor.getIssueManager().getIssueObject(issueId);

//            String isSystemCall = map == null ? "" : map.get("isSystemCall");

            IssueInputParameters issueInputParameters = getIssueInputParameters(mutableIssue.getProjectId(),Utils.getReviewListCustomFiledMap(),updateCustomFiledBean);
            runTransitionByName(mutableIssue, applicationUser, updateCustomFiledBean.getTransitionName(), issueInputParameters);
            //更新字段
//            if ("1".equals(isSystemCall)) {
//                CustomField customField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(Constant.isSystemCallCustomFieldId);
//                mutableIssue.setCustomFieldValue(customField, isSystemCall);
//                ComponentAccessor.getIssueManager().updateIssue(applicationUser, mutableIssue, EventDispatchOption.ISSUE_UPDATED, false);
//            }
            try {
                String attachmentObject = updateCustomFiledBean.getMap().get("attachmentObject");
                if (ObjectUtil.isNotEmpty(attachmentObject)) {
                    JSONObject jsonObject = JSON.parseObject(attachmentObject, JSONObject.class);
                    addTestFailureAttachment(jsonObject.getJSONArray("bomCostingAttachment"), isOnline, "BOM核算文件", mutableIssue, applicationUser);
                }
            } catch (Exception e) {
                log.error("文件上传失败:{}", Utils.errInfo(e));
            }
            log.error("转换执行接口完成");
            resultBean.setValue("S");
        } catch (Exception e) {
            log.error(Utils.errInfo(e));
            resultBean.setMessage(e.getMessage());
        }
        return resultBean;
    }

    public ResultBean runTransitionByName(String token, String transitionName,UpdateCustomFiledBean updateCustomFiledBean) {
        ResultBean resultBean = new ResultBean();
        try {
            Long issueId = updateCustomFiledBean.getIssueId();
            log.error("进入转换执行接口:Token:"+token+" issue:" + issueId + "转换id:" + transitionName);
            ApplicationUser applicationUser = JwtTokenUtil.getApplicationUserByToken(token);
            MutableIssue mutableIssue = ComponentAccessor.getIssueManager().getIssueObject(issueId);
            String statusId = mutableIssue.getStatusId();
            Map<String, String> map = updateCustomFiledBean.getMap();
//            String isSystemCall = map == null ? "" : map.get("isSystemCall");
            runTransitionByName(mutableIssue, applicationUser, transitionName, issueService.newIssueInputParameters());
            //更新字段
//            if ("1".equals(isSystemCall)) {
//                CustomField customField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(Constant.isSystemCallCustomFieldId);
//                mutableIssue.setCustomFieldValue(customField, isSystemCall);
//                ComponentAccessor.getIssueManager().updateIssue(applicationUser, mutableIssue, EventDispatchOption.ISSUE_UPDATED, false);
//            }
            String statusId1 = mutableIssue.getStatusId();
            log.error("转换执行接口完成");
            resultBean.setValue(statusId.equals(statusId1) ? "S" : "E");
        } catch (Exception e) {
            log.error(Utils.errInfo(e));
            resultBean.setMessage(e.getMessage());
        }
        return resultBean;
    }

    public ResultBean getJiraSelectOption(String fieldName) {
        ResultBean resultBean = new ResultBean();
        Long customFieldId = Utils.getSelectCustomFiledMap().getOrDefault(fieldName, 0L);
        List<JiraOptionBean> jiraOptionBeanList = new ArrayList<>();
        try {
            CustomField customField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(customFieldId);
            if (customField == null) {
                throw new IllegalArgumentException("该字段未适配");
            }
            List<FieldConfigScheme> configurationSchemes = customField.getConfigurationSchemes();
            for (FieldConfigScheme configurationScheme : configurationSchemes) {
                List<JiraContextNode> jiraContextNodeList = configurationScheme.getContexts();
                for (Option option : customField.getOptions("", ArrayUtil.isEmpty(jiraContextNodeList) ? new ProjectContext(null, ComponentAccessor.getProjectManager()) : jiraContextNodeList.get(0))) {
                    if (Boolean.FALSE.equals(option.getDisabled())) {
                        jiraOptionBeanList.add(new JiraOptionBean.Builder().id(option.getOptionId()).value(option.getValue()).sequence(option.getSequence()).build());
                    }
                }
            }
            resultBean.setValue(jiraOptionBeanList);
        } catch (Exception e) {
            log.error(Utils.errInfo(e));
            resultBean.setMessage(e.getMessage());
        }
        return resultBean;
    }

    public ResultBean checkUser(String userName) {
        ResultBean resultBean = new ResultBean();
        ApplicationUser applicationUser = ComponentAccessor.getUserManager().getUserByName(userName);
        if (applicationUser != null && applicationUser.isActive()) {
            resultBean.setValue("s");
        } else {
            resultBean.setValue("Jira系统不存在该用户");
        }
        return resultBean;
    }

    /**
     * 根据id到ProductFileMsgAo获取位置，并输出文件到响应
     * @param attachmentId
     * @param response
     */
    public void downloadAttachment(Long attachmentId, HttpServletResponse response) {
        try {
//            ProductFileMsgAo productFileMsgAo = ao.get(ProductFileMsgAo.class, id);
//            int i = 0;
//            while (productFileMsgAo.getFilePath() == null || i++ < 1000) {
//                productFileMsgAo = ao.get(ProductFileMsgAo.class, id);
//            }
//            if (productFileMsgAo == null) {
//                throw new IllegalArgumentException("未查询到该ID对应的数据：" + id);
//            }
            //获取文件所在位置，outputStream输出
//            String filePath = productFileMsgAo.getFilePath();
            log.error("文件下载接口调用，attachmentId：" + attachmentId);
//            String filePath = "D:\\test\\test1.pdf";


            byte[] fileBytes;
            Attachment attachment = ComponentAccessor.getAttachmentManager().getAttachment(attachmentId);
            File attachmentFile = AttachmentUtils.getAttachmentFile(attachment);
            fileBytes = FileUtil.readBytes(attachmentFile);
            //设置contentType
            response.setContentType(attachment.getMimetype());
            String filename = new String(attachment.getFilename().getBytes(), StandardCharsets.UTF_8);
            response.setHeader("Content-Disposition", "inline;fileName=" + filename);
//            response.addHeader("Content-Disposition", "attachment;filename=\"" + filename +"\"");
            //获取outputStream
            ServletOutputStream outputStream = response.getOutputStream();
            //输出
            IoUtil.write(outputStream, true, fileBytes);
//            System.out.println("文件下载接口调用完成，附件ID：" + attachmentId);
            log.error("文件下载接口调用完成，附件ID：" + attachmentId);
        } catch (Exception e) {
            e.printStackTrace();
            log.error(">>> 预览文件异常：%s", e.getMessage());
            throw new RuntimeException(e);
        }
    }

    public ResultBean issueCreateWebhook(String issueId) {
        ResultBean resultBean = new ResultBean();
        log.error("问题创建Webhook调用，问题id：{}", issueId);
        try {
            MutableIssue mutableIssue = ComponentAccessor.getIssueManager().getIssueObject(Long.parseLong(issueId));
            Long projectId = mutableIssue.getProjectId();
            String issueTypeIdString = mutableIssue.getIssueTypeId();
            long issueTypeId = Long.parseLong(issueTypeIdString == null ? "0" : issueTypeIdString);
            //产品管理项目转阶段子任务

            if (Constant.productManageProjectIdList.contains(projectId) && Constant.productStageIssueTypeId.equals(issueTypeId)) {
                CustomField productStageCustomField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(Constant.productStageCustomFieldId);
                Option productStageOption = productStageCustomField == null ? null : (Option) mutableIssue.getCustomFieldValue(productStageCustomField);
                Long optionId = productStageOption == null ? 0L : productStageOption.getOptionId();
                if (Constant.productStageSchemeFreezeIdList.contains(optionId) || optionId.equals(18931L)) {
//                    mutableIssue.setDescription("{color:#FF0000}请上传方案评审报告以及会议纪要，并填写实际评审时间{color}");
                    mutableIssue.setDescription("{color:#FF0000}请上传方案评审报告以及会议纪要，并填写实际评审时间{color}");
                } else if (optionId.equals(18932L)) {
                    mutableIssue.setDescription("{color:#FF0000}请上传PPAP资料包{color}");
                } else if (optionId.equals(18933L)) {
                    mutableIssue.setDescription("{color:#FF0000}请上传量产通知书{color}");
                } else if (optionId.equals(22441L)) {
                    mutableIssue.setDescription("{color:#FF0000}请上传《A样DR报告》以及会议纪要，并填写实际评审时间{color}");
                } else if (optionId.equals(22442L)) {
                    mutableIssue.setDescription("{color:#FF0000}请上传《B样DF报告》以及会议纪要，并填写实际评审时间{color}");
                }
                ComponentAccessor.getIssueManager().updateIssue(mutableIssue.getReporter(), mutableIssue, EventDispatchOption.ISSUE_UPDATED, false);
            } else if (Constant.productManageProjectIdList.contains(projectId)&& Constant.productIssueTypeId.equals(issueTypeId)) {
                CustomField productDepartmentCustomField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(Constant.subDepartCustID);
                Map<String, Option> productDepartmentOptionMap = productDepartmentCustomField == null ? new HashMap<>() : (Map<String, Option>) mutableIssue.getCustomFieldValue(productDepartmentCustomField);
                Option productDepartmentParentOption = productDepartmentOptionMap.getOrDefault(null, null);
                CustomField projectManagerCustomField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(Constant.projectManagerCustomFieldId);
                ApplicationUser applicationUser = ComponentAccessor.getUserManager().getUserByName("115649");//默认王文君
//                if (productDepartmentParentOption != null && Constant.jmDepartmentOptionList.contains(productDepartmentParentOption.getOptionId())) {
//                    applicationUser = ComponentAccessor.getUserManager().getUserByName("067155");//荆门阮祝华
//                }else
                if (productDepartmentParentOption != null && Constant.vCylinderDepartmentOptionList.contains(productDepartmentParentOption.getOptionId())){
                    applicationUser = ComponentAccessor.getUserManager().getUserByName("065032");//V圆柱向宝玉
                }
                mutableIssue.setAssignee(applicationUser);
                mutableIssue.setCustomFieldValue(projectManagerCustomField,applicationUser);
                ComponentAccessor.getIssueManager().updateIssue(mutableIssue.getReporter(), mutableIssue, EventDispatchOption.ISSUE_UPDATED, false);

            } else if (Objects.equals(projectId, 12903L)) {
                //项目管理部创建项目，参与人默认添加项目管理部成员

            }
        } catch (Exception e) {
            log.error("问题创建Webhook调用异常：{}", Utils.errInfo(e));
            resultBean.setMessage(e.getMessage());
        }
        return resultBean;
    }


    public ResultBean issueUpdateWebhook(String issueId) {
        ResultBean resultBean = new ResultBean();
        log.error("问题更新Webhook调用，问题id：{}", issueId);
        try {
            Thread.sleep(500);
            MutableIssue mutableIssue = ComponentAccessor.getIssueManager().getIssueObject(Long.parseLong(issueId));
            ApplicationUser assignee = mutableIssue.getAssignee();
/*            if (assignee != null && "103187".equals(assignee.getUsername())) {
                log.error("转办曹晶晶，问题id：{}", issueId);
                ApplicationUser newAssignee = ComponentAccessor.getUserManager().getUserByName("067627");
                mutableIssue.setAssignee(newAssignee);
                CommentManager commentManager = ComponentAccessor.getCommentManager();
                String commentString = "【自动回复】：休假中，此流程节点曹晶晶代理";
                commentManager.create(mutableIssue, assignee, commentString, true);
                ComponentAccessor.getIssueManager().updateIssue(assignee, mutableIssue, EventDispatchOption.ISSUE_UPDATED, false);
            }*/
        } catch (Exception e) {
            log.error("问题更新Webhook调用异常：{}", Utils.errInfo(e));
            resultBean.setMessage(e.getMessage());
        }
        return resultBean;
    }

    public ResultBean repeatedApprovalJumpWebhook(String issueId) {
        ResultBean resultBean = new ResultBean();
        log.error("重复审批跳过Webhook调用，问题id：{}", issueId);
        try {
            Thread.sleep(1000);
            MutableIssue mutableIssue = ComponentAccessor.getIssueManager().getIssueObject(Long.parseLong(issueId));
            String transitionName = "重复审批跳转";
            String projectKeyStr = ComponentAccessor.getApplicationProperties().getString(Constant.repeated_approval_jump_enable_project);
            log.error("重复审批跳过Webhook调用，配置：{}", projectKeyStr);
            if (projectKeyStr.contains(mutableIssue.getProjectObject().getKey()) || projectKeyStr.contains(mutableIssue.getProjectId().toString())) {
                transitionNameByCondition(mutableIssue, transitionName);
            }
        } catch (Exception e) {
            log.error("重复审批跳过Webhook调用异常：{}", Utils.errInfo(e));
            resultBean.setMessage(e.getMessage());
        }
        return resultBean;
    }

    public void transitionNameByCondition(MutableIssue mutableIssue, String transitionName) {
        ApplicationUser assignee = mutableIssue.getAssignee();
        CustomField checkUserCustomField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(12185L);//已审核
        List<ApplicationUser> customFieldValue = checkUserCustomField == null ? new ArrayList<>() : (List<ApplicationUser>) mutableIssue.getCustomFieldValue(checkUserCustomField);
        boolean isJump = (assignee == null) || (ObjectUtil.isNotEmpty(customFieldValue) && customFieldValue.contains(assignee));
        if (isJump) {//ApplicationUser内部已重写equals，使用用户key判断
            JiraWorkflow workflow = ComponentAccessor.getWorkflowManager().getWorkflow(mutableIssue);
            Long currentStatusId = Long.valueOf(mutableIssue.getStatusId());
            Collection<WorkflowTransitionEntry> workflowTransitionEntryCollection = transitionManager.getTransitions(Collections.singletonList(workflow));
            for (WorkflowTransitionEntry workflowTransitionEntry : workflowTransitionEntryCollection) {
                for (TransitionEntry transition : workflowTransitionEntry.getTransitions()) {
                    Long fromStatusId = transition.getFromStatusId();
                    if (currentStatusId.equals(fromStatusId) && transitionName.equals(transition.getName().trim())) {
                        ApplicationUser applicationUser = ComponentAccessor.getUserManager().getUserByName(Constant.dladminUserName);
                        IssueInputParameters issueInputParameters = issueService.newIssueInputParameters();
                        IssueService.TransitionValidationResult validationResult = issueService.validateTransition(applicationUser, mutableIssue.getId(), transition.getTransitionId(), issueInputParameters);
                        if (validationResult.isValid()) {
                            IssueService.IssueResult issueResult = issueService.transition(applicationUser, validationResult);
                            if (!issueResult.isValid()) {
                                log.error("转换（{}）执行失败：{}", transition.getTransitionId(), issueResult.getErrorCollection());
                            } else {
                                log.error("转换（{}）执行成功，问题id：{}", transition.getTransitionId(), mutableIssue.getId());
                            }
                        } else {
                            log.error("执行失败，该转换（{}）不可执行：{}", transition.getTransitionId(), validationResult.getErrorCollection());
                            continue;
                        }
                        break;
                    }
                }
            }
        }
    }

    public MutableIssue getRelationIssue(String relationType,Issue issue) {
        MutableIssue mutableIssue = ComponentAccessor.getIssueManager().getIssueObject(issue.getId());
//        Permissions permissions=null;
        MutableIssue resultIssue = mutableIssue;
        switch (relationType) {
            case "parent_issue":
                MutableIssue parentIssue = ComponentAccessor.getIssueManager().getIssueObject(mutableIssue.getParentId());
                if (parentIssue == null) {
                    break;
                }
                resultIssue = parentIssue;
                break;
            case "sub_issue":
                Collection<Issue> subIssueList = mutableIssue.getSubTaskObjects();
                if (subIssueList == null || subIssueList.isEmpty()) {
                    break;
                }
                resultIssue = ComponentAccessor.getIssueManager().getIssueObject(subIssueList.iterator().next().getKey());
//                for (Issue sunIssue : subIssueList) {
//                    resultIssue = ComponentAccessor.getIssueManager().getIssueObject(sunIssue.getKey());
//                }
                break;
            case "epic_issue":
                CustomField epicLinkCustomField = ((List<CustomField>) ComponentAccessor.getCustomFieldManager().getCustomFieldObjectsByName("史诗链接")).get(0);
                Object epicLink = mutableIssue.getCustomFieldValue(epicLinkCustomField);
                if (epicLink == null) {
                    break;
                }
                resultIssue = ComponentAccessor.getIssueManager().getIssueObject(String.valueOf(epicLink));
                break;
//            case "epic_link_issue":
//                String epicName = (String) mutableIssue.getCustomFieldValue(epicCustomField);
//                if (epicName == null || epicName.isEmpty()) {
//                    break;
//                }
//                String jql = "史诗链接 = " + mutableIssue.getKey();
//                SearchService searchService = (SearchService) ComponentAccessor.getComponentOfType(SearchService.class);
//                SearchService.ParseResult parseResult = searchService.parseQuery(currentUser, jql);
//                Query query = parseResult.getQuery();
//                SearchResults searchResults = searchService.search(currentUser, query, PagerFilter.getUnlimitedFilter());
//                List<Issue> storyIssueList = searchResults.getResults();
//                for (Issue storyIssue : storyIssueList) {
//                    resultIssue = ComponentAccessor.getIssueManager().getIssueObject(storyIssue.getKey());
//                }
//                break;
            case "current_issue":
            default:
                resultIssue = mutableIssue;
        }
        return resultIssue;
    }

    public List<MutableIssue> getRelationIssueList(String relationType, MutableIssue mutableIssue, ApplicationUser applicationUser) throws SearchException {
        List<MutableIssue> mutableIssueList = new ArrayList<>();
        switch (relationType) {
            case "parent_issue":
                MutableIssue parentIssue = ComponentAccessor.getIssueManager().getIssueObject(mutableIssue.getParentId());
                if (parentIssue == null) {
                    break;
                }
                mutableIssueList.add(parentIssue);
                break;
            case "sub_issue":
                Collection<Issue> subIssueList = mutableIssue.getSubTaskObjects();
                if (subIssueList == null || subIssueList.isEmpty()) {
                    break;
                }
                mutableIssueList.addAll(subIssueList.stream().map(issue -> ComponentAccessor.getIssueManager().getIssueObject(issue.getId())).collect(Collectors.toList()));
                break;
            case "epic_issue":
                CustomField epicLinkCustomField = ((List<CustomField>) ComponentAccessor.getCustomFieldManager().getCustomFieldObjectsByName("史诗链接")).get(0);
                Object epicLink = mutableIssue.getCustomFieldValue(epicLinkCustomField);
                if (epicLink == null) {
                    break;
                }
                mutableIssueList.add(ComponentAccessor.getIssueManager().getIssueObject(String.valueOf(epicLink)));
                break;
            case "epic_link_issue":
                CustomField epicCustomField = ((List<CustomField>) ComponentAccessor.getCustomFieldManager().getCustomFieldObjectsByName("史诗名称")).get(0);
                String epicName = (String) mutableIssue.getCustomFieldValue(epicCustomField);
                if (epicName == null || epicName.isEmpty()) {
                    break;
                }
                String jql = "史诗链接 = " + mutableIssue.getKey();
                SearchService searchService = (SearchService) ComponentAccessor.getComponentOfType(SearchService.class);
                SearchService.ParseResult parseResult = searchService.parseQuery(applicationUser, jql);
                Query query = parseResult.getQuery();
                SearchResults searchResults = searchService.search(applicationUser, query, PagerFilter.getUnlimitedFilter());
                List<Issue> storyIssueList = searchResults.getResults();
                mutableIssueList.addAll(storyIssueList.stream().map(issue -> ComponentAccessor.getIssueManager().getIssueObject(issue.getId())).collect(Collectors.toList()));
                break;
            case "current_issue":
                mutableIssueList.add(mutableIssue);
                break;
            default:
//                resultIssue = mutableIssue;
        }
        return mutableIssueList;
    }


    public ResultBean getTransitionByWorkFlow(WorkflowBean workflowBean) {
        ResultBean resultBean = new ResultBean();
        try {
            String workflowName = workflowBean.getWorkflowName();
            String workflowMode = workflowBean.getWorkflowMode();
            JiraWorkflow workflow = null;
//            JiraWorkflow draftWorkflow = ComponentAccessor.getWorkflowManager().getDraftWorkflow(workflowName);
            if ("draft".equals(workflowMode)) {
                workflow = ComponentAccessor.getWorkflowManager().getDraftWorkflow(workflowName);
            }else{
                workflow = ComponentAccessor.getWorkflowManager().getWorkflow(workflowName);
            }
            Collection<ActionDescriptor> allActions = workflow.getAllActions();
            List<Map<String, String>> collect = allActions.stream().map(e -> {
                Map<String, String> map = new HashMap<>();
                map.put("id", e.getId()+"");
                map.put("name", e.getName());
                return map;
            }).collect(Collectors.toList());
//            for (ActionDescriptor allAction : allActions) {
//                allAction.getId();
//                allAction.getName();
//            }

            resultBean.setValue(collect);
        } catch (Exception e) {
            e.printStackTrace();
            resultBean.setMessage(e.getMessage());
        }
        return resultBean;
    }

    public ResultBean runTransitionById(int isOnline, UpdateCustomFiledBean updateCustomFiledBean) {
        ResultBean resultBean = new ResultBean();

        Map<String, String> map = updateCustomFiledBean.getMap();
        String attachmentObject = map.get("attachmentObject");
        log.error("转换并更新接口参数:isOnline={},{}", isOnline, updateCustomFiledBean);
        Long issueId = updateCustomFiledBean.getIssueId();
        MutableIssue mutableIssue = ComponentAccessor.getIssueManager().getIssueObject(issueId);
        ApplicationUser applicationUser = ComponentAccessor.getUserManager().getUserByName(updateCustomFiledBean.getUserName());
        try {
            if (mutableIssue == null) {
                mutableIssue = ComponentAccessor.getIssueManager().getIssueObject(updateCustomFiledBean.getIssueKey());
                if (mutableIssue == null) {
                    throw new IllegalArgumentException("问题ID传递错误，获取问题失败, " + issueId);
                } else {
                    issueId = mutableIssue.getId();
                    updateCustomFiledBean.setIssueId(issueId);
                }
            }
            if (map.getOrDefault("stageFileComplete", null) == null) {
                map.put("stageFileComplete", "1");
            }

            Long transitionId = updateCustomFiledBean.getTransitionId();

            IssueInputParameters issueInputParameters = getIssueInputParameters(mutableIssue.getProjectId(), Utils.getReviewListCustomFiledMap(), updateCustomFiledBean);

/*            //翻译为Jira字段值
            for (Map.Entry<String, String> entry : map.entrySet()) {
                String entryKey = entry.getKey();
                String entryValue = entry.getValue();
                Long customFiledId = Utils.getReviewListCustomFiledMap().getOrDefault(entryKey, 0L);
                CustomField customFiled = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(customFiledId);
                if (customFiled == null || entryValue == null || "".equals(entryValue)) {

                    continue;
                }
                String customFieldTypeKey = customFiled.getCustomFieldType().getKey();
                switch (customFieldTypeKey) {
                    case "com.atlassian.jira.plugin.system.customfieldtypes:select":
                        if (NumberUtil.isNumber(entryValue)){
                            Option selectOption = optionsManager.findByOptionId(Long.parseLong(entryValue));
                            selectOption = selectOption == null ? optionsManager.findByOptionId(Utils.getSelectOptionId(entryKey, Long.parseLong(entryValue))) : selectOption;
                            if (selectOption != null && !Boolean.FALSE.equals(selectOption.getDisabled())) {
                                mutableIssue.setCustomFieldValue(customFiled, selectOption);
                                issueInputParameters.addCustomFieldValue(customFiledId, String.valueOf(selectOption.getOptionId()));
                            }
                        } else {
                            JiraContextNode jiraContextNode = new ProjectContext(Constant.testFailureProjectId, ComponentAccessor.getProjectManager());

                            Options options = customFiled.getOptions("", jiraContextNode);
                            for (Option option : options) {
                                if (entryValue.equals(option.getValue())) {
                                    mutableIssue.setCustomFieldValue(customFiled, option);
                                    issueInputParameters.addCustomFieldValue(customFiledId, String.valueOf(option.getOptionId()));
                                }
                            }
                        }
//                        Long optionId = 0L;
//                        switch (entryKey) {
//                            case "projectLevel":
//                                optionId = Utils.getProjectLevelMap().entrySet().stream().filter(e -> e.getValue().equals(Long.parseLong(entryValue))).map(Map.Entry::getKey).findFirst().orElse(-1L);
//                                break;
//                            default:
//                                break;
//                        }
//                        issueInputParameters.addCustomFieldValue(customFiledId, String.valueOf(optionId));
                        break;
                    case "com.atlassian.jira.plugin.system.customfieldtypes:cascadingselect":
                        Option option = ComponentAccessor.getOptionsManager().findByOptionId(Long.parseLong(entryValue));
                        if (ObjectUtils.isEmpty(option)) {
                            throw new IllegalArgumentException("根据选项id未找到选项：" + entryKey);
                        }
                        Option parentOption = option.getParentOption();
                        if (ObjectUtils.isEmpty(parentOption)) {
                            issueInputParameters.addCustomFieldValue(
                                    customFiledId,
                                    String.valueOf(option.getOptionId())
                            );
                        } else {
                            issueInputParameters.addCustomFieldValue(
                                    customFiledId,
                                    String.valueOf(parentOption.getOptionId())
                            );
                            issueInputParameters.addCustomFieldValue(
                                    "customfield_" + customFiledId + ":1",
                                    String.valueOf(option.getOptionId())
                            );
                        }
                        break;
                    case "com.atlassian.jira.plugin.system.customfieldtypes:userpicker":
                        ApplicationUser user = ComponentAccessor.getUserManager().getUserByName(entryValue);
                        if (user == null) {
                            throw new IllegalArgumentException("用户工号传递错误，jira未找到用户：" + entryValue);
                        }
                        issueInputParameters.addCustomFieldValue(customFiledId, user.getUsername());
                        break;
                    case "com.atlassian.jira.plugin.system.customfieldtypes:multiuserpicker":
                        issueInputParameters.addCustomFieldValue(customFiledId, entryValue);
                        break;
                    case "com.atlassian.jira.plugin.system.customfieldtypes:datepicker":
                    case "com.atlassian.jira.plugin.system.customfieldtypes:datetime":
                        Date date = java.sql.Date.valueOf(entryValue);
                        DateTimeFormatter dateFormatter = ComponentAccessor.getComponent(DateTimeFormatter.class);
                        dateFormatter = dateFormatter.forLoggedInUser();
                        String dateStr = dateFormatter.withStyle(DateTimeStyle.DATE_PICKER).format(date);
                        issueInputParameters.addCustomFieldValue(customFiledId, dateStr);
                        break;
                    case "com.atlassian.jira.plugin.system.customfieldtypes:textfield":
                    case "com.atlassian.jira.plugin.system.customfieldtypes:textarea":
                    case "com.atlassian.jira.plugin.system.customfieldtypes:float":
                        issueInputParameters.addCustomFieldValue(customFiledId, entryValue);
                        break;
                    default:
                        throw new IllegalStateException("未适配字段类型: " + customFieldTypeKey);
                }
            }*/

            runTransitionById(mutableIssue, applicationUser, transitionId, issueInputParameters);

            /* 添加文件处理逻辑
              {
                  "map": {
                      "attachmentObject": {
                          "failureDescriptionPicture": [
                              {
                                  "attachmentId": 1683123123123,
                                  "attachmentName": "文件名1"
                              },
                              {
                                  "attachmentId": 1683123123124,
                                  "attachmentName": "文件名2"
                              }
                          ],
                          "noticeOfFailure": [
                              {
                                  "attachmentId": 1683123123123,
                                  "attachmentName": "文件名"
                              }
                          ],
                          "faAnalysisReport": [
                               {
                                  "attachmentId": 1683123123123,
                                  "attachmentName": "文件名"
                              }
                          ],
                          "faAnalysisReport": [
                               {
                                  "attachmentId": 1683123123123,
                                  "attachmentName": "文件名"
                              }
                          ]
                      }
                  }
              }
             */
            try {
                if (ObjectUtil.isNotEmpty(attachmentObject)) {
                    JSONObject jsonObject = JSON.parseObject(attachmentObject, JSONObject.class);
                    addTestFailureAttachment(jsonObject.getJSONArray("failureDescriptionPicture"), isOnline, "失效描述图片", mutableIssue, applicationUser);
                    addTestFailureAttachment(jsonObject.getJSONArray("noticeOfFailure"), isOnline, "失效告知书", mutableIssue, applicationUser);
                    addTestFailureAttachment(jsonObject.getJSONArray("faAnalysisReport"), isOnline, "分析报告", mutableIssue, applicationUser);
                    addTestFailureAttachment(jsonObject.getJSONArray("faBreakReport"), isOnline, "拆解报告", mutableIssue, applicationUser);
                }
            } catch (Exception e) {
                log.error("文件上传失败:{}", Utils.errInfo(e));
            }

        } catch (Exception e) {
            log.error("执行异常:{}", Utils.errInfo(e));
            resultBean.setMessage(e.getMessage());
        }
        return resultBean;
    }

    private void addTestFailureAttachment(JSONArray faBreakReport, int isOnline, String cateName, MutableIssue mutableIssue, ApplicationUser applicationUser) throws Exception {
        if (ObjectUtil.isNotEmpty(faBreakReport)){
            List<AttachmentBean> attachmentBeanList = faBreakReport.stream().map(e -> JSON.parseObject(((JSONObject) e).toJSONString(), AttachmentBean.class)).collect(Collectors.toList());
            attachmentBeanList.forEach(attachmentBean->{
                attachmentBean.setAttachmentUrl((isOnline == 1 ? Constant.pbiUrl : Constant.pbiUrlTest)+"/sysFileInfo/preview?id=" + attachmentBean.getAttachmentId());
                attachmentBean.setAttachmentPath(Utils.getTestFailureFilePath() + "fileTemp" + File.separator);
                attachmentBean.setAttachmentCate(cateName);
            });
            addAttachmentToIssueAndMove(mutableIssue, applicationUser,attachmentBeanList);
        }
    }

    public void addAttachmentToIssueAndMove(MutableIssue mutableIssue, ApplicationUser applicationUser, List<AttachmentBean> attachmentBeanList) throws Exception {

        for (AttachmentBean attachmentBean : attachmentBeanList) {

            log.debug("即将下载文件：fileUrl=" + attachmentBean.getAttachmentUrl() + " savePath=" + attachmentBean.getAttachmentPath() + attachmentBean.getAttachmentName());
            String filePath = Utils.downloadHttpUrl(attachmentBean.getAttachmentUrl(), attachmentBean.getAttachmentPath(), attachmentBean.getAttachmentName());
            File file = new File(filePath);
            if (!file.exists()) {
                throw new IllegalStateException("未找到文件: " + filePath);
            }

            String contentType = Files.probeContentType(file.toPath());

            //附加附件到issue，创建附件同时删除源文件 Map<String, Object> attachmentProperties
            CreateAttachmentParamsBean createAttachmentParamsBean =
                    new CreateAttachmentParamsBean(file, attachmentBean.getAttachmentName(), contentType, applicationUser, mutableIssue, false, false, null, new Date(), false);
            ChangeItemBean attachment = attachmentManager.createAttachment(createAttachmentParamsBean);
            attachmentBean.setAttachmentId(Long.valueOf(attachment.getTo()));
            log.info("文件上传成功，ChangeItemBean结果：" + JSONObject.toJSONString(attachment));
        }
        attachmentBeanList = attachmentBeanList.stream().filter(e -> e.getAttachmentId() != null).collect(Collectors.toList());
        List<String> moveCateNameList = attachmentBeanList.stream()
                .map(AttachmentBean::getAttachmentCate)
                .filter(ObjectUtil::isNotEmpty).collect(Collectors.toList());
        log.info("文件上传成功，需要移动的分类：" + moveCateNameList);
        log.info("文件上传成功，需要移动的文件：" + JSONObject.toJSONString(attachmentBeanList));
        if (ObjectUtil.isNotEmpty(moveCateNameList)) {
            //移动附件到指定分类下  获取issue下附件列表、获取附件分类，存在匹配的分类，转移到该分类下

            //智能附件 分类Map获取，获取附件分类ID
            List<Map<String, Object>> attachmentCategoriesMapList = Utils.getAttachmentCategoriesMapList(mutableIssue);
            for (String cateName : moveCateNameList) {
                Double categoriesId = null;
                for (Map<String, Object> attachmentMap : attachmentCategoriesMapList) {
                    String categoriesName = String.valueOf(attachmentMap.get("name"));
                    if (cateName.equals(categoriesName)) {
                        categoriesId = (Double) attachmentMap.get("id");
                        break;
                    }
                }
//                List<AttachmentBean> attachmentBeanList1 = attachmentBeanList.stream().filter(e -> e.getAttachmentCate().equals(cateName)).collect(Collectors.toList());
                List<Attachment> attachmentList = attachmentBeanList.stream().map(e -> attachmentManager.getAttachment(e.getAttachmentId())).collect(Collectors.toList());
                if (categoriesId != null) {
//                        Utils.moveAttachmentToCategories(mutableIssue.getKey(),categoriesId.longValue(),attachmentList);
                    //移动附件API 数据格式: issueKey=CPGL-550&toCatId=68&attachments=81103&attachments=81104
//                    String data = "issueKey=" + mutableIssue.getKey() + "&toCatId=" + categoriesId + "&attachments=" + attachmentBeanList1.stream().map(e -> e.getAttachmentId().toString()).collect(Collectors.joining("&attachments="));
//                    Utils.moveAttachmentToCategories(data);
                    Utils.moveAttachmentToCategories(mutableIssue.getKey(),categoriesId.longValue(),attachmentList);
                }
            }
        }
    }

    public IssueInputParameters getIssueInputParameters(Long projectId,Map<String,Long> customFiledMap,UpdateCustomFiledBean updateCustomFiledBean) {
        IssueInputParameters issueInputParameters = issueService.newIssueInputParameters();
        Map<String, String> map = updateCustomFiledBean.getMap();
        //翻译为Jira字段值
        for (Map.Entry<String, String> entry : map.entrySet()) {
            String entryKey = entry.getKey();
            String entryValue = entry.getValue();
            Long customFiledId = customFiledMap.getOrDefault(entryKey, 0L);
            if (customFiledId == 0) {
                customFiledId = NumberUtil.isNumber(entryKey)
                        ? Long.parseLong(entryKey)
                        : (entryKey.startsWith("customfield_") ? Long.parseLong(entryKey.split("_")[1]) : 0L);
            }
            CustomField customFiled = customFiledId == 0 ? getCustomFieldByConfig(entryKey) : customFieldManager.getCustomFieldObject(customFiledId);
            if (customFiled == null || entryValue == null || "".equals(entryValue)) {
                if ("summary".equals(entry.getKey())) {
                    issueInputParameters.setSummary(entryValue);
//                    mutableIssue.setSummary(entryValue);
//                    }else if ("issueType".equals(entry.getKey())) {
//                        if ("1".equals(entryValue)) {
//                            mutableIssue.setIssueTypeId(Constant.platformIssueTypeId + "");
//                        } else if ("2".equals(entryValue)) {
//                            mutableIssue.setIssueTypeId(Constant.topicIssueTypeId + "");
//                        }
                }else if ("comment".equals(entryKey)) {
                    issueInputParameters.setComment(entryValue);
                } else if ("reporter".equals(entryKey)) {
                    ApplicationUser user = ComponentAccessor.getUserManager().getUserByName(entryValue);
                    if (user == null) {
                        throw new IllegalArgumentException("用户工号传递错误，jira未找到用户：" + entryValue);
                    }
                    issueInputParameters.setReporterId(user.getUsername());
                } else if ("assignee".equals(entryKey)) {
                    ApplicationUser user = ComponentAccessor.getUserManager().getUserByName(entryValue);
                    if (user == null) {
                        throw new IllegalArgumentException("用户工号传递错误，jira未找到用户：" + entryValue);
                    }
                    issueInputParameters.setAssigneeId(entryValue);
                } else if ("priority".equals(entryKey)) {
                    issueInputParameters.setPriorityId(entryValue);
                } else if ("description".equals(entryKey)) {
                    issueInputParameters.setDescription(entryValue);
                }
                continue;
            }

            String customFieldTypeKey = customFiled.getCustomFieldType().getKey();
            switch (customFieldTypeKey) {
                case "com.atlassian.jira.plugin.system.customfieldtypes:select":
                    Long optionId = NumberUtil.isNumber(entryValue) ? Utils.getSelectOptionId(entryKey, Long.parseLong(entryValue)) : Utils.getOptionIdByName(projectId, customFiled, entryValue);
                    //根据ID获取
                    issueInputParameters.addCustomFieldValue(customFiledId, String.valueOf(optionId));
                    break;
                case "com.atlassian.jira.plugin.system.customfieldtypes:cascadingselect":
                    Option option = NumberUtil.isNumber(entryValue) ? optionsManager.findByOptionId(Long.parseLong(entryValue)) : Utils.getOptionByName(projectId, customFiled, entryValue);
                    if (ObjectUtils.isEmpty(option)) {
                        break;//未找到选项，不设置值
//                        throw new IllegalArgumentException("根据选项id未找到选项：" + entryKey);
                    }
                    Option parentOption = option.getParentOption();
                    if (ObjectUtils.isEmpty(parentOption)) {
                        issueInputParameters.addCustomFieldValue(
                                customFiledId,
                                String.valueOf(option.getOptionId())
                        );
                    } else {
                        issueInputParameters.addCustomFieldValue(
                                customFiledId,
                                String.valueOf(parentOption.getOptionId())
                        );
                        issueInputParameters.addCustomFieldValue(
                                "customfield_" + customFiledId + ":1",
                                String.valueOf(option.getOptionId())
                        );
                    }
                    break;
                case "com.atlassian.jira.plugin.system.customfieldtypes:userpicker":
                    ApplicationUser user = userManager.getUserByName(entryValue);
                    if (user == null) {
                        throw new IllegalArgumentException("用户工号传递错误，jira未找到用户：" + entryValue);
                    }
                    issueInputParameters.addCustomFieldValue(customFiledId, user.getUsername());
                    break;
                case "com.atlassian.jira.plugin.system.customfieldtypes:multiuserpicker":
                    for (String userName : Convert.toList(String.class, entryValue)) {
                        ApplicationUser userByName = userManager.getUserByName(userName);
                        if (userByName == null) {
                            throw new IllegalArgumentException("用户工号传递错误，jira未找到用户：" + userName);
                        }
                    }
                    issueInputParameters.addCustomFieldValue(customFiledId, entryValue);
                    break;
                case "com.atlassian.jira.plugin.system.customfieldtypes:datepicker":
                case "com.atlassian.jira.plugin.system.customfieldtypes:datetime":
                    //格式 yyyy-MM-dd HH:mm:ss
                    entryValue = entryValue.length() == 10 ? entryValue + " 00:00:00" : entryValue;
                    issueInputParameters.addCustomFieldValue(customFiledId, Utils.getDateTimeStr(entryValue));
//                    Date date = java.sql.Date.valueOf(entryValue);
//                    DateTimeFormatter dateFormatter = ComponentAccessor.getComponent(DateTimeFormatter.class);
//                    dateFormatter = dateFormatter.forLoggedInUser();
//                    String dateStr = dateFormatter.withStyle(DateTimeStyle.DATE_PICKER).format(date);
//                    issueInputParameters.addCustomFieldValue(customFiledId, dateStr);
                    break;
                case "com.atlassian.jira.plugin.system.customfieldtypes:textfield":
                case "com.atlassian.jira.plugin.system.customfieldtypes:textarea":
                case "com.atlassian.jira.plugin.system.customfieldtypes:float":
                case "com.atlassian.jira.plugin.system.customfieldtypes:labels":
                case "com.atlassian.jira.plugin.system.customfieldtypes:url":
                    issueInputParameters.addCustomFieldValue(customFiledId, entryValue);
                    break;
                default:
                    throw new IllegalStateException("未适配字段类型: " + customFieldTypeKey);
            }
        }
        return issueInputParameters;
    }

    public void runTransitionById(MutableIssue mutableIssue, ApplicationUser currentUser, Long transitionId, IssueInputParameters issueInputParameters) {
        stringSet.add(mutableIssue.getKey());
        IssueService.TransitionValidationResult validationResult = issueService.validateTransition(currentUser, mutableIssue.getId(), transitionId.intValue(), issueInputParameters);
        if (validationResult.isValid()) {
            IssueService.IssueResult transitionResult = issueService.transition(currentUser, validationResult);
            if (!transitionResult.isValid()) {
                throw new IllegalArgumentException(transitionResult.getErrorCollection().toString());
            }
        } else {
            throw new IllegalArgumentException(validationResult.getErrorCollection().toString());
        }
        stringSet.remove(mutableIssue.getKey());
    }

    public void runTransitionByName(MutableIssue mutableIssue, ApplicationUser currentUser, String transitionName,IssueInputParameters issueInputParameters) {
        stringSet.add(mutableIssue.getKey());
        String statusId = mutableIssue.getStatusId();
        JiraWorkflow workflow = ComponentAccessor.getWorkflowManager().getWorkflow(mutableIssue);
        List<Integer> transitionIdList = new ArrayList<>();
        Collection<WorkflowTransitionEntry> workflowTransitionEntryCollection = transitionManager.getTransitions(Collections.singletonList(workflow));
        for (WorkflowTransitionEntry workflowTransitionEntry : workflowTransitionEntryCollection) {
            Collection<TransitionEntry> transitionEntryCollection = workflowTransitionEntry.getTransitions();
            String workflowName = workflowTransitionEntry.getWorkflow().getName();
            if (workflow.getName().equals(workflowName)) {
                transitionIdList = transitionEntryCollection.stream().filter(e -> (statusId.equals(e.getFromStatusId() + "") || e.getIsGlobal()) && transitionName.equals(e.getName().trim())).map(TransitionEntry::getTransitionId).collect(Collectors.toList());
                if (transitionIdList.isEmpty() && "批准".equals(transitionName)) {//成果副院长页面单独处理
                    transitionIdList = transitionEntryCollection.stream().filter(e -> (statusId.equals(e.getFromStatusId() + "") || e.getIsGlobal()) && "通过".equals(e.getName().trim())).map(TransitionEntry::getTransitionId).collect(Collectors.toList());
                }
            }
        }
        if (transitionIdList.isEmpty()) {
            throw new IllegalArgumentException("未找到该名称的转换");
        }

        Optional<IssueService.TransitionValidationResult> first = transitionIdList.stream()
                .map(e -> issueService.validateTransition(currentUser, mutableIssue.getId(), e, issueInputParameters))
                .filter(ServiceResultImpl::isValid).findFirst();
        if (first.isPresent()) {
            IssueService.IssueResult issueResult = issueService.transition(currentUser, first.get());
            if (!issueResult.isValid()) {
//                log.error("转换执行错误：{}", issueResult.getErrorCollection());
                throw new IllegalArgumentException("转换执行错误："+issueResult.getErrorCollection());
            }
        } else {
//            log.error("不存在可执行的转换：{}", mutableIssue.getKey());
            throw new IllegalArgumentException("不存在可执行的转换：" + mutableIssue.getKey());
        }
        stringSet.remove(mutableIssue.getKey());
    }

    public ResultBean createIssue(int isOnline, UpdateCustomFiledBean updateCustomFiledBean) {
        log.error("进入通用问题创建接口:" + updateCustomFiledBean + " isOnline:" + isOnline);
        ResultBean resultBean = new ResultBean();
        try {
            String userName = updateCustomFiledBean.getUserName();
            ApplicationUser currentUser = userManager.getUserByName(userName);
            if (currentUser == null) {
                throw new IllegalArgumentException("用户工号传递错误，jira未找到用户：" + userName);
            }

            JiraAuthenticationContext context = ComponentAccessor.getComponent(JiraAuthenticationContext.class);
            context.setLoggedInUser(currentUser);
            //productRZ:testProject;productRZ:returnSample;
            UpdateCustomFiledBean createIssueParam = Utils.getCreateIssueType(isOnline,updateCustomFiledBean.getIssueType());
            Long projectId = createIssueParam.getProjectId();
            String issueTypeId = createIssueParam.getIssueType();

            /*创建issue参数*/
            IssueInputParameters issueInputParameters = getIssueInputParameters(projectId, Utils.getReviewListCustomFiledMap(), updateCustomFiledBean);
            // 设置项目ID
            issueInputParameters.setProjectId(projectId);
            // 设置问题类型
            issueInputParameters.setIssueTypeId(issueTypeId);
            // 设置报告人
            issueInputParameters.setReporterId(currentUser.getUsername());


            //创建issue参数校验
            IssueService.CreateValidationResult createValidationResult = issueService.validateCreate(currentUser, issueInputParameters);

            if (!createValidationResult.isValid()) {
                throw new CreateException("创建问题参数校验失败：" + createValidationResult.getErrorCollection());
            }
            //创建issue
            IssueService.IssueResult issueResult = issueService.create(currentUser, createValidationResult);
            if (!issueResult.isValid()) {
                throw new CreateException("创建问题失败：" + issueResult.getErrorCollection());
            }
            MutableIssue mutableIssue = issueResult.getIssue();

            Map<String, Object> resultMap = new HashMap<>();
            resultMap.put("issueId", mutableIssue.getId());
            resultMap.put("issueKey", mutableIssue.getKey());
            log.info("通用问题创建成功：{}", resultMap);
            resultBean.setValue(resultMap);

        } catch (Exception e) {
            log.error(Utils.errInfo(e));
            resultBean.setMessage(e.getMessage());
        }
        return resultBean;
    }

    public ResultBean createSubIssue(int isOnline, UpdateCustomFiledBean updateCustomFiledBean) {
        log.error("进入通用问题创建接口:" + updateCustomFiledBean + " isOnline:" + isOnline);
        ResultBean resultBean = new ResultBean();
        try {
            String userName = updateCustomFiledBean.getUserName();
            ApplicationUser currentUser = userManager.getUserByName(userName);
            if (currentUser == null) {
                throw new IllegalArgumentException("用户工号传递错误，jira未找到用户：" + userName);
            }
            MutableIssue parentIssue = ComponentAccessor.getIssueManager().getIssueObject(updateCustomFiledBean.getParentIssueId());

            JiraAuthenticationContext context = ComponentAccessor.getComponent(JiraAuthenticationContext.class);
            context.setLoggedInUser(currentUser);
            //productRZ:testProject;productRZ:returnSample;
            UpdateCustomFiledBean createIssueParam = Utils.getCreateIssueType(isOnline,updateCustomFiledBean.getIssueType());
            Long projectId = createIssueParam.getProjectId();
            String issueTypeId = createIssueParam.getIssueType();

            /*创建issue参数*/
            IssueInputParameters issueInputParameters = getIssueInputParameters(projectId, Utils.getReviewListCustomFiledMap(), updateCustomFiledBean);
            // 设置项目ID
            issueInputParameters.setProjectId(projectId);
            // 设置问题类型
            issueInputParameters.setIssueTypeId(issueTypeId);
            // 设置报告人
            issueInputParameters.setReporterId(currentUser.getUsername());


            //创建issue参数校验
            IssueService.CreateValidationResult createValidationResult = issueService.validateSubTaskCreate(currentUser, parentIssue.getId(), issueInputParameters);

            if (!createValidationResult.isValid()) {
                throw new CreateException("创建问题参数校验失败：" + createValidationResult.getErrorCollection());
            }
            //创建issue
            IssueService.IssueResult issueResult = issueService.create(currentUser, createValidationResult);
            if (!issueResult.isValid()) {
                throw new CreateException("创建问题失败：" + issueResult.getErrorCollection());
            }
            MutableIssue mutableIssue = issueResult.getIssue();
            SubTaskManager subTaskManager = ComponentAccessor.getSubTaskManager();
            subTaskManager.createSubTaskIssueLink(parentIssue, mutableIssue, currentUser);
            mutableIssue.setParentId(parentIssue.getId());
            ComponentAccessor.getIssueManager().updateIssue(currentUser, mutableIssue, EventDispatchOption.ISSUE_UPDATED, false);

            Map<String, Object> resultMap = new HashMap<>();
            resultMap.put("issueId", mutableIssue.getId());
            resultMap.put("issueKey", mutableIssue.getKey());
            log.info("通用问题创建成功：{}", resultMap);
            resultBean.setValue(resultMap);

        } catch (Exception e) {
            log.error(Utils.errInfo(e));
            resultBean.setMessage(e.getMessage());
        }
        return resultBean;
    }

    public Long getCustomFieldIdByConfig(String fieldName) {
        // 尝试从缓存获取
        Long fieldId = fieldIdCache.getOrDefault(fieldName, -1L);
        if (isCacheExpired() || fieldId == -1L) {
            synchronized (this) {
                // 双重检查锁定
                if (isCacheExpired() || fieldId == -1L) {
                    loadCache();
                }
            }
            fieldId = fieldIdCache.getOrDefault(fieldName, -1L);
        }
        return fieldId;
    }

    public CustomField getCustomFieldByConfig(String fieldName) {
        return customFieldManager.getCustomFieldObject(getCustomFieldIdByConfig(fieldName));
    }

    private boolean isCacheExpired() {
        return System.currentTimeMillis() - lastLoadTime.get() > CACHE_EXPIRY;
    }

    private synchronized void loadCache() {
        try {
            log.debug("Loading custom field mapping from application properties");

            // 从应用属性获取配置
            String customFieldMap = ComponentAccessor.getApplicationProperties()
                    .getString(Constant.customFieldNameAndIdMap);

            // 处理空配置
            if (customFieldMap == null || customFieldMap.isEmpty()) {
                log.warn("Custom field mapping configuration is empty");
                fieldIdCache.clear();
                lastLoadTime.set(System.currentTimeMillis());
                return;
            }

            // 解析JSON配置
            JSONObject config = JSONObject.parseObject(customFieldMap);

            // 清空并重建缓存
            fieldIdCache.clear();
            for (Map.Entry<String, Object> entry : config.entrySet()) {
                String key = entry.getKey();
                Object value = entry.getValue();

                // 处理不同类型数值
                if (value instanceof Long) {
                    fieldIdCache.put(key, (Long) value);
                } else if (value instanceof Number) {
                    fieldIdCache.put(key, ((Number) value).longValue());
                } else if (value instanceof String) {
                    try {
                        fieldIdCache.put(key, Long.parseLong((String) value));
                    } catch (NumberFormatException e) {
                        log.warn("Invalid field ID format for key {}: {}", key, value);
                    }
                }
            }

            lastLoadTime.set(System.currentTimeMillis());
            log.info("Loaded {} custom field mappings", fieldIdCache.size());
        } catch (Exception e) {
            log.error("Failed to load custom field mapping", e);
            // 保留旧缓存，但更新加载时间避免频繁重试
            lastLoadTime.set(System.currentTimeMillis());
        }
    }

//    public void testUserManager(){
//        userManager.
//        avatarManager.update();
//        avatarService.setCustomUserAvatar();
//        ComponentAccessor.getOptionsManager().findByOptionId();
//    }
}
