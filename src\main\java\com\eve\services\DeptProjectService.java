package com.eve.services;

import com.atlassian.activeobjects.external.ActiveObjects;
import com.eve.ao.DeptProjectAo;
import com.eve.beans.DeptProjectBean;
import com.eve.beans.ResultBean;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;

public class DeptProjectService {
    private ActiveObjects ao;

    public DeptProjectService(ActiveObjects ao) {
        this.ao = ao;
    }

    public List<DeptProjectBean> list(String deptName) {
        List<DeptProjectBean> deptProjectList = new ArrayList<>();
        try {
            DeptProjectAo[] deptProjectAos = null;
            if (deptName == null || "".equals(deptName)) {
                deptProjectAos = ao.find(DeptProjectAo.class);
            } else {
                deptProjectAos = ao.find(DeptProjectAo.class, "DEPT_NAME LIKE '%" + deptName + "%'");
            }
            for (DeptProjectAo deptProjectAo : deptProjectAos) {
                DeptProjectBean deptProjectBean = new DeptProjectBean();
                BeanUtils.copyProperties(deptProjectAo, deptProjectBean);
                deptProjectList.add(deptProjectBean);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return deptProjectList;
    }

    public DeptProjectBean getByDeptId(Long deptId) {
        DeptProjectBean deptProjectBean = new DeptProjectBean();
        try {
            DeptProjectAo[] deptProjectAos = null;
            if (deptId == 0 || deptId.equals(null)) {
                deptProjectAos = ao.find(DeptProjectAo.class);
            } else {
                deptProjectAos = ao.find(DeptProjectAo.class, "dept_id = "+deptId);
            }
            BeanUtils.copyProperties(deptProjectAos[0], deptProjectBean);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return deptProjectBean;
    }

    public ResultBean getById(Long id) {
        ResultBean resultBean = new ResultBean();
        try {
            DeptProjectAo deptProjectAo = ao.get(DeptProjectAo.class, id);
            DeptProjectBean depProjectBean = new DeptProjectBean();
            BeanUtils.copyProperties(deptProjectAo, depProjectBean);
            resultBean.setValue(depProjectBean);
        } catch (Exception e) {
            e.printStackTrace();
            resultBean.setMessage(e.getMessage());
        }
        return resultBean;
    }

    public ResultBean insert(DeptProjectBean deptProjectBean) {
        ResultBean resultBean = new ResultBean();
        try {
            DeptProjectAo deptProjectAo =
                    deptProjectBean.getId() != null && deptProjectBean.getId() != 0
                            ? ao.get(DeptProjectAo.class, deptProjectBean.getId())
                            : ao.create(DeptProjectAo.class);
            deptProjectAo.setDeptName(deptProjectBean.getDeptName());
            deptProjectAo.setProjectId(deptProjectBean.getProjectId());
            deptProjectAo.setIssueTypeId(deptProjectBean.getIssueTypeId());
            deptProjectAo.setDeptId(deptProjectBean.getDeptId());
            deptProjectAo.setExecutor(deptProjectBean.getExecutor());
            deptProjectAo.save();
            
            resultBean.setValue(deptProjectAo.getId());
            
        } catch (Exception e) {
            e.printStackTrace();
            resultBean.setMessage(e.getMessage());
        }
        return resultBean;
    }

    public ResultBean delete(Long id) {
        ResultBean resultBean = new ResultBean();
        try {
            DeptProjectAo[] deptProjectAos = ao.find(DeptProjectAo.class, "ID = ?", id);
            ao.delete(deptProjectAos);
        } catch (Exception e) {
            e.printStackTrace();
            resultBean.setMessage(e.getMessage());
        }
        return resultBean;
    }

}
