package com.eve.services;

import com.atlassian.activeobjects.external.ActiveObjects;
import com.atlassian.jira.component.ComponentAccessor;
import com.atlassian.jira.issue.context.JiraContextNode;
import com.atlassian.jira.issue.context.ProjectContext;
import com.atlassian.jira.issue.customfields.option.Option;
import com.atlassian.jira.issue.customfields.option.Options;
import com.atlassian.jira.issue.fields.CustomField;
import com.eve.ao.DeptsAo;
import com.eve.beans.DeptsBean;
import com.eve.beans.ResultBean;
import com.eve.utils.Constant;
import com.eve.utils.Utils;

import java.util.ArrayList;
import java.util.List;

public class DeptsService {
    
    private ActiveObjects ao;

    public DeptsService(ActiveObjects ao) {
        this.ao = ao;
    }

    public List<DeptsBean> list(String nameMatch) {
        
        List<DeptsBean> deptsList = new ArrayList<>();
        try {
            DeptsAo[] deptsAos = null;
            CustomField testCustfield = Utils.getCustomFieldByID(Constant.subDepartCustID);
            JiraContextNode jiraContextNode = new ProjectContext(Constant.travelReportProjectId, ComponentAccessor.getProjectManager());
            Options options = testCustfield.getOptions("", jiraContextNode);
            if (nameMatch == null || "".equals(nameMatch)) {
                deptsAos = ao.find(DeptsAo.class);
            } else {
                deptsAos = ao.find(DeptsAo.class, "NAME_MATCH LIKE '%" + nameMatch + "%'");
            }
            for (DeptsAo deptsAo : deptsAos) {
                Long deptId = deptsAo.getDeptId();
                String deptName = null;
                Long subDeptId = deptsAo.getSubDeptId();
                String subDeptName = null;
                for (Option option : options) {
                    if (deptId.equals(option.getOptionId())) {
                        deptName = option.getValue();
                        for (Option childOption : option.getChildOptions()) {
                            if (subDeptId.equals(childOption.getOptionId())) {
                                subDeptName = childOption.getValue();
                                break;
                            }
                        }
                        break;
                    }
                }
                DeptsBean deptsBean = new DeptsBean(deptsAo.getId(), deptId, deptName, subDeptId, subDeptName, deptsAo.getNameMatch());
                deptsList.add(deptsBean);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return deptsList;
    }

    public DeptsBean getByName(String nameMatch) {
        DeptsBean deptsBean = null;
        try {
            DeptsAo[] deptsAos = null;
            CustomField testCustfield = Utils.getCustomFieldByID(Constant.subDepartCustID);
            JiraContextNode jiraContextNode = new ProjectContext(Constant.travelReportProjectId, ComponentAccessor.getProjectManager());
            Options options = testCustfield.getOptions("", jiraContextNode);
            if (nameMatch == null || "".equals(nameMatch)) {
                deptsAos = ao.find(DeptsAo.class);
            } else {
                deptsAos = ao.find(DeptsAo.class, "POSITION('"+nameMatch+"' IN name_match) or POSITION(name_match IN '"+nameMatch+"')");
            }
            Long deptId = deptsAos[0].getDeptId();
            String deptName = null;
            Long subDeptId = deptsAos[0].getSubDeptId();
            String subDeptName = null;
            for (Option option : options) {
                if (deptId.equals(option.getOptionId())) {
                    deptName = option.getValue();
                    for (Option childOption : option.getChildOptions()) {
                        if (subDeptId.equals(childOption.getOptionId())) {
                            subDeptName = childOption.getValue();
                            break;
                        }
                    }
                    break;
                }
            }
            deptsBean = new DeptsBean(deptsAos[0].getId(), deptId, deptName, subDeptId, subDeptName, deptsAos[0].getNameMatch());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return deptsBean;
    }

    public ResultBean getById(Long id) {
        ResultBean resultBean = new ResultBean();
        try {
            CustomField testCustfield = Utils.getCustomFieldByID(Constant.subDepartCustID);
            JiraContextNode jiraContextNode = new ProjectContext(Constant.travelReportProjectId, ComponentAccessor.getProjectManager());
            Options options = testCustfield.getOptions("", jiraContextNode);
            DeptsAo deptsAo = ao.get(DeptsAo.class, id);
            Long deptId = deptsAo.getDeptId();
            String deptName = null;
            Long subDeptId = deptsAo.getSubDeptId();
            String subDeptName = null;
            for (Option option : options) {
                if (deptId.equals(option.getOptionId())) {
                    deptName = option.getValue();
                    for (Option childOption : option.getChildOptions()) {
                        if (subDeptId.equals(childOption.getOptionId())) {
                            subDeptName = childOption.getValue();
                            break;
                        }
                    }
                    break;
                }
            }
            DeptsBean deptsBean = new DeptsBean(deptsAo.getId(), deptId, deptName, subDeptId, subDeptName, deptsAo.getNameMatch());
            resultBean.setValue(deptsBean);
        } catch (Exception e) {
            e.printStackTrace();
            resultBean.setMessage(e.getMessage());
        }
        return resultBean;
    }

    public ResultBean insert(DeptsBean deptsBean) {
        ResultBean resultBean = new ResultBean();
        try {
            DeptsAo deptsAo =
                    deptsBean.getId() != null && deptsBean.getId() != 0
                            ? ao.get(DeptsAo.class, deptsBean.getId())
                            : ao.create(DeptsAo.class);
            deptsAo.setNameMatch(deptsBean.getNameMatch());
            deptsAo.setDeptId(deptsBean.getDeptId());
            deptsAo.setSubDeptId(deptsBean.getSubDeptId());
            deptsAo.save();
            resultBean.setValue(deptsAo.getId());
        } catch (Exception e) {
            e.printStackTrace();
            resultBean.setMessage(e.getMessage());
        }
        return resultBean;
    }

    public ResultBean delete(Long id) {
        ResultBean resultBean = new ResultBean();
        try {
            DeptsAo[] deptsAos = ao.find(DeptsAo.class, "ID = ?", id);
            ao.delete(deptsAos);
        } catch (Exception e) {
            e.printStackTrace();
            resultBean.setMessage(e.getMessage());
        }
        return resultBean;
    }
}
