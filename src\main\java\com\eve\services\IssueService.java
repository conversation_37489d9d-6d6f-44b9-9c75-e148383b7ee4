package com.eve.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.atlassian.activeobjects.external.ActiveObjects;
import com.atlassian.crowd.embedded.api.Group;
import com.atlassian.jira.component.ComponentAccessor;
import com.atlassian.jira.issue.Issue;
import com.atlassian.jira.issue.IssueInputParameters;
import com.atlassian.jira.issue.context.JiraContextNode;
import com.atlassian.jira.issue.context.ProjectContext;
import com.atlassian.jira.issue.fields.CustomField;
import com.atlassian.jira.issue.issuetype.IssueType;
import com.atlassian.jira.issue.status.Status;
import com.atlassian.jira.project.Project;
import com.atlassian.jira.security.JiraAuthenticationContext;
import com.atlassian.jira.transition.DefaultTransitionManager;
import com.atlassian.jira.transition.TransitionEntry;
import com.atlassian.jira.transition.TransitionManager;
import com.atlassian.jira.transition.WorkflowTransitionEntry;
import com.atlassian.jira.user.ApplicationUser;
import com.atlassian.jira.workflow.JiraWorkflow;
import com.eve.beans.DeptProjectBean;
import com.eve.beans.DeptsBean;
import com.eve.beans.ResultBean;
import com.eve.beans.TripRequestBean;
import com.eve.utils.Constant;
import com.eve.utils.Utils;
import com.opensymphony.workflow.loader.ActionDescriptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;


public class IssueService {

    private static final Logger log = LoggerFactory.getLogger(IssueService.class);
    private ActiveObjects ao;

    @Resource
    private DeptProjectService deptProjectService;

    @Resource
    private DeptsService deptsService;

    public IssueService(ActiveObjects ao) {
        this.ao = ao;
    }

    public ResultBean createIssue(TripRequestBean tripRequestBean) {
        ResultBean resultBean = new ResultBean();
        if (tripRequestBean.getBoss() == null || "".equals(tripRequestBean.getBoss())) {
            tripRequestBean.setBoss("083779");
        }
        String data = JSON.toJSONString(tripRequestBean, SerializerFeature.WriteMapNullValue);
        log.error("创建出差报告任务参数：" + data);
//        TripRequestMsgAO tripRequestMsgAO = ao.create(TripRequestMsgAO.class);
//        tripRequestMsgAO.setApplicant(tripRequestBean.getApplicant());
//        tripRequestMsgAO.setTripReason(tripRequestBean.getTripReason());
//        tripRequestMsgAO.setTripAddress(tripRequestBean.getTripAddress());
//        tripRequestMsgAO.setTripDays(tripRequestBean.getTripDays());
//        tripRequestMsgAO.setDept(tripRequestBean.getDept());
//        tripRequestMsgAO.setTripStartDate(tripRequestBean.getTripStartDate());
//        tripRequestMsgAO.setTripEndDate(tripRequestBean.getTripEndDate());
//        tripRequestMsgAO.setBoss(tripRequestBean.getBoss());
//        tripRequestMsgAO.setCreateDate(new java.sql.Timestamp(System.currentTimeMillis()));
        try {
            DeptsBean deptsBean = deptsService.getByName(tripRequestBean.getDept());

            String applicant = tripRequestBean.getApplicant();
            if (null == deptsBean || deptsBean.getDeptId() == null) {
                resultBean.setValue("部门未配置");
                return resultBean;
//                deptsBean = deptsService.getByName("DLTEST");
            }

//            if (null == deptsBean) {
//                resultBean.setMessage("部门匹配不正确");
//                return resultBean;
//            }
            String data1 = JSON.toJSONString(deptsBean, SerializerFeature.WriteMapNullValue);
            log.error("匹配之后的部门：" + data1);
            DeptProjectBean deptProjectBean = deptProjectService.getByDeptId(deptsBean.getDeptId());

            //模拟用户登录
            JiraAuthenticationContext context = ComponentAccessor.getComponent(JiraAuthenticationContext.class);
            ApplicationUser reporter = ComponentAccessor.getUserManager().getUser(deptProjectBean.getExecutor());
            if (reporter == null) {
                resultBean.setMessage("报告人配置错误");
                return resultBean;
            }
            context.setLoggedInUser(reporter);

            // 获取项目信息
            Project project = ComponentAccessor.getProjectManager().getProjectObj(deptProjectBean.getProjectId());
            if (project == null) {
                resultBean.setMessage("项目不存在");
                return resultBean;
            }
            // 获取问题类型信息
            IssueType issueType = ComponentAccessor.getConstantsManager().getIssueType(deptProjectBean.getIssueTypeId() + "");
            if (issueType == null) {
                resultBean.setMessage("问题类型不存在");
                return resultBean;
            }
            log.error("项目信息、问题类型获取成功");
            // 获取issue的服务
            com.atlassian.jira.bc.issue.IssueService issueService = ComponentAccessor.getIssueService();

            /*创建issue参数*/
            IssueInputParameters issueInputParameters = issueService.newIssueInputParameters();
            String tripUserName = tripRequestBean.getApplicant();
            ApplicationUser tripUser = Utils.getApplicationUserByName(tripUserName);
            if (tripUser == null) {
                while (tripUserName.length() < 6) {
                    tripUserName = "0" + tripUserName;
                }
                tripUser = Utils.getApplicationUserByName(tripUserName);
                if (tripUser == null) {
                    throw new IllegalArgumentException("JIRA系统不存在该用户");
                }
            }
            //是否具有查看出差报告的权限
            Collection<String> allGroupNames = ComponentAccessor.getGroupManager().getGroupNamesForUser(tripUser);
            if (!allGroupNames.contains("动力电池研究院")) {
                Group group = ComponentAccessor.getGroupManager().getGroup("动力电池研究院");
                if (group != null) {
                    ComponentAccessor.getUserUtil().addUserToGroup(group, tripUser);
                }
            }
            String boss = tripRequestBean.getBoss();
            ApplicationUser bossUser = Utils.getApplicationUserByName("083779");
            if (bossUser == null) {
                while (boss.length() < 6) {
                    boss = "0" + boss;
                }
                bossUser = Utils.getApplicationUserByName(boss);
                if (bossUser == null) {
                    throw new IllegalArgumentException("JIRA系统不存在该用户");
                }
            }
            log.error("参数校验通过");
            // 设置项目类型
            issueInputParameters.setProjectId(project.getId());
            // 设置问题类型
            issueInputParameters.setIssueTypeId(issueType.getId());
            // 设置经办人
            issueInputParameters.setAssigneeId(reporter.getUsername());
            // 设置报告人
            issueInputParameters.setReporterId(reporter.getUsername());
            //设置概要
            issueInputParameters.setSummary(
                    tripUser.getDisplayName()
                    + "出差报告-" + tripRequestBean.getTripAddress()
                    + "-" + tripRequestBean.getTripStartDate().replace("-", ".")
                    + "-" + tripRequestBean.getTripEndDate().replace("-", ".")
            );

            //设置申请人
            CustomField applicantCustField = Utils.getCustomFieldByID(Constant.applicantCustID);
            issueInputParameters.addCustomFieldValue(
                    applicantCustField.getIdAsLong(),
                    tripUser.getUsername()
            );

            //设置上司
//            if (boss != null) {
//                CustomField bossCustField = Utils.getCustomFieldByID(Constant.bossCustID);
//                issueInputParameters.addCustomFieldValue(
//                        bossCustField.getIdAsLong(),
//                        bossUser.getUsername()
//                );
//            }
            //设置出差事由
            CustomField tripReasonCustField = Utils.getCustomFieldByID(Constant.tripReasonCustID);
            issueInputParameters.addCustomFieldValue(
                    tripReasonCustField.getIdAsLong(),
                    tripRequestBean.getTripReason()
            );

            //设置出差地点
            CustomField tripAddressCustField = Utils.getCustomFieldByID(Constant.tripAddressCustID);
            issueInputParameters.addCustomFieldValue(
                    tripAddressCustField.getIdAsLong(),
                    tripRequestBean.getTripAddress()
            );

            //设置出差天数
            CustomField tripDaysCustField = Utils.getCustomFieldByID(Constant.tripDaysCustID);
            issueInputParameters.addCustomFieldValue(
                    tripDaysCustField.getIdAsLong(),
                    tripRequestBean.getTripDays()
            );

            //设置开始日期
            CustomField tripStartDateCustField = Utils.getCustomFieldByID(Constant.tripStartDateCustID);
            issueInputParameters.addCustomFieldValue(
                    tripStartDateCustField.getIdAsLong(),
                    Utils.getDateStr(tripRequestBean.getTripStartDate())
            );

            //设置结束日期
            CustomField tripEndDateCustField = Utils.getCustomFieldByID(Constant.tripEndDateCustID);
            issueInputParameters.addCustomFieldValue(
                    tripEndDateCustField.getIdAsLong(),
                    Utils.getDateStr(tripRequestBean.getTripEndDate())
            );

            //设置到期日期

            String dueDate = Utils.plusDates(4, tripRequestBean.getTripEndDate());
            issueInputParameters.setDueDate(Utils.getDateStr(dueDate));

            //设置子部门id
//            CustomField tripDepartCustField = Utils.getCustomFieldByID(Constant.subDepartCustID);
//            issueInputParameters.addCustomFieldValue(
//                    tripDepartCustField.getIdAsLong(),
//                    String.valueOf(deptsBean.getDeptId())
//            );
//            issueInputParameters.addCustomFieldValue(
//                    Constant.subDepartKey,
//                    String.valueOf(deptsBean.getSubDeptId())
//            );

            JiraContextNode jiraContextNode = new ProjectContext(project.getId(), ComponentAccessor.getProjectManager());
            com.atlassian.jira.bc.issue.IssueService.CreateValidationResult createValidationResult = issueService.validateCreate(reporter, issueInputParameters);

            if (!createValidationResult.isValid()) {

                resultBean.setMessage(createValidationResult.getErrorCollection().toString());
                return resultBean;
            }
            com.atlassian.jira.bc.issue.IssueService.IssueResult createResult = issueService.create(reporter, createValidationResult);
            Issue issue = createResult.getIssue();
//            ChangeHistoryManager changeHistoryManager = ComponentAccessor.getChangeHistoryManager();
//            List<ChangeHistoryItem> changeHistoryItemList = ComponentAccessor.getChangeHistoryManager().getAllChangeItems(issue);
//            for (ChangeHistoryItem ChangeItem : changeHistoryItemList) {
//                if (ChangeItem.getField() == "status") {
//
//                }
//            }
            /*
            //设置子部门id
            Option option = ComponentAccessor.getOptionsManager().findByOptionId(deptsBean.getSubDeptId());
            Option parentOption = option.getParentOption();
            Map<Object, Object> map = new HashMap();
            map.put(null,parentOption);
            map.put("1",option);
            CustomField tripDepartCustField = Utils.getCustomFieldByID(Constant.subDepartCustID);
            issue.setCustomFieldValue(tripDepartCustField,map);
            ComponentAccessor.getIssueManager().updateIssue(reporter, issue, EventDispatchOption.ISSUE_UPDATED, false);
            */
            resultBean.setValue(issue.getKey());
        } catch (Exception e) {
            log.error("创建出差报告任务失败：" + e.getMessage());
            resultBean.setMessage("错误：" + e.getMessage());
        }

        return resultBean;
    }

    public void saveTripMsgToAo(TripRequestBean tripRequestBean) {
        Issue issue = null;
        Status status = issue.getStatus();
        String statusId = status.getId();
        //根据问题获取工作流
        JiraWorkflow workflow = ComponentAccessor.getWorkflowManager().getWorkflow(issue);
        String workflowName = workflow.getName();
        Collection<ActionDescriptor> workflowAllActions = workflow.getAllActions();
        for (ActionDescriptor workflowAction : workflowAllActions) {
            List postFunctions = workflowAction.getPostFunctions();
            String workflowActionXml = workflowAction.asXML();
            for (Object postFunction : postFunctions) {
                Class<?> postFunctionClass = postFunction.getClass();
            }
        }
        List<JiraWorkflow> workflowList = new ArrayList<>();
        workflowList.add(workflow);
        //获取转换入口，可能存在多个工作流，每个工作流有一组转换
        TransitionManager transitionManager = new DefaultTransitionManager();
        Collection<WorkflowTransitionEntry> workflowTransitionEntryCollection = transitionManager.getTransitions(workflowList);
        for (WorkflowTransitionEntry workflowTransitionEntry : workflowTransitionEntryCollection) {
            Collection<TransitionEntry> transitionEntryCollection = workflowTransitionEntry.getTransitions();
            JiraWorkflow workflow1 = workflowTransitionEntry.getWorkflow();
            String workflow1Name = workflow1.getName();
            if (workflowName.equals(workflow1Name)) {
                for (TransitionEntry transition : transitionEntryCollection) {
                    int transitionId = transition.getTransitionId();
                    String transitionName = transition.getName();
                    Long fromStatusId = transition.getFromStatusId();
                    Long toStatusId = transition.getToStatusId();
                }
            }
        }
    }
}
