package com.eve.services;

import com.atlassian.activeobjects.external.ActiveObjects;
import com.eve.ao.ProgressAo;
import com.eve.beans.ProgressBean;
import com.eve.beans.ResultBean;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/6
 */
public class ProgressService {
    private ActiveObjects ao;

    public ProgressService(ActiveObjects ao) {
        this.ao = ao;
    }

    public List<ProgressBean> list(Long issueId, int num) {

        int offset = (num) * 10;

        List<ProgressBean> progressList = new ArrayList<>();
        try {
            ProgressAo[] progressAos = null;
            if (null == issueId || 0 == issueId) {
                progressAos = ao.find(ProgressAo.class);
            } else {
                progressAos = ao.find(ProgressAo.class, "ISSUE_ID  = " + issueId + " ORDER BY ID desc limit "+offset+",10");
            }
            for (ProgressAo progressAo : progressAos) {
                ProgressBean progressBean = new ProgressBean(
                        progressAo.getId(),
                        progressAo.getIssueId(),
                        progressAo.getStage(),
                        progressAo.getNextStep(),
                        progressAo.getRisk(),
                        progressAo.getStrategy(),
                        progressAo.getComment(),
                        progressAo.getCreateDate()
                );
                progressList.add(progressBean);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return progressList;
    }

    public int getCount(Long issueId) {
        return ao.count(ProgressAo.class, "ISSUE_ID  = " + issueId);
    }

    public List<ProgressBean> getByName(Long issueId, String progress) {
        List<ProgressBean> progressList = new ArrayList<>();
        try {
            ProgressAo[] progressAos = null;
            if (progress == null || "".equals(progress)) {
                progressAos = ao.find(ProgressAo.class);
            } else {
                progressAos = ao.find(ProgressAo.class, "ISSUE_ID  = " + issueId + " and STAGE like '%" + progress + "%'" + " order by ID desc");
            }
            for (ProgressAo progressAo : progressAos) {
                ProgressBean progressBean = new ProgressBean(
                        progressAo.getId(),
                        progressAo.getIssueId(),
                        progressAo.getStage(),
                        progressAo.getNextStep(),
                        progressAo.getRisk(),
                        progressAo.getStrategy(),
                        progressAo.getComment(),
                        progressAo.getCreateDate()
                );
                progressList.add(progressBean);
            }
            /*progressList.add(new ProgressBean(
                    progressAos[0].getId(), progressAos[0].getIssueId(),
                    progressAos[0].getStage(), progressAos[0].getNextStep(),
                    progressAos[0].getRisk(), progressAos[0].getStrategy(),
                    progressAos[0].getComment()
            ));*/
        } catch (Exception e) {
            e.printStackTrace();
        }
        return progressList;
    }

    public ResultBean getById(Long id) {
        ResultBean resultBean = new ResultBean();
        try {
            ProgressAo progressAo = ao.get(ProgressAo.class, id);
            ProgressBean progressBean = new ProgressBean(
                    progressAo.getId(), progressAo.getIssueId(),
                    progressAo.getStage(), progressAo.getNextStep(),
                    progressAo.getRisk(), progressAo.getStrategy(),
                    progressAo.getComment(),
                    progressAo.getCreateDate()
            );
            resultBean.setValue(progressBean);
        } catch (Exception e) {
            e.printStackTrace();
            resultBean.setMessage(e.getMessage());
        }
        return resultBean;
    }

    public ResultBean insert(ProgressBean progressBean) {
        ResultBean resultBean = new ResultBean();
        try {
            ProgressAo progressAo =
                    progressBean.getId() != null && progressBean.getId() != 0
                            ? ao.get(ProgressAo.class, progressBean.getId())
                            : ao.create(ProgressAo.class);
            progressAo.setIssueId(progressBean.getIssueId());
            progressAo.setStage(progressBean.getStage());
            progressAo.setNextStep(progressBean.getNextStep());
            progressAo.setRisk(progressBean.getRisk());
            progressAo.setStrategy(progressBean.getStrategy());
            progressAo.setComment(progressBean.getComment());
            if (progressBean.getId() == 0 || progressBean.getId() == null)
                progressAo.setCreateDate(new java.sql.Timestamp(System.currentTimeMillis()));
            progressAo.save();
            resultBean.setValue(progressAo.getId());
        } catch (Exception e) {
            e.printStackTrace();
            resultBean.setMessage(e.getMessage());
        }
        return resultBean;
    }

    public ResultBean delete(Long id) {
        ResultBean resultBean = new ResultBean();
        try {
            ProgressAo[] progressAos = ao.find(ProgressAo.class, "ID = ?", id);
            ao.delete(progressAos);
        } catch (Exception e) {
            e.printStackTrace();
            resultBean.setMessage(e.getMessage());
        }
        return resultBean;
    }
}
