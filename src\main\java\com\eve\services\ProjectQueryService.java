package com.eve.services;

import com.atlassian.jira.bc.issue.search.SearchService;
import com.atlassian.jira.component.ComponentAccessor;
import com.atlassian.jira.issue.Issue;
import com.atlassian.jira.issue.attachment.Attachment;
import com.atlassian.jira.issue.customfields.option.Option;
import com.atlassian.jira.issue.fields.CustomField;
import com.atlassian.jira.issue.search.SearchResults;
import com.atlassian.jira.issue.status.Status;
import com.atlassian.jira.user.ApplicationUser;

import com.atlassian.jira.web.bean.PagerFilter;
import com.atlassian.query.Query;
import com.eve.beans.ProjectQueryParamBean;
import com.eve.beans.ProjectQueryResponseBean;
import com.eve.beans.ResultBean;
import com.eve.utils.Constant;
import com.eve.utils.JiraCustomTool;
import com.eve.utils.Utils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.ObjectUtils;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/10/24
 */
public class ProjectQueryService {
    private static final Logger log = LoggerFactory.getLogger(ProjectQueryService.class);

    private JiraCustomTool jiraCustomTool;

    public ProjectQueryService(JiraCustomTool jiraCustomTool) {
        this.jiraCustomTool = jiraCustomTool;
    }

    public ResultBean queryProject(ProjectQueryParamBean projectQueryParamBean) {
        ResultBean resultBean = new ResultBean();
        StringBuilder jql = new StringBuilder("project in (");
        try {
            String userName = projectQueryParamBean.getUserName();
            String projectType = projectQueryParamBean.getProjectType();
            if (ObjectUtils.isEmpty(userName)) {
                throw new IllegalArgumentException("请传递工号查询数据");
            }
            ApplicationUser applicationUser = ComponentAccessor.getUserManager().getUserByName(userName);
            if (ObjectUtils.isEmpty(applicationUser)) {
                throw new IllegalArgumentException("该工号未查询到Jira对应用户：" + userName);
            }
            for (Long projectId : Constant.projectManageProjectIdList) {
                jql.append(projectId).append(",");
            }
            if (jql.toString().endsWith(",")) {
                jql.deleteCharAt(jql.length() - 1);
            }
            jql.append(") AND reporter = '").append(applicationUser.getUsername()).append("' ");
            if ("topic".equals(projectType)) {
                jql.append(" AND issueType = ").append(Constant.topicIssueTypeId);
            } else if ("platform".equals(projectType)) {
                jql.append(" AND issueType = ").append(Constant.platformIssueTypeId);
            }
            //获取字段
            CustomField projectCodeCustomField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(Constant.projectCodeCustomFieldId);
            CustomField projectNameCustomField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(Constant.projectNameCustomFieldId);
            CustomField projectBackGroundCustomField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(Constant.projectBackGroundCustomFieldId);
            CustomField projectPurposeCustomField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(Constant.projectPurposeCustomFieldId);
            CustomField projectTargetCustomField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(Constant.projectTargetCustomFieldId);
            CustomField technicalSummaryPassDateCustomField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(Constant.technicalSummaryPassDateCustomFieldId);


            List<ProjectQueryResponseBean> projectQueryResponseBeanList = new ArrayList<>();
            SearchService searchService = ComponentAccessor.getComponentOfType(SearchService.class);
            SearchService.ParseResult parseResult = searchService.parseQuery(applicationUser, jql.toString());
            Query query = parseResult.getQuery();
            SearchResults searchResults = searchService.search(applicationUser, query, PagerFilter.getUnlimitedFilter());
            List<Issue> storyIssueList = searchResults.getResults();
            if (storyIssueList == null) {
                storyIssueList = new ArrayList<>();
            }
            for (Issue issue : storyIssueList) {
                String projectCode = projectCodeCustomField == null ? null : (String) issue.getCustomFieldValue(projectCodeCustomField);
                String projectName = projectNameCustomField == null ? null : (String) issue.getCustomFieldValue(projectNameCustomField);
                String projectBackGround = projectBackGroundCustomField == null ? null : (String) issue.getCustomFieldValue(projectBackGroundCustomField);
                String projectPurpose = projectPurposeCustomField == null ? null : (String) issue.getCustomFieldValue(projectPurposeCustomField);
                String projectTarget = projectTargetCustomField == null ? null : (String) issue.getCustomFieldValue(projectTargetCustomField);
                Timestamp technicalSummaryPassDate = technicalSummaryPassDateCustomField == null ? null : (Timestamp) issue.getCustomFieldValue(technicalSummaryPassDateCustomField);

                Collection<Attachment> attachmentCollection = issue.getAttachments();
                List<Long> attachmentIdList = attachmentCollection.stream().map(Attachment::getId).collect(Collectors.toList());

                Status status = issue.getStatus();
                String statusName = status.getName();
                Timestamp issueCreateDate = issue.getCreated();
                projectQueryResponseBeanList.add(
                        new ProjectQueryResponseBean(
                                issue.getId(),
                                issue.getKey(),
                                projectCode == null ? "" : projectCode,
                                projectName == null ? "" : projectName,
                                projectBackGround == null ? "" : projectBackGround,
                                projectPurpose == null ? "" : projectPurpose,
                                projectTarget == null ? "" : projectTarget,
                                attachmentIdList,
                                statusName,
                                issueCreateDate == null ? "" : Utils.getDateFormat(issueCreateDate),
                                technicalSummaryPassDate == null ? "" : Utils.getDateFormat(technicalSummaryPassDate),
                                issue.getSummary()
                        )
                );
            }
            resultBean.setValue(projectQueryResponseBeanList);
        } catch (Exception e) {
            e.printStackTrace();
            resultBean.setMessage(e.getMessage());
            return resultBean;
        }
        return resultBean;
    }

    /**
     * 根据工号查询实验报告
     * @param projectQueryParamBean
     * @return
     */
    public ResultBean queryTestReport(ProjectQueryParamBean projectQueryParamBean) {
        ResultBean resultBean = new ResultBean();
        StringBuilder jql = new StringBuilder("project in (");
        try {
            String userName = projectQueryParamBean.getUserName();

            if (ObjectUtils.isEmpty(userName)) {
                throw new IllegalArgumentException("请传递工号查询数据");
            }
            ApplicationUser applicationUser = ComponentAccessor.getUserManager().getUserByName(userName);
            if (ObjectUtils.isEmpty(applicationUser)) {
                throw new IllegalArgumentException("该工号未查询到Jira对应用户：" + userName);
            }
            for (Long projectId : Constant.testReportProjectIdList) {
                jql.append(projectId).append(",");
            }
            if (jql.toString().endsWith(",")) {
                jql.deleteCharAt(jql.length() - 1);
            }
            jql.append(") AND cf[11127] = '").append(applicationUser.getUsername()).append("' ");//责任人（11127）
//            if ("topic".equals(projectType)) {
//                jql.append(" AND issueType = ").append(Constant.topicIssueTypeId);
//            } else if ("platform".equals(projectType)) {
//                jql.append(" AND issueType = ").append(Constant.platformIssueTypeId);
//            }
            //获取字段
            CustomField testReportCodeCustomField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(Constant.testReportCodeCustomFieldId);
            CustomField testReportNameCustomField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(Constant.testReportNameCustomFieldId);
            CustomField testReportPurposeCustomField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(Constant.testReportPurposeCustomFieldId);
            CustomField testReportPassDateCustomField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(Constant.testReportPassDateCustomFieldId);


            List<ProjectQueryResponseBean> projectQueryResponseBeanList = new ArrayList<>();
            SearchService searchService = ComponentAccessor.getComponentOfType(SearchService.class);
            SearchService.ParseResult parseResult = searchService.parseQuery(applicationUser, jql.toString());
            Query query = parseResult.getQuery();
            SearchResults searchResults = searchService.search(applicationUser, query, PagerFilter.getUnlimitedFilter());
            List<Issue> storyIssueList = searchResults.getResults();
            if (storyIssueList == null) {
                storyIssueList = new ArrayList<>();
            }
            for (Issue issue : storyIssueList) {
                String testReportCode = testReportCodeCustomField == null ? null : (String) issue.getCustomFieldValue(testReportCodeCustomField);
                String testReportName = testReportNameCustomField == null ? null : (String) issue.getCustomFieldValue(testReportNameCustomField);
                String testReportPurpose = testReportPurposeCustomField == null ? null : (String) issue.getCustomFieldValue(testReportPurposeCustomField);
                Timestamp testReportPassDate = testReportPassDateCustomField == null ? null : (Timestamp) issue.getCustomFieldValue(testReportPassDateCustomField);

                Collection<Attachment> attachmentCollection = issue.getAttachments();
                List<Long> attachmentIdList = attachmentCollection.stream().map(Attachment::getId).collect(Collectors.toList());

                Status status = issue.getStatus();
                String statusName = status.getName();
                Timestamp issueCreateDate = issue.getCreated();
                projectQueryResponseBeanList.add(
                        new ProjectQueryResponseBean(
                                issue.getId(),
                                issue.getKey(),
                                testReportCode == null ? "" : testReportCode,
                                testReportName == null ? "" : testReportName,
                                testReportPurpose == null ? "" : testReportPurpose,
                                attachmentIdList,
                                statusName,
                                issueCreateDate == null ? "" : Utils.getDateFormat(issueCreateDate),
                                testReportPassDate == null ? "" : Utils.getDateFormat(testReportPassDate),
                                issue.getSummary()
                        )
                );
            }
            resultBean.setValue(projectQueryResponseBeanList);
        } catch (Exception e) {
            e.printStackTrace();
            resultBean.setMessage(e.getMessage());
        }
        return resultBean;
    }

    public ResultBean queryProductManage(ProjectQueryParamBean projectQueryParamBean) {
        ResultBean resultBean = new ResultBean();
        StringBuilder jql = new StringBuilder("project in (");
        try {
            String userName = projectQueryParamBean.getUserName();
            if (ObjectUtils.isEmpty(userName)) {
                throw new IllegalArgumentException("请传递工号查询数据");
            }
            ApplicationUser applicationUser = ComponentAccessor.getUserManager().getUserByName(userName);
            if (ObjectUtils.isEmpty(applicationUser)) {
                throw new IllegalArgumentException("该工号未查询到Jira对应用户：" + userName);
            }
            for (Long projectId : Constant.productManageProjectIdList) {
                jql.append(projectId).append(",");
            }
            if (jql.toString().endsWith(",")) {
                jql.deleteCharAt(jql.length() - 1);
            }
            jql.append(") AND reporter = '").append(applicationUser.getUsername()).append("' ");//责任人（11127）
            jql.append(" AND issueType = ").append(Constant.productIssueTypeId);
            //获取字段
            CustomField productNameCustomField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(Constant.productNameCustomFieldId);
            CustomField projectNameCustomField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(Constant.projectNameCustomFieldId);
            CustomField productStageCustomField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(Constant.productStageCustomFieldId);
            CustomField productCateCustomField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(Constant.productCateCustomFieldId);
            CustomField productModelCustomField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(Constant.productModelCustomFieldId);

            List<ProjectQueryResponseBean> projectQueryResponseBeanList = new ArrayList<>();
            SearchService searchService = ComponentAccessor.getComponentOfType(SearchService.class);
            SearchService.ParseResult parseResult = searchService.parseQuery(applicationUser, jql.toString());
            Query query = parseResult.getQuery();
            SearchResults searchResults = searchService.search(applicationUser, query, PagerFilter.getUnlimitedFilter());
            List<Issue> storyIssueList = searchResults.getResults();
            if (storyIssueList == null) {
                storyIssueList = new ArrayList<>();
            }
            for (Issue issue : storyIssueList) {
                String productName = productNameCustomField == null ? null : (String) issue.getCustomFieldValue(productNameCustomField);
//                String projectName = projectNameCustomField == null ? null : (String) issue.getCustomFieldValue(projectNameCustomField);
                Option productStageOption = productStageCustomField == null ? null : (Option) issue.getCustomFieldValue(productStageCustomField);
                Map<Object, Option> productCateOptionMap = productCateCustomField == null ? null : (Map<Object, Option>) issue.getCustomFieldValue(productCateCustomField);
                String productModel = productModelCustomField == null ? null : (String) issue.getCustomFieldValue(productModelCustomField);

                Collection<Attachment> attachmentCollection = issue.getAttachments();
                List<Long> attachmentIdList = ObjectUtils.isEmpty(attachmentCollection) ? new ArrayList<>() : attachmentCollection.stream().map(Attachment::getId).collect(Collectors.toList());

                String statusName = issue.getStatus().getName();
                Timestamp issueCreateDate = issue.getCreated();
                projectQueryResponseBeanList.add(
                        new ProjectQueryResponseBean.Builder()
                                .issueId(issue.getId())
                                .issueKey(issue.getKey())
                                .attachments(attachmentIdList)
                                .status(statusName)
                                .startDate(issueCreateDate == null ? "" : Utils.getDateFormat(issueCreateDate))
                                .endDate("")
                                .summary(issue.getSummary())
                                .code(productName == null ? "" : productName)
                                .name(issue.getSummary())
                                .stage(productStageOption == null ? "" : productStageOption.getValue())
                                .cate(ObjectUtils.isEmpty(productCateOptionMap) ? "" : productCateOptionMap.size() == 1 ? productCateOptionMap.get(null).getValue() : productCateOptionMap.get(null).getValue() + "-" + productCateOptionMap.get("1").getValue())
                                .model(productModel == null ? "" : productModel)
                                .build()
                );
            }
            resultBean.setValue(projectQueryResponseBeanList);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("产品信息查询异常：" + Utils.errInfo(e));
            resultBean.setMessage(e.getMessage());
        }
        return resultBean;
    }

    public ResultBean queryTravelReport(ProjectQueryParamBean projectQueryParamBean) {
        ResultBean resultBean = new ResultBean();
        StringBuilder jql = new StringBuilder("project in (");
        try {
            String userName = projectQueryParamBean.getUserName();
            if (ObjectUtils.isEmpty(userName)) {
                throw new IllegalArgumentException("请传递工号查询数据");
            }
            ApplicationUser applicationUser = ComponentAccessor.getUserManager().getUserByName(userName);
            if (ObjectUtils.isEmpty(applicationUser)) {
                throw new IllegalArgumentException("该工号未查询到Jira对应用户：" + userName);
            }
            for (Long projectId : Constant.travelReportProjectIdList) {
                jql.append(projectId).append(",");
            }
            if (jql.toString().endsWith(",")) {
                jql.deleteCharAt(jql.length() - 1);
            }
            jql.append(") AND cf[10925] = '").append(applicationUser.getUsername()).append("' ");//报告提交人（10925）
//            jql.append(" AND issueType = ").append(Constant.productIssueTypeId);
            //获取字段
            CustomField travelReasonCustomField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(Constant.travelReasonCustomFieldId );
            CustomField travelAddrCustomField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(Constant.travelAddrCustomFieldId);
            CustomField travelDaysCustomField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(Constant.travelDaysCustomFieldId);
            CustomField leaderScoreCustomField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(Constant.leaderScoreCustomFieldId);
            CustomField manageScoreCustomField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(Constant.manageScoreCustomFieldId);
            CustomField travelReportSubmitDateCustomField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(Constant.travelReportSubmitDateCustomFieldId);
            CustomField leaderEvaluationCustomField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(Constant.leaderEvaluationCustomFieldId);

            List<ProjectQueryResponseBean> projectQueryResponseBeanList = new ArrayList<>();
            SearchService searchService = ComponentAccessor.getComponentOfType(SearchService.class);
            SearchService.ParseResult parseResult = searchService.parseQuery(applicationUser, jql.toString());
            Query query = parseResult.getQuery();
            SearchResults searchResults = searchService.search(applicationUser, query, PagerFilter.getUnlimitedFilter());
            List<Issue> storyIssueList = searchResults.getResults();
            if (storyIssueList == null) {
                storyIssueList = new ArrayList<>();
            }
            for (Issue issue : storyIssueList) {
                String travelReason = travelReasonCustomField == null ? null : (String) issue.getCustomFieldValue(travelReasonCustomField);
                String travelAddr = travelAddrCustomField == null ? null : (String) issue.getCustomFieldValue(travelAddrCustomField);
                Double travelDays = travelDaysCustomField == null ? null : (Double) issue.getCustomFieldValue(travelDaysCustomField);
                Double leaderScore = leaderScoreCustomField == null ? null : (Double) issue.getCustomFieldValue(leaderScoreCustomField);
                String leaderEvaluation = leaderEvaluationCustomField == null ? null : (String) issue.getCustomFieldValue(leaderEvaluationCustomField);
                Double manageScore = manageScoreCustomField == null ? null : (Double) issue.getCustomFieldValue(manageScoreCustomField);
                Timestamp travelReportSubmitDate = travelReportSubmitDateCustomField == null ? null : (Timestamp) issue.getCustomFieldValue(travelReportSubmitDateCustomField);

                Collection<Attachment> attachmentCollection = issue.getAttachments();
                List<Long> attachmentIdList = ObjectUtils.isEmpty(attachmentCollection) ? new ArrayList<>() : attachmentCollection.stream().map(Attachment::getId).collect(Collectors.toList());

                String statusName = issue.getStatus().getName();
                Timestamp issueCreateDate = issue.getCreated();
                projectQueryResponseBeanList.add(
                        new ProjectQueryResponseBean.Builder()
                                .issueId(issue.getId())
                                .issueKey(issue.getKey())
                                .attachments(attachmentIdList)
                                .status(statusName)
                                .startDate(issueCreateDate == null ? "" : Utils.getDateFormat(issueCreateDate))
                                .endDate(travelReportSubmitDate == null ? "" : Utils.getDateFormat(travelReportSubmitDate))
                                .summary(issue.getSummary())
                                .reason(travelReason == null ? "" : travelReason)
                                .addr(travelAddr == null ? "" : travelAddr)
                                .days(travelDays == null ? "" : travelDays.intValue() + "")
                                .leaderScore(leaderScore == null ? "" : leaderScore.intValue() + "")
                                .leaderEvaluation(leaderEvaluation == null ? "" : leaderEvaluation)
                                .manageScore(manageScore == null ? "" : manageScore.intValue() + "")
                                .build()
                );
            }
            resultBean.setValue(projectQueryResponseBeanList);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("出差报告信息查询异常：" + Utils.errInfo(e));
            resultBean.setMessage(e.getMessage());
        }
        return resultBean;
    }

    public ResultBean queryAchievementManage(ProjectQueryParamBean projectQueryParamBean) {
        ResultBean resultBean = new ResultBean();
        StringBuilder jql = new StringBuilder("project in (");
        try {
            String userName = projectQueryParamBean.getUserName();
            if (ObjectUtils.isEmpty(userName)) {
                throw new IllegalArgumentException("请传递工号查询数据");
            }
            ApplicationUser applicationUser = ComponentAccessor.getUserManager().getUserByName(userName);
            if (ObjectUtils.isEmpty(applicationUser)) {
                throw new IllegalArgumentException("该工号未查询到Jira对应用户：" + userName);
            }
            for (Long projectId : Constant.achievementManageProjectIdList) {
                jql.append(projectId).append(",");
            }
            if (jql.toString().endsWith(",")) {
                jql.deleteCharAt(jql.length() - 1);
            }
            jql.append(") AND issueType in (");
            for (Long projectId : Constant.achievementManageIssueTypeIdList) {
                jql.append(projectId).append(",");
            }
            if (jql.toString().endsWith(",")) {
                jql.deleteCharAt(jql.length() - 1);
            }
            jql.append(") AND (cf[11698] = '").append(applicationUser.getUsername()).append("' OR reporter = '").append(applicationUser.getUsername()).append("') AND status = 11059 ");//主要完成人员名单（11698） 成果认定通过（11059）
            log.error("成果信息查询JQL：{}", jql);
//            jql.append(" AND issueType = ").append(Constant.productIssueTypeId);
            //获取字段
            CustomField achievementCateCustomField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(Constant.achievementCateCustomFieldId);
            CustomField mainCompletePersonCustomField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(Constant.mainCompletePersonCustomFieldId);
            CustomField achievementNameCustomField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(Constant.achievementNameCustomFieldId);
            CustomField achievementModalityCustomField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(Constant.achievementModalityCustomFieldId);
            CustomField achievementContentCustomFieldId = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(Constant.achievementContentCustomFieldId);
            CustomField directorScoreCustomField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(Constant.directorScoreCustomFieldId);
            CustomField directorEvaluationCustomField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(Constant.directorEvaluationCustomFieldId);
            CustomField averageScoreCustomField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(Constant.averageScoreCustomFieldId);
            CustomField achievementPassDateCustomField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(Constant.achievementPassDateCustomFieldId);

            List<ProjectQueryResponseBean> projectQueryResponseBeanList = new ArrayList<>();
            SearchService searchService = ComponentAccessor.getComponentOfType(SearchService.class);
            SearchService.ParseResult parseResult = searchService.parseQuery(applicationUser, jql.toString());
            Query query = parseResult.getQuery();
            SearchResults searchResults = searchService.search(applicationUser, query, PagerFilter.getUnlimitedFilter());
            List<Issue> storyIssueList = searchResults.getResults();
            if (storyIssueList == null) {
                storyIssueList = new ArrayList<>();
            }
            for (Issue issue : storyIssueList) {
                Map<String, Option> achievementCateMap = achievementCateCustomField == null ? null : (Map<String, Option>) issue.getCustomFieldValue(achievementCateCustomField);
                List<ApplicationUser> mainCompletePersonList = mainCompletePersonCustomField == null ? new ArrayList<>() : (List<ApplicationUser>) issue.getCustomFieldValue(mainCompletePersonCustomField);
                List<String> firstAndLastNameList = jiraCustomTool.getFirstAndLastName(mainCompletePersonList);
                String achievementName = achievementNameCustomField == null ? null : (String) issue.getCustomFieldValue(achievementNameCustomField);
                List<Option> achievementModalityOptionList = achievementModalityCustomField == null ? null : (List<Option>) issue.getCustomFieldValue(achievementModalityCustomField);
                List<String> achievementModalityValueList = ObjectUtils.isEmpty(achievementModalityOptionList) ? new ArrayList<>() : achievementModalityOptionList.stream().map(Option::getValue).collect(Collectors.toList());
                String achievementContent = achievementContentCustomFieldId == null ? null : (String) issue.getCustomFieldValue(achievementContentCustomFieldId);
                Double directorScore = directorScoreCustomField == null ? null : (Double) issue.getCustomFieldValue(directorScoreCustomField);
                String directorEvaluation = directorEvaluationCustomField == null ? null : (String) issue.getCustomFieldValue(directorEvaluationCustomField);
                Double averageScore = averageScoreCustomField == null ? null : (Double) issue.getCustomFieldValue(averageScoreCustomField);
                Timestamp achievementPassDate = achievementPassDateCustomField == null ? null : (Timestamp) issue.getCustomFieldValue(achievementPassDateCustomField);

                Collection<Attachment> attachmentCollection = issue.getAttachments();
                List<Long> attachmentIdList = ObjectUtils.isEmpty(attachmentCollection) ? new ArrayList<>() : attachmentCollection.stream().map(Attachment::getId).collect(Collectors.toList());
                attachmentIdList = new ArrayList<>();
                String statusName = issue.getStatus().getName();
                Timestamp issueCreateDate = issue.getCreated();
                projectQueryResponseBeanList.add(
                        new ProjectQueryResponseBean.Builder()
                                .issueId(issue.getId())
                                .issueKey(issue.getKey())
                                .attachments(attachmentIdList)
                                .status(statusName)
                                .startDate(issueCreateDate == null ? "" : Utils.getDateFormat(issueCreateDate))
                                .endDate(achievementPassDate == null ? "" : Utils.getDateFormat(achievementPassDate))
                                .summary(issue.getSummary())
                                .cate(ObjectUtils.isEmpty(achievementCateMap) ? "" : achievementCateMap.size() == 1 ? achievementCateMap.get(null).getValue() : achievementCateMap.get(null).getValue() + "-" + achievementCateMap.get("1").getValue())
                                .name(achievementName == null ? "" : achievementName)
                                .modality(ObjectUtils.isEmpty(achievementModalityValueList) ? new ArrayList<>() : achievementModalityValueList)
                                .content(achievementContent == null ? "" : achievementContent)
                                .directorScore(directorScore == null ? "" : directorScore.intValue() + "")
                                .directorEvaluation(directorEvaluation == null ? "" : directorEvaluation)
                                .averageScore(averageScore == null ? "" : averageScore.intValue() + "")
                                .mainCompletePerson(firstAndLastNameList)
                                .build()
                );

            }
            resultBean.setValue(projectQueryResponseBeanList);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("成果管理信息查询异常：" + Utils.errInfo(e));
            resultBean.setMessage(e.getMessage());
        }
        return resultBean;
    }
}
