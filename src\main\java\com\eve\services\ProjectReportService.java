package com.eve.services;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.atlassian.jira.bc.issue.IssueService;
import com.atlassian.jira.component.ComponentAccessor;
import com.atlassian.jira.datetime.DateTimeFormatter;
import com.atlassian.jira.datetime.DateTimeStyle;
import com.atlassian.jira.event.type.EventDispatchOption;
import com.atlassian.jira.exception.CreateException;
import com.atlassian.jira.issue.*;
import com.atlassian.jira.issue.context.JiraContextNode;
import com.atlassian.jira.issue.context.ProjectContext;
import com.atlassian.jira.issue.customfields.option.Option;
import com.atlassian.jira.issue.customfields.option.Options;
import com.atlassian.jira.issue.fields.CustomField;
import com.atlassian.jira.issue.issuetype.IssueType;
import com.atlassian.jira.issue.resolution.Resolution;
import com.atlassian.jira.issue.status.Status;
import com.atlassian.jira.project.Project;
import com.atlassian.jira.security.JiraAuthenticationContext;
import com.atlassian.jira.user.ApplicationUser;
import com.atlassian.jira.util.thread.JiraThreadLocalUtil;
import com.eve.beans.*;
import com.eve.utils.Constant;
import com.eve.utils.JiraCustomTool;
import com.eve.utils.JwtTokenUtil;
import com.eve.utils.Utils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/11/16
 */
public class ProjectReportService {
    private static final Logger log = LoggerFactory.getLogger(ProjectReportService.class);
    private static final org.apache.log4j.Logger log4jLogger = org.apache.log4j.Logger.getLogger(ProjectReportService.class);
    private JiraCustomTool jiraCustomTool;
    private CustomFieldManager customFieldManager;
    private IssueService issueService;
    @Resource
    private CustomToolService customToolService;

    public ProjectReportService(JiraCustomTool jiraCustomTool, CustomFieldManager customFieldManager, IssueService issueService) {
        this.jiraCustomTool = jiraCustomTool;
        this.customFieldManager = customFieldManager;
        this.issueService = issueService;
    }

    /**
     * 项目分类获取，及对应分类项目数量
     */
    @Deprecated
    public ResultBean getProjectCates() {
        ResultBean resultBean = new ResultBean();
        try {
            List<JiraOptionBean> productCateBeans = new ArrayList<>();
            CustomField projectCateCustomField = Utils.getCustomFieldByID(Constant.projectCateCustomFieldId);
            JiraContextNode jiraContextNode = new ProjectContext(Constant.projectManageProjectId, ComponentAccessor.getProjectManager());
            Options options = projectCateCustomField.getOptions("", jiraContextNode);
            ApplicationUser dladminUser = ComponentAccessor.getUserManager().getUserByName(Constant.dladminUserName);
            for (Option option : options) {
                //禁用的选项，不获取
                if (Boolean.TRUE.equals(option.getDisabled())) {
                    continue;
                }
                StringBuilder jqlBuilder = new StringBuilder("project in (");

                //项目管理的三个项目
                for (Long projectId : Constant.projectManageProjectIdList) {
                    jqlBuilder.append(projectId).append(",");
                }
                if (jqlBuilder.toString().endsWith(",")) {
                    jqlBuilder.deleteCharAt(jqlBuilder.length() - 1);
                    jqlBuilder.append(") ");
                }
                jqlBuilder.append(" AND issueType in (").append(Constant.platformIssueTypeId).append(", ").append(Constant.topicIssueTypeId).append(") ");

                String jql = jqlBuilder + " AND cf[" + Constant.projectCateCustomFieldId + "] in cascadeOption(" + option.getOptionId() + ")";
                int resultsTotal = jiraCustomTool.getTotalNumByJql(dladminUser,jql);

                productCateBeans.add(
                        new JiraOptionBean(option.getOptionId(), 1L, option.getValue(), resultsTotal + "", option.getSequence())
                );
                List<Option> childsOptions = option.getChildOptions();
                for (Option child : childsOptions) {
                    if (Boolean.TRUE.equals(child.getDisabled())) {
                        continue;
                    }
                    jql = jqlBuilder + " AND cf[" + Constant.projectCateCustomFieldId + "] in cascadeOption(" + option.getOptionId() + ", " + child.getOptionId() + ")";
                    resultsTotal = jiraCustomTool.getTotalNumByJql(dladminUser,jql);
                    productCateBeans.add(
                            new JiraOptionBean(child.getOptionId(), child.getParentOption().getOptionId(), child.getValue(), resultsTotal + "", child.getSequence())
                    );
                }
            }
            resultBean.setValue(productCateBeans);
        } catch (Exception e) {
            e.printStackTrace();
            resultBean.setMessage(e.getMessage());
        }
        return resultBean;
    }
    /**
     * 获取级联选项
     */
    public ResultBean getJiraCascadingSelect(String fieldName) {
        ResultBean resultBean = new ResultBean();
        try {
            List<JiraOptionBean> productCateBeans = new ArrayList<>();
            Map<String, Long> cascadingSelectCustomFiledMap = Utils.getCascadingSelectCustomFiledMap();
            Long customFieldId = cascadingSelectCustomFiledMap.getOrDefault(fieldName, 0L);
            CustomField projectCateCustomField = Utils.getCustomFieldByID(customFieldId);
            if (projectCateCustomField == null) {
                throw new IllegalArgumentException("该字段未适配");
            }else if (!"com.atlassian.jira.plugin.system.customfieldtypes:cascadingselect".equals(projectCateCustomField.getCustomFieldType().getKey())) {
                throw new IllegalArgumentException("字段不为级联选项");
            }
            JiraContextNode jiraContextNode = new ProjectContext(Constant.projectManageProjectId, ComponentAccessor.getProjectManager());
            Options options = projectCateCustomField.getOptions("", jiraContextNode);
            List<Project> projectList = ComponentAccessor.getProjectManager().getProjectObjects();
            for (Project project : projectList) {
                if (options != null) {
                    break;
                }
                jiraContextNode = new ProjectContext(project.getId(), ComponentAccessor.getProjectManager());
                options = projectCateCustomField.getOptions("", jiraContextNode);
            }
            List<Long> subDepartCustomFieldOptionIdList = Arrays.asList(
                    18863L,
                    18846L,
                    22492L,
                    22487L,
                    18711L,
                    22101L,
                    22269L,
                    22105L,
                    18854L,
                    22493L,
                    18876L,
                    22494L,
                    18859L,
                    18829L,
                    22263L,
                    22495L,
                    22496L,
                    22497L


//                    22101L,
//                    18863L,
//                    18846L,
//                    22105L,
//                    18876L,
//                    18854L,
//                    18829L,
//                    18859L,
//                    18711L,
//                    22269L,
//                    22263L
            );//办公室-18868L
            for (Option option : options) {
                boolean isCancel = false;
                isCancel = customFieldId.equals(Constant.subDepartCustID) && !subDepartCustomFieldOptionIdList.contains(option.getOptionId());
                isCancel = isCancel || option.getDisabled();
                //禁用的选项，不获取
                if (isCancel) {
                    continue;
                }
                productCateBeans.add(
                        new JiraOptionBean(option.getOptionId(), 1L, option.getValue(), "",option.getSequence())
                );
                List<Option> childsOptions = option.getChildOptions();
                for (Option child : childsOptions) {
                    if (Boolean.TRUE.equals(child.getDisabled())) {
                        continue;
                    }
                    productCateBeans.add(
                            new JiraOptionBean(child.getOptionId(), child.getParentOption().getOptionId(), child.getValue(), "",child.getSequence())
                    );
                }
            }
            resultBean.setValue(productCateBeans);
        } catch (Exception e) {
            Utils.errInfo(e);
            resultBean.setMessage(e.getMessage());
        }
        return resultBean;
    }

    /**
     * 根据分类获取项目，传入分类为空时获取全部项目
     * @param token
     * @param parentId
     * @param cateId
     * @param startDate
     * @param endDate
     * @param area
     * @param isOnline
     * @return
     */
    public ResultBean getProjectByCate(String token, Long parentId, Long cateId, String startDate, String endDate, String area, Integer isOnline) {
        ResultBean resultBean = new ResultBean();
        try {
            ApplicationUser applicationUser = ComponentAccessor.getUserManager().getUserByName(Constant.dladminUserName);
//            ApplicationUser applicationUser = JwtTokenUtil.getApplicationUserByToken(token);
            String projects = "(XMGL, LFPXMGL)";
            if (area.contains("HZ")) {
                projects = "(XMGL)";
            } else if (area.contains("JM")) {
                projects = "(LFPXMGL)";
            }
            if ((area.contains("HZ") && area.contains("JM")) || area.contains("ALL")) {
                projects = "(XMGL, LFPXMGL)";
            }
            StringBuilder jqlBuilder = new StringBuilder("project in ");
            jqlBuilder.append(projects);
            jqlBuilder.append(" AND issueType in standardIssueTypes() ");
            String jql = jqlBuilder.toString();
            if (!ObjectUtils.isEmpty(parentId) && !ObjectUtils.isEmpty(cateId)) {
                if (parentId.equals(1L)) {
                    jql = jqlBuilder + " AND cf[" + Constant.projectCateCustomFieldId + "] in cascadeOption(" + cateId + ")";
                } else {
                    jql = jqlBuilder + " AND cf[" + Constant.projectCateCustomFieldId + "] in cascadeOption(" + parentId + ", " + cateId + ")";
                }
            }

            if (!ObjectUtils.isEmpty(startDate)) {
                jql += "AND cf[" + Constant.reviewDateCustomFieldId + "] >= '" + startDate + "'";
                if (!ObjectUtils.isEmpty(endDate)) {
                    jql += " AND cf[" + Constant.reviewDateCustomFieldId + "] <= '" + endDate + "'";
                }
            }
            log.error("根据项目分类获取接口JQL构造完成:" + jql);
            List<Issue> issueList = jiraCustomTool.getIssueListByJql(applicationUser, jql);
            List<ProjectReportBean> projectReportBeanList = getProjectReportBeanList(applicationUser, issueList,isOnline);

            resultBean.setValue(ObjectUtils.isEmpty(issueList) ? new ArrayList<>() : projectReportBeanList);
        } catch (Exception e) {
            log.error(Utils.errInfo(e));
            resultBean.setMessage(e.getMessage());
        }
        return resultBean;
    }

    @NotNull
    private List<ProjectReportBean> getReportBeanListByNullRetry(ApplicationUser applicationUser, List<Issue> issueList) {
        List<ProjectReportBean> projectReportBeanList = getProjectReportBeanList(applicationUser, issueList, null);
//        int countDown = 5;
//        while (projectReportBeanList.stream().anyMatch(Objects::isNull) && countDown > 0) {
//            log.error("捕获到空数据，重新获取，剩余超时次数: " + countDown--);
//            projectReportBeanList = getProjectReportBeanList(applicationUser, issueList);
//        }
//        long count = projectReportBeanList.stream().filter(Objects::isNull).count();
//        if (count > 0) {
//            log.error("存在{}条空数据，进行过滤操作", count);
//            projectReportBeanList = projectReportBeanList.stream().filter(Objects::nonNull).collect(Collectors.toList());
//        }else{
//            log.error("无空数据，根据项目分类获取接口查询结果大小:{}", projectReportBeanList.size());
//        }
        return projectReportBeanList;
    }

    /**
     * 延期项目
     * @param token
     * @param isOnline
     * @return
     */
    public ResultBean getDelayProjects(String token, int isOnline) {
        ResultBean resultBean = new ResultBean();
        try {
//            ApplicationUser applicationUser = JwtTokenUtil.getApplicationUserByToken(token);
            ApplicationUser applicationUser = ComponentAccessor.getUserManager().getUserByName(Constant.dladminUserName);
//            String jql = "project in (XMGL, LFPXMGL, WSXEDC) AND issuetype in standardIssueTypes() AND resolution = Unresolved";
            String jql = "project in (XMGL, LFPXMGL) AND issuetype in standardIssueTypes() AND resolution = Unresolved";
            List<Issue> issueList = jiraCustomTool.getIssueListByJql(applicationUser, jql);
            resultBean.setValue(ObjectUtils.isEmpty(issueList) ? new ArrayList<>() : getReportBeanListByNullRetry(applicationUser, issueList));
        } catch (Exception e) {
            log.error(Utils.errInfo(e));
            resultBean.setMessage(e.getMessage());
        }
        return resultBean;
    }

    public ResultBean getProjectReviewList(String token, int isOnline, UpdateCustomFiledBean updateCustomFiledBean) {
        ResultBean resultBean = new ResultBean();
        try {
            ApplicationUser applicationUser = JwtTokenUtil.getApplicationUserByToken(token);
            String userName = updateCustomFiledBean.getUserName();
            Map<String, String> map = updateCustomFiledBean.getMap();
            StringBuilder jql = new StringBuilder(isOnline == 1 ? "project in (XMGL, LFPXMGL)" : "project in (WSXEDC,XMGLCSJM)");
            for (Map.Entry<String, String> entry : map.entrySet()) {
                String entryKey = entry.getKey();
                String entryValue = entry.getValue();
                Long customFiledId = Utils.getReviewListCustomFiledMap().getOrDefault(entryKey, 0L);
                CustomField customFiled = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(customFiledId);
                if (customFiled == null || entryValue == null || "".equals(entryValue)) {
                    continue;
                }
                String customFieldTypeKey = customFiled.getCustomFieldType().getKey();
                switch (customFieldTypeKey) {
                    case "com.atlassian.jira.plugin.system.customfieldtypes:textfield":
                    case "com.atlassian.jira.plugin.system.customfieldtypes:textarea":
                        jql.append(" AND cf[").append(customFiledId).append("] = ").append(entryValue);
                        break;
//                    case "com.atlassian.jira.plugin.system.customfieldtypes:select":
                    default:
                        break;
                }
            }

            List<Issue> issueList = jiraCustomTool.getIssueListByJql(applicationUser, jql.toString());
            resultBean.setValue(ObjectUtils.isEmpty(issueList) ? new ArrayList<>() : getProjectReviewListBeanList(applicationUser, issueList));
        } catch (Exception e) {
            log.error(Utils.errInfo(e));
            resultBean.setMessage(e.getMessage());
        }
        return resultBean;
    }

    public ResultBean getProjectReviewList(String token, Long reviewListType, String reviewMonth, String startCreateDate, String endCreateDate, String area, int isOnline) {
//        log.error("评审清单接口参数:1-" + token + "2-" + reviewListType);
        ResultBean resultBean = new ResultBean();
        try {
            ApplicationUser applicationUser = JwtTokenUtil.getApplicationUserByToken(token);
            if (ObjectUtils.isEmpty(applicationUser)) {
                throw new IllegalArgumentException("该用户在Jira系统中不存在");
            }
            Map<Long, Long> projectReviewListTypeMap = Utils.getProjectReviewListTypeMap();
            Long statusId = projectReviewListTypeMap.getOrDefault(reviewListType, 0L);

            String projects = "(WSXEDC,XMGLCSJM)";
            if (isOnline == 1) {
                if (area.contains("HZ")) {
                    projects = "(XMGL)";
                } else if (area.contains("JM")) {
                    projects = "(LFPXMGL)";
                }
                if ((area.contains("HZ") && area.contains("JM")) || area.contains("ALL")) {
                    projects = "(XMGL, LFPXMGL)";
                }
//            } else {
//                projects = "(WSXEDC,XMGLCSJM)";
            }
            String jql = "project in " + projects + " AND issuetype in standardIssueTypes() AND status = " + statusId;
            if (reviewListType == 1L) {//所级项目管理员确认
                jql = "project in " + projects + " AND issuetype in standardIssueTypes() AND status = " + statusId + " AND cf[" + Constant.projectManagerCustomFieldId + "] = '" + applicationUser.getUsername() + "'";
            } else if (reviewListType == 2L) {//所长
//                jql = "project in " + projects + " AND issuetype in standardIssueTypes() AND status = " + statusId + " AND cf[" + Constant.headOfTheInstituteCustomFieldId + "] = '" + applicationUser.getUsername() + "'";
                jql = "project in " + projects + " AND issuetype in standardIssueTypes() AND cf[" + Constant.headOfTheInstituteCustomFieldId + "] = '" + applicationUser.getUsername() + "'";
            } else if (reviewListType == 3L) {//项目管理员
                jql = "project in " + projects + " AND issuetype in standardIssueTypes() AND status = " + statusId + " AND (cf[" + Constant.projectManagerCustomFieldId + "] = '" + applicationUser.getUsername() + "' OR assignee = '" + applicationUser.getUsername() + "')";
            } else if (reviewListType == 4L) {//院长
//                jql = "project in " + projects + " AND issuetype in standardIssueTypes() AND status = " + statusId + " AND (cf[" + Constant.presidentCustomFieldId + "] = '" + applicationUser.getUsername() + "' OR assignee = '" + applicationUser.getUsername() + "')";
                jql = "project in " + projects + " AND issuetype in standardIssueTypes() AND status = " + statusId;
            } else if (reviewListType == 5L) {
                jql = "project in " + projects + " AND issuetype in standardIssueTypes() AND reporter = '" + applicationUser.getUsername() + "'";
            }
            if (!ObjectUtils.isEmpty(reviewMonth)) {
                jql += " AND cf[" + Constant.reviewMonthCustomFieldId + "] ~ '" + reviewMonth + "'";
            }
            if (!ObjectUtils.isEmpty(startCreateDate)) {
                jql += " AND created  >= '" + startCreateDate + "'";
            }
            if (!ObjectUtils.isEmpty(endCreateDate)) {
                jql += " AND created  <= '" + endCreateDate + "'";
            }
            log.error("评审清单接口JQL：" + jql);

            List<Issue> issueList = jiraCustomTool.getIssueListByJql(applicationUser, jql);
            List<ProjectReviewListBean> projectReviewListBeanList = getProjectReviewListBeanList(applicationUser, issueList);

            if (reviewListType.equals(5L)) {//按时间排序，
                projectReviewListBeanList.sort(Comparator.comparing(ProjectReviewListBean::getCreateDate, Comparator.reverseOrder()));
                Integer i = 0;
                while (i < projectReviewListBeanList.size()) {
                    projectReviewListBeanList.get(i).setNo(i.longValue() + 1);
                    i++;
                }
            }
            resultBean.setValue(projectReviewListBeanList);
            log.error("评审清单接口结束" + projectReviewListBeanList.size());

        } catch (Exception e) {
            log.error(Utils.errInfo(e));
            resultBean.setMessage(e.getMessage());
        }
        return resultBean;
    }

    public List<ProjectReviewListBean> getProjectReviewListBeanList(ApplicationUser applicationUser, List<Issue> issueList) {
        long no = 0L;
        long optionNum = 0L;
        List<ProjectReviewListBean> projectReviewListBeanList = new ArrayList<>();
        //获取自定义字段
        CustomField projectCateCustomField = customFieldManager.getCustomFieldObject(Constant.projectCateCustomFieldId);
        CustomField projectNameCustomField = customFieldManager.getCustomFieldObject(Constant.projectNameCustomFieldId);
        CustomField projectPurposeCustomField = customFieldManager.getCustomFieldObject(Constant.projectPurposeCustomFieldId);
        CustomField projectTargetCustomField = customFieldManager.getCustomFieldObject(Constant.projectTargetCustomFieldId);
        CustomField projectBackGroundCustomField = customFieldManager.getCustomFieldObject(Constant.projectBackGroundCustomFieldId);
        CustomField researchContentCustomField = customFieldManager.getCustomFieldObject(Constant.researchContentCustomFieldId);
        CustomField projectLevelCustomField = customFieldManager.getCustomFieldObject(Constant.projectLevelCustomFieldId);
        CustomField departmentCustomField = customFieldManager.getCustomFieldObject(Constant.subDepartCustID);
        CustomField jmDepartmentCustomField = customFieldManager.getCustomFieldObject(Constant.jmDepartmentCustID);
        CustomField affiliatedPlatformCustomField = customFieldManager.getCustomFieldObject(Constant.affiliatedPlatformCustomFieldId);//归属平台
        CustomField platformCateCustomField = customFieldManager.getCustomFieldObject(Constant.platformCateCustomFieldId);//平台分类

        CustomField directLeaderCustomField = customFieldManager.getCustomFieldObject(Constant.directLeaderCustomFieldId);
        CustomField inspectorGeneralCustomField = customFieldManager.getCustomFieldObject(Constant.inspectorGeneralCustomFieldId);
        CustomField deputyHeadOfTheInstituteCustomField = customFieldManager.getCustomFieldObject(Constant.deputyHeadOfTheInstituteCustomFieldId);
        CustomField headOfTheInstituteCustomField = customFieldManager.getCustomFieldObject(Constant.headOfTheInstituteCustomFieldId);
        CustomField presidentCustomField = customFieldManager.getCustomFieldObject(Constant.presidentCustomFieldId);
        CustomField projectManagerCustomField = customFieldManager.getCustomFieldObject(Constant.projectManagerCustomFieldId);
        CustomField platformLeaderCustomFieldId = customFieldManager.getCustomFieldObject(Constant.platformLeaderCustomFieldId);//平台负责人

        CustomField projectLeaderCustomField = customFieldManager.getCustomFieldObject(Constant.topicLeaderCustomFieldId);//平台负责人

        CustomField reviewResultCustomField = customFieldManager.getCustomFieldObject(Constant.reviewResultCustomFieldId);
        CustomField reviewResult1CustomField = customFieldManager.getCustomFieldObject(Constant.reviewResult1CustomFieldId);
        CustomField reviewOpinion1CustomField = customFieldManager.getCustomFieldObject(Constant.reviewOpinion1CustomFieldId);
        CustomField reviewResult2CustomField = customFieldManager.getCustomFieldObject(Constant.reviewResult2CustomFieldId);
        CustomField reviewOpinion2CustomField = customFieldManager.getCustomFieldObject(Constant.reviewOpinion2CustomFieldId);
        CustomField secondReviewCustomField = customFieldManager.getCustomFieldObject(Constant.secondReviewCustomFieldId);
        CustomField projectCompleteCustomField = customFieldManager.getCustomFieldObject(Constant.projectCompleteCustomFieldId);
        CustomField topicTypeCustomField = customFieldManager.getCustomFieldObject(Constant.topicTypeCustomFieldId);
        //获取归属平台、项目分类全部选项，按先归属平台、后分类排序
        JiraContextNode jiraContextNode = new ProjectContext(Constant.projectManageProjectId, ComponentAccessor.getProjectManager());
        Options affiliatedPlatformOptions = affiliatedPlatformCustomField == null ? null : affiliatedPlatformCustomField.getOptions("", jiraContextNode);
        Map<Long, Long> affiliatedPlatformOptionSequenceMap = new HashMap<>();
        if (!ObjectUtils.isEmpty(affiliatedPlatformOptions)) {
            for (Option option : affiliatedPlatformOptions) {
                for (Option childOption : option.getChildOptions()) {
                    affiliatedPlatformOptionSequenceMap.put(childOption.getOptionId(), optionNum++);
                }
                affiliatedPlatformOptionSequenceMap.put(option.getOptionId(), optionNum++);
            }
        }
        Options platformCateOptions = affiliatedPlatformCustomField == null ? null : affiliatedPlatformCustomField.getOptions("", jiraContextNode);
        Map<Long, Long> platformCateOptionSequenceMap = new HashMap<>();
        if (!ObjectUtils.isEmpty(platformCateOptions)) {
            for (Option option : platformCateOptions) {
                for (Option childOption : option.getChildOptions()) {
                    platformCateOptionSequenceMap.put(childOption.getOptionId(), optionNum++);
                }
                platformCateOptionSequenceMap.put(option.getOptionId(), optionNum++);
            }
        }
        Options options = projectCateCustomField == null ? null : projectCateCustomField.getOptions("", jiraContextNode);
        Map<Long, Long> cateOptionSequenceMap = new HashMap<>();
        if (!ObjectUtils.isEmpty(options)) {
            for (Option option : options) {
                for (Option childOption : option.getChildOptions()) {
                    cateOptionSequenceMap.put(childOption.getOptionId(), optionNum++);
                }
                cateOptionSequenceMap.put(option.getOptionId(), optionNum++);
            }
        }
        Map<Long, Long> projectLevelMap = Utils.getProjectLevelMap();
        Map<Long, Long> reviewResultMap = Utils.getReviewResultMap();
        Map<Long, Long> reviewResult1Map = Utils.getReviewResult1Map();
        Map<Long, Long> reviewResult2Map = Utils.getReviewResult2Map();
        List<String> unPassStatusList = Utils.getUnPassStatusList();
        List<String> toBeReviewedList = Utils.getToBeReviewedList();
        for (Issue issue : issueList) {
            String statusId = issue.getStatusId();
            String statusName = issue.getStatus().getName();
//            Long issueType = Constant.platformIssueTypeId.equals(Long.parseLong(issue.getIssueTypeId())) ? 1L : Constant.topicIssueTypeId.equals(Long.parseLong(issue.getIssueTypeId())) ? 2L : 0L;
            Long issueType = 1L;//1-平台，2-课题

            long isInitiationPass = unPassStatusList.contains(statusId) ? 2L : toBeReviewedList.contains(statusId) ? 0L : 1L;
            ApplicationUser reporter = issue.getReporter();

            Map<String, Option> affiliatedPlatform = affiliatedPlatformCustomField == null ? new HashMap<>() : (Map<String, Option>) issue.getCustomFieldValue(affiliatedPlatformCustomField);
            if (ObjectUtil.isEmpty(affiliatedPlatform)) {
                affiliatedPlatform = new HashMap<>();
            } else {
                no = affiliatedPlatform.get(null) == null
                        ? 999L
                        : affiliatedPlatform.get("1") == null
                        ? affiliatedPlatformOptionSequenceMap.getOrDefault(affiliatedPlatform.get(null).getOptionId(), 999L)
                        : affiliatedPlatformOptionSequenceMap.getOrDefault(affiliatedPlatform.get("1").getOptionId(), 999L);
            }
            Map<String, Option> platformCate = platformCateCustomField == null ? new HashMap<>() : (Map<String, Option>) issue.getCustomFieldValue(platformCateCustomField);
            if (ObjectUtil.isEmpty(platformCate)) {
                platformCate = new HashMap<>();
            } else {
                no = platformCate.get(null) == null
                        ? 999L
                        : platformCate.get("1") == null
                        ? platformCateOptionSequenceMap.getOrDefault(platformCate.get(null).getOptionId(), 999L)
                        : platformCateOptionSequenceMap.getOrDefault(platformCate.get("1").getOptionId(), 999L);
            }

            Map<String, Option> projectCateMap = projectCateCustomField == null ? new HashMap<>() : (Map<String, Option>) issue.getCustomFieldValue(projectCateCustomField);
            Option parentOption = projectCateMap.get(null);
            Option option = projectCateMap.get("1");
            String cate1 = ObjectUtils.isEmpty(parentOption) ? "" : parentOption.getValue();
            String cate2 = "";
            String cate3 = "";
            Long projectCateOptionId = parentOption == null ? 0L : parentOption.getOptionId();
            if (!ObjectUtils.isEmpty(option)) {
                projectCateOptionId = option.getOptionId();
                String optionValue = option.getValue();
                String[] optionValues = optionValue.split("-");
                cate2 = optionValues[0];
                if (optionValues.length > 1) {
                    cate3 = optionValues[1];
                }
                no = no * cateOptionSequenceMap.size() + cateOptionSequenceMap.getOrDefault(option.getOptionId(), 99999L);
            } else {
                no = no * cateOptionSequenceMap.size() + (ObjectUtils.isEmpty(parentOption) ? 99999L : cateOptionSequenceMap.getOrDefault(parentOption.getOptionId(), 99999L));
            }

            String projectName = projectNameCustomField == null ? null : (String) issue.getCustomFieldValue(projectNameCustomField);
            String projectPurpose = projectPurposeCustomField == null ? null : (String) issue.getCustomFieldValue(projectPurposeCustomField);
            String projectTarget = projectTargetCustomField == null ? null : (String) issue.getCustomFieldValue(projectTargetCustomField);
            String projectBackGround = projectBackGroundCustomField == null ? null : (String) issue.getCustomFieldValue(projectBackGroundCustomField);
            String researchContent = researchContentCustomField == null ? null : (String) issue.getCustomFieldValue(researchContentCustomField);
            Option projectLevel = projectLevelCustomField == null ? null : (Option) issue.getCustomFieldValue(projectLevelCustomField);
            Map<String, Option> department = departmentCustomField == null ? new HashMap<>() : (Map<String, Option>) issue.getCustomFieldValue(departmentCustomField);
            Map<String, Option> jmDepartment = jmDepartmentCustomField == null ? new HashMap<>() : (Map<String, Option>) issue.getCustomFieldValue(jmDepartmentCustomField);
            if (ObjectUtil.isNotEmpty(jmDepartment)) {
                department = jmDepartment;
            } else {
                jmDepartment = new HashMap<>();
            }
            ApplicationUser directLeader = directLeaderCustomField == null ? null : (ApplicationUser) issue.getCustomFieldValue(directLeaderCustomField);
            ApplicationUser inspectorGeneral = inspectorGeneralCustomField == null ? null : (ApplicationUser) issue.getCustomFieldValue(inspectorGeneralCustomField);
            ApplicationUser deputyHeadOfTheInstitute = deputyHeadOfTheInstituteCustomField == null ? null : (ApplicationUser) issue.getCustomFieldValue(deputyHeadOfTheInstituteCustomField);
            ApplicationUser headOfTheInstitute = headOfTheInstituteCustomField == null ? null : (ApplicationUser) issue.getCustomFieldValue(headOfTheInstituteCustomField);
            ApplicationUser president = presidentCustomField == null ? null : (ApplicationUser) issue.getCustomFieldValue(presidentCustomField);
            ApplicationUser projectManager = projectManagerCustomField == null ? null : (ApplicationUser) issue.getCustomFieldValue(projectManagerCustomField);
            ApplicationUser platformLeader = platformLeaderCustomFieldId == null ? null : (ApplicationUser) issue.getCustomFieldValue(platformLeaderCustomFieldId);
            String projectLeader = projectLeaderCustomField == null ? null : (String) issue.getCustomFieldValue(projectLeaderCustomField);

            Option reviewResult = reviewResultCustomField == null ? null : (Option) issue.getCustomFieldValue(reviewResultCustomField);
            Option reviewResult1 = reviewResult1CustomField == null ? null : (Option) issue.getCustomFieldValue(reviewResult1CustomField);
            String reviewOpinion1 = reviewOpinion1CustomField == null ? null : (String) issue.getCustomFieldValue(reviewOpinion1CustomField);
            Option reviewResult2 = reviewResult2CustomField == null ? null : (Option) issue.getCustomFieldValue(reviewResult2CustomField);
            String reviewOpinion2 = reviewOpinion2CustomField == null ? null : (String) issue.getCustomFieldValue(reviewOpinion2CustomField);
            String secondReviewValue = secondReviewCustomField == null ? null : (String) issue.getCustomFieldValue(secondReviewCustomField);
            Timestamp projectCompleteDate= projectCompleteCustomField == null ? null : (Timestamp) issue.getCustomFieldValue(projectCompleteCustomField);
            Option topicTypeOption = topicTypeCustomField == null ? null : (Option) issue.getCustomFieldValue(topicTypeCustomField);

            if (ObjectUtils.isEmpty(reviewResult2)) {
                if (ObjectUtils.isEmpty(reviewResult1)) {
                    isInitiationPass = reviewResult == null ? isInitiationPass : reviewResultMap.getOrDefault(reviewResult.getOptionId(), isInitiationPass);
                } else {
                    isInitiationPass = reviewResult1Map.getOrDefault(reviewResult1.getOptionId(), isInitiationPass);
                }
            } else {
                isInitiationPass = reviewResult2Map.getOrDefault(reviewResult2.getOptionId(), isInitiationPass);
            }

            //根据到期日计算延期时间，已解决项目不计算延期时间
            Resolution resolution = issue.getResolution();

            Long delayDay = resolution == null ? Utils.diffDate(new Date(), issue.getDueDate()) : 0L;

            boolean isCompleteProject = projectCompleteDate != null;
            //1-正常、2-延期、3-暂停（10762）、4-停止（11349）、5-结项（10775、10322）
            String projectStatus = "10762".equals(statusId) ? "3" : "11349".equals(statusId) ? "4" : isCompleteProject ? "5" : delayDay > 6 ? "2" : "1";

            ProjectReviewListBean projectReviewListBean = new ProjectReviewListBean(
                    issue.getId(),
                    issue.getKey(),
                    Long.parseLong(statusId),
                    statusName,
                    ObjectUtils.isEmpty(secondReviewValue) ? 0L : 1L,
                    no,
                    cate1,
                    cate2,
                    cate3,
                    projectCateOptionId,
                    projectName == null ? "" : projectName,
                    projectPurpose == null ? "" : projectPurpose,
                    projectTarget == null ? "" : projectTarget,
                    projectBackGround == null ? "" : projectBackGround,
                    researchContent == null ? "" : researchContent,
                    issueType,
                    projectLevel == null ? 4L : projectLevelMap.getOrDefault(projectLevel.getOptionId(), 4L),
                    projectLeader == null ? "" : projectLeader,
                    reporter == null ? "" : reporter.getUsername(),
                    department.get(null) == null ? "" : department.get(null).getValue(),
                    department.get("1") == null ? "" : department.get("1").getValue(),
                    department.get(null) == null ? 0L : department.get(null).getOptionId(),
                    department.get(null) == null ? 0L : department.get("1") == null ? department.get(null).getOptionId() : department.get("1").getOptionId(),

                    directLeader == null ? "" : jiraCustomTool.getFirstAndLastName(directLeader),
                    directLeader == null ? "" : directLeader.getUsername(),
                    inspectorGeneral == null ? "" : jiraCustomTool.getFirstAndLastName(inspectorGeneral),
                    inspectorGeneral == null ? "" : inspectorGeneral.getUsername(),
                    deputyHeadOfTheInstitute == null ? "" : jiraCustomTool.getFirstAndLastName(deputyHeadOfTheInstitute),
                    deputyHeadOfTheInstitute == null ? "" : deputyHeadOfTheInstitute.getUsername(),
                    headOfTheInstitute == null ? "" : jiraCustomTool.getFirstAndLastName(headOfTheInstitute),
                    headOfTheInstitute == null ? "" : headOfTheInstitute.getUsername(),
                    president == null ? "" : jiraCustomTool.getFirstAndLastName(president),
                    president == null ? "" : president.getUsername(),
                    projectManager == null ? "" : jiraCustomTool.getFirstAndLastName(projectManager),
                    projectManager == null ? "" : projectManager.getUsername(),
                    reviewResult1 == null ? null : reviewResult1Map.getOrDefault(reviewResult1.getOptionId(), null),
                    reviewOpinion1 == null ? "" : reviewOpinion1,
                    reviewResult2 == null ? null : reviewResult2Map.getOrDefault(reviewResult2.getOptionId(), null),
                    reviewOpinion2 == null ? "" : reviewOpinion2
            );
            projectReviewListBean.setCreateDate(Utils.getDateFormat(issue.getCreated()));
            projectReviewListBean.setIsInitiationPass(isInitiationPass);
            projectReviewListBean.setIsCompleteProject(isCompleteProject ? 1L : 0L);
            projectReviewListBean.setProjectStatus(projectStatus);
            Option affiliatedPlatformOption = affiliatedPlatform.getOrDefault("1", affiliatedPlatform.getOrDefault(null,null));
            projectReviewListBean.setAffiliatedPlatform(affiliatedPlatformOption == null ? 0L : affiliatedPlatformOption.getOptionId());
            projectReviewListBean.setAffiliatedPlatform1(affiliatedPlatform.get(null) == null ? "" : affiliatedPlatform.get(null).getValue());
            projectReviewListBean.setAffiliatedPlatform2(affiliatedPlatform.get("1") == null ? "" : affiliatedPlatform.get("1").getValue());
            projectReviewListBean.setPlatformLeader(jiraCustomTool.getFirstAndLastName(platformLeader));
            projectReviewListBean.setPlatformLeaderId(platformLeader == null ? "" : platformLeader.getUsername());
            projectReviewListBean.setParentJmDepartment(jmDepartment.get(null) == null ? 0L : jmDepartment.get(null).getOptionId());
            projectReviewListBean.setParentJmDepartmentName(jmDepartment.get(null) == null ? "" : jmDepartment.get(null).getValue());
            projectReviewListBean.setChildJmDepartment(jmDepartment.get(null) == null ? 0L : jmDepartment.get("1") == null ? jmDepartment.get(null).getOptionId() : jmDepartment.get("1").getOptionId());
            projectReviewListBean.setChildJmDepartmentName(jmDepartment.get("1") == null ? "" : jmDepartment.get("1").getValue());
            projectReviewListBean.setArea(Constant.lfpProjectManageProjectId.equals(issue.getProjectId())||Constant.lfpProjectManageTestProjectId.equals(issue.getProjectId())?"JM":"HZ");
            projectReviewListBean.setTopicType(topicTypeOption==null?null:topicTypeOption.getOptionId());
            projectReviewListBeanList.add(projectReviewListBean);

        }
        return projectReviewListBeanList;
    }
    public ResultBean runTransitionByName(String token, String transitionName,UpdateCustomFiledBean updateCustomFiledBean) {
        ResultBean resultBean = new ResultBean();
        try {
            Long issueId = updateCustomFiledBean.getIssueId();
            log.error("进入转换执行接口:Token:"+token+" issue:" + issueId + "转换id:" + transitionName);
            ApplicationUser applicationUser = JwtTokenUtil.getApplicationUserByToken(token);
            MutableIssue mutableIssue = ComponentAccessor.getIssueManager().getIssueObject(issueId);
            String statusId = mutableIssue.getStatusId();
            Map<String, String> map = updateCustomFiledBean.getMap();

            String isSystemCall = map == null ? "" : map.get("isSystemCall");
            customToolService.runTransitionByName(mutableIssue, applicationUser, transitionName, issueService.newIssueInputParameters());
            //更新字段
//            if ("1".equals(isSystemCall)) {
//                CustomField customField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(Constant.isSystemCallCustomFieldId);
//                mutableIssue.setCustomFieldValue(customField, isSystemCall);
//                ComponentAccessor.getIssueManager().updateIssue(applicationUser, mutableIssue, EventDispatchOption.ISSUE_UPDATED, false);
//            }
            String statusId1 = mutableIssue.getStatusId();
            log.error("转换执行接口完成");
            resultBean.setValue(statusId.equals(statusId1) ? "S" : "E");
        } catch (Exception e) {
            log.error(Utils.errInfo(e));
            resultBean.setMessage(e.getMessage());
        }
        return resultBean;
    }

    public ResultBean runTransitionByNameList(String token, List<UpdateCustomFiledBean> updateCustomFiledBeanList) {
        ResultBean resultBean = new ResultBean();
        try {
            log.error("进入批量执行转换接口:Token:"+token+" updateCustomFiledBeanList:" + JSONObject.toJSONString(updateCustomFiledBeanList));
            if (ObjectUtils.isEmpty(updateCustomFiledBeanList)) {
                throw new IllegalArgumentException("传递参数为空");
            }
//            ExecutorService executor = new ThreadPoolExecutor(5, 5,0L, TimeUnit.MILLISECONDS,new LinkedBlockingQueue<Runnable>());
//            final CountDownLatch countDownLatch = new CountDownLatch(updateCustomFiledBeanList.size());
            //执行转换
            List<Long> successList = Collections.synchronizedList(new ArrayList<>());
            List<Long> failList = Collections.synchronizedList(new ArrayList<>());
            Map<Long, String> failMap = new ConcurrentHashMap<>();
//            AtomicInteger runNum = new AtomicInteger();
//            log.error("runNumAll:{}", countDownLatch.getCount());
            //记录本次执行转换的issue
            for (UpdateCustomFiledBean updateCustomFiledBean : updateCustomFiledBeanList) {
//                executor.execute(() -> {
                    Long issueId = updateCustomFiledBean.getIssueId();
//                    JiraThreadLocalUtil jiraThreadLocalUtil = ComponentAccessor.getComponent(JiraThreadLocalUtil.class);
//                    jiraThreadLocalUtil.preCall();
                    try {
                        String transitionName = updateCustomFiledBean.getTransitionName();
                        ApplicationUser applicationUser = ComponentAccessor.getUserManager().getUserByName(updateCustomFiledBean.getUserName());
//                        ApplicationUser applicationUser = JwtTokenUtil.getApplicationUserByToken(token);
                        if (ObjectUtils.isEmpty(applicationUser)) {
                            throw new IllegalArgumentException("Jira不存在该用户：" + updateCustomFiledBean.getUserName());
                        }
                        //模拟用户登录
                        JiraAuthenticationContext context = ComponentAccessor.getComponent(JiraAuthenticationContext.class);
                        context.setLoggedInUser(applicationUser);
                        MutableIssue mutableIssue = ComponentAccessor.getIssueManager().getIssueObject(issueId);
                        IssueInputParameters issueInputParameters = customToolService.getIssueInputParameters(mutableIssue.getProjectId(),Utils.getReviewListCustomFiledMap(),updateCustomFiledBean);
                        customToolService.runTransitionByName(mutableIssue, applicationUser, transitionName, issueInputParameters);
                        successList.add(issueId);
//                        jiraThreadLocalUtil.postCall(log4jLogger);
//                        countDownLatch.countDown();
                    } catch (Exception e) {
                        log.error("转换执行失败：issueId:"+issueId+" Exception:"+Utils.errInfo(e));
                        failList.add(issueId);
                        failMap.put(issueId, e.getMessage());
                    } finally {
//                        log.error("runNum:{}", runNum.incrementAndGet());
//                        jiraThreadLocalUtil.postCall(log4jLogger);
//                        countDownLatch.countDown();
                    }
//                });
            }

//            try {
//                countDownLatch.await();
//            } catch (Exception e) {
//                log.error("多线程构造数据出错：" + e.getMessage());
//            } finally {
//                executor.shutdown();
//            }

            Map<String, Object> resultMap = new HashMap<>();
            //successList更新字段信息，失败的不进行更新
            //新线程更新信息
            List<UpdateCustomFiledBean> updateCustomFiledBeans = updateCustomFiledBeanList.stream().filter(e -> successList.contains(e.getIssueId())).collect(Collectors.toList());
            new Thread(()->{
                JiraThreadLocalUtil jiraThreadLocalUtil = ComponentAccessor.getComponent(JiraThreadLocalUtil.class);
                jiraThreadLocalUtil.preCall();
                try {
                    for (UpdateCustomFiledBean updateCustomFiledBean : updateCustomFiledBeans) {
                        Map<String, String> map = updateCustomFiledBean.getMap();
                        if (map == null) {
                            continue;
                        }
                        updateCustomFiled(token, 0, updateCustomFiledBean);
                    }
                } catch (Exception e) {
                    log.error("更新信息异常Exception:" + Utils.errInfo(e));
                } finally {
                    jiraThreadLocalUtil.postCall((org.apache.log4j.Logger) log);
                }
            }).start();
            resultMap.put("successList", successList);
            resultMap.put("failList", failList);
            resultMap.put("failMap", failMap);
            resultBean.setValue(resultMap);
            log.error("批量执行转换接口完成，回传参数：{}", resultMap);
        } catch (Exception e) {
            log.error(Utils.errInfo(e));
            resultBean.setMessage(e.getMessage());
        }
        return resultBean;
    }

    public ResultBean updateCustomFiled(String token, int isOnline, UpdateCustomFiledBean updateCustomFiledBean) {
        ResultBean resultBean = new ResultBean();
        try {
            log.error("进入issue信息更新接口" + updateCustomFiledBean + " token:" + token);
//            ApplicationUser applicationUser = JwtTokenUtil.getApplicationUserByToken(token);
            Long issueId = updateCustomFiledBean.getIssueId();
            Map<String, String> map = updateCustomFiledBean.getMap();
            ApplicationUser applicationUser = ComponentAccessor.getUserManager().getUserByName(updateCustomFiledBean.getUserName());
            if (applicationUser == null) {
                throw new IllegalArgumentException("Jira系统不存在该用户");
            }
            Map<String, Long> reviewListCustomFiledMap = Utils.getReviewListCustomFiledMap();
            MutableIssue mutableIssue = ComponentAccessor.getIssueManager().getIssueObject(issueId == null ? 0L : issueId);
            if (Objects.equals(issueId, 0L) && ObjectUtils.isEmpty(mutableIssue)) {
                String jql = "issuetype in standardIssueTypes() AND cf[" + reviewListCustomFiledMap.get("projectName") + "] = " + map.get("projectName")+" AND cf[" + reviewListCustomFiledMap.get("researchContent") + "] = " + map.get("researchContent");
                List<Issue> list = jiraCustomTool.getIssueListByJql(applicationUser, jql);

                if (!ObjectUtils.isEmpty(list)) {
                    return resultBean;
                }
                mutableIssue = creatProjectIssue(isOnline,updateCustomFiledBean);
//                resultBean.setValue("问题创建完成:" + mutableIssue.getKey());
//                return resultBean;
            }



            Map<Long, Long> reviewResult1Map = Utils.getReviewResult1Map();
            Map<Long, Long> reviewResult2Map = Utils.getReviewResult2Map();
            Map<Long, Long> projectLevelMap = Utils.getProjectLevelMap();
            int updateSuccess = 0;
            for (Map.Entry<String, String> entry : map.entrySet()) {
                String entryKey = entry.getKey();
                String entryValue = entry.getValue();
                Long customFiledId = reviewListCustomFiledMap.getOrDefault(entryKey, 0L);

                CustomField customFiled = customFieldManager.getCustomFieldObject(customFiledId);
                if (customFiled == null || entryValue == null || "".equals(entryValue)) {
                    if ("summary".equals(entry.getKey())) {
                        mutableIssue.setSummary(entryValue);
//                    }else if ("issueType".equals(entry.getKey())) {
//                        if ("1".equals(entryValue)) {
//                            mutableIssue.setIssueTypeId(Constant.platformIssueTypeId + "");
//                        } else if ("2".equals(entryValue)) {
//                            mutableIssue.setIssueTypeId(Constant.topicIssueTypeId + "");
//                        }
                    }else if ("comment".equals(entryKey)) {
                        ComponentAccessor.getCommentManager().create(mutableIssue, applicationUser, entryValue, true);
                    } else if ("projectLeaderId".equals(entryKey)) {
                        ApplicationUser user = ComponentAccessor.getUserManager().getUserByName(entryValue);
                        if (user == null) {
                            throw new IllegalArgumentException("用户工号传递错误，jira未找到用户：" + entryValue);
                        }
                        mutableIssue.setReporter(user);
                    }
                    continue;
                }
                String customFieldTypeKey = customFiled.getCustomFieldType().getKey();
//                log.error("issue信息更新customFieldTypeKey：" + customFieldTypeKey);
                switch (customFieldTypeKey) {
                    case "com.atlassian.jira.plugin.system.customfieldtypes:select":
                        Long optionId = 0L;
                        switch (entryKey) {
                            case "reviewResult1":
                                for (Map.Entry<Long, Long> longEntry : reviewResult1Map.entrySet()) {
                                    if (longEntry.getValue().equals(Long.parseLong(entryValue))) {
                                        optionId = longEntry.getKey();
                                    }
                                }
                                break;
                            case "reviewResult2":
                                optionId = reviewResult2Map.entrySet().stream().filter(e -> e.getValue().equals(Long.parseLong(entryValue))).map(Map.Entry::getKey).findFirst().orElse(0L);
                                break;
                            case "projectLevel":
                                optionId = projectLevelMap.entrySet().stream().filter(e -> e.getValue().equals(Long.parseLong(entryValue))).map(Map.Entry::getKey).findFirst().orElse(4L);
                                break;
                            default:
                                break;
                        }
                        JiraContextNode jiraContextNode = new ProjectContext(mutableIssue.getProjectId(), ComponentAccessor.getProjectManager());
                        Options options = customFiled.getOptions("", jiraContextNode);
//                        mutableIssue.setCustomFieldValue(customFiled, options.stream().filter(e -> optionId.equals(e.getOptionId())).findFirst().orElse(null));
                        for (Option option : options) {
                            if (optionId.equals(option.getOptionId())) {
                                mutableIssue.setCustomFieldValue(customFiled, option);
                                break;
                            }
                        }
                        break;
                    case "com.atlassian.jira.plugin.system.customfieldtypes:cascadingselect":
//                        String projectCate = map.getOrDefault(entryKey, "");
//                        List<ProjectCateBean> projectCateBeanList = ObjectUtils.isEmpty(projectCate) ? new ArrayList<>() : JSONObject.parseArray(projectCate, ProjectCateBean.class);
//                        Long cascadingParentOptionId = ObjectUtils.isEmpty(projectCateBeanList) ? 0L : projectCateBeanList.stream().filter(e -> e.getPid().equals(1L)).map(ProjectCateBean::getId).findFirst().orElse(0L);
//                        Long cascadingOptionId = ObjectUtils.isEmpty(projectCateBeanList) ? 0L : projectCateBeanList.stream().filter(e -> e.getPid().equals(cascadingParentOptionId)).map(ProjectCateBean::getId).findFirst().orElse(0L);
                        Option option = ComponentAccessor.getOptionsManager().findByOptionId(Long.parseLong(entryValue));
                        if (option == null) {
                            continue;
                        }
                        Option parentOption = option.getParentOption();
                        Map<String, Option> optionMap = new HashMap<>();
                        if (parentOption != null) {
                            optionMap.put(null,parentOption);
                            optionMap.put("1",option);
                        } else {
                            optionMap.put(null,option);
                            optionMap.put("1",null);
                        }
                        mutableIssue.setCustomFieldValue(customFiled,optionMap);
                        break;
                    case "com.atlassian.jira.plugin.system.customfieldtypes:userpicker":
                        ApplicationUser user = ComponentAccessor.getUserManager().getUserByName(entryValue);
                        if (user == null) {
                            throw new IllegalArgumentException("用户工号传递错误，jira未找到用户：" + entryValue);
                        }
                        mutableIssue.setCustomFieldValue(customFiled, user);
                        break;
                    case "com.atlassian.jira.plugin.system.customfieldtypes:textfield":
                    case "com.atlassian.jira.plugin.system.customfieldtypes:textarea":
                        if ("projectName".equals(entryKey)) {
                            mutableIssue.setSummary(entryValue + "-" + jiraCustomTool.getFirstAndLastName(mutableIssue.getReporter()));
                        }
                        mutableIssue.setCustomFieldValue(customFiled, entryValue);
                        break;
                    case "com.atlassian.jira.plugin.system.customfieldtypes:float":
                        mutableIssue.setCustomFieldValue(customFiled, Double.valueOf(entryValue));
                        break;
                    default:
                        break;
                }
                updateSuccess++;
            }
            ComponentAccessor.getIssueManager().updateIssue(applicationUser, mutableIssue, EventDispatchOption.ISSUE_UPDATED, false);
            Map<String, Object> resultMap = new HashMap<>();
            resultMap.put("total", (long) map.size());
            resultMap.put("update", (long) updateSuccess);
            resultMap.put("issueId", mutableIssue.getId());
            resultMap.put("issueKey", mutableIssue.getKey());
            log.error("issue信息更新接口返回数据:{}", resultMap);
            resultBean.setValue(resultMap);
        } catch (Exception e) {
            log.error(Utils.errInfo(e));
            resultBean.setMessage(e.getMessage());
        }
        return resultBean;
    }
    public MutableIssue creatProjectIssue(int isOnline, UpdateCustomFiledBean updateCustomFiledBean) throws CreateException {
        Map<String, String> map = updateCustomFiledBean.getMap();
//        String userName = updateCustomFiledBean.getUserName();
        String userName = map.getOrDefault("projectLeaderId", "");
        String area = map.getOrDefault("area", "HZ");
        ApplicationUser applicationUser = ComponentAccessor.getUserManager().getUserByName(userName);
        Assert.notNull(applicationUser, "课题负责人工号传递错误，jira未找到用户：" + userName);
        map.put("topicLeader",jiraCustomTool.getFirstAndLastName(applicationUser));//报告人设置到课题负责人
        Map<Long, Long> projectLevelMap = Utils.getProjectLevelMap();
        //模拟用户登录
        JiraAuthenticationContext context = ComponentAccessor.getComponent(JiraAuthenticationContext.class);
        context.setLoggedInUser(applicationUser);
        log.error("开始创建问题，参数：" + updateCustomFiledBean);
        //获取issue的服务
        IssueService issueService = ComponentAccessor.getIssueService();
        //创建issue参数
        IssueInputParameters issueInputParameters = issueService.newIssueInputParameters();
        // 设置项目ID
//        issueInputParameters.setProjectId("JM".equals(map.getOrDefault("projectId",""))?Constant.projectManageProjectId:Constant.lfpProjectManageProjectId);

//        List<Long> lfpDepartmentIdList = Arrays.asList(18711L, 22269L, 18854L, 18876L,22487L);
        //18711L-铁锂电池研究一所、22269L-储能电池研究所、18854L-基础技术研究一所、18876L-工程技术研究一所、22487L-锰铁锂电池研究所
        //所属部门 in cascadeOption(18711) OR 所属部门 in cascadeOption(22269) OR 所属部门 in cascadeOption(18854) OR 所属部门 in cascadeOption(18876)
        boolean isJmProject = "JM".equals(area);
        Long projectId = 0L;
        if (1 == isOnline) {
//            Long departmentCateId = Long.parseLong(map.get("departmentCate"));
//            Option option = ComponentAccessor.getOptionsManager().findByOptionId(departmentCateId);
//            if (ObjectUtils.isEmpty(option)){
//                throw new IllegalArgumentException("根据选项id未找到选项：departmentCate");
//            }
//            Option parentOption = option.getParentOption();
//            //18711L-方形、18876L-工程，创建到荆门LFP项目。创建到惠州项目
//            issueInputParameters.setProjectId(
//                    ((parentOption != null) ? lfpDepartmentIdList.contains(parentOption.getOptionId()) : lfpDepartmentIdList.contains(departmentCateId))
//                            ? Constant.lfpProjectManageProjectId
//                            : Constant.projectManageProjectId);
            projectId = isJmProject ? Constant.lfpProjectManageProjectId : Constant.projectManageProjectId;
        } else {
            projectId = isJmProject ? Constant.lfpProjectManageTestProjectId : Constant.projectManageTestProjectId;//测试项目id
        }
        String platformIssueTypeId = Constant.platformIssueTypeId.toString();
        customToolService.stringSet.add(projectId + platformIssueTypeId);
        issueInputParameters.setProjectId(projectId);
        //设置问题类型
        issueInputParameters.setIssueTypeId(platformIssueTypeId);
        //设置概要
        issueInputParameters.setSummary(map.getOrDefault("projectName", "/") + "-" + applicationUser.getDisplayName());


        // 设置报告人
        issueInputParameters.setReporterId(applicationUser.getKey());
        //设置字段值
        for (Map.Entry<String, String> entry : map.entrySet()) {
            String entryKey = entry.getKey();
            String entryValue = entry.getValue();
            Long customFiledId = Utils.getReviewListCustomFiledMap().getOrDefault(entryKey, 0L);
            CustomField customFiled = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(customFiledId);
            if (customFiled == null || entryValue == null || "".equals(entryValue)) {
                continue;
            }

            String customFieldTypeKey = customFiled.getCustomFieldType().getKey();
            switch (customFieldTypeKey) {
                case "com.atlassian.jira.plugin.system.customfieldtypes:select":
                    Long optionId = 0L;
                    switch (entryKey) {
                        case "projectLevel":
                            optionId = projectLevelMap.entrySet().stream().filter(e -> e.getValue().equals(Long.parseLong(entryValue))).map(Map.Entry::getKey).findFirst().orElse(-1L);
                            break;
                        case "topicType":
                            optionId = Long.parseLong(entryValue);
                            break;
                        default:
                            break;
                    }
//                    log.error("创建issue等级字段ID：" + optionId + "，选项ID：" + customFiledId);
                    issueInputParameters.addCustomFieldValue(customFiledId, String.valueOf(optionId));
                    break;
                case "com.atlassian.jira.plugin.system.customfieldtypes:cascadingselect":
                    Option option = ComponentAccessor.getOptionsManager().findByOptionId(Long.parseLong(entryValue));
                    if (ObjectUtils.isEmpty(option)){
                        throw new IllegalArgumentException("根据选项id未找到选项：" + entryKey);
                    }
                    Option parentOption = option.getParentOption();
                    if (ObjectUtils.isEmpty(parentOption)) {
                        issueInputParameters.addCustomFieldValue(
                                customFiledId,
                                String.valueOf(option.getOptionId())
                        );
                    } else {
                        issueInputParameters.addCustomFieldValue(
                                customFiledId,
                                String.valueOf(parentOption.getOptionId())
                        );
                        issueInputParameters.addCustomFieldValue(
                                "customfield_"+customFiledId+":1",
                                String.valueOf(option.getOptionId())
                        );
                    }
                    break;
                case "com.atlassian.jira.plugin.system.customfieldtypes:userpicker":
                    ApplicationUser user = ComponentAccessor.getUserManager().getUserByName(entryValue);
                    if (user == null) {
                        throw new IllegalArgumentException("用户工号传递错误，jira未找到用户：" + entryValue);
                    }
                    issueInputParameters.addCustomFieldValue(customFiledId, user.getUsername());
                    break;
                case "com.atlassian.jira.plugin.system.customfieldtypes:datepicker":
                case "com.atlassian.jira.plugin.system.customfieldtypes:datetime":
                    Date date = java.sql.Date.valueOf(entryValue);
                    DateTimeFormatter dateFormatter = ComponentAccessor.getComponent(DateTimeFormatter.class);
                    dateFormatter = dateFormatter.forLoggedInUser();
                    String dateStr = dateFormatter.withStyle(DateTimeStyle.DATE_PICKER).format(date);
                    issueInputParameters.addCustomFieldValue(customFiledId, dateStr);
                    break;
                case "com.atlassian.jira.plugin.system.customfieldtypes:textfield":
                case "com.atlassian.jira.plugin.system.customfieldtypes:textarea":
                    issueInputParameters.addCustomFieldValue(customFiledId, entryValue);
                    break;
                default:
                    customToolService.stringSet.remove(projectId + platformIssueTypeId);
                    throw new IllegalStateException("Unexpected value: " + customFieldTypeKey);
            }
        }
        //创建issue参数校验
        IssueService.CreateValidationResult createValidationResult = issueService.validateCreate(applicationUser, issueInputParameters);

        if (!createValidationResult.isValid()) {
            customToolService.stringSet.remove(projectId + platformIssueTypeId);
            throw new CreateException("创建问题参数校验失败：" + createValidationResult.getErrorCollection());
        }
        //创建issue
        IssueService.IssueResult createResult = issueService.create(applicationUser, createValidationResult);
        if (!createResult.isValid()) {
            customToolService.stringSet.remove(projectId + platformIssueTypeId);
            throw new CreateException("创建问题失败：" + createResult.getErrorCollection().getErrorMessages());
        }
        customToolService.stringSet.remove(projectId + platformIssueTypeId);
        return createResult.getIssue();
    }

    public ResultBean queryInitiationPassRate(String token, String startDateStr, String endDateStr, String area) {

        ResultBean resultBean = new ResultBean();
        try {
//            ApplicationUser applicationUser = JwtTokenUtil.getApplicationUserByToken(token);
            ApplicationUser applicationUser = ComponentAccessor.getUserManager().getUserByName(Constant.dladminUserName);
            if (ObjectUtils.isEmpty(applicationUser)) {
                throw new IllegalArgumentException("该用户在Jira系统中不存在");
            }
            String projects = "";
            if (area.contains("HZ")) {
                projects = "(XMGL)";
            } else if (area.contains("JM")) {
                projects = "(LFPXMGL)";
            }
            if ((area.contains("HZ") && area.contains("JM")) || area.contains("ALL")) {
                projects = "(XMGL, LFPXMGL)";
            }
            String jql = "project in " + projects + " AND issuetype in standardIssueTypes() AND cf[" + Constant.reviewDateCustomFieldId + "] >= " + startDateStr + " AND cf[" + Constant.reviewDateCustomFieldId + "] <= " + endDateStr;
            log.error("立项通过/不通过清单:1-" + token + " 2-" + startDateStr + " 3-" + endDateStr + " JQL-" + jql);
//            long start1 = System.currentTimeMillis();
            List<Issue> issueList = jiraCustomTool.getIssueListByJql(applicationUser, jql);
//            long start2 = System.currentTimeMillis();
            List<ProjectReportBean> projectReportBeanList = ObjectUtils.isEmpty(issueList) ? new ArrayList<>() : getReportBeanListByNullRetry(applicationUser, issueList);
//            long start3 = System.currentTimeMillis();
            resultBean.setValue(projectReportBeanList);
//            log.error("start1" + start1 + "start2" + start2 + "start3" + start3);
//            log.error("立项通过/不通过清单结束" + projectReportBeanList.size());
        } catch (Exception e) {
            log.error(Utils.errInfo(e));
            resultBean.setMessage(e.getMessage());
        }
        return resultBean;
    }

    /**
     * 项目管理-项目列表处理
     * @param applicationUser
     * @param issueList
     * @param isOnline
     * @return
     */
    private List<ProjectReportBean> getProjectReportBeanList(ApplicationUser applicationUser, List<Issue> issueList, Integer isOnline) {
//        long start = System.currentTimeMillis();
        //获取自定义字段
        CustomField subDepartCustomField = customFieldManager.getCustomFieldObject(Constant.subDepartCustID);
        CustomField jmDepartmentCustomField = customFieldManager.getCustomFieldObject(Constant.jmDepartmentCustID);
        CustomField projectCateCustomField = customFieldManager.getCustomFieldObject(Constant.projectCateCustomFieldId);
        CustomField affiliatedPlatformCustomField = customFieldManager.getCustomFieldObject(Constant.affiliatedPlatformCustomFieldId);
        CustomField platformCateCustomField = customFieldManager.getCustomFieldObject(Constant.platformCateCustomFieldId);
        CustomField projectNameCustomField = customFieldManager.getCustomFieldObject(Constant.projectNameCustomFieldId);
        CustomField projectCodeCustomField = customFieldManager.getCustomFieldObject(Constant.projectCodeCustomFieldId);
        CustomField projectBackGroundCustomField = customFieldManager.getCustomFieldObject(Constant.projectBackGroundCustomFieldId);
        CustomField projectPurposeCustomField = customFieldManager.getCustomFieldObject(Constant.projectPurposeCustomFieldId);
        CustomField projectTargetCustomField = customFieldManager.getCustomFieldObject(Constant.projectTargetCustomFieldId);
        CustomField projectLevelCustomField = customFieldManager.getCustomFieldObject(Constant.projectLevelCustomFieldId);
        CustomField developmentBudgetCustomField = customFieldManager.getCustomFieldObject(Constant.developmentBudgetCustomFieldId);
        CustomField progressCustomField = customFieldManager.getCustomFieldObject(Constant.progressCustomFieldId);
        CustomField problemAndRiskCustomField = customFieldManager.getCustomFieldObject(Constant.problemAndRiskCustomFieldId);
        CustomField strategyCustomField = customFieldManager.getCustomFieldObject(Constant.strategyCustomFieldId);
        CustomField nextPlanCustomField = customFieldManager.getCustomFieldObject(Constant.nextPlanCustomFieldId);
        CustomField planInitiationDateCustomField = customFieldManager.getCustomFieldObject(Constant.planInitiationDateCustomFieldId);
        CustomField initiationPassDateCustomField = customFieldManager.getCustomFieldObject(Constant.initiationPassDateCustomFieldId);
        CustomField initiationDateCustomField = customFieldManager.getCustomFieldObject(Constant.initiationDateCustomFieldId);
        CustomField planSchemeReviewDateCustomField = customFieldManager.getCustomFieldObject(Constant.planSchemeReviewDateCustomFieldId);
        CustomField schemeReviewPassDateCustomField = customFieldManager.getCustomFieldObject(Constant.schemeReviewPassDateCustomFieldId);
        CustomField planTechnicalSummaryDateCustomField = customFieldManager.getCustomFieldObject(Constant.planTechnicalSummaryDateCustomFieldId);
        CustomField technicalSummaryPassDateCustomField = customFieldManager.getCustomFieldObject(Constant.technicalSummaryPassDateCustomFieldId);
        CustomField projectCompleteCustomField = customFieldManager.getCustomFieldObject(Constant.projectCompleteCustomFieldId);
        CustomField topicLeaderCustomField = customFieldManager.getCustomFieldObject(Constant.topicLeaderCustomFieldId);

        CustomField reviewResultCustomField = customFieldManager.getCustomFieldObject(Constant.reviewResultCustomFieldId);
        CustomField reviewResult1CustomField = customFieldManager.getCustomFieldObject(Constant.reviewResult1CustomFieldId);
//        CustomField reviewOpinion1CustomField = customFieldManager.getCustomFieldObject(Constant.reviewOpinion1CustomFieldId);
        CustomField reviewResult2CustomField = customFieldManager.getCustomFieldObject(Constant.reviewResult2CustomFieldId);
//        CustomField reviewOpinion2CustomField = customFieldManager.getCustomFieldObject(Constant.reviewOpinion2CustomFieldId);
//        CustomField secondReviewCustomField = customFieldManager.getCustomFieldObject(Constant.secondReviewCustomFieldId);

        List<ProjectReportBean> projectReportBeanList = new Vector<>();//多线程操作数组，使用vector
        Map<Long, Long> reviewResultMap = Utils.getReviewResultMap();
        Map<Long, Long> reviewResult1Map = Utils.getReviewResult1Map();
        Map<Long, Long> reviewResult2Map = Utils.getReviewResult2Map();
        List<String> unPassStatusList = Utils.getUnPassStatusList();
        List<String> toBeReviewedList = Utils.getToBeReviewedList();

//        ExecutorService executor = Executors.newFixedThreadPool(5);
        ExecutorService executor = new ThreadPoolExecutor(5, 5,0L, TimeUnit.MILLISECONDS,new LinkedBlockingQueue<Runnable>());
        final CountDownLatch countDownLatch = new CountDownLatch(issueList.size());

        for (Issue issue : issueList) {
            executor.execute(()->{
                String statusId = issue.getStatusId();
                long isInitiationPass = unPassStatusList.contains(statusId) ? 2L : toBeReviewedList.contains(statusId) ? 0L : 1L;

                Map<String, Option> subDepartMap = subDepartCustomField == null ? new HashMap<>() : (Map<String, Option>) issue.getCustomFieldValue(subDepartCustomField);
                List<JiraOptionBean> subDepartList = jiraCustomTool.tranCascadingToList(subDepartMap);

                Map<String, Option> jmDepartmentMap = jmDepartmentCustomField == null ? new HashMap<>() : (Map<String, Option>) issue.getCustomFieldValue(jmDepartmentCustomField);
                List<JiraOptionBean> jmDepartmentList = jiraCustomTool.tranCascadingToList(jmDepartmentMap);
                if (ObjectUtil.isNotEmpty(jmDepartmentList)) {
                    subDepartList = jmDepartmentList;
                }

                Map<String, Option> projectCateMap = projectCateCustomField == null ? new HashMap<>() : (Map<String, Option>) issue.getCustomFieldValue(projectCateCustomField);
                List<JiraOptionBean> projectCateList = jiraCustomTool.tranCascadingToList(projectCateMap);

                Map<String, Option> affiliatedPlatformMap = affiliatedPlatformCustomField == null ? new HashMap<>() : (Map<String, Option>) issue.getCustomFieldValue(affiliatedPlatformCustomField);
                List<JiraOptionBean> affiliatedPlatformList = jiraCustomTool.tranCascadingToList(affiliatedPlatformMap);
                Map<String, Option> platformCateMap = platformCateCustomField == null ? new HashMap<>() : (Map<String, Option>) issue.getCustomFieldValue(platformCateCustomField);
                List<JiraOptionBean> platformCateList = jiraCustomTool.tranCascadingToList(platformCateMap);

                IssueType issueType = issue.getIssueType();
                String projectName = projectNameCustomField == null ? null : (String) issue.getCustomFieldValue(projectNameCustomField);
                String projectCode = projectCodeCustomField == null ? null : (String) issue.getCustomFieldValue(projectCodeCustomField);
                String projectBackGround = projectBackGroundCustomField == null ? null : (String) issue.getCustomFieldValue(projectBackGroundCustomField);
                String projectPurpose = projectPurposeCustomField == null ? null : (String) issue.getCustomFieldValue(projectPurposeCustomField);
                String projectTarget = projectTargetCustomField == null ? null : (String) issue.getCustomFieldValue(projectTargetCustomField);
                Option projectLevel = projectLevelCustomField == null ? null : (Option) issue.getCustomFieldValue(projectLevelCustomField);
                Double developmentBudget = developmentBudgetCustomField == null ? null : (Double) issue.getCustomFieldValue(developmentBudgetCustomField);
                Status status = issue.getStatus();
                String statusName = status == null ? "" : status.getName();
                String progress = progressCustomField == null ? null : (String) issue.getCustomFieldValue(progressCustomField);
                String problemAndRisk = problemAndRiskCustomField == null ? null : (String) issue.getCustomFieldValue(problemAndRiskCustomField);
                String strategy = strategyCustomField == null ? null : (String) issue.getCustomFieldValue(strategyCustomField);
                String nextPlan = nextPlanCustomField == null ? null : (String) issue.getCustomFieldValue(nextPlanCustomField);
                String topicLeader = topicLeaderCustomField == null ? null : (String) issue.getCustomFieldValue(topicLeaderCustomField);
                Timestamp planInitiationDate = planInitiationDateCustomField == null ? null : (Timestamp) issue.getCustomFieldValue(planInitiationDateCustomField);
                Timestamp initiationPassDate = initiationPassDateCustomField == null ? null : (Timestamp) issue.getCustomFieldValue(initiationPassDateCustomField);
                Timestamp initiationDate = initiationDateCustomField == null ? null : (Timestamp) issue.getCustomFieldValue(initiationDateCustomField);
                Timestamp planSchemeReviewDate = planSchemeReviewDateCustomField == null ? null : (Timestamp) issue.getCustomFieldValue(planSchemeReviewDateCustomField);
                Timestamp schemeReviewPassDate = schemeReviewPassDateCustomField == null ? null : (Timestamp) issue.getCustomFieldValue(schemeReviewPassDateCustomField);
                Timestamp planTechnicalSummaryDate = planTechnicalSummaryDateCustomField == null ? null : (Timestamp) issue.getCustomFieldValue(planTechnicalSummaryDateCustomField);
                Timestamp technicalSummaryPassDate = technicalSummaryPassDateCustomField == null ? null : (Timestamp) issue.getCustomFieldValue(technicalSummaryPassDateCustomField);
                Timestamp projectCompleteDate = projectCompleteCustomField == null ? null : (Timestamp) issue.getCustomFieldValue(projectCompleteCustomField);

                //获取立项结果
                Option reviewResult = reviewResultCustomField == null ? null : (Option) issue.getCustomFieldValue(reviewResultCustomField);
                Option reviewResult1 = reviewResult1CustomField == null ? null : (Option) issue.getCustomFieldValue(reviewResult1CustomField);
//                String reviewOpinion1 = reviewOpinion1CustomField == null ? null : (String) issue.getCustomFieldValue(reviewOpinion1CustomField);
                Option reviewResult2 = reviewResult2CustomField == null ? null : (Option) issue.getCustomFieldValue(reviewResult2CustomField);
//                String reviewOpinion2 = reviewOpinion2CustomField == null ? null : (String) issue.getCustomFieldValue(reviewOpinion2CustomField);
//                String secondReviewValue = secondReviewCustomField == null ? null : (String) issue.getCustomFieldValue(secondReviewCustomField);


                if (ObjectUtils.isEmpty(reviewResult2)) {
                    if (ObjectUtils.isEmpty(reviewResult1)) {
                        isInitiationPass = reviewResult == null ? isInitiationPass : reviewResultMap.getOrDefault(reviewResult.getOptionId(), isInitiationPass);
                    } else {
                        isInitiationPass = reviewResult1Map.getOrDefault(reviewResult1.getOptionId(), isInitiationPass);//一次评审结果不为空，以一次评审结果为准
                    }
                } else {
                    isInitiationPass = reviewResult2Map.getOrDefault(reviewResult2.getOptionId(), isInitiationPass);//二次评审结果不为空，以二次评审结果为准
                }

//                if (ObjectUtils.isEmpty(reviewResult)) {
//                    if (ObjectUtils.isEmpty(reviewResult2)) {
//                        if (ObjectUtils.isEmpty(reviewResult1)) {
//                            isInitiationPass = reviewResult1Map.getOrDefault(reviewResult1.getOptionId(), isInitiationPass);//一次评审结果不为空，以一次评审结果为准
//                        }
//                    } else {
//                        isInitiationPass = reviewResult2Map.getOrDefault(reviewResult2.getOptionId(), isInitiationPass);//二次评审结果不为空，以二次评审结果为准
//                    }
//                } else {
//                    isInitiationPass = reviewResultMap.getOrDefault(reviewResult.getOptionId(), isInitiationPass);//评审结果不为空，以评审结果为准
//                }


                //根据到期日计算延期时间，已解决项目不计算延期时间
                Resolution resolution = issue.getResolution();

                boolean isCompleteProject = projectCompleteDate != null;
                Long delayDay = resolution == null ? Utils.diffDate(new Date(), issue.getDueDate()) : 0L;

                //1-正常、2-延期、3-暂停（10762）、4-停止（11349）、5-结项（10775、10322）
                String projectStatus = "10762".equals(statusId) ? "3" : "11349".equals(statusId) ? "4" : isCompleteProject ? "5" : delayDay > 6 ? "2" : "1";

                ProjectReportBean projectReportBean = new ProjectReportBean(
                        subDepartList,
                        projectCateList,
                        issueType == null ? "" : issueType.getName(),
                        projectName == null ? "" : projectName,
                        projectCode == null ? "" : projectCode,
                        projectBackGround == null ? "" : projectBackGround,
                        projectPurpose == null ? "" : projectPurpose,
                        projectTarget == null ? "" : projectTarget,
                        projectLevel == null ? "B" : projectLevel.getValue(),
                        developmentBudget == null ? "" : developmentBudget.toString(),
                        statusName,
                        progress == null ? "" : progress,
                        problemAndRisk == null ? "" : problemAndRisk,
                        strategy == null ? "" : strategy,
                        nextPlan == null ? "" : nextPlan,
                        planInitiationDate == null ? "" : Utils.getDateFormat(planInitiationDate),
                        initiationPassDate == null ? "" : Utils.getDateFormat(initiationPassDate),
                        planTechnicalSummaryDate == null ? "" : Utils.getDateFormat(planTechnicalSummaryDate),
                        technicalSummaryPassDate == null ? "" : Utils.getDateFormat(technicalSummaryPassDate)
                );
                projectReportBean.setIsInitiationPass(isInitiationPass);
                projectReportBean.setProjectLeader(topicLeader == null ? jiraCustomTool.getFirstAndLastName(issue.getReporter()) : topicLeader);
                projectReportBean.setPlanSchemeReviewDate(planSchemeReviewDate == null ? "" : Utils.getDateFormat(planSchemeReviewDate));
                projectReportBean.setSchemeReviewPassDate(schemeReviewPassDate == null ? "" : Utils.getDateFormat(schemeReviewPassDate));
                projectReportBean.setDelayDay(delayDay < 0 ? 0L : delayDay);
                projectReportBean.setIsCompleteProject(isCompleteProject ? 1L : 0L);
                projectReportBean.setStatusId(statusId);
                projectReportBean.setInitiationDate(initiationDate == null ? "" : Utils.getDateFormat(initiationDate));
                projectReportBean.setIssueId(issue.getId());
                projectReportBean.setIssueKey(issue.getKey());
                projectReportBean.setProjectStatus(projectStatus);
//                projectReportBean.setAffiliatedPlatformList(Constant.projectManageProjectId.equals(issue.getProjectId()) || Constant.projectManageTestProjectId.equals(issue.getProjectId()) ? platformCateList : affiliatedPlatformList);
                List<JiraOptionBean> jiraOptionBeanList = (Constant.projectManageProjectId.equals(issue.getProjectId()) || Constant.projectManageTestProjectId.equals(issue.getProjectId())) ? platformCateList : affiliatedPlatformList;
                projectReportBean.setAffiliatedPlatformList(jiraOptionBeanList);
                projectReportBean.setPlatformCateList(platformCateList);
                projectReportBean.setJmDepartmentList(jmDepartmentList);

                projectReportBeanList.add(projectReportBean);
                countDownLatch.countDown();
            });

        }
        try {
            boolean await = countDownLatch.await(60, TimeUnit.SECONDS);
            if (!await) {
                log.error("多线程构造数据超时！");
            }
        } catch (Exception e) {
            log.error("多线程构造数据出错：" + e.getMessage());
        } finally {
            executor.shutdown();
        }
//        long end = System.currentTimeMillis();
//        log.error("平台|课题项目查询，耗时：" + (end - start) + "ms");
        return projectReportBeanList;
    }


}
