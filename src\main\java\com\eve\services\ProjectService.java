package com.eve.services;

import com.atlassian.jira.component.ComponentAccessor;
import com.atlassian.jira.project.Project;
import com.atlassian.jira.project.ProjectCategory;
import com.eve.beans.CategoryBean;
import com.eve.beans.ProjectBean;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;
import java.util.List;

public class ProjectService {

    public List<CategoryBean> getCategorys() {
        List<CategoryBean> categoryBeans = new ArrayList();
        Collection<ProjectCategory> projectCategories = ComponentAccessor.getProjectManager().getAllProjectCategories();
        Iterator<ProjectCategory> it = projectCategories.iterator();
        while (it.hasNext()) {
            ProjectCategory projectCategory = it.next();
            categoryBeans.add(new CategoryBean(projectCategory.getId(), projectCategory.getName()));
        }
        return categoryBeans;
    }

    public List<ProjectBean> getProdectsByCate(Long cateId) {
        List<ProjectBean> projectBeans = new ArrayList();
        Collection<Project> projects =
                cateId != 0 ? ComponentAccessor.getProjectManager().getProjectObjectsFromProjectCategory(cateId):
                ComponentAccessor.getProjectManager().getProjectObjectsWithNoCategory();
        Iterator<Project> it = projects.iterator();
        while (it.hasNext()){
            Project project = it.next();
            projectBeans.add(new ProjectBean(project.getId(),project.getName(),project.getKey(),project.getUrl()));
        }
        return projectBeans;
    }

}
