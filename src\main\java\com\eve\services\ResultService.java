package com.eve.services;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.atlassian.jira.bc.issue.IssueService;
import com.atlassian.jira.component.ComponentAccessor;
import com.atlassian.jira.config.SubTaskManager;
import com.atlassian.jira.event.type.EventDispatchOption;
import com.atlassian.jira.exception.CreateException;
import com.atlassian.jira.issue.CustomFieldManager;
import com.atlassian.jira.issue.Issue;
import com.atlassian.jira.issue.IssueInputParameters;
import com.atlassian.jira.issue.MutableIssue;
import com.atlassian.jira.issue.customfields.manager.OptionsManager;
import com.atlassian.jira.issue.customfields.option.Option;
import com.atlassian.jira.issue.fields.CustomField;
import com.atlassian.jira.security.JiraAuthenticationContext;
import com.atlassian.jira.user.ApplicationUser;
import com.atlassian.jira.user.util.UserManager;
import com.eve.beans.*;
import com.eve.utils.Constant;
import com.eve.utils.JiraCustomTool;
import com.eve.utils.Utils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/1/14
 */
public class ResultService {
    private static final Logger log = LoggerFactory.getLogger(ResultService.class);

    private JiraCustomTool jiraCustomTool;
    private CustomFieldManager customFieldManager;
    private IssueService issueService;
    private OptionsManager optionsManager;
    private UserManager userManager;
    @Resource
    private CustomToolService customToolService;

    public ResultService(JiraCustomTool jiraCustomTool, CustomFieldManager customFieldManager, IssueService issueService, OptionsManager optionsManager, UserManager userManager) {
        this.jiraCustomTool = jiraCustomTool;
        this.customFieldManager = customFieldManager;
        this.issueService = issueService;
        this.optionsManager = optionsManager;
        this.userManager = userManager;
    }

    public ResultBean getAllResult(String area, Integer isOnline) {
        ResultBean resultBean = new ResultBean();
        try {
            ApplicationUser applicationUser = ComponentAccessor.getUserManager().getUserByName(Constant.dladminUserName);
//            String jql = "project in (LFPCGGL, CG) AND issuetype in (标准化类成果, 知识产权成果, 课题研究成果) AND status = 成果认定通过";//cf[11845] 成果认定通过日期
            // AND cf["+Constant.achievementPassDateCustomFieldId+"] >= 2022-01-01
//            String projects = "(CG, LFPCGGL)";
//            if (area.contains("HZ")) {
//                projects = "(CG)";
//            } else if (area.contains("JM")) {
//                projects = "(LFPCGGL)";
//            }
//            if ((area.contains("HZ") && area.contains("JM")) || area.contains("ALL")) {
//                projects = "(CG, LFPCGGL)";
//            }
//            String jql = "project in " + projects + " AND issuetype in (标准化类成果, 知识产权成果, 课题研究成果) AND status = 成果认定通过 AND assignee was \"029026\" ";//cf[11845] 成果认定通过日期

            String projects = "(XMGL, LFPXMGL)";
            if (area.contains("HZ")) {
                projects = "(XMGL)";
            } else if (area.contains("JM")) {
                projects = "(LFPXMGL)";
            }
            if ((area.contains("HZ") && area.contains("JM")) || area.contains("ALL")) {
                projects = "(XMGL, LFPXMGL)";
            }
            String jql = "project in " + projects + " AND issuetype in (技术课题) AND status = 成果认定通过 ";//cf[11845] 成果认定通过日期
            log.error("查询地区："+area+" 成果清单查询JQL：" + jql);
            List<Issue> issueList = jiraCustomTool.getIssueListByJql(applicationUser, jql);
            log.error("查询结果："+issueList.size());
            List<ProjectReportBean> resultList = getResultList(applicationUser, issueList,isOnline);
            log.error("成果数据构造结果："+resultList.size());
            resultBean.setValue(resultList);
        } catch (Exception e) {
            log.error(Utils.errInfo(e));
            resultBean.setMessage(e.getMessage());
        }
        return resultBean;
    }

    public List<ProjectReportBean> getResultList(ApplicationUser applicationUser, List<Issue> issueList, Integer isOnline) {

        List<ProjectReportBean> projectReportBeanList = new ArrayList<>();
        //获取字段
        CustomField subDepartCustomField = customFieldManager.getCustomFieldObject(Constant.subDepartCustID);
        CustomField jmDepartmentCustomField = customFieldManager.getCustomFieldObject(Constant.jmDepartmentCustID);
        CustomField achievementCateCustomField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(Constant.projectCateCustomFieldId);
        CustomField mainCompletePersonCustomField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(Constant.mainCompletePersonCustomFieldId);
        CustomField achievementNameCustomField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(Constant.projectNameCustomFieldId);
        CustomField achievementModalityCustomField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(Constant.achievementModalityCustomFieldId);
        CustomField achievementContentCustomFieldId = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(Constant.achievementContentCustomFieldId);
        CustomField directorScoreCustomField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(Constant.directorScoreCustomFieldId);
        CustomField gainDirectorScoreCustomField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(Constant.gainDirectorScoreCustomFieldId);
        CustomField directorEvaluationCustomField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(Constant.directorEvaluationCustomFieldId);
        CustomField averageScoreCustomField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(Constant.averageScoreCustomFieldId);
        CustomField presidentScoreCustomField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(Constant.presidentScoreCustomFieldId);
        CustomField gainPresidentScoreCustomField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(Constant.gainPresidentScoreCustomFieldId);
        CustomField presidentEvaluationCustomField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(Constant.presidentEvaluationCustomFieldId);
        CustomField achievementPassDateCustomField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(Constant.achievementPassDateCustomFieldId);
        CustomField initiationDateCustomField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(Constant.initiationDateCustomFieldId);
        CustomField resultAfter22YearCustomField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(Constant.resultAfter22YearCustomFieldId);
        CustomField affiliatedPlatformCustomField = customFieldManager.getCustomFieldObject(Constant.affiliatedPlatformCustomFieldId);
        CustomField platformCateCustomField = customFieldManager.getCustomFieldObject(Constant.platformCateCustomFieldId);
        CustomField topicLeaderCustomField = customFieldManager.getCustomFieldObject(Constant.topicLeaderCustomFieldId);

        for (Issue issue : issueList) {
            boolean isGainProject = Constant.achievementManageIssueTypeIdList.contains(Long.valueOf(issue.getIssueTypeId()));
//            String resultAfter22Year = resultAfter22YearCustomField == null ? null : (String) issue.getCustomFieldValue(resultAfter22YearCustomField);
//            if (resultAfter22Year != null && resultAfter22Year.contains("否")) {//22年之前立项的课题成果，不统计
//                continue;
//            }
            Map<String, Option> subDepartMap = subDepartCustomField == null ? new HashMap<>() : (Map<String, Option>) issue.getCustomFieldValue(subDepartCustomField);
            List<JiraOptionBean> subDepartList = jiraCustomTool.tranCascadingToList(subDepartMap);
            Map<String, Option> jmDepartmentMap = jmDepartmentCustomField == null ? new HashMap<>() : (Map<String, Option>) issue.getCustomFieldValue(jmDepartmentCustomField);
            List<JiraOptionBean> jmDepartmentList = jiraCustomTool.tranCascadingToList(jmDepartmentMap);
            if (ObjectUtil.isNotEmpty(jmDepartmentList)) {
                subDepartList = jmDepartmentList;
            }

            Map<String, Option> achievementCateMap = achievementCateCustomField == null ? null : (Map<String, Option>) issue.getCustomFieldValue(achievementCateCustomField);
            List<JiraOptionBean> resultCateList = jiraCustomTool.tranCascadingToList(achievementCateMap);
            Map<String, Option> affiliatedPlatformMap = affiliatedPlatformCustomField == null ? new HashMap<>() : (Map<String, Option>) issue.getCustomFieldValue(affiliatedPlatformCustomField);
            List<JiraOptionBean> affiliatedPlatformList = jiraCustomTool.tranCascadingToList(affiliatedPlatformMap);
            Map<String, Option> platformCateMap = platformCateCustomField == null ? new HashMap<>() : (Map<String, Option>) issue.getCustomFieldValue(platformCateCustomField);
            List<JiraOptionBean> platformCateList = jiraCustomTool.tranCascadingToList(platformCateMap);
            List<ApplicationUser> mainCompletePersonList = mainCompletePersonCustomField == null ? new ArrayList<>() : (List<ApplicationUser>) issue.getCustomFieldValue(mainCompletePersonCustomField);
            List<String> firstAndLastNameList = jiraCustomTool.getFirstAndLastName(mainCompletePersonList);
            String achievementName = achievementNameCustomField == null ? null : (String) issue.getCustomFieldValue(achievementNameCustomField);
            List<Option> achievementModalityOptionList = achievementModalityCustomField == null ? null : (List<Option>) issue.getCustomFieldValue(achievementModalityCustomField);
            List<JiraOptionBean> resultTypeBeanList = new ArrayList<>();
            if (!ObjectUtils.isEmpty(achievementModalityOptionList)) {
                resultTypeBeanList = achievementModalityOptionList.stream().map(e -> new JiraOptionBean(e.getOptionId(), 1L, e.getValue(), "", e.getSequence())).collect(Collectors.toList());
            }
//            List<String> achievementModalityValueList = ObjectUtils.isEmpty(achievementModalityOptionList) ? new ArrayList<>() : achievementModalityOptionList.stream().map(Option::getValue).collect(Collectors.toList());
            String achievementContent = achievementContentCustomFieldId == null ? null : (String) issue.getCustomFieldValue(achievementContentCustomFieldId);
            Double directorScore = isGainProject
                    ? (directorScoreCustomField == null ? null : (Double) issue.getCustomFieldValue(directorScoreCustomField))
                    : (gainDirectorScoreCustomField == null ? null : (Double) issue.getCustomFieldValue(gainDirectorScoreCustomField));
            String directorEvaluation = directorEvaluationCustomField == null ? null : (String) issue.getCustomFieldValue(directorEvaluationCustomField);
            Double averageScore = averageScoreCustomField == null ? null : (Double) issue.getCustomFieldValue(averageScoreCustomField);
            String presidentEvaluation = presidentEvaluationCustomField == null ? null : (String) issue.getCustomFieldValue(presidentEvaluationCustomField);
            Double presidentScore = isGainProject
                    ? (presidentScoreCustomField == null ? null : (Double) issue.getCustomFieldValue(presidentScoreCustomField))
                    : (gainPresidentScoreCustomField == null ? null : (Double) issue.getCustomFieldValue(gainPresidentScoreCustomField));
            Timestamp achievementPassDate = achievementPassDateCustomField == null ? null : (Timestamp) issue.getCustomFieldValue(achievementPassDateCustomField);
            Timestamp initiationDate = initiationDateCustomField == null ? null : (Timestamp) issue.getCustomFieldValue(initiationDateCustomField);
            String topicLeader = topicLeaderCustomField == null ? null : (String) issue.getCustomFieldValue(topicLeaderCustomField);
            Collection<Issue> subTaskObjects = issue.getSubTaskObjects();
            String resultReviewRequestIssueReporterIds = subTaskObjects.stream()
                    .filter(e -> e.getResolution() == null && e.getIssueTypeId().equals(Constant.resultReviewRequestIssueTypeId + ""))
                    .map(e -> e.getReporter().getUsername() + "," + e.getKey()).collect(Collectors.joining(";"));

//            Collection<Attachment> attachmentCollection = issue.getAttachments();
//            List<Long> attachmentIdList = ObjectUtils.isEmpty(attachmentCollection) ? new ArrayList<>() : attachmentCollection.stream().map(Attachment::getId).collect(Collectors.toList());
//            Timestamp issueCreateDate = issue.getCreated();
            String statusName = issue.getStatus().getName();
            List<JiraOptionBean> jiraOptionBeanList = (Constant.projectManageProjectId.equals(issue.getProjectId()) || Constant.projectManageTestProjectId.equals(issue.getProjectId())) ? platformCateList : affiliatedPlatformList;
            ProjectReportBean projectReportBean = new ProjectReportBean.Builder()
                    .issueId(issue.getId()).issueKey(issue.getKey()).statusName(statusName)
                    .resultName(achievementName).departmentCateList(subDepartList).resultCateList(resultCateList).resultTypeList(resultTypeBeanList)
                    .resultContent(achievementContent == null ? "" : achievementContent)
                    .directorScore(directorScore == null ? "" : directorScore.intValue() + "")
                    .directorEvaluation(directorEvaluation == null ? "" : directorEvaluation)
                    .presidentScore(presidentScore == null
                            ? (directorScore == null ? "" : directorScore.intValue() + "")
                            : presidentScore.intValue() + "")
                    .presidentEvaluation(presidentEvaluation == null
                            ? (directorEvaluation == null ? "" : directorEvaluation)
                            : presidentEvaluation)
                    .averageScore(averageScore == null ? "" : averageScore.intValue() + "")
                    .initiationDate(initiationDate == null ? "" : Utils.getDateFormat(initiationDate))
                    .resultPassDate(achievementPassDate == null ? "" : Utils.getDateFormat(achievementPassDate))
                    .mainCompletePerson(firstAndLastNameList)
                    .projectLeader(topicLeader)
                    .resultLeader(jiraCustomTool.getFirstAndLastName(issue.getReporter()))
                    .affiliatedPlatformList(jiraOptionBeanList)
                    .platformCateList(platformCateList)
                    .requestReporterIds(resultReviewRequestIssueReporterIds).build();
            projectReportBeanList.add(projectReportBean);
        }
        return projectReportBeanList;
    }

    public ResultBean getReviewResult(Long issueId) {
        MutableIssue mutableIssue = ComponentAccessor.getIssueManager().getIssueObject(issueId);
        Collection<Issue> subIssueList = mutableIssue.getSubTaskObjects();
        ResultBean resultBean = new ResultBean();
        try {
            List<ResultReviewBean> resultReviewBeanList = new ArrayList<>();
            subIssueList = subIssueList.stream().filter(e -> e.getIssueTypeId().equals(Constant.gainReviewSubIssueTypeId + "")).collect(Collectors.toList());
            boolean isGainProject = Constant.achievementManageIssueTypeIdList.contains(Long.valueOf(mutableIssue.getIssueTypeId()));
            for (Issue issue : subIssueList) {
                CustomField reviewUserCustomField = customFieldManager.getCustomFieldObject(Constant.reviewUserCustomFieldId);//评审人
                CustomField reviewScoreCustomField = customFieldManager.getCustomFieldObject(Constant.reviewScoreCustomFieldId);//评分
                CustomField reviewOpinionCustomField = customFieldManager.getCustomFieldObject(Constant.reviewOpinionCustomFieldId);//评价

                ApplicationUser reviewUser = (ApplicationUser) issue.getCustomFieldValue(reviewUserCustomField);
                Double reviewScore = (Double) issue.getCustomFieldValue(reviewScoreCustomField);
                String reviewOpinion = (String) issue.getCustomFieldValue(reviewOpinionCustomField);
                ResultReviewBean reviewReviewBean = new ResultReviewBean.Builder()
                        .reviewType("review")
                        .reviewUser(reviewUser.getDisplayName())
                        .reviewScore(reviewScore == null ? "" : reviewScore.toString())
                        .reviewOpinion(reviewOpinion == null ? "/" : reviewOpinion)
                        .build();
                resultReviewBeanList.add(reviewReviewBean);
            }
            CustomField headOfTheInstituteCustomField = customFieldManager.getCustomFieldObject(Constant.headOfTheInstituteCustomFieldId);//所长
            CustomField directorScoreCustomField = customFieldManager.getCustomFieldObject(isGainProject ? Constant.directorScoreCustomFieldId : Constant.gainDirectorScoreCustomFieldId);//所长评分
            CustomField directorEvaluationCustomField = customFieldManager.getCustomFieldObject(Constant.directorEvaluationCustomFieldId);//所长评价

            CustomField presidentCustomField = customFieldManager.getCustomFieldObject(Constant.presidentCustomFieldId);//院长
            CustomField presidentScoreCustomField = customFieldManager.getCustomFieldObject(isGainProject ? Constant.presidentScoreCustomFieldId : Constant.gainPresidentScoreCustomFieldId);//院长评分
            CustomField presidentEvaluationCustomField = customFieldManager.getCustomFieldObject(Constant.presidentEvaluationCustomFieldId);//院长评价

            ApplicationUser headOfTheInstitute = (ApplicationUser) mutableIssue.getCustomFieldValue(headOfTheInstituteCustomField);
            Double directorScore = (Double) mutableIssue.getCustomFieldValue(directorScoreCustomField);
            String directorEvaluation = (String) mutableIssue.getCustomFieldValue(directorEvaluationCustomField);
            if (directorScore != null) {
                ResultReviewBean headOfTheInstituteReviewReviewBean = new ResultReviewBean.Builder()
                        .reviewType("headOfTheInstituteReview")
                        .reviewUser(headOfTheInstitute == null ? "" : headOfTheInstitute.getDisplayName())
                        .reviewScore(directorScore == null ? "" : directorScore.toString())
                        .reviewOpinion(directorEvaluation == null ? "/" : directorEvaluation)
                        .build();
                resultReviewBeanList.add(headOfTheInstituteReviewReviewBean);
            }

            ApplicationUser president = (ApplicationUser) mutableIssue.getCustomFieldValue(presidentCustomField);
            Double presidentScore = (Double) mutableIssue.getCustomFieldValue(presidentScoreCustomField);
            String presidentEvaluation = (String) mutableIssue.getCustomFieldValue(presidentEvaluationCustomField);
            if (presidentScore != null) {
                ResultReviewBean presidentReviewReviewBean = new ResultReviewBean.Builder()
                        .reviewType("presidentReview")
                        .reviewUser(president == null ? (Constant.lfpProjectManageProjectId.equals(mutableIssue.getProjectId()) ? "苑丁丁" : "David.He(何巍)") : president.getDisplayName())
                        .reviewScore(presidentScore == null ? "" : presidentScore.toString())
                        .reviewOpinion(presidentEvaluation == null ? "/" : presidentEvaluation)
                        .build();
                resultReviewBeanList.add(presidentReviewReviewBean);
            }
            log.error("获取课题成果评分："+JSON.toJSONString(resultReviewBeanList));
            resultBean.setValue(resultReviewBeanList);
        } catch (Exception e) {
            log.error(Utils.errInfo(e));
            resultBean.setMessage(e.getMessage());
        }
        return resultBean;
    }

    public ResultBean getResultReviewRequest(Long issueId) {

        MutableIssue mutableIssue = ComponentAccessor.getIssueManager().getIssueObject(issueId);
        Collection<Issue> subIssueList = mutableIssue.getSubTaskObjects();
        ResultBean resultBean = new ResultBean();
        try {
            List<ResultReviewBean> resultReviewBeanList = new ArrayList<>();

            for (Issue issue : subIssueList) {
                CustomField reviewUserCustomField = customFieldManager.getCustomFieldObject(Constant.reviewUserCustomFieldId);//评审人
                CustomField reviewScoreCustomField = customFieldManager.getCustomFieldObject(Constant.reviewScoreCustomFieldId);//评分
                CustomField reviewOpinionCustomField = customFieldManager.getCustomFieldObject(Constant.reviewOpinionCustomFieldId);//评价

                ApplicationUser reviewUser = (ApplicationUser) issue.getCustomFieldValue(reviewUserCustomField);
                Double reviewScore = (Double) issue.getCustomFieldValue(reviewScoreCustomField);
                String reviewOpinion = (String) issue.getCustomFieldValue(reviewOpinionCustomField);
                ResultReviewBean reviewReviewBean = new ResultReviewBean.Builder()
                        .reviewType("review")
                        .reviewUser(reviewUser.getDisplayName())
                        .reviewScore(reviewScore == null ? "" : reviewScore.toString())
                        .reviewOpinion(reviewOpinion)
                        .build();
                resultReviewBeanList.add(reviewReviewBean);
            }
            CustomField headOfTheInstituteCustomField = customFieldManager.getCustomFieldObject(Constant.headOfTheInstituteCustomFieldId);//所长
            CustomField directorScoreCustomField = customFieldManager.getCustomFieldObject(Constant.directorScoreCustomFieldId);//所长评分
            CustomField directorEvaluationCustomField = customFieldManager.getCustomFieldObject(Constant.directorEvaluationCustomFieldId);//所长评价

            CustomField presidentCustomField = customFieldManager.getCustomFieldObject(Constant.presidentCustomFieldId);//院长
            CustomField presidentScoreCustomField = customFieldManager.getCustomFieldObject(Constant.presidentScoreCustomFieldId);//院长评分
            CustomField presidentEvaluationCustomField = customFieldManager.getCustomFieldObject(Constant.presidentEvaluationCustomFieldId);//院长评价

            ApplicationUser headOfTheInstitute = (ApplicationUser) mutableIssue.getCustomFieldValue(headOfTheInstituteCustomField);
            Double directorScore = (Double) mutableIssue.getCustomFieldValue(directorScoreCustomField);
            String directorEvaluation = (String) mutableIssue.getCustomFieldValue(directorEvaluationCustomField);
            ResultReviewBean headOfTheInstituteReviewReviewBean = new ResultReviewBean.Builder()
                    .reviewType("headOfTheInstituteReview")
                    .reviewUser(headOfTheInstitute == null ? "" : headOfTheInstitute.getDisplayName())
                    .reviewScore(directorScore == null ? "" : directorScore.toString())
                    .reviewOpinion(directorEvaluation == null ? "" : directorEvaluation)
                    .build();
            resultReviewBeanList.add(headOfTheInstituteReviewReviewBean);

            ApplicationUser president = (ApplicationUser) mutableIssue.getCustomFieldValue(presidentCustomField);
            Double presidentScore = (Double) mutableIssue.getCustomFieldValue(presidentScoreCustomField);
            String presidentEvaluation = (String) mutableIssue.getCustomFieldValue(presidentEvaluationCustomField);
            ResultReviewBean presidentReviewReviewBean = new ResultReviewBean.Builder()
                    .reviewType("presidentReview")
                    .reviewUser(president == null ? "" : president.getDisplayName())
                    .reviewScore(presidentScore == null ? "" : presidentScore.toString())
                    .reviewOpinion(presidentEvaluation == null ? "" : presidentEvaluation)
                    .build();
            resultReviewBeanList.add(presidentReviewReviewBean);
            log.error("获取课题成果评分："+JSON.toJSONString(resultReviewBeanList));
            resultBean.setValue(resultReviewBeanList);
        } catch (Exception e) {
            log.error(Utils.errInfo(e));
            resultBean.setMessage(e.getMessage());
        }
        return resultBean;
    }

    public ResultBean resultReviewRequestCreate(int isOnline, UpdateCustomFiledBean updateCustomFiledBean) {
//        String userName = updateCustomFiledBean.getUserName();
//        Map<String, String> map = updateCustomFiledBean.getMap();
        log.error("进入成果查阅申请创建接口:" + updateCustomFiledBean + " isOnline:" + isOnline);
        ResultBean resultBean = new ResultBean();
        try {
            MutableIssue parentIssue = ComponentAccessor.getIssueManager().getIssueObject(updateCustomFiledBean.getIssueId());
//            Map<String, String> map = updateCustomFiledBean.getMap();
            String userName = updateCustomFiledBean.getUserName();
            ApplicationUser currentUser = userManager.getUserByName(userName);
            if (currentUser == null) {
                throw new IllegalArgumentException("用户工号传递错误，jira未找到用户：" + userName);
            }

//            String testCate = map.get("testCate");//测试分类 testCate customfield_12512
//            String testProject = map.get("testProject");//测试项目 testProject customfield_13046
//            String summary = map.get("summary");//概要 summary
//            String requireDepartment = map.get("requireDepartment");//需求方部门长 requireDepartment customfield_
//            ApplicationUser requireDepartmentUser = userManager.getUserByName(requireDepartment);
//            if (requireDepartmentUser == null) {
//                throw new IllegalArgumentException("用户工号传递错误，jira未找到用户：" + requireDepartment);
//            }

//            Map<String, Long> updateCustomFiledMap = Utils.getResultCustomFiledMap();

//        CustomField projectNameCustomField = customFieldManager.getCustomFieldObject(Constant.projectName);
//        String projectName = (String) parentIssue.getCustomFieldValue(projectNameCustomField);

            JiraAuthenticationContext context = ComponentAccessor.getComponent(JiraAuthenticationContext.class);
            context.setLoggedInUser(currentUser);

            /*创建issue参数*/
            IssueInputParameters issueInputParameters = customToolService.getIssueInputParameters(parentIssue.getProjectId(),Utils.getReviewListCustomFiledMap(),updateCustomFiledBean);
            // 设置项目ID todo
            issueInputParameters.setProjectId(parentIssue.getProjectId());
            // 设置问题类型
            issueInputParameters.setIssueTypeId(Constant.resultReviewRequestIssueTypeId + "");
            // 设置经办人为部门经理
//            issueInputParameters.setAssigneeId(requireDepartmentUser.getUsername());
            // 设置报告人
            issueInputParameters.setReporterId(currentUser.getUsername());
            //设置概要 已设置
//            issueInputParameters.setSummary(summary);
            //取产品状态的对应值
//                issueInputParameters.addCustomFieldValue(Constant.productStateCustId, productStatus.get(entry.getValue()) + "");
//                String jhpsrqStr = Utils.getDateFormat(jhpsrDate);
//                issueInputParameters.addCustomFieldValue(Constant.productPlanReviewDateCustId, Utils.getDateStr(jhpsrqStr));

/*            for (Map.Entry<String, String> entry : map.entrySet()) {
                String entryKey = entry.getKey();
                String entryValue = entry.getValue();
                Long customFiledId = updateCustomFiledMap.getOrDefault(entryKey, 0L);

                if (customFiledId == 0) {
                    customFiledId = NumberUtil.isNumber(entryKey) ? Long.parseLong(entryKey) : entryKey.contains("_") ? Long.parseLong(entryKey.split("_")[1]) : 0L;
                }

                CustomField customFiled = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(customFiledId);
                if (customFiled == null || entryValue == null || "".equals(entryValue)) {
                    if ("summary".equals(entryKey)) {
                        issueInputParameters.setSummary(entryValue);
                    } else if ("priority".equals(entryKey)) {
                        issueInputParameters.setPriorityId(entryValue);
                    } else if ("description".equals(entryKey)) {
                        issueInputParameters.setDescription(entryValue);
                    }
                    continue;
                }
                String customFieldTypeKey = customFiled.getCustomFieldType().getKey();
                switch (customFieldTypeKey) {
                    case "com.atlassian.jira.plugin.system.customfieldtypes:select":
                        issueInputParameters.addCustomFieldValue(customFiledId, entryValue);
                        break;
                    case "com.atlassian.jira.plugin.system.customfieldtypes:userpicker":
                        ApplicationUser user = userManager.getUserByName(entryValue);
                        if (user == null) {
                            throw new IllegalArgumentException("用户工号传递错误，jira未找到用户：" + entryValue);
                        }
                        issueInputParameters.addCustomFieldValue(customFiledId, user.getUsername());
                        break;
                    case "com.atlassian.jira.plugin.system.customfieldtypes:multiuserpicker":
                        issueInputParameters.addCustomFieldValue(customFiledId, entryValue);
                        break;
                    case "com.atlassian.jira.plugin.system.customfieldtypes:datepicker":
                    case "com.atlassian.jira.plugin.system.customfieldtypes:datetime":
                        //格式 yyyy-MM-dd HH:mm:ss
                        entryValue = entryValue.length() == 10 ? entryValue + " 00:00:00" : entryValue;
                        issueInputParameters.addCustomFieldValue(customFiledId, Utils.getDateTimeStr(entryValue));
                        break;
                    case "com.atlassian.jira.plugin.system.customfieldtypes:textfield":
                    case "com.atlassian.jira.plugin.system.customfieldtypes:textarea":
                    case "com.atlassian.jira.plugin.system.customfieldtypes:url":
                        issueInputParameters.addCustomFieldValue(customFiledId, entryValue);
                        break;

                    default:
                        throw new IllegalStateException("Unexpected value: " + customFieldTypeKey);
                }
            }*/

//            log.error(issueInputParameters.getCustomFieldValue());
            //创建issue参数校验
            com.atlassian.jira.bc.issue.IssueService.CreateValidationResult createValidationResult = issueService.validateSubTaskCreate(currentUser,parentIssue.getId(), issueInputParameters);


            if (!createValidationResult.isValid()) {
                throw new CreateException("创建问题参数校验失败：" + createValidationResult.getErrorCollection());
            }
            //创建issue
            IssueService.IssueResult subIssueRes = issueService.create(currentUser, createValidationResult);
            if (!subIssueRes.isValid()) {
                throw new CreateException("创建问题失败：" + subIssueRes.getErrorCollection());
            }
            MutableIssue subIssue = subIssueRes.getIssue();
            SubTaskManager subTaskManager = ComponentAccessor.getSubTaskManager();
            subTaskManager.createSubTaskIssueLink(parentIssue, subIssue, currentUser);
            subIssue.setParentId(parentIssue.getId());
//            IssueService.IssueResult createResult = issueService.create(currentUser, createValidationResult);
//            MutableIssue mutableIssue = createResult.getIssue();
            //更新
//            subIssue.setAssignee(requireDepartmentUser);
//            CustomField customFiled = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(Constant.requireDepartmentCustomFieldId);
//            subIssue.setCustomFieldValue(customFiled, requireDepartmentUser);
            ComponentAccessor.getIssueManager().updateIssue(currentUser, subIssue, EventDispatchOption.ISSUE_UPDATED, false);
            log.info("成果查阅申请流程创建成功");
            resultBean.setValue(subIssue.getId());



        } catch (Exception e) {
            log.error(Utils.errInfo(e));
            resultBean.setMessage(e.getMessage());
        }
        return resultBean;
    }


    public ResultBean getGainSubReviewIssue(String token, UpdateCustomFiledBean updateCustomFiledBean) {
        ResultBean resultBean = new ResultBean();
        try {
//            ApplicationUser applicationUser = JwtTokenUtil.getApplicationUserByToken(token);
//            String userName = updateCustomFiledBean.getUserName();
            Long issueId = updateCustomFiledBean.getIssueId();
//            Map<String, String> map = updateCustomFiledBean.getMap();

            MutableIssue mutableIssue = ComponentAccessor.getIssueManager().getIssueObject(issueId);

            List<Issue> gainSubReviewIssueList = mutableIssue.getSubTaskObjects().stream().filter(e -> e.getIssueTypeId().equals(Constant.gainReviewSubIssueTypeId.toString())).collect(Collectors.toList());

            List<GainSubReviewBean> gainSubReviewBeanList = new ArrayList<>();
            CustomField reviewUserCustomField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(Constant.reviewUserCustomFieldId);
            CustomField reviewScoreCustomField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(Constant.reviewScoreCustomFieldId);
            CustomField reviewOpinionCustomField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(Constant.reviewOpinionCustomFieldId);

            for (Issue issue : gainSubReviewIssueList) {
                ApplicationUser reviewUser = issue.getCustomFieldValue(reviewUserCustomField) == null ? null : (ApplicationUser) issue.getCustomFieldValue(reviewUserCustomField);
                Double reviewScore = issue.getCustomFieldValue(reviewScoreCustomField) == null ? 0 : (Double) issue.getCustomFieldValue(reviewScoreCustomField);
                String reviewOpinion = issue.getCustomFieldValue(reviewOpinionCustomField) == null ? "" : (String) issue.getCustomFieldValue(reviewOpinionCustomField);

                GainSubReviewBean gainSubReviewBean = new GainSubReviewBean.Builder()
                        .issueId(issue.getId())
                        .issueKey(issue.getKey())
                        .parentId(issueId)
                        .reviewUserName(reviewUser == null ? "" : reviewUser.getDisplayName())
                        .reviewScore(reviewScore + "")
                        .reviewOpinion(reviewOpinion)
                        .build();
                gainSubReviewBeanList.add(gainSubReviewBean);
            }

            resultBean.setValue(gainSubReviewBeanList);
        } catch (Exception e) {
            log.error(Utils.errInfo(e));
            resultBean.setMessage(e.getMessage());
        }
        return resultBean;
    }
}
