package com.eve.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.atlassian.activeobjects.external.ActiveObjects;
import com.atlassian.crowd.embedded.api.User;
import com.atlassian.jira.component.ComponentAccessor;
import com.atlassian.jira.health.checks.database.LockedDatabaseOfBizDelegator;
import com.atlassian.jira.ofbiz.DefaultOfBizDelegator;
import com.atlassian.jira.ofbiz.OfBizDelegator;
import com.atlassian.jira.ofbiz.WrappingOfBizDelegator;
import com.atlassian.jira.user.ApplicationUser;
import com.eve.ao.CostCenterAo;
import com.eve.ao.InnerOrderTypeAo;
import com.eve.beans.CostCenterBean;
import com.eve.beans.InnerOrderTypeBean;
import com.eve.beans.ResultBean;
import com.eve.utils.Constant;
import com.eve.utils.HttpUtils;
import com.eve.utils.Utils;
import org.ofbiz.core.entity.GenericDelegator;
import org.ofbiz.core.entity.GenericEntityException;
import org.ofbiz.core.entity.GenericValue;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.http.*;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/5/31
 */
public class SapCreateProjectService {
    private static final Logger log = LoggerFactory.getLogger(SapCreateProjectService.class);
    private ActiveObjects ao;
    private OfBizDelegator ofBizDelegator;

    public SapCreateProjectService(ActiveObjects ao, OfBizDelegator ofBizDelegator) {
        this.ao = ao;
        this.ofBizDelegator = ofBizDelegator;
    }

    public ResultBean updateCostCenter() {
        ResultBean resultBean = new ResultBean();
        try {
            //调用成本中心获取接口，将数据写入AO表
            CostCenterAo[] costCenterAos = ao.find(CostCenterAo.class, "1=1");
//            List<CostCenterAo> costCenterAoList = Arrays.stream(costCenterAos).filter(e -> e.isDisableFlag() == null).collect(Collectors.toList());

            String url = Constant.getCostCenterInterface + "?bukrs=ALL";
            Map<String, String> headers = new HashMap<>();
            headers.put("appKey", Constant.sapAppKey);
            headers.put("Content-Type", MediaType.APPLICATION_JSON_UTF8_VALUE);
            String callback = HttpUtils.doGet(url, headers);
            log.error("sap成本中心获取接口响应：" + callback);
            JSONObject costCenterFromSapJson = JSON.parseObject(callback);
            JSONArray rowsData= (JSONArray) costCenterFromSapJson.get("rows");
            List list = JSON.parseObject(JSON.toJSON(rowsData).toString(), List.class);
            List<CostCenterBean> costCenterBeanList = new ArrayList<>();
            for(Object object :list) {
                //将每个Object解析成对象
                CostCenterBean costCenterBean = JSON.parseObject(object.toString(), CostCenterBean.class);
                costCenterBeanList.add(costCenterBean);
//                System.out.println(costCenterBean.toString());
            }
            if (!costCenterBeanList.isEmpty()) {
                for (CostCenterAo costCenterAo : costCenterAos) {
                    costCenterAo.setDisableFlag(true);
                    costCenterAo.save();
                }
//                for (CostCenterAo costCenterAo : costCenterAos) {
//                    List<CostCenterBean> costCenterBeanList1 = costCenterBeanList.stream().filter(e -> e.getKOSTL().equals(costCenterAo.getKOSTL())).collect(Collectors.toList());
//                    if (costCenterBeanList1 == null || costCenterBeanList1.isEmpty()) {
//
//                    } else {
//
//                    }
//                }
//                for (CostCenterBean costCenterBean : costCenterBeanList) {
//                    List<CostCenterAo> costCenterAoList = Arrays.stream(costCenterAos).filter(e -> e.getKOSTL().equals(costCenterBean.getKOSTL())).collect(Collectors.toList());
//                    if (costCenterAoList == null || costCenterAoList.isEmpty()) {
//
//                    } else {
//
//                    }
//                }
//                ao.delete(costCenterAos);
                for (CostCenterBean costCenterBean : costCenterBeanList) {
                    List<CostCenterAo> costCenterAoList = Arrays.stream(costCenterAos).filter(e -> e.getKOSTL().equals(costCenterBean.getKOSTL())).collect(Collectors.toList());
                    CostCenterAo costCenterAo;
                    if (costCenterAoList.isEmpty()) {
                        costCenterAo = ao.create(CostCenterAo.class);
                    } else {
                        //成本中心已存在，保留原ID，更新信息
                        costCenterAo = costCenterAoList.get(0);
                    }
                    BeanUtils.copyProperties(costCenterBean, costCenterAo, "id","activeInProjectFlag");
                    costCenterAo.save();
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            resultBean.setMessage(e.getMessage());
        }
        return resultBean;
    }

    public ResultBean listCostCenter(int curNum,String costCenterName) {
        ResultBean resultBean = new ResultBean();
        int offset = (curNum) * 10;
        List<CostCenterBean> costCenterBeanList = new ArrayList<>();
        try {
//            ApplicationUser applicationUser = ComponentAccessor.getUserManager().getUserByName("083779");
//            String firstAndLastName = getFirstAndLastName(applicationUser);
//            String test = test();
            CostCenterAo[] costCenterAos;
            int count = 0;
            if (ObjectUtils.isEmpty(costCenterName)) {
                costCenterAos = ao.find(CostCenterAo.class, " 1=1 ORDER BY ID ASC limit " + offset + ",10");
                count = ao.count(CostCenterAo.class);
            } else {
                costCenterAos = ao.find(CostCenterAo.class, " KTEXT LIKE '%" + costCenterName + "%' ORDER BY ID ASC limit " + offset + ",10");
                count = ao.count(CostCenterAo.class," KTEXT LIKE '%" + costCenterName + "%' ");
            }
            for (CostCenterAo costCenterAo : costCenterAos) {
                CostCenterBean costCenterBean = new CostCenterBean();
                BeanUtils.copyProperties(costCenterAo, costCenterBean);
                costCenterBeanList.add(costCenterBean);
            }

            resultBean.setValue(costCenterBeanList);
            resultBean.setPageNum(count);
            resultBean.setCur(curNum);
        } catch (Exception e) {
            e.printStackTrace();
            resultBean.setMessage(e.getMessage());
            return resultBean;
        }
//        return costCenterBeanList.subList(0, 10);
        return resultBean;
    }
    public List<CostCenterBean> listCostCenter() {
        List<CostCenterBean> costCenterBeanList = new ArrayList<>();
        try {

            CostCenterAo[] costCenterAos = ao.find(CostCenterAo.class, " KTEXT NOT LIKE '%禁用%' AND (KTEXT like '%动力电池%' OR KTEXT like '%小圆柱电池研究所%') ORDER BY ID ASC");
            for (CostCenterAo costCenterAo : costCenterAos) {
                CostCenterBean costCenterBean = new CostCenterBean();
                BeanUtils.copyProperties(costCenterAo, costCenterBean);
                costCenterBeanList.add(costCenterBean);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return costCenterBeanList;
        }
        return costCenterBeanList;
    }

    public ResultBean updateOrderType(InnerOrderTypeBean innerOrderTypeBean) {
        ResultBean resultBean = new ResultBean();
        try {
            Long id = innerOrderTypeBean.getId();
            if (id == null || id == 0L) {
                InnerOrderTypeAo[] innerOrderTypeAos = ao.find(InnerOrderTypeAo.class, String.format("ORDER_TYPE_CODE  = '%s'", innerOrderTypeBean.getOrderTypeCode()));
                if (!(innerOrderTypeAos == null || innerOrderTypeAos.length == 0)) {
                    throw new IllegalArgumentException("该订单类型已存在");
                }
//            ao.deleteWithSQL(InnerOrderTypeAo.class, "id = " + innerOrderTypeBean.getId());
                InnerOrderTypeAo innerOrderTypeAo = ao.create(InnerOrderTypeAo.class);
                BeanUtils.copyProperties(innerOrderTypeBean, innerOrderTypeAo);
                innerOrderTypeAo.save();
                resultBean.setValue(innerOrderTypeAo.getId());
            } else {
                InnerOrderTypeAo innerOrderTypeAo = ao.get(InnerOrderTypeAo.class, id);
                BeanUtils.copyProperties(innerOrderTypeBean, innerOrderTypeAo);
                innerOrderTypeAo.save();
            }

        } catch (Exception e) {
            e.printStackTrace();
            resultBean.setMessage(e.getMessage());
        }
        return resultBean;
    }
    public ResultBean deleteOrderType(Long id) {
        ResultBean resultBean = new ResultBean();
        try {
            InnerOrderTypeAo innerOrderTypeAo = ao.get(InnerOrderTypeAo.class, id);
            if (innerOrderTypeAo == null) {
                throw new IllegalArgumentException("数据不存在，id传递错误");
            }
            ao.delete(innerOrderTypeAo);
//            ao.deleteWithSQL(InnerOrderTypeAo.class, "id = " + id);
            resultBean.setValue("true");
        } catch (Exception e) {
            e.printStackTrace();
            resultBean.setMessage(e.getMessage());
        }
        return resultBean;
    }
    public ResultBean getOrderType(Long id) {
        ResultBean resultBean = new ResultBean();
        InnerOrderTypeBean innerOrderTypeBean = new InnerOrderTypeBean();
        try {
            InnerOrderTypeAo innerOrderTypeAo = ao.get(InnerOrderTypeAo.class, id);
            if (innerOrderTypeAo == null) {
                throw new IllegalArgumentException("数据不存在，id传递错误");
            }
            BeanUtils.copyProperties(innerOrderTypeAo, innerOrderTypeBean);
//            ao.deleteWithSQL(InnerOrderTypeAo.class, "id = " + id);
            resultBean.setValue(innerOrderTypeBean);
        } catch (Exception e) {
            e.printStackTrace();
            resultBean.setMessage(e.getMessage());
        }
        return resultBean;
    }
    public List<InnerOrderTypeBean> listOrderType(String orderTypeName) {
        List<InnerOrderTypeBean> innerOrderTypeBeanList = new ArrayList<>();
        try {
            InnerOrderTypeAo[] innerOrderTypeAos;
            if (orderTypeName == null || "".equals(orderTypeName)) {
                innerOrderTypeAos = ao.find(InnerOrderTypeAo.class);
            } else {
                innerOrderTypeAos = ao.find(InnerOrderTypeAo.class, "ORDER_TYPE_NAME Like '%" + orderTypeName + "%'");
            }
            for (InnerOrderTypeAo innerOrderTypeAo : innerOrderTypeAos) {
                InnerOrderTypeBean innerOrderTypeBean = new InnerOrderTypeBean();
                BeanUtils.copyProperties(innerOrderTypeAo, innerOrderTypeBean);
                innerOrderTypeBeanList.add(innerOrderTypeBean);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return innerOrderTypeBeanList;
    }
    public InnerOrderTypeBean getOrderTypeById(String id) {
        InnerOrderTypeBean innerOrderTypeBean = new InnerOrderTypeBean();
        InnerOrderTypeAo innerOrderTypeAo = ao.get(InnerOrderTypeAo.class, Long.parseLong(id));
        BeanUtils.copyProperties(innerOrderTypeAo, innerOrderTypeBean);
        return innerOrderTypeBean;
    }
    public CostCenterBean getCostCentById(String id) {
        CostCenterBean costCenterBean = new CostCenterBean();
        CostCenterAo costCenterAo = ao.get(CostCenterAo.class, Long.parseLong(id));
        if (costCenterAo == null) {
            CostCenterAo[] costCenterAos = ao.find(CostCenterAo.class, " KOSTL = '" + id + "'");
            if (ObjectUtils.isEmpty(costCenterAos)) {
                return costCenterBean;
            }
            costCenterAo = costCenterAos[0];
        }
        BeanUtils.copyProperties(costCenterAo, costCenterBean);
        return costCenterBean;
    }
    public String getFirstAndLastName(ApplicationUser applicationUser) throws GenericEntityException {
        String name = "";
//        OfBizDelegator ofBizDelegator = new DefaultOfBizDelegator(null);
//        GenericValue cwdUserGV = ofBizDelegator.findById("User", applicationUser.getId());
//        DefaultOfBizDelegator defaultOfBizDelegator = new DefaultOfBizDelegator();

//        GenericDelegator genericDelegator = new GenericDelegator();
//        LockedDatabaseOfBizDelegator lockedDatabaseOfBizDelegator = new LockedDatabaseOfBizDelegator();
//        List<GenericValue> byField = lockedDatabaseOfBizDelegator.findByField("User", "userName", applicationUser.getName());
//        WrappingOfBizDelegator wrappingOfBizDelegator = new WrappingOfBizDelegator();
//        DefaultOfBizDelegator.findByFieldfindByField("User", "userName", applicationUser.getName());
//        genericDelegator.findByAnd("user")
        List<GenericValue> byField = ofBizDelegator.findByField("User", "userName", applicationUser.getName());
        GenericValue cwdUserGV = byField.get(0);


        String lastName = "";
        if (cwdUserGV.get("lastName") != null) {
            lastName = cwdUserGV.get("lastName") + "";
        }
        String firstName = "";
        if (cwdUserGV.get("firstName") != null) {
            firstName = cwdUserGV.get("firstName") + "";
        }

        String userName = lastName + firstName;
        if (!"".equals(userName)) {
            name = userName;
        } else {
            name = applicationUser.getDisplayName();
        }
        return name;
    }

    public String test() throws Exception {
        Map<String, String> header = new HashMap<>();
        Map<String, String> dataMap = new HashMap<>();
        dataMap.put("username", "083779");
        dataMap.put("sys", "OA");
        String password = "OA1234567";
        String keyrules = "OA" + ":" + password + ":" + "083779";
        String backLog = "";
        try {
            dataMap.put("password", Utils.encryptByJira("083779", keyrules));
            String data = JSON.toJSONString(dataMap);

            backLog = HttpUtils.doPostBackLog("http://jira.evebattery.com/rest/sso/1.0/user/createToken", data, header);
        } catch (Exception e) {
            System.err.println(e);
            throw e;
        }

        return backLog;
    }
//    public List<OrderTypeOptionBean> getOrderTypeOptions() {
//        List<OrderTypeOptionBean> orderTypeOptionBeanList = new ArrayList<>();
//
//        return orderTypeOptionBeanList;
//    }

//    public JSONObject doGetSap(String url, Map<String, String> params) {
//        HttpHeaders headers = new HttpHeaders();
//
//        if (params == null) {
//            params = new HashMap<>();
//        }
//        headers.add("appKey", Constant.sapAppKey);
//        headers.add("Content-Type", MediaType.APPLICATION_JSON_UTF8_VALUE);
//
//        ResponseEntity<String> response = restTemplate.exchange(
//                url,
//                HttpMethod.GET,
//                new HttpEntity(headers),
//                String.class,
//                params);
//
//        JSONObject res = JSONObject.parseObject(response.getBody());
//        return res;
//    }

//    public JSONObject doPostSap(String url, Map<String, String> params) {
//
//        SimpleClientHttpRequestFactory requestFactory = new SimpleClientHttpRequestFactory();
//        requestFactory.setConnectTimeout(3000);
//        requestFactory.setReadTimeout(3000);
//
//        HttpHeaders headers = new HttpHeaders();
//        headers.add("appKey", Constant.sapAppKey);
//        headers.add("Content-Type", MediaType.APPLICATION_JSON_UTF8_VALUE);
//
//        HttpEntity<String> formEntity = new HttpEntity<String>(JSONObject.toJSONString(params), headers);
//
//        RestTemplate rest = new RestTemplate();
//
//        ResponseEntity<String> response = rest.exchange(
//                url,
//                HttpMethod.POST,
//                formEntity,
//                String.class);
//
//        JSONObject res = JSONObject.parseObject(response.getBody());
//
//        return res;
//    }
}
