package com.eve.services;

import com.atlassian.activeobjects.external.ActiveObjects;
import com.atlassian.core.util.FileUtils;
import com.atlassian.jira.component.ComponentAccessor;
import com.atlassian.jira.issue.Issue;
import com.atlassian.jira.issue.attachment.Attachment;
import com.atlassian.jira.issue.customfields.CustomFieldType;
import com.atlassian.jira.issue.customfields.option.Option;
import com.atlassian.jira.issue.fields.CustomField;
import com.atlassian.jira.ofbiz.OfBizDelegator;
import com.atlassian.jira.project.Project;
import com.atlassian.jira.user.ApplicationUser;
import com.atlassian.jira.util.AttachmentUtils;
import com.eve.beans.OptionBean;
import com.eve.beans.ResultBean;

import java.io.File;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


public class ServiceUpdateDue {




    private static SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");



    public ResultBean queryFieldList(String fieldType) {
        ResultBean resultBean = new ResultBean();
        List<OptionBean> optionBeanList = new ArrayList<>();
        try {

            // 获取所有的自定义字段
            List<CustomField> customFieldList = ComponentAccessor.getCustomFieldManager().getCustomFieldObjects();
            // 获取指定的自定义字段的类型
            CustomFieldType customFieldType = ComponentAccessor.getCustomFieldManager().getCustomFieldType(fieldType);
            for (CustomField custom : customFieldList) {
                if (custom.getCustomFieldType().getKey().equals(customFieldType.getKey())) {
                    OptionBean optionCustomBean = new OptionBean();
                    optionCustomBean.setId(custom.getIdAsLong());
                    optionCustomBean.setName(custom.getFieldName());
                    optionBeanList.add(optionCustomBean);
                }
            }

            resultBean.setValue(optionBeanList);
        } catch (Exception e) {
            e.printStackTrace();
            resultBean.setMessage(e.getMessage());
        }
        return resultBean;
    }

    /**
     * 查询特殊签字类型列表
     *
     * @return
     */
    public List<OptionBean> queryOptionBeanList() {
        // 添加人员类型选项
        List<OptionBean> optionBeanList = new ArrayList<>();
        OptionBean optionBean = new OptionBean();
        optionBean.setValue("-1");
        optionBean.setName("请选择");

        OptionBean optionBean1 = new OptionBean();
        optionBean1.setValue("com.atlassian.jira.plugin.system.customfieldtypes:datetime");
        optionBean1.setName("日期时间");

        OptionBean optionBean2 = new OptionBean();
        optionBean2.setValue("com.atlassian.jira.plugin.system.customfieldtypes:datepicker");
        optionBean2.setName("日期");



        optionBeanList.add(optionBean);
        optionBeanList.add(optionBean1);
        optionBeanList.add(optionBean2);

        return optionBeanList;
    }



    public String getCustomFieldValue(Issue issue,CustomField customField){
        String customFieldValue = "";
        Object object = issue.getCustomFieldValue(customField);
        if (object instanceof String){
            customFieldValue = (String) object;
        }else if (object instanceof Integer){
            customFieldValue = String.valueOf(object);
        }else if (object instanceof Option){
            Option option = (Option) object;
            customFieldValue = option.getValue();
        }else if (object instanceof Date){
            Date date = new Date();
            customFieldValue = sdf.format(date);
        }else if (object instanceof Project){
            Project project = (Project) object;
            customFieldValue = project.getName();
        }else if (object instanceof ApplicationUser){
            ApplicationUser user = (ApplicationUser) object;
            customFieldValue = user.getDisplayName();
        }else if (object instanceof Float){
            customFieldValue = String.valueOf(object);
        }else if (object instanceof Double){
            BigDecimal bg = BigDecimal.valueOf((Double) object).setScale(2, RoundingMode.UP);
            double num = bg.doubleValue();
            if (Math.round(num) - num == 0) {
                customFieldValue = String.valueOf((long) num);
            }else {
                customFieldValue = String.valueOf(object);
            }
        }
        return customFieldValue;
    }

}
