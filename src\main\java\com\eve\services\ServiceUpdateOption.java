package com.eve.services;

import cn.hutool.core.util.ArrayUtil;
import com.atlassian.jira.component.ComponentAccessor;
import com.atlassian.jira.issue.Issue;
import com.atlassian.jira.issue.context.JiraContextNode;
import com.atlassian.jira.issue.context.ProjectContext;
import com.atlassian.jira.issue.customfields.CustomFieldType;
import com.atlassian.jira.issue.customfields.option.Option;
import com.atlassian.jira.issue.customfields.option.Options;
import com.atlassian.jira.issue.fields.CustomField;
import com.atlassian.jira.issue.fields.config.FieldConfigScheme;
import com.atlassian.jira.project.Project;
import com.atlassian.jira.user.ApplicationUser;
import com.eve.beans.OptionBean;
import com.eve.beans.OptionsValueBean;
import com.eve.beans.ResultBean;
import com.eve.utils.Utils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


public class ServiceUpdateOption {

    private static final Logger log = LoggerFactory.getLogger(ServiceUpdateOption.class);

    private static SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

    public static ResultBean queryFieldList(String fieldType) {
        ResultBean resultBean = new ResultBean();
        List<OptionBean> optionBeanList = new ArrayList<>();
        try {
            // 获取所有的自定义字段
            List<CustomField> customFieldList = ComponentAccessor.getCustomFieldManager().getCustomFieldObjects();

            // 获取指定的自定义字段的类型
            CustomFieldType customFieldType = ComponentAccessor.getCustomFieldManager().getCustomFieldType(fieldType);
            for (CustomField custom : customFieldList) {
                if (custom.getCustomFieldType().getKey().equals(customFieldType.getKey())) {
                    OptionBean optionCustomBean = new OptionBean();
                    optionCustomBean.setId(custom.getIdAsLong());
                    optionCustomBean.setName(custom.getFieldName());
                    optionBeanList.add(optionCustomBean);
                }
            }

            resultBean.setValue(optionBeanList);
        } catch (Exception e) {
            log.error(Utils.errInfo(e));
            resultBean.setMessage(e.getMessage());
        }
        return resultBean;
    }

    public static ResultBean queryOptionsList(Long field_field) {
        ResultBean resultBean = new ResultBean();
//        List<OptionBean> optionBeanList = new ArrayList<>();
        try {
            // 获取所有的自定义字段
            CustomField customField = Utils.getCustomFieldByID(field_field);
            List<OptionsValueBean> optionsValue = new ArrayList<>();
            List<FieldConfigScheme> configurationSchemes = customField.getConfigurationSchemes();
            for (FieldConfigScheme configurationScheme : configurationSchemes) {
                String configurationSchemeName = configurationScheme.getName();
                List<JiraContextNode> jiraContextNodeList = configurationScheme.getContexts();
                JiraContextNode jiraContextNode = ArrayUtil.isEmpty(jiraContextNodeList)
                        ? new ProjectContext(null, ComponentAccessor.getProjectManager())
                        : jiraContextNodeList.get(0);
                Options options = customField.getOptions("", jiraContextNode);
                for (Option option : options) {
                    Boolean optionDisabled = option.getDisabled();
                    optionsValue.add(new OptionsValueBean(option.getValue() + optionDisabled + configurationSchemeName, option.getOptionId()));
                    List<Option> childOptions = option.getChildOptions();
                    if (!ObjectUtils.isEmpty(childOptions)) {
                        for (Option childOption : childOptions) {
                            Boolean childOptionDisabled = childOption.getDisabled();
                            optionsValue.add(new OptionsValueBean(childOption.getValue() + childOptionDisabled + configurationSchemeName, childOption.getOptionId()));
                        }
                    }
                }
            }
            resultBean.setValue(optionsValue);
        } catch (Exception e) {
            log.error(Utils.errInfo(e));
            resultBean.setMessage(e.getMessage());
        }
        return resultBean;
    }

    public List<OptionBean> queryOptionBeanList() {
        // 查询出单选字段值

//        OptionBean optionBean1 = new OptionBean();
//        optionBean1.setValue("com.atlassian.jira.plugin.system.customfieldtypes:textfield");
//        optionBean1.setName("单行文本");
        List<OptionBean> optionBeanList = new ArrayList<>();
        OptionBean optionBean = new OptionBean();
        optionBean.setValue("-1");
        optionBean.setName("请选择");
        OptionBean optionBean2 = new OptionBean();
        optionBean2.setValue("com.atlassian.jira.plugin.system.customfieldtypes:select");
        optionBean2.setName("下拉单选");


//        OptionBean optionBean3 = new OptionBean();
//        optionBean3.setValue("com.atlassian.jira.plugin.system.customfieldtypes:datetime");
//        optionBean3.setName("日期");
//
//        OptionBean optionBean4 = new OptionBean();
//        optionBean4.setValue("com.atlassian.jira.plugin.system.customfieldtypes:userpicker");
//        optionBean4.setName("用户");
//
//        OptionBean optionBean5 = new OptionBean();
//        optionBean5.setValue("com.atlassian.jira.plugin.system.customfieldtypes:project");
//        optionBean5.setName("项目");
//
//        OptionBean optionBean6 = new OptionBean();
//        optionBean6.setValue("com.atlassian.jira.plugin.system.customfieldtypes:textarea");
//        optionBean6.setName("文本域");
//
//        OptionBean optionBean7 = new OptionBean();
//        optionBean7.setValue("com.atlassian.jira.plugin.system.customfieldtypes:float");
//        optionBean7.setName("数值域");

          optionBeanList.add(optionBean);
//        optionBeanList.add(optionBean1);
          optionBeanList.add(optionBean2);
//        optionBeanList.add(optionBean3);
//        optionBeanList.add(optionBean4);
//        optionBeanList.add(optionBean5);
//        optionBeanList.add(optionBean6);
//        optionBeanList.add(optionBean7);

        return optionBeanList;
    }

    public String getCustomFieldValue(Issue issue, CustomField customField) {
        String customFieldValue = "";
        Object object = issue.getCustomFieldValue(customField);
        if (object instanceof String) {
            customFieldValue = (String) object;
        } else if (object instanceof Integer) {
            customFieldValue = String.valueOf(object);
        } else if (object instanceof Option) {
            Option option = (Option) object;
            customFieldValue = option.getValue();
        } else if (object instanceof Date) {
            Date date = new Date();
            customFieldValue = sdf.format(date);
        } else if (object instanceof Project) {
            Project project = (Project) object;
            customFieldValue = project.getName();
        } else if (object instanceof ApplicationUser) {
            ApplicationUser user = (ApplicationUser) object;
            customFieldValue = user.getDisplayName();
        } else if (object instanceof Float) {
            customFieldValue = String.valueOf(object);
        } else if (object instanceof Double) {
            BigDecimal bg = BigDecimal.valueOf((Double) object).setScale(2, RoundingMode.UP);
            double num = bg.doubleValue();
            if (Math.round(num) - num == 0) {
                customFieldValue = String.valueOf((long) num);
            } else {
                customFieldValue = String.valueOf(object);
            }
        }
        return customFieldValue;
    }
}
