package com.eve.services;

import com.atlassian.jira.component.ComponentAccessor;
import com.atlassian.jira.issue.MutableIssue;
import com.atlassian.jira.issue.changehistory.ChangeHistory;
import com.atlassian.jira.issue.changehistory.ChangeHistoryManager;
import com.atlassian.jira.issue.history.ChangeItemBean;
import com.atlassian.jira.user.ApplicationUser;
import com.eve.beans.ResultBean;
import com.eve.beans.ReviewCodeBean;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/10/26
 */
public class ShowApprovalNodeService {
    private static final Logger log = LoggerFactory.getLogger(SapCreateProjectService.class);


    public ResultBean getNodeMsg(Long issueId) {
        ResultBean resultBean = new ResultBean();
        try {
            MutableIssue mutableIssue = ComponentAccessor.getIssueManager().getIssueObject(issueId);
            ChangeHistoryManager changeHistoryManager = ComponentAccessor.getChangeHistoryManager();
            List<ChangeHistory> changeHistories = changeHistoryManager.getChangeHistories(mutableIssue);
            List<ChangeItemBean> statusChangeItemBeanList = new ArrayList<>();
            List<ReviewCodeBean> reviewCodeBeanList = new ArrayList<>();
            for (ChangeHistory changeHistory : changeHistories) {
                ApplicationUser authorObject = changeHistory.getAuthorObject();
                ApplicationUser toUser = null;
                ApplicationUser fromUser = null;
                List<ChangeItemBean> changeItemBeans = changeHistory.getChangeItemBeans();
                for (ChangeItemBean changeItemBean : changeItemBeans) {
                    if ("status".equals(changeItemBean.getField())) {
                        statusChangeItemBeanList.add(changeItemBean);
                        ChangeItemBean assigneeChangeItemBean;
                        List<ChangeItemBean> collect = changeItemBeans.stream().filter(e -> "assignee".equals(e.getField())).collect(Collectors.toList());
                        if (!ObjectUtils.isEmpty(collect)) {
                            assigneeChangeItemBean = collect.get(0);
                            String from = assigneeChangeItemBean.getFrom();
                            String to = assigneeChangeItemBean.getTo();
                            toUser = ComponentAccessor.getUserManager().getUserByKey(to);
                            fromUser = ComponentAccessor.getUserManager().getUserByKey(from);
                            reviewCodeBeanList.add(new ReviewCodeBean(
                                    authorObject.getUsername(),
                                    authorObject.getDisplayName(),
                                    fromUser==null?"":fromUser.getUsername(),
                                    fromUser==null?"":fromUser.getDisplayName(),
                                    toUser==null?"":toUser.getUsername(),
                                    toUser==null?"":toUser.getDisplayName(),
                                    "",
                                    changeItemBean));
                        }
                    }

                }
            }
            statusChangeItemBeanList = statusChangeItemBeanList.stream().sorted(Comparator.comparing(ChangeItemBean::getCreated).reversed()).collect(Collectors.toList());
            resultBean.setValue(reviewCodeBeanList);
        } catch (Exception e) {
            e.printStackTrace();
            resultBean.setMessage(e.getMessage());
        }
        return null;
    }

}
