package com.eve.services;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.atlassian.activeobjects.external.ActiveObjects;
import com.atlassian.crowd.embedded.api.Group;
import com.atlassian.jira.bc.issue.IssueService;
import com.atlassian.jira.component.ComponentAccessor;
import com.atlassian.jira.config.IssueTypeManager;
import com.atlassian.jira.datetime.DateTimeFormatter;
import com.atlassian.jira.datetime.DateTimeStyle;
import com.atlassian.jira.event.type.EventDispatchOption;
import com.atlassian.jira.exception.CreateException;
import com.atlassian.jira.issue.*;
import com.atlassian.jira.issue.attachment.Attachment;
import com.atlassian.jira.issue.attachment.CreateAttachmentParamsBean;
import com.atlassian.jira.issue.context.JiraContextNode;
import com.atlassian.jira.issue.context.ProjectContext;
import com.atlassian.jira.issue.customfields.manager.OptionsManager;
import com.atlassian.jira.issue.customfields.option.Option;
import com.atlassian.jira.issue.customfields.option.Options;
import com.atlassian.jira.issue.fields.CustomField;
import com.atlassian.jira.issue.history.ChangeItemBean;
import com.atlassian.jira.issue.issuetype.IssueType;
import com.atlassian.jira.project.Project;
import com.atlassian.jira.security.JiraAuthenticationContext;
import com.atlassian.jira.user.ApplicationUser;
import com.atlassian.jira.user.util.UserManager;
import com.eve.ao.TripRequestMsgAo;
import com.eve.beans.*;
import com.eve.utils.Constant;
import com.eve.utils.Utils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.io.File;
import java.nio.file.Files;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/9/13
 */
public class TestFailureService {
    private static final Logger log = LoggerFactory.getLogger(TestFailureService.class);
    private ActiveObjects ao;
    @Resource
    private DeptProjectService deptProjectService;
    @Resource
    private DeptsService deptsService;
    @Resource
    private CustomFieldManager customFieldManager;
    @Resource
    private IssueManager issueManager;
    @Resource
    private OptionsManager optionsManager;
    @Resource
    private UserManager userManager;
//    @Resource
//    private IssueTypeManager issueTypeManager;
    @Resource
    private AttachmentManager attachmentManager;
    @Resource
    private CustomToolService customToolService;

    public TestFailureService(ActiveObjects ao) {
        this.ao = ao;
    }


    public ResultBean createTestFailure(int isOnline, UpdateCustomFiledBean updateCustomFiledBean) {
        ResultBean resultBean = new ResultBean();
        String issueTypeId = updateCustomFiledBean.getIssueType();
        Map<String, String> map = updateCustomFiledBean.getMap();
        String userName = updateCustomFiledBean.getUserName();
        MutableIssue mutableIssue = null;
        ApplicationUser applicationUser = ComponentAccessor.getUserManager().getUserByName(userName);
        Assert.notNull(applicationUser, "工号传递错误，jira未找到用户：" + userName);
//        String userName = map.getOrDefault("projectLeaderId", "");
        try {
//        map.put("topicLeader",jiraCustomTool.getFirstAndLastName(applicationUser));//报告人设置到课题负责人
//            Map<Long, Long> projectLevelMap = Utils.getProjectLevelMap();
            //模拟用户登录
            JiraAuthenticationContext context = ComponentAccessor.getComponent(JiraAuthenticationContext.class);
            context.setLoggedInUser(applicationUser);
            log.error("开始创建测试失效问题，参数：" + JSONObject.toJSONString(updateCustomFiledBean));
            //获取issue的服务
            com.atlassian.jira.bc.issue.IssueService issueService = ComponentAccessor.getIssueService();
            //创建issue参数
            IssueInputParameters issueInputParameters = issueService.newIssueInputParameters();
            // 设置项目ID
            issueInputParameters.setProjectId(isOnline == 1 ? Constant.testFailureProjectId : Constant.testFailureProjectIdTest);

//            List<Long> lfpDepartmentIdList = Arrays.asList(18711L, 22269L, 18854L, 18876L);//18711L-动力电池研究所、22269L-储能电池研究所、18854L-基础技术研究所、18876L-工程研究所
            //所属部门 in cascadeOption(18711) OR 所属部门 in cascadeOption(22269) OR 所属部门 in cascadeOption(18854) OR 所属部门 in cascadeOption(18876)
//        if (1 == isOnline) {
//            Long departmentCateId = Long.parseLong(map.get("departmentCate"));
//            Option option = ComponentAccessor.getOptionsManager().findByOptionId(departmentCateId);
//            if (ObjectUtils.isEmpty(option)){
//                throw new IllegalArgumentException("根据选项id未找到选项：departmentCate");
//            }
//            Option parentOption = option.getParentOption();
//            //18711L-方形、18876L-工程，创建到荆门LFP项目。创建到惠州项目
//            issueInputParameters.setProjectId(
//                    ((parentOption != null) ? lfpDepartmentIdList.contains(parentOption.getOptionId()) : lfpDepartmentIdList.contains(departmentCateId))
//                            ? Constant.lfpProjectManageProjectId
//                            : Constant.projectManageProjectId);
//        } else {
//            issueInputParameters.setProjectId(Constant.projectManageTestProjectId);//测试项目id
//        }

            //设置问题类型
            issueInputParameters.setIssueTypeId(issueTypeId);
            //设置概要
            issueInputParameters.setSummary(map.getOrDefault("fileCode", "/") + "-DPV测试失效");


            // 设置报告人
            issueInputParameters.setReporterId(applicationUser.getKey());
            //设置字段值
            for (Map.Entry<String, String> entry : map.entrySet()) {
                String entryKey = entry.getKey();
                String entryValue = entry.getValue();
                Long customFiledId = NumberUtil.isNumber(entryKey) ? Long.parseLong(entryKey) : Utils.getReviewListCustomFiledMap().getOrDefault(entryKey, 0L);
                CustomField customFiled = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(customFiledId);
                if (customFiled == null || entryValue == null || "".equals(entryValue)) {
                    if ("summary".equals(entryKey)) {
//                        IssueType issueType = issueTypeManager.getIssueType(issueTypeId);
//                        issueInputParameters.setSummary(entryValue + "-" + issueType.getName());
                        issueInputParameters.setSummary(entryValue);
                    } else if ("priority".equals(entryKey)) {
                        issueInputParameters.setPriorityId(entryValue);
                    } else if ("description".equals(entryKey)) {
                        issueInputParameters.setDescription(entryValue);
                    }
                    continue;
                }

                String customFieldTypeKey = customFiled.getCustomFieldType().getKey();
                switch (customFieldTypeKey) {
                    case "com.atlassian.jira.plugin.system.customfieldtypes:select":
//                    if (NumberUtil.isNumber(entryValue)){
//                        Option selectOption = optionsManager.findByOptionId(Long.parseLong(entryValue));
//                        selectOption = selectOption == null ? optionsManager.findByOptionId(Utils.getSelectOptionId(entryKey, Long.parseLong(entryValue))) : selectOption;
//                        if (selectOption == null || Boolean.FALSE.equals(selectOption.getDisabled())) {
//                            mutableIssue.setCustomFieldValue(customFiled, selectOption);
//                            issueInputParameters.addCustomFieldValue(customFiledId, selectOption.getOptionId().toString());
//                        }
//                    }
                        if (NumberUtil.isNumber(entryValue)){
                            issueInputParameters.addCustomFieldValue(customFiledId, entryValue);
                        } else {
                            JiraContextNode jiraContextNode = new ProjectContext(Constant.testFailureProjectId, ComponentAccessor.getProjectManager());
                            Options options = customFiled.getOptions("", jiraContextNode);
                            for (Option option : options) {
                                if (entryValue.equals(option.getValue())) {
                                    issueInputParameters.addCustomFieldValue(customFiledId, option.getOptionId().toString());
//                                    mutableIssue.setCustomFieldValue(customFiled, option);
                                }
                            }
                        }

                        break;
                    case "com.atlassian.jira.plugin.system.customfieldtypes:cascadingselect":
                        Option option = ComponentAccessor.getOptionsManager().findByOptionId(Long.parseLong(entryValue));
                        if (ObjectUtils.isEmpty(option)) {
                            throw new IllegalArgumentException("根据选项id未找到选项：" + entryKey);
                        }
                        Option parentOption = option.getParentOption();
                        if (ObjectUtils.isEmpty(parentOption)) {
                            issueInputParameters.addCustomFieldValue(
                                    customFiledId,
                                    String.valueOf(option.getOptionId())
                            );
                        } else {
                            issueInputParameters.addCustomFieldValue(
                                    customFiledId,
                                    String.valueOf(parentOption.getOptionId())
                            );
                            issueInputParameters.addCustomFieldValue(
                                    "customfield_" + customFiledId + ":1",
                                    String.valueOf(option.getOptionId())
                            );
                        }
                        break;
                    case "com.atlassian.jira.plugin.system.customfieldtypes:userpicker":
                        ApplicationUser user = ComponentAccessor.getUserManager().getUserByName(entryValue);
                        if (user == null) {
                            throw new IllegalArgumentException("用户工号传递错误，jira未找到用户：" + entryValue);
                        }
                        issueInputParameters.addCustomFieldValue(customFiledId, entryValue);
                        break;
                    case "com.atlassian.jira.plugin.system.customfieldtypes:multiuserpicker":
//                        List<String> applicationUserNameList = Convert.toList(String.class, entryValue);
//                        List<ApplicationUser> applicationUserList = applicationUserNameList.stream().map(e -> userManager.getUserByName(e)).collect(Collectors.toList());
                        issueInputParameters.addCustomFieldValue(customFiledId, entryValue);
                        break;
                    case "com.atlassian.jira.plugin.system.customfieldtypes:datepicker":
                    case "com.atlassian.jira.plugin.system.customfieldtypes:datetime":
                        Date date = NumberUtil.isNumber(entryValue) ? new Date(Long.parseLong(entryValue)) : java.sql.Date.valueOf(entryValue);
                        DateTimeFormatter dateFormatter = ComponentAccessor.getComponent(DateTimeFormatter.class);
                        dateFormatter = dateFormatter.forLoggedInUser();
                        String dateStr = dateFormatter.withStyle(DateTimeStyle.DATE_PICKER).format(date);
                        issueInputParameters.addCustomFieldValue(customFiledId, dateStr);
                        break;
                    case "com.atlassian.jira.plugin.system.customfieldtypes:textfield":
                    case "com.atlassian.jira.plugin.system.customfieldtypes:textarea":
                    case "com.atlassian.jira.plugin.system.customfieldtypes:float":
                        issueInputParameters.addCustomFieldValue(customFiledId, entryValue);
                        break;
                    default:
                        throw new IllegalStateException("Unexpected customFieldTypeKey: " + customFieldTypeKey);
                }
            }
            //创建issue参数校验
            com.atlassian.jira.bc.issue.IssueService.CreateValidationResult createValidationResult = issueService.validateCreate(applicationUser, issueInputParameters);

            if (!createValidationResult.isValid()) {
                throw new CreateException("创建问题参数校验失败：" + createValidationResult.getErrorCollection());
            }
            //创建issue
            IssueService.IssueResult createResult = issueService.create(applicationUser, createValidationResult);
            if (!createResult.isValid()) {
                throw new CreateException("创建问题失败：" + createResult.getErrorCollection().getErrorMessages());
            }
            mutableIssue = createResult.getIssue();
            updateCustomFiledBean.setIssueId(mutableIssue.getId());
            updateTestFailure(isOnline, updateCustomFiledBean, true);
//            Long issueId = updateCustomFiledBean.getIssueId();
//            MutableIssue mutableIssue = issueManager.getIssueObject(issueId);
            if (mutableIssue.getAssignee() == null) {
                //设置经办人
                ApplicationUser applicationUser1 = null;
                if (Constant.DPVTestFailureRecordIssueTypeId.toString().equals(issueTypeId)) {
//                    applicationUser1 = userManager.getUserByName(map.get("departmentManager"));
                }else if (Constant.failureCellInStoreIssueTypeId.toString().equals(issueTypeId)) {
                    applicationUser1 = userManager.getUserByName(map.get("laboratoryResponsible"));
//                    log.error("创建入库流程设置经办人：{}", map.get("laboratoryResponsible"));
//                }else if (Constant.failureCellOutStoreIssueTypeId.toString().equals(issueTypeId)) {
//                    applicationUser1 = userManager.getUserByName(map.get("headOfTheInstitute"));
                }else if (Constant.FAResponsibleAssignIssueTypeId.toString().equals(issueTypeId)) {
                    applicationUser1 = userManager.getUserByName(map.get("headOfTheInstitute"));
                }else if (Constant.FAAnalyseReportIssueTypeId.toString().equals(issueTypeId)) {
                    applicationUser1 = userManager.getUserByName(map.get("faChargeAccount"));
                }
                if (applicationUser1 != null) {
                    mutableIssue.setAssignee(applicationUser1);
                    ComponentAccessor.getIssueManager().updateIssue(applicationUser, mutableIssue, EventDispatchOption.ISSUE_UPDATED, false);
                }
            }

            //文件
            try {
                String failureDescriptionPicture = map.get("failureDescriptionPicture");
                /*
                    return {
                      uid: file.uid,                            // 使用组件生成的唯一标识
                      id: fileId,                //文件id
                      name: file.name,                          // 文件名
                      size: rawFile ? rawFile.size : file.size, // 优先从原始文件获取大小
                      type: rawFile ? rawFile.type : file.type, // 文件类型
                      url: '/api/sysFileInfo/preview?Authorization=Bearer ' + Vue.ls.get('Access-Token') + '&id=' + fileId, // 预览地址
                      status: file.status, // 状态
                    };
                 */
                if (failureDescriptionPicture != null) {
                    List<FailureDescriptionPictureBean> pictureBeanList = JSONObject.parseArray(failureDescriptionPicture, FailureDescriptionPictureBean.class);

                    List<AttachmentBean> attachmentBeanList = pictureBeanList.stream().map(e -> new AttachmentBean.Builder().attachmentId(e.getId()).attachmentName(e.getName()).build()).collect(Collectors.toList());
                    attachmentBeanList.forEach(attachmentBean->{
                        attachmentBean.setAttachmentUrl((isOnline == 1 ? Constant.pbiUrl : Constant.pbiUrlTest)+"/sysFileInfo/preview?id=" + attachmentBean.getAttachmentId());
                        attachmentBean.setAttachmentPath(Utils.getTestFailureFilePath() + "fileTemp" + File.separator);
                        attachmentBean.setAttachmentCate("失效描述图片");
                    });
                    customToolService.addAttachmentToIssueAndMove(mutableIssue,applicationUser,attachmentBeanList);
                }

                //mutableIssue、applicationUser、fileUrl、fileName、cateName
                String fileId = map.get("fileId");
                if (fileId != null) {
                    //文件附加到ISSUE上面
                    //下载文件
                    List<AttachmentBean> attachmentBeanList = new ArrayList<>();
                    attachmentBeanList.add(new AttachmentBean.Builder()
                            .attachmentUrl((isOnline == 1 ? Constant.pbiUrl : Constant.pbiUrlTest)+"/sysFileInfo/preview?id=" + fileId)
                            .attachmentName(map.get("fileName"))
                            .attachmentPath(Utils.getTestFailureFilePath() + "fileTemp" + File.separator)
                            .attachmentCate("失效告知书")
                            .build());
                    customToolService.addAttachmentToIssueAndMove(mutableIssue,applicationUser,attachmentBeanList);
//                    extracted(mutableIssue, applicationUser, fileUrl, fileName, cateName, savePath);
                }

            } catch (Exception e){
                log.error("测试失效文件上传失败：{}", Utils.errInfo(e));
            }
            resultBean.setValue(mutableIssue.getKey());
        } catch (Exception e) {
            log.error("创建测试失效失败：{}", Utils.errInfo(e));
            resultBean.setMessage("创建测试失效失败：" + e.getMessage());
        } finally {


        }
        return resultBean;
    }

    private void extracted(MutableIssue mutableIssue, ApplicationUser applicationUser, String fileUrl, String fileName, String cateName, String savePath) throws Exception {

        log.debug("即将下载文件：fileUrl=" + fileUrl + " savePath=" + savePath + fileName);
        String filePath = Utils.downloadHttpUrl(fileUrl, savePath, fileName);
        File file = new File(filePath);
        if (!file.exists()) {
            throw new IllegalStateException("未找到文件: " + filePath);
        }

        String contentType = Files.probeContentType(file.toPath());

        //附加附件到issue，创建附件同时删除源文件 Map<String, Object> attachmentProperties
        CreateAttachmentParamsBean createAttachmentParamsBean =
                new CreateAttachmentParamsBean(file, fileName, contentType, applicationUser, mutableIssue, false, false, null, new Date(), false);
        ChangeItemBean attachment = attachmentManager.createAttachment(createAttachmentParamsBean);
        log.debug("文件上传成功，ChangeItemBean结果：" + JSONObject.toJSONString(attachment));
        if (ObjectUtil.isNotEmpty(cateName)) {
            //移动附件到指定分类下  获取issue下附件列表、获取附件分类，存在匹配的分类，转移到该分类下
            Double categoriesId = null;
            //智能附件 分类Map获取，获取附件分类ID
            List<Map<String, Object>> attachmentCategoriesMapList = Utils.getAttachmentCategoriesMapList(mutableIssue);
            for (Map<String, Object> attachmentMap : attachmentCategoriesMapList) {
                String categoriesName = String.valueOf(attachmentMap.get("name"));
                if (cateName.equals(categoriesName)) {
                    categoriesId = (Double) attachmentMap.get("id");
                    break;
                }
            }
            if (categoriesId != null) {
//                        Utils.moveAttachmentToCategories(mutableIssue.getKey(),categoriesId.longValue(),attachmentList);
                //移动附件API 数据格式: issueKey=CPGL-550&toCatId=68&attachments=81103&attachments=81104
                String data = "issueKey=" + mutableIssue.getKey() + "&toCatId=" + categoriesId + "&attachments="+attachment.getTo();
                Utils.moveAttachmentToCategories(data);
            }
        }
    }

    public ResultBean updateTestFailure(int isOnline, UpdateCustomFiledBean updateCustomFiledBean,Boolean isUpdate){
        ResultBean resultBean = new ResultBean();
        Long issueId = updateCustomFiledBean.getIssueId();
        String userName = updateCustomFiledBean.getUserName();
        ApplicationUser applicationUser = userManager.getUserByName(userName);
        Map<String, String> map = updateCustomFiledBean.getMap();
        try{
            MutableIssue mutableIssue = issueManager.getIssueObject(issueId);
            int updateSuccess = 0;
            for (Map.Entry<String, String> entry : map.entrySet()) {
                String entryKey = entry.getKey();
                String entryValue = entry.getValue();
                Long customFiledId = NumberUtil.isNumber(entryKey) ? Long.parseLong(entryKey) : Utils.getReviewListCustomFiledMap().getOrDefault(entryKey, 0L);
                CustomField customFiled = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(customFiledId);
                if (customFiled == null || entryValue == null || "".equals(entryValue)) {
                    continue;
                }

                String customFieldTypeKey = customFiled.getCustomFieldType().getKey();
                switch (customFieldTypeKey) {
                    case "com.atlassian.jira.plugin.system.customfieldtypes:select":
                        if (NumberUtil.isNumber(entryValue)){
                            Option selectOption = optionsManager.findByOptionId(Long.parseLong(entryValue));
                            selectOption = selectOption == null ? optionsManager.findByOptionId(Utils.getSelectOptionId(entryKey, Long.parseLong(entryValue))) : selectOption;
                            if (selectOption != null && Boolean.FALSE.equals(selectOption.getDisabled())) {
                                mutableIssue.setCustomFieldValue(customFiled, selectOption);
                            }
                        } else {
                            JiraContextNode jiraContextNode = new ProjectContext(Constant.testFailureProjectId, ComponentAccessor.getProjectManager());

                            Options options = customFiled.getOptions("", jiraContextNode);
                            for (Option option : options) {
                                if (entryValue.equals(option.getValue())) {
                                    mutableIssue.setCustomFieldValue(customFiled, option);
                                }
                            }
                        }
                        break;
                    case "com.atlassian.jira.plugin.system.customfieldtypes:cascadingselect":
                        Option option = optionsManager.findByOptionId(Long.parseLong(entryValue));
                        if (option == null) {
                            continue;
                        }
                        Option parentOption = option.getParentOption();
                        Map<String, Option> optionMap = new HashMap<>();
                        if (parentOption != null) {
                            optionMap.put(null,parentOption);
                            optionMap.put("1",option);
                        } else {
                            optionMap.put(null,option);
                            optionMap.put("1",null);
                        }
                        mutableIssue.setCustomFieldValue(customFiled,optionMap);
                        break;
                    case "com.atlassian.jira.plugin.system.customfieldtypes:userpicker":
                        ApplicationUser user = ComponentAccessor.getUserManager().getUserByName(entryValue);
                        if (user == null) {
                            throw new IllegalArgumentException("用户工号传递错误，jira未找到用户：" + entryValue);
                        }
                        mutableIssue.setCustomFieldValue(customFiled, user);
                        break;
                    case "com.atlassian.jira.plugin.system.customfieldtypes:multiuserpicker":
                        List<String> applicationUserNameList = Convert.toList(String.class, entryValue);
                        List<ApplicationUser> applicationUserList = new ArrayList<>();
                        for (String userName1 : applicationUserNameList) {
                            ApplicationUser userByName = userManager.getUserByName(userName1);
                            if (userByName == null) {
                                throw new IllegalArgumentException("用户工号传递错误，jira未找到用户：" + userName1);
                            }
                            applicationUserList.add(userByName);
                        }
                        mutableIssue.setCustomFieldValue(customFiled, applicationUserList);
                        break;
                    case "com.atlassian.jira.plugin.system.customfieldtypes:datepicker":
                    case "com.atlassian.jira.plugin.system.customfieldtypes:datetime":
                        //格式 yyyy-MM-dd HH:mm:ss
                        if (NumberUtil.isNumber(entryValue)) {
                            Timestamp timestamp = new Timestamp(Long.parseLong(entryValue));
                            mutableIssue.setCustomFieldValue(customFiled, timestamp);
                        }else {
                            entryValue = entryValue.length() == 10 ? entryValue + " 00:00:00" : entryValue;
                            //java.sql.Date.valueOf(String s)的s格式必须为YYYY-MM-DD格式
//                        Date date = java.sql.Date.valueOf(entryValue);
//                        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//                        String time = df.format(date);
                            Timestamp timestamp = Timestamp.valueOf(entryValue);
//                        Timestamp timestamp = entryValue.length() == 10 ? Timestamp.valueOf(entryValue + " 00:00:00") : Timestamp.valueOf(entryValue);

                            mutableIssue.setCustomFieldValue(customFiled, timestamp);
                        }

                        break;
                    case "com.atlassian.jira.plugin.system.customfieldtypes:textfield":
                    case "com.atlassian.jira.plugin.system.customfieldtypes:textarea":
                        mutableIssue.setCustomFieldValue(customFiled, entryValue);
                        break;
                    case "com.atlassian.jira.plugin.system.customfieldtypes:float":
                        mutableIssue.setCustomFieldValue(customFiled, Double.valueOf(entryValue));
                        break;
                    default:
                        throw new IllegalStateException("Unexpected customFieldTypeKey: " + customFieldTypeKey);
                }
                updateSuccess++;
            }
            if (Boolean.TRUE.equals(isUpdate)) {
                ComponentAccessor.getIssueManager().updateIssue(applicationUser, mutableIssue, EventDispatchOption.ISSUE_UPDATED, false);
            }
            Map<String, Object> resultMap = new HashMap<>();
            resultMap.put("total", (long) map.size());
            resultMap.put("update", (long) updateSuccess);
            resultMap.put("issueId", mutableIssue.getId());
            resultMap.put("issueKey", mutableIssue.getKey());
            resultBean.setValue(resultMap);

        }catch (Exception e){
            log.error("更新测试失效失败：{}", Utils.errInfo(e));
            resultBean.setMessage("更新测试失效失败：" + e.getMessage());
        }

        return resultBean;
    }
}
