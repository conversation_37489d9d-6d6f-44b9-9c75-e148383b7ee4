package com.eve.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.atlassian.activeobjects.external.ActiveObjects;
import com.atlassian.crowd.embedded.api.Group;
import com.atlassian.jira.component.ComponentAccessor;
import com.atlassian.jira.event.type.EventDispatchOption;
import com.atlassian.jira.issue.*;
import com.atlassian.jira.issue.fields.CustomField;
import com.atlassian.jira.issue.issuetype.IssueType;
import com.atlassian.jira.project.Project;
import com.atlassian.jira.security.JiraAuthenticationContext;
import com.atlassian.jira.user.ApplicationUser;
import com.eve.ao.TripRequestMsgAo;
import com.eve.beans.DeptProjectBean;
import com.eve.beans.DeptsBean;
import com.eve.beans.ResultBean;
import com.eve.beans.TripRequestBean;
import com.eve.utils.Constant;
import com.eve.utils.Utils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/9/13
 */
public class TripReportService {
    private static final Logger log = LoggerFactory.getLogger(TripReportService.class);
    private ActiveObjects ao;
    @Resource
    private DeptProjectService deptProjectService;
    @Resource
    private DeptsService deptsService;
    @Resource
    private CustomFieldManager customFieldManager;
    @Resource
    private IssueManager issueManager;

    public TripReportService(ActiveObjects ao) {
        this.ao = ao;
    }

    public ResultBean createTripReport(int isOnline, TripRequestBean tripRequestBean) {
        ResultBean resultBean = new ResultBean();
        if (tripRequestBean.getBoss() == null || "".equals(tripRequestBean.getBoss())) {
            tripRequestBean.setBoss("083779");
        }
        String data = JSON.toJSONString(tripRequestBean, SerializerFeature.WriteMapNullValue);
        log.error("创建出差报告任务参数：" + data);
//        TripRequestMsgAO tripRequestMsgAO = ao.create(TripRequestMsgAO.class);
//        tripRequestMsgAO.setApplicant(tripRequestBean.getApplicant());
//        tripRequestMsgAO.setTripReason(tripRequestBean.getTripReason());
//        tripRequestMsgAO.setTripAddress(tripRequestBean.getTripAddress());
//        tripRequestMsgAO.setTripDays(tripRequestBean.getTripDays());
//        tripRequestMsgAO.setDept(tripRequestBean.getDept());
//        tripRequestMsgAO.setTripStartDate(tripRequestBean.getTripStartDate());
//        tripRequestMsgAO.setTripEndDate(tripRequestBean.getTripEndDate());
//        tripRequestMsgAO.setBoss(tripRequestBean.getBoss());
//        tripRequestMsgAO.setCreateDate(new java.sql.Timestamp(System.currentTimeMillis()));
        try {



            DeptProjectBean deptProjectBean = new DeptProjectBean();
            if (isOnline == 1) {//正式环境，匹配部分区分两个地区的出差报告
//                deptProjectBean.setExecutor("083779");
//                deptProjectBean.setProjectId(11516L);
//                deptProjectBean.setIssueTypeId(10906L);
                DeptsBean deptsBean = deptsService.getByName(tripRequestBean.getDept());

                if (null == deptsBean || deptsBean.getDeptId() == null) {
                    resultBean.setValue("部门未配置");
                    return resultBean;
//                deptsBean = deptsService.getByName("DLTEST");
                }

//            if (null == deptsBean) {
//                resultBean.setMessage("部门匹配不正确");
//                return resultBean;
//            }
                String data1 = JSON.toJSONString(deptsBean, SerializerFeature.WriteMapNullValue);
                log.error("匹配之后的部门：" + data1);
                deptProjectBean = deptProjectService.getByDeptId(deptsBean.getDeptId());

            } else {
                deptProjectBean.setExecutor("083779");
                deptProjectBean.setProjectId(11516L);
                deptProjectBean.setIssueTypeId(10906L);
            }
            //模拟用户登录
            JiraAuthenticationContext context = ComponentAccessor.getComponent(JiraAuthenticationContext.class);
            ApplicationUser reporter = ComponentAccessor.getUserManager().getUser(deptProjectBean.getExecutor());
            if (reporter == null) {
                resultBean.setMessage("报告人配置错误");
                return resultBean;
            }
            context.setLoggedInUser(reporter);

            // 获取项目信息
            Project project = ComponentAccessor.getProjectManager().getProjectObj(deptProjectBean.getProjectId());
            if (project == null) {
                resultBean.setMessage("项目不存在");
                return resultBean;
            }
            // 获取问题类型信息
            IssueType issueType = ComponentAccessor.getConstantsManager().getIssueType(deptProjectBean.getIssueTypeId() + "");
            if (issueType == null) {
                resultBean.setMessage("问题类型不存在");
                return resultBean;
            }
            log.error("项目信息、问题类型获取成功");
            // 获取issue的服务
            com.atlassian.jira.bc.issue.IssueService issueService = ComponentAccessor.getIssueService();

            /*创建issue参数*/
            IssueInputParameters issueInputParameters = issueService.newIssueInputParameters();
            String tripUserName = tripRequestBean.getApplicant();
            ApplicationUser tripUser = Utils.getApplicationUserByName(tripUserName);
            if (tripUser == null) {
                while (tripUserName.length() < 6) {
                    tripUserName = "0" + tripUserName;
                }
                tripUser = Utils.getApplicationUserByName(tripUserName);
                if (tripUser == null) {
                    throw new IllegalArgumentException("JIRA系统不存在该用户");
                }
            }
            //是否具有查看出差报告的权限
            Collection<String> allGroupNames = ComponentAccessor.getGroupManager().getGroupNamesForUser(tripUser);
            if (!allGroupNames.contains("动力电池研究院")) {
                Group group = ComponentAccessor.getGroupManager().getGroup("jira-software-users");
                if (group != null) {
                    ComponentAccessor.getUserUtil().addUserToGroup(group, tripUser);
                }
                Group group1 = ComponentAccessor.getGroupManager().getGroup("confluence-users");
                if (group1 != null) {
                    ComponentAccessor.getUserUtil().addUserToGroup(group1, tripUser);
                }
                Group group2 = ComponentAccessor.getGroupManager().getGroup("动力电池研究院");
                if (group2 != null) {
                    ComponentAccessor.getUserUtil().addUserToGroup(group2, tripUser);
                }
            }
            String boss = tripRequestBean.getBoss();
            ApplicationUser bossUser = Utils.getApplicationUserByName(boss);

            if (bossUser == null) {
                while (boss.length() < 6) {
                    boss = "0" + boss;
                }
                bossUser = Utils.getApplicationUserByName(boss);
                if (bossUser == null) {
                    throw new IllegalArgumentException("JIRA系统不存在该用户");
                }
            }
            log.error("参数校验通过");
            TripRequestMsgAo tripRequestMsgAo = ao.create(TripRequestMsgAo.class);
            tripRequestMsgAo.setDept(tripRequestBean.getDept());
            tripRequestMsgAo.setApplicant(tripRequestBean.getApplicant());
            tripRequestMsgAo.setBoss(tripRequestBean.getBoss());
            tripRequestMsgAo.setTripReason(tripRequestBean.getTripReason());
            tripRequestMsgAo.setTripAddress(tripRequestBean.getTripAddress());
            tripRequestMsgAo.setTripStartDate(tripRequestBean.getTripStartDate());
            tripRequestMsgAo.setTripEndDate(tripRequestBean.getTripEndDate());
            tripRequestMsgAo.setTripDays(tripRequestBean.getTripDays());
            tripRequestMsgAo.setBusinessCode(tripRequestBean.getBusinessCode());
            tripRequestMsgAo.setIsOnline(isOnline + "");
            tripRequestMsgAo.setIssueId(null);
            tripRequestMsgAo.setCreateResult("0");
            tripRequestMsgAo.setReceiveDate(new Timestamp(System.currentTimeMillis()));
            tripRequestMsgAo.save();

            // 设置项目类型
            issueInputParameters.setProjectId(project.getId());
            // 设置问题类型
            issueInputParameters.setIssueTypeId(issueType.getId());
            // 设置经办人
            issueInputParameters.setAssigneeId(reporter.getUsername());
            // 设置报告人
            issueInputParameters.setReporterId(reporter.getUsername());
            //设置概要
            issueInputParameters.setSummary(
                    tripUser.getDisplayName()
                            + "出差报告-" + tripRequestBean.getTripAddress()
                            + "-" + tripRequestBean.getTripStartDate().replace("-", ".")
                            + "-" + tripRequestBean.getTripEndDate().replace("-", ".")
            );

            //设置申请人
            issueInputParameters.addCustomFieldValue(Constant.applicantCustID, tripUser.getUsername());
            //设置上司
            if (boss != null) {
                issueInputParameters.addCustomFieldValue(Constant.bossCustID, bossUser.getUsername());
            }
            //设置出差事由
            issueInputParameters.addCustomFieldValue(Constant.tripReasonCustID, tripRequestBean.getTripReason());
            //设置出差地点
            issueInputParameters.addCustomFieldValue(Constant.tripAddressCustID, tripRequestBean.getTripAddress());
            //设置出差天数
            issueInputParameters.addCustomFieldValue(Constant.tripDaysCustID, tripRequestBean.getTripDays());
            //设置开始日期
            issueInputParameters.addCustomFieldValue(Constant.tripStartDateCustID, Utils.getDateStr(tripRequestBean.getTripStartDate()));
            //设置结束日期
            issueInputParameters.addCustomFieldValue(Constant.tripEndDateCustID, Utils.getDateStr(tripRequestBean.getTripEndDate()));
            //设置到期日
            String dueDate = Utils.plusDates(4, tripRequestBean.getTripEndDate());
            issueInputParameters.setDueDate(Utils.getDateStr(dueDate));

            com.atlassian.jira.bc.issue.IssueService.CreateValidationResult createValidationResult = issueService.validateCreate(reporter, issueInputParameters);

            if (!createValidationResult.isValid()) {

                resultBean.setMessage(createValidationResult.getErrorCollection().toString());
                return resultBean;
            }
            com.atlassian.jira.bc.issue.IssueService.IssueResult createResult = issueService.create(reporter, createValidationResult);
            Issue issue = createResult.getIssue();
            Map<String, Object> resultMap = new HashMap<>();
            //重新获取
            Long aoId = tripRequestMsgAo.getId();
            tripRequestMsgAo = ao.get(TripRequestMsgAo.class, aoId);
            tripRequestMsgAo.setIssueId(issue.getId() + "");
            tripRequestMsgAo.setCreateResult("1");
            tripRequestMsgAo.save();
            resultMap.put("issueId", issue.getId());
            resultBean.setValue(issue.getId());
        } catch (Exception e) {
            log.error("创建出差报告任务失败：{}", Utils.errInfo(e));
            resultBean.setMessage("错误：" + e.getMessage());
        }

        return resultBean;
    }

    public ResultBean editTripReport(TripRequestBean tripRequestBean) {
        ResultBean resultBean = new ResultBean();
        String data = JSON.toJSONString(tripRequestBean, SerializerFeature.WriteMapNullValue);
        log.error("编辑出差报告任务参数：" + data);
        String issueIdStr = tripRequestBean.getIssueId();
        
        CustomField tripReasonCustomField = customFieldManager.getCustomFieldObject(Constant.tripReasonCustID);//出差事由
        CustomField tripAddressCustomField = customFieldManager.getCustomFieldObject(Constant.tripAddressCustID);//出差地点
        CustomField tripStartDateCustomField = customFieldManager.getCustomFieldObject(Constant.tripStartDateCustID);//出差开始日期
        CustomField tripEndDateCustomField = customFieldManager.getCustomFieldObject(Constant.tripEndDateCustID);//出差结束日期
        CustomField tripDaysCustomField = customFieldManager.getCustomFieldObject(Constant.tripDaysCustID);//出差时间

        CustomField applicantCustomField = customFieldManager.getCustomFieldObject(Constant.applicantCustID);//报告提交人

        try {
            MutableIssue tripReportIssue = issueManager.getIssueObject(StringUtils.isNumeric(issueIdStr) ? Long.parseLong(issueIdStr) : 0L);
            if (tripReportIssue == null) {
                resultBean.setMessage("未找到出差报告任务");
                return resultBean;
            }
            ApplicationUser tripUser = (ApplicationUser) tripReportIssue.getCustomFieldValue(applicantCustomField);
            tripReportIssue.setCustomFieldValue(tripReasonCustomField, tripRequestBean.getTripReason());
            tripReportIssue.setCustomFieldValue(tripAddressCustomField, tripRequestBean.getTripAddress());
            tripReportIssue.setCustomFieldValue(tripStartDateCustomField, Utils.getTimestamp(tripRequestBean.getTripStartDate()));
            tripReportIssue.setCustomFieldValue(tripEndDateCustomField, Utils.getTimestamp(tripRequestBean.getTripEndDate()));
            tripReportIssue.setCustomFieldValue(tripDaysCustomField, Double.parseDouble(tripRequestBean.getTripDays()));
            String dueDate = Utils.plusDates(4, tripRequestBean.getTripEndDate());
            tripReportIssue.setDueDate(Utils.getTimestamp(dueDate));
            tripReportIssue.setSummary(tripUser.getDisplayName()
                    + "出差报告-" + tripRequestBean.getTripAddress()
                    + "-" + tripRequestBean.getTripStartDate().replace("-", ".")
                    + "-" + tripRequestBean.getTripEndDate().replace("-", "."));
            
            issueManager.updateIssue(tripUser, tripReportIssue, EventDispatchOption.ISSUE_UPDATED, false);
            
            resultBean.setValue(issueIdStr);
        } catch (Exception e) {
            log.error("编辑出差报告失败：{}", Utils.errInfo(e));
            resultBean.setMessage("错误：" + e.getMessage());
        }
        return resultBean;
    }
}
