package com.eve.utils;

import com.eve.beans.CopyFieldBean;

import java.util.*;

public class Constant {

    //sap接口
//    public static String getCostCenterInterfaceTest = "http://10.1.2.70:8080/gateway/dev/sap/v_plm_kostl";//bukrs=? sap测试版获取成本中心接口
    public static String getCostCenterInterface = "http://10.1.3.20:8080/gateway/dev/sap/v_plm_kostl";//bukrs=? sap正式版获取成本中心接口
//    public static String createProjectInterfaceTest = "http://10.1.2.70:8080/restcloud/dev/sap/ZFM_PLM_CO_001";
    public static String createProjectInterface = "http://10.1.3.20:8080/gateway/dev/sap/ZFM_PLM_CO_001";
    public static String sapTestAppKey = "627327aba8f26f04184ef8e2";
    public static String sapAppKey = "61dd2c64ee02641cb1558a5e";
    public static String jiraAuthorization = "Basic ZGxhZG1pbjpqaXJhLmRsYWRtaW4=";//jira访问权限
    //项目设置的自定义字段
    public static Long travelReportProjectId = 10922L;//出差报告项目id
    public static Long applicantCustID = 10925L;//报告提交人
    public static Long bossCustID = 10922L;//直属上司
    public static Long tripReasonCustID = 11108L;//出差事由
    public static Long tripAddressCustID = 11113L;//出差地点
    public static Long tripStartDateCustID = 11105L;//出差开始日期
    public static Long tripEndDateCustID = 11106L;//出差结束日期
    public static Long tripDaysCustID = 11110L;//出差天数
    public static Long subDepartCustID = 11714L;//部门（研究院）
    public static String subDepartKey = "customfield_11714:1";//部门（研究院）第二选项
    public static Long jmDepartmentCustID = 11007L;//部门（荆门院）
    public static List<Long> jmDepartmentOptionList = new ArrayList<>(Arrays.asList(22269L, 18711L));//荆门部门
    public static List<Long> vCylinderDepartmentOptionList = new ArrayList<>(Arrays.asList(18863L));//V圆柱部门
    public static Long issueNumber = 12187L;//正式版为-12187L;
    public static Long innerOrderType = 12400L;//内部订单类型 正式环境-0000L 开发环境-12400L
    public static Long costCenter = 12404L;//成本中心 正式环境-0000L 开发环境-12404L
    public static Long innerOrderCodeCustomFieldId = 11815L;//SAP内部订单编号
    public static Long affiliatedPlatformCustomFieldId = 12901L;//归属平台
    public static Long platformLeaderCustomFieldId = 12902L;//平台负责人
    public static Long topicTypeCustomFieldId = 11153L;//课题类型->课题来源
    public static Long platformCateCustomFieldId = 10906L;//平台分类

    //通用字段
    public static String pbiUrl = "http://*********:89";//PBI接口 正式环境：http://*********:89/
    public static String pbiUrlTest = "http://**********:83";//PBI接口 测试环境：http://**********:83/  http://***********:8065/
    public static String userPickerFieldType = "com.atlassian.jira.plugin.system.customfieldtypes:userpicker";
    public static String multiUserPickerFieldType = "com.atlassian.jira.plugin.system.customfieldtypes:multiuserpicker";
    public static String numFieldType = "com.atlassian.jira.plugin.system.customfieldtypes:float";
    public static String textFieldType = "com.atlassian.jira.plugin.system.customfieldtypes:textfield";
    public static String textareaFieldType = "com.atlassian.jira.plugin.system.customfieldtypes:textarea";
    public static String dateFieldType = "com.atlassian.jira.plugin.system.customfieldtypes:datepicker";
    public static String dateTimeFieldType = "com.atlassian.jira.plugin.system.customfieldtypes:datetime";
//    public static String selectFieldType = "com.atlassian.jira.plugin.system.customfieldtypes:radiobuttons";
    public static String selectFieldType = "com.atlassian.jira.plugin.system.customfieldtypes:select";
    public static String multiselectFieldType = "com.atlassian.jira.plugin.system.customfieldtypes:multiselect";
    public static String cascadingSelectFieldType = "com.atlassian.jira.plugin.system.customfieldtypes:cascadingselect";

    public static Long progressCustId = 11235L;//进度
    public static Long nextStepCustId = 11238L;//下一步计划
    public static Long riskCustId = 11236L;//问题/风险
    public static Long strageCustId = 11237L;//对策
    public static Long processUpdateDateCustomFieldId = 11570L;//进度更新日期
    public static String project_process_project_id = "HKTXPROPERTIESKEY.eve-jira.project_process_project_id";//需要配置项目进度填写的项目id
    public static String current_signed_file_project_key = "HKTXPROPERTIESKEY.eve-jira.current_signed_file_project_key";//当前签审文件panel显示项目id
    public static String add_page_to_pdf_path = "HKTXPROPERTIESKEY.eve-jira.add_page_to_pdf_path";//插入页面到pdf配置
    public static String repeated_approval_jump_enable_project = "HKTXPROPERTIESKEY.eve-jira.repeated_approval_jump_enable_project";//重复审批跳转激活项目
    public static String customFieldNameAndIdMap = "HKTXPROPERTIESKEY.eve-jira.custom_field_map";//字段id配置
    public static String dladminUserName = "dladmin";//动力电池管理员账号
    public static Long directLeaderCustomFieldId = 11128L;//直属领导
    public static Long inspectorGeneralCustomFieldId = 11196L;//总监
    public static Long deputyHeadOfTheInstituteCustomFieldId = 11073L;//副所长
    public static Long headOfTheInstituteCustomFieldId = 11208L;//所长
    public static Long presidentCustomFieldId = 11903L;//院长
    public static Long projectManagerCustomFieldId = 11129L;//项目管理员
    public static Long attachmentSignCateCustomFieldId = 11804L;//签字文件类别

    //测试失效字段
    public static Long testFailureProjectId = 13003L;//测试失效项目ID
    public static Long testFailureProjectIdTest = 13004L;//测试失效测试项目ID 13004
    public static String testFailureApprovalUrl = "http://*********:89/open/jira/receiveTestFailureCheck";//审批完成PBI接口 正式环境：http://*********:88/
    public static String testFailureApprovalUrlTest = "http://**********:83/open/jira/receiveTestFailureCheck";//审批完成PBI接口 测试环境：http://**********:83/  http://***********:8065/
    public static Long productDepartmentCustomFieldId = 11714L;//部门
    public static Long businessIdCustomFieldId = 11175L;//业务ID
    public static Long faResponsibleCustomFieldId = 12014L;//FA责任人
    public static Long faDueDateCustomFieldId = 13005L;//FA期限
    public static Long inStoreDateCustomFieldId = 13006L;//入库日期
    public static Long stockLocateCustomFieldId = 13008L;//存放库位 old 12158L text new 13008L select
    public static Long ourStoreDateCustomFieldId = 13007L;//出库日期
    public static Long cellCodeCustomFieldId = 13045L;//电芯编码
    public static Long folderNoCustomFieldId = 13026L;//委托单号 电芯编码换过来的，原电芯编码变为文本域
    public static Long testProjectNameCustomFieldId = 13046L;//测试项目
    public static Long cellNumCustomFieldId = 11156L;//电芯数量
    public static Long testTypeCustomFieldId = 12511L;//试验类型
    public static Long laboratoryCustomFieldId = 10526L;//检测实验室
    public static Long folderUserCustomFieldId = 12960L;//委托人
    public static Long cellBatchCustomFieldId = 12501L;//电芯批次
    public static Long testCateCustomFieldId = 12509L;//测试类别
    public static Long failureCateCustomFieldId = 12510L;//失效类别
    public static Long testFailureDescriptionCustomFieldId = 11600L;//失效描述
    public static Long improveEffectEvaluationCustomFieldId = 12138L;//改善效果评价
    public static Long failureAnalysisContentCustomFieldId = 12203L;//失效分析点
    public static Long causeAnalysisCustomFieldId = 12932L;//原因分析
    public static Long tempMeasuresCustomFieldId = 12951L;//临时措施
    public static Long longTermMeasuresCustomFieldId = 12917L;//长期措施
    public static Long resultVerificationCustomFieldId = 12950L;//结果验证
    public static Long reviewerCustomFieldId = 10722L;//审核人
    public static Long approverCustomFieldId = 11146L;//批准人



    public static Long DPVTestFailureRecordIssueTypeId = 12101L;//DPV测试失效登记问题类型
    public static Long failureCellInStoreIssueTypeId = 12102L;//失效电芯入库申请问题类型
    public static Long failureCellOutStoreIssueTypeId = 12103L;//失效电芯出库申请问题类型
    public static Long FAResponsibleAssignIssueTypeId = 12104L;//FA责任人指定问题类型
    public static Long FAAnalyseReportIssueTypeId = 12105L;//FA分析报告问题类型

    public static Long sampleManagerCustomFieldId = 10612L;//样品管理员
    public static Long laboratoryResponsibleCustomFieldId = 11068L;//实验室负责人
    public static Long departmentManagerCustomFieldId = 12926L;//部门经理
    public static Long productManagerCustomFieldId = 11873L;//产品经理
    public static Long productMajordomoCustomFieldId = 11917L;//产品总监
    public static Long DQECustomFieldId = 11883L;//DQE
    public static Long vicePresidentCustomFieldId = 11606L;//副院长
    public static Long countersignerCustomFieldId = 12800L;//会签人员


    //项目管理字段
    public static List<Long> projectManageProjectIdList = new ArrayList<>(Arrays.asList(10925L, 11338L));
    public static List<Long> projectManageTestProjectIdList = new ArrayList<>(Arrays.asList(11533L, 13101L));
    public static List<Long> projectReviewStatusIdList = new ArrayList<>(Arrays.asList(10625L, 11905L, 11906L, 10745L, 11933L));
    public static String projectManageArchiveAttachmentUrl = "http://*********:89/open/jira/receiveFile";//审批完成PBI接口 正式环境：http://*********:88/
    public static String projectManageArchiveAttachmentUrlTest = "http://**********:83/open/jira/receiveFile";//审批完成PBI接口 测试环境：http://**********:83/  http://***********:80/
    public static Long projectManageProjectId = 10925L;//项目管理ID
    public static Long lfpProjectManageProjectId = 11338L;//LFP项目管理ID
    public static Long projectManageTestProjectId = 11533L;//测试项目ID
    public static Long lfpProjectManageTestProjectId = 13101L;//测试项目ID
//    public static Long platformIssueTypeId = 10914L;//平台技术项目问题类型ID
    public static Long platformIssueTypeId = 10907L;//平台技术项目问题类型ID
//    public static Long topicIssueTypeId = 11100L;//课题项目问题类型ID
    public static Long topicIssueTypeId = 10907L;//课题项目问题类型ID
    public static Long gainReviewSubIssueTypeId = 11305L;//课题成果子任务问题类型ID
    public static Long projectCateCustomFieldId = 11569L;//项目分类
    public static Long projectNameCustomFieldId = 11101L;//项目名称
    public static Long projectCodeCustomFieldId = 11243L;//项目代号
    public static Long projectBackGroundCustomFieldId = 11187L;//项目背景
    public static Long projectPurposeCustomFieldId = 11188L;//项目目的
    public static Long projectTargetCustomFieldId = 11189L;//项目目标
    public static Long projectLevelCustomFieldId = 11191L;//项目等级
    public static Long developmentBudgetCustomFieldId = 11190L;//开发预算（万元）
    public static Long progressCustomFieldId = 11235L;//进度
    public static Long problemAndRiskCustomFieldId = 11236L;//问题/风险
    public static Long strategyCustomFieldId = 11237L;//对策
    public static Long nextPlanCustomFieldId = 11238L;//下一步计划
    public static Long planInitiationDateCustomFieldId = 11523L;//计划立项&方案评审日期
    public static Long initiationPassDateCustomFieldId = 11217L;//立项&方案评审通过日期
    public static Long initiationDateCustomFieldId = 11401L;//立项评审日期
    public static Long resultTopicInitiationDateCustomFieldId = 11409L;//成果的课题立项日期
    public static Long planSchemeReviewDateCustomFieldId = 11095L;//计划方案评审日期
    public static Long schemeReviewPassDateCustomFieldId = 11216L;//方案评审通过日期
    public static Long planTechnicalSummaryDateCustomFieldId = 11653L;//计划技术总结日期
    public static Long technicalSummaryPassDateCustomFieldId = 11219L;//技术总结评审通过日期
    public static Long secondReviewCustomFieldId = 11031L;//是否二次评审
    public static Long researchContentCustomFieldId = 10921L;//研究内容
    public static Long reviewResultCustomFieldId = 11220L;//立项评审结果
    public static Long reviewDateCustomFieldId = 11401L;//立项评审日期
    public static Long projectCompleteCustomFieldId = 11072L;//结项日期
    public static Long gainResultCustomFieldId = 11847L;//成果评审结果
    public static Long gainDirectorScoreCustomFieldId = 12172L;//成果所长评分
    public static Long gainVicePresidentScoreCustomFieldId = 13070L;//成果副院长评分
    public static Long gainVicePresidentOpinionCustomFieldId = 13071L;//成果副院长评价
    public static Long gainPresidentScoreCustomFieldId = 11134L;//成果院长评分
    public static Long gainPresidentOpinionCustomFieldId = 12103L;//成果院长评价

    public static Long reviewResult1CustomFieldId = 12715L;//一次评审结果
    public static Long reviewOpinion1CustomFieldId = 11503L;//一次评审意见
    public static Long reviewResult2CustomFieldId = 10912L;//二次评审结果
    public static Long reviewOpinion2CustomFieldId = 11233L;//二次评审意见

    public static Long reviewMonthCustomFieldId = 10916L;//评审月份
    public static Long isSystemCallCustomFieldId = 11676L;//接口调用标志
    public static Long pauseStatusId = 10000L;

    public static Long topicLeaderCustomFieldId = 12174L;//课题负责人
    public static Long submitterCustomFieldId = 13132L;//申请人
    public static Long departmentStrCustomFieldId = 13133L;//申请人部门


    //实验报告字段
    public static List<Long> testReportProjectIdList = new ArrayList<>(Arrays.asList(10924L, 11242L));
    public static Long testReportCodeCustomFieldId = 11114L;//实验单号
    public static Long testReportNameCustomFieldId = 11115L;//项目名称
    public static Long testReportPurposeCustomFieldId = 11116L;//项目目的
    public static Long testReportPassDateCustomFieldId = 11151L;//报告通过日期

    //产品管理字段
    public static List<Long> productManageProjectIdList = new ArrayList<>(Arrays.asList(11527L,11549L));//测试-11549L 正式-11527L
    public static List<Long> productStageSchemeFreezeIdList = new ArrayList<>(Arrays.asList(18943L,18944L));//产品阶段，方案冻结选项ID
    public static List<Long> productStageM4ToM6IdList = new ArrayList<>(Arrays.asList(18931L,18932L,18933L));//M4-M6选项ID
    public static Long productIssueTypeId = 11317L;//产品研发问题类型ID
    public static Long productStageIssueTypeId = 11310L;//产品研发问题类型ID
    public static Long productNameCustomFieldId = 11912L;//产品名称
    public static Long productStageCustomFieldId = 11557L;//产品阶段
    public static Long productCateCustomFieldId = 11890L;//产品类别
    public static Long productModelCustomFieldId = 11161L;//产品型号

    public static Long vCylinderProductDepartmentCustId = 12038L;//V圆柱产品部门

    public static Long stageFileCompleteCustomFieldId = 11676L;//接口执行流程标志

    //出差报告字段
    public static List<Long> travelReportProjectIdList = new ArrayList<>(Arrays.asList(10922L,11509L,11239L,11528L));
    public static Long travelReportIssueTypeId = 10906L;//出差报告问题类型ID
    public static Long travelReasonCustomFieldId = 11108L;//出差事由
    public static Long travelAddrCustomFieldId = 11113L;//出差地点
    public static Long travelDaysCustomFieldId = 11110L;//出差天数
    public static Long leaderScoreCustomFieldId = 11091L;//直属领导评分
    public static Long leaderEvaluationCustomFieldId = 11109L;//直属领导评价
    public static Long manageScoreCustomFieldId = 11112L;//综合管理部经理评分
    public static Long travelReportSubmitDateCustomFieldId = 11202L;//报告提交日期

    //成果管理
    public static List<Long> achievementManageProjectIdList = new ArrayList<>(Arrays.asList(11340L,11508L));
    public static List<Long> achievementManageIssueTypeIdList = new ArrayList<>(Arrays.asList(11303L,11300L,11301L,11302L));
    public static Long mainCompletePersonCustomFieldId = 11698L;//主要完成人员名单
    public static Long achievementCateCustomFieldId = 12039L;//成果分类
    public static Long achievementNameCustomFieldId = 11695L;//成果名称
    public static Long achievementModalityCustomFieldId = 11704L;//成果的形式
    public static Long achievementContentCustomFieldId = 11697L;//成果内容简介
    public static Long directorScoreCustomFieldId = 11126L;//所长评分
    public static Long directorEvaluationCustomFieldId = 11581L;//所长评价
    public static Long averageScoreCustomFieldId = 11844L;//平均分（评委）
    public static Long achievementPassDateCustomFieldId = 11845L;//成果认定通过日期
    public static Long resultAfter22YearCustomFieldId = 11511L;//是否22年后立项的课题成果
    public static Long reviewUserCustomFieldId = 11839L;//评审人
    public static Long reviewScoreCustomFieldId = 11819L;//评分 class java.lang.Double
    public static Long reviewOpinionCustomFieldId = 11838L;//评价
    public static Long presidentScoreCustomFieldId = 11571L;//院长评分
    public static Long presidentEvaluationCustomFieldId = 12103L;//院长评价
    public static Long requestReasonCustomFieldId = 12523L;//申请原因

    //成果查阅申请
    public static Long resultReviewRequestIssueTypeId = 12112L;//成果查阅申请问题类型
    public static Long resultReviewRequestProject = 13007L;//项目id 正式项目-11527L 测试项目-11549L
    public static Long resultReviewRequestProjectTest = 13006L;//项目id 正式项目-11527L 测试项目-11549L
    public static String resultReviewRequestToPbiUrl = "http://*********:89/gateway/product/params/testdata";//共享平台审批完成推送PBI  正式：*********:88
    public static String resultReviewRequestToPbiUrlTest = "http://**********:83/gateway/product/params/testdata";//共享平台审批完成推送PBI测试   测试：**********:83 本地：***********:86

    public static Long requireDepartmentCustomFieldId = 13056L;//需求方部门长
//    public static Long testProjectCustomFieldId = 13046L;//测试项目
//    public static Long detailLinkCustomFieldId = 11030L;//详情链接
//    public static Long departmentManagerCustomFieldId = 12926L;//部门经理
//    public static Long isAgreeCustomFieldId = 10524L;//是否同意

    //产品成本 BOM成本核算
    public static Long bomCostIssueTypeId = 12204L;//BOM成本问题类型
    public static Long productCostProject = 13105L;//项目id 正式项目-11527L 测试项目-11549L
    public static Long productCostProjectTest = 13104L;//项目id 正式项目-11527L 测试项目-11549L
    public static String  productBomCostToPbiUrl = "http://*********:89/open/jira/receiveBomCostAudit";//BOM成本核算审批完成推送PBI  正式：*********:88
    public static String productBomCostToPbiUrlTest = "http://**********:83/open/jira/receiveBomCostAudit";//BOM成本核算审批完成推送PBI测试   测试：**********:83 本地：***********:86
    public static Long usageDescriptionCustomFieldId = 12204L;//用途说明
    public static Long productStateCustId = 11891L; //产品状态自定义字段id
    public static Long ratedCapacityCustomFieldId = 13128L;//额定容量
    public static Long ratedVoltageCustomFieldId = 13129L;//额定电压
    public static Long ratedEnergyCustomFieldId = 13130L;//额定能量
    public static Long bomCodeCustId = 12941L;//BOM编号
    public static Long customerTypeCustomFieldId = 12525L;//客户类型
    public static Long positiveElectrodeSystemCustomFieldId = 12105L;//正极体系
    public static Long accountingCodeCustomFieldId = 11159L;//核算代码
    public static Long submitLinkCustomFieldId = 13134L;//提交链接
    public static Long majordomoCustId = 11196L;//部门长/总监


    public static String runEnvironment = "0";//运行环境: 0-线上正式环境 1-线上测试环境 2-CJW本地开发环境
    public static List<CopyFieldBean> assigneeAndReporter = new ArrayList<>(Arrays.asList(
                    new CopyFieldBean("assignee","经办人"),
                    new CopyFieldBean("reporter","报告人")
            ));
    public static Map<String, String> copyTypeMap = new HashMap<String, String>() {{
        put("current_issue", "当前问题");
        put("parent_issue", "父任务");
        put("sub_issue", "子任务");
        put("epic_issue", "EPIC");
        put("epic_link_issue", "EPIC链接的问题");
    }};
    public static Map<String, List<String>> createProjectOrderTypeMap = new HashMap<String, List<String>>() {{
        put("1000",new ArrayList<>(Arrays.asList("ZD01","亿纬锂能研发项目内部订单类型-1000")));//亿纬锂能研发项目内部订单类型-1000
        put("1100",new ArrayList<>(Arrays.asList("ZD02","金源公司研发项目内部订单类型-1100")));//金源公司研发项目内部订单类型-1100
        put("1200",new ArrayList<>(Arrays.asList("ZD03","亿纬亚洲研发项目内部订单类型-1200")));//亿纬亚洲研发项目内部订单类型-1200
        put("2000",new ArrayList<>(Arrays.asList("ZD04","亿纬电子研发项目内部订单类型-2000")));//亿纬电子研发项目内部订单类型-2000
        put("2100",new ArrayList<>(Arrays.asList("ZD05","亿纬通讯研发项目内部订单类型-2100")));//亿纬通讯研发项目内部订单类型-2100
        put("2200",new ArrayList<>(Arrays.asList("ZD06","智媄研发项目内部订单类型-2200")));//智媄研发项目内部订单类型-2200
        put("2200",new ArrayList<>(Arrays.asList("ZD07","达信通信研发项目内部订单类型-2200")));//达信通信研发项目内部订单类型-2200
        put("2400",new ArrayList<>(Arrays.asList("ZD08","美达通信研发项目内部订单类型-2400")));//美达通信研发项目内部订单类型-2400
        put("3000",new ArrayList<>(Arrays.asList("ZD09","亿纬赛恩斯研发项目内部订单类型-3000")));//亿纬赛恩斯研发项目内部订单类型-3000
        put("4000",new ArrayList<>(Arrays.asList("ZD10","湖北亿纬动力研发项目内部订单类型-4000")));//湖北亿纬动力研发项目内部订单类型-4000
        put("1500",new ArrayList<>(Arrays.asList("ZD11","荆门亿纬研发项目内部订单类型-1500")));//荆门亿纬研发项目内部订单类型-1500
        put("1700",new ArrayList<>(Arrays.asList("ZD12","孚安特研发项目内部订单类型-1700")));//孚安特研发项目内部订单类型-1700
        put("1600",new ArrayList<>(Arrays.asList("ZD13","惠州创能研发项目内部订单类型-1600")));//惠州创能研发项目内部订单类型-1600
        put("2500",new ArrayList<>(Arrays.asList("ZD14","宁波亿纬创能研发项目内部订单类型-2500")));//宁波亿纬创能研发项目内部订单类型-2500
        put("4200",new ArrayList<>(Arrays.asList("ZD15","惠州亿纬动力研发项目内部订单类型-4200")));//惠州亿纬动力研发项目内部订单类型-4200
        put("1900",new ArrayList<>(Arrays.asList("ZD16","亿纬星笙能源研发项目内部订单类型-1900")));//亿纬星笙能源研发项目内部订单类型-1900
        put("4600",new ArrayList<>(Arrays.asList("ZD17","江苏亿纬林洋研发项目内部订单类型-4600")));//江苏亿纬林洋研发项目内部订单类型-4600
    }};

    static {
        switch (runEnvironment) {
            default:
            case "0":
                innerOrderType = 12601L;//内部订单类型
                costCenter = 12600L;//成本中心
                break;
            case "1":
                break;
            case "2":
                innerOrderType = 12001L;//内部订单类型
                costCenter = 12000L;//成本中心
                break;
        }
    }
}
