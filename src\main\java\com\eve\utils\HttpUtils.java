package com.eve.utils;

import com.alibaba.fastjson.JSON;
import com.sun.jersey.api.client.Client;
import com.sun.jersey.api.client.ClientResponse;
import com.sun.jersey.api.client.WebResource;


import javax.net.ssl.*;
import javax.ws.rs.core.MediaType;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.X509Certificate;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class HttpUtils {
    private static Logger log = LoggerFactory.getLogger(HttpUtils.class);

    public static String doPostBackLog(String url,String data, Map<String, String> headers) {
        String callback = null;
        ClientResponse response = null;

        log.info("请求的参数" + data);

        try {
            // 禁用ssl
            disableSslVerification();
            Client client = Client.create();
            WebResource.Builder requestBuilder = client.resource(url).getRequestBuilder();
            if (headers == null) {
                headers = new HashMap<>();
            }
            Set<String> strings = headers.keySet();
            if (!strings.contains("Content-Type")) {
                requestBuilder.type(MediaType.APPLICATION_JSON_TYPE);
            }
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                requestBuilder.header(entry.getKey(), entry.getValue());
            }
            response = requestBuilder.post(ClientResponse.class, data);

            callback = response.getEntity(String.class);
        } catch (Exception e) {
            log.error(Utils.errInfo(e));
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return callback;
    }

    private static void disableSslVerification() {
        try {
            // Create a trust manager that does not validate certificate chains
            TrustManager[] trustAllCerts = new TrustManager[]{new X509TrustManager() {
                @Override
                public X509Certificate[] getAcceptedIssuers() {
                    return null;
                }

                @Override
                public void checkClientTrusted(X509Certificate[] certs, String authType) {
                }

                @Override
                public void checkServerTrusted(X509Certificate[] certs, String authType) {
                }
            }
            };

            // Install the all-trusting trust manager
            SSLContext sc = SSLContext.getInstance("SSL");
            sc.init(null, trustAllCerts, new java.security.SecureRandom());
            HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory());

            // Create all-trusting host name verifier
            HostnameVerifier allHostsValid = (hostname, session) -> true;

            // Install the all-trusting host verifier
            HttpsURLConnection.setDefaultHostnameVerifier(allHostsValid);
        } catch (NoSuchAlgorithmException | KeyManagementException e) {
            e.printStackTrace();
        }
    }

    public static String doGet(String url, Map<String, String> headers) throws Exception {
        String callback = null;
        ClientResponse response = null;

        // 禁用ssl
        disableSslVerification();
        Client client = Client.create();
        WebResource.Builder requestBuilder = client.resource(url).getRequestBuilder();
        if (headers == null) {
            headers = new HashMap<>();
        }
        Set<String> strings = headers.keySet();
        if (!strings.contains("Content-Type")) {
            requestBuilder.type(MediaType.APPLICATION_JSON_TYPE);
        }
        requestBuilder.header("Authorization", Constant.jiraAuthorization);
        for (Map.Entry<String, String> entry : headers.entrySet()) {
            requestBuilder.header(entry.getKey(), entry.getValue());
        }
        response = requestBuilder.get(ClientResponse.class);

        callback = response.getEntity(String.class);

        return callback;
    }

}
