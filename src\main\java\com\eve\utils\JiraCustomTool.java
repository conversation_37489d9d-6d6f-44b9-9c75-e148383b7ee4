package com.eve.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.atlassian.jira.bc.ServiceResultImpl;
import com.atlassian.jira.bc.issue.IssueService;
import com.atlassian.jira.bc.issue.search.SearchService;
import com.atlassian.jira.component.ComponentAccessor;
import com.atlassian.jira.config.properties.APKeys;
import com.atlassian.jira.issue.Issue;
import com.atlassian.jira.issue.IssueInputParameters;
import com.atlassian.jira.issue.IssueManager;
import com.atlassian.jira.issue.MutableIssue;
import com.atlassian.jira.issue.attachment.Attachment;
import com.atlassian.jira.issue.customfields.CustomFieldUtils;
import com.atlassian.jira.issue.customfields.option.Option;
import com.atlassian.jira.issue.fields.CustomField;
import com.atlassian.jira.issue.search.SearchException;
import com.atlassian.jira.issue.search.SearchResults;
import com.atlassian.jira.mail.MailService;
import com.atlassian.jira.notification.NotificationRecipient;
import com.atlassian.jira.ofbiz.OfBizDelegator;
import com.atlassian.jira.security.JiraAuthenticationContext;
import com.atlassian.jira.transition.TransitionEntry;
import com.atlassian.jira.transition.TransitionManager;
import com.atlassian.jira.transition.WorkflowTransitionEntry;
import com.atlassian.jira.user.ApplicationUser;
import com.atlassian.jira.util.ErrorCollection;
import com.atlassian.jira.web.bean.PagerFilter;
import com.atlassian.jira.workflow.JiraWorkflow;
import com.eve.beans.JiraOptionBean;
import com.eve.beans.SmartAttachmentCateBean;
import org.ofbiz.core.entity.GenericValue;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.ObjectUtils;

import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/11/8
 */
public class JiraCustomTool {
    private static final Logger log = LoggerFactory.getLogger(JiraCustomTool.class);

    private IssueManager issueManager = ComponentAccessor.getIssueManager();
    private OfBizDelegator ofBizDelegator;
    private SearchService searchService;
    private MailService mailService;
    private IssueService issueService;
    private TransitionManager transitionManager;

    public JiraCustomTool(OfBizDelegator ofBizDelegator, SearchService searchService, MailService mailService, IssueService issueService, TransitionManager transitionManager) {
        this.ofBizDelegator = ofBizDelegator;
        this.searchService = searchService;
        this.mailService = mailService;
        this.issueService = issueService;
        this.transitionManager = transitionManager;
    }

    public List<Attachment> getIssueAttachmentByCate(Issue issue, String cateName) throws Exception {
        List<Attachment> attachmentList = new ArrayList<>();

        Collection<Attachment> attachments = issue.getAttachments();
        try {
            // 获取类别列表信息
            Map<String, String> headers = new HashMap<>();
            headers.put("atlas-authorization", ComponentAccessor.getComponent(JiraAuthenticationContext.class).getLoggedInUser().getUsername());
            String baseUrl = ComponentAccessor.getApplicationProperties().getString(APKeys.JIRA_BASEURL);
            String callback = HttpUtils.doGet(
                    baseUrl + "/rest/attach-cat/1.0/attachments?issueKey=" + issue.getKey(),
                    headers
            );
//            log.error("文件类别获取结果：{}", callback);
            List<Long> attachmentIds = new ArrayList<>();
            JSONObject jsonObject = JSON.parseObject(callback);

            List<SmartAttachmentCateBean> smartAttachmentCateBeanList = JSONObject.parseArray(jsonObject.getString("categories"), SmartAttachmentCateBean.class);

            Optional<SmartAttachmentCateBean> faBreakReportFirst = smartAttachmentCateBeanList.stream().filter(e -> cateName.contains(e.getName())).findFirst();
            if (faBreakReportFirst.isPresent()) {
                SmartAttachmentCateBean smartAttachmentCateBean = faBreakReportFirst.get();
                attachmentIds.addAll(smartAttachmentCateBean.getAttachmentIds());

                List<SmartAttachmentCateBean> documents = smartAttachmentCateBean.getDocuments();
                if (!ObjectUtils.isEmpty(documents)) {//有成组的同名文件，取最新的文件(文件id最大)
                    documents.stream().map(SmartAttachmentCateBean::getAttachmentIds)
                            .filter(e -> !ObjectUtils.isEmpty(e))
                            .map(e -> e.stream().max(Comparator.comparing(Long::longValue)).orElse(0L))
                            .forEach(attachmentIds::add);
                }
            }
            attachmentList = attachments.stream().filter(e -> attachmentIds.contains(e.getId())).collect(Collectors.toList());

        } catch (Exception e) {
            throw e;
        }
        return attachmentList;
    }

    public String getFirstAndLastName(ApplicationUser applicationUser) {
        if (ObjectUtils.isEmpty(applicationUser)) {
            return "";
        }
        List<GenericValue> byField = ofBizDelegator.findByField("User", "userName", applicationUser.getName());
        if (ObjectUtils.isEmpty(byField)) {
            return applicationUser.getDisplayName();
        }
        GenericValue cwdUserGV = byField.get(0);
        if (ObjectUtils.isEmpty(cwdUserGV)) {
            return applicationUser.getDisplayName();
        }
        String lastName = "";
        if (cwdUserGV.get("lastName") != null) {
            lastName = cwdUserGV.get("lastName") + "";
        }
        String firstName = "";
        if (cwdUserGV.get("firstName") != null) {
            firstName = cwdUserGV.get("firstName") + "";
        }

        String userName = lastName + firstName;
        String displayName = applicationUser.getDisplayName();
        if (!"".equals(userName) && displayName.contains(userName)) {
            return userName;
        } else {
            return displayName;
        }
    }

    public List<String> getFirstAndLastName(List<ApplicationUser> applicationUserList) {
        List<String> nameList = new ArrayList<>();
        if (ObjectUtils.isEmpty(applicationUserList)) {
            return nameList;
        }
        for (ApplicationUser applicationUser : applicationUserList) {
            List<GenericValue> byField = ofBizDelegator.findByField("User", "userName", applicationUser.getName());
            if (ObjectUtils.isEmpty(byField)) {
                continue;
            }
            GenericValue cwdUserGV = byField.get(0);
            if (ObjectUtils.isEmpty(cwdUserGV)) {
                continue;
            }
            String lastName = "";
            if (cwdUserGV.get("lastName") != null) {
                lastName = cwdUserGV.get("lastName") + "";
            }
            String firstName = "";
            if (cwdUserGV.get("firstName") != null) {
                firstName = cwdUserGV.get("firstName") + "";
            }

            String userName = lastName + firstName;
            String displayName = applicationUser.getDisplayName();
            if (!"".equals(userName) && displayName.contains(userName)) {
                nameList.add(userName);
            } else {
                nameList.add(displayName);
            }
        }
        return nameList;
    }

    public boolean matchJql(Issue issue, String jql, ApplicationUser applicationUser) throws SearchException {
        if (ObjectUtils.isEmpty(jql)) {
            return true;
        }
        jql = "key=" + issue.getKey() + " and (" + jql + ")";
        SearchService.ParseResult parseResult = this.searchService.parseQuery(applicationUser, jql);
        if (parseResult.isValid()) {
            SearchResults<Issue> searchResult = this.searchService.search(applicationUser, parseResult.getQuery(), PagerFilter.newPageAlignedFilter(0, 1));
//            SearchResults<Issue> searchResult = this.searchService.search(applicationUser, parseResult.getQuery(), PagerFilter.getUnlimitedFilter());
            return searchResult.getResults().size() > 0;
        } else {
            return false;
        }
    }

    public List<MutableIssue> getMutableIssueListByJql(ApplicationUser applicationUser,String jql) throws SearchException {
        if (ObjectUtils.isEmpty(jql)) {
            return new ArrayList<>();
        }
        SearchService.ParseResult parseResult = this.searchService.parseQuery(applicationUser, jql);
        if (parseResult.isValid()) {
//            SearchResults<Issue> searchResult = this.searchService.search(applicationUser, parseResult.getQuery(), PagerFilter.newPageAlignedFilter(0, 1));
            SearchResults<Issue> searchResult = this.searchService.search(applicationUser, parseResult.getQuery(), PagerFilter.getUnlimitedFilter());
            return searchResult.getResults().stream().map(issue -> this.issueManager.getIssueObject(issue.getKey())).collect(Collectors.toList());
        } else {
            return new ArrayList<>();
        }
    }

    public List<Issue> getIssueListByJql(ApplicationUser applicationUser, String jql) throws SearchException {
        if (ObjectUtils.isEmpty(jql)) {
            return new ArrayList<>();
        }
        SearchService.ParseResult parseResult = this.searchService.parseQuery(applicationUser, jql);
        if (parseResult.isValid()) {
            SearchResults<Issue> searchResult = this.searchService.search(applicationUser, parseResult.getQuery(), PagerFilter.getUnlimitedFilter());
            return searchResult.getResults();
        } else {
            return new ArrayList<>();
        }
    }

    public int getTotalNumByJql(ApplicationUser applicationUser, String jql) throws SearchException {
        if (ObjectUtils.isEmpty(jql)) {
            return 0;
        }
        SearchService.ParseResult parseResult = this.searchService.parseQuery(applicationUser, jql);
        if (parseResult.isValid()) {
            SearchResults<Issue> searchResult = this.searchService.search(applicationUser, parseResult.getQuery(), PagerFilter.getUnlimitedFilter());
            return searchResult.getTotal();
        } else {
            return 0;
        }
    }

    /**
     * 获取issue中的字段值，返回形式为字符串
     * @param customFieldId 字段key，以customfield_开头
     * @param issue issue
     * @return 字段值
     */
    public String getCustomFieldValue(String customFieldId, Issue issue) {
        if (customFieldId == null || !customFieldId.startsWith("customfield_")) {
            throw new IllegalArgumentException("字段ID格式不正确，请检查");
        }
        return getCustomFieldValue(CustomFieldUtils.getCustomFieldId(customFieldId), issue);
    }

    /**
     * 获取issue中的字段值，返回形式为字符串
     * @param customFieldId 字段ID
     * @param issue issue
     * @return 字段值
     */
    public String getCustomFieldValue(Long customFieldId, Issue issue) {
        CustomField customFieldObject = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(customFieldId);
        if (customFieldObject == null) {
            throw new IllegalArgumentException("未找到该ID的自定义字段");
        }
        return getCustomFieldValue(customFieldObject, issue);
    }

    /**
     * 获取issue中的字段值，返回形式为字符串
     * @param customFieldObject 字段对象
     * @param issue issue
     * @return 字段值
     */
    public String getCustomFieldValue(CustomField customFieldObject, Issue issue) {
        if (issue == null || customFieldObject == null || issue.getCustomFieldValue(customFieldObject) == null) {
            return null;
        }
        if (Constant.userPickerFieldType.equals(customFieldObject.getCustomFieldType().getKey())) {
            ApplicationUser customFieldValue = (ApplicationUser) issue.getCustomFieldValue(customFieldObject);
            return customFieldValue.getDisplayName();
        } else if (Constant.multiUserPickerFieldType.equals(customFieldObject.getCustomFieldType().getKey())) {
            List<ApplicationUser> customFieldValue = (List<ApplicationUser>) issue.getCustomFieldValue(customFieldObject);
            List<String> strings = customFieldValue.stream().map(ApplicationUser::getDisplayName).collect(Collectors.toList());
            return strings.toString();
        } else if (Constant.selectFieldType.equals(customFieldObject.getCustomFieldType().getKey())) {
            Option customFieldValue = (Option) issue.getCustomFieldValue(customFieldObject);
            return customFieldValue.getValue();
        } else if (Constant.multiselectFieldType.equals(customFieldObject.getCustomFieldType().getKey())) {
            Map<Object, Option> customFieldValue = (Map<Object, Option>) issue.getCustomFieldValue(customFieldObject);
            if (customFieldValue == null) {
                return null;
            } else if (customFieldValue.size() == 1) {
                return customFieldValue.get(null).getValue();
            } else if (customFieldValue.size() == 2) {
                return customFieldValue.get(null).getValue() + "->" + customFieldValue.get("1").getValue();
            }
            return "";
        } else if (Constant.textFieldType.equals(customFieldObject.getCustomFieldType().getKey())
                || Constant.textareaFieldType.equals(customFieldObject.getCustomFieldType().getKey())) {
            return (String) issue.getCustomFieldValue(customFieldObject);
        } else if (Constant.dateFieldType.equals(customFieldObject.getCustomFieldType().getKey())) {
            Timestamp customFieldValue = (Timestamp) issue.getCustomFieldValue(customFieldObject);
            SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");//定义格式，不显示毫秒
            return df.format(customFieldValue);
        } else if (Constant.dateTimeFieldType.equals(customFieldObject.getCustomFieldType().getKey())) {
            Timestamp customFieldValue = (Timestamp) issue.getCustomFieldValue(customFieldObject);
            SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");//定义格式，显示到秒
            return df.format(customFieldValue);
        } else if (Constant.numFieldType.equals(customFieldObject.getCustomFieldType().getKey())) {
            Double customFieldValue = (Double) issue.getCustomFieldValue(customFieldObject);
            return customFieldValue == null ? null : customFieldValue.toString();
        } else {
            return null;
//            throw new IllegalArgumentException("不支持的字段类型");
        }
    }

    public List<MutableIssue> getMutableIssueByJql(ApplicationUser applicationUser, String jql) throws SearchException {
        SearchService.ParseResult parseResult = this.searchService.parseQuery(applicationUser, jql);
        if (parseResult.isValid()) {
            SearchResults<Issue> searchResult = this.searchService.search(applicationUser, parseResult.getQuery(), PagerFilter.getUnlimitedFilter());
            return searchResult.getResults().stream().map(issue -> this.issueManager.getIssueObject(issue.getKey())).collect(Collectors.toList());
        } else {
            return new ArrayList<>();
        }

    }
    /**
     * 单个issue调用jira本身的邮件服务发送一封邮件
     * @param issue 问题数据源(单个问题)
     * @param emailTitle 邮件标题（可传入字段id替换数据）
     * @param sendUserFields 收件人字段id
     * @param sendFields 内容字段id
     */
    public void sendEmail(Issue issue, String emailTitle, List<String> sendUserFields, List<String> sendFields) {
        List<String> fieldNameList = new ArrayList<>();
        List<String> fieldValueList = new ArrayList<>();
        List<ApplicationUser> sendUserList = new ArrayList<>();
        //标题
        String regex = "(?<=\\{)[^}]*(?=\\})";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(emailTitle);
        while (matcher.find()) {
            String fieldId = matcher.group();
            CustomField customFieldObject = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(fieldId);
            if (customFieldObject == null) {
                continue;
            }
            emailTitle = emailTitle.replaceAll("\\{" + fieldId + "\\}", getCustomFieldValue(customFieldObject, issue));
        }

        //内容
        fieldNameList.add("问题链接");
        fieldValueList.add(issue.getKey());
        fieldNameList.add("概要");
        fieldValueList.add(issue.getSummary());
        fieldNameList.add("状态");
        fieldValueList.add(issue.getStatus().getName());
        for (String sendField : sendFields) {
            CustomField customFieldObject = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(sendField);
            if (customFieldObject == null) {
                continue;
            }
            fieldNameList.add(customFieldObject.getFieldName());
            fieldValueList.add(getCustomFieldValue(customFieldObject, issue));
        }

        //邮件接收人 每个人都发一封邮件
        for (String sendUserField : sendUserFields) {
            if ("assignee".equals(sendUserField)) {
                sendUserList.add(issue.getAssignee());
            } else if ("reporter".equals(sendUserField)) {
                sendUserList.add(issue.getReporter());
            } else {
                CustomField customFieldObject = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(sendUserField);
                ApplicationUser sendUser = (ApplicationUser) issue.getCustomFieldValue(customFieldObject);
                sendUserList.add(sendUser);
            }
        }
        Map<String, Object> contextMap = new HashMap<>();//推送到模板的数据
        contextMap.put("emailTitle", emailTitle);
        contextMap.put("fieldNameList", fieldNameList);
        contextMap.put("fieldValueList", fieldValueList);
        String emailSubjectTemplate = "templates/postfunctions/SendEmailFunction/sendEmailSubjectTemplate.vm";
        String emailBodyTemplate = "templates/postfunctions/SendEmailFunction/sendEmailBodyTemplate.vm";
        //发送邮件
        sendUserList.forEach(applicationUser -> mailService.sendRenderedMail(applicationUser, new NotificationRecipient(applicationUser), emailSubjectTemplate, emailBodyTemplate, contextMap));
    }

    /**
     * 传入的issue调用jira本身的邮件服务发送一封邮件
     * @param issueList 问题数据源
     * @param emailTitle 邮件标题
     * @param sendUserNameList 收件人工号
     * @param sendFields 内容字段id
     */
    public void sendEmail(List<Issue> issueList, String emailTitle, List<String> sendUserNameList, List<String> sendFields) {
        List<List<String>> allIssueFieldNameList = new ArrayList<>();
        List<List<String>> allIssueFieldValueList = new ArrayList<>();
        //内容
        for (Issue issue : issueList) {
            List<String> fieldNameList = new ArrayList<>();
            List<String> fieldValueList = new ArrayList<>();
            fieldNameList.add("问题链接");
            fieldValueList.add(issue.getKey());
            fieldNameList.add("概要");
            fieldValueList.add(issue.getSummary());
            fieldNameList.add("状态");
            fieldValueList.add(issue.getStatus().getName());
            for (String sendField : sendFields) {
                CustomField customFieldObject = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(sendField);
                if (customFieldObject == null) {
                    continue;
                }
                fieldNameList.add(customFieldObject.getFieldName());
                fieldValueList.add(getCustomFieldValue(customFieldObject, issue));
            }
            allIssueFieldNameList.add(fieldNameList);
            allIssueFieldValueList.add(fieldValueList);
        }

        //邮件接收人 每个人都发一封邮件
        List<ApplicationUser> sendUserList = sendUserNameList.stream().map(sendUserName -> ComponentAccessor.getUserManager().getUserByName(sendUserName)).collect(Collectors.toList());
        Map<String, Object> contextMap = new HashMap<>();//推送到模板的数据
        contextMap.put("emailTitle", emailTitle);
        contextMap.put("fieldNameList", allIssueFieldNameList.get(0));
        contextMap.put("allIssueFieldValueList", allIssueFieldValueList.get(0));
        String emailSubjectTemplate = "templates/utils/sendEmailSubjectTemplate.vm";
        String emailBodyTemplate = "templates/utils/sendEmailBodyTemplate.vm";
        //发送邮件
        sendUserList.forEach(applicationUser -> mailService.sendRenderedMail(applicationUser, new NotificationRecipient(applicationUser), emailSubjectTemplate, emailBodyTemplate, contextMap));
    }

    public void runTransitionById(MutableIssue mutableIssue, ApplicationUser currentUser, Long transitionId, IssueInputParameters issueInputParameters) {
        IssueService.TransitionValidationResult validationResult = issueService.validateTransition(currentUser, mutableIssue.getId(), transitionId.intValue(), issueInputParameters);
        if (validationResult.isValid()) {
            IssueService.IssueResult transitionResult = issueService.transition(currentUser, validationResult);
            if (!transitionResult.isValid()) {
                throw new IllegalArgumentException(transitionResult.getErrorCollection().toString());
            }
        } else {
            throw new IllegalArgumentException(validationResult.getErrorCollection().toString());
        }
    }

    public void runTransitionByName(MutableIssue mutableIssue, ApplicationUser currentUser, String transitionName,IssueInputParameters issueInputParameters) {
        String statusId = mutableIssue.getStatusId();
        JiraWorkflow workflow = ComponentAccessor.getWorkflowManager().getWorkflow(mutableIssue);
        List<Integer> transitionIdList = new ArrayList<>();
        int transitionId = 1;
        Collection<WorkflowTransitionEntry> workflowTransitionEntryCollection = transitionManager.getTransitions(Collections.singletonList(workflow));
        for (WorkflowTransitionEntry workflowTransitionEntry : workflowTransitionEntryCollection) {
            Collection<TransitionEntry> transitionEntryCollection = workflowTransitionEntry.getTransitions();
            String workflowName = workflowTransitionEntry.getWorkflow().getName();
            if (workflow.getName().equals(workflowName)) {
                transitionIdList = transitionEntryCollection.stream().filter(e -> (statusId.equals(e.getFromStatusId() + "") || e.getIsGlobal()) && transitionName.equals(e.getName().trim())).map(TransitionEntry::getTransitionId).collect(Collectors.toList());
                if (transitionIdList.isEmpty() && "批准".equals(transitionName)) {
                    transitionIdList = transitionEntryCollection.stream().filter(e -> (statusId.equals(e.getFromStatusId() + "") || e.getIsGlobal()) && "通过".equals(e.getName().trim())).map(TransitionEntry::getTransitionId).collect(Collectors.toList());
                }
//                for (TransitionEntry transitionEntry : transitionEntryCollection) {
//                    if (statusId.equals(transitionEntry.getFromStatusId() + "") && transitionEntry.getName().contains(transitionName)) {
////                        transitionId = transitionEntry.getTransitionId();
//                        transitionIdList.add(transitionEntry.getTransitionId());
////                        break;
//                    }
//                }
            }
        }
        if (transitionIdList.isEmpty()) {
            throw new IllegalArgumentException("未找到该名称的转换");
        }
//        for (Integer transition : transitionIdList) {
//            transitionId = transition;
//            //        IssueInputParameters issueInputParameters = issueService.newIssueInputParameters();
////        String[] strings = new String[5];
////        if (!ObjectUtils.isEmpty(map)) {
////            for (Map.Entry<Long, String> entry : map.entrySet()) {
////                issueInputParameters.addCustomFieldValue(entry.getKey(),entry.getValue());
////            }
////        }
//            log.error("issue：" + mutableIssue.getId() + mutableIssue.getKey() + "转换ID：" + transitionId);
//            IssueService.TransitionValidationResult validationResult = issueService.validateTransition(currentUser, mutableIssue.getId(), transitionId, issueInputParameters);
//
//            if (validationResult.isValid()) {
//                IssueService.IssueResult issueResult = issueService.transition(currentUser, validationResult);
//                break;
//            } else {
//                ErrorCollection errorCollection = validationResult.getErrorCollection();
//                log.error("转换执行失败：{}", errorCollection.getErrorMessages());
//                throw new IllegalArgumentException(errorCollection.getErrorMessages().toString());
//            }
//
//        }
        Optional<IssueService.TransitionValidationResult> first = transitionIdList.stream()
                .map(e -> issueService.validateTransition(currentUser, mutableIssue.getId(), e, issueInputParameters))
                .filter(e->e.isValid()).findFirst();
        if (first.isPresent()) {
            IssueService.IssueResult issueResult = issueService.transition(currentUser, first.get());
            if (!issueResult.isValid()) {
                log.error("转换执行失败：{}", issueResult.getErrorCollection());
                throw new IllegalArgumentException("转换执行错误");
            }
        } else {
            log.error("转换执行失败：{}", mutableIssue.getKey());
            throw new IllegalArgumentException("不存在可执行的转换");
        }

    }

    /**
     * 694568904853777 1209966251316089
     * 694568904853776 1209966251316088
     * 694568904853775 1209966251316087
     * 694568904853774 1209966251316086
     * 694568904853773 1209966251316085
     * 694568904853772 1209966251316084
     * 694568904853771 1209966251316083
     * 694568904853770 1209966251316082
     * 694568904853769
     * 694568904853768
     * 694568904853767
     * 694568904853766
     * 694568904853765
     * 694568904853764
     * 694568904853763
     * 694568904853762
     * 694568904853761
     * 694568904853760
     * 694568904845568
     * 728513026887552
     * 698781345504576
     * 728570394136449
     * 728570394136448
     * 744778032586336
     * 818391752922176
     * 1187209467542688
     * @param cascadingMap
     * @return
     */
    public List<JiraOptionBean> tranCascadingToList(Map<String, Option> cascadingMap) {

        List<JiraOptionBean> cascadingList = new ArrayList<>();
        Option parentOption = null;
        Option option = null;
        if (!ObjectUtils.isEmpty(cascadingMap)) {
            parentOption = cascadingMap.get(null);
            option = cascadingMap.get("1");
        }
        if (!ObjectUtils.isEmpty(parentOption)) {
            cascadingList.add(new JiraOptionBean(parentOption.getOptionId(), 1L, parentOption.getValue(), "",parentOption.getSequence()));
            if (!ObjectUtils.isEmpty(option)) {
                cascadingList.add(new JiraOptionBean(option.getOptionId(), parentOption.getOptionId(), option.getValue(), "",option.getSequence()));
            }
        }
        return cascadingList;
    }
}
