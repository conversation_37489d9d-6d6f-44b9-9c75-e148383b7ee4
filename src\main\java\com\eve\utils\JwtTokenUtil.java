package com.eve.utils;

import cn.hutool.core.bean.BeanUtil;
import com.atlassian.jira.component.ComponentAccessor;
import com.atlassian.jira.user.ApplicationUser;
import com.eve.beans.JwtPayLoad;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.ExpiredJwtException;
import io.jsonwebtoken.JwtException;
import io.jsonwebtoken.Jwts;

import java.util.Date;

public class JwtTokenUtil {
    private static  String jwtSecret = "ey4zyggtnrojuq0eyy8z76kgeelocybt";
    public static ApplicationUser getApplicationUserByToken(String token) {

            if (!checkToken(token)) {
                throw new IllegalArgumentException("token验证失败");
            }
            if (isTokenExpired(token)) {
                throw new IllegalArgumentException("token过期");
            }
            JwtPayLoad jwtPayLoad = JwtTokenUtil.getJwtPayLoad(token);
//        ApplicationUser applicationUser = ComponentAccessor.getUserManager().getUserByName(Constant.dladminUserName);
        return ComponentAccessor.getUserManager().getUserByName(jwtPayLoad.getAccount());
    }
    private static Claims getClaimsFromToken(String token) {
        return Jwts.parser()
                .setSigningKey(jwtSecret)
                .parseClaimsJws(token)
                .getBody();
    }

    public static Boolean checkToken(String token) {
        try {
            getClaimsFromToken(token);
            return true;
        } catch (JwtException jwtException) {
            return false;
        }
    }

    public static Boolean isTokenExpired(String token) {
        try {
            Claims claims = getClaimsFromToken(token);
            final Date expiration = claims.getExpiration();
            return expiration.before(new Date());
        } catch (ExpiredJwtException expiredJwtException) {
            return true;
        }
    }

    public static JwtPayLoad getJwtPayLoad(String token) {
        Claims claims = getClaimsFromToken(token);
        return BeanUtil.mapToBean(claims, JwtPayLoad.class, false);
    }
}
