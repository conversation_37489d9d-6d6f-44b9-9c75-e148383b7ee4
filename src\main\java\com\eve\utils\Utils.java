package com.eve.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.atlassian.jira.component.ComponentAccessor;
import com.atlassian.jira.config.properties.APKeys;
import com.atlassian.jira.config.util.AttachmentPathManager;
import com.atlassian.jira.datetime.DateTimeFormatter;
import com.atlassian.jira.datetime.DateTimeStyle;
import com.atlassian.jira.issue.Issue;
import com.atlassian.jira.issue.attachment.Attachment;
import com.atlassian.jira.issue.context.ProjectContext;
import com.atlassian.jira.issue.customfields.option.Option;
import com.atlassian.jira.issue.customfields.option.Options;
import com.atlassian.jira.issue.fields.CustomField;
import com.atlassian.jira.user.ApplicationUser;
import com.eve.beans.UpdateCustomFiledBean;
import com.google.gson.Gson;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.httpclient.HttpException;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.util.ObjectUtils;
import sun.misc.BASE64Encoder;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import javax.ws.rs.core.Response;
import java.io.*;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.net.*;
import java.security.SecureRandom;
import java.sql.Timestamp;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


public class Utils {
    private static final Logger log = LoggerFactory.getLogger(Utils.class);
    private static String localIp = "";

    /**
     *  选择字段ID获取
     * @param entryKey
     * @param entryValue
     * @return optionId
     */
    public static Long getSelectOptionId(String entryKey, Long entryValue) {
        Long optionId = -1L;
        Option option = ComponentAccessor.getOptionsManager().findByOptionId(entryValue);
        if (option != null) {
            return option.getOptionId();
        }
        switch (entryKey) {
            case "reviewResult1":
                optionId = getReviewResult1Map().entrySet().stream().filter(e -> e.getValue().equals(entryValue)).map(Map.Entry::getKey).findFirst().orElse(-1L);
                break;
            case "reviewResult2":
                optionId = getReviewResult2Map().entrySet().stream().filter(e -> e.getValue().equals(entryValue)).map(Map.Entry::getKey).findFirst().orElse(-1L);
                break;
            case "projectLevel":
                optionId = getProjectLevelMap().entrySet().stream().filter(e -> e.getValue().equals(entryValue)).map(Map.Entry::getKey).findFirst().orElse(-1L);
                break;
            default:
                break;
        }
        return optionId;
    }

    /**
     * 选择字段ID获取
     *
     * @param projectId 项目ID
     * @param customFiled 字段
     * @param entryValue 字段值
     * @return optionId
     */
    public static Long getOptionIdByName(Long projectId, CustomField customFiled, String entryValue) {
        Option option = getOptionByName(projectId, customFiled, entryValue);
        return option == null ? -1L : option.getOptionId();
    }

    public static Option getOptionByName(Long projectId, CustomField customFiled, String entryValue) {
        Options options = customFiled.getOptions("", new ProjectContext(projectId, ComponentAccessor.getProjectManager()));
        return options.stream().filter(e -> e.getValue().equals(entryValue)).findFirst().orElse(null);
    }

    public static UpdateCustomFiledBean getCreateIssueType(int isOnline, String issueType) {
        Long projectId = 0L;
        String issueTypeId = Constant.resultReviewRequestIssueTypeId + "";
        String[] split = issueType.split(":");
        if ("productRZ".equals(split[0])) {//productRZ
            projectId = isOnline == 1 ? 1L : 0L;
            if ("testProject".equals(split[1])){
                issueTypeId = "testProject";
            }else if ("returnSample".equals(split[1])){
                issueTypeId = "returnSample";
            }
        }
        return new UpdateCustomFiledBean.Builder().projectId(projectId).issueType(issueTypeId).build();
    }

    public static Map<Long, Long> getProjectReviewListTypeMap() {
        return new HashMap<Long, Long>() {{
            put(1L, 11933L);//所级项目管理员确认
            put(2L, 10625L);//所长审核
            put(3L, 11905L);//项目管理部审核
            put(4L, 10745L);//全院立项评审
            put(5L, 0L);//工程师立项项目
        }};
    }
    //立项评审结果
    public static Map<Long, Long> getReviewResultMap() {
        return new HashMap<Long, Long>() {{
            put(18633L, 1L);//通过
            put(10885L, 2L);//不通过
            put(10884L, 3L);//再确认
        }};
    }
    //一次评审结果
    public static Map<Long, Long> getReviewResult1Map() {
        return new HashMap<Long, Long>() {{
            put(22116L, 1L);//通过
            put(22117L, 2L);//不通过
            put(22118L, 3L);//再确认
        }};
    }
    //二次评审结果
    public static Map<Long, Long> getReviewResult2Map() {
        return new HashMap<Long, Long>() {{
            put(10650L, 1L);//通过
            put(10651L, 2L);//不通过
        }};
    }
    public static Map<Long, Long> getProjectLevelMap() {
        return new HashMap<Long, Long>() {{
//            put(18748L, 1L);//S+
            put(12526L, 2L);//S
            put(10863L, 3L);//A
            put(10864L, 4L);//B
//            put(18950L, 5L);//C
        }};
    }
    public static Map<Long, String> getAffiliatedPlatformCodeMap() {
        return new HashMap<Long, String>() {{
            put(22178L, "CL");//关键原材料特殊特性识别
            put(22179L, "HB");//绿色环保新材料与新工艺
            put(22180L, "DJ");//高效电极工艺开发
            put(22181L, "ZZ");//电池成型组装工艺开发
            put(22286L, "HC");//电池注液化成工艺开发
            put(22289L, "SX");//电池老化与筛选工艺开发
            put(22182L, "JG");//创新电池结构技术
            put(22183L, "SJ");//电池原理与功能设计平台
            put(22184L, "CJ");//电池应用场景技术研究
            put(22223L, "SZ");//研发数字化平台
        }};
    }
    public static Map<String, Long> getReviewListCustomFiledMap() {
        return new HashMap<String, Long>() {{
            put("reviewResult1", Constant.reviewResult1CustomFieldId);
            put("reviewOpinion1", Constant.reviewOpinion1CustomFieldId);
            put("reviewResult2", Constant.reviewResult2CustomFieldId);
            put("reviewOpinion2", Constant.reviewOpinion2CustomFieldId);
            put("projectName", Constant.projectNameCustomFieldId);
            put("projectCate", Constant.projectCateCustomFieldId);
            put("affiliatedPlatform", Constant.affiliatedPlatformCustomFieldId);
            put("platformCate", Constant.platformCateCustomFieldId);
            put("departmentCate", Constant.subDepartCustID);
            put("jmDepartment", Constant.jmDepartmentCustID);
            put("projectLevel", Constant.projectLevelCustomFieldId);
            put("projectPurpose", Constant.projectPurposeCustomFieldId);
            put("projectTarget", Constant.projectTargetCustomFieldId);
            put("projectBackGround", Constant.projectBackGroundCustomFieldId);
            put("researchContent", Constant.researchContentCustomFieldId);
            put("directLeaderId", Constant.directLeaderCustomFieldId);
            put("inspectorGeneralId", Constant.inspectorGeneralCustomFieldId);
            put("platformLeader", Constant.platformLeaderCustomFieldId);
            put("platformLeaderId", Constant.platformLeaderCustomFieldId);
            put("deputyHeadOfTheInstituteId", Constant.deputyHeadOfTheInstituteCustomFieldId);
            put("headOfTheInstituteId", Constant.headOfTheInstituteCustomFieldId);
            put("presidentId", Constant.presidentCustomFieldId);
            put("projectManagerId", Constant.projectManagerCustomFieldId);
            put("reviewMonth", Constant.reviewMonthCustomFieldId);
            put("topicLeader", Constant.topicLeaderCustomFieldId);
//            put("projectLeaderId", Constant.topicLeaderCustomFieldId);
            put("isSystemCall", Constant.isSystemCallCustomFieldId);//接口调用标志
            put("topicType", Constant.topicTypeCustomFieldId);//课题类型
            put("submitter", Constant.submitterCustomFieldId);//申请人
            put("departmentStr", Constant.departmentStrCustomFieldId);//申请人部门

            //测试失效
            put("productDepartment", Constant.productDepartmentCustomFieldId);
            put("productName", Constant.productNameCustomFieldId);
            put("businessId", Constant.businessIdCustomFieldId);
            put("cellCode", Constant.cellCodeCustomFieldId);
            put("folderNo", Constant.folderNoCustomFieldId);
            put("testProjectName", Constant.testProjectNameCustomFieldId);
            put("cellNum", Constant.cellNumCustomFieldId);
            put("testType", Constant.testTypeCustomFieldId);
            put("laboratoryId", Constant.laboratoryCustomFieldId);
            put("folderUserAccount", Constant.folderUserCustomFieldId);
            put("cellBatch", Constant.cellBatchCustomFieldId);
            put("testCate", Constant.testCateCustomFieldId);
            put("failureCate", Constant.failureCateCustomFieldId);
            put("testFailureDescription", Constant.testFailureDescriptionCustomFieldId);
            put("failureAnalysisContent", Constant.failureAnalysisContentCustomFieldId);
            put("stageFileComplete", Constant.stageFileCompleteCustomFieldId);
            put("causeAnalysis", Constant.causeAnalysisCustomFieldId );
            put("tempMeasures", Constant.tempMeasuresCustomFieldId );
            put("longTermMeasures", Constant.longTermMeasuresCustomFieldId );
            put("resultVerification", Constant.resultVerificationCustomFieldId );

            put("sampleManager", Constant.sampleManagerCustomFieldId);
            put("laboratoryResponsible", Constant.laboratoryResponsibleCustomFieldId);
            put("departmentManager", Constant.departmentManagerCustomFieldId);
            put("productManager", Constant.productManagerCustomFieldId);
            put("productMajordomo", Constant.productMajordomoCustomFieldId);
            put("DQE", Constant.DQECustomFieldId);
            put("headOfTheInstitute", Constant.headOfTheInstituteCustomFieldId);
            put("vicePresident", Constant.vicePresidentCustomFieldId);
            put("president", Constant.presidentCustomFieldId);
            put("countersigner", Constant.countersignerCustomFieldId);

            put("stockLocate", Constant.stockLocateCustomFieldId);
            put("faResponsible", Constant.faResponsibleCustomFieldId);
            put("faChargeAccount", Constant.faResponsibleCustomFieldId);
            put("faDeadline", Constant.faDueDateCustomFieldId);


            //成果评审
            put("gainPresidentScore", Constant.gainPresidentScoreCustomFieldId);
            put("gainPresidentOpinion", Constant.gainPresidentOpinionCustomFieldId);
            put("gainVicePresidentScore", Constant.gainVicePresidentScoreCustomFieldId);
            put("gainVicePresidentOpinion", Constant.gainVicePresidentOpinionCustomFieldId);

 //            put("projectCate", Constant.projectCateCustomFieldId);//项目分类
            put("requireDepartment", Constant.requireDepartmentCustomFieldId);//需求方部门长
            put("resultCate", Constant.achievementCateCustomFieldId);//成果分类
//            put("productCate", Constant.productCateCustomFieldId);//产品类别
//            put("affiliatedPlatform", Constant.affiliatedPlatformCustomFieldId);//归属平台
//            put("platformCate", Constant.platformCateCustomFieldId);//归属平台
            put("department", Constant.subDepartCustID);//部门
            put("projectManager", Constant.projectManagerCustomFieldId);
            put("directLeader", Constant.directLeaderCustomFieldId);
            put("requestReason", Constant.requestReasonCustomFieldId);

            //BOM核算
            put("usageDescription", Constant.usageDescriptionCustomFieldId);//用途说明
            put("productStatus", Constant.productStateCustId);//产品状态
            put("ratedCapacity", Constant.ratedCapacityCustomFieldId);//额定容量
            put("ratedVoltage", Constant.ratedVoltageCustomFieldId);//额定电压
            put("ratedEnergy", Constant.ratedEnergyCustomFieldId);//额定能量
            put("bomCode", Constant.bomCodeCustId);//BOM编号
            put("customerType", Constant.customerTypeCustomFieldId);//客户类型
            put("positiveElectrodeSystem", Constant.positiveElectrodeSystemCustomFieldId);//正极体系
            put("accountingCode", Constant.accountingCodeCustomFieldId);//核算代码
            put("submitLink", Constant.submitLinkCustomFieldId);//提交链接
            put("dept", Constant.subDepartCustID);//部门
            put("majordomo", Constant.majordomoCustId);//部门长/总监

        }};
    }

    public static Map<String, Long> getCascadingSelectCustomFiledMap() {
        return new HashMap<String, Long>() {{
            put("projectCate", Constant.projectCateCustomFieldId);//项目分类
            put("department", Constant.subDepartCustID);//部门
            put("resultCate", Constant.achievementCateCustomFieldId);//成果分类
            put("productCate", Constant.productCateCustomFieldId);//产品类别
            put("affiliatedPlatform", Constant.affiliatedPlatformCustomFieldId);//归属平台
            put("platformCate", Constant.platformCateCustomFieldId);//平台分类
        }};
    }

    public static Map<String, Long> getSelectCustomFiledMap() {
        return new HashMap<String, Long>() {{
            put("vCylinderProductDepartment",Constant.vCylinderProductDepartmentCustId);//V圆柱产品部门

        }};
    }

    /**
     * 平台课题待评审不通过状态
     * @return
     */
    public static List<String> getUnPassStatusList() {
        return Arrays.asList(
                "10748",//结束
                "11115"//立项不通过
        );
    }

    /**
     * 平台课题待评审状态
     * @return
     */
    public static List<String> getToBeReviewedList() {
        return Arrays.asList(
                "11112",//预立项审核
//                "11803",//项目信息填写
//                "11801",//财务确认成本中心
//                "10916",//立项材料-待提交
//                "10629",//直属领导审核
//                "11024",//总监审核
                "11114",//预立项-修改
//                "10625",//副院长/所长审核
//                "11905",//项目管理部-审核
//                "10754",//立项评审
                "11907"//立项信息修改
        );
    }

    public static Map<String, Long> getResultCustomFiledMap() {
        return new HashMap<String, Long>() {{
//            put("projectCate", Constant.projectCateCustomFieldId);//项目分类
            put("requireDepartment", Constant.requireDepartmentCustomFieldId);//需求方部门长
            put("resultCate", Constant.achievementCateCustomFieldId);//成果分类
//            put("productCate", Constant.productCateCustomFieldId);//产品类别
            put("affiliatedPlatform", Constant.affiliatedPlatformCustomFieldId);//归属平台
            put("platformCate", Constant.platformCateCustomFieldId);//归属平台
        }};
    }

    public static ApplicationUser getApplicationUserByName(String name) {
        return ComponentAccessor
                .getUserManager()
                .getUserByName(name);
    }

    public static CustomField getCustomFieldByID(Long id) {
        return ComponentAccessor
                .getCustomFieldManager()
                .getCustomFieldObject(id);
    }

    public static Long diffDate(Date acturalDate, Date planedDate) {
        if (planedDate == null) {
            return 0L;
        }
        if (acturalDate != null) {
            double diffDay = (acturalDate.getTime() - planedDate.getTime()) / 86400000;
            diffDay = Math.ceil(diffDay);

            return new Long((long) diffDay);
        }

        Date now = new Date();
        double diffDay = (now.getTime() - planedDate.getTime()) / 86400000;
        if (diffDay < 0) {
            return 0L;
        }

        return new Long((long) diffDay);
    }

    public static String plusDates(int num, String newDate) throws ParseException {
        SimpleDateFormat f = new SimpleDateFormat("yyyy-MM-dd");
        Date d = new Date(f.parse(newDate).getTime() + 24 * 3600 * 1000 * num);
        return f.format(d);
    }

    public static String getDateStr(String dateStr) {
        LocalDate startLocalDate = LocalDate.parse(dateStr);
        Date parsedDateTime = Date.from(startLocalDate.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
        DateTimeFormatter dateTimeFormatter = ComponentAccessor.getComponent(DateTimeFormatter.class);
        dateTimeFormatter = dateTimeFormatter.forLoggedInUser();
        return dateTimeFormatter.withStyle(DateTimeStyle.DATE_PICKER).format(parsedDateTime);
    }

    public static String getDateFormat(java.sql.Timestamp timestamp) {
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");//定义格式，不显示毫秒
        return df.format(timestamp);
    }

    public static String getLoginToken(String userName) {
        Map<String, String> header = new HashMap<>();
        Map<String, String> dataMap = new HashMap<>();
        dataMap.put("username", userName);
        dataMap.put("sys", "OA");
        String password = "OA1234567";
        String keyrules = "OA" + ":" + password + ":" + userName;
        String backLog = "";
        String token = "";
        try {
            dataMap.put("password", Utils.encryptByJira("083779", keyrules));
            String data = JSON.toJSONString(dataMap);

            backLog = HttpUtils.doPostBackLog("http://jira.evebattery.com/rest/sso/1.0/user/createToken", data, header);
            JSONObject jsonObject = JSON.parseObject(backLog);
            token = jsonObject.getString("token");
//            JSONObject res = JSONObject.parseObject(response.getBody());
        } catch (Exception e) {
            errInfo(e);
        }

        return token;
    }

    public static String encryptByJira(String content, String encodeRules) {
        String aesEncode = null;
        try {
            //1.构造密钥生成器，指定为AES算法,不区分大小写
            KeyGenerator keygen = KeyGenerator.getInstance("AES");
            //2.根据ecnodeRules规则初始化密钥生成器
            //生成一个128位的随机源,根据传入的字节数组
            SecureRandom secureRandom = SecureRandom.getInstance("SHA1PRNG");
            secureRandom.setSeed(encodeRules.getBytes());
            keygen.init(256, secureRandom);
            //3.产生原始对称密钥
            SecretKey original_key = keygen.generateKey();
            //4.获得原始对称密钥的字节数组
            byte[] raw = original_key.getEncoded();
            //5.根据字节数组生成AES密钥
            SecretKey key = new SecretKeySpec(raw, "AES");
            //6.根据指定算法AES自成密码器
            Cipher cipher = Cipher.getInstance("AES");
            //7.初始化密码器，第一个参数为加密(Encrypt_mode)或者解密解密(Decrypt_mode)操作，第二个参数为使用的KEY
            cipher.init(Cipher.ENCRYPT_MODE, key);
            //8.获取加密内容的字节数组(这里要设置为utf-8)不然内容中如果有中文和英文混合中文就会解密为乱码
            byte[] byte_encode = content.getBytes("utf-8");
            //9.根据密码器的初始化方式--加密：将数据加密
            byte[] byte_AES = cipher.doFinal(byte_encode);
            //10.将加密后的数据转换为字符串
            //这里用Base64Encoder中会找不到包
            //解决办法：
            //在项目的Build path中先移除JRE System Library，再添加库JRE System Library，重新编译后就一切正常了。
            //aesEncode=new Base64().encodeToString(byte_AES);
            aesEncode = new BASE64Encoder().encode(byte_AES);
            //11.将字符串返回
        } catch (Exception e) {
            e.printStackTrace();
        }
        //        log.error("AESEncode error,content:"+content);
        //如果有错就返加null
        return aesEncode;
    }

    public static List getSublist(List list, int fetchSize, int idx) {
        if (list.size() == 0) {
            return list;
        }
        if (list.size() <= fetchSize) {
            return list;
        }
        if (list.size() >= fetchSize * (idx)) {
            return list.subList(fetchSize * (idx - 1), fetchSize * (idx));
        }
        if (list.size() >= fetchSize * (idx - 1)) {
            return list.subList(fetchSize * (idx - 1), list.size());
        } else {
            return new ArrayList();
        }
    }

    //转换yyyy-MM-dd格式的时间字符串为timeStamp
    public static Timestamp getTimestamp(String date) throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date d = sdf.parse(date);
        return new Timestamp(d.getTime());
    }

    public static Timestamp getTimestamp(Date date) throws ParseException {
        return new Timestamp(date.getTime());
    }

    public static String randomString32() {
        String aa = RandomStringUtils.randomNumeric(1000);
        return aa;
    }

    public void quchong(List list) {
        HashSet hs2 = new HashSet<Long>(list);
        list.clear();
        list.addAll(hs2);
    }

    public static String parseIssueKey(String value) {
        String temp = value.trim();
        if (!temp.startsWith("GEARS-")) {
            return null;
        }

        //GEARS-(0-9)$
        Pattern p = Pattern.compile("GEARS-[0-9]*");
        Matcher m = p.matcher(temp);
        if (m.find()) {
            return m.group();
        }
        return null;
    }

    public static String removeIssueKey(String value) {
        if (!value.startsWith("GEARS-")) {
            return value;
        }

        String key = parseIssueKey(value);
        if (StringUtils.isEmpty(key)) {
            return value;
        }
        String text = value.replaceAll(key, "");
        text = text.trim();
        if ("-".equalsIgnoreCase(text)) {
            text = value.replaceFirst("-", "");
        }
        return text;
    }

    public static boolean isIssueKey(String value) {
        String temp = value.trim();
        if (!temp.startsWith("GEARS-")) {
            return false;
        }

        //验证用户密码:^[a-zA-Z]\w{5,17}$ 正确格式为：以字母开头，长度在6-18之间，只能包含字符、数字和下划线。
        //GEARS-(0-9)$
        Pattern p = Pattern.compile("GEARS-[0-9]*");
        Matcher m = p.matcher(temp);
        return m.matches();
    }

    public static boolean isEmail(String email) {
        //
        Pattern p = Pattern.compile("^[a-z0-9]+([._\\\\-]*[a-z0-9])*@([a-z0-9]+[-a-z0-9]*[a-z0-9]+.){1,63}");
        // Pattern p = Pattern.compile("^([a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+(\\.([a-zA-Z0-9_-])+)+$");
        Matcher m = p.matcher(email);
        return m.matches();
    }

    public static double paserHour(Long value) {
        if (value == null) {
            return 0D;
        }
        return Utils.o2d((Utils.o2d(value) / (60 * 60)), 2);
    }

    public static double paserHour(Double value) {
        if (value == null) {
            return 0D;
        }
        return Utils.o2d((Utils.o2d(value) / (60 * 60)), 2);
    }


    public static double paserDay(Long value) {
        if (value == null) {
            return 0D;
        }
        return Utils.o2d((Utils.o2d(value) / (60 * 60 * 8)), 2);
    }

    public static double paserDay(Long value, Double workhour) {
        if (value == null) {
            return 0D;
        }
        if (workhour == null || workhour < 0D) {
            workhour = 8d;
        }
        return Utils.o2d((Utils.o2d(value) / (60 * 60 * workhour)), 2);
    }

    public static double paserDay(Double value, Double workhour) {
        if (value == null) {
            return 0D;
        }
        if (workhour == null || workhour < 0.1D) {
            workhour = 8d;
        }

        return Utils.o2d((Utils.o2d(value) / (60 * 60 * workhour)), 2);
    }


    public static String getOrderSqlStr(String sortdata, boolean needOrderBy) {
        StringBuffer sql = new StringBuffer("");
        if (Utils.o2s(sortdata).length() > 0) {
            sortdata = sortdata.replace('[', ' ').replace(']', ' ').trim();
            String[] sa = sortdata.split(",");
            if (sortdata.length() > 0 && sa.length > 0) {
                if (needOrderBy) sql.append(" order by ");
                for (String s : sa) {
                    String[] ss = s.split("_");
                    sql.append(ss[0]).append(" ").append(ss[1]).append(",");
                }
                if (sql.toString().endsWith(",")) {
                    sql.deleteCharAt(sql.length() - 1);
                }
            }
        }
        return sql.toString();
    }


    public static String o2s(Object o) {
        if (null == o) {
            return "";
        }
        if (o instanceof Long) {
            return ((Long) o).toString();
        }
        if (o instanceof Integer) {
            return ((Integer) o).toString();
        }
        if (o instanceof Double) {
            return ((Double) o).toString();
        }
        if (o instanceof String) {
            return ((String) o).trim();
        }
        return o.toString().trim();
    }

    public static String o2s(Object o, String dValue) {
        if (null == o) {
            return dValue;
        }
        if (o instanceof String) {
            return ((String) o).trim();
        }
        return o.toString().trim();
    }


    public static int o2i(Object o) {
        int r = 0;
        try {
            if (o instanceof Double) {
                r = ((Double) o).intValue();
            } else if (o instanceof Long) {
                r = ((Long) o).intValue();
            } else if (o instanceof Integer) {
                r = ((Integer) o).intValue();
            } else if (o instanceof BigDecimal) {
                r = ((BigDecimal) o).intValue();
            } else {
                r = Integer.valueOf(o2s(o));
            }
        } catch (Exception e) {

        }
        return r;
    }


    public static boolean o2b(Object o) {
        boolean r = false;
        try {
            if (o instanceof Long) {
                if (((Long) o).intValue() != 0) {
                    r = true;
                }
            } else if (o instanceof Integer) {
                if (((Integer) o).intValue() != 0) {
                    r = true;
                }
            } else if (o instanceof BigDecimal) {
                if (((BigDecimal) o).intValue() != 0) {
                    r = true;
                }
            } else if (o instanceof Boolean) {
                r = ((Boolean) o).booleanValue();
            } else if (o instanceof String) {
                r = "true".equalsIgnoreCase((String) o);
            }
        } catch (Exception e) {
            r = false;
        }
        return r;
    }


    public static long o2l(Object o) {
        long r = 0;
        try {
            if (o instanceof Double) {
                r = ((Double) o).longValue();
            } else if (o instanceof Long) {
                r = ((Long) o).longValue();
            } else if (o instanceof Integer) {
                r = ((Integer) o).longValue();
            } else if (o instanceof BigDecimal) {
                r = ((BigDecimal) o).longValue();
            } else {
                r = Long.parseLong(o2s(o));
            }
        } catch (Exception e) {

        }
        return r;
    }


    public static double o2d(Object o) {
        double r = 0;
        try {
            String s = o2s(o);
            if (s.indexOf(',') > 0) {
                s = s.replaceAll(",", "");
            }
            r = Double.parseDouble(s);
        } catch (Exception e) {
        }
        return r;
    }

    public static String getDateTimeStr(String dateStr) {
        LocalDateTime startLocalDate = LocalDateTime.parse(dateStr, java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        Date parsedDateTime = Date.from(startLocalDate.atZone(ZoneId.systemDefault()).toInstant());
        DateTimeFormatter dateTimeFormatter = ComponentAccessor.getComponent(DateTimeFormatter.class);
        dateTimeFormatter = dateTimeFormatter.forLoggedInUser();
        return dateTimeFormatter.withStyle(DateTimeStyle.DATE_PICKER).format(parsedDateTime);
    }

    /**
     * @param o
     * @return
     * @throws Exception
     */
    public static java.sql.Timestamp o2sqlDate(Object o) throws Exception {
        if (o == null) {
            return null;
        }
        return new java.sql.Timestamp(o2utilDate(o).getTime());
    }

    public static java.sql.Date o2dbSqlDate(Object o) {
        if (o == null) {
            return null;
        }
        return new java.sql.Date(o2utilDate(o).getTime());
    }

    public static String date2s(Date date) {
        String dateStr = null;
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        dateStr = sdf.format(date);
        return dateStr;

    }

    /**
     * @param o
     * @return
     * @throws Exception
     */
    public static Date o2utilDate(Object o) {
        try {
            if (o == null) {
                return null;
            }
            if (o instanceof String) {
                String v = o2s(o);
                SimpleDateFormat sdf = null;
                if (vl(v) && v.length() == 6) { //yyyyMM
                    sdf = new SimpleDateFormat("yyyyMM");
                    return sdf.parse(v);
                } else if (vl(v) && v.length() == 8) {  //yyyyMMdd
                    sdf = new SimpleDateFormat("yyyyMMdd");
                    return sdf.parse(v);
                } else if (v.indexOf('-') > 0 && v.length() == 10) { //yyyy-MM-dd
                    sdf = new SimpleDateFormat("yyyy-MM-dd");
                    return sdf.parse(v);
                } else if (v.indexOf('-') > 0 && v.indexOf(':') > 0 && v.length() == 19) {
                    //yyyy-MM-dd HH:mm:ss
                    sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    return sdf.parse(v);
                } else if (v.indexOf('-') > 0 && (v.length() == 9 || v.length() == 8)) {
                    //yyyy-M-dd
                    sdf = new SimpleDateFormat("yyyy-M-dd");
                    return sdf.parse(v);
                } else {
                    //throw new Exception("未实现的日期格式: "+o);
                    return null;
                }
            } else if (o instanceof Date
                    || o instanceof java.sql.Date
                    || o instanceof java.sql.Timestamp) {
                return (Date) o;
            }
        } catch (ParseException e) {
            // PrintMsgUtils.putExceptionMsg(e);
        }
        return null;

    }

    public static int o2z(int i) {
        if (i > 0) {
            return i;
        } else {
            return -i;
        }
    }

    public static double o2d(Object o, int scale) {
        if (scale < 0) {
            scale = 0;
        }
        DecimalFormat df = new DecimalFormat("0.##########");
        double t = new BigDecimal(o2d(o)).divide(new BigDecimal(1),
                scale, BigDecimal.ROUND_HALF_UP).doubleValue();
        String s = df.format(t).toString();
        return o2d(s);
    }

    public static String format2Scare(Object o) {
        DecimalFormat df = new DecimalFormat("0.00");
        return df.format(o);
    }


    public static boolean vd(Object o) {
        try {
            Double.parseDouble(o2s(o));
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    public static boolean vi(Object o) {
        try {
            Integer.parseInt(o2s(o));
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    public static boolean vl(Object o) {
        try {
            Long.parseLong(o2s(o));
            return true;
        } catch (Exception e) {
            return false;
        }
    }


    public static double dr(double v, int scale) {
        if (scale < 0) {
            scale = 0;
        }
        return new BigDecimal(v).divide(new BigDecimal(1),
                scale, BigDecimal.ROUND_HALF_UP).doubleValue();
    }


    public static boolean doubleEquals(double d1, double d2, double vc) {
        if ((d1 - d2 > 0) && d1 - d2 < vc) {
            return true;
        }
        if ((d1 - d2 < 0) && d2 - d1 < vc) {
            return true;
        }
        return false;
    }


    public static String mergerRepeat(String org, String repeatChar) {
        try {
            return o2s(org.replaceAll(repeatChar + repeatChar + "+", repeatChar));
        } catch (Exception e) {
            return org;
        }
    }


    public static boolean hashSuperClass(Class thisClass, Class superClass) {
        if (thisClass == superClass) {
            return true;
        }
        Class sc = thisClass.getSuperclass();
        boolean flag = false;
        while (sc != null) {
            if (sc == superClass) {
                flag = true;
                break;
            }
            sc = sc.getSuperclass();
        }
        return flag;
    }


    public static boolean isImplementedInterface(Class thisClass, Class implementedInterface) {
        if (thisClass == implementedInterface) {
            return true;
        }
        Class sc = thisClass;
        Class[] iis = null;
        boolean flag = false;
        while (sc != null) {
            iis = sc.getInterfaces();
            for (int i = 0; i < iis.length; i++) {
                if (iis[i] == implementedInterface) {
                    flag = true;
                    break;
                }
            }
            if (flag) {
                break;
            }
            sc = sc.getSuperclass();
        }
        return flag;
    }

    public static Field findField(Class ownerClass, String fieldName) {
        Field field = null;
        while (ownerClass != null) {
            try {
                field = ownerClass.getDeclaredField(fieldName);
            } catch (Exception e) {
            }
            if (field != null) {
                break;
            }
            ownerClass = ownerClass.getSuperclass();
        }
        return field;
    }


    public static Field[] findFields(Class ownerClass, boolean findSuperClassField) {
        Field[] fields = null;
        ArrayList al = new ArrayList();
        while (ownerClass != null) {
            try {
                fields = ownerClass.getDeclaredFields();
                if (fields != null && fields.length > 0) {
                    al.addAll(Arrays.asList(fields));
                }
            } catch (Exception e) {
                ;
            }
            if (findSuperClassField) {
                ownerClass = ownerClass.getSuperclass();
            } else {
                break;
            }
        }
        return (Field[]) al.toArray(new Field[al.size()]);
    }

    public static Method findMethod(Class methodOwner, String methodName, Class[] argTypes) {
        Method method = null;
        do {
            if (methodOwner == null) {
                break;
            }
            Method[] methods = methodOwner.getMethods();
            Class[] gts = null;
            ArrayList list = new ArrayList();
            boolean found = false;
            Field typeField = null;
            for (int i = 0; i < methods.length; i++) {
                method = methods[i];
                gts = method.getParameterTypes();
                if (method.getName().equals(methodName)
                        && gts.length == argTypes.length) {
                    list.add(method);
                    found = true;
                    for (int j = 0; j < gts.length; j++) {
                        if (gts[j] != argTypes[j]) {
                            found = false;
                            break;
                        }
                    }
                    if (found) {
                        break;
                    }
                }
            }
            if (!found) {
                int fitCnt = 0;
                Iterator ir = list.iterator();
                while (ir.hasNext()) {
                    method = (Method) ir.next();
                    fitCnt = 0;
                    gts = method.getParameterTypes();
                    for (int j = 0; j < gts.length; j++) {
                        found = false;
                        try {
                            typeField = argTypes[j].getField("TYPE");
                            if (typeField != null
                                    && gts[j] == typeField.get(argTypes[j])) {
                                fitCnt++;
                                found = true;
                            }
                        } catch (Exception e) {
                        }
                        if (!found && (hashSuperClass(argTypes[j], gts[j])
                                || isImplementedInterface(argTypes[j], gts[j]))) {
                            fitCnt++;
                        }
                    }
                    if (fitCnt == gts.length) {
                        found = true;
                        break;
                    }
                }
                if (!found) {
                    method = null;
                }
            } else {
                list.clear();
                list = null;
            }
            if (method != null) {
                method.setAccessible(true);
            }
            /*else
            {
                method = findMethod(methodOwner.getSuperclass(), methodName, argTypes);
            }*/
        }
        while (false);
        return method;
    }

    public static Method findMethod(String name, Object[] args, Object target) throws Exception {
        return findMethod(name, args, target.getClass().getMethods());
    }

    public static Method findMethod(String name, Object[] args, Method[] ms) throws Exception {
        return findMethod(name, args, ms, null);
    }

    public static Method findMethod(String name, Object[] args, Method[] ms, List newArgs) throws Exception {
        Class[] ats;
        int fc;
        Class pc, ac;
        Field f;
        Method method = null;
        for (Method m : ms) {
            if (!m.getName().equals(name)) {
                continue;
            }
            ats = m.getParameterTypes();
            if (ats.length > args.length) {
                continue;
            }
            fc = 0;
            for (int i = 0; i < ats.length; i++) {
                pc = ats[i];
                if (null == args[i]) {
                    fc++;
                    continue;
                }
                ac = args[i].getClass();
                if (args[i] instanceof Number) {
                    f = ac.getField("TYPE");
                    f.setAccessible(true);
                    if (f.get(ac) == pc) {
                        fc++;
                        continue;
                    }
                }
                if (pc != ac) {
                    break;
                }
                fc++;
            }
            if (fc == ats.length && fc == args.length) {
                method = m;
                break;
            }
            fc = 0;
            for (int i = 0; i < ats.length; i++) {
                pc = ats[i];
                if (null == args[i]) {
                    fc++;
                    continue;
                }
                ac = args[i].getClass();
                if (args[i] instanceof Number) {
                    f = ac.getField("TYPE");
                    f.setAccessible(true);
                    if (f.get(ac) == pc) {
                        fc++;
                        continue;
                    }
                }
                if ((pc != ac) && (!Utils.isImplementedInterface(ac, pc)
                        && !Utils.hashSuperClass(ac, pc))) {
                    break;
                }
                fc++;
            }
            if (fc == ats.length && fc == args.length) {
                method = m;
                break;
            }
            if (null != newArgs && ats.length - 1 == fc &&
                    Object[].class == ats[ats.length - 1]) {
                method = m;
                newArgs.clear();
                for (int j = fc; j < args.length; j++) {
                    newArgs.add(args[j]);
                }
                Object[] lastArg = newArgs.toArray();
                newArgs.clear();
                for (int j = 0; j < fc; j++) {
                    newArgs.add(args[j]);
                }
                newArgs.add(lastArg);
                break;
            }
        }
        return method;
    }

    /**
     * @param basicDate yyyy-MM-dd
     * @param n
     * @return yyyy-MM-dd
     */
    public static String afterDate(String basicDate, int n) {
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        Date tmpDate = null;
        try {
            tmpDate = df.parse(basicDate);
            long nDay = ((tmpDate.getTime() / (24 * 60 * 60 * 1000) + n) * (24 * 60 * 60 * 1000));
            tmpDate.setTime(nDay);
        } catch (Exception e) {
            //PrintMsgUtils.putExceptionMsg(e);
        }
        return df.format(tmpDate);
    }

    /**
     * @param n
     * @return yyyy-MM-dd
     */
    public static String afterDate(int n) {
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        Date tmpDate = new Date();

        long nDay = ((tmpDate.getTime() / (24 * 60 * 60 * 1000) + n) * (24 * 60 * 60 * 1000));
        tmpDate.setTime(nDay);

        return df.format(tmpDate);
    }


    public static Object invokeMethod(String name, Object[] args, Method[] ms, Object target) throws Exception {
        List list = new ArrayList();
        Method method = findMethod(name, args, ms, list);
        if (null == method) {
            throw new NoSuchMethodException(name);
        }
        method.setAccessible(true);
        try {
            if (list.size() > 0) {
                args = list.toArray();
            }
            return method.invoke(target, args);
        } catch (InvocationTargetException e) {
            throw new Exception(Utils.getFirstCause(e));
        } finally {
            method.setAccessible(false);
        }
    }

    public static Object invokeMethod(String name, Object[] args, Object target) throws Exception {
        return invokeMethod(name, args, target.getClass().getMethods(), target);
    }


    public static Method getGetter(Class clazz, Field field) {
        StringBuffer getterName = new StringBuffer();
        String fieldName = field.getName();
        String prix;
        if (fieldName.indexOf("is") == 0) {
            fieldName = fieldName.substring(2);
        }
        if (field.getType() == boolean.class) {
            getterName.append("is");
        } else {
            getterName.append("get");
        }
        getterName.append(fieldName.substring(0, 1).toUpperCase());
        getterName.append(fieldName.substring(1));
        Class[] argType = {};
        Method method = findMethod(clazz, getterName.toString(), argType);
        if (null == method) {
            fieldName = field.getName();
            getterName.setLength(0);
            getterName.append(fieldName.substring(0, 1).toUpperCase());
            getterName.append(fieldName.substring(1));
            method = findMethod(clazz, getterName.toString(), argType);
        }
        return method;
    }


    public static Method getSetter(Class clazz, Field field) {
        StringBuffer getterName = new StringBuffer();
        String fieldName = field.getName();
        String prix;
        if (fieldName.indexOf("is") == 0) {
            fieldName = fieldName.substring(2);
        }
        getterName.append("set");
        getterName.append(fieldName.substring(0, 1).toUpperCase());
        getterName.append(fieldName.substring(1));
        Class[] argType = {field.getType()};
        Method method = findMethod(clazz, getterName.toString(), argType);
        if (null == method) {
            fieldName = field.getName();
            getterName.setLength(0);
            getterName.append("set");
            getterName.append(fieldName.substring(0, 1).toUpperCase());
            getterName.append(fieldName.substring(1));
            method = findMethod(clazz, getterName.toString(), argType);
        }
        return method;
    }


    public static Map<String, Object> vo2map(Object vo) {
        return vo2map(vo, "", "");
    }


    public static Map<String, Object> vo2map(Object vo, String prefix, String splitter) {
        Class clazz = vo.getClass();
        LinkedHashMap map = new LinkedHashMap();
        Field[] fields = findFields(clazz, false);
        Field f;
        Method getter;
        Object obj;
        for (int i = 0; i < fields.length; i++) {
            obj = null;
            f = fields[i];
            getter = getGetter(clazz, f);
            if (null == getter) {
                continue;
            }
            try {
                getter.setAccessible(true);
                obj = getter.invoke(vo);
            } catch (Exception e) {
            }
            map.put(prefix + splitter + f.getName().toUpperCase(), obj);
        }
        return map;
    }

    public static Map<String, Object> vo2mapUpperCase(Object vo) {
        Class clazz = vo.getClass();
        LinkedHashMap map = new LinkedHashMap();
        Field[] fields = findFields(clazz, false);
        Field f;
        Method getter;
        Object obj;
        for (int i = 0; i < fields.length; i++) {
            obj = null;
            f = fields[i];
            getter = getGetter(clazz, f);
            if (null == getter) {
                continue;
            }
            try {
                getter.setAccessible(true);
                obj = getter.invoke(vo);
            } catch (Exception e) {
            }
            map.put(f.getName().toUpperCase(), obj);
        }
        return map;
    }


    public static String fit(String s, int tl, char c, char dc) {
        s = o2s(s);
        int len = s.getBytes().length;
        if (len >= tl) {
            return s;
        }
        int ml = tl - len;
        StringBuffer sb = new StringBuffer();
        if ('L' == dc) {
            for (int i = 0; i < ml; i++) {
                sb.append(c);
            }
            sb.append(s);
        } else if ('R' == dc) {
            sb.append(s);
            for (int i = 0; i < ml; i++) {
                sb.append(c);
            }
        } else {
            return s;
        }
        return sb.toString();
    }

    public static String fitL(String s, int tl, char c) {
        return fit(s, tl, c, 'L');
    }

    public static String fitR(String s, int tl, char c) {
        return fit(s, tl, c, 'R');
    }

    public static List s2list(String s, String sp) {
        s = o2s(s);
        String[] sa = s.split(sp);
        List list = new ArrayList();
        for (int i = 0; i < sa.length; i++) {
            s = o2s(sa[i]);
            if (s.length() < 1) {
                continue;
            }
            list.add(s);
        }
        return list;
    }

    public static Throwable getFirstCause(Throwable e) {
        while (null != e.getCause()) {
            e = e.getCause();
        }
        return e;
    }


    public static Object beanCopy4DestPostfix(Object dest, Object src,
                                              String postfix, boolean onlyCopyPostfix) throws Exception {
        Map map = vo2map(src);
        Field f;
        Method m;
        Object v;
        Class clazz = dest.getClass();
        Field[] fs = findFields(clazz, true);
        for (int i = 0; i < fs.length; i++) {
            f = fs[i];
            String name = null;
            try {
                name = f.getName().substring(0, f.getName().length() - postfix.length());
            } catch (Exception e) {
                System.err.println();
            }
            if (!onlyCopyPostfix && map.containsKey(f.getName())) name = f.getName();
            else if (map.containsKey(name)) {
            } else continue;
            v = map.get(name);
            if (null == v) {
                continue;
            }
            m = getSetter(clazz, f);
            if (null == m) {
                continue;
            }
            m.invoke(dest, new Object[]{v});
        }
        return dest;
    }

    public static Object beanCopy4DestPostfix(Object dest, Object src, String postfix) throws Exception {
        return beanCopy4DestPostfix(dest, src, postfix, false);
    }

    public static void copyValue(Object orgObj, Object dstObj) {
        try {
            BeanUtils.copyProperties(dstObj, orgObj);
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        } catch (InvocationTargetException e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * @param orgObj
     * @param dstObj
     * @param property
     */
    public static void copyValue(Object orgObj, Object dstObj, String property) {
        if (o2s(property).isEmpty()) {
            return;
        }
        if (!orgObj.getClass().equals(dstObj.getClass())) {
            // return;
        }
        Class clazz = orgObj.getClass();

        Field[] fields = findFields(clazz, false);
        Field f;
        Method getterOrg;
        Method setterDst;

        Object obj;
        for (int i = 0; i < fields.length; i++) {
            obj = null;
            f = fields[i];
            if (!f.getName().equalsIgnoreCase(property)) {
                continue;
            }

            getterOrg = getGetter(clazz, f);
            setterDst = getSetter(clazz, f);
            if (null == getterOrg) {
                continue;
            }
            try {
                getterOrg.setAccessible(true);
                obj = getterOrg.invoke(orgObj, null);
                if (obj == null || "".equals(obj)) {

                } else {
                    setterDst.invoke(dstObj, obj);
                }

            } catch (Exception e) {

            }

        }
    }

    /**
     * @param dest
     * @return
     * @throws Exception
     */
    public static Object beanClear(Object dest) throws Exception {
        Field f;
        Method ms, mg;
        Object v;
        Class clazz = dest.getClass();
        Field[] fs = findFields(clazz, true);
        for (int i = 0; i < fs.length; i++) {
            f = fs[i];
            mg = getGetter(clazz, f);
            if (null == mg) {
                continue;
            }
            v = mg.invoke(dest, new Object[]{});
            if (null == v || !((v instanceof String && o2s(v).length() < 1)
                    || (v instanceof Long && (Long) v < 1L)
                    || (v instanceof Integer && (Integer) v < 1))) {
                continue;
            }
            ms = getSetter(clazz, f);
            if (null == ms) {
                continue;
            }
            ms.invoke(dest, new Object[]{null});
        }
        return dest;
    }

    public static Class[] loadClass(String packageName, String className, ClassLoader cl) throws Exception {
        if (null != className) {
            ClassNotFoundException ex;
            try {
                return new Class[]{Class.forName(packageName + "." + className)};
            } catch (ClassNotFoundException e) {
                ex = e;
                try {
                    return new Class[]{Class.forName(packageName + "$" + className)};
                } catch (ClassNotFoundException e1) {
                    throw ex;
                }
            }
        } else {
            String path = packageName.replace(".", "/");
            if (null == cl) {
                cl = Utils.class.getClassLoader();
            }
            URL url = cl.getResource(path);
            if (null == url) {
                return null;
            }
            File f = new File(url.getPath());
            if (!f.isDirectory()) {
                return null;
            }
            int index;
            String cn;
            File[] fs = f.listFiles();
            Class[] cls = new Class[fs.length];
            for (int i = 0; i < fs.length; i++) {
                cn = fs[i].getName();
                index = cn.lastIndexOf(".");
                if (index > -1) {
                    cn = cn.substring(0, index);
                }
                cls[i] = loadClass(packageName, cn, cl)[0];
            }
            return cls;
        }
    }

    public static Class[] loadPackageClass(String packageName, ClassLoader cl) throws Exception {
        return loadClass(packageName, null, cl);
    }

    public static String getClassName(Class clazz) {
        if (null == clazz) {
            return "";
        }
        String s = clazz.getName();
        int i = s.lastIndexOf(".");
        if (i > -1) {
            s = s.substring(i + 1);
        }
        i = s.lastIndexOf("$");
        if (i > -1) {
            s = s.substring(i + 1);
        }
        return s;
    }

    public static String getClassName(Object obj) {
        if (null == obj) {
            return "";
        }
        if (obj instanceof Class) {
            return getClassName((Class) obj);
        }
        return getClassName(obj.getClass());
    }

    public static String getObjetListName(Class clazz) {
        return getClassName(clazz) + "List";
    }

    public static String getObjetMapName(Class clazz) {
        return getClassName(clazz) + "Map";
    }

    public static Date date8Add(String d8, int iy, int im, int id) {
        d8 = o2s(d8);
        if (8 != d8.length()) {
            return null;
        }
        Calendar c = Calendar.getInstance();
        c.set(o2i(d8.substring(0, 4)), o2i(d8.substring(4, 6)), o2i(d8.substring(6, 8)));
        c.add(Calendar.YEAR, iy);
        c.add(Calendar.MONTH, im - 1);
        c.add(Calendar.DAY_OF_MONTH, id);
        return c.getTime();
    }


    public static String entime2sttime(String arg) throws Exception {
        String ret = "";
        try {
            if (arg != null && arg.length() == 5) {
                arg = arg + (getNowYear());
            }
            if (arg == null)
                return "";
            Date date = (new SimpleDateFormat("ddMMMyy", Locale.ENGLISH)).parse(arg);

            ret = (new SimpleDateFormat("yyyy-MM-dd")).format(date);


        } catch (Exception e) {
            return "";
        } finally {
        }
        return ret;
    }


    public static String yearEnderStr(int y) {
        Calendar calendar = new GregorianCalendar();
        Date trialTime = new Date();
        calendar.setTime(trialTime);
        return "" + (calendar.get(Calendar.YEAR) + y) + "-12-31";
    }


    public static String sttime2entime(Object str) throws Exception {
        String ret = "";
        try {
            Date date = o2utilDate(str);
            if (date == null) {
                return null;
            }
            ret = (new SimpleDateFormat("ddMMMyy", Locale.ENGLISH))
                    .format(date).toUpperCase();
        } catch (Exception e) {
            return null;
        } finally {
        }
        return ret;
    }

    public static String getClassPath() {
        return Thread.currentThread().getContextClassLoader().getResource("").getPath();
    }

    public static String getHostAddress() {
        String ip = "";
        try {
            ip = InetAddress.getLocalHost().getHostAddress();
        } catch (UnknownHostException e) {
            //
        } finally {
        }
        return ip;
    }


    public static String getResponseIp() {

        if (localIp != null && !localIp.isEmpty()) {
            return localIp;
        }
        String ip = " ";
        try {
            Enumeration netInterfaces = NetworkInterface.getNetworkInterfaces();
            while (netInterfaces.hasMoreElements()) {
                NetworkInterface ni = (NetworkInterface) netInterfaces.nextElement();
                InetAddress inetAddress = (InetAddress) ni.getInetAddresses().nextElement();
                if (inetAddress != null) {
                    ip = inetAddress.getHostAddress() + ",";
                }
            }
        } catch (Exception e) {
            if (ip == null || ip.isEmpty()) {
                ip = " ";
            } else {
                ip = ip.replaceAll("127.0.0.1", "");
            }
        } finally {
        }
        localIp = ip;
        return ip;

    }

    public static String getServiceMechaneName() {
        String ip = "";
        try {
            ip = InetAddress.getLocalHost().getHostName();

        } catch (Exception e) {
            //PrintMsgUtils.putExceptionMsg(e);
        } finally {
        }
        return ip;
    }

    public static int countChineseChar(String args) {
        int count = 0;
        String regEx = "[\u4e00-\u9fa5]";
        Pattern p = Pattern.compile(regEx);
        Matcher m = p.matcher(args);
        while (m.find()) {
            for (int i = 0; i <= m.groupCount(); i++) {
                count = count + 1;
            }
        }
        return count;
    }

    public static int countByte(String args) {
        int count = 0;
        count = countChineseChar(args);
        count = args.length() + count;

        return count;
    }

    public static String trimStrR(String org, String cut) {
        try {

            while (org.endsWith(cut)) {
                org = org.substring(0, org.lastIndexOf(cut));
            }
        } catch (Exception e) {

        } finally {
        }
        return org;
    }

    public static String trimStrL(String org, String cut) {
        try {
            while (org.startsWith(cut)) {
                org = org.replaceFirst(cut, "");
            }

        } catch (Exception e) {

        } finally {
        }
        return org;
    }

    public static String trimStr(String org, String cut) {

        try {
            while (org.startsWith(cut)) {
                org = org.replaceFirst(cut, "");
            }

            while (org.endsWith(cut)) {
                org = org.substring(0, org.lastIndexOf(cut));
            }
        } catch (Exception e) {

        } finally {
        }
        return org;
    }

    /**
     * @param org
     * @param cutStr
     * @param cnt
     * @return
     */
    public static String cutStr(String org, char cutStr, int cnt) {

        try {
            int idx = 0;
            for (int i = 0; i < cnt; i++) {
                idx = org.indexOf(cutStr);
                if (idx > -1) {
                    org = org.substring(idx + 1);
                } else {
                    break;
                }

            }
        } catch (Exception e) {

        } finally {
        }
        return org;
    }

    /**
     * @param org
     * @param cutStr
     * @param cnt
     * @return
     */
    public static int indexOf(String org, char cutStr, int cnt) {
        int idx = -1;
        int idx2 = 0;
        try {
            for (int i = 0; i < cnt; i++) {
                idx2 = org.indexOf(cutStr);
                if (idx2 > -1) {
                    org = org.substring(idx2 + 1);
                    idx = idx + idx2 + 1;
                } else {
                    if (cnt > (i)) {
                        idx = -1;
                    }
                    //idx = idx+org.length()+1;
                    break;
                }
            }
        } catch (Exception e) {

        } finally {
        }
        return idx;
    }

    public static void print(Object obj) {
        // System.out.println("#####"+obj.toString());
    }

    public static String getNowYear() {
        Calendar calendar = new GregorianCalendar();
        Date trialTime = new Date();
        calendar.setTime(trialTime);
        return "" + calendar.get(Calendar.YEAR);
    }

    public static String sortStr(String org) {
        char[] arr = o2s(org).toCharArray();
        Arrays.sort(arr);
        return new String(arr);
    }

    /**
     * @param org
     * @param splitStr
     * @return
     */
    public static String sortStr(String org, String splitStr) {
        if (splitStr == null || splitStr.isEmpty()) {
            return org;

        }
        String[] arr = o2s(org).split(splitStr);
        Arrays.sort(arr);
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < arr.length; i++) {
            if (arr[i] == null || arr[i].isEmpty()) {
                continue;
            }
            if (i == arr.length - 1) {
                sb.append(arr[i]);
            } else {
                sb.append(arr[i]).append(splitStr);
            }
        }
        return sb.toString();
    }

    public String oValue(List list) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < list.size(); i++) {
            // Object obj = list.get(i);
            sb.append(i).append(":").append(o2s(list.get(i))).append(";");
        }
        return sb.toString();
    }

    public static int idxInMiddleOfString(String args) {
        //如果为侬，返回0
        if (o2s(args).isEmpty()) return 0;
        int len = countByte(args);

        if (len == args.length()) return args.length() / 2;

        int idx = 0;

        int begin = len / 4;
        int tmp = 0;
        for (int i = begin; i < args.length(); i++) {
            tmp = countByte(args.substring(0, i));
            if ((len / 2 - tmp) <= 0) {
                idx = i - 1;
                break;
            }
        }
        return idx;
    }

    public static boolean haveNumeric(String str) {
        boolean have = false;
        for (int i = 0; i < str.length(); i++) {
            if (Character.isDigit(str.charAt(i))) {
                have = true;
                break;
            }
        }
        return have;
    }

    public static String getJqlName(String str) {
        String aa = "'\\\"";
        String bb = "\\\"'";

        String jqlName = aa + str + bb;
        return jqlName;
    }

    public static int getIntRound(Object arg) {
        int dd = o2i(arg);
        return getIntRound(dd);
    }

    public static int getIntRound(int arg) {
        if (arg < 2) {
            return 2;
        }
        if (arg < 5) {
            return 5;
        }
        if (arg < 10) {
            arg = 10;
            return arg;
        }
        if (arg > 10 && arg <= 20) {
            //  arg = (arg / 10 + 1) * 10;
            arg = 20;
            return arg;
        }
        if (arg > 20 && arg <= 30) {
            //  arg = (arg / 10 + 1) * 10;
            arg = 30;
            return arg;
        }
        if (arg > 30 && arg <= 50) {
            //  arg = (arg / 10 + 1) * 10;
            arg = 50;
            return arg;
        }
        if (arg > 50 && arg <= 100) {
            //  arg = (arg / 10 + 1) * 10;
            arg = 100;
            return arg;
        }
        if (arg > 100 && arg <= 1000) {
            arg = (arg / 100 + 1) * 100;
            return arg;
        }
        if (arg > 1000 && arg <= 10000) {
            arg = (arg / 1000 + 1) * 1000;
            return arg;
        }
        if (arg > 10000 && arg <= 100000) {
            arg = (arg / 10000 + 1) * 10000;
            return arg;
        }
        if (arg > 100000 && arg <= 1000000) {
            arg = (arg / 100000 + 1) * 100000;
            return arg;
        }
        return arg;
    }

    public static long getLongRound(long arg) {
        return arg + 1;
    }


    public static long get5RoundUp(double d) {
        return Math.round(d / 5);
    }

    public static int randomInt() {
        int ret = 0;
        try {
            ret = Utils.o2i(Math.random() * 20);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return ret;
    }

    //将异常堆栈信息转化为字符串
    public static String errInfo(Exception e) {
        StringWriter sw = null;
        PrintWriter pw = null;
        try {
            sw = new StringWriter();
            pw = new PrintWriter(sw);
            // 将出错的栈信息输出到printWriter中
            e.printStackTrace(pw);
            pw.flush();
            sw.flush();
        } finally {
            if (sw != null) {
                try {
                    sw.close();
                } catch (IOException e1) {
                    e1.printStackTrace();
                }
            }
            if (pw != null) {
                pw.close();
            }
        }
        return sw.toString();
    }

    /**
     * 获取issue中的附件分类map
     * @param issue 目标问题
     * @return MapList
     *       {
     *          "id":1,
     *          "name":"Sample",
     *          "position":0,
     *          "attachments":[//文件
     *             10000,
     *             10002
     *          ],
     *          "documents":[//相同文件的多个版本
     *             {
     *                "id":1,
     *                "name":"document",
     *                "attachmentIds":[
     *                   10003,
     *                   10004,
     *                   10005
     *                ]
     *             },...
     *          ]
     *       },...
     * @throws Exception
     */
    public static List<Map<String, Object>> getAttachmentCategoriesMapList(Issue issue) throws Exception {
        List<Map<String, Object>> dataMapList = new ArrayList<>();

        try {
            // 获取jira基础访问路径   http://localhost:2990/jira
            String baseUrl = ComponentAccessor.getApplicationProperties().getString(APKeys.JIRA_BASEURL);
            String getUrl = baseUrl + "/rest/attach-cat/1.0/attachments?issueKey=" + issue.getKey();
//            ApplicationUser reporter = issue.getReporter();
            //获取类别列表信息
//            map.put("atlas-authorization",reporter.getUserName())
            String callback = HttpUtils.doGet(getUrl, null);
//            Assert.notNull(callback, "附件分类获取失败");
            log.error("获取附件分类调用响应："+callback);
            Map<String, Object> callbackMap = (Map<String, Object>) new Gson().fromJson(callback, Object.class);
            Object callbackObject = callbackMap.get("categories");

            if (callbackObject instanceof ArrayList) {//多个分类
                dataMapList = (List<Map<String, Object>>) callbackObject;
            } else {//只有一个分类
                dataMapList = new ArrayList<>();
                Map<String, Object> dataMap = (Map<String, Object>) callbackObject;
                dataMapList.add(dataMap);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
        return dataMapList;
    }

    /**
     * 智能附件Api-移动附件
     *
     * @param issueKey 问题关键字
     * @apiNote 数据格式: issueKey=CPGL-550&toCatId=68&attachments=81103&attachments=81104
     */
    public static void moveAttachmentToCategories(String issueKey, Long categoryId, List<Attachment> attachmentList) {
        StringBuilder data = new StringBuilder("issueKey=" + issueKey);
        data.append("&toCatId=").append(categoryId);
        for (Attachment attachment : attachmentList) {
            data.append("&attachments=").append(attachment.getId());
        }
        // 移动附件API
        String baseUrl = ComponentAccessor.getApplicationProperties().getString(APKeys.JIRA_BASEURL);
        String moveUrl = baseUrl + "/rest/attach-cat/1.0/attachments/move";
        Map<String, String> header = new HashMap<>();
        header.put("Content-Type", MediaType.TEXT_PLAIN_VALUE);
        header.put("Authorization", Constant.jiraAuthorization);
        //新开线程，等待后处理运行结束后在移动附件
        new Thread(() -> {
            String respond = HttpUtils.doPostBackLog(moveUrl, data.toString(), header);
            log.info("移动附件API调用响应>> %s ", respond);
        }).start();
    }

    /**
     * 智能附件Api-移动附件
     *
     * @param data 请求数据
     * @apiNote 数据格式: issueKey=CPGL-550&toCatId=68&attachments=81103&attachments=81104
     */
    public static void moveAttachmentToCategories(String data) {
        // 移动附件API
        String baseUrl = ComponentAccessor.getApplicationProperties().getString(APKeys.JIRA_BASEURL);
        String moveUrl = baseUrl + "/rest/attach-cat/1.0/attachments/move";
        Map<String, String> header = new HashMap<>();
        header.put("Content-Type", MediaType.TEXT_PLAIN_VALUE);
        header.put("Authorization", Constant.jiraAuthorization);
        //新开线程，等待后处理运行结束后在移动附件
        new Thread(() -> {
            String respond = HttpUtils.doPostBackLog(moveUrl, data, header);
            log.info("移动附件API调用响应>> %s ", respond);
        }).start();
    }

    /**
     * 下载文件---返回下载后的文件存储路径
     *
     * @param url      文件地址
     * @param savePath      存储目录
     * @param fileName 存储文件名
     * @return
     */
    public static String downloadHttpUrl(String url, String savePath, String fileName) throws IOException {
        try {
            URL fileUrl = new URL(url);
            HttpURLConnection conn = (HttpURLConnection) fileUrl.openConnection();
            // 设置超时间为3秒
            conn.setConnectTimeout(3 * 1000);
            // 防止屏蔽程序抓取而返回403错误
            conn.setRequestProperty("User-Agent", "Mozilla/4.0 (compatible; MSIE 5.0; Windows NT; DigExt)");
            conn.setRequestProperty("Authorization","Bearer eyJhbGciOiJIUzUxMiJ9.**********************************************************************************************************************************************************************************************************************.P1JWgtRVk1sTPPLiCgZNuleYyPZRf2ooByC_mmu9scs6SVbpJHgSsKd8AtscjDwg3Fw7D4QN31vgtA5jeedj3g");

            if (Response.Status.OK.getStatusCode() == conn.getResponseCode()) {
                // 得到输入流
                InputStream inputStream = conn.getInputStream();
                if (ObjectUtils.isEmpty(inputStream)) {
                    throw new HttpException("文件流获取失败");
                }
                // 获取字节数组
                byte[] getData = readInputStream(inputStream);
                // 文件保存位置
                File saveDir = new File(savePath);
                if (!saveDir.exists()) {
                    if (!saveDir.mkdirs()) {
                        throw new IOException("文件夹创建失败");
                    }
                }
                File file = new File(saveDir + File.separator + fileName);
                FileOutputStream fos = new FileOutputStream(file);
                fos.write(getData);
                if (fos != null) {
                    fos.close();
                }
                if (inputStream != null) {
                    inputStream.close();
                }
//                log.error("info:"+fileUrl+" download success");
            } else {
                throw new HttpException("下载文件失败: " + conn.getResponseCode());
            }
        } catch (Exception e) {
//            log.error("下载文件失败{}", errInfo(e));
            throw e;
        }
        return savePath + fileName;
    }
    /**
     * 从输入流中获取字节数组
     *
     * @param inputStream
     * @return
     * @throws IOException
     */
    public static byte[] readInputStream(InputStream inputStream) throws IOException {
        byte[] buffer = new byte[1024];
        int len = 0;
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        while ((len = inputStream.read(buffer)) != -1) {
            bos.write(buffer, 0, len);
        }
        bos.close();
        return bos.toByteArray();

    }

    /**
     * 生成测试失效文件保存路径
     * @return 保存路径
     */
    public static String getTestFailureFilePath() {
        AttachmentPathManager attachmentPathManager = ComponentAccessor.getAttachmentPathManager();
        String defaultPath = attachmentPathManager.getDefaultAttachmentPath();
        String filePath = defaultPath.replace("attachments", "testFailureFile");
        return filePath + File.separator;
    }

    public static String getGainOldFilePath(String issueKey) {
        AttachmentPathManager attachmentPathManager = ComponentAccessor.getAttachmentPathManager();
        String defaultPath = attachmentPathManager.getDefaultAttachmentPath();
        String filePath = defaultPath.replace("attachments", "topic");
        filePath = filePath + File.separator + issueKey;
        return filePath;
    }


//    /**
//     * pptToPdf
//     * @param pptPath PPT文件路径
//     * @param pdfDir 生成的PDF文件路径
//     * @return
//     */
//    public static boolean pptToPdf(String pptPath, String pdfDir) {
//
//        if (StringUtils.isEmpty(pptPath)) {
//            throw new RuntimeException("word文档路径不能为空");
//        }
//
//        if (StringUtils.isEmpty(pdfDir)) {
//            throw new RuntimeException("pdf目录不能为空");
//        }
//
//
//        String pdfPath = pdfDir + "te." + "pdf";
//
//        Document document = null;
//        HSLFSlideShow hslfSlideShow = null;
//        FileOutputStream fileOutputStream = null;
//        PdfWriter pdfWriter = null;
//
//        try {
//            //使用输入流ppt文件
//            hslfSlideShow = new HSLFSlideShow(new FileInputStream(pptPath));
//
//            // 获取ppt文件页面
//            Dimension dimension = hslfSlideShow.getPageSize();
//
//            fileOutputStream = new FileOutputStream(pdfPath);
//
//            document = new Document();
//
//            // pdfWriter实例
//            pdfWriter = PdfWriter.getInstance(document, fileOutputStream);
//
//            document.open();
//
//            PdfPTable pdfPTable = new PdfPTable(1);
//
//            List<HSLFSlide> hslfSlideList = hslfSlideShow.getSlides();
//
//            for (int i=0; i < hslfSlideList.size(); i++) {
//                HSLFSlide hslfSlide = hslfSlideList.get(i);
//
//                for (HSLFShape shape : hslfSlide.getShapes()) {
//
//                    if (shape instanceof HSLFTextShape){
//                        // 设置字体, 解决中文乱码
//                        HSLFTextShape textShape = (HSLFTextShape) shape;
//
//                        for (HSLFTextParagraph textParagraph : textShape.getTextParagraphs()) {
//                            for (HSLFTextRun textRun : textParagraph.getTextRuns()) {
//                                textRun.setFontFamily("宋体");
//                            }
//                        }
//                    }
//
//                }
//                BufferedImage bufferedImage = new BufferedImage((int)dimension.getWidth(), (int)dimension.getHeight(), BufferedImage.TYPE_INT_RGB);
//
//                Graphics2D graphics2d = bufferedImage.createGraphics();
//
//                graphics2d.setPaint(Color.white);
//                graphics2d.setFont(new java.awt.Font("宋体", java.awt.Font.PLAIN, 12));
//
//                hslfSlide.draw(graphics2d);
//
//                graphics2d.dispose();
//
//                Image image = Image.getInstance(bufferedImage, null);
//                image.scalePercent(50f);
//
//                // 写入单元格
//                pdfPTable.addCell(new PdfPCell(image, true));
//                document.add(image);
//            }
//
//        } catch (Exception e) {
//            e.printStackTrace();
//            return false;
//        } finally {
//            try {
//                if (document != null) {
//                    document.close();
//                }
//                if (fileOutputStream != null) {
//                    fileOutputStream.close();
//                }
//                if (pdfWriter != null) {
//                    pdfWriter.close();
//                }
//            } catch (IOException e) {
//                e.printStackTrace();
//            }
//        }
//        return true;
//    }
//
//    /**
//     *
//     * @Title: pptxToPdf
//     * @param pptPath PPT文件路径
//     * @param pdfDir 生成的PDF文件路径
//     */
//    public static boolean pptxToPdf(String pptPath, String pdfDir) {
//
//        if (StringUtils.isEmpty(pptPath)) {
//            throw new RuntimeException("word文档路径不能为空");
//        }
//
//        if (StringUtils.isEmpty(pdfDir)) {
//            throw new RuntimeException("pdf目录不能为空");
//        }
//
//        String pdfPath = pdfDir + "te." + "pdf";
//
//        Document document = null;
//
//        XMLSlideShow slideShow = null;
//
//
//        FileOutputStream fileOutputStream = null;
//
//        PdfWriter pdfWriter = null;
//
//
//        try {
//            //使用输入流pptx文件
//            slideShow = new XMLSlideShow(new FileInputStream(pptPath));
//            //获取幻灯片的尺寸
//            Dimension dimension = slideShow.getPageSize();
//            //新增pdf输出流，准备讲ppt写出
//            fileOutputStream = new FileOutputStream(pdfPath);
//            //创建一个写内容的容器
//            document = new Document();
//            //使用输出流写入
//            pdfWriter = PdfWriter.getInstance(document, fileOutputStream);
//            //使用之前必须打开<You have to open the document before you can write content.>
//            document.open();
//
//            PdfPTable pdfPTable = new PdfPTable(1);
//            //获取幻灯片
//            List<XSLFSlide> slideList = slideShow.getSlides();
//
//            for (int i = 0, row = slideList.size(); i < row; i++) {
//                //获取每一页幻灯片
//                XSLFSlide slide = slideList.get(i);
//
//                for (XSLFShape shape : slide.getShapes()) {
//                    //判断是否是文本
//                    if(shape instanceof XSLFTextShape){
//                        // 设置字体, 解决中文乱码
//                        XSLFTextShape textShape = (XSLFTextShape) shape;
//                        for (XSLFTextParagraph textParagraph : textShape.getTextParagraphs()) {
//                            for (XSLFTextRun textRun : textParagraph.getTextRuns()) {
//                                textRun.setFontFamily("宋体");
//                            }
//                        }
//                    }
//                }
//
//
//                //根据幻灯片尺寸创建图形对象
//                BufferedImage bufferedImage = new BufferedImage((int)dimension.getWidth(), (int)dimension.getHeight(), BufferedImage.TYPE_INT_RGB);
//
//                Graphics2D graphics2d = bufferedImage.createGraphics();
//
//                graphics2d.setPaint(Color.white);
//                graphics2d.setFont(new java.awt.Font("宋体", java.awt.Font.PLAIN, 12));
//
//                //把内容写入图形对象
//                slide.draw(graphics2d);
//
//                graphics2d.dispose();
//
//                //封装到Image对象中
//                Image image = Image.getInstance(bufferedImage, null);
//                image.scalePercent(50f);
//
//                // 写入单元格
//                pdfPTable.addCell(new PdfPCell(image, true));
//                document.add(image);
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//            return false;
//        } finally {
//            try {
//                if (document != null) {
//                    document.close();
//                }
//                if (fileOutputStream != null) {
//                    fileOutputStream.close();
//                }
//                if (pdfWriter != null) {
//                    pdfWriter.close();
//                }
//            } catch (IOException e) {
//                e.printStackTrace();
//            }
//        }
//        return true;
//    }
//
//    public static boolean convertPPTToPDF(File file, File toFile) {
//        try {
//            Document pdfDocument = new Document();
//            PdfWriter pdfWriter = PdfWriter.getInstance(pdfDocument, new FileOutputStream(toFile));
//            FileInputStream is = new FileInputStream(file);
//            HSLFSlideShow hslfSlideShow = new HSLFSlideShow(is);
//            double zoom = 2;
//            if (hslfSlideShow == null) {
//                is = new FileInputStream(file);
//                XMLSlideShow ppt = new XMLSlideShow(is);
//                if (ppt == null) {
//                    throw new NullPointerException("This PPTX get data is error....");
//                }
//                Dimension pgsize = ppt.getPageSize();
//                List<XSLFSlide> slide = ppt.getSlides();
//                AffineTransform at = new AffineTransform();
//                at.setToScale(zoom, zoom);
//                pdfDocument.setPageSize(new Rectangle((float) pgsize.getWidth(), (float) pgsize.getHeight()));
//                pdfWriter.open();
//                pdfDocument.open();
//                PdfPTable table = new PdfPTable(1);
//                for (XSLFSlide xslfSlide : slide) {
//                    BufferedImage img = new BufferedImage((int) Math.ceil(pgsize.width * zoom), (int) Math.ceil(pgsize.height * zoom), BufferedImage.TYPE_INT_RGB);
//                    Graphics2D graphics = img.createGraphics();
//                    graphics.setTransform(at);
//
//                    graphics.setPaint(Color.white);
//                    graphics.fill(new Rectangle2D.Float(0, 0, pgsize.width, pgsize.height));
//                    xslfSlide.draw(graphics);
//                    graphics.getPaint();
//                    Image slideImage = Image.getInstance(img, null);
//                    table.addCell(new PdfPCell(slideImage, true));
//                }
//                ppt.close();
//                pdfDocument.add(table);
//                pdfDocument.close();
//                pdfWriter.close();
//                System.out.println(file.getAbsolutePath() + "Powerpoint file converted to PDF successfully");
//                return true;
//            }
//
//            Dimension pgsize = hslfSlideShow.getPageSize();
//            List<HSLFSlide> slides = hslfSlideShow.getSlides();
//            pdfDocument.setPageSize(new Rectangle((float) pgsize.getWidth(), (float) pgsize.getHeight()));
//            pdfWriter.open();
//            pdfDocument.open();
//            AffineTransform at = new AffineTransform();
//            PdfPTable table = new PdfPTable(1);
//            for (HSLFSlide hslfSlide : slides) {
//                BufferedImage img = new BufferedImage((int) Math.ceil(pgsize.width * zoom), (int) Math.ceil(pgsize.height * zoom), BufferedImage.TYPE_INT_RGB);
//                Graphics2D graphics = img.createGraphics();
//                graphics.setTransform(at);
//
//                graphics.setPaint(Color.white);
//                graphics.fill(new Rectangle2D.Float(0, 0, pgsize.width, pgsize.height));
//                hslfSlide.draw(graphics);
//                graphics.getPaint();
//                Image slideImage = Image.getInstance(img, null);
//                table.addCell(new PdfPCell(slideImage, true));
//            }
//            hslfSlideShow.close();
//            pdfDocument.add(table);
//            pdfDocument.close();
//            pdfWriter.close();
//            System.out.println(file.getAbsolutePath() + "Powerpoint file converted to PDF successfully");
//            return true;
//        } catch (Exception e) {
//            System.out.println(file.getAbsolutePath() + "--->" + e.getMessage());
//            return false;
//        } catch (Error error) {
//            error.printStackTrace();
//            return false;
//        }
//    }
//
//    private XMLSlideShow convertPPTToPDFByPPTX(FileInputStream is) {
//        try {
//            return new XMLSlideShow(is);
//        } catch (IOException e) {
//            return null;
//        }
//    }
//
//    private HSLFSlideShow convertPPTToPDFByPPT(FileInputStream is) {
//        try {
//            return new HSLFSlideShow(is);
//        } catch (Exception e) {
//            return null;
//        }
//    }
}
