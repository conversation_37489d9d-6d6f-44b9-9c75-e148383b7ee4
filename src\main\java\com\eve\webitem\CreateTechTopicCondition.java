package com.eve.webitem;

import com.atlassian.crowd.embedded.api.Group;
import com.atlassian.jira.component.ComponentAccessor;
import com.atlassian.jira.issue.Issue;
import com.atlassian.jira.plugin.webfragment.conditions.AbstractWebCondition;
import com.atlassian.jira.plugin.webfragment.model.JiraHelper;
import com.atlassian.jira.user.ApplicationUser;
import com.eve.utils.Constant;

import java.util.Collection;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/4/6
 */
public class CreateTechTopicCondition extends AbstractWebCondition {
    @Override
    public boolean shouldDisplay(ApplicationUser user,
                                 JiraHelper jiraHelper) {
//        Map<String, Object> map = jiraHelper.getContextParams();
//        final Issue issue = (Issue) map.get("issue");
        if (user == null) {
            return false;
        }
        Collection<Group> groupsForUser = ComponentAccessor.getGroupManager().getGroupsForUser(user);
        return groupsForUser.stream().anyMatch(e -> "动力电池研究院".equals(e.getName()));
    }
}
