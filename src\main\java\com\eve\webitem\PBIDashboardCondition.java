package com.eve.webitem;

import com.atlassian.crowd.embedded.api.Group;
import com.atlassian.jira.component.ComponentAccessor;
import com.atlassian.jira.plugin.webfragment.conditions.AbstractWebCondition;
import com.atlassian.jira.plugin.webfragment.model.JiraHelper;
import com.atlassian.jira.security.Permissions;
import com.atlassian.jira.user.ApplicationUser;

import java.util.Collection;

/**
 * <AUTHOR>
 * @date 2024/6/14
 */
public class PBIDashboardCondition extends AbstractWebCondition {
    @Override
    public boolean shouldDisplay(ApplicationUser user,
                                 JiraHelper jiraHelper) {
//        Map<String, Object> map = jiraHelper.getContextParams();
//        final Issue issue = (Issue) map.get("issue");
        if (user == null) {
            return false;
        }
        Permissions permissions = null;
        Collection<Group> groupsForUser = ComponentAccessor.getGroupManager().getGroupsForUser(user);
        return user.getUsername().equals("083779")||user.getUsername().equals("071716")||user.getUsername().equals("083974")||user.getUsername().equals("073406");
//        return groupsForUser.stream().anyMatch(e -> "JIRA系统管理员".equals(e.getName()));
    }
}
