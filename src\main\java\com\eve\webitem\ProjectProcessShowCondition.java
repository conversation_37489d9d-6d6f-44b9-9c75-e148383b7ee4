package com.eve.webitem;

import com.atlassian.jira.component.ComponentAccessor;
import com.atlassian.jira.issue.Issue;
import com.atlassian.jira.issue.issuetype.IssueType;
import com.atlassian.jira.issue.resolution.Resolution;
import com.atlassian.jira.plugin.webfragment.conditions.AbstractWebCondition;
import com.atlassian.jira.plugin.webfragment.model.JiraHelper;
import com.atlassian.jira.project.Project;
import com.atlassian.jira.user.ApplicationUser;
import com.eve.utils.Constant;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/4/6
 */
public class ProjectProcessShowCondition extends AbstractWebCondition {
    @Override
    public boolean shouldDisplay(ApplicationUser user,
                                 JiraHelper jiraHelper) {
        Map<String, Object> map = jiraHelper.getContextParams();
        final Issue issue = (Issue) map.get("issue");
        Resolution resolution = issue.getResolution();
        IssueType issueType = issue.getIssueType();
        if (resolution != null || user == null || issueType == null || issueType.isSubTask()) {
            return false;
        }
        String projectIdStr = ComponentAccessor.getApplicationProperties().getString(Constant.project_process_project_id);
        if (projectIdStr == null) {
            return false;
        }
        String[] projectIdAtr = projectIdStr.split(",");
        List<String> projectIdList = Arrays.asList(projectIdAtr);
        String projectId = issue.getProjectId() + "";

        ApplicationUser reporter = issue.getReporter();
        //满足条件(项目管理的项目时)显示web-item
        return projectIdList.contains(projectId) && user.getUsername().equals(reporter.getUsername());
    }
}
