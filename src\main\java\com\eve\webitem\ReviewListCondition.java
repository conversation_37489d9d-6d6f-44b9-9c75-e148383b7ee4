package com.eve.webitem;

import com.atlassian.jira.issue.Issue;
import com.atlassian.jira.plugin.webfragment.conditions.AbstractWebCondition;
import com.atlassian.jira.plugin.webfragment.model.JiraHelper;
import com.atlassian.jira.user.ApplicationUser;
import com.eve.utils.Constant;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/4/6
 */
public class ReviewListCondition extends AbstractWebCondition {
    @Override
    public boolean shouldDisplay(ApplicationUser user,
                                 JiraHelper jiraHelper) {
        if (user == null) {
            return false;
        }
        Map<String, Object> map = jiraHelper.getContextParams();
        final Issue issue = (Issue) map.get("issue");
        String statusId = issue.getStatusId();
        ApplicationUser assignee = issue.getAssignee();
        //满足条件(项目管理的项目时)显示web-item
        return Constant.projectReviewStatusIdList.contains(Long.valueOf(statusId)) && user.getUsername().equals(assignee.getUsername());
//        return false;
    }
}
