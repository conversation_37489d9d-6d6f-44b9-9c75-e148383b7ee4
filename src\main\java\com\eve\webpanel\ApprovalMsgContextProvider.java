package com.eve.webpanel;

import com.atlassian.jira.component.ComponentAccessor;
import com.atlassian.jira.config.properties.APKeys;
import com.atlassian.jira.issue.Issue;
import com.atlassian.jira.project.Project;
import com.atlassian.plugin.PluginParseException;
import com.atlassian.plugin.web.ContextProvider;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/12/1
 */
public class ApprovalMsgContextProvider implements ContextProvider {
    @Override
    public void init(Map<String, String> map) throws PluginParseException {
    }
    @Override
    public Map<String, Object> getContextMap(Map<String, Object> context) {
        Map<String, Object> map = new HashMap<>();
        final Issue issue = (Issue) context.get("issue");
        final Project project = (Project) context.get("project");

        map.put("issueId", issue.getId() + "");
        map.put("issueTypeId",issue.getIssueTypeId());
        map.put("issueKey", issue.getKey());
        map.put("projectKey", project.getKey());
        map.put("projectId", project.getId());
        String baseurl = ComponentAccessor.getApplicationProperties().getString(APKeys.JIRA_BASEURL);
        map.put("baseUrl", baseurl);
        return map;
    }
}
