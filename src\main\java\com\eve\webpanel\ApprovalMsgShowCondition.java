package com.eve.webpanel;

import com.atlassian.jira.issue.Issue;
import com.atlassian.jira.plugin.webfragment.conditions.AbstractWebCondition;
import com.atlassian.jira.plugin.webfragment.model.JiraHelper;
import com.atlassian.jira.project.Project;
import com.atlassian.jira.user.ApplicationUser;
import com.eve.services.ApprovalMsgOpenConfigService;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/12/1
 */
public class ApprovalMsgShowCondition extends AbstractWebCondition {

    private ApprovalMsgOpenConfigService approvalMsgOpenConfigService;

    public ApprovalMsgShowCondition(ApprovalMsgOpenConfigService approvalMsgOpenConfigService) {
        this.approvalMsgOpenConfigService = approvalMsgOpenConfigService;
    }

    @Override
    public boolean shouldDisplay(ApplicationUser user,
                                 JiraHelper jiraHelper) {

        Project projectManageProject = jiraHelper.getProject();

        Map map = jiraHelper.getContextParams();
        final Issue issue = (Issue) map.get("issue");
        final Project project = (Project) map.get("project");
        String issueTypeId = issue.getIssueTypeId();

        return approvalMsgOpenConfigService.queryProjectApprovalMsgStatus(projectManageProject == null ? project.getKey() : projectManageProject.getKey());

    }
}
