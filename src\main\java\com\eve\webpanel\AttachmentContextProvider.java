package com.eve.webpanel;

import com.atlassian.jira.bc.project.DefaultProjectService;
import com.atlassian.jira.bc.project.ProjectService;
import com.atlassian.jira.component.ComponentAccessor;
import com.atlassian.jira.config.properties.APKeys;
import com.atlassian.jira.issue.Issue;
import com.atlassian.jira.project.Project;
import com.atlassian.jira.user.ApplicationUser;
import com.atlassian.plugin.PluginParseException;
import com.atlassian.plugin.web.ContextProvider;

import java.util.HashMap;
import java.util.Map;

public class AttachmentContextProvider implements ContextProvider {
    @Override
    public void init(Map<String, String> map) throws PluginParseException {
    }
    @Override
    public Map<String, Object> getContextMap(Map<String, Object> context) {
        Map map = new HashMap();
        final Issue issue = (Issue) context.get("issue");
        final Project project = (Project) context.get("project");
//        ApplicationUser applicationUser=(ApplicationUser) context.get("user");
//        ProjectService projectService = new DefaultProjectService();
//        ProjectService.DeleteProjectValidationResult deleteProjectValidationResult = projectService.validateDeleteProject(applicationUser, project.getKey());
//        projectService.deleteProject(applicationUser, deleteProjectValidationResult);
////        ComponentAccessor.getProjectManager().removeProject(project);
////        ProjectService.deleteProject()
        map.put("issueid", issue.getId()+"");
        //map.put("typeid",issue.getIssueTypeId());
        map.put("issuekey",issue.getKey());
        String baseurl = ComponentAccessor.getApplicationProperties().getString(APKeys.JIRA_BASEURL);
        map.put("baseURL",baseurl);
        return map;
    }
}
