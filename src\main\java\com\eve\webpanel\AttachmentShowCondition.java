package com.eve.webpanel;

import com.atlassian.jira.component.ComponentAccessor;
import com.atlassian.jira.issue.Issue;
import com.atlassian.jira.issue.fields.CustomField;
import com.atlassian.jira.issue.issuetype.IssueType;
import com.atlassian.jira.plugin.webfragment.conditions.AbstractWebCondition;
import com.atlassian.jira.plugin.webfragment.model.JiraHelper;
import com.atlassian.jira.project.Project;
import com.atlassian.jira.user.ApplicationUser;
import com.eve.utils.Constant;
import com.eve.utils.Utils;

import java.util.Map;


public class AttachmentShowCondition extends AbstractWebCondition {
    @Override
    public boolean shouldDisplay(ApplicationUser user,
                                 JiraHelper jiraHelper) {
        Project projectManageProject = jiraHelper.getProject();
        String projectKeyStr = ComponentAccessor.getApplicationProperties().getString(Constant.current_signed_file_project_key);

        Map map = jiraHelper.getContextParams();
        final Issue issue = (Issue) map.get("issue");
        CustomField customField = Utils.getCustomFieldByID(Constant.attachmentSignCateCustomFieldId);
        Object customFieldValue = issue.getCustomFieldValue(customField);

        if (projectKeyStr == null || customFieldValue == null) {
            return false;
        }
        //显示项目已配置，且签字文件类别字段有值
        if (projectKeyStr.contains(projectManageProject.getKey())) {
            return true;
        }

//        if (projectManageProject.getKey().equals("XMGL")&&issue.getIssueTypeId().equals(10914L+"")) {
//            return true;
//        }
        return false;
    }
}
