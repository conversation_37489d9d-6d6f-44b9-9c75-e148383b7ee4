package com.eve.webpanel;

import com.atlassian.jira.component.ComponentAccessor;
import com.atlassian.jira.config.properties.APKeys;
import com.atlassian.jira.issue.Issue;
import com.atlassian.jira.project.Project;
import com.atlassian.plugin.PluginParseException;
import com.atlassian.plugin.web.ContextProvider;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/4/6
 */
public class progressContextProvider implements ContextProvider {

    @Override
    public void init(Map<String, String> map) throws PluginParseException {
    }

    @Override
    public Map<String, Object> getContextMap(Map<String, Object> context) {
        Map map = new HashMap();
        final Issue issue = (Issue) context.get("issue");
        final Project project = (Project) context.get("project");
        map.put("issueid", issue.getId()+"");
        map.put("typeid",issue.getIssueTypeId());
        map.put("projectkey",project.getKey());
        String baseurl = ComponentAccessor.getApplicationProperties().getString(APKeys.JIRA_BASEURL);
        map.put("baseURL",baseurl);
        return map;
    }
}
