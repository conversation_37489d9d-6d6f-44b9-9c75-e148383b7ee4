package com.eve.workflow.conditions;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.atlassian.jira.component.ComponentAccessor;
import com.atlassian.jira.issue.Issue;
import com.atlassian.jira.issue.MutableIssue;
import com.atlassian.jira.issue.fields.CustomField;
import com.atlassian.jira.workflow.condition.AbstractJiraCondition;
import com.eve.services.CustomToolService;
import com.eve.utils.Utils;
import com.opensymphony.module.propertyset.PropertySet;
import com.opensymphony.workflow.WorkflowException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/2/14
 */
public class CompareFieldValueCondition extends AbstractJiraCondition {
    private static final Logger log = LoggerFactory.getLogger(CompareFieldValueCondition.class);
    private CustomToolService customToolService;

    public CompareFieldValueCondition(CustomToolService customToolService) {
        this.customToolService = customToolService;
    }

    @Override
    public boolean passesCondition(Map transientVars, Map args, PropertySet ps) throws WorkflowException {
        try {
            Issue issue = super.getIssue(transientVars);
            //获取参数
            JSONObject jsonObject = JSON.parseObject((String) args.get("paramsJson"));
            String sourceIssue = String.valueOf(jsonObject.get("sourceIssue"));
            String customFieldId = String.valueOf(jsonObject.get("customField"));
            String targetIssue = String.valueOf(jsonObject.get("targetIssue"));
            String customField1Id = String.valueOf(jsonObject.get("customField1"));
            String isEqual = String.valueOf(jsonObject.get("isEqual"));
            String isShow = String.valueOf(jsonObject.get("isShow"));

            MutableIssue sourceMutableIssue = customToolService.getRelationIssue(sourceIssue, issue);
            MutableIssue targetMutableIssue = customToolService.getRelationIssue(targetIssue, issue);
            //处理参数
            CustomField customField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(customFieldId);
            CustomField customField1 = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(customField1Id);
            Object customFieldValue = sourceMutableIssue.getCustomFieldValue(customField);
            Object customField1Value = targetMutableIssue.getCustomFieldValue(customField1);
            if (customFieldValue == null || customField1Value == null) {
                return false;
            }
            boolean fieldIsEqual = customFieldValue.equals(customField1Value);
            boolean equal = Boolean.parseBoolean(isEqual);
            boolean show = Boolean.parseBoolean(isShow);

            return show == (fieldIsEqual == equal);
        } catch (Exception e) {
            log.error(Utils.errInfo(e));
            return true;
        }
    }
}
