package com.eve.workflow.conditions;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.atlassian.jira.component.ComponentAccessor;
import com.atlassian.jira.issue.fields.CustomField;
import com.atlassian.jira.plugin.workflow.AbstractWorkflowPluginFactory;
import com.atlassian.jira.plugin.workflow.WorkflowPluginConditionFactory;
import com.eve.utils.Constant;
import com.opensymphony.workflow.loader.AbstractDescriptor;
import com.opensymphony.workflow.loader.ConditionDescriptor;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/2/14
 */
public class CompareFieldValueConditionFactory extends AbstractWorkflowPluginFactory implements WorkflowPluginConditionFactory {
    @Override
    protected void getVelocityParamsForInput(Map<String, Object> map) {
        List<CustomField> customFieldList = ComponentAccessor.getCustomFieldManager().getCustomFieldObjects();//获取全部字段
//        CustomField customField = ComponentAccessor.getCustomFieldManager().getCustomFieldInstance();
        map.put("copyTypeMap", Constant.copyTypeMap);
        map.put("customFieldList", customFieldList);
    }

    @Override
    protected void getVelocityParamsForEdit(Map<String, Object> map, AbstractDescriptor abstractDescriptor) {
        List<CustomField> customFieldList = ComponentAccessor.getCustomFieldManager().getCustomFieldObjects();//获取全部字段
        map.put("copyTypeMap", Constant.copyTypeMap);
        map.put("customFieldList", customFieldList);

        ConditionDescriptor conditionDescriptor = (ConditionDescriptor) abstractDescriptor;
        JSONObject jsonObject = JSON.parseObject((String) conditionDescriptor.getArgs().get("paramsJson"));
        String sourceIssue = String.valueOf(jsonObject.get("sourceIssue"));
        String customField = String.valueOf(jsonObject.get("customField"));
        String targetIssue = String.valueOf(jsonObject.get("targetIssue"));
        String customField1 = String.valueOf(jsonObject.get("customField1"));
        String isEqual = String.valueOf(jsonObject.get("isEqual"));
        String isShow = String.valueOf(jsonObject.get("isShow"));

        map.put("sourceIssue", "null".equals(sourceIssue) ? "current_issue" : sourceIssue);
        map.put("customField", customField);
        map.put("targetIssue", "null".equals(targetIssue) ? "current_issue" : targetIssue);
        map.put("customField1", customField1);
        map.put("isEqual", isEqual);
        map.put("isShow", isShow);
    }

    @Override
    protected void getVelocityParamsForView(Map<String, Object> map, AbstractDescriptor abstractDescriptor) {
        ConditionDescriptor conditionDescriptor = (ConditionDescriptor) abstractDescriptor;
        JSONObject jsonObject = JSON.parseObject((String) conditionDescriptor.getArgs().get("paramsJson"));
        String sourceIssue = String.valueOf(jsonObject.get("sourceIssue"));
        String customFieldId = String.valueOf(jsonObject.get("customField"));
        String targetIssue = String.valueOf(jsonObject.get("targetIssue"));
        String customField1Id = String.valueOf(jsonObject.get("customField1"));
        String isEqual = String.valueOf(jsonObject.get("isEqual"));
        String isShow = String.valueOf(jsonObject.get("isShow"));
        CustomField customField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(customFieldId);
        CustomField customField1 = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(customField1Id);

        map.put("sourceIssue", "null".equals(sourceIssue) ? "当前问题" : Constant.copyTypeMap.getOrDefault(sourceIssue, "未知关系"));
        map.put("customField", customField == null ? "该字段不存在" : customField.getFieldName());
        map.put("targetIssue", "null".equals(targetIssue) ? "当前问题" : Constant.copyTypeMap.getOrDefault(targetIssue, "未知关系"));
        map.put("customField1", customField1 == null ? "该字段不存在" : customField1.getFieldName());
        map.put("isEqual", "true".equals(isEqual) ? "相等" : "不相等");
        map.put("isShow", "true".equals(isShow) ? "显示" : "不显示");
    }

    @Override
    public Map<String, ?> getDescriptorParams(Map<String, Object> map) {
        Map<String, Object> hashMap = new HashMap<>();
        try {
            String[] sourceIssue = (String[]) map.get("sourceIssue");
            String[] customField = (String[]) map.get("customField");
            String[] targetIssue = (String[]) map.get("targetIssue");
            String[] customField1 = (String[]) map.get("customField1");
            String[] isEqual = (String[]) map.get("isEqual");
            String[] isShow = (String[]) map.get("isShow");
            JSONObject resp = new JSONObject();
            resp.put("sourceIssue", sourceIssue[0]);
            resp.put("customField", customField[0]);
            resp.put("targetIssue", targetIssue[0]);
            resp.put("customField1", customField1[0]);
            resp.put("isEqual", isEqual[0]);
            resp.put("isShow", isShow[0]);
            hashMap.put("paramsJson", resp.toJSONString());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return hashMap;
    }
}