package com.eve.workflow.conditions;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.atlassian.jira.component.ComponentAccessor;
import com.atlassian.jira.issue.Issue;
import com.atlassian.jira.issue.fields.CustomField;
import com.atlassian.jira.workflow.condition.AbstractJiraCondition;
import com.eve.utils.Utils;
import com.opensymphony.module.propertyset.PropertySet;
import com.opensymphony.workflow.WorkflowException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/10/21
 */
public class JudgeFieldHasValueCondition extends AbstractJiraCondition {
    private static final Logger log = LoggerFactory.getLogger(JudgeFieldHasValueCondition.class);

    @Override
    public boolean passesCondition(Map transientVars, Map args, PropertySet ps) throws WorkflowException {
        try {
            Issue issue = super.getIssue(transientVars);
            //获取参数
            JSONObject jsonObject = JSON.parseObject((String) args.get("paramsJson"));
            String customFieldId = String.valueOf(jsonObject.get("customField"));
            String isEmpty = String.valueOf(jsonObject.get("isEmpty"));
            String isShow = String.valueOf(jsonObject.get("isShow"));
            //处理参数
            CustomField customField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(customFieldId);
            Object customFieldValue = issue.getCustomFieldValue(customField);
            boolean fieldIsEmpty = customFieldValue == null;
            boolean empty = Boolean.parseBoolean(isEmpty);
            boolean show = Boolean.parseBoolean(isShow);

            return show == (fieldIsEmpty == empty);
        } catch (Exception e) {
            log.error(Utils.errInfo(e));
            return true;
        }
    }
}
