package com.eve.workflow.conditions;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.atlassian.jira.component.ComponentAccessor;
import com.atlassian.jira.issue.fields.CustomField;
import com.atlassian.jira.plugin.workflow.AbstractWorkflowPluginFactory;
import com.atlassian.jira.plugin.workflow.WorkflowPluginConditionFactory;
import com.eve.utils.Constant;
import com.opensymphony.workflow.loader.AbstractDescriptor;
import com.opensymphony.workflow.loader.ConditionDescriptor;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/10/21
 */
public class JudgeFieldHasValueConditionFactory extends AbstractWorkflowPluginFactory implements WorkflowPluginConditionFactory {
    @Override
    protected void getVelocityParamsForInput(Map<String, Object> map) {
        List<CustomField> customFieldList = ComponentAccessor.getCustomFieldManager().getCustomFieldObjects();//获取全部字段
        map.put("customFieldList", customFieldList);
    }

    @Override
    protected void getVelocityParamsForEdit(Map<String, Object> map, AbstractDescriptor abstractDescriptor) {
        List<CustomField> customFieldList = ComponentAccessor.getCustomFieldManager().getCustomFieldObjects();//获取全部字段
        map.put("customFieldList", customFieldList);

        ConditionDescriptor conditionDescriptor = (ConditionDescriptor) abstractDescriptor;
        JSONObject jsonObject = JSON.parseObject((String) conditionDescriptor.getArgs().get("paramsJson"));
        String customField = String.valueOf(jsonObject.get("customField"));
        String isEmpty = String.valueOf(jsonObject.get("isEmpty"));
        String isShow = String.valueOf(jsonObject.get("isShow"));
        map.put("customField", customField);
        map.put("isEmpty", isEmpty);
        map.put("isShow", isShow);
    }

    @Override
    protected void getVelocityParamsForView(Map<String, Object> map, AbstractDescriptor abstractDescriptor) {
        ConditionDescriptor conditionDescriptor = (ConditionDescriptor) abstractDescriptor;
        JSONObject jsonObject = JSON.parseObject((String) conditionDescriptor.getArgs().get("paramsJson"));
        String customFieldId = String.valueOf(jsonObject.get("customField"));
        String isEmpty = String.valueOf(jsonObject.get("isEmpty"));
        String isShow = String.valueOf(jsonObject.get("isShow"));
        CustomField customField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(customFieldId);

        map.put("customField", customField == null ? "该字段不存在" : customField.getFieldName());
        map.put("isEmpty", "true".equals(isEmpty) ? "为空" : "不为空");
        map.put("isShow", "true".equals(isShow) ? "显示" : "不显示");
    }

    @Override
    public Map<String, ?> getDescriptorParams(Map<String, Object> map) {
        Map<String, Object> hashMap = new HashMap<>();
        try {
            String[] customField = (String[]) map.get("customField");
            String[] isEmpty = (String[]) map.get("isEmpty");
            String[] isShow = (String[]) map.get("isShow");
            JSONObject resp = new JSONObject();
            resp.put("customField", customField[0]);
            resp.put("isEmpty", isEmpty[0]);
            resp.put("isShow", isShow[0]);
            hashMap.put("paramsJson", resp.toJSONString());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return hashMap;
    }
}