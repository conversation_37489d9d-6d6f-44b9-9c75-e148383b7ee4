package com.eve.workflow.conditions;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.atlassian.jira.component.ComponentAccessor;
import com.atlassian.jira.exception.DataAccessException;
import com.atlassian.jira.issue.Issue;
import com.atlassian.jira.issue.MutableIssue;
import com.atlassian.jira.issue.fields.CustomField;
import com.atlassian.jira.user.ApplicationUser;
import com.atlassian.jira.workflow.condition.AbstractJiraCondition;
import com.atlassian.plugin.spring.scanner.annotation.component.Scanned;
import com.eve.utils.Utils;
import com.opensymphony.module.propertyset.PropertySet;
import com.opensymphony.workflow.WorkflowException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/8/4
 */
@Scanned
public class JudgeUserCountOfMultiUserCondition extends AbstractJiraCondition {
    private static final Logger log = LoggerFactory.getLogger(JudgeUserCountOfMultiUserCondition.class);

    @Override
    public boolean passesCondition(Map transientVars, Map args, PropertySet ps) throws WorkflowException {
        boolean judgeResult = true;
        try {
            Issue issue = super.getIssue(transientVars);
            JSONObject jsonObject = JSON.parseObject((String) args.get("judgeUserCountOfMultiUserJson"));
            String multiUser = String.valueOf(jsonObject.get("multiUser"));
            String compareType = String.valueOf(jsonObject.get("compareType"));
            String targetUserCount = String.valueOf(jsonObject.get("targetUserCount"));
            int targetUserCountInt = Integer.parseInt(targetUserCount);
            int userCount = 0;
            CustomField multiUserCustomField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(multiUser);
            if (multiUserCustomField == null) {
                return true;
            }
            List<ApplicationUser> applicationUserList = (List<ApplicationUser>) issue.getCustomFieldValue(multiUserCustomField);
            if (applicationUserList != null) {
                userCount = applicationUserList.size();
            }
            switch (compareType) {
                case "less":
                    if (userCount > targetUserCountInt) {
                        judgeResult = false;
                    }
                    break;
                case "more":
                    if (userCount < targetUserCountInt) {
                        judgeResult = false;
                    }
                    break;
                case "equal":
                    if (userCount != targetUserCountInt) {
                        judgeResult = false;
                    }
                    break;
                default:
                    break;
            }
            return judgeResult;
        } catch (Exception e) {
            log.error(Utils.errInfo(e));
            return true;
        }
    }
}
