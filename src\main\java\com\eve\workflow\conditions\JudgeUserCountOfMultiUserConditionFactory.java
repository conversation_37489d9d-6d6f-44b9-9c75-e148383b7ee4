package com.eve.workflow.conditions;

import com.alibaba.fastjson.JSONObject;
import com.atlassian.jira.component.ComponentAccessor;
import com.atlassian.jira.issue.fields.CustomField;
import com.atlassian.jira.plugin.workflow.AbstractWorkflowPluginFactory;
import com.atlassian.jira.plugin.workflow.WorkflowPluginConditionFactory;
import com.atlassian.plugin.spring.scanner.annotation.component.Scanned;
import com.eve.utils.Constant;
import com.opensymphony.workflow.loader.AbstractDescriptor;
import com.opensymphony.workflow.loader.ConditionDescriptor;
import com.opensymphony.workflow.loader.FunctionDescriptor;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/8/4
 */
@Scanned
public class JudgeUserCountOfMultiUserConditionFactory extends AbstractWorkflowPluginFactory implements WorkflowPluginConditionFactory {
    @Override
    protected void getVelocityParamsForInput(Map<String, Object> map) {
        List<CustomField> customFieldList = ComponentAccessor.getCustomFieldManager().getCustomFieldObjects();//获取全部字段
        List<CustomField> multiUserPickerCustomFieldList = new ArrayList();
        for (CustomField customField:customFieldList){
            if (Constant.multiUserPickerFieldType.equals(customField.getCustomFieldType().getKey())){
                multiUserPickerCustomFieldList.add(customField);
            }
        }
        map.put("customFieldList", multiUserPickerCustomFieldList);
    }

    @Override
    protected void getVelocityParamsForEdit(Map<String, Object> map, AbstractDescriptor abstractDescriptor) {
        List<CustomField> customFieldList = ComponentAccessor.getCustomFieldManager().getCustomFieldObjects();//获取全部字段
        List<CustomField> multiUserPickerCustomFieldList = new ArrayList();
        for (CustomField customField:customFieldList){
            if (Constant.multiUserPickerFieldType.equals(customField.getCustomFieldType().getKey())){
                multiUserPickerCustomFieldList.add(customField);
            }
        }
        map.put("customFieldList", multiUserPickerCustomFieldList);

        ConditionDescriptor conditionDescriptor = (ConditionDescriptor) abstractDescriptor;
        JSONObject jsonObject = JSONObject.parseObject((String) conditionDescriptor.getArgs().get("judgeUserCountOfMultiUserJson"));
        String multiUser = String.valueOf(jsonObject.get("multiUser"));
        String compareType = String.valueOf(jsonObject.get("compareType"));
        String targetUserCount = String.valueOf(jsonObject.get("targetUserCount"));
        map.put("multiUser", multiUser);
        map.put("compareType", compareType);
        map.put("targetUserCount", targetUserCount);
    }

    @Override
    protected void getVelocityParamsForView(Map<String, Object> map, AbstractDescriptor abstractDescriptor) {
        ConditionDescriptor conditionDescriptor = (ConditionDescriptor) abstractDescriptor;
        JSONObject jsonObject = JSONObject.parseObject((String) conditionDescriptor.getArgs().get("judgeUserCountOfMultiUserJson"));
        String multiUserFieldId = String.valueOf(jsonObject.get("multiUser"));
        String compareType = String.valueOf(jsonObject.get("compareType"));
        String targetUserCount = String.valueOf(jsonObject.get("targetUserCount"));
        CustomField customField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(multiUserFieldId);
        if (customField != null) {
            map.put("multiUser", customField.getFieldName());
        }
        map.put("compareType", compareType);
        map.put("targetUserCount", targetUserCount);
    }

    @Override
    public Map<String, ?> getDescriptorParams(Map<String, Object> map) {
        Map<String, Object> hashMap = new HashMap<>();
        try {
            String[] multiUser = (String[]) map.get("multiUser");
            String[] compareType = (String[]) map.get("compareType");
            String[] targetUserCount = (String[]) map.get("targetUserCount");
            JSONObject resp = new JSONObject();
            resp.put("multiUser", multiUser[0]);
            resp.put("compareType", compareType[0]);
            resp.put("targetUserCount", targetUserCount[0]);
            hashMap.put("judgeUserCountOfMultiUserJson", resp.toJSONString());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return hashMap;
    }
}
