package com.eve.workflow.conditions;

import com.alibaba.fastjson.JSONObject;
import com.atlassian.jira.bc.issue.search.SearchService;
import com.atlassian.jira.component.ComponentAccessor;
import com.atlassian.jira.issue.Issue;
import com.atlassian.jira.issue.fields.CustomField;
import com.atlassian.jira.issue.search.SearchResults;
import com.atlassian.jira.user.ApplicationUser;
import com.atlassian.jira.web.bean.PagerFilter;
import com.atlassian.jira.workflow.condition.AbstractJiraCondition;
import com.atlassian.plugin.spring.scanner.annotation.component.Scanned;
import com.atlassian.query.Query;
import com.eve.utils.JiraCustomTool;
import com.eve.utils.Utils;
import com.opensymphony.module.propertyset.PropertySet;
import com.opensymphony.workflow.WorkflowException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.ObjectUtils;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/8/4
 */
@Scanned
public class MatchJqlInIssueCondition extends AbstractJiraCondition {
    private static final Logger log = LoggerFactory.getLogger(MatchJqlInIssueCondition.class);
    private JiraCustomTool jiraCustomTool;

    public MatchJqlInIssueCondition(JiraCustomTool jiraCustomTool) {
        this.jiraCustomTool = jiraCustomTool;
    }

    @Override
    public boolean passesCondition(Map transientVars, Map args, PropertySet ps) throws WorkflowException {
        try {
            Issue issue = super.getIssue(transientVars);
            JSONObject jsonObject = JSONObject.parseObject((String) args.get("matchJqlInIssueConditionJson"));
            String jqlStr = String.valueOf(jsonObject.get("jqlStr"));

            String jqlConditionEnabled = String.valueOf(jsonObject.get("jqlConditionEnabled"));
            String jqlCondition = String.valueOf(jsonObject.get("jqlCondition"));
            //需要通过jql校验才执行
            ApplicationUser currentUser = ComponentAccessor.getJiraAuthenticationContext().getLoggedInUser();
            if ("true".equals(jqlConditionEnabled) && !jiraCustomTool.matchJql(issue, jqlCondition, currentUser)) {
                return false;//jql条件激活且不满足jql条件，不执行该功能
            }
            if (ObjectUtils.isEmpty(jqlStr) || "null".equals(jqlStr)) {
                return true;
            }
            jqlStr = "issueKey = " + issue.getKey() + " AND (" + jqlStr + ")";
            log.info("拼接后的查询JQL：" + jqlStr);
            SearchService searchService = ComponentAccessor.getComponentOfType(SearchService.class);
            SearchService.ParseResult parseResult = searchService.parseQuery(currentUser, jqlStr);
            if (parseResult.isValid()) {
                Query query = parseResult.getQuery();
                SearchResults searchResults = searchService.search(currentUser, query, PagerFilter.getUnlimitedFilter());
                List<Issue> storyIssueList = searchResults.getResults();
                return !(storyIssueList == null || storyIssueList.isEmpty());
            }
            return true;
        } catch (Exception e) {
            log.error(Utils.errInfo(e));
            return true;
        }
    }
}
