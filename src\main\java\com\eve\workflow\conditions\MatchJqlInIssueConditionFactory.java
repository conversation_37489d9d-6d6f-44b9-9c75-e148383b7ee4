package com.eve.workflow.conditions;

import com.alibaba.fastjson.JSONObject;
import com.atlassian.jira.component.ComponentAccessor;
import com.atlassian.jira.issue.fields.CustomField;
import com.atlassian.jira.plugin.workflow.AbstractWorkflowPluginFactory;
import com.atlassian.jira.plugin.workflow.WorkflowPluginConditionFactory;
import com.atlassian.plugin.spring.scanner.annotation.component.Scanned;
import com.eve.utils.Constant;
import com.opensymphony.workflow.loader.AbstractDescriptor;
import com.opensymphony.workflow.loader.ConditionDescriptor;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/8/4
 */
@Scanned
public class MatchJqlInIssueConditionFactory extends AbstractWorkflowPluginFactory implements WorkflowPluginConditionFactory {
    @Override
    protected void getVelocityParamsForInput(Map<String, Object> map) {

    }

    @Override
    protected void getVelocityParamsForEdit(Map<String, Object> map, AbstractDescriptor abstractDescriptor) {
        ConditionDescriptor conditionDescriptor = (ConditionDescriptor) abstractDescriptor;
        JSONObject jsonObject = JSONObject.parseObject((String) conditionDescriptor.getArgs().get("matchJqlInIssueConditionJson"));
        String jqlStr = String.valueOf(jsonObject.get("jqlStr"));
        String jqlConditionEnabled = String.valueOf(jsonObject.get("jqlConditionEnabled"));
        String jqlCondition = String.valueOf(jsonObject.get("jqlCondition"));

        map.put("jqlConditionEnabled", jqlConditionEnabled);
        map.put("jqlCondition", jqlCondition);
        map.put("jqlStr", jqlStr);
    }

    @Override
    protected void getVelocityParamsForView(Map<String, Object> map, AbstractDescriptor abstractDescriptor) {
        ConditionDescriptor conditionDescriptor = (ConditionDescriptor) abstractDescriptor;
        JSONObject jsonObject = JSONObject.parseObject((String) conditionDescriptor.getArgs().get("matchJqlInIssueConditionJson"));
        String jqlStr = String.valueOf(jsonObject.get("jqlStr"));
        String jqlConditionEnabled = String.valueOf(jsonObject.get("jqlConditionEnabled"));
        String jqlCondition = String.valueOf(jsonObject.get("jqlCondition"));

        map.put("jqlConditionEnabled", jqlConditionEnabled);
        map.put("jqlCondition", jqlCondition);
        map.put("jqlStr", jqlStr);
    }

    @Override
    public Map<String, ?> getDescriptorParams(Map<String, Object> map) {
        Map<String, Object> hashMap = new HashMap<>();
        try {
            String[] jqlStr = (String[]) map.get("jqlStr");
            String[] jqlConditionEnabled = (String[]) map.get("jqlConditionEnabled");
            String[] jqlCondition = (String[]) map.get("jqlCondition");
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("jqlStr", jqlStr[0]);
            jsonObject.put("jqlConditionEnabled", jqlConditionEnabled[0]);
            jsonObject.put("jqlCondition", jqlCondition[0]);
            hashMap.put("matchJqlInIssueConditionJson", jsonObject.toJSONString());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return hashMap;
    }
}
