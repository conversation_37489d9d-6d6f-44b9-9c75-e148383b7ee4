package com.eve.workflow.conditions;

import cn.hutool.core.util.ArrayUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.atlassian.jira.component.ComponentAccessor;
import com.atlassian.jira.issue.Issue;
import com.atlassian.jira.issue.fields.CustomField;
import com.atlassian.jira.user.ApplicationUser;
import com.atlassian.jira.workflow.condition.AbstractJiraCondition;
import com.atlassian.plugin.spring.scanner.annotation.component.Scanned;
import com.eve.utils.Utils;
import com.opensymphony.module.propertyset.PropertySet;
import com.opensymphony.workflow.WorkflowException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/8/4
 */
@Scanned
public class MultiApprovalCheckCondition extends AbstractJiraCondition {
    private static final Logger log = LoggerFactory.getLogger(MultiApprovalCheckCondition.class);

    @Override
    public boolean passesCondition(Map transientVars, Map args, PropertySet ps) throws WorkflowException {
        boolean judgeResult = true;
        try {
            Issue issue = super.getIssue(transientVars);
            JSONObject jsonObject = JSON.parseObject((String) args.get("parmJson"));
            String multiUser = String.valueOf(jsonObject.get("multiUser"));
            String showCondition = String.valueOf(jsonObject.get("showCondition"));
            String multiUser2 = String.valueOf(jsonObject.get("multiUser2"));
//            String targetUserCount = String.valueOf(jsonObject.get("targetUserCount"));
//            int targetUserCountInt = Integer.parseInt(targetUserCount);
            int userCount = 0;
            int userCount2 = 0;
            CustomField multiUserCustomField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(multiUser);
            CustomField multiUserCustomField2 = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(multiUser2);
            if (multiUserCustomField == null || multiUserCustomField2 == null) {
                return true;
            }
            List<ApplicationUser> applicationUserList = (List<ApplicationUser>) issue.getCustomFieldValue(multiUserCustomField);
            List<ApplicationUser> applicationUserList2 = (List<ApplicationUser>) issue.getCustomFieldValue(multiUserCustomField2);
            //选择的签审人员字段为空，无需进行签审
            if (ArrayUtil.isEmpty(applicationUserList)) {
                return false;
            }
            //签审人员字段不为空，已签审为空，进行签审
            if (ArrayUtil.isEmpty(applicationUserList2)) {
                return true;
            }
            //签审人员字段不为空，已签审不为空，进行签审判断
            //已签审完成的用户名
            List<String> userNameList = applicationUserList2.stream().map(ApplicationUser::getUsername).collect(Collectors.toList());
            //找出不在已签审列表的用户
            Optional<ApplicationUser> optional = applicationUserList.stream().filter(e -> !userNameList.contains(e.getUsername())).findAny();
            //若存在，则返回true，显示转换，继续进行签审
            judgeResult = optional.isPresent();
            if ("noApproval".equals(showCondition)) {
                judgeResult = !judgeResult;
            }

//            if (applicationUserList != null) {
//                userCount = applicationUserList.size();
//            }

//            switch (compareType) {
//                case "less":
//                    judgeResult = userCount < userCount2;
//                    break;
//                case "more":
//                    judgeResult = userCount > userCount2;
//                    break;
//                case "equal":
//                    judgeResult = userCount > userCount2;
//                    break;
//                default:
//                    break;
//            }
            return judgeResult;
        } catch (Exception e) {
            log.error(Utils.errInfo(e));
            return true;
        }
    }
}
