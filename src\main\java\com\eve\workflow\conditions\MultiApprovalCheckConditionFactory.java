package com.eve.workflow.conditions;

import com.alibaba.fastjson.JSONObject;
import com.atlassian.jira.component.ComponentAccessor;
import com.atlassian.jira.issue.fields.CustomField;
import com.atlassian.jira.plugin.workflow.AbstractWorkflowPluginFactory;
import com.atlassian.jira.plugin.workflow.WorkflowPluginConditionFactory;
import com.atlassian.plugin.spring.scanner.annotation.component.Scanned;
import com.eve.utils.Constant;
import com.opensymphony.workflow.loader.AbstractDescriptor;
import com.opensymphony.workflow.loader.ConditionDescriptor;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/8/4
 */
@Scanned
public class MultiApprovalCheckConditionFactory extends AbstractWorkflowPluginFactory implements WorkflowPluginConditionFactory {
    @Override
    protected void getVelocityParamsForInput(Map<String, Object> map) {
        List<CustomField> customFieldList = ComponentAccessor.getCustomFieldManager().getCustomFieldObjects();//获取全部字段
        List<CustomField> multiUserPickerCustomFieldList = new ArrayList();
        for (CustomField customField:customFieldList){
            if (Constant.multiUserPickerFieldType.equals(customField.getCustomFieldType().getKey())){
                multiUserPickerCustomFieldList.add(customField);
            }
        }
        map.put("customFieldList", multiUserPickerCustomFieldList);
    }

    @Override
    protected void getVelocityParamsForEdit(Map<String, Object> map, AbstractDescriptor abstractDescriptor) {
        List<CustomField> customFieldList = ComponentAccessor.getCustomFieldManager().getCustomFieldObjects();//获取全部字段
        List<CustomField> multiUserPickerCustomFieldList = new ArrayList();
        for (CustomField customField:customFieldList){
            if (Constant.multiUserPickerFieldType.equals(customField.getCustomFieldType().getKey())){
                multiUserPickerCustomFieldList.add(customField);
            }
        }
        map.put("customFieldList", multiUserPickerCustomFieldList);

        ConditionDescriptor conditionDescriptor = (ConditionDescriptor) abstractDescriptor;
        JSONObject jsonObject = JSONObject.parseObject((String) conditionDescriptor.getArgs().get("parmJson"));
        String multiUser = String.valueOf(jsonObject.get("multiUser"));
        String multiUser2 = String.valueOf(jsonObject.get("multiUser2"));
        String showCondition = String.valueOf(jsonObject.get("showCondition"));
//        String targetUserCount = String.valueOf(jsonObject.get("targetUserCount"));
        map.put("multiUser", multiUser);
        map.put("multiUser2", multiUser2);
        map.put("showCondition", showCondition);
//        map.put("targetUserCount", targetUserCount);
    }

    @Override
    protected void getVelocityParamsForView(Map<String, Object> map, AbstractDescriptor abstractDescriptor) {
        ConditionDescriptor conditionDescriptor = (ConditionDescriptor) abstractDescriptor;
        JSONObject jsonObject = JSONObject.parseObject((String) conditionDescriptor.getArgs().get("parmJson"));
        String multiUserFieldId = String.valueOf(jsonObject.get("multiUser"));
        String multiUserFieldId2 = String.valueOf(jsonObject.get("multiUser2"));
        String showCondition = String.valueOf(jsonObject.get("showCondition"));
//        String targetUserCount = String.valueOf(jsonObject.get("targetUserCount"));
        CustomField customField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(multiUserFieldId);
        CustomField customField2 = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(multiUserFieldId2);
        if (customField != null) {
            map.put("multiUser", customField.getFieldName());
        }
        if (customField2 != null) {
            map.put("multiUser2", customField2.getFieldName());
        }
        map.put("showCondition", showCondition);
//        map.put("targetUserCount", targetUserCount);
    }

    @Override
    public Map<String, ?> getDescriptorParams(Map<String, Object> map) {
        Map<String, Object> hashMap = new HashMap<>();
        try {
            String[] multiUser = (String[]) map.get("multiUser");
            String[] multiUser2 = (String[]) map.get("multiUser2");
            String[] showCondition = (String[]) map.get("showCondition");
//            String[] targetUserCount = (String[]) map.get("targetUserCount");
            JSONObject resp = new JSONObject();
            resp.put("multiUser", multiUser[0]);
            resp.put("multiUser2", multiUser2[0]);
            resp.put("showCondition", showCondition[0]);
//            resp.put("targetUserCount", targetUserCount[0]);
            hashMap.put("parmJson", resp.toJSONString());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return hashMap;
    }
}
