package com.eve.workflow.conditions;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.atlassian.jira.component.ComponentAccessor;
import com.atlassian.jira.issue.Issue;
import com.atlassian.jira.issue.MutableIssue;
import com.atlassian.jira.issue.customfields.option.Option;
import com.atlassian.jira.issue.fields.CustomField;
import com.atlassian.jira.user.ApplicationUser;
import com.atlassian.jira.workflow.condition.AbstractJiraCondition;
import com.atlassian.plugin.spring.scanner.annotation.component.Scanned;
import com.eve.utils.JiraCustomTool;
import com.eve.utils.Utils;
import com.opensymphony.module.propertyset.PropertySet;
import com.opensymphony.workflow.WorkflowException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.ObjectUtils;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/9/9
 */
@Scanned
public class MultiselectContainOptionCondition extends AbstractJiraCondition {
    private static final Logger log = LoggerFactory.getLogger(MultiselectContainOptionCondition.class);
    private JiraCustomTool jiraCustomTool;

    public MultiselectContainOptionCondition(JiraCustomTool jiraCustomTool) {
        this.jiraCustomTool = jiraCustomTool;
    }

    @Override
    public boolean passesCondition(Map transientVars, Map args, PropertySet ps) throws WorkflowException {
        try {
            Issue issue = super.getIssue(transientVars);
            JSONObject jsonObject = JSON.parseObject((String) args.get("parmJson"));

            String fieldId = String.valueOf(jsonObject.get("fieldId"));
            List<String> optionIdList = JSON.parseArray(String.valueOf(jsonObject.get("optionIdList")), String.class);
            String isShow = String.valueOf(jsonObject.get("isShow"));//null true false

            String jqlConditionEnabled = String.valueOf(jsonObject.get("jqlConditionEnabled"));
            String jqlCondition = String.valueOf(jsonObject.get("jqlCondition"));
            //需要通过jql校验才执行
            ApplicationUser currentUser = ComponentAccessor.getJiraAuthenticationContext().getLoggedInUser();
            if ("true".equals(jqlConditionEnabled) && !jiraCustomTool.matchJql(issue, jqlCondition, currentUser)) {
                return false;//jql条件激活且不满足jql条件，不执行该功能
            }
//            boolean show = true;
            MutableIssue mutableIssue = ComponentAccessor.getIssueManager().getIssueObject(issue.getKey());
            CustomField customField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(fieldId);
            if (customField != null && !ObjectUtils.isEmpty(optionIdList)) {
                Object object = issue.getCustomFieldValue(customField);
//                log.error("issueKey:{}，字段值：{}",issue.getKey(),object);
                if (object == null) {
                    return false;
                } else if (object instanceof Option) {
                    Option option = (Option) object;
                    return "false".equals(isShow) ? !optionIdList.contains(option.getOptionId() + "") : optionIdList.contains(option.getOptionId() + "");
                } else if (object instanceof Map) {
                    HashMap<Integer, Option> optionMap = (HashMap<Integer, Option>) object;
                    List<String> collect = optionMap.values().stream().map(e -> e.getOptionId() + "").collect(Collectors.toList());
                    return "false".equals(isShow) ? optionIdList.stream().noneMatch(collect::contains) : optionIdList.stream().anyMatch(collect::contains);
                } else if (object instanceof Collection) {
                    List<Option> optionList = (List<Option>) object;
                    List<String> collect = optionList.stream().map(e -> e.getOptionId() + "").collect(Collectors.toList());
                    return "false".equals(isShow) ? optionIdList.stream().noneMatch(collect::contains) : optionIdList.stream().anyMatch(collect::contains);
                }
            }
            return true;
        } catch (Exception e) {
            log.error(Utils.errInfo(e));
            return true;
        }
    }
}