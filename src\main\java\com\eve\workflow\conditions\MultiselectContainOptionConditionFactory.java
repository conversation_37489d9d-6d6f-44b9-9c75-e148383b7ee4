package com.eve.workflow.conditions;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.atlassian.jira.component.ComponentAccessor;
import com.atlassian.jira.issue.customfields.manager.OptionsManager;
import com.atlassian.jira.issue.customfields.manager.OptionsService;
import com.atlassian.jira.issue.customfields.option.Option;
import com.atlassian.jira.issue.customfields.option.OptionUtils;
import com.atlassian.jira.issue.fields.CustomField;
import com.atlassian.jira.plugin.workflow.AbstractWorkflowPluginFactory;
import com.atlassian.jira.plugin.workflow.WorkflowPluginConditionFactory;
import com.atlassian.plugin.spring.scanner.annotation.component.Scanned;
import com.eve.beans.OptionsValueBean;
import com.eve.beans.ResultBean;
import com.eve.services.ServiceUpdateOption;
import com.eve.utils.Utils;
import com.opensymphony.workflow.loader.AbstractDescriptor;
import com.opensymphony.workflow.loader.ConditionDescriptor;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/9/9
 */
@Scanned
public class MultiselectContainOptionConditionFactory extends AbstractWorkflowPluginFactory implements WorkflowPluginConditionFactory {

    private OptionsManager optionsManager;

    public MultiselectContainOptionConditionFactory(OptionsManager optionsManager){
        this.optionsManager = optionsManager;
    }
    @Override
    protected void getVelocityParamsForInput(Map<String, Object> map) {
        List<String> fieldTypeList = Arrays.asList(
                "com.atlassian.jira.plugin.system.customfieldtypes:cascadingselect",
                "com.atlassian.jira.plugin.system.customfieldtypes:select",
                "com.atlassian.jira.plugin.system.customfieldtypes:multiselect",
                "com.atlassian.jira.plugin.system.customfieldtypes:radiobuttons",
                "com.atlassian.jira.plugin.system.customfieldtypes:multicheckboxes");
        List<CustomField> customFieldList = ComponentAccessor.getCustomFieldManager().getCustomFieldObjects();
        List<CustomField> customFieldSelectList = customFieldList.stream().filter(customField -> fieldTypeList.contains(customField.getCustomFieldType().getKey())).collect(Collectors.toList());
        CustomField customField = customFieldSelectList.get(0);
        ResultBean resultBean = ServiceUpdateOption.queryOptionsList(customField.getIdAsLong());

        List<OptionsValueBean> optionsList = (List<OptionsValueBean>)resultBean.getValue();

        map.put("customFieldList", customFieldSelectList);
        map.put("optionsList", optionsList);
    }

    @Override
    protected void getVelocityParamsForEdit(Map<String, Object> map, AbstractDescriptor abstractDescriptor) {
        List<String> fieldTypeList = Arrays.asList(
                "com.atlassian.jira.plugin.system.customfieldtypes:cascadingselect",
                "com.atlassian.jira.plugin.system.customfieldtypes:select",
                "com.atlassian.jira.plugin.system.customfieldtypes:multiselect",
                "com.atlassian.jira.plugin.system.customfieldtypes:radiobuttons",
                "com.atlassian.jira.plugin.system.customfieldtypes:multicheckboxes");
        List<CustomField> customFieldList = ComponentAccessor.getCustomFieldManager().getCustomFieldObjects();
        List<CustomField> customFieldListOptions = customFieldList.stream().filter(customField -> fieldTypeList.contains(customField.getCustomFieldType().getKey())).collect(Collectors.toList());

        ConditionDescriptor conditionDescriptor = (ConditionDescriptor) abstractDescriptor;
        JSONObject jsonObject = JSON.parseObject((String) conditionDescriptor.getArgs().get("parmJson"));
        String fieldId = String.valueOf(jsonObject.get("fieldId"));
        List<String> optionIdList = JSON.parseArray(String.valueOf(jsonObject.get("optionIdList")), String.class);
        String isShow = String.valueOf(jsonObject.get("isShow"));

        String jqlConditionEnabled = String.valueOf(jsonObject.get("jqlConditionEnabled"));
        String jqlCondition = String.valueOf(jsonObject.get("jqlCondition"));

        ResultBean resultBean = ServiceUpdateOption.queryOptionsList(Long.parseLong(fieldId.split("_")[1]));
        List<OptionsValueBean> optionsList = (List<OptionsValueBean>)resultBean.getValue();

        map.put("customFieldList", customFieldListOptions);
        map.put("optionsList", optionsList);
        
        map.put("fieldId", fieldId);

        map.put("optionIdList", optionIdList);
        map.put("isShow", isShow);
        map.put("jqlConditionEnabled", jqlConditionEnabled);
        map.put("jqlCondition", jqlCondition);
        
    }

    @Override
    protected void getVelocityParamsForView(Map<String, Object> map, AbstractDescriptor abstractDescriptor) {
        ConditionDescriptor conditionDescriptor = (ConditionDescriptor) abstractDescriptor;
        JSONObject jsonObject = JSON.parseObject((String) conditionDescriptor.getArgs().get("parmJson"));
        String fieldId = String.valueOf(jsonObject.get("fieldId"));

        CustomField filetypeCustField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(fieldId);
        String fieldName = filetypeCustField == null ? "字段已删除" + fieldId : filetypeCustField.getFieldName();
        List<String> optionIdList = JSON.parseArray(String.valueOf(jsonObject.get("optionIdList")), String.class);
        String optionValue = optionIdList.stream().map(e -> {
            Option option = optionsManager.findByOptionId(Long.parseLong(e));
            return option == null ? "-" : option.getValue();
        }).filter(anObject -> !"-".equals(anObject)).collect(Collectors.joining(";"));
        String isShow = String.valueOf(jsonObject.get("isShow"));
        String jqlConditionEnabled = String.valueOf(jsonObject.get("jqlConditionEnabled"));
        String jqlCondition = String.valueOf(jsonObject.get("jqlCondition"));

        map.put("jqlConditionEnabled", jqlConditionEnabled);
        map.put("jqlCondition", jqlCondition);
        map.put("isShow", isShow);
        map.put("fieldName", fieldName);
//        map.put("optionIdList", optionIdList);
        map.put("optionValue", optionValue);
    }

    @Override
    public Map<String, ?> getDescriptorParams(Map<String, Object> map) {
        Map<String, Object> hashMap = new HashMap<>();
        try {
            String[] fieldId = (String[]) map.get("fieldId");
            String[] optionIdList = (String[]) map.get("optionIdList");
            String[] isShow = (String[]) map.get("isShow");
            String[] jqlConditionEnabled = (String[]) map.get("jqlConditionEnabled");
            String[] jqlCondition = (String[]) map.get("jqlCondition");
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("fieldId", fieldId[0]);
            jsonObject.put("optionIdList", optionIdList);
            jsonObject.put("isShow", isShow[0]);
            jsonObject.put("jqlConditionEnabled", jqlConditionEnabled[0]);
            jsonObject.put("jqlCondition", jqlCondition[0]);
            hashMap.put("parmJson", jsonObject.toJSONString());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return hashMap;
    }
}
