package com.eve.workflow.postfunctions;

import com.alibaba.fastjson.JSONObject;
import com.atlassian.jira.bc.issue.search.SearchService;
import com.atlassian.jira.component.ComponentAccessor;
import com.atlassian.jira.event.type.EventDispatchOption;
import com.atlassian.jira.issue.Issue;
import com.atlassian.jira.issue.MutableIssue;
import com.atlassian.jira.issue.fields.CustomField;
import com.atlassian.jira.issue.search.SearchException;
import com.atlassian.jira.issue.search.SearchResults;
import com.atlassian.jira.project.Project;
import com.atlassian.jira.user.ApplicationUser;
import com.atlassian.jira.web.bean.PagerFilter;
import com.atlassian.query.Query;
import com.eve.utils.Constant;
import com.opensymphony.module.propertyset.PropertySet;
import com.opensymphony.workflow.WorkflowException;
import electric.soap.rpc.In;

import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2022/7/19 14:12
 */
public class AddIssueNumberFunction extends JsuWorkflowFunction {

    @Override
    public void doIt(Map transientVars, Map args, PropertySet ps) throws WorkflowException {
        try {

            //取出当前的issue对应的项目和查找今天最新的问题编号
            MutableIssue mutableIssue =super.getIssue(transientVars);
            Long projectId =  mutableIssue.getProjectId();
            Date date = new Date();
            SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMdd");
            String nowTime = formatter.format(date);
            CustomField noCustomId = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(Constant.issueNumber);

            Project projectObj = ComponentAccessor.getProjectManager().getProjectObj(projectId);
            String projectKey= projectObj.getKey();
            String jql = "createdDate >= startOfDay() and project = "+projectId;
            ApplicationUser userAdmin = ComponentAccessor.getUserManager().getUserByName("hktx");
            SearchService searchService = ComponentAccessor.getComponentOfType(SearchService.class);
            SearchService.ParseResult parseResult = searchService.parseQuery(userAdmin, jql);
            Query query =parseResult.getQuery();//查询出今日的ISSUE
            SearchResults searchResults = searchService.search(userAdmin, query, PagerFilter.getUnlimitedFilter());
            List<Issue> issueList = searchResults.getResults();
            List<Integer> numberList = new ArrayList<>();
            if (issueList.isEmpty()){
                //创建编号
                String issueBh = "PR-"+projectKey+"-"+nowTime+"-01";
                mutableIssue.setCustomFieldValue(noCustomId,issueBh);
            }
            else {
                //查找最大的编号+1
                for (Issue a:issueList){
                    String issueNumber =  (String) a.getCustomFieldValue(noCustomId);
                    String no;
                    if (issueNumber!=null){
                         no = issueNumber.substring(issueNumber.lastIndexOf("-")+1);
                    }
                    else {
                         no = "0";
                    }
                    int  number = Integer.valueOf(no).intValue();
                    numberList.add(number);
                }
                int maxNo = Collections.max(numberList);//最大的no
                maxNo=maxNo+1;
                String issueNo;
                if (maxNo<10){
                     issueNo =  "0"+maxNo;
                } else {
                     issueNo = String.valueOf(maxNo);
                }
                String issueBh = "PR-"+projectKey+"-"+nowTime+"-"+issueNo;
                mutableIssue.setCustomFieldValue(noCustomId,issueBh);

            }
            ApplicationUser currentUser = ComponentAccessor.getJiraAuthenticationContext().getLoggedInUser();
            ComponentAccessor.getIssueManager().updateIssue(currentUser, mutableIssue, EventDispatchOption.ISSUE_UPDATED, false);
        } catch (Exception e) {
            e.printStackTrace();
            throw new WorkflowException("Exception: " + e);
        }
    }
}
