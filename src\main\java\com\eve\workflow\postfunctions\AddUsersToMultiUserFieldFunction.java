package com.eve.workflow.postfunctions;

import com.alibaba.fastjson.JSONObject;
import com.atlassian.jira.component.ComponentAccessor;
import com.atlassian.jira.issue.MutableIssue;
import com.atlassian.jira.issue.fields.CustomField;
import com.atlassian.jira.user.ApplicationUser;
import com.eve.utils.JiraCustomTool;
import com.opensymphony.module.propertyset.PropertySet;
import com.opensymphony.workflow.WorkflowException;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/7/28
 */
public class AddUsersToMultiUserFieldFunction extends JsuWorkflowFunction {
    private JiraCustomTool jiraCustomTool;

    public AddUsersToMultiUserFieldFunction(JiraCustomTool jiraCustomTool) {
        this.jiraCustomTool = jiraCustomTool;
    }

    @Override
    public void doIt(Map transientVars, Map args, PropertySet ps) throws WorkflowException {
        try {
            MutableIssue mutableIssue = super.getIssue(transientVars);
            ApplicationUser currentUser = ComponentAccessor.getJiraAuthenticationContext().getLoggedInUser();
            Integer actionId = (Integer) transientVars.get("actionId");
            JSONObject jsonObject = JSONObject.parseObject((String) args.get("parmJson"));
            String userFieldId = String.valueOf(jsonObject.get("multiUserField"));
            String userNames = String.valueOf(jsonObject.get("userName"));
//            List<String> userList = JSON.parseArray(String.valueOf(jsonObject.get("userList")), String.class);
            String jqlConditionEnabled = String.valueOf(jsonObject.get("jqlConditionEnabled"));
            String jqlCondition = String.valueOf(jsonObject.get("jqlCondition"));

            //需要通过jql校验才执行
            if ("true".equals(jqlConditionEnabled) && !jiraCustomTool.matchJql(mutableIssue, jqlCondition, currentUser)) {
                return;
            }
            CustomField customField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(userFieldId);
            Object customFieldValue1 = mutableIssue.getCustomFieldValue(customField);
            List<ApplicationUser> customFieldValue = customFieldValue1 == null ? new ArrayList<>() : (List<ApplicationUser>) customFieldValue1;
            List<ApplicationUser> addUserList = new ArrayList<>();
            for (String userName : userNames.split(",")) {
                ApplicationUser applicationUser = ComponentAccessor.getUserManager().getUserByNameEvenWhenUnknown(userName);
                if ("assignee".equals(userFieldId)) {
                    if (ComponentAccessor.getUserManager().isUserExisting(applicationUser)) {
                        mutableIssue.setAssignee(applicationUser);
                        break;
                    }
                }else if ("reporter".equals(userFieldId)){
                    if (ComponentAccessor.getUserManager().isUserExisting(applicationUser)) {
                        mutableIssue.setReporter(applicationUser);
                        break;
                    }
                } else{
                    if (customField != null && ComponentAccessor.getUserManager().isUserExisting(applicationUser) && !customFieldValue.contains(applicationUser)) {
                        addUserList.add(applicationUser);
//                        mutableIssue.setCustomFieldValue(customField, applicationUser);
                    }
                }
            }
            if (!addUserList.isEmpty()) {
                addUserList.addAll(customFieldValue);//添加回旧用户
                mutableIssue.setCustomFieldValue(customField, addUserList);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new WorkflowException(e);
        }
    }
}
