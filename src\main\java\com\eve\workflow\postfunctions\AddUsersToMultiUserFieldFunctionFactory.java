package com.eve.workflow.postfunctions;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.atlassian.jira.component.ComponentAccessor;
import com.atlassian.jira.issue.fields.CustomField;
import com.atlassian.jira.plugin.workflow.AbstractWorkflowPluginFactory;
import com.atlassian.jira.plugin.workflow.WorkflowPluginFunctionFactory;
import com.eve.beans.CopyFieldBean;
import com.eve.utils.Constant;
import com.opensymphony.workflow.loader.AbstractDescriptor;
import com.opensymphony.workflow.loader.FunctionDescriptor;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/7/28
 */
public class AddUsersToMultiUserFieldFunctionFactory extends AbstractWorkflowPluginFactory implements WorkflowPluginFunctionFactory {
    @Override
    protected void getVelocityParamsForInput(Map<String, Object> map) {
        List<CustomField> customFieldList = ComponentAccessor.getCustomFieldManager().getCustomFieldObjects();//获取全部字段
//        List<CustomField> userPickerCustomFieldList = new ArrayList();
//        for (CustomField customField:customFieldList){
//            if (Constant.userPickerFieldType.equals(customField.getCustomFieldType().getKey())){
//                userPickerCustomFieldList.add(customField);
//            }
//        }
        List<CopyFieldBean> copyFieldBeanList = new ArrayList<>(Constant.assigneeAndReporter);
        for (CustomField customField:customFieldList){
//            CustomFieldType customFieldType = customField.getCustomFieldType();
            if (Constant.multiUserPickerFieldType.equals(customField.getCustomFieldType().getKey())){
                CopyFieldBean copyFieldBean = new CopyFieldBean();
                copyFieldBean.setId(customField.getId());
                copyFieldBean.setName(customField.getFieldName());
                copyFieldBeanList.add(copyFieldBean);//用户多选字段
            }
        }
        map.put("customFieldList", copyFieldBeanList);
    }

    @Override
    protected void getVelocityParamsForEdit(Map<String, Object> map, AbstractDescriptor abstractDescriptor) {
        if (!(abstractDescriptor instanceof FunctionDescriptor)) {
            throw new IllegalArgumentException("Descriptor must be a ConditionDescriptor.");
        }
        List<CustomField> customFieldList = ComponentAccessor.getCustomFieldManager().getCustomFieldObjects();//获取全部字段
//        List<CustomField> multiUserPickerCustomFieldList = new ArrayList();
//        for (CustomField customField:customFieldList){
//            if (Constant.userPickerFieldType.equals(customField.getCustomFieldType().getKey())){
//                multiUserPickerCustomFieldList.add(customField);
//            }
//        }
        List<CopyFieldBean> copyFieldBeanList = new ArrayList<>(Constant.assigneeAndReporter);
        for (CustomField customField:customFieldList){
//            CustomFieldType customFieldType = customField.getCustomFieldType();
            if (Constant.userPickerFieldType.equals(customField.getCustomFieldType().getKey())){
                CopyFieldBean copyFieldBean = new CopyFieldBean();
                copyFieldBean.setId(customField.getId());
                copyFieldBean.setName(customField.getFieldName());
                copyFieldBeanList.add(copyFieldBean);//用户单选字段
            }
        }
        map.put("customFieldList", copyFieldBeanList);

        FunctionDescriptor conditionDescriptor = (FunctionDescriptor) abstractDescriptor;
        JSONObject jsonObject = JSONObject.parseObject((String) conditionDescriptor.getArgs().get("parmJson"));
        String multiUserField = String.valueOf(jsonObject.get("multiUserField"));
        String userName = String.valueOf(jsonObject.get("userName"));
//        List<String> userList = JSON.parseArray(String.valueOf(jsonObject.get("userList")), String.class);
        String jqlConditionEnabled = String.valueOf(jsonObject.get("jqlConditionEnabled"));
        String jqlCondition = String.valueOf(jsonObject.get("jqlCondition"));

        map.put("multiUserField", multiUserField);
        map.put("userName", userName);
        map.put("jqlConditionEnabled", jqlConditionEnabled);
        map.put("jqlCondition", jqlCondition);
    }

    @Override
    protected void getVelocityParamsForView(Map<String, Object> map, AbstractDescriptor abstractDescriptor) {
        if (!(abstractDescriptor instanceof FunctionDescriptor)) {
            throw new IllegalArgumentException("Descriptor must be a ConditionDescriptor.");
        }
        FunctionDescriptor conditionDescriptor = (FunctionDescriptor) abstractDescriptor;
        JSONObject jsonObject = JSONObject.parseObject((String) conditionDescriptor.getArgs().get("parmJson"));
        String userFieldId = String.valueOf(jsonObject.get("multiUserField"));
        String userName = String.valueOf(jsonObject.get("userName"));
//        List<String> userList = JSON.parseArray(String.valueOf(jsonObject.get("userList")), String.class);
        String jqlConditionEnabled = String.valueOf(jsonObject.get("jqlConditionEnabled"));
        String jqlCondition = String.valueOf(jsonObject.get("jqlCondition"));

        map.put("userName", userName);
        map.put("jqlConditionEnabled", jqlConditionEnabled);
        map.put("jqlCondition", jqlCondition);
        CustomField customField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(userFieldId);
        if (customField != null) {
            map.put("multiUserField", customField.getFieldName());
        }else {
            map.put("multiUserField", userFieldId);
        }
    }

    @Override
    public Map<String, ?> getDescriptorParams(Map<String, Object> map) {
        Map<String, Object> hashMap = new HashMap<>();
        try {
            String[] multiUserField = (String[]) map.get("multiUserField");
            String[] userName = (String[]) map.get("userName");
//            String[] userList = (String[]) map.get("userList");
            String[] jqlConditionEnabled = (String[]) map.get("jqlConditionEnabled");
            String[] jqlCondition = (String[]) map.get("jqlCondition");
            JSONObject resp = new JSONObject();
            resp.put("multiUserField", multiUserField[0]);
            resp.put("userName", userName[0]);
//            resp.put("userList", userList);
            resp.put("jqlConditionEnabled", jqlConditionEnabled[0]);
            resp.put("jqlCondition", jqlCondition[0]);
            hashMap.put("parmJson", resp.toJSONString());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return hashMap;
    }
}
