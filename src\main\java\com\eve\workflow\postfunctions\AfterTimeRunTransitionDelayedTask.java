package com.eve.workflow.postfunctions;

import com.atlassian.jira.bc.issue.IssueService;
import com.atlassian.jira.component.ComponentAccessor;
import com.atlassian.jira.issue.IssueInputParameters;
import com.atlassian.jira.issue.MutableIssue;
import com.atlassian.jira.issue.fields.CustomField;
import com.atlassian.jira.user.ApplicationUser;
import com.atlassian.jira.util.thread.JiraThreadLocalUtil;
import com.atlassian.jira.workflow.JiraWorkflow;
import com.eve.services.CustomToolService;
import com.eve.utils.Utils;
import com.opensymphony.workflow.loader.ActionDescriptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Date;
import java.util.Optional;
import java.util.concurrent.Delayed;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @since 2025/1/6
 */
public class AfterTimeRunTransitionDelayedTask implements Delayed, Runnable {
    private static final Logger log = LoggerFactory.getLogger(AfterTimeRunTransitionFunction.class);

    private final String name;
    private final long startTime; // 任务开始执行的时间戳
    private final Long issueId;
    private final String statusId;
    private final String transitionId;
    private final String transitionUserFieldId;
    @Resource
    private CustomToolService customToolService;


    public AfterTimeRunTransitionDelayedTask(String name, long delayMs, Long issueId, String statusId, String transitionId, String transitionUserFieldId) {
        this.name = name;
        this.startTime = System.currentTimeMillis() + delayMs;
        this.issueId = issueId;
        this.statusId = statusId;
        this.transitionId = transitionId;
        this.transitionUserFieldId = transitionUserFieldId;
    }

    @Override
    public long getDelay(TimeUnit unit) {
        // 返回当前时间与任务开始时间之间的差值，转换为指定的时间单位
        long diff = startTime - System.currentTimeMillis();
        return unit.convert(diff, TimeUnit.MILLISECONDS);
    }

    @Override
    public int compareTo(Delayed o) {
        if (this.getDelay(TimeUnit.MILLISECONDS) < o.getDelay(TimeUnit.MILLISECONDS)) {
            return -1;
        }
        if (this.getDelay(TimeUnit.MILLISECONDS) > o.getDelay(TimeUnit.MILLISECONDS)) {
            return 1;
        }
        return 0;
    }

    @Override
    public void run() {
        // 任务执行逻辑
        log.error("name：{}，startTime：{}，issueId：{}，statusId：{}，transitionId：{}",name, startTime, issueId, statusId, transitionId);
        JiraThreadLocalUtil jiraThreadLocalUtil = ComponentAccessor.getComponent(JiraThreadLocalUtil.class);
        jiraThreadLocalUtil.preCall();

        try {
            MutableIssue mutableIssue = ComponentAccessor.getIssueManager().getIssueObject(issueId);
            customToolService.transitionNameByCondition(mutableIssue,"超时跳转");
/*            MutableIssue mutableIssue = ComponentAccessor.getIssueManager().getIssueObject(issueId);
            if (statusId.equals(mutableIssue.getStatusId())) {
                JiraWorkflow workflow = ComponentAccessor.getWorkflowManager().getWorkflow(mutableIssue);
                Collection<ActionDescriptor> allActions = workflow.getAllActions();
                Optional<ActionDescriptor> first = allActions.stream().filter(e -> transitionId.equals(e.getId() + "")).findFirst();
                if (first.isPresent()) {
                    ApplicationUser applicationUser = mutableIssue.getAssignee();
                    if ("assignee".equals(transitionUserFieldId)) {
                        applicationUser = mutableIssue.getAssignee();
                    }else if ("reporter".equals(transitionUserFieldId)){
                        ApplicationUser reporter = mutableIssue.getReporter();
                        if (applicationUser != null) {
                            applicationUser = reporter;
                        }
                    } else {
                        CustomField customField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(transitionUserFieldId);
                        if (customField != null) {
                            Object customFieldValue = mutableIssue.getCustomFieldValue(customField);
                            if (customFieldValue != null) {
                                applicationUser = (ApplicationUser)customFieldValue;
                            }
                        }
                    }
                    ActionDescriptor actionDescriptor = first.get();
                    IssueService issueService = ComponentAccessor.getIssueService();
                    IssueInputParameters issueInputParameters = issueService.newIssueInputParameters();
                    IssueService.TransitionValidationResult transitionValidationResult = issueService.validateTransition(applicationUser, mutableIssue.getId(), actionDescriptor.getId(), issueInputParameters);
                    if (transitionValidationResult.isValid()) {
                        IssueService.IssueResult transition = issueService.transition(applicationUser, transitionValidationResult);
                        if (!transition.isValid()) {
                            log.error("延时执行失败，{}指定的转换({})执行结果：{}",mutableIssue.getKey(),transitionId,transition.getErrorCollection());
                        }
                        //成功，添加评论，超时自动执行

                    }else {
                        log.error("延时执行失败，{}指定的转换({})不符合执行条件：{}",mutableIssue.getKey(),transitionId,transitionValidationResult.getErrorCollection());
                    }
                }else {
                    log.error("延时执行失败，{}指定的转换({})不存在",mutableIssue.getKey(),transitionId);
                }
            }*/
        } catch (Exception e) {
            log.error("延时执行失败，异常：{}", Utils.errInfo(e));
        } finally {
            jiraThreadLocalUtil.postCall((org.apache.log4j.Logger) log);
        }
    }
}
