package com.eve.workflow.postfunctions;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.atlassian.jira.component.ComponentAccessor;
import com.atlassian.jira.issue.MutableIssue;
import com.atlassian.jira.user.ApplicationUser;
import com.atlassian.jira.workflow.JiraWorkflow;
import com.eve.utils.JiraCustomTool;
import com.eve.utils.Utils;
import com.opensymphony.module.propertyset.PropertySet;
import com.opensymphony.workflow.WorkflowException;
import com.opensymphony.workflow.loader.ActionDescriptor;
import com.opensymphony.workflow.loader.RestrictionDescriptor;
import com.opensymphony.workflow.loader.ResultDescriptor;
import com.opensymphony.workflow.loader.StepDescriptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.concurrent.*;

/**
 * <AUTHOR>
 * @date 2022/9/2
 */
public class AfterTimeRunTransitionFunction extends JsuWorkflowFunction {
    private static final Logger log = LoggerFactory.getLogger(AfterTimeRunTransitionFunction.class);
    private JiraCustomTool jiraCustomTool;

    public AfterTimeRunTransitionFunction(JiraCustomTool jiraCustomTool) {
        this.jiraCustomTool = jiraCustomTool;
    }

    @Override
    public void doIt(Map transientVars, Map args, PropertySet ps) throws WorkflowException {
        MutableIssue mutableIssue = super.getIssue(transientVars);

        try {
            Integer actionId = (Integer) transientVars.get("actionId");//当前转换id


            String fieldSignJson = String.valueOf(args.get("parmJson"));
            JSONObject jsonObject = JSON.parseObject(fieldSignJson);
            String jqlConditionEnabled = String.valueOf(jsonObject.get("jqlConditionEnabled"));
            String jqlCondition = String.valueOf(jsonObject.get("jqlCondition"));

            //需要通过jql校验才执行
            ApplicationUser currentUser = ComponentAccessor.getJiraAuthenticationContext().getLoggedInUser();
            if ("true".equals(jqlConditionEnabled) && !jiraCustomTool.matchJql(mutableIssue, jqlCondition, currentUser)) {
                return ;//jql条件激活且不满足jql条件，不执行该功能
            }

            String delayTime = String.valueOf(jsonObject.get("delayTime"));//超时时间
            String timeUnit = String.valueOf(jsonObject.get("timeUnit"));//超时单位
            String transitionId = String.valueOf(jsonObject.get("transitionId"));//转换ID
            String transitionUserFieldId = String.valueOf(jsonObject.get("transitionUserField"));//转换ID
            String statusId = mutableIssue.getStatusId();
            Long issueId = mutableIssue.getId();

            JiraWorkflow workflow = ComponentAccessor.getWorkflowManager().getWorkflow(mutableIssue);
            Collection<ActionDescriptor> allActions = workflow.getAllActions();
            ActionDescriptor actionDescriptor = allActions.stream().filter(e -> e.getId() == actionId).findFirst().orElse(null);
            // 获取无条件结果（最常见的情况）
            RestrictionDescriptor restriction = actionDescriptor.getRestriction();
            ResultDescriptor result = actionDescriptor.getUnconditionalResult();

            // 获取目标步骤
            result.getStep();
            // 创建一个 DelayQueue 实例
            DelayQueue<AfterTimeRunTransitionDelayedTask> queue = new DelayQueue<>();

            // 添加一个延时任务到队列中
            long delayTimeInMilliseconds = 30 * 60 * 1000L; // 30分钟 * 60秒/分钟 * 1000毫秒/秒
            if ("m".equals(timeUnit)) {
                delayTimeInMilliseconds = Long.parseLong(delayTime) * 60 * 1000L; // 分钟 * 60秒/分钟 * 1000毫秒/秒
            } else if ("h".equals(timeUnit)) {
                delayTimeInMilliseconds = Long.parseLong(delayTime) * 60 * 60 * 1000L; // 小时 * 60分钟/小时 * 60秒/分钟 * 1000毫秒/秒
            } else if ("d".equals(timeUnit)) {
                delayTimeInMilliseconds = Long.parseLong(delayTime) * 24 * 60 * 60 * 1000L; // 天 * 24小时/天 * 60分钟/小时 * 60秒/分钟 * 1000毫秒/秒
            }
            queue.put(new AfterTimeRunTransitionDelayedTask("延时执行转换任务", delayTimeInMilliseconds, issueId, statusId, transitionId, transitionUserFieldId));

            // 创建线程池来并发处理任务
//            ExecutorService executorService = Executors.newCachedThreadPool();


            // 启动一个单独的工作线程来持续监控 DelayQueue 并分发任务给线程池
            new Thread(() -> {
                ExecutorService executorService = new ThreadPoolExecutor(1, 2,60L, TimeUnit.MILLISECONDS,new LinkedBlockingQueue<Runnable>(10));
                try {
                    while (!Thread.currentThread().isInterrupted()) {
                        // take() 阻塞直到有到期的任务可以取出
                        AfterTimeRunTransitionDelayedTask task = queue.take();
                        // 提交任务给线程池执行，这样不会阻塞工作线程
                        executorService.submit(task);

                        // 如果队列为空，则结束工作线程
                        if (queue.isEmpty()) {
                            Thread.sleep(500);
                            Thread.currentThread().interrupt();
                            break;
                        }
                    }
                } catch (InterruptedException e) {
                    log.error("延时任务完成异常");
                } finally {
                    // 关闭线程池
                    executorService.shutdown();
                    log.error("延时任务已完成");
                    try {
                        if (!executorService.awaitTermination(5, TimeUnit.SECONDS)) {
                            executorService.shutdownNow();
                        }
                    } catch (InterruptedException ie) {
                        executorService.shutdownNow();
                    }
                }
            }).start();

            // 输出提示信息
            log.error("延时任务已生成");

            // 让主线程等待一段时间以便观察任务执行情况（此处仅用于演示，实际应用中可能不需要）
            Thread.sleep(500); // 简单休眠以确保工作线程开始运行


        } catch (Exception e) {
            log.error("延时任务生成异常" + Utils.errInfo(e));
            throw new WorkflowException(e);
        }
    }
}
