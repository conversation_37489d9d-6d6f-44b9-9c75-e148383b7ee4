package com.eve.workflow.postfunctions;

import com.alibaba.fastjson.JSONObject;
import com.atlassian.jira.component.ComponentAccessor;
import com.atlassian.jira.plugin.workflow.AbstractWorkflowPluginFactory;
import com.atlassian.jira.plugin.workflow.WorkflowPluginFunctionFactory;
import com.eve.beans.CopyFieldBean;
import com.eve.utils.Constant;
import com.opensymphony.workflow.loader.AbstractDescriptor;
import com.opensymphony.workflow.loader.FunctionDescriptor;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/9/2
 */
public class AfterTimeRunTransitionFunctionFactory extends AbstractWorkflowPluginFactory implements WorkflowPluginFunctionFactory {
    @Override
    protected void getVelocityParamsForInput(Map<String, Object> map) {
        List<CopyFieldBean> copyFieldBeanList = new ArrayList<>(Constant.assigneeAndReporter);
        //用户单选字段
        ComponentAccessor.getCustomFieldManager().getCustomFieldObjects().stream()
                .filter(customField -> Constant.userPickerFieldType.equals(customField.getCustomFieldType().getKey()))
                .map(customField -> new CopyFieldBean(customField.getId(), customField.getFieldName()))
                .forEach(copyFieldBeanList::add);

        map.put("customFieldList", copyFieldBeanList);

    }

    @Override
    protected void getVelocityParamsForEdit(Map<String, Object> map, AbstractDescriptor abstractDescriptor) {
        if (!(abstractDescriptor instanceof FunctionDescriptor)) {
            throw new IllegalArgumentException("Descriptor must be a FunctionDescriptor.");
        }
        FunctionDescriptor functionDescriptor = (FunctionDescriptor) abstractDescriptor;
        List<CopyFieldBean> copyFieldBeanList = new ArrayList<>(Constant.assigneeAndReporter);
        //用户单选字段
        ComponentAccessor.getCustomFieldManager().getCustomFieldObjects().stream()
                .filter(customField -> Constant.userPickerFieldType.equals(customField.getCustomFieldType().getKey()))
                .map(customField -> new CopyFieldBean(customField.getId(), customField.getFieldName()))
                .forEach(copyFieldBeanList::add);

        map.put("customFieldList", copyFieldBeanList);

        JSONObject jsonObject = JSONObject.parseObject((String) functionDescriptor.getArgs().get("parmJson"));
        String delayTime = String.valueOf(jsonObject.get("delayTime"));
        String timeUnit = String.valueOf(jsonObject.get("timeUnit"));
        String transitionId = String.valueOf(jsonObject.get("transitionId"));
        String transitionUserField = String.valueOf(jsonObject.get("transitionUserField"));
        String jqlConditionEnabled = String.valueOf(jsonObject.get("jqlConditionEnabled"));
        String jqlCondition = String.valueOf(jsonObject.get("jqlCondition"));
        map.put("delayTime", delayTime);
        map.put("timeUnit", timeUnit);
        map.put("transitionId", transitionId);
        map.put("transitionUserField", transitionUserField);
        map.put("jqlConditionEnabled", jqlConditionEnabled);
        map.put("jqlCondition", jqlCondition);
    }

    @Override
    protected void getVelocityParamsForView(Map<String, Object> map, AbstractDescriptor abstractDescriptor) {
        if (!(abstractDescriptor instanceof FunctionDescriptor)) {
            throw new IllegalArgumentException("Descriptor must be a FunctionDescriptor.");
        }
        FunctionDescriptor functionDescriptor = (FunctionDescriptor) abstractDescriptor;
        JSONObject jsonObject = JSONObject.parseObject((String) functionDescriptor.getArgs().get("parmJson"));
        String delayTime = String.valueOf(jsonObject.get("delayTime"));
        String timeUnit = String.valueOf(jsonObject.get("timeUnit"));
        String transitionId = String.valueOf(jsonObject.get("transitionId"));
        String transitionUserField = String.valueOf(jsonObject.get("transitionUserField"));
        String jqlConditionEnabled = String.valueOf(jsonObject.get("jqlConditionEnabled"));
        String jqlCondition = String.valueOf(jsonObject.get("jqlCondition"));
        if (delayTime != null) {
            map.put("delayTime", delayTime);
        }
        if (timeUnit != null) {
            map.put("timeUnit", timeUnit);
        }
        if (timeUnit != null) {
            map.put("transitionId", transitionId);
        }
        map.put("transitionUserField", transitionUserField);
        map.put("jqlConditionEnabled", jqlConditionEnabled);
        map.put("jqlCondition", jqlCondition);
    }

    @Override
    public Map<String, ?> getDescriptorParams(Map<String, Object> map) {
        Map<String, Object> hashMap = new HashMap<>();
        try {
            JSONObject jsonObject = new JSONObject();
            String[] delayTime = (String[]) map.get("delayTime");
            jsonObject.put("delayTime", delayTime[0]);
            String[] timeUnit = (String[]) map.get("timeUnit");
            jsonObject.put("timeUnit", timeUnit[0]);
            String[] transitionId = (String[]) map.get("transitionId");
            jsonObject.put("transitionId", transitionId[0]);
            String[] transitionUserField = (String[]) map.get("transitionUserField");
            jsonObject.put("transitionUserField", transitionUserField[0]);
            String[] jqlConditionEnabled = (String[]) map.get("jqlConditionEnabled");
            jsonObject.put("jqlConditionEnabled", jqlConditionEnabled[0]);
            String[] jqlCondition = (String[]) map.get("jqlCondition");
            jsonObject.put("jqlCondition", jqlCondition[0]);
            hashMap.put("parmJson", jsonObject.toJSONString());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return hashMap;
    }
}
