package com.eve.workflow.postfunctions;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.atlassian.jira.component.ComponentAccessor;
import com.atlassian.jira.config.properties.APKeys;
import com.atlassian.jira.issue.MutableIssue;
import com.atlassian.jira.issue.attachment.Attachment;
import com.atlassian.jira.issue.customfields.option.Option;
import com.atlassian.jira.issue.fields.CustomField;
import com.atlassian.jira.security.xsrf.XsrfTokenGenerator;
import com.atlassian.jira.user.ApplicationUser;
import com.eve.beans.SmartAttachmentCateBean;
import com.eve.utils.Constant;
import com.eve.utils.HttpUtils;
import com.eve.utils.Utils;
import com.opensymphony.module.propertyset.PropertySet;
import com.opensymphony.workflow.WorkflowException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.net.URLEncoder;
import java.sql.Timestamp;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2022/9/2
 */
public class ArchiveAttachmentFunction extends JsuWorkflowFunction {
    private static final Logger log = LoggerFactory.getLogger(ArchiveAttachmentFunction.class);

    @Autowired
    private XsrfTokenGenerator xsrfTokenGenerator;

    @Override
    public void doIt(Map transientVars, Map args, PropertySet ps) throws WorkflowException {
        MutableIssue mutableIssue = super.getIssue(transientVars);
        ApplicationUser reporter = mutableIssue.getReporter();
        ApplicationUser currentUser = ComponentAccessor.getJiraAuthenticationContext().getLoggedInUser();

        try {

            String fieldSignJson = String.valueOf(args.get("parmJson"));
            JSONObject funtionJsonObject = JSON.parseObject(fieldSignJson);

            String archiveCate = String.valueOf(funtionJsonObject.get("archiveCate"));//归档类别
            String fileCate = String.valueOf(funtionJsonObject.get("fileCate"));//文件类别名称
            if (StringUtils.isEmpty(fileCate)) {
                return;
            }
            String[] split = fileCate.split(",");
            // 获取类别列表信息
            String baseUrl = ComponentAccessor.getApplicationProperties().getString(APKeys.JIRA_BASEURL);
            Map<String, String> headers = new HashMap<>();
//            headers.put("atlas-authorization", ComponentAccessor.getComponent(JiraAuthenticationContext.class).getLoggedInUser().getUsername());
            headers.put("atlas-authorization", currentUser.getUsername());
            String callback = HttpUtils.doGet(
                    baseUrl + "/rest/attach-cat/1.0/attachments?issueKey=" + mutableIssue.getKey(),
                    headers
            );
            log.error("获取附件分类响应：" + callback);
            JSONObject jsonObject = JSON.parseObject(callback);
            Collection<Attachment> attachments = mutableIssue.getAttachments();

            List<SmartAttachmentCateBean> smartAttachmentCateBeanList = JSONObject.parseArray(jsonObject.getString("categories"), SmartAttachmentCateBean.class);
            List<JSONObject> resultJson = new ArrayList<>();
            for (String cate : split) {
                Optional<SmartAttachmentCateBean> cateFirst = smartAttachmentCateBeanList.stream().filter(e -> e.getName().equals(cate)).findFirst();
                if (cateFirst.isPresent()) {
                    //存在该文件分类，获取文件
                    SmartAttachmentCateBean smartAttachmentCateBean = cateFirst.get();
                    List<Long> attachmentIdList = smartAttachmentCateBean.getAttachmentIds();

                    List<SmartAttachmentCateBean> documents = smartAttachmentCateBean.getDocuments();
                    if (!ObjectUtils.isEmpty(documents)) {//有成组的同名文件，取最新的文件(文件id最大)
                        documents.stream().map(SmartAttachmentCateBean::getAttachmentIds)
                                .filter(attachmentIds -> !ObjectUtils.isEmpty(attachmentIds))
                                .map(attachmentIds -> attachmentIds.stream().max(Comparator.comparing(Long::longValue)).orElse(0L))
                                .forEach(attachmentIdList::add);
                    }
                    if (!attachmentIdList.isEmpty()) {
                        for (Long aLong : attachmentIdList) {
                            Attachment attachment = attachments.stream().filter(e -> e.getId().equals(aLong)).findFirst().get();
//                            String[] split1 = attachment.getFilename().split(".");
                            JSONObject attachmentJson = new JSONObject();
//                            attachmentJson.put("cate", cate);//文件类别名称
                            attachmentJson.put("fileName", attachment.getFilename());
//                            attachmentJson.put("fileType", split1[split1.length - 1]);
                            attachmentJson.put("fileId", attachment.getId());
                            attachmentJson.put("issueId", mutableIssue.getId());
                            attachmentJson.put("issueKey", mutableIssue.getKey());

//                            String attachmentUrl = baseUrl + "/secure/attachment/" + attachment.getId() + "/" + URLEncoder.encode(attachment.getFilename(),"UTF-8");
                            resultJson.add(attachmentJson);

                        }




                    }
                }
            }
            CustomField affiliatedPlatformCustomField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(Constant.platformCateCustomFieldId);//平台分类
            CustomField projectNameCustomField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(Constant.projectNameCustomFieldId);//项目名称

            String projectName = projectNameCustomField == null ? null : (String) mutableIssue.getCustomFieldValue(projectNameCustomField);

            Map<String, Option> affiliatedPlatform = affiliatedPlatformCustomField == null ? new HashMap<>() : (Map<String, Option>) mutableIssue.getCustomFieldValue(affiliatedPlatformCustomField);
//            if (ObjectUtils.isEmpty(affiliatedPlatform)) {
//                affiliatedPlatform = new HashMap<>();
//            }
            Option affiliatedPlatParent = affiliatedPlatform.get(null);
            Option affiliatedPlatChildren = affiliatedPlatform.get("1");

            JSONObject paramJson = new JSONObject();
            paramJson.put("type", archiveCate + "Achievement");//topicAchievement、gainAchievement
            paramJson.put("issueId", mutableIssue.getId());
            paramJson.put("issueKey", mutableIssue.getKey());
            paramJson.put("affiliatedPlatformParentName", affiliatedPlatParent == null ? "" : affiliatedPlatParent.getValue());
            paramJson.put("affiliatedPlatformChildrenName", affiliatedPlatChildren == null ? "" : affiliatedPlatChildren.getValue());
            paramJson.put("projectName", projectName == null ? "" : projectName);
            paramJson.put("userName", currentUser.getUsername());
//            paramJson.put("orderNumber", affiliatedPlatParent == null ? "" : affiliatedPlatParent.getSequence());
            paramJson.put("fileList", resultJson);

            String data = JSON.toJSONString(paramJson);

            HashMap<String, String> header = new HashMap<>();
            header.put("Content-Type", MediaType.APPLICATION_JSON_VALUE);
            String backLog = "";
            log.error("文件库归档推送PBI数据>>{}", data);
            if (Constant.projectManageTestProjectIdList.contains(mutableIssue.getProjectId())) {
                backLog = HttpUtils.doPostBackLog(Constant.projectManageArchiveAttachmentUrlTest, data, header);
            }else {
                backLog = HttpUtils.doPostBackLog(Constant.projectManageArchiveAttachmentUrl, data, header);
            }
            JSONObject callBackJson = JSON.parseObject(backLog);
            Boolean success = callBackJson.getBoolean("success");
            Boolean respData = callBackJson.getBoolean("data");
            if (!(Boolean.TRUE.equals(success)) || !(Boolean.TRUE.equals(respData))) {
                //失败，失败原因添加到评论
                String message = callBackJson.getString("message");
                String exceptionClazz = callBackJson.getString("exceptionClazz");
                String comment = "[~083779] \n" + message + "\n" + exceptionClazz;
                ApplicationUser userByName = ComponentAccessor.getUserManager().getUserByName("083779");
                ComponentAccessor.getCommentManager().create(mutableIssue, userByName, comment, true);
            }

            log.error("文件库归档推送PBI结果>>{}", backLog);


        } catch (Exception e) {
            log.error(Utils.errInfo(e));
            throw new WorkflowException(e);
        }
    }
}
