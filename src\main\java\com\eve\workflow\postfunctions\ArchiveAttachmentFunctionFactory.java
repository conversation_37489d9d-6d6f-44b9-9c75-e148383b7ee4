package com.eve.workflow.postfunctions;

import com.alibaba.fastjson.JSONObject;
import com.atlassian.jira.issue.status.Status;
import com.atlassian.jira.plugin.workflow.AbstractWorkflowPluginFactory;
import com.atlassian.jira.plugin.workflow.WorkflowPluginFunctionFactory;
import com.eve.utils.Constant;
import com.opensymphony.workflow.loader.AbstractDescriptor;
import com.opensymphony.workflow.loader.FunctionDescriptor;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2022/9/2
 */
public class ArchiveAttachmentFunctionFactory extends AbstractWorkflowPluginFactory implements WorkflowPluginFunctionFactory {
    @Override
    protected void getVelocityParamsForInput(Map<String, Object> map) {
//        Collection<Status> allIssueStatusList = new ArrayList<>();
//        for (Status status : allIssueStatusList) {
//            String id = status.getId();
//        }
//        map.put("copyTypeMap", Constant.copyTypeMap);
//        map.put("allIssueStatusList", allIssueStatusList);
    }

    @Override
    protected void getVelocityParamsForEdit(Map<String, Object> map, AbstractDescriptor abstractDescriptor) {
        if (!(abstractDescriptor instanceof FunctionDescriptor)) {
            throw new IllegalArgumentException("Descriptor must be a FunctionDescriptor.");
        }

        FunctionDescriptor functionDescriptor = (FunctionDescriptor) abstractDescriptor;
        JSONObject jsonObject = JSONObject.parseObject((String) functionDescriptor.getArgs().get("parmJson"));
        String archiveCate = String.valueOf(jsonObject.get("archiveCate"));
        String fileCate = String.valueOf(jsonObject.get("fileCate"));
        map.put("archiveCate", archiveCate);
        map.put("fileCate", fileCate);
    }

    @Override
    protected void getVelocityParamsForView(Map<String, Object> map, AbstractDescriptor abstractDescriptor) {
        if (!(abstractDescriptor instanceof FunctionDescriptor)) {
            throw new IllegalArgumentException("Descriptor must be a FunctionDescriptor.");
        }
        FunctionDescriptor functionDescriptor = (FunctionDescriptor) abstractDescriptor;
        JSONObject jsonObject = JSONObject.parseObject((String) functionDescriptor.getArgs().get("parmJson"));
        String archiveCate = String.valueOf(jsonObject.get("archiveCate"));
        String fileCate = String.valueOf(jsonObject.get("fileCate"));
        if (archiveCate != null) {
            map.put("archiveCate", archiveCate);
        }
        if (fileCate != null) {
            map.put("fileCate", fileCate);
        }
    }

    @Override
    public Map<String, ?> getDescriptorParams(Map<String, Object> map) {
        Map<String, Object> hashMap = new HashMap<>();
        try {
            String[] archiveCate = (String[]) map.get("archiveCate");
            String[] fileCate = (String[]) map.get("fileCate");
            JSONObject resp = new JSONObject();
            resp.put("archiveCate", archiveCate[0]);
            resp.put("fileCate", fileCate[0]);
            hashMap.put("parmJson", resp.toJSONString());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return hashMap;
    }
}
