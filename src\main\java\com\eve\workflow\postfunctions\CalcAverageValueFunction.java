package com.eve.workflow.postfunctions;

import com.alibaba.fastjson.JSONObject;
import com.atlassian.jira.bc.issue.IssueService;
import com.atlassian.jira.component.ComponentAccessor;
import com.atlassian.jira.issue.MutableIssue;
import com.atlassian.jira.issue.fields.CustomField;
import com.atlassian.jira.user.ApplicationUser;
import com.eve.utils.JiraCustomTool;
import com.eve.utils.Utils;
import com.opensymphony.module.propertyset.PropertySet;
import com.opensymphony.workflow.WorkflowException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/5/9
 */
public class CalcAverageValueFunction extends JsuWorkflowFunction {
    private static final Logger log = LoggerFactory.getLogger(CalcAverageValueFunction.class);
    private JiraCustomTool jiraCustomTool;

    public CalcAverageValueFunction(JiraCustomTool jiraCustomTool) {
        this.jiraCustomTool = jiraCustomTool;
    }

    @Override
    public void doIt(Map transientVars, Map args, PropertySet ps) throws WorkflowException {
        try {
            MutableIssue mutableIssue = super.getIssue(transientVars);
            ApplicationUser currentUser = ComponentAccessor.getJiraAuthenticationContext().getLoggedInUser();
            IssueService issueService = ComponentAccessor.getIssueService();
            IssueService.TransitionValidationResult transitionValidationResult = issueService.validateTransition(mutableIssue.getAssignee(), mutableIssue.getId(), 6, issueService.newIssueInputParameters());
            if (transitionValidationResult.isValid()) {
                issueService.transition(currentUser, transitionValidationResult);
            }


            String fieldSignJson = String.valueOf(args.get("copyFieldJson"));
            JSONObject jsonObject = JSONObject.parseObject(fieldSignJson);

            List<String> sourceFields = JSONObject.parseArray(String.valueOf(jsonObject.get("sourceFields")), String.class);
            String targetField = String.valueOf(jsonObject.get("targetField"));
            String jqlConditionEnabled = String.valueOf(jsonObject.get("jqlConditionEnabled"));
            String jqlCondition = String.valueOf(jsonObject.get("jqlCondition"));

            //需要通过jql校验才执行
            if ("true".equals(jqlConditionEnabled) && !jiraCustomTool.matchJql(mutableIssue, jqlCondition, currentUser)) {
                return;
            }

            BigDecimal valueResult = new BigDecimal(0);
            BigDecimal valueNum = new BigDecimal(0);
            for (String sourceField : sourceFields) {
                CustomField sourceCustomField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(sourceField);
                Double sourceValue = (Double) mutableIssue.getCustomFieldValue(sourceCustomField);
                if (sourceValue != null) {
                    valueResult = valueResult.add(new BigDecimal(sourceValue));
                    valueNum = valueNum.add(new BigDecimal(1));
                }
            }
            BigDecimal divide = valueNum.intValue() == 0 ? new BigDecimal(0) : valueResult.divide(valueNum,2, RoundingMode.HALF_UP);
            CustomField targetCustomField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(targetField);
            mutableIssue.setCustomFieldValue(targetCustomField, divide.doubleValue());

        } catch (Exception e) {
            log.error(Utils.errInfo(e));
            throw new WorkflowException(e);
        }

    }

}
