package com.eve.workflow.postfunctions;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.atlassian.jira.component.ComponentAccessor;
import com.atlassian.jira.issue.fields.CustomField;
import com.atlassian.jira.plugin.workflow.AbstractWorkflowPluginFactory;
import com.atlassian.jira.plugin.workflow.WorkflowPluginFunctionFactory;
import com.eve.beans.CopyFieldBean;
import com.eve.services.CopyFieldService;
import com.eve.utils.Constant;
import com.opensymphony.workflow.loader.AbstractDescriptor;
import com.opensymphony.workflow.loader.FunctionDescriptor;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/5/9
 */
public class CalcAverageValueFunctionFactory extends AbstractWorkflowPluginFactory implements WorkflowPluginFunctionFactory {
    @Autowired
    CopyFieldService copyFieldService;
    @Override
    protected void getVelocityParamsForInput(Map<String, Object> map) {
        List<CustomField> customFieldList = ComponentAccessor.getCustomFieldManager().getCustomFieldObjects();//获取全部字段
        List<CopyFieldBean> copyFieldBeanList = new ArrayList<>();
        for (CustomField customField:customFieldList){
            if (Constant.numFieldType.equals(customField.getCustomFieldType().getKey())){
                CopyFieldBean copyFieldBean = new CopyFieldBean();
                copyFieldBean.setId(customField.getId());
                copyFieldBean.setName(customField.getFieldName());
                copyFieldBeanList.add(copyFieldBean);//数值字段
            }
        }

        map.put("customFieldList", copyFieldBeanList);
    }

    @Override
    protected void getVelocityParamsForEdit(Map<String, Object> map, AbstractDescriptor abstractDescriptor) {
        if (!(abstractDescriptor instanceof FunctionDescriptor)) {
            throw new IllegalArgumentException("Descriptor must be a FunctionDescriptor.");
        }
        FunctionDescriptor conditionDescriptor = (FunctionDescriptor) abstractDescriptor;
        JSONObject jsonObject = JSON.parseObject((String) conditionDescriptor.getArgs().get("copyFieldJson"));

        List<String> sourceFields = JSON.parseArray(String.valueOf(jsonObject.get("sourceFields")), String.class);
        String targetField = String.valueOf(jsonObject.get("targetField"));
        String jqlConditionEnabled = String.valueOf(jsonObject.get("jqlConditionEnabled"));
        String jqlCondition = String.valueOf(jsonObject.get("jqlCondition"));

        List<CustomField> customFieldList = ComponentAccessor.getCustomFieldManager().getCustomFieldObjects();//获取全部字段
        List<CopyFieldBean> copyFieldBeanList = new ArrayList<>();
        for (CustomField customField:customFieldList){
            if (Constant.numFieldType.equals(customField.getCustomFieldType().getKey())){
                CopyFieldBean copyFieldBean = new CopyFieldBean();
                copyFieldBean.setId(customField.getId());
                copyFieldBean.setName(customField.getFieldName());
                copyFieldBeanList.add(copyFieldBean);//数值字段
            }
        }

        map.put("customFieldList", copyFieldBeanList);

        map.put("sourceFields", sourceFields);
        map.put("targetField", targetField);
        map.put("jqlConditionEnabled", jqlConditionEnabled);
        map.put("jqlCondition", jqlCondition);
    }

    @Override
    protected void getVelocityParamsForView(Map<String, Object> map, AbstractDescriptor abstractDescriptor) {
        if (!(abstractDescriptor instanceof FunctionDescriptor)) {
            throw new IllegalArgumentException("Descriptor must be a FunctionDescriptor.");
        }
        try {
            FunctionDescriptor conditionDescriptor = (FunctionDescriptor) abstractDescriptor;
            JSONObject jsonObject = JSONObject.parseObject((String) conditionDescriptor.getArgs().get("copyFieldJson"));


            List<String> sourceFields = JSONObject.parseArray(String.valueOf(jsonObject.get("sourceFields")), String.class);
            String targetField = String.valueOf(jsonObject.get("targetField"));
            String jqlConditionEnabled = String.valueOf(jsonObject.get("jqlConditionEnabled"));
            String jqlCondition = String.valueOf(jsonObject.get("jqlCondition"));

            List<String> targetFieldList = new ArrayList<>();
            for (String sourceField : sourceFields) {
                targetFieldList.add(ComponentAccessor.getCustomFieldManager().getCustomFieldObject(sourceField).getFieldName());
            }
            map.put("sourceFields", targetFieldList);
            map.put("targetField", ComponentAccessor.getCustomFieldManager().getCustomFieldObject(targetField).getFieldName());
            map.put("jqlConditionEnabled", jqlConditionEnabled);
            map.put("jqlCondition", jqlCondition);
            map.put("copyFieldJson", jsonObject.toJSONString());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public Map<String, ?> getDescriptorParams(Map<String, Object> map) {
        Map<String,Object> hashMap = new HashMap<>();
        try {
            String[] sourceFields = (String[]) map.get("sourceFields");
            String[] targetField = (String[]) map.get("targetField");
            String[] jqlConditionEnabled = (String[]) map.get("jqlConditionEnabled");
            String[] jqlCondition = (String[]) map.get("jqlCondition");
            JSONObject resp = new JSONObject();
            resp.put("sourceFields", sourceFields);
            resp.put("targetField", targetField[0]);
            resp.put("jqlConditionEnabled", jqlConditionEnabled[0]);
            resp.put("jqlCondition", jqlCondition[0]);
            hashMap.put("copyFieldJson", resp.toJSONString());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return hashMap;
    }
}
