package com.eve.workflow.postfunctions;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.atlassian.jira.bc.issue.IssueService;
import com.atlassian.jira.component.ComponentAccessor;
import com.atlassian.jira.issue.MutableIssue;
import com.atlassian.jira.issue.customfields.option.Option;
import com.atlassian.jira.issue.fields.CustomField;
import com.atlassian.jira.user.ApplicationUser;
import com.eve.utils.JiraCustomTool;
import com.eve.utils.Utils;
import com.opensymphony.module.propertyset.PropertySet;
import com.opensymphony.workflow.WorkflowException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.ObjectUtils;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/10/31
 */
public class ChangeIssueDescriptionFunction  extends JsuWorkflowFunction {
    private static final Logger log = LoggerFactory.getLogger(ChangeIssueDescriptionFunction.class);

    private JiraCustomTool jiraCustomTool;
    private IssueService issueService;

    public ChangeIssueDescriptionFunction(JiraCustomTool jiraCustomTool, IssueService issueService) {
        this.jiraCustomTool = jiraCustomTool;
        this.issueService = issueService;
    }

    @Override
    public void doIt(Map transientVars, Map args, PropertySet ps) throws WorkflowException {
        try {
            MutableIssue mutableIssue = super.getIssue(transientVars);
//            ApplicationUser currentUser = ComponentAccessor.getJiraAuthenticationContext().getLoggedInUser();
            String fieldSignJson = String.valueOf(args.get("parmJson"));
            JSONObject jsonObject = JSON.parseObject(fieldSignJson);
//            JSONObject jsonObject = JSON.parseObject((String) args.get("parmJson"));


            String fieldId = String.valueOf(jsonObject.get("fieldId"));
            List<String> optionIdList = JSON.parseArray(String.valueOf(jsonObject.get("optionIdList")), String.class);

            String jqlConditionEnabled = String.valueOf(jsonObject.get("jqlConditionEnabled"));
            String jqlCondition = String.valueOf(jsonObject.get("jqlCondition"));
            String descriptionText = String.valueOf(jsonObject.get("descriptionText"));

            //需要通过jql校验才执行
            ApplicationUser currentUser = ComponentAccessor.getJiraAuthenticationContext().getLoggedInUser();
            if ("true".equals(jqlConditionEnabled) && !jiraCustomTool.matchJql(mutableIssue, jqlCondition, currentUser)) {
                return ;//jql条件激活且不满足jql条件，不执行该功能
            }

            CustomField customField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(fieldId);
            boolean isUpdateDescription = true;
            if (customField != null && !ObjectUtils.isEmpty(optionIdList)) {
                Object object = mutableIssue.getCustomFieldValue(customField);
                if (object != null) {
                    if (object instanceof Option) {
                        Option option = (Option) object;
                        isUpdateDescription = optionIdList.contains(option.getOptionId() + "");
                    } else if (object instanceof Collection) {
                        Collection<Option> optionList = (Collection<Option>) object;
                        List<String> collect = optionList.stream().map(e -> e.getOptionId() + "").collect(Collectors.toList());
                        isUpdateDescription = optionIdList.stream().anyMatch(collect::contains);
                    }
                }else {
                    isUpdateDescription = false;
                }
            }
            if (isUpdateDescription) {
//                new Thread(()->{
//                    try {
//                        Thread.sleep(2000);
//                        MutableIssue mutableIssue = ComponentAccessor.getIssueManager().getIssueObject(mutableIssue.getId());

//                    IssueInputParameters issueInputParameters = issueService.newIssueInputParameters();
//                    issueInputParameters.setDescription(descriptionText);
//                    IssueService.UpdateValidationResult updateValidationResult = issueService.validateUpdate(currentUser, mutableIssue.getId(), issueInputParameters);
//                    issueService.update(currentUser, updateValidationResult, EventDispatchOption.ISSUE_UPDATED, false);

                        mutableIssue.setDescription(descriptionText);//更新描述

//                        ComponentAccessor.getIssueManager().updateIssue(currentUser, mutableIssue, EventDispatchOption.ISSUE_UPDATED, false);
//                    } catch (InterruptedException e) {
//                        log.error(Utils.errInfo(e));
//                    }
//                }).start();
            }

        } catch (Exception e) {
            log.error(Utils.errInfo(e));
            throw new WorkflowException(e);
        }
    }
}
