package com.eve.workflow.postfunctions;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.atlassian.jira.component.ComponentAccessor;
import com.atlassian.jira.issue.customfields.CustomFieldType;
import com.atlassian.jira.issue.customfields.manager.OptionsManager;
import com.atlassian.jira.issue.customfields.option.Option;
import com.atlassian.jira.issue.fields.CustomField;
import com.atlassian.jira.issue.issuetype.IssueType;
import com.atlassian.jira.plugin.workflow.AbstractWorkflowPluginFactory;
import com.atlassian.jira.plugin.workflow.WorkflowPluginFunctionFactory;
import com.atlassian.jira.user.ApplicationUser;
import com.eve.beans.CopyFieldBean;
import com.eve.beans.OptionsValueBean;
import com.eve.beans.ResultBean;
import com.eve.services.CopyFieldService;
import com.eve.services.ServiceUpdateOption;
import com.eve.utils.Constant;
import com.eve.utils.Utils;
import com.opensymphony.workflow.loader.AbstractDescriptor;
import com.opensymphony.workflow.loader.ConditionDescriptor;
import com.opensymphony.workflow.loader.FunctionDescriptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/10/31
 */
public class ChangeIssueDescriptionFunctionFactory extends AbstractWorkflowPluginFactory implements WorkflowPluginFunctionFactory {

    private static final Logger log = LoggerFactory.getLogger(ChangeIssueDescriptionFunction.class);

    private OptionsManager optionsManager;

    public ChangeIssueDescriptionFunctionFactory(OptionsManager optionsManager){
        this.optionsManager = optionsManager;
    }

    @Override
    protected void getVelocityParamsForInput(Map<String, Object> map) {
        List<String> fieldTypeList = Arrays.asList(
                "com.atlassian.jira.plugin.system.customfieldtypes:cascadingselect",
                "com.atlassian.jira.plugin.system.customfieldtypes:select",
                "com.atlassian.jira.plugin.system.customfieldtypes:multiselect",
                "com.atlassian.jira.plugin.system.customfieldtypes:radiobuttons",
                "com.atlassian.jira.plugin.system.customfieldtypes:multicheckboxes");
        List<CustomField> customFieldList = ComponentAccessor.getCustomFieldManager().getCustomFieldObjects();
        List<CustomField> customFieldSelectList = customFieldList.stream().filter(customField -> fieldTypeList.contains(customField.getCustomFieldType().getKey())).collect(Collectors.toList());
        CustomField customField = customFieldSelectList.get(0);
        ResultBean resultBean = ServiceUpdateOption.queryOptionsList(customField.getIdAsLong());

        List<OptionsValueBean> optionsList = (List<OptionsValueBean>)resultBean.getValue();

        map.put("customFieldList", customFieldSelectList);
        map.put("optionsList", optionsList);
    }

    @Override
    protected void getVelocityParamsForEdit(Map<String, Object> map, AbstractDescriptor abstractDescriptor) {
        if (!(abstractDescriptor instanceof FunctionDescriptor)) {
            throw new IllegalArgumentException("Descriptor must be a FunctionDescriptor.");
        }
        try {
            List<String> fieldTypeList = Arrays.asList(
                    "com.atlassian.jira.plugin.system.customfieldtypes:cascadingselect",
                    "com.atlassian.jira.plugin.system.customfieldtypes:select",
                    "com.atlassian.jira.plugin.system.customfieldtypes:multiselect",
                    "com.atlassian.jira.plugin.system.customfieldtypes:radiobuttons",
                    "com.atlassian.jira.plugin.system.customfieldtypes:multicheckboxes");
            List<CustomField> customFieldList = ComponentAccessor.getCustomFieldManager().getCustomFieldObjects();
            List<CustomField> customFieldSelectList = customFieldList.stream().filter(customField -> fieldTypeList.contains(customField.getCustomFieldType().getKey())).collect(Collectors.toList());


            FunctionDescriptor conditionDescriptor = (FunctionDescriptor) abstractDescriptor;
            JSONObject jsonObject = JSON.parseObject((String) conditionDescriptor.getArgs().get("parmJson"));
            String fieldId = String.valueOf(jsonObject.get("fieldId"));
            List<String> optionIdList = JSON.parseArray(String.valueOf(jsonObject.get("optionIdList")), String.class);


            String jqlConditionEnabled = String.valueOf(jsonObject.get("jqlConditionEnabled"));
            String jqlCondition = String.valueOf(jsonObject.get("jqlCondition"));
            String descriptionText = String.valueOf(jsonObject.get("descriptionText"));

            ResultBean resultBean = ServiceUpdateOption.queryOptionsList(Long.parseLong(fieldId.split("_")[1]));

            List<OptionsValueBean> optionsList = (List<OptionsValueBean>) resultBean.getValue();

            map.put("customFieldList", customFieldSelectList);
            map.put("optionsList", optionsList);


            map.put("fieldId", fieldId);

            map.put("optionIdList", optionIdList);

            map.put("jqlConditionEnabled", jqlConditionEnabled);
            map.put("jqlCondition", jqlCondition);
            map.put("descriptionText", descriptionText);
        } catch (Exception e) {
            log.error(Utils.errInfo(e));
        }

    }

    @Override
    protected void getVelocityParamsForView(Map<String, Object> map, AbstractDescriptor abstractDescriptor) {
        if (!(abstractDescriptor instanceof FunctionDescriptor)) {
            throw new IllegalArgumentException("Descriptor must be a FunctionDescriptor.");
        }
        try {
            FunctionDescriptor conditionDescriptor = (FunctionDescriptor) abstractDescriptor;
            JSONObject jsonObject = JSON.parseObject((String) conditionDescriptor.getArgs().get("parmJson"));
            String fieldId = String.valueOf(jsonObject.get("fieldId"));

            CustomField filetypeCustField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(fieldId);
            String fieldName = filetypeCustField == null ? "字段已删除" + fieldId : filetypeCustField.getFieldName();
            List<String> optionIdList = JSON.parseArray(String.valueOf(jsonObject.get("optionIdList")), String.class);
            String optionValue = optionIdList == null ? "" : optionIdList.stream().map(e -> {
                Option option = optionsManager.findByOptionId(Long.parseLong(e));
                return option == null ? "-" : option.getValue();
            }).filter(anObject -> !"-".equals(anObject)).collect(Collectors.joining(";"));
            String jqlConditionEnabled = String.valueOf(jsonObject.get("jqlConditionEnabled"));
            String jqlCondition = String.valueOf(jsonObject.get("jqlCondition"));
            String descriptionText = String.valueOf(jsonObject.get("descriptionText"));

            map.put("jqlConditionEnabled", jqlConditionEnabled);
            map.put("jqlCondition", jqlCondition);
            map.put("fieldName", fieldName);
//        map.put("optionIdList", optionIdList);
            map.put("optionValue", optionValue);
            map.put("descriptionText", descriptionText);

        } catch (Exception e) {
            log.error(Utils.errInfo(e));
        }
    }

    @Override
    public Map<String, ?> getDescriptorParams(Map<String, Object> map) {
        Map<String,Object> hashMap = new HashMap<>();
        try {
            String[] fieldId = (String[]) map.get("fieldId");
            String[] optionIdList = (String[]) map.get("optionIdList");
            String[] jqlConditionEnabled = (String[]) map.get("jqlConditionEnabled");
            String[] jqlCondition = (String[]) map.get("jqlCondition");
            String[] descriptionText = (String[]) map.get("descriptionText");
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("fieldId", fieldId[0]);
            jsonObject.put("optionIdList", optionIdList);
            jsonObject.put("jqlConditionEnabled", jqlConditionEnabled[0]);
            jsonObject.put("jqlCondition", jqlCondition[0]);
            jsonObject.put("descriptionText", descriptionText[0]);
            log.error("更新描述后处理参数处理{}",jsonObject.toJSONString());
            hashMap.put("parmJson", jsonObject.toJSONString());
        } catch (Exception e) {
            log.error(Utils.errInfo(e));
        }
        return hashMap;
    }
}
