package com.eve.workflow.postfunctions;

import com.alibaba.fastjson.JSONObject;
import com.atlassian.jira.bc.issue.IssueService;
import com.atlassian.jira.bc.issue.search.SearchService;
import com.atlassian.jira.component.ComponentAccessor;
import com.atlassian.jira.event.type.EventDispatchOption;
import com.atlassian.jira.issue.Issue;
import com.atlassian.jira.issue.MutableIssue;
import com.atlassian.jira.issue.fields.CustomField;
import com.atlassian.jira.issue.link.IssueLinkManager;
import com.atlassian.jira.issue.link.LinkCollection;
import com.atlassian.jira.issue.search.SearchResults;
import com.atlassian.jira.user.ApplicationUser;
import com.atlassian.jira.web.bean.PagerFilter;
import com.atlassian.query.Query;
import com.eve.utils.JiraCustomTool;
import com.opensymphony.module.propertyset.PropertySet;
import com.opensymphony.workflow.WorkflowException;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/5/9
 */
public class CopyFieldFunction extends JsuWorkflowFunction {
    private JiraCustomTool jiraCustomTool;

    public CopyFieldFunction(JiraCustomTool jiraCustomTool) {
        this.jiraCustomTool = jiraCustomTool;
    }

    @Override
    public void doIt(Map transientVars, Map args, PropertySet ps) throws WorkflowException {
        try {
            MutableIssue mutableIssue = super.getIssue(transientVars);
            MutableIssue sourceIssue = mutableIssue;
//            List<MutableIssue> targetIssueList = new ArrayList<>();
            ApplicationUser currentUser = ComponentAccessor.getJiraAuthenticationContext().getLoggedInUser();
            IssueLinkManager issueLinkManager = ComponentAccessor.getIssueLinkManager();
            LinkCollection linkCollection = issueLinkManager.getLinkCollection(mutableIssue, currentUser);
            IssueService issueService = ComponentAccessor.getIssueService();
            IssueService.TransitionValidationResult transitionValidationResult = issueService.validateTransition(mutableIssue.getAssignee(), mutableIssue.getId(), 6, issueService.newIssueInputParameters());
            if (transitionValidationResult.isValid()) {
                issueService.transition(currentUser, transitionValidationResult);
            }


            String fieldSignJson = String.valueOf(args.get("copyFieldJson"));
            JSONObject jsonObject = JSONObject.parseObject(fieldSignJson);

            String source_issue = String.valueOf(jsonObject.get("source_issue"));
            List<String> target_issue = JSONObject.parseArray(String.valueOf(jsonObject.get("target_issue")), String.class);
            List<String> issue_type = JSONObject.parseArray(String.valueOf(jsonObject.get("issue_type")), String.class);
            String field_type = String.valueOf(jsonObject.get("field_type"));
            String source_field = String.valueOf(jsonObject.get("source_field"));
            List<String> target_field = JSONObject.parseArray(String.valueOf(jsonObject.get("target_field")), String.class);
            String jqlConditionEnabled = String.valueOf(jsonObject.get("jqlConditionEnabled"));
            String jqlCondition = String.valueOf(jsonObject.get("jqlCondition"));

            //需要通过jql校验才执行
            if ("true".equals(jqlConditionEnabled) && !jiraCustomTool.matchJql(mutableIssue, jqlCondition, currentUser)) {
                return;
            }

            CustomField epicLinkCustomField = ((List<CustomField>) ComponentAccessor.getCustomFieldManager().getCustomFieldObjectsByName("史诗链接")).get(0);
            CustomField epicCustomField = ((List<CustomField>) ComponentAccessor.getCustomFieldManager().getCustomFieldObjectsByName("史诗名称")).get(0);

            switch (source_issue) {
                case "parent_issue":
                    sourceIssue = ComponentAccessor.getIssueManager().getIssueObject(mutableIssue.getParentId());
                    if (sourceIssue == null) {
                        break;
                    }
                    break;
                case "sub_issue":
                    Collection<Issue> subIssueList = mutableIssue.getSubTaskObjects();
                    if (subIssueList == null || subIssueList.isEmpty()) {
                        break;
                    }
                    for (Issue sunIssue : subIssueList) {
                        sourceIssue = ComponentAccessor.getIssueManager().getIssueObject(sunIssue.getKey());
                    }
                    break;
                case "epic_issue":
                    Object epicLink = mutableIssue.getCustomFieldValue(epicLinkCustomField);
                    if (epicLink == null) {
                        break;
                    }
                    sourceIssue = ComponentAccessor.getIssueManager().getIssueObject(String.valueOf(epicLink));
                    break;
                case "epic_link_issue":
                    String epicName = (String) mutableIssue.getCustomFieldValue(epicCustomField);
                    if (epicName == null || epicName.isEmpty()) {
                        break;
                    }
                    String jql = "史诗链接 = " + mutableIssue.getKey();
                    SearchService searchService = (SearchService) ComponentAccessor.getComponentOfType(SearchService.class);
                    SearchService.ParseResult parseResult = searchService.parseQuery(currentUser, jql);
                    Query query = parseResult.getQuery();
                    SearchResults searchResults = searchService.search(currentUser, query, PagerFilter.getUnlimitedFilter());
                    List<Issue> storyIssueList = searchResults.getResults();
                    for (Issue storyIssue : storyIssueList) {
                        sourceIssue = ComponentAccessor.getIssueManager().getIssueObject(storyIssue.getKey());
                    }
                    break;
                case "current_issue":
                default:
                    sourceIssue = mutableIssue;
            }
            for (String targetIssueType : target_issue) {
                List<MutableIssue> targetIssueList = new ArrayList<>();
                Boolean isUpdateIssue = true;
                switch (targetIssueType) {
                    case "parent_issue":
                        MutableIssue parentIssue = ComponentAccessor.getIssueManager().getIssueObject(mutableIssue.getParentId());
                        if (parentIssue == null) {
                            break;
                        }
                        targetIssueList.add(parentIssue);
                        break;
                    case "sub_issue":
                        Collection<Issue> subIssueList = mutableIssue.getSubTaskObjects();
                        if (subIssueList == null || subIssueList.isEmpty()) {
                            break;
                        }
                        for (Issue subIssue : subIssueList) {
                            MutableIssue issue = ComponentAccessor.getIssueManager().getIssueObject(subIssue.getKey());
                            targetIssueList.add(issue);
                        }
                        //过滤测试
//                    List<Issue> issueList = subIssueList.stream().filter(e -> issue_type.contains(e.getIssueTypeId())).sorted(Comparator.comparing(Issue::getUpdated).reversed()).collect(Collectors.toList());
                        break;
                    case "epic_issue":
                        Object epicLink = mutableIssue.getCustomFieldValue(epicLinkCustomField);
                        if (epicLink == null) {
                            break;
                        }
                        MutableIssue epicIssue = ComponentAccessor.getIssueManager().getIssueObject(String.valueOf(epicLink));
                        targetIssueList.add(epicIssue);
                        break;
                    case "epic_link_issue":
                        String epicName = (String) mutableIssue.getCustomFieldValue(epicCustomField);
                        if (epicName == null || epicName.isEmpty()) {
                            break;
                        }
                        String jql = "史诗链接 = " + mutableIssue.getKey();
                        SearchService searchService = (SearchService) ComponentAccessor.getComponentOfType(SearchService.class);
                        SearchService.ParseResult parseResult = searchService.parseQuery(currentUser, jql);
                        Query query = parseResult.getQuery();
                        SearchResults searchResults = searchService.search(currentUser, query, PagerFilter.getUnlimitedFilter());
                        List<Issue> storyIssueList = searchResults.getResults();
                        for (Issue storyIssue : storyIssueList) {
                            MutableIssue epicLinkIssue = ComponentAccessor.getIssueManager().getIssueObject(storyIssue.getKey());
                            targetIssueList.add(epicLinkIssue);
                        }
                        break;
                    case "current_issue":
                    default:
                        isUpdateIssue = false;
                        targetIssueList.add(mutableIssue);
                }
                copyFieldValueToIssue(sourceIssue, targetIssueList, issue_type, source_field, target_field, isUpdateIssue);
            }
            //目标问题为一个，当前问题复制到当前问题

//            if (targetIssueList.size() == 1 && mutableIssue.getKey().equals(targetIssueList.get(0).getKey())) {
//                copyFieldValueToIssue(sourceIssue, targetIssueList, issue_type, source_field, target_field, false);
//            } else {
//                MutableIssue finalSourceIssue = sourceIssue;
//                new Thread(() -> {
//                    try {
//                        copyFieldValueToIssue(finalSourceIssue, targetIssueList, issue_type, source_field, target_field, true);
//                    } catch (Exception e) {
//                        e.printStackTrace();
//                    }
//                }).start();
//            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new WorkflowException("Exception: ", e);
        }

    }

    public void copyFieldValueToIssue(MutableIssue sourceIssue, List<MutableIssue> targetIssueList, List<String> issueTypeList, String sourceField, List<String> targetFieldList, boolean isUpdate) {

        for (MutableIssue targetIssue : targetIssueList) {
            if (issueTypeList.contains("") || issueTypeList.contains(targetIssue.getIssueTypeId())) {
                //判断
                Object sourceValue;
                switch (sourceField) {
                    case "assignee":
                        sourceValue = sourceIssue.getAssignee();
                        break;
                    case "reporter":
                        sourceValue = sourceIssue.getReporter();
                        break;
                    case "dueDate":
                        sourceValue = sourceIssue.getDueDate();
                        break;
                    case "":
                        return;
                    default:
                        CustomField sourceCustomField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(sourceField);
                        sourceValue = sourceIssue.getCustomFieldValue(sourceCustomField);
                }
                if (sourceValue == null) {
                    return;
                }
                for (String targetField : targetFieldList) {
                    switch (targetField) {
                        case "assignee":
                            targetIssue.setAssignee((ApplicationUser) sourceValue);
                            break;
                        case "reporter":
                            targetIssue.setReporter((ApplicationUser) sourceValue);
                            break;
                        case "dueDate":
                            targetIssue.setDueDate((Timestamp) sourceValue);
                            break;
                        case "":
                            return;
                        default:
                            CustomField sourceCustomField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(targetField);
                            targetIssue.setCustomFieldValue(sourceCustomField, sourceValue);
                    }
                }
                if (isUpdate) {
                    ComponentAccessor.getIssueManager().updateIssue(ComponentAccessor.getJiraAuthenticationContext().getLoggedInUser(), targetIssue, EventDispatchOption.ISSUE_UPDATED, false);
                }
            }
        }

    }
}
