package com.eve.workflow.postfunctions;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONArray;
import com.atlassian.jira.bc.issue.search.SearchService;
import com.atlassian.jira.component.ComponentAccessor;
import com.atlassian.jira.issue.Issue;
import com.atlassian.jira.issue.MutableIssue;
import com.atlassian.jira.issue.customfields.CustomFieldType;
import com.atlassian.jira.issue.fields.CustomField;
import com.atlassian.jira.issue.issuetype.IssueType;
import com.atlassian.jira.issue.search.SearchException;
import com.atlassian.jira.issue.search.SearchResults;
import com.atlassian.jira.plugin.workflow.AbstractWorkflowPluginFactory;
import com.atlassian.jira.plugin.workflow.WorkflowPluginFunctionFactory;
import com.atlassian.jira.user.ApplicationUser;
import com.atlassian.jira.web.bean.PagerFilter;
import com.atlassian.query.Query;
import com.eve.beans.CopyFieldBean;
import com.eve.services.CopyFieldService;
import com.eve.utils.Constant;
import com.eve.utils.Utils;
import com.opensymphony.workflow.loader.AbstractDescriptor;
import com.opensymphony.workflow.loader.FunctionDescriptor;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2022/5/9
 */
public class CopyFieldFunctionFactory extends AbstractWorkflowPluginFactory implements WorkflowPluginFunctionFactory {
    @Autowired
    CopyFieldService copyFieldService;
    @Override
    protected void getVelocityParamsForInput(Map<String, Object> map) {
        List<CustomField> customFieldList = ComponentAccessor.getCustomFieldManager().getCustomFieldObjects();//获取全部字段
        List<CustomFieldType<?, ?>> customFieldTypeList = ComponentAccessor.getCustomFieldManager().getCustomFieldTypes();
        List<IssueType> allIssueTypeList = (List<IssueType>) ComponentAccessor.getConstantsManager().getAllIssueTypeObjects();
        List<CopyFieldBean> copyFieldBeanList = new ArrayList<>(Constant.assigneeAndReporter);
        for (CustomField customField:customFieldList){
//            CustomFieldType customFieldType = customField.getCustomFieldType();
            if (Constant.userPickerFieldType.equals(customField.getCustomFieldType().getKey())){
                CopyFieldBean copyFieldBean = new CopyFieldBean();
                copyFieldBean.setId(customField.getId());
                copyFieldBean.setName(customField.getFieldName());
                copyFieldBeanList.add(copyFieldBean);//用户单选字段
            }
        }
        map.put("copyTypeMap", Constant.copyTypeMap);
        map.put("allIssueTypeList", allIssueTypeList);
        map.put("customFieldList", copyFieldBeanList);
        map.put("customFieldTypeList", customFieldTypeList);

        map.put("source_issue", "current_issue");
        map.put("target_issue", "current_issue");
        map.put("issue_type", "");
        map.put("field_type", Constant.userPickerFieldType);
        map.put("source_field", copyFieldBeanList.get(0).getId());
        map.put("target_field", copyFieldBeanList.get(0).getId());
    }

    @Override
    protected void getVelocityParamsForEdit(Map<String, Object> map, AbstractDescriptor abstractDescriptor) {
        if (!(abstractDescriptor instanceof FunctionDescriptor)) {
            throw new IllegalArgumentException("Descriptor must be a ConditionDescriptor.");
        }
        FunctionDescriptor conditionDescriptor = (FunctionDescriptor) abstractDescriptor;
        JSONObject jsonObject = JSON.parseObject((String) conditionDescriptor.getArgs().get("copyFieldJson"));

        String source_issue = String.valueOf(jsonObject.get("source_issue"));
        List<String> target_issue = JSON.parseArray(String.valueOf(jsonObject.get("target_issue")), String.class);
        List<String> issue_type = JSON.parseArray(String.valueOf(jsonObject.get("issue_type")), String.class);
        String field_type = String.valueOf(jsonObject.get("field_type"));
        String source_field = String.valueOf(jsonObject.get("source_field"));
        List<String> target_field = JSON.parseArray(String.valueOf(jsonObject.get("target_field")), String.class);
        String jqlConditionEnabled = String.valueOf(jsonObject.get("jqlConditionEnabled"));
        String jqlCondition = String.valueOf(jsonObject.get("jqlCondition"));

        List<IssueType> allIssueTypeList = (List<IssueType>) ComponentAccessor.getConstantsManager().getAllIssueTypeObjects();
        List<CopyFieldBean> copyFieldBeanList = copyFieldService.queryFieldList(field_type);
        List<CustomFieldType<?, ?>> customFieldTypeList = ComponentAccessor.getCustomFieldManager().getCustomFieldTypes();

        map.put("copyTypeMap", Constant.copyTypeMap);
        map.put("allIssueTypeList", allIssueTypeList);
        map.put("customFieldList", copyFieldBeanList);
        map.put("customFieldTypeList", customFieldTypeList);

        map.put("source_issue", source_issue);
        map.put("target_issue", target_issue);
        map.put("issue_type", issue_type);
        map.put("field_type", field_type);
        map.put("source_field", source_field);
        map.put("target_field", target_field);
        map.put("jqlConditionEnabled", jqlConditionEnabled);
        map.put("jqlCondition", jqlCondition);
    }

    @Override
    protected void getVelocityParamsForView(Map<String, Object> map, AbstractDescriptor abstractDescriptor) {
        if (!(abstractDescriptor instanceof FunctionDescriptor)) {
            throw new IllegalArgumentException("Descriptor must be a ConditionDescriptor.");
        }
        ApplicationUser user = ComponentAccessor.getUserManager().getUserByName("071716");
        try {
            FunctionDescriptor conditionDescriptor = (FunctionDescriptor) abstractDescriptor;
            JSONObject jsonObject = JSONObject.parseObject((String) conditionDescriptor.getArgs().get("copyFieldJson"));

            String source_issue = String.valueOf(jsonObject.get("source_issue"));
            List<String> target_issue = JSONObject.parseArray(String.valueOf(jsonObject.get("target_issue")), String.class);
            List<String> issue_type = JSONObject.parseArray(String.valueOf(jsonObject.get("issue_type")), String.class);
            String field_type = String.valueOf(jsonObject.get("field_type"));
            String source_field = String.valueOf(jsonObject.get("source_field"));
            List<String> target_field = JSONObject.parseArray(String.valueOf(jsonObject.get("target_field")), String.class);
            String jqlConditionEnabled = String.valueOf(jsonObject.get("jqlConditionEnabled"));
            String jqlCondition = String.valueOf(jsonObject.get("jqlCondition"));
            switch (source_field) {
                case "assignee":
                    map.put("source_field", "经办人");
                    break;
                case "reporter":
                    map.put("source_field", "报告人");
                    break;
                case "dueDate":
                    map.put("source_field", "到期日");
                    break;
                default:
                    map.put("source_field", ComponentAccessor.getCustomFieldManager().getCustomFieldObject(source_field).getFieldName());
            }
            map.put("source_issue", Constant.copyTypeMap.get(source_issue));
            if (target_issue != null) {
                List<String> targetIssueList = new ArrayList<>();
                for (String targetIssue : target_issue) {
                    targetIssueList.add(Constant.copyTypeMap.get(targetIssue));
                }
                map.put("target_issue", targetIssueList);
            }
            if (issue_type != null) {
                List<String> issueTypeName = new ArrayList<>();
                for (String s : issue_type) {
                    IssueType issueType=ComponentAccessor.getConstantsManager().getIssueType(s);
                    if (issueType == null) {
                        continue;
                    }
                    issueTypeName.add(issueType.getName());
                }
                map.put("issue_type", issueTypeName);
            }
            map.put("field_type", field_type);
            List<String> targetFieldList = new ArrayList<>();
            for (String targetField : target_field) {
                switch (targetField) {
                    case "assignee":
                        targetFieldList.add("经办人");
                        break;
                    case "reporter":
                        targetFieldList.add("报告人");
                        break;
                    case "dueDate":
                        targetFieldList.add("到期日");
                        break;
                    default:
                        targetFieldList.add(ComponentAccessor.getCustomFieldManager().getCustomFieldObject(targetField).getFieldName());
                }
            }
            map.put("target_field", targetFieldList);
            map.put("jqlConditionEnabled", jqlConditionEnabled);
            map.put("jqlCondition", jqlCondition);
            map.put("copyFieldJson", jsonObject.toJSONString());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public Map<String, ?> getDescriptorParams(Map<String, Object> map) {
        Map<String,Object> hashMap = new HashMap<>();
        try {
            String[] source_issue = (String[]) map.get("source_issue");
            String[] target_issue = (String[]) map.get("target_issue");
            String[] issue_type = (String[]) map.get("issue_type");
            String[] field_type = (String[]) map.get("field_type");
            String[] source_field = (String[]) map.get("source_field");
            String[] target_field = (String[]) map.get("target_field");
            String[] jqlConditionEnabled = (String[]) map.get("jqlConditionEnabled");
            String[] jqlCondition = (String[]) map.get("jqlCondition");
            JSONObject resp = new JSONObject();
            resp.put("source_issue", source_issue[0]);
            resp.put("target_issue", target_issue);
            resp.put("issue_type", issue_type);
            resp.put("field_type", field_type[0]);
            resp.put("source_field", source_field[0]);
            resp.put("target_field", target_field);
            resp.put("jqlConditionEnabled", jqlConditionEnabled[0]);
            resp.put("jqlCondition", jqlCondition[0]);
            hashMap.put("copyFieldJson", resp.toJSONString());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return hashMap;
    }
}
