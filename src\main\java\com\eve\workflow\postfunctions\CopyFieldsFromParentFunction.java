package com.eve.workflow.postfunctions;

import com.alibaba.fastjson.JSONObject;
import com.atlassian.jira.component.ComponentAccessor;
import com.atlassian.jira.event.type.EventDispatchOption;
import com.atlassian.jira.issue.Issue;
import com.atlassian.jira.issue.MutableIssue;
import com.atlassian.jira.issue.fields.CustomField;
import com.atlassian.jira.user.ApplicationUser;
import com.opensymphony.module.propertyset.PropertySet;
import com.opensymphony.workflow.WorkflowException;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/7/27
 */
public class CopyFieldsFromParentFunction extends JsuWorkflowFunction {
    @Override
    public void doIt(Map transientVars, Map args, PropertySet ps) throws WorkflowException {
        try {
            MutableIssue mutableIssue = super.getIssue(transientVars);
            ApplicationUser currentUser = ComponentAccessor.getJiraAuthenticationContext().getLoggedInUser();
            Issue parentIssue = mutableIssue.getParentObject();
            if (parentIssue == null) {
                return;
            }
            String fieldSignJson = String.valueOf(args.get("copyFieldsFromParentJson"));
            JSONObject jsonObject = JSONObject.parseObject(fieldSignJson);
            List<String> fieldIdList = JSONObject.parseArray(String.valueOf(jsonObject.get("fields")), String.class);
            if (fieldIdList != null) {
                for (String fieldId : fieldIdList) {
                    CustomField customField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(fieldId);
                    if (customField != null) {
                        Object customFieldValue = parentIssue.getCustomFieldValue(customField);
                        mutableIssue.setCustomFieldValue(customField, customFieldValue);
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new WorkflowException(e);
        }
    }
}
