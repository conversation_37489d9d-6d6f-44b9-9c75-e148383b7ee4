package com.eve.workflow.postfunctions;

import com.alibaba.fastjson.JSONObject;
import com.atlassian.jira.component.ComponentAccessor;
import com.atlassian.jira.issue.fields.CustomField;
import com.atlassian.jira.plugin.workflow.AbstractWorkflowPluginFactory;
import com.atlassian.jira.plugin.workflow.WorkflowPluginFunctionFactory;
import com.opensymphony.workflow.loader.AbstractDescriptor;
import com.opensymphony.workflow.loader.FunctionDescriptor;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/7/27
 */
public class CopyFieldsFromParentFunctionFactory extends AbstractWorkflowPluginFactory implements WorkflowPluginFunctionFactory {
    @Override
    protected void getVelocityParamsForInput(Map<String, Object> map) {
        List<CustomField> customFieldList = ComponentAccessor.getCustomFieldManager().getCustomFieldObjects();//获取全部字段
//        for (CustomField customField : customFieldList) {
//            String name = customField.getName();
//            String fieldName = customField.getFieldName();
//            String id = customField.getId();
//        }
        map.put("customFieldList", customFieldList);
    }

    @Override
    protected void getVelocityParamsForEdit(Map<String, Object> map, AbstractDescriptor abstractDescriptor) {
        if (!(abstractDescriptor instanceof FunctionDescriptor)) {
            throw new IllegalArgumentException("Descriptor must be a ConditionDescriptor.");
        }
        List<CustomField> customFieldList = ComponentAccessor.getCustomFieldManager().getCustomFieldObjects();//获取全部字段
        FunctionDescriptor conditionDescriptor = (FunctionDescriptor) abstractDescriptor;
        JSONObject jsonObject = JSONObject.parseObject((String) conditionDescriptor.getArgs().get("copyFieldsFromParentJson"));
        List<String> fieldIdList = JSONObject.parseArray(String.valueOf(jsonObject.get("fields")), String.class);
        map.put("customFieldList", customFieldList);
        map.put("fields", fieldIdList);
    }

    @Override
    protected void getVelocityParamsForView(Map<String, Object> map, AbstractDescriptor abstractDescriptor) {
        if (!(abstractDescriptor instanceof FunctionDescriptor)) {
            throw new IllegalArgumentException("Descriptor must be a ConditionDescriptor.");
        }
        try{
            FunctionDescriptor conditionDescriptor = (FunctionDescriptor) abstractDescriptor;
            JSONObject jsonObject = JSONObject.parseObject((String) conditionDescriptor.getArgs().get("copyFieldsFromParentJson"));
            List<String> fieldIdList = JSONObject.parseArray(String.valueOf(jsonObject.get("fields")), String.class);
            List<String> fieldNameList = new ArrayList<>();
            for (String fieldId : fieldIdList) {
                CustomField customField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(fieldId);
                if (customField!=null) {
                    fieldNameList.add(customField.getFieldName());
                }
            }
            map.put("fields", fieldNameList);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public Map<String, ?> getDescriptorParams(Map<String, Object> map) {
        Map<String,Object> hashMap = new HashMap<>();
        try {
            String[] fields = (String[]) map.get("fields");
            JSONObject resp = new JSONObject();
            resp.put("fields", fields);
            hashMap.put("copyFieldsFromParentJson", resp.toJSONString());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return hashMap;
    }
}
