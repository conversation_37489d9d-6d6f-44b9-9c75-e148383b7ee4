package com.eve.workflow.postfunctions;

import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.atlassian.jira.bc.issue.IssueService;
import com.atlassian.jira.component.ComponentAccessor;
import com.atlassian.jira.config.SubTaskManager;
import com.atlassian.jira.event.type.EventDispatchOption;
import com.atlassian.jira.exception.CreateException;
import com.atlassian.jira.issue.IssueInputParameters;
import com.atlassian.jira.issue.MutableIssue;
import com.atlassian.jira.issue.fields.CustomField;
import com.atlassian.jira.user.ApplicationUser;
import com.eve.utils.Constant;
import com.eve.utils.JiraCustomTool;
import com.eve.utils.Utils;
import com.opensymphony.module.propertyset.PropertySet;
import com.opensymphony.workflow.WorkflowException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.*;

/**
 * <AUTHOR>
 * @date 2022/9/2
 */
public class CreateSubIssueFunction extends JsuWorkflowFunction {
    private static final Logger log = LoggerFactory.getLogger(CreateSubIssueFunction.class);
    private JiraCustomTool jiraCustomTool;
    private IssueService issueService;

    public CreateSubIssueFunction(JiraCustomTool jiraCustomTool, IssueService issueService) {
        this.jiraCustomTool = jiraCustomTool;
        this.issueService = issueService;
    }

    @Override
    public void doIt(Map transientVars, Map args, PropertySet ps) throws WorkflowException {
        MutableIssue mutableIssue = super.getIssue(transientVars);
        try {

            String fieldSignJson = String.valueOf(args.get("parmJson"));
            JSONObject jsonObject = JSON.parseObject(fieldSignJson);
            String jqlConditionEnabled = String.valueOf(jsonObject.get("jqlConditionEnabled"));
            String jqlCondition = String.valueOf(jsonObject.get("jqlCondition"));

            //需要通过jql校验才执行
            ApplicationUser currentUser = ComponentAccessor.getJiraAuthenticationContext().getLoggedInUser();
            if ("true".equals(jqlConditionEnabled) && !jiraCustomTool.matchJql(mutableIssue, jqlCondition, currentUser)) {
                return ;//jql条件激活且不满足jql条件，不执行该功能
            }

            String createParam = String.valueOf(jsonObject.get("createParam"));//转换ID
            Map<String, String> paramMap = new HashMap<>();
            String[] split = createParam.split(";");
            for (String s : split) {
                String[] split1 = s.split(":");
                paramMap.put(split1[0], split1[1]);
            }

            /*创建issue参数，issueInputParameters*/
            IssueInputParameters issueInputParameters = issueService.newIssueInputParameters();
            // 设置项目ID todo
            issueInputParameters.setProjectId(mutableIssue.getProjectId());
            // 设置问题类型
            issueInputParameters.setIssueTypeId("10003");
            // 设置经办人
//            issueInputParameters.setAssigneeId(currentUser.getUsername());
            // 设置报告人
            issueInputParameters.setReporterId(currentUser.getUsername());
            //取产品阶段的对应值
//                issueInputParameters.addCustomFieldValue(productStage.getIdAsLong(), op.getOptionId() + "");
            //设置概要
            issueInputParameters.setSummary("");
            //取产品状态的对应值
//                issueInputParameters.addCustomFieldValue(Constant.productStateCustId, productStatus.get(entry.getValue()) + "");
//                String jhpsrqStr = Utils.getDateFormat(jhpsrDate);
//                issueInputParameters.addCustomFieldValue(Constant.productPlanReviewDateCustId, Utils.getDateStr(jhpsrqStr));

            for (Map.Entry<String, String> entry : paramMap.entrySet()) {
                String entryKey = entry.getKey();
                String entryValue = entry.getValue();
                CustomField customFiled = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(entryKey);
                if (customFiled == null || entryValue == null || "".equals(entryValue)) {
                    if ("summary".equals(entryKey)) {
//                        log.error(entryKey + "=" + entryValue);
                        issueInputParameters.setSummary(entryValue);
                    } else if ("assignee".equals(entryKey)) {
//                        log.error(entryKey + "=" + entryValue);
                        issueInputParameters.setAssigneeId(entryValue);
                    } else if ("priority".equals(entryKey)) {
                        issueInputParameters.setPriorityId(entryValue);
                    } else if ("description".equals(entryKey)) {
                        issueInputParameters.setDescription(entryValue);
                    }
                    continue;
                }
//                String customFiledIdStr = customFiled.getId();
                String customFieldTypeKey = customFiled.getCustomFieldType().getKey();
                switch (customFieldTypeKey) {
                    case "com.atlassian.jira.plugin.system.customfieldtypes:select":
                    case "com.atlassian.jira.plugin.system.customfieldtypes:multiuserpicker":
                    case "com.atlassian.jira.plugin.system.customfieldtypes:userpicker":
                    case "com.atlassian.jira.plugin.system.customfieldtypes:textfield":
                    case "com.atlassian.jira.plugin.system.customfieldtypes:textarea":
                    case "com.atlassian.jira.plugin.system.customfieldtypes:url":
                        issueInputParameters.addCustomFieldValue(entryKey, entryValue);
                        break;
                    case "com.atlassian.jira.plugin.system.customfieldtypes:datepicker":
                    case "com.atlassian.jira.plugin.system.customfieldtypes:datetime":
                        //格式 yyyy-MM-dd HH:mm:ss
                        entryValue = entryValue.length() == 10 ? entryValue + " 00:00:00" : entryValue;
                        issueInputParameters.addCustomFieldValue(entryKey, Utils.getDateTimeStr(entryValue));
                        break;
                    default:
                        throw new IllegalStateException("Unexpected value: " + customFieldTypeKey);
                }
            }

//            log.error(issueInputParameters.getCustomFieldValue());
            //创建issue参数校验
            com.atlassian.jira.bc.issue.IssueService.CreateValidationResult createValidationResult = issueService.validateSubTaskCreate(currentUser,mutableIssue.getId(), issueInputParameters);


            if (!createValidationResult.isValid()) {
                throw new CreateException("创建子任务参数校验失败：" + createValidationResult.getErrorCollection());
            }
            //创建issue
            IssueService.IssueResult subIssueRes = issueService.create(currentUser, createValidationResult);
            if (!subIssueRes.isValid()) {
                throw new CreateException("创建子任务失败：" + subIssueRes.getErrorCollection().getErrorMessages());
            }
            MutableIssue subIssue = subIssueRes.getIssue();
            SubTaskManager subTaskManager = ComponentAccessor.getSubTaskManager();
            subTaskManager.createSubTaskIssueLink(mutableIssue, subIssue, currentUser);
            subIssue.setParentId(mutableIssue.getId());

        } catch (Exception e) {
            log.error(Utils.errInfo(e));
            throw new WorkflowException(e);
        }
    }
}
