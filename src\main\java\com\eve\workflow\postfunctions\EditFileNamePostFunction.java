package com.eve.workflow.postfunctions;


import com.atlassian.jira.component.ComponentAccessor;
import com.atlassian.jira.issue.AttachmentManager;
import com.atlassian.jira.issue.IssueManager;
import com.atlassian.jira.issue.MutableIssue;
import com.atlassian.jira.issue.attachment.Attachment;
import com.atlassian.jira.issue.fields.CustomField;
import com.atlassian.jira.ofbiz.OfBizDelegator;
import com.opensymphony.module.propertyset.PropertySet;
import com.opensymphony.workflow.WorkflowException;
import org.ofbiz.core.entity.GenericValue;

import java.util.List;
import java.util.Map;

/**
 * This is the post-function class that gets executed at the end of the transition.
 * Any parameters that were saved in your factory class will be available in the transientVars Map.
 */
public class EditFileNamePostFunction extends JsuWorkflowFunction {
    private OfBizDelegator ofBizDelegator;
    long custFieldID = 11851L;
    String strFileName = "";

    public EditFileNamePostFunction(OfBizDelegator ofBizDelegator) {
        this.ofBizDelegator = ofBizDelegator;
    }

    @Override
    public void doIt(Map transientVars, Map args, PropertySet ps) throws WorkflowException {

        MutableIssue issue = super.getIssue(transientVars);
        // todo 获取当前issue
        IssueManager issueManager = ComponentAccessor.getIssueManager();
        //MutableIssue issue = issueManager.getIssueObject("");

        CustomField custField = ComponentAccessor
                .getCustomFieldManager()
                .getCustomFieldObject(custFieldID);
        
        if (custField == null){
            return;
        }

        AttachmentManager attachmentManager = ComponentAccessor.getAttachmentManager();
        List<Attachment> attachmentList = attachmentManager.getAttachments(issue);

        if (attachmentList.size() != 1) {
            return;
        }
        try {
            for (Attachment attachment : attachmentList) {
                GenericValue attachmentGV = ofBizDelegator.findById("FileAttachment", attachment.getId());
                strFileName = attachment.getFilename();
                
                strFileName = strFileName.indexOf(".") !=-1
                        ? issue.getCustomFieldValue(custField) + strFileName.substring(strFileName.lastIndexOf("."))
                        : issue.getCustomFieldValue(custField)+"";
                
                attachmentGV.set("filename", strFileName);
                attachmentGV.store();
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new WorkflowException(e);
        }
    }
}