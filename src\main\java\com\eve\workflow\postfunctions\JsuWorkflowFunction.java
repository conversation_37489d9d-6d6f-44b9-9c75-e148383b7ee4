package com.eve.workflow.postfunctions;

import com.atlassian.jira.component.ComponentAccessor;
import com.atlassian.jira.security.JiraAuthenticationContext;
import com.atlassian.jira.util.I18nHelper;
import com.atlassian.jira.workflow.function.issue.AbstractJiraFunctionProvider;
import com.opensymphony.module.propertyset.PropertySet;
import com.opensymphony.workflow.WorkflowException;

import java.util.Map;

public abstract class JsuWorkflowFunction extends AbstractJiraFunctionProvider {
    private I18nHelper i18nHelperDelegate;

    public JsuWorkflowFunction() {
    }

    @Override
    public void execute(Map transientVars, Map args, PropertySet ps) throws WorkflowException {
        doIt(transientVars, args, ps);
    }

    public abstract void doIt(Map transientVars, Map args, PropertySet ps) throws WorkflowException;


    public String getText(String key) {
        return getI18nHelper().getText(key);
    }

    public String getText(String key, String params) {
        return getI18nHelper().getText(key, params);
    }

    public String getText(String key, String params, String params1) {

        return getI18nHelper().getText(key, params, params1);
    }

    public String getText(String key, String params, String params1, String params2) {
        return getI18nHelper().getText(key, params, params1, params2);
    }


    public String getText(String key, String[] params) {
        return "";
        // return getI18nHelper().getText(key,params);
    }


    protected I18nHelper getI18nHelper() {
        if (this.i18nHelperDelegate == null) {
            this.i18nHelperDelegate = ((JiraAuthenticationContext) ComponentAccessor.getComponentOfType(JiraAuthenticationContext.class)).getI18nHelper();
        }
        return this.i18nHelperDelegate;
    }

}
