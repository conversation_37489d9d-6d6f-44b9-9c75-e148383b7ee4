package com.eve.workflow.postfunctions;

import com.atlassian.jira.component.ComponentAccessor;
import com.atlassian.jira.issue.Issue;
import com.atlassian.jira.issue.fields.CustomField;
import com.eve.beans.ProgressBean;
import com.eve.services.ProgressService;
import com.eve.utils.Constant;
import com.eve.utils.Utils;
import com.opensymphony.module.propertyset.PropertySet;
import com.opensymphony.workflow.WorkflowException;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/4/7
 */
public class ProgressPostFunctions extends JsuWorkflowFunction{
    @Autowired
    private ProgressService progressService;

    public ProgressPostFunctions(ProgressService progressService) {
        this.progressService = progressService;
    }
    @Override
    public void doIt(Map transientVars, Map args, PropertySet ps) throws WorkflowException {
        try {
            Issue issue = super.getIssue(transientVars);

            CustomField processCustField = Utils.getCustomFieldByID(Constant.progressCustId);
            CustomField nextStepCustField = Utils.getCustomFieldByID(Constant.nextStepCustId);
            CustomField riskCustField = Utils.getCustomFieldByID(Constant.riskCustId);
            CustomField strageCustField = Utils.getCustomFieldByID(Constant.strageCustId);

            String productProcess = processCustField == null ? "" : (String) issue.getCustomFieldValue(processCustField);
            String productNextStep = nextStepCustField == null ? "" : (String) issue.getCustomFieldValue(nextStepCustField);
            String productRisk = riskCustField == null ? "" : (String) issue.getCustomFieldValue(riskCustField);
            String productStrage = strageCustField == null ? "" : (String) issue.getCustomFieldValue(strageCustField);


            com.atlassian.jira.issue.comments.Comment comment  = ComponentAccessor.getCommentManager().getLastComment(issue);



            progressService.insert(new ProgressBean(
                    0L,
                    issue.getId(),
                    productProcess,
                    productNextStep,
                    productRisk,
                    productStrage,
                    comment == null ? "" : comment.getBody(),
                    new java.sql.Timestamp(System.currentTimeMillis())
            ));

        } catch (Exception e) {
            throw e;
        }
    }
}
