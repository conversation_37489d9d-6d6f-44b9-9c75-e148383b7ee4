package com.eve.workflow.postfunctions;

import com.atlassian.activeobjects.tx.Transactional;
import com.atlassian.jira.component.ComponentAccessor;
import com.atlassian.jira.issue.Issue;
import com.atlassian.jira.issue.MutableIssue;
import com.atlassian.jira.issue.customfields.option.Option;
import com.atlassian.jira.issue.fields.CustomField;
import com.atlassian.jira.issue.issuetype.IssueType;
import com.atlassian.jira.user.ApplicationUser;
import com.eve.utils.Constant;
import com.eve.utils.JiraCustomTool;
import com.eve.utils.Utils;
import com.opensymphony.module.propertyset.PropertySet;
import com.opensymphony.workflow.WorkflowException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.util.ObjectUtils;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/1/17
 */
public class ProjectCodeCreateFunction extends JsuWorkflowFunction {
    private static final Logger log = LoggerFactory.getLogger(ProjectCodeCreateFunction.class);

    private JiraCustomTool jiraCustomTool;

    public ProjectCodeCreateFunction(JiraCustomTool jiraCustomTool) {
        this.jiraCustomTool = jiraCustomTool;
    }

    @Override
    public void doIt(Map transientVars, Map args, PropertySet ps) throws WorkflowException {
        MutableIssue mutableIssue = super.getIssue(transientVars);
        ApplicationUser reporter = mutableIssue.getReporter();
        IssueType issueType = mutableIssue.getIssueType();
        try {
//            String jql = Constant.projectManageTestProjectIdList.contains(mutableIssue.getProjectId()) ? "project in (WSXEDC,XMGLCSJM)" : "project in (XMGL, LFPXMGL)";
            String jql = "project = " + mutableIssue.getProjectId();

            jql += " AND issuetype = " + mutableIssue.getIssueTypeId() + " AND cf[" + Constant.projectCodeCustomFieldId + "] is not EMPTY AND cf[" + Constant.reviewMonthCustomFieldId + "] is not EMPTY";
            ApplicationUser dladminUser = ComponentAccessor.getUserManager().getUserByName(Constant.dladminUserName);
            List<Issue> issueList = jiraCustomTool.getIssueListByJql(dladminUser, jql);
            CustomField projectCodeCustomField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(Constant.projectCodeCustomFieldId);
            CustomField projectLevelCustomField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(Constant.projectLevelCustomFieldId);
            CustomField projectNameCustomField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(Constant.projectNameCustomFieldId);
            Option projectLevelOption = (Option) mutableIssue.getCustomFieldValue(projectLevelCustomField);
            String projectName = projectNameCustomField == null ? "" : (String) mutableIssue.getCustomFieldValue(projectNameCustomField);
            List<String> projectCodeList = issueList.stream().map(e -> (String) e.getCustomFieldValue(projectCodeCustomField)).collect(Collectors.toList());
            String year = new SimpleDateFormat("yy", Locale.CHINESE).format(new Date());
            //hz 2-4 4   jm 4-6 6
//            List<Long> codeList = projectCodeList.stream().filter(e -> e.length() > 4 && e.substring(2, 4).equals(year)).map(e -> Long.parseLong(e.substring(4))).collect(Collectors.toList());
            List<Long> codeList = projectCodeList.stream().filter(e -> e.length() > 5 && e.substring(e.length()-6, e.length()-4).equals(year)).map(e -> Long.parseLong(e.substring(e.length()-4))).collect(Collectors.toList());

//            String issueTypeCode = Objects.equals(mutableIssue.getIssueTypeId(), Constant.platformIssueTypeId + "") ? "P" : Objects.equals(mutableIssue.getIssueTypeId(), Constant.topicIssueTypeId + "") ? "K" : "X";
            String issueTypeCode = "K";
            String projectLevel = projectLevelOption == null  ? "B" : projectLevelOption.getValue().equals("A") ? "A" : projectLevelOption.getValue().equals("S+") ? "S" : projectLevelOption.getValue().equals("S") ? "S" : "B";

            StringBuilder code = new StringBuilder(ObjectUtils.isEmpty(codeList) ? "1" : Collections.max(codeList) + 1 + "");
            if ("X".equals(issueTypeCode)) {
                throw new WorkflowException("问题类型错误!");
            }
            while (code.length() < 4) {
                code.insert(0, "0");
            }
            String platform = "";
            if (mutableIssue.getProjectId().equals(Constant.lfpProjectManageTestProjectId) || mutableIssue.getProjectId().equals(Constant.lfpProjectManageProjectId)) {
                CustomField platformCustomField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(Constant.affiliatedPlatformCustomFieldId);
//                mutableIssue.getCustomFieldValue(platformCustomField)
                Map<String, Option> affiliatedPlatformMap = platformCustomField == null ? new HashMap<>() : (Map<String, Option>) mutableIssue.getCustomFieldValue(platformCustomField);
                Option option = affiliatedPlatformMap.get(null);
                platform = Utils.getAffiliatedPlatformCodeMap().getOrDefault(option == null ? 0L : option.getOptionId(), "");
            }
            String projectCode = platform + issueTypeCode + projectLevel + year + code;
            mutableIssue.setCustomFieldValue(projectCodeCustomField, projectCode);
            //更新概要
            mutableIssue.setSummary(projectCode + "-" + projectName + "-" + jiraCustomTool.getFirstAndLastName(reporter));
        } catch (Exception e) {
            log.error(Utils.errInfo(e));
            throw new WorkflowException(e);
        }
    }
}
