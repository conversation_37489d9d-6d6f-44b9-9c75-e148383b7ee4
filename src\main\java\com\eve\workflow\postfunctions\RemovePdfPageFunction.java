package com.eve.workflow.postfunctions;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.atlassian.core.util.FileUtils;
import com.atlassian.jira.bc.issue.IssueService;
import com.atlassian.jira.component.ComponentAccessor;
import com.atlassian.jira.issue.MutableIssue;
import com.atlassian.jira.issue.attachment.Attachment;
import com.atlassian.jira.ofbiz.OfBizDelegator;
import com.atlassian.jira.user.ApplicationUser;
import com.atlassian.jira.util.AttachmentUtils;
import com.eve.utils.JiraCustomTool;
import com.eve.utils.Utils;
import com.opensymphony.module.propertyset.PropertySet;
import com.opensymphony.workflow.WorkflowException;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.ofbiz.core.entity.GenericValue;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/9/2
 */
public class RemovePdfPageFunction extends JsuWorkflowFunction {
    private static final Logger log = LoggerFactory.getLogger(RemovePdfPageFunction.class);
    private JiraCustomTool jiraCustomTool;
    private OfBizDelegator ofBizDelegator;
    private IssueService issueService;

    public RemovePdfPageFunction(JiraCustomTool jiraCustomTool, OfBizDelegator ofBizDelegator, IssueService issueService) {
        this.jiraCustomTool = jiraCustomTool;
        this.ofBizDelegator = ofBizDelegator;
        this.issueService = issueService;
    }

    @Override
    public void doIt(Map transientVars, Map args, PropertySet ps) throws WorkflowException {
        MutableIssue mutableIssue = super.getIssue(transientVars);
        try {

            String fieldSignJson = String.valueOf(args.get("parmJson"));
            JSONObject jsonObject = JSON.parseObject(fieldSignJson);
            String jqlConditionEnabled = String.valueOf(jsonObject.get("jqlConditionEnabled"));
            String jqlCondition = String.valueOf(jsonObject.get("jqlCondition"));

            //需要通过jql校验才执行
            ApplicationUser currentUser = ComponentAccessor.getJiraAuthenticationContext().getLoggedInUser();
            if ("true".equals(jqlConditionEnabled) && !jiraCustomTool.matchJql(mutableIssue, jqlCondition, currentUser)) {
                return ;//jql条件激活且不满足jql条件，不执行该功能
            }

            String fileCate = jsonObject.getString("fileCate");//
            Integer startPosition = jsonObject.getInteger("startPosition");//
            Integer removePageNum = jsonObject.getInteger("removePageNum");//
            String gainOldFilePath = Utils.getGainOldFilePath(mutableIssue.getKey());
            Path directory = Paths.get(gainOldFilePath);
            if (!Files.exists(directory)) {
                Files.createDirectories(directory);
            }
            List<Attachment> attachmentList = jiraCustomTool.getIssueAttachmentByCate(mutableIssue, fileCate);

            for (Attachment attachment : attachmentList) {
                if (!"application/pdf".equals(attachment.getMimetype())) {
                    continue;
                }
                File attachmentFile = AttachmentUtils.getAttachmentFile(attachment);
                File file = new File(gainOldFilePath + File.separator + System.currentTimeMillis() + attachment.getFilename());
                FileUtils.copyFile(attachmentFile, file, true);// 将原文件流保存到本地
//                File pageFile = new File(pageFilePath);

                // 加载源文件
//                PDDocument sourceDoc = PDDocument.load(pageFile);
//                PDPage sourcePage = sourceDoc.getPage(0);

                // 加载目标文件到内存
                PDDocument targetDoc = PDDocument.load(new FileInputStream(attachmentFile), (String) null);

                // 在内存中构建合并后的文档
                PDDocument mergedDoc = new PDDocument();

                // 插入目标文件前 startPosition 页（若不足则补空白页）
                for (int i = 0; i < startPosition; i++) {
                    if (i < targetDoc.getNumberOfPages()) {
                        mergedDoc.addPage(targetDoc.getPage(i));
//                    } else {
//                        mergedDoc.addPage(new PDPage());  // 补空白页
                    }
                }

                // 插入源文件页
//                mergedDoc.addPage(sourcePage);

                // 插入目标文件剩余页
                for (int i = startPosition + removePageNum; i < targetDoc.getNumberOfPages(); i++) {
                    mergedDoc.addPage(targetDoc.getPage(i));
                }

                // 将合并后的文档保存到内存中的字节流
                ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
                mergedDoc.save(outputStream);
                mergedDoc.close();

                // 直接覆盖原目标文件
                try (FileOutputStream fos = new FileOutputStream(attachmentFile, false)) {  // false 表示覆盖
                    fos.write(outputStream.toByteArray());
                }finally {
                    outputStream.close();
                }
                GenericValue attachmentGV = ofBizDelegator.findById("FileAttachment", attachment.getId());
                attachmentGV.set("filesize", attachmentFile.length());
                attachmentGV.store();

            }


        } catch (Exception e) {
            log.error(Utils.errInfo(e));
            throw new WorkflowException(e);
        }
    }
}
