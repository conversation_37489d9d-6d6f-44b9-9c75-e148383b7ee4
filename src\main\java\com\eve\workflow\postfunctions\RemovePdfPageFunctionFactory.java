package com.eve.workflow.postfunctions;

import com.alibaba.fastjson.JSONObject;
import com.atlassian.jira.component.ComponentAccessor;
import com.atlassian.jira.plugin.workflow.AbstractWorkflowPluginFactory;
import com.atlassian.jira.plugin.workflow.WorkflowPluginFunctionFactory;
import com.eve.utils.Constant;
import com.opensymphony.workflow.loader.AbstractDescriptor;
import com.opensymphony.workflow.loader.FunctionDescriptor;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/9/2
 */
public class RemovePdfPageFunctionFactory extends AbstractWorkflowPluginFactory implements WorkflowPluginFunctionFactory {
    @Override
    protected void getVelocityParamsForInput(Map<String, Object> map) {
    }

    @Override
    protected void getVelocityParamsForEdit(Map<String, Object> map, AbstractDescriptor abstractDescriptor) {
        if (!(abstractDescriptor instanceof FunctionDescriptor)) {
            throw new IllegalArgumentException("Descriptor must be a FunctionDescriptor.");
        }
        FunctionDescriptor functionDescriptor = (FunctionDescriptor) abstractDescriptor;

        //edit
        JSONObject jsonObject = JSONObject.parseObject((String) functionDescriptor.getArgs().get("parmJson"));
        String fileCate = String.valueOf(jsonObject.get("fileCate"));
        String startPosition = String.valueOf(jsonObject.get("startPosition"));
        String removePageNum = String.valueOf(jsonObject.get("removePageNum"));
        String jqlConditionEnabled = String.valueOf(jsonObject.get("jqlConditionEnabled"));
        String jqlCondition = String.valueOf(jsonObject.get("jqlCondition"));
        map.put("fileCate", fileCate);
        map.put("startPosition", startPosition);
        map.put("removePageNum", removePageNum);
        map.put("jqlConditionEnabled", jqlConditionEnabled);
        map.put("jqlCondition", jqlCondition);
    }

    @Override
    protected void getVelocityParamsForView(Map<String, Object> map, AbstractDescriptor abstractDescriptor) {
        if (!(abstractDescriptor instanceof FunctionDescriptor)) {
            throw new IllegalArgumentException("Descriptor must be a FunctionDescriptor.");
        }
        FunctionDescriptor functionDescriptor = (FunctionDescriptor) abstractDescriptor;
        JSONObject jsonObject = JSONObject.parseObject((String) functionDescriptor.getArgs().get("parmJson"));
        String fileCate = String.valueOf(jsonObject.get("fileCate"));
        String startPosition = String.valueOf(jsonObject.get("startPosition"));
        String removePageNum = String.valueOf(jsonObject.get("removePageNum"));
        String jqlConditionEnabled = String.valueOf(jsonObject.get("jqlConditionEnabled"));
        String jqlCondition = String.valueOf(jsonObject.get("jqlCondition"));

        map.put("fileCate", fileCate);
        map.put("startPosition", startPosition);
        map.put("removePageNum", removePageNum);
        map.put("jqlConditionEnabled", jqlConditionEnabled);
        map.put("jqlCondition", jqlCondition);
    }

    @Override
    public Map<String, ?> getDescriptorParams(Map<String, Object> map) {
        Map<String, Object> hashMap = new HashMap<>();
        try {
            JSONObject jsonObject = new JSONObject();
            String[] fileCate = (String[]) map.get("fileCate");
            jsonObject.put("fileCate", fileCate[0]);
            String[] startPosition = (String[]) map.get("startPosition");
            jsonObject.put("startPosition", startPosition[0]);
            String[] removePageNum = (String[]) map.get("removePageNum");
            jsonObject.put("removePageNum", removePageNum[0]);
            String[] jqlConditionEnabled = (String[]) map.get("jqlConditionEnabled");
            jsonObject.put("jqlConditionEnabled", jqlConditionEnabled[0]);
            String[] jqlCondition = (String[]) map.get("jqlCondition");
            jsonObject.put("jqlCondition", jqlCondition[0]);
            hashMap.put("parmJson", jsonObject.toJSONString());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return hashMap;
    }
}
