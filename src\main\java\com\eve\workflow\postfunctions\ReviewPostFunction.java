package com.eve.workflow.postfunctions;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.atlassian.jira.component.ComponentAccessor;
import com.atlassian.jira.event.type.EventDispatchOption;
import com.atlassian.jira.issue.IssueManager;
import com.atlassian.jira.issue.MutableIssue;
import com.atlassian.jira.issue.fields.CustomField;
import com.atlassian.jira.user.ApplicationUser;
import com.opensymphony.module.propertyset.PropertySet;
import com.opensymphony.workflow.WorkflowException;


/**
 * This is the post-function class that gets executed at the end of the transition.
 * Any parameters that were saved in your factory class will be available in the transientVars Map.
 */
public class ReviewPostFunction extends JsuWorkflowFunction {
    
    long childCustFieldID = 11839L;
    long parentCustFieldID = 10503L;

    @Override
    public void doIt(Map transientVars, Map args, PropertySet ps) throws WorkflowException {
        try {
            MutableIssue mutableIssue = super.getIssue(transientVars);
            MutableIssue parentIssue = ComponentAccessor
                    .getIssueManager()
                    .getIssueObject(mutableIssue.getParentId());
            IssueManager issueManager = ComponentAccessor.getIssueManager();

            CustomField parentCustField = ComponentAccessor
                    .getCustomFieldManager()
                    .getCustomFieldObject(parentCustFieldID);
            CustomField childCustField = ComponentAccessor
                    .getCustomFieldManager()
                    .getCustomFieldObject(childCustFieldID);
            
            if (null == parentCustField){
                return;
            }

            List<ApplicationUser> parentusers = (List<ApplicationUser>) parentIssue.getCustomFieldValue(parentCustField);
            if (null == parentusers){
                parentusers = new ArrayList<ApplicationUser>();
            }
            ApplicationUser childusers = (ApplicationUser) mutableIssue.getCustomFieldValue(childCustField);
            if (null == childusers){
                return;
            }
            parentusers.add(childusers);
            parentIssue.setCustomFieldValue(parentCustField, parentusers);
            ApplicationUser currentUser = ComponentAccessor.getJiraAuthenticationContext().getLoggedInUser();
            issueManager.updateIssue(currentUser, parentIssue, EventDispatchOption.ISSUE_UPDATED, false);

        } catch (Exception e) {
            throw e;
        }
    }

}