package com.eve.workflow.postfunctions;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.atlassian.jira.bc.issue.IssueService;
import com.atlassian.jira.component.ComponentAccessor;
import com.atlassian.jira.issue.IssueInputParameters;
import com.atlassian.jira.issue.MutableIssue;
import com.atlassian.jira.security.JiraAuthenticationContext;
import com.atlassian.jira.user.ApplicationUser;
import com.atlassian.jira.util.thread.JiraThreadLocalUtil;
import com.eve.services.ResultService;
import com.eve.utils.JiraCustomTool;
import com.eve.utils.Utils;
import com.opensymphony.module.propertyset.PropertySet;
import com.opensymphony.workflow.WorkflowException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/12/26
 */
public class RunNextTransitionFunction extends JsuWorkflowFunction {
    private static final Logger log = LoggerFactory.getLogger(ResultService.class);
    private JiraCustomTool jiraCustomTool;
    private IssueService issueService;

    public RunNextTransitionFunction(JiraCustomTool jiraCustomTool, IssueService issueService) {
        this.jiraCustomTool = jiraCustomTool;
        this.issueService = issueService;
    }

    @Override
    public void doIt(Map transientVars, Map args, PropertySet ps) throws WorkflowException {
        MutableIssue mutableIssue = super.getIssue(transientVars);
        try {
            String fieldSignJson = String.valueOf(args.get("parmJson"));
            JSONObject jsonObject = JSON.parseObject(fieldSignJson);
            String transitionId = String.valueOf(jsonObject.get("transitionId"));
            String jqlConditionEnabled = String.valueOf(jsonObject.get("jqlConditionEnabled"));
            String jqlCondition = String.valueOf(jsonObject.get("jqlCondition"));
            //需要通过jql校验才执行
            ApplicationUser currentUser = ComponentAccessor.getJiraAuthenticationContext().getLoggedInUser();
            if ("true".equals(jqlConditionEnabled) && !jiraCustomTool.matchJql(mutableIssue, jqlCondition, currentUser)) {
                return;//jql条件激活且不满足jql条件，不执行该功能
            }

//        if (validationResult.isValid()) {
//            new Thread(() -> {
//                synchronized (this){
//                    while (mutableIssue.getStatusId().equals(currentStatusId)) {
//                        try {
//                            wait();
//                        } catch (InterruptedException ignored) {
//
//                        }
//                    }

                JiraThreadLocalUtil jiraThreadLocalUtil = ComponentAccessor.getComponent(JiraThreadLocalUtil.class);
                jiraThreadLocalUtil.preCall();
//
                try {
//                    Thread.sleep(3000);
                    IssueInputParameters issueInputParameters = issueService.newIssueInputParameters();
                    IssueService.TransitionValidationResult validationResult = issueService.validateTransition(currentUser, mutableIssue.getId(), Integer.parseInt(transitionId), issueInputParameters);

                    issueService.transition(currentUser, validationResult);
                } catch (Exception e) {
                    log.error(Utils.errInfo(e));
                } finally {
                    jiraThreadLocalUtil.postCall((org.apache.log4j.Logger) log);
                }


//                }
//            }).start();
//        }
        } catch (Exception e) {
            throw new WorkflowException("RunNextTransitionFunction: ", e);
        }
    }
}
