package com.eve.workflow.postfunctions;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.atlassian.jira.component.ComponentAccessor;
import com.atlassian.jira.issue.fields.CustomField;
import com.atlassian.jira.plugin.workflow.AbstractWorkflowPluginFactory;
import com.atlassian.jira.plugin.workflow.WorkflowPluginFunctionFactory;
import com.atlassian.jira.transition.TransitionEntry;
import com.atlassian.jira.transition.TransitionManager;
import com.atlassian.jira.transition.WorkflowTransitionEntry;
import com.atlassian.jira.workflow.JiraWorkflow;
import com.atlassian.jira.workflow.WorkflowManager;
import com.eve.beans.CopyFieldBean;
import com.eve.beans.WorkflowBean;
import com.eve.utils.Constant;
import com.eve.utils.Utils;
import com.opensymphony.workflow.loader.AbstractDescriptor;
import com.opensymphony.workflow.loader.FunctionDescriptor;

import java.io.File;
import java.io.FileInputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/12/26
 */
public class RunNextTransitionFunctionFactory extends AbstractWorkflowPluginFactory implements WorkflowPluginFunctionFactory {
    @Override
    protected void getVelocityParamsForInput(Map<String, Object> map) {
        WorkflowManager workflowManager = ComponentAccessor.getWorkflowManager();
        TransitionManager transitionManager = ComponentAccessor.getComponentOfType(TransitionManager.class);
        Collection<JiraWorkflow> jiraWorkflows = workflowManager.getWorkflows();
        Collection<WorkflowTransitionEntry> workflowTransitionEntries = transitionManager.getTransitions(jiraWorkflows);
        List<WorkflowBean> workflowBeanList = new ArrayList<>();
        for (WorkflowTransitionEntry workflowTransitionEntry : workflowTransitionEntries) {
            JiraWorkflow workflow = workflowTransitionEntry.getWorkflow();
            workflowBeanList.add(new WorkflowBean(workflow.getName(), (List<TransitionEntry>) workflowTransitionEntry.getTransitions()));
        }

        map.put("jiraWorkflows", jiraWorkflows);
        map.put("workflowBeanList", workflowBeanList);
        try {
//            Utils.pptToPdf("D:\\test\\ppt\\template1.ppt","D:\\test\\pdf\\");
//            Utils.pptxToPdf("D:\\test\\pptx\\template1.pptx","D:\\test\\pdf");
//            Utils.ppt2pdf(new FileInputStream("D:\\test\\ppt\\template1.ppt"));
//            Utils.convertPPTToPDF(new File("D:\\test\\ppt\\template1.ppt"),new File("D:\\test\\pdf\\template1.pdf"));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    protected void getVelocityParamsForEdit(Map<String, Object> map, AbstractDescriptor abstractDescriptor) {
        getVelocityParamsForInput(map);
        getVelocityParamsForView(map, abstractDescriptor);
    }

    @Override
    protected void getVelocityParamsForView(Map<String, Object> map, AbstractDescriptor abstractDescriptor) {
        if (!(abstractDescriptor instanceof FunctionDescriptor)) {
            throw new IllegalArgumentException("Descriptor must be a FunctionDescriptor.");
        }
        FunctionDescriptor conditionDescriptor = (FunctionDescriptor) abstractDescriptor;
        JSONObject jsonObject = JSON.parseObject((String) conditionDescriptor.getArgs().get("parmJson"));

        String transitionId = String.valueOf(jsonObject.get("transitionId"));
        String jqlConditionEnabled = String.valueOf(jsonObject.get("jqlConditionEnabled"));
        String jqlCondition = String.valueOf(jsonObject.get("jqlCondition"));

        map.put("transitionId", transitionId);
        map.put("jqlConditionEnabled", jqlConditionEnabled);
        map.put("jqlCondition", jqlCondition);
        map.put("parmJson", jsonObject);
    }

    @Override
    public Map<String, ?> getDescriptorParams(Map<String, Object> map) {
        Map<String, Object> hashMap = new HashMap<>();
        try {
            String[] transitionId = (String[]) map.get("transitionId");
            String[] jqlConditionEnabled = (String[]) map.get("jqlConditionEnabled");
            String[] jqlCondition = (String[]) map.get("jqlCondition");
            JSONObject resp = new JSONObject();
            resp.put("transitionId", transitionId[0]);
            resp.put("jqlConditionEnabled", jqlConditionEnabled[0]);
            resp.put("jqlCondition", jqlCondition[0]);
            hashMap.put("parmJson", resp.toJSONString());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return hashMap;
    }
}
