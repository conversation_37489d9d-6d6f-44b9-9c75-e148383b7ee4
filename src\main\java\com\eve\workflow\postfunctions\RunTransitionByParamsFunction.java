package com.eve.workflow.postfunctions;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.atlassian.jira.bc.issue.IssueService;
import com.atlassian.jira.component.ComponentAccessor;
import com.atlassian.jira.event.type.EventDispatchOption;
import com.atlassian.jira.issue.CustomFieldManager;
import com.atlassian.jira.issue.MutableIssue;
import com.atlassian.jira.issue.fields.CustomField;
import com.atlassian.jira.issue.fields.screen.FieldScreen;
import com.atlassian.jira.issue.fields.screen.FieldScreenLayoutItem;
import com.atlassian.jira.issue.fields.screen.FieldScreenManager;
import com.atlassian.jira.issue.fields.screen.FieldScreenTab;
import com.atlassian.jira.transition.TransitionManager;
import com.atlassian.jira.user.ApplicationUser;
import com.atlassian.jira.web.action.admin.translation.TranslationManager;
import com.atlassian.jira.workflow.JiraWorkflow;
import com.eve.beans.TransitionScreenFieldBean;
import com.eve.services.CustomToolService;
import com.eve.utils.JiraCustomTool;
import com.eve.utils.Utils;
import com.opensymphony.module.propertyset.PropertySet;
import com.opensymphony.workflow.WorkflowException;
import com.opensymphony.workflow.loader.ActionDescriptor;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/7/27
 */
public class RunTransitionByParamsFunction extends JsuWorkflowFunction {
    private static final Logger log = LoggerFactory.getLogger(RunTransitionByParamsFunction.class);
    private CustomToolService customToolService;
    private JiraCustomTool jiraCustomTool;
    private IssueService issueService;
    private TransitionManager transitionManager;
    private CustomFieldManager customFieldManager;
    private FieldScreenManager fieldScreenManager;

    public RunTransitionByParamsFunction(CustomToolService customToolService, JiraCustomTool jiraCustomTool, IssueService issueService, TransitionManager transitionManager, CustomFieldManager customFieldManager, FieldScreenManager fieldScreenManager) {
        this.customToolService = customToolService;
        this.jiraCustomTool = jiraCustomTool;
        this.issueService = issueService;
        this.transitionManager = transitionManager;
        this.customFieldManager = customFieldManager;
        this.fieldScreenManager = fieldScreenManager;
    }

    @Override
    public void doIt(Map transientVars, Map args, PropertySet ps) throws WorkflowException {
        try {
            MutableIssue mutableIssue = super.getIssue(transientVars);
            ApplicationUser currentUser = ComponentAccessor.getJiraAuthenticationContext().getLoggedInUser();
            JSONObject jsonObject = JSONObject.parseObject((String) args.get("parmJson"));

            String issueRelation = String.valueOf(jsonObject.get("issueRelation"));
            List<String> issueStatusList = JSONObject.parseArray(String.valueOf(jsonObject.get("issueStatusList")), String.class);
            String copyCustomFieldOfScreen = String.valueOf(jsonObject.get("copyCustomFieldOfScreen"));
            String transitionName = String.valueOf(jsonObject.get("transitionName"));

            List<MutableIssue> relationIssueList = customToolService.getRelationIssueList(issueRelation, mutableIssue, currentUser);
            for (MutableIssue relationIssue : relationIssueList) {
                String statusId = relationIssue.getStatusId();
                if (issueStatusList.contains(statusId)) {
                    if ("true".equals(copyCustomFieldOfScreen)) {
                        //复制界面上填写的字段，再进行转换。例 暂停原因，暂停时间
                        JiraWorkflow workflow = ComponentAccessor.getWorkflowManager().getWorkflow(mutableIssue);
                        Integer actionId = (Integer) transientVars.get("actionId");
                        Collection<ActionDescriptor> allActions = workflow.getAllActions();
                        ActionDescriptor actionDescriptor = allActions.stream().filter(e -> e.getId() == actionId).findFirst().orElse(null);

                        if (actionDescriptor != null && StringUtils.isNotEmpty(actionDescriptor.getView()) && actionDescriptor.getMetaAttributes().containsKey(JiraWorkflow.ACTION_SCREEN_ATTRIBUTE)) {
                            Long fieldScreenId = new Long((String) actionDescriptor.getMetaAttributes().get(JiraWorkflow.ACTION_SCREEN_ATTRIBUTE));
                            //根据界面id获取界面字段
                            FieldScreen fieldScreen = fieldScreenManager.getFieldScreen(fieldScreenId);
                            List<FieldScreenTab> fieldScreenTabs = fieldScreenManager.getFieldScreenTabs(fieldScreen);
                            for (FieldScreenTab fieldScreenTab : fieldScreenTabs) {
                                List<FieldScreenLayoutItem> fieldScreenLayoutItems = fieldScreenTab.getFieldScreenLayoutItems();
                                for (FieldScreenLayoutItem fieldScreenLayoutItem : fieldScreenLayoutItems) {
                                    String fieldId = fieldScreenLayoutItem.getFieldId();
                                    CustomField customFieldObject = customFieldManager.getCustomFieldObject(fieldId);
                                    Object customFieldValue = mutableIssue.getCustomFieldValue(customFieldObject);
                                    relationIssue.setCustomFieldValue(customFieldObject, customFieldValue);
                                }
                            }
                            ComponentAccessor.getIssueManager().updateIssue(currentUser, relationIssue, EventDispatchOption.ISSUE_UPDATED, false);
                        }
                    }
                    //执行转换
//                    jiraCustomTool.runTransitionByName(mutableIssue, currentUser, transitionName, issueService.newIssueInputParameters());

//                    customToolService.getTransitionByIssue(currentUser.getUsername(), mutableIssue.getId());
                }
            }


//            String multiUserFieldId = String.valueOf(jsonObject.get("multiUser"));
//            String multiUserFieldId2 = String.valueOf(jsonObject.get("multiUser2"));
//            CustomField customField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(multiUserFieldId);
//            CustomField customField2 = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(multiUserFieldId2);
//            if (customField != null && customField2 != null) {
//
//                List<ApplicationUser> applicationUserList = (List<ApplicationUser>) mutableIssue.getCustomFieldValue(customField);
//                List<ApplicationUser> applicationUserList2 = (List<ApplicationUser>) mutableIssue.getCustomFieldValue(customField2);
//                if (!(applicationUserList == null || applicationUserList.isEmpty())) {
//                    List<ApplicationUser> newApplicationUserList = new ArrayList<>();
//
//                    if ((applicationUserList2 == null || applicationUserList2.isEmpty())) {
//                        newApplicationUserList = applicationUserList;
//                        applicationUserList2 = new ArrayList<>();
//                    } else {//筛选掉已经审批过的人
//                        List<String> userNameList = applicationUserList2.stream().map(ApplicationUser::getUsername).collect(Collectors.toList());
//                        newApplicationUserList = applicationUserList.stream().filter(e -> !userNameList.contains(e.getUsername())).collect(Collectors.toList());
//                    }
//                    if (newApplicationUserList.isEmpty()) {
//                        return;//已经全部审批完成
//                    }
//                    mutableIssue.setAssignee(newApplicationUserList.get(0));
//                    applicationUserList2.add(newApplicationUserList.get(0));
////                    applicationUserList.remove(applicationUserList.get(0));
//                    mutableIssue.setCustomFieldValue(customField2, applicationUserList2);
//                }
//            }
        } catch (Exception e) {
            log.error(Utils.errInfo(e));
            throw new WorkflowException(e);
        }
    }
}
