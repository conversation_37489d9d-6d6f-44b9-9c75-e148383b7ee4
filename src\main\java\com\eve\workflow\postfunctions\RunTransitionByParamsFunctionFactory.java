package com.eve.workflow.postfunctions;

import com.alibaba.fastjson.JSONObject;
import com.atlassian.jira.component.ComponentAccessor;
import com.atlassian.jira.config.DefaultStatusManager;
import com.atlassian.jira.config.StatusManager;
import com.atlassian.jira.issue.fields.CustomField;
import com.atlassian.jira.issue.status.Status;
import com.atlassian.jira.plugin.workflow.AbstractWorkflowPluginFactory;
import com.atlassian.jira.plugin.workflow.WorkflowPluginFunctionFactory;
import com.eve.utils.Constant;
import com.opensymphony.workflow.loader.AbstractDescriptor;
import com.opensymphony.workflow.loader.FunctionDescriptor;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2022/7/27
 */
public class RunTransitionByParamsFunctionFactory extends AbstractWorkflowPluginFactory implements WorkflowPluginFunctionFactory {

    private StatusManager statusManager;

    public RunTransitionByParamsFunctionFactory(StatusManager statusManager) {
        this.statusManager = statusManager;
    }

    @Override
    protected void getVelocityParamsForInput(Map<String, Object> map) {
        Collection<Status> allIssueStatusList = statusManager.getStatuses();
//        for (Status status : allIssueStatusList) {
//            String id = status.getId();
//        }
        map.put("copyTypeMap", Constant.copyTypeMap);
        map.put("allIssueStatusList", allIssueStatusList);
    }

    @Override
    protected void getVelocityParamsForEdit(Map<String, Object> map, AbstractDescriptor abstractDescriptor) {
        if (!(abstractDescriptor instanceof FunctionDescriptor)) {
            throw new IllegalArgumentException("Descriptor must be a ConditionDescriptor.");
        }
        Collection<Status> allIssueStatusList = statusManager.getStatuses();
        map.put("copyTypeMap", Constant.copyTypeMap);
        map.put("allIssueStatusList", allIssueStatusList);

        FunctionDescriptor conditionDescriptor = (FunctionDescriptor) abstractDescriptor;
        JSONObject jsonObject = JSONObject.parseObject((String) conditionDescriptor.getArgs().get("parmJson"));
        String issueRelation = String.valueOf(jsonObject.get("issueRelation"));
        List<String> issueStatusList = JSONObject.parseArray(String.valueOf(jsonObject.get("issueStatusList")), String.class);
        String copyCustomFieldOfScreen = String.valueOf(jsonObject.get("copyCustomFieldOfScreen"));
        String transitionName = String.valueOf(jsonObject.get("transitionName"));
        map.put("issueRelation", issueRelation);
        map.put("issueStatusList", issueStatusList);
        map.put("copyCustomFieldOfScreen", copyCustomFieldOfScreen);
        map.put("transitionName", transitionName);
    }

    @Override
    protected void getVelocityParamsForView(Map<String, Object> map, AbstractDescriptor abstractDescriptor) {
        if (!(abstractDescriptor instanceof FunctionDescriptor)) {
            throw new IllegalArgumentException("Descriptor must be a ConditionDescriptor.");
        }
        FunctionDescriptor conditionDescriptor = (FunctionDescriptor) abstractDescriptor;
        JSONObject jsonObject = JSONObject.parseObject((String) conditionDescriptor.getArgs().get("parmJson"));
        String issueRelation = String.valueOf(jsonObject.get("issueRelation"));
        List<String> issueStatusList = JSONObject.parseArray(String.valueOf(jsonObject.get("issueStatusList")), String.class);
        String copyCustomFieldOfScreen = String.valueOf(jsonObject.get("copyCustomFieldOfScreen"));
        String transitionName = String.valueOf(jsonObject.get("transitionName"));
        if (issueRelation != null) {
            map.put("issueRelation", issueRelation);
        }
        map.put("issueStatusList", issueStatusList);
        map.put("copyCustomFieldOfScreen", copyCustomFieldOfScreen);
        if (transitionName != null) {
            map.put("transitionName", transitionName);
        }
    }

    @Override
    public Map<String, ?> getDescriptorParams(Map<String, Object> map) {
        Map<String, Object> hashMap = new HashMap<>();
        try {
            String[] issueRelation = (String[]) map.get("issueRelation");
            String[] issueStatusList = (String[]) map.get("issueStatusList");
            String[] copyCustomFieldOfScreen = (String[]) map.get("copyCustomFieldOfScreen");
            String[] transitionName = (String[]) map.get("transitionName");
            JSONObject resp = new JSONObject();
            resp.put("issueRelation", issueRelation[0]);
            resp.put("issueStatusList", issueStatusList);
            resp.put("copyCustomFieldOfScreen", copyCustomFieldOfScreen[0]);
            resp.put("transitionName", transitionName[0]);
            hashMap.put("parmJson", resp.toJSONString());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return hashMap;
    }
}
