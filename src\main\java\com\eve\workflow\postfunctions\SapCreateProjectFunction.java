package com.eve.workflow.postfunctions;

import com.alibaba.fastjson.JSON;
import com.atlassian.jira.component.ComponentAccessor;
import com.atlassian.jira.issue.MutableIssue;
import com.atlassian.jira.issue.fields.CustomField;
import com.atlassian.jira.user.ApplicationUser;
import com.eve.beans.CostCenterBean;
import com.eve.beans.InnerOrderTypeBean;
import com.eve.utils.Constant;
import com.eve.utils.HttpUtils;
import com.opensymphony.module.propertyset.PropertySet;
import com.opensymphony.workflow.InvalidInputException;
import com.opensymphony.workflow.WorkflowException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/9/2
 */
public class SapCreateProjectFunction extends JsuWorkflowFunction {
    private static final Logger log = LoggerFactory.getLogger(SapCreateProjectFunction.class);
    @Override
    public void doIt(Map transientVars, Map args, PropertySet ps) throws WorkflowException {
        MutableIssue mutableIssue = super.getIssue(transientVars);
        ApplicationUser reporter = mutableIssue.getReporter();
        try {
            CustomField innerOrderTypeField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(Constant.innerOrderType);
            CustomField costCenterField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(Constant.costCenter);
            CustomField developmentBudgetField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(Constant.developmentBudgetCustomFieldId);
            CustomField projectNameField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(Constant.projectNameCustomFieldId);
            CustomField innerOrderCodeField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(Constant.innerOrderCodeCustomFieldId);

            List<InnerOrderTypeBean> innerOrderTypeBeanList = (List<InnerOrderTypeBean>) mutableIssue.getCustomFieldValue(innerOrderTypeField);
            List<CostCenterBean> costCenterBeanList = (List<CostCenterBean>) mutableIssue.getCustomFieldValue(costCenterField);
            Double developmentBudget = (Double) mutableIssue.getCustomFieldValue(developmentBudgetField);
            String projectName = (String) mutableIssue.getCustomFieldValue(projectNameField);
            if (innerOrderTypeBeanList == null && costCenterBeanList == null) {
                return;
            }
            if (innerOrderTypeBeanList == null) {
                throw new WorkflowException("请选择内部订单类型");
            }
            if (costCenterBeanList == null) {
                throw new WorkflowException("请选择成本中心");
            }
            String orderTypeCode = innerOrderTypeBeanList.get(0).getOrderTypeCode();
            String bukrs = costCenterBeanList.get(0).getBUKRS();
            String kostl = costCenterBeanList.get(0).getKOSTL();
            String lrzxdm = costCenterBeanList.get(0).getLRZXDM();

            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH-mm-ss");
            String dateStr = sdf.format(new Date());

            Map<String, String> dataMap = new HashMap<>();
            dataMap.put("IV_AUFART", orderTypeCode);//订单类型
            dataMap.put("IV_BUKRS", bukrs);//公司代码
            dataMap.put("IV_AUFUSER5", dateStr);//申请日期
            dataMap.put("IV_AUFUSER6", "动力电池");//部门
            dataMap.put("IV_KOSTL", kostl);//成本中心
            dataMap.put("IV_AUFEX", mutableIssue.getKey());//外部订单编号 OA单据编号-Jira问题关键字代替
            dataMap.put("IV_AUFUSER4", String.valueOf(developmentBudget*10000));//估算成本金额  预算
            dataMap.put("IV_AUFUSER2", reporter.getName());//负责人工号
            dataMap.put("IV_PRCTR", lrzxdm);//利润中心代码
            dataMap.put("IV_AUFUSER0", reporter.getName());//申请人工号
            dataMap.put("IV_AUFTEXT", projectName);//项目名称

            Map<String, String> headers = new HashMap<>();
            headers.put("appKey", Constant.sapAppKey);
            headers.put("Content-Type", MediaType.APPLICATION_JSON_UTF8_VALUE);
            String data = JSON.toJSONString(dataMap);
            log.error("sap立项调用参数：" + data);
            String callback = HttpUtils.doPostBackLog(Constant.createProjectInterface, data, headers);
            log.error("sap立项调用结果：" + callback);
            Map<String, Object> map = new HashMap<String, Object>();
            map = JSON.parseObject(callback);
            if ("S".equals(map.get("EV_FLAG"))) {
                mutableIssue.setCustomFieldValue(innerOrderCodeField,map.get("EV_AUFNR"));
            }else {
                throw new InvalidInputException("SAP创建项目失败");
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new WorkflowException(e);
        }
    }
}
