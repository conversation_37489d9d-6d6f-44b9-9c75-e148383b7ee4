package com.eve.workflow.postfunctions;

import com.alibaba.fastjson.JSONObject;
import com.atlassian.jira.component.ComponentAccessor;
import com.atlassian.jira.issue.MutableIssue;
import com.atlassian.jira.issue.fields.CustomField;
import com.atlassian.jira.user.ApplicationUser;
import com.eve.utils.Utils;
import com.opensymphony.module.propertyset.PropertySet;
import com.opensymphony.workflow.WorkflowException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/7/27
 */
public class SelectFieldToMultiApprovalFunction extends JsuWorkflowFunction {
    private static final Logger log = LoggerFactory.getLogger(SelectFieldToMultiApprovalFunction.class);
    @Override
    public void doIt(Map transientVars, Map args, PropertySet ps) throws WorkflowException {
        try {
            MutableIssue mutableIssue = super.getIssue(transientVars);
            JSONObject jsonObject = JSONObject.parseObject((String) args.get("parmJson"));
            String multiUserFieldId = String.valueOf(jsonObject.get("multiUser"));
            String multiUserFieldId2 = String.valueOf(jsonObject.get("multiUser2"));
            CustomField customField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(multiUserFieldId);
            CustomField customField2 = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(multiUserFieldId2);
            if (customField != null && customField2 != null) {

                List<ApplicationUser> applicationUserList = (List<ApplicationUser>) mutableIssue.getCustomFieldValue(customField);
                List<ApplicationUser> applicationUserList2 = (List<ApplicationUser>) mutableIssue.getCustomFieldValue(customField2);
                if (!(applicationUserList == null || applicationUserList.isEmpty())) {
                    List<ApplicationUser> newApplicationUserList = new ArrayList<>();

                    if ((applicationUserList2 == null || applicationUserList2.isEmpty())) {
                        newApplicationUserList = applicationUserList;
                        applicationUserList2 = new ArrayList<>();
                    } else {//筛选掉已经审批过的人
                        List<String> userNameList = applicationUserList2.stream().map(ApplicationUser::getUsername).collect(Collectors.toList());
                        newApplicationUserList = applicationUserList.stream().filter(e -> !userNameList.contains(e.getUsername())).collect(Collectors.toList());
                    }
                    if (newApplicationUserList.isEmpty()) {
                        return;//已经全部审批完成
                    }
                    mutableIssue.setAssignee(newApplicationUserList.get(0));
                    applicationUserList2.add(newApplicationUserList.get(0));
//                    applicationUserList.remove(applicationUserList.get(0));
                    mutableIssue.setCustomFieldValue(customField2, applicationUserList2);
                }
            }
        } catch (Exception e) {
            log.error(Utils.errInfo(e));
            throw new WorkflowException(e);
        }
    }
}
