package com.eve.workflow.postfunctions;

import com.alibaba.fastjson.JSONObject;
import com.atlassian.jira.component.ComponentAccessor;
import com.atlassian.jira.issue.fields.CustomField;
import com.atlassian.jira.plugin.workflow.AbstractWorkflowPluginFactory;
import com.atlassian.jira.plugin.workflow.WorkflowPluginFunctionFactory;
import com.eve.utils.Constant;
import com.opensymphony.workflow.loader.AbstractDescriptor;
import com.opensymphony.workflow.loader.FunctionDescriptor;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/7/27
 */
public class SelectFieldToMultiApprovalFunctionFactory extends AbstractWorkflowPluginFactory implements WorkflowPluginFunctionFactory {
    @Override
    protected void getVelocityParamsForInput(Map<String, Object> map) {
        List<CustomField> customFieldList = ComponentAccessor.getCustomFieldManager().getCustomFieldObjects();//获取全部字段
        List<CustomField> multiUserPickerCustomFieldList = new ArrayList();
        for (CustomField customField:customFieldList){
            if (Constant.multiUserPickerFieldType.equals(customField.getCustomFieldType().getKey())){
                multiUserPickerCustomFieldList.add(customField);
            }
        }
        map.put("customFieldList", multiUserPickerCustomFieldList);
    }

    @Override
    protected void getVelocityParamsForEdit(Map<String, Object> map, AbstractDescriptor abstractDescriptor) {
        if (!(abstractDescriptor instanceof FunctionDescriptor)) {
            throw new IllegalArgumentException("Descriptor must be a ConditionDescriptor.");
        }
        List<CustomField> customFieldList = ComponentAccessor.getCustomFieldManager().getCustomFieldObjects();//获取全部字段
        List<CustomField> multiUserPickerCustomFieldList = new ArrayList();
        for (CustomField customField:customFieldList){
            if (Constant.multiUserPickerFieldType.equals(customField.getCustomFieldType().getKey())){
                multiUserPickerCustomFieldList.add(customField);
            }
        }
        map.put("customFieldList", multiUserPickerCustomFieldList);

        FunctionDescriptor conditionDescriptor = (FunctionDescriptor) abstractDescriptor;
        JSONObject jsonObject = JSONObject.parseObject((String) conditionDescriptor.getArgs().get("parmJson"));
        String multiUser = String.valueOf(jsonObject.get("multiUser"));
        String multiUser2 = String.valueOf(jsonObject.get("multiUser2"));
        map.put("multiUser", multiUser);
        map.put("multiUser2", multiUser2);
    }

    @Override
    protected void getVelocityParamsForView(Map<String, Object> map, AbstractDescriptor abstractDescriptor) {
        if (!(abstractDescriptor instanceof FunctionDescriptor)) {
            throw new IllegalArgumentException("Descriptor must be a ConditionDescriptor.");
        }
        FunctionDescriptor conditionDescriptor = (FunctionDescriptor) abstractDescriptor;
        JSONObject jsonObject = JSONObject.parseObject((String) conditionDescriptor.getArgs().get("parmJson"));
        String multiUserFieldId = String.valueOf(jsonObject.get("multiUser"));
        String multiUserFieldId2 = String.valueOf(jsonObject.get("multiUser2"));
        CustomField customField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(multiUserFieldId);
        if (customField != null) {
            map.put("multiUser", customField.getFieldName());
        }
        CustomField customField2 = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(multiUserFieldId2);
        if (customField2 != null) {
            map.put("multiUser2", customField2.getFieldName());
        }
    }

    @Override
    public Map<String, ?> getDescriptorParams(Map<String, Object> map) {
        Map<String, Object> hashMap = new HashMap<>();
        try {
            String[] multiUser = (String[]) map.get("multiUser");
            String[] multiUser2 = (String[]) map.get("multiUser2");
            JSONObject resp = new JSONObject();
            resp.put("multiUser", multiUser[0]);
            resp.put("multiUser2", multiUser2[0]);
            hashMap.put("parmJson", resp.toJSONString());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return hashMap;
    }
}
