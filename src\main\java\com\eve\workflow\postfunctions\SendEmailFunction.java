package com.eve.workflow.postfunctions;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.atlassian.jira.component.ComponentAccessor;
import com.atlassian.jira.issue.MutableIssue;
import com.atlassian.jira.mail.MailService;
import com.atlassian.jira.security.xsrf.XsrfTokenGenerator;
import com.atlassian.jira.user.ApplicationUser;
import com.atlassian.jira.workflow.JiraWorkflow;
import com.eve.utils.JiraCustomTool;
import com.opensymphony.module.propertyset.PropertySet;
import com.opensymphony.workflow.WorkflowException;
import com.opensymphony.workflow.loader.ActionDescriptor;
import com.opensymphony.workflow.loader.WorkflowDescriptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/12/9
 */
public class SendEmailFunction extends JsuWorkflowFunction {
    private JiraCustomTool jiraCustomTool;
    private MailService mailService;
    private static final Logger log = LoggerFactory.getLogger(SendEmailFunction.class);

    public SendEmailFunction(JiraCustomTool jiraCustomTool, MailService mailService) {
        this.jiraCustomTool = jiraCustomTool;
        this.mailService = mailService;
    }

    @Override
    public void doIt(Map transientVars, Map args, PropertySet ps) throws WorkflowException {
        MutableIssue mutableIssue = super.getIssue(transientVars);
        try{
            String fieldSignJson = String.valueOf(args.get("parmJson"));
            JSONObject jsonObject = JSON.parseObject(fieldSignJson);
            List<String> sendFields = JSON.parseArray(String.valueOf(jsonObject.get("sendFields")), String.class);
            List<String> sendUserFields = JSON.parseArray(String.valueOf(jsonObject.get("sendUserFields")), String.class);
            String emailTitle = String.valueOf(jsonObject.get("emailTitle"));

            String jqlConditionEnabled = String.valueOf(jsonObject.get("jqlConditionEnabled"));
            String jqlCondition = String.valueOf(jsonObject.get("jqlCondition"));
            //需要通过jql校验才执行
            ApplicationUser currentUser = ComponentAccessor.getJiraAuthenticationContext().getLoggedInUser();
            if ("true".equals(jqlConditionEnabled) && !jiraCustomTool.matchJql(mutableIssue, jqlCondition, currentUser)) {
                return;//jql条件激活且不满足jql条件，不执行该功能
            }
            //发送邮件
//            jiraCustomTool.sendEmail(mutableIssue, emailTitle, sendUserFields, sendFields);
            JiraWorkflow workflow = ComponentAccessor.getWorkflowManager().getWorkflow(mutableIssue);
            WorkflowDescriptor descriptor = workflow.getDescriptor();
            ActionDescriptor actionDescriptor = descriptor.getAction(21);
            Map<String, Object> metaAttributes = actionDescriptor.getMetaAttributes();
            String fieldScreenId = (String) metaAttributes.get("jira.fieldscreen.id");

            String view = actionDescriptor.getView();
            XsrfTokenGenerator xsrfTokenGenerator = ComponentAccessor.getComponent(XsrfTokenGenerator.class);
            String token = xsrfTokenGenerator.generateToken();

            log.error("后处理发送邮件发送已完成");
        } catch (Exception e) {
            throw new WorkflowException("SendEmailException: ", e);
        }
    }
}
