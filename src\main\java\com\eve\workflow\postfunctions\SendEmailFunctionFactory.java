package com.eve.workflow.postfunctions;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.atlassian.jira.component.ComponentAccessor;
import com.atlassian.jira.issue.customfields.CustomFieldType;
import com.atlassian.jira.issue.fields.CustomField;
import com.atlassian.jira.issue.issuetype.IssueType;
import com.atlassian.jira.plugin.workflow.AbstractWorkflowPluginFactory;
import com.atlassian.jira.plugin.workflow.WorkflowPluginFunctionFactory;
import com.atlassian.jira.user.ApplicationUser;
import com.eve.beans.CopyFieldBean;
import com.eve.utils.Constant;
import com.opensymphony.workflow.loader.AbstractDescriptor;
import com.opensymphony.workflow.loader.FunctionDescriptor;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/12/9
 */
public class SendEmailFunctionFactory extends AbstractWorkflowPluginFactory implements WorkflowPluginFunctionFactory {
    @Override
    protected void getVelocityParamsForInput(Map<String, Object> map) {
        List<CustomField> customFieldList = ComponentAccessor.getCustomFieldManager().getCustomFieldObjects();//获取全部字段
        List<CustomField> collect = customFieldList.stream().filter(e -> Constant.userPickerFieldType.equals(e.getCustomFieldType().getKey())).collect(Collectors.toList());

        List<CopyFieldBean> userCustomFieldList = new ArrayList<>(Constant.assigneeAndReporter);
        for (CustomField customField:customFieldList){
            if (Constant.userPickerFieldType.equals(customField.getCustomFieldType().getKey())){
                CopyFieldBean copyFieldBean = new CopyFieldBean();
                copyFieldBean.setId(customField.getId());
                copyFieldBean.setName(customField.getName());
                userCustomFieldList.add(copyFieldBean);//用户单选字段
            }
        }
        map.put("customFieldList", customFieldList);
        map.put("userPickerCustomFieldList", userCustomFieldList);
    }

    @Override
    protected void getVelocityParamsForEdit(Map<String, Object> map, AbstractDescriptor abstractDescriptor) {
        List<CustomField> customFieldList = ComponentAccessor.getCustomFieldManager().getCustomFieldObjects();//获取全部字段
        List<CustomField> collect = customFieldList.stream().filter(e -> Constant.userPickerFieldType.equals(e.getCustomFieldType().getKey())).collect(Collectors.toList());

        List<CopyFieldBean> userCustomFieldList = new ArrayList<>(Constant.assigneeAndReporter);
        for (CustomField customField:customFieldList){
            if (Constant.userPickerFieldType.equals(customField.getCustomFieldType().getKey())){
                CopyFieldBean copyFieldBean = new CopyFieldBean();
                copyFieldBean.setId(customField.getId());
                copyFieldBean.setName(customField.getName());
                userCustomFieldList.add(copyFieldBean);//用户单选字段
            }
        }
        map.put("customFieldList", customFieldList);
        map.put("userPickerCustomFieldList", userCustomFieldList);

        //参数
        FunctionDescriptor conditionDescriptor = (FunctionDescriptor) abstractDescriptor;
        JSONObject jsonObject = JSON.parseObject((String) conditionDescriptor.getArgs().get("parmJson"));

        List<String> sendFields = JSON.parseArray(String.valueOf(jsonObject.get("sendFields")), String.class);
        List<String> sendUserFields = JSON.parseArray(String.valueOf(jsonObject.get("sendUserFields")), String.class);
        String emailTitle = String.valueOf(jsonObject.get("emailTitle"));
        String jqlConditionEnabled = String.valueOf(jsonObject.get("jqlConditionEnabled"));
        String jqlCondition = String.valueOf(jsonObject.get("jqlCondition"));

        map.put("sendFields", sendFields);
        map.put("sendUserFields", sendUserFields);
        map.put("emailTitle", emailTitle);
        map.put("jqlConditionEnabled", jqlConditionEnabled);
        map.put("jqlCondition", jqlCondition);
    }

    @Override
    protected void getVelocityParamsForView(Map<String, Object> map, AbstractDescriptor abstractDescriptor) {
        if (!(abstractDescriptor instanceof FunctionDescriptor)) {
            throw new IllegalArgumentException("Descriptor must be a FunctionDescriptor.");
        }
        FunctionDescriptor conditionDescriptor = (FunctionDescriptor) abstractDescriptor;
        JSONObject jsonObject = JSON.parseObject((String) conditionDescriptor.getArgs().get("parmJson"));

        List<String> sendFields = JSON.parseArray(String.valueOf(jsonObject.get("sendFields")), String.class);
        List<String> sendUserFields = JSON.parseArray(String.valueOf(jsonObject.get("sendUserFields")), String.class);
        String emailTitle = String.valueOf(jsonObject.get("emailTitle"));
        String jqlConditionEnabled = String.valueOf(jsonObject.get("jqlConditionEnabled"));
        String jqlCondition = String.valueOf(jsonObject.get("jqlCondition"));

        map.put("sendFields", sendFields);
        map.put("sendUserFields", sendUserFields);
        map.put("emailTitle", emailTitle);
        map.put("jqlConditionEnabled", jqlConditionEnabled);
        map.put("jqlCondition", jqlCondition);
        map.put("parmJson", jsonObject);
    }

    @Override
    public Map<String, ?> getDescriptorParams(Map<String, Object> map) {
        Map<String,Object> hashMap = new HashMap<>();
        try {
            String[] sendFields = (String[]) map.get("sendFields");
            String[] sendUserFields = (String[]) map.get("sendUserFields");
            String[] emailTitle = (String[]) map.get("emailTitle");
            String[] jqlConditionEnabled = (String[]) map.get("jqlConditionEnabled");
            String[] jqlCondition = (String[]) map.get("jqlCondition");
            JSONObject resp = new JSONObject();
            resp.put("sendFields", sendFields);
            resp.put("sendUserFields", sendUserFields);
            resp.put("emailTitle", emailTitle[0]);
            resp.put("jqlConditionEnabled", jqlConditionEnabled[0]);
            resp.put("jqlCondition", jqlCondition[0]);
            hashMap.put("parmJson", resp.toJSONString());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return hashMap;
    }
}
