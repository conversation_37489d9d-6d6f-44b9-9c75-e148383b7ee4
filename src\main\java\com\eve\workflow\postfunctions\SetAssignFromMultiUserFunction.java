package com.eve.workflow.postfunctions;

import com.alibaba.fastjson.JSONObject;
import com.atlassian.jira.component.ComponentAccessor;
import com.atlassian.jira.issue.Issue;
import com.atlassian.jira.issue.MutableIssue;
import com.atlassian.jira.issue.fields.CustomField;
import com.atlassian.jira.user.ApplicationUser;
import com.opensymphony.module.propertyset.PropertySet;
import com.opensymphony.workflow.WorkflowException;
import com.opensymphony.workflow.loader.FunctionDescriptor;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/7/27
 */
public class SetAssignFromMultiUserFunction extends JsuWorkflowFunction {
    @Override
    public void doIt(Map transientVars, Map args, PropertySet ps) throws WorkflowException {
        try {
            MutableIssue mutableIssue = super.getIssue(transientVars);
            JSONObject jsonObject = JSONObject.parseObject((String) args.get("setAssignFromMultiUserJson"));
            String multiUserFieldId = String.valueOf(jsonObject.get("multiUser"));
            CustomField customField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(multiUserFieldId);
            if (customField != null) {
                List<ApplicationUser> applicationUserList = (List<ApplicationUser>) mutableIssue.getCustomFieldValue(customField);
                if (!(applicationUserList == null || applicationUserList.isEmpty())) {
                    mutableIssue.setAssignee(applicationUserList.get(0));
                    applicationUserList.remove(applicationUserList.get(0));
                    mutableIssue.setCustomFieldValue(customField, applicationUserList);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new WorkflowException(e);
        }
    }
}
