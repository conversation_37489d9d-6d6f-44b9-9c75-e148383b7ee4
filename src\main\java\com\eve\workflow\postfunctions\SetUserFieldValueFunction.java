package com.eve.workflow.postfunctions;

import com.alibaba.fastjson.JSONObject;
import com.atlassian.jira.component.ComponentAccessor;
import com.atlassian.jira.issue.MutableIssue;
import com.atlassian.jira.issue.fields.CustomField;
import com.atlassian.jira.user.ApplicationUser;
import com.eve.utils.JiraCustomTool;
import com.opensymphony.module.propertyset.PropertySet;
import com.opensymphony.workflow.WorkflowException;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/7/28
 */
public class SetUserFieldValueFunction extends JsuWorkflowFunction {
    private JiraCustomTool jiraCustomTool;

    public SetUserFieldValueFunction(JiraCustomTool jiraCustomTool) {
        this.jiraCustomTool = jiraCustomTool;
    }

    @Override
    public void doIt(Map transientVars, Map args, PropertySet ps) throws WorkflowException {
        try {
            MutableIssue mutableIssue = super.getIssue(transientVars);
            ApplicationUser currentUser = ComponentAccessor.getJiraAuthenticationContext().getLoggedInUser();
            Integer actionId = (Integer) transientVars.get("actionId");
            JSONObject jsonObject = JSONObject.parseObject((String) args.get("setUserFieldValueJson"));
            String userFieldId = String.valueOf(jsonObject.get("userField"));
            String fieldValue = String.valueOf(jsonObject.get("fieldValue"));
            String jqlConditionEnabled = String.valueOf(jsonObject.get("jqlConditionEnabled"));
            String jqlCondition = String.valueOf(jsonObject.get("jqlCondition"));

            //需要通过jql校验才执行
            if ("true".equals(jqlConditionEnabled) && !jiraCustomTool.matchJql(mutableIssue, jqlCondition, currentUser)) {
                return;
            }
            ApplicationUser applicationUser = ComponentAccessor.getUserManager().getUserByNameEvenWhenUnknown(fieldValue);
            if ("assignee".equals(userFieldId)) {
                if (ComponentAccessor.getUserManager().isUserExisting(applicationUser)) {
                    mutableIssue.setAssignee(applicationUser);
                }
            }else if ("reporter".equals(userFieldId)){
                if (ComponentAccessor.getUserManager().isUserExisting(applicationUser)) {
                    mutableIssue.setReporter(applicationUser);
                }
            } else{
                CustomField customField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(userFieldId);
                if (customField != null && ComponentAccessor.getUserManager().isUserExisting(applicationUser)) {
                    mutableIssue.setCustomFieldValue(customField, applicationUser);
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
            throw new WorkflowException(e);
        }
    }
}
