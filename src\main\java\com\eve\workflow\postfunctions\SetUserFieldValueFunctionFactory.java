package com.eve.workflow.postfunctions;

import com.alibaba.fastjson.JSONObject;
import com.atlassian.jira.component.ComponentAccessor;
import com.atlassian.jira.issue.fields.CustomField;
import com.atlassian.jira.plugin.workflow.AbstractWorkflowPluginFactory;
import com.atlassian.jira.plugin.workflow.WorkflowPluginFunctionFactory;
import com.eve.beans.CopyFieldBean;
import com.eve.utils.Constant;
import com.opensymphony.workflow.loader.AbstractDescriptor;
import com.opensymphony.workflow.loader.FunctionDescriptor;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/7/28
 */
public class SetUserFieldValueFunctionFactory extends AbstractWorkflowPluginFactory implements WorkflowPluginFunctionFactory {
    @Override
    protected void getVelocityParamsForInput(Map<String, Object> map) {
        List<CustomField> customFieldList = ComponentAccessor.getCustomFieldManager().getCustomFieldObjects();//获取全部字段
//        List<CustomField> userPickerCustomFieldList = new ArrayList();
//        for (CustomField customField:customFieldList){
//            if (Constant.userPickerFieldType.equals(customField.getCustomFieldType().getKey())){
//                userPickerCustomFieldList.add(customField);
//            }
//        }
        List<CopyFieldBean> copyFieldBeanList = new ArrayList<>(Constant.assigneeAndReporter);
        for (CustomField customField:customFieldList){
//            CustomFieldType customFieldType = customField.getCustomFieldType();
            if (Constant.userPickerFieldType.equals(customField.getCustomFieldType().getKey())){
                CopyFieldBean copyFieldBean = new CopyFieldBean();
                copyFieldBean.setId(customField.getId());
                copyFieldBean.setName(customField.getFieldName());
                copyFieldBeanList.add(copyFieldBean);//用户单选字段
            }
        }
        map.put("customFieldList", copyFieldBeanList);
    }

    @Override
    protected void getVelocityParamsForEdit(Map<String, Object> map, AbstractDescriptor abstractDescriptor) {
        if (!(abstractDescriptor instanceof FunctionDescriptor)) {
            throw new IllegalArgumentException("Descriptor must be a ConditionDescriptor.");
        }
        List<CustomField> customFieldList = ComponentAccessor.getCustomFieldManager().getCustomFieldObjects();//获取全部字段
//        List<CustomField> multiUserPickerCustomFieldList = new ArrayList();
//        for (CustomField customField:customFieldList){
//            if (Constant.userPickerFieldType.equals(customField.getCustomFieldType().getKey())){
//                multiUserPickerCustomFieldList.add(customField);
//            }
//        }
        List<CopyFieldBean> copyFieldBeanList = new ArrayList<>(Constant.assigneeAndReporter);
        for (CustomField customField:customFieldList){
//            CustomFieldType customFieldType = customField.getCustomFieldType();
            if (Constant.userPickerFieldType.equals(customField.getCustomFieldType().getKey())){
                CopyFieldBean copyFieldBean = new CopyFieldBean();
                copyFieldBean.setId(customField.getId());
                copyFieldBean.setName(customField.getFieldName());
                copyFieldBeanList.add(copyFieldBean);//用户单选字段
            }
        }
        map.put("customFieldList", copyFieldBeanList);

        FunctionDescriptor conditionDescriptor = (FunctionDescriptor) abstractDescriptor;
        JSONObject jsonObject = JSONObject.parseObject((String) conditionDescriptor.getArgs().get("setUserFieldValueJson"));
        String userField = String.valueOf(jsonObject.get("userField"));
        String fieldValue = String.valueOf(jsonObject.get("fieldValue"));
        String jqlConditionEnabled = String.valueOf(jsonObject.get("jqlConditionEnabled"));
        String jqlCondition = String.valueOf(jsonObject.get("jqlCondition"));

        map.put("userField", userField);
        map.put("fieldValue", fieldValue);
        map.put("jqlConditionEnabled", jqlConditionEnabled);
        map.put("jqlCondition", jqlCondition);
    }

    @Override
    protected void getVelocityParamsForView(Map<String, Object> map, AbstractDescriptor abstractDescriptor) {
        if (!(abstractDescriptor instanceof FunctionDescriptor)) {
            throw new IllegalArgumentException("Descriptor must be a ConditionDescriptor.");
        }
        FunctionDescriptor conditionDescriptor = (FunctionDescriptor) abstractDescriptor;
        JSONObject jsonObject = JSONObject.parseObject((String) conditionDescriptor.getArgs().get("setUserFieldValueJson"));
        String userFieldId = String.valueOf(jsonObject.get("userField"));
        String fieldValue = String.valueOf(jsonObject.get("fieldValue"));
        String jqlConditionEnabled = String.valueOf(jsonObject.get("jqlConditionEnabled"));
        String jqlCondition = String.valueOf(jsonObject.get("jqlCondition"));

        map.put("fieldValue", fieldValue);
        map.put("jqlConditionEnabled", jqlConditionEnabled);
        map.put("jqlCondition", jqlCondition);
        CustomField customField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(userFieldId);
        if (customField != null) {
            map.put("userField", customField.getFieldName());
        }else {
            map.put("userField", userFieldId);
        }
    }

    @Override
    public Map<String, ?> getDescriptorParams(Map<String, Object> map) {
        Map<String, Object> hashMap = new HashMap<>();
        try {
            String[] userField = (String[]) map.get("userField");
            String[] fieldValue = (String[]) map.get("fieldValue");
            String[] jqlConditionEnabled = (String[]) map.get("jqlConditionEnabled");
            String[] jqlCondition = (String[]) map.get("jqlCondition");
            JSONObject resp = new JSONObject();
            resp.put("userField", userField[0]);
            resp.put("fieldValue", fieldValue[0]);
            resp.put("jqlConditionEnabled", jqlConditionEnabled[0]);
            resp.put("jqlCondition", jqlCondition[0]);
            hashMap.put("setUserFieldValueJson", resp.toJSONString());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return hashMap;
    }
}
