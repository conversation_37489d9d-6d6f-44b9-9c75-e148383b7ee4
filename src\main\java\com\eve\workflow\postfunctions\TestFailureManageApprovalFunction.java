package com.eve.workflow.postfunctions;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.atlassian.jira.component.ComponentAccessor;
import com.atlassian.jira.component.pico.ComponentManager;
import com.atlassian.jira.config.properties.APKeys;
import com.atlassian.jira.issue.MutableIssue;
import com.atlassian.jira.issue.attachment.Attachment;
import com.atlassian.jira.issue.customfields.option.Option;
import com.atlassian.jira.issue.fields.CustomField;
import com.atlassian.jira.security.xsrf.XsrfTokenGenerator;
import com.atlassian.jira.user.ApplicationUser;
import com.eve.beans.SmartAttachmentCateBean;
import com.eve.utils.Constant;
import com.eve.utils.HttpUtils;
import com.eve.utils.Utils;
import com.opensymphony.module.propertyset.PropertySet;
import com.opensymphony.workflow.WorkflowException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.util.ObjectUtils;

import java.net.URLEncoder;
import java.sql.Timestamp;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2022/9/2
 */
public class TestFailureManageApprovalFunction extends JsuWorkflowFunction {
    private static final Logger log = LoggerFactory.getLogger(TestFailureManageApprovalFunction.class);

    @Autowired
    private XsrfTokenGenerator xsrfTokenGenerator;

    @Override
    public void doIt(Map transientVars, Map args, PropertySet ps) throws WorkflowException {
        MutableIssue mutableIssue = super.getIssue(transientVars);
        ApplicationUser reporter = mutableIssue.getReporter();
        ApplicationUser currentUser = ComponentAccessor.getJiraAuthenticationContext().getLoggedInUser();

        try {
//            CustomField innerOrderTypeField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(Constant.innerOrderType);
//            CustomField costCenterField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(Constant.costCenter);
//            CustomField developmentBudgetField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(Constant.developmentBudgetCustomFieldId);
//            CustomField projectNameField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(Constant.projectNameCustomFieldId);
//            CustomField innerOrderCodeField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(Constant.innerOrderCodeCustomFieldId);
//
//            List<InnerOrderTypeBean> innerOrderTypeBeanList = (List<InnerOrderTypeBean>) mutableIssue.getCustomFieldValue(innerOrderTypeField);
//            List<CostCenterBean> costCenterBeanList = (List<CostCenterBean>) mutableIssue.getCustomFieldValue(costCenterField);
//            Double developmentBudget = (Double) mutableIssue.getCustomFieldValue(developmentBudgetField);
//            String projectName = (String) mutableIssue.getCustomFieldValue(projectNameField);


            CustomField productDesignBusinessIdCustomField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(Constant.businessIdCustomFieldId);
            //todo 责任人、分析截止时间
            CustomField inStoreDateCustomField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(Constant.inStoreDateCustomFieldId);
            CustomField stockLocateCustomField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(Constant.stockLocateCustomFieldId);
            CustomField ourStoreDateCustomField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(Constant.ourStoreDateCustomFieldId);
            CustomField faResponsibleCustomField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(Constant.faResponsibleCustomFieldId);
            CustomField faDueDateCustomField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(Constant.faDueDateCustomFieldId);
            CustomField improveEffectEvaluationCustomField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(Constant.improveEffectEvaluationCustomFieldId);
            CustomField reviewerCustomField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(Constant.reviewerCustomFieldId);
            CustomField approverCustomField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(Constant.approverCustomFieldId);


            String productDesignBusinessId = (String) mutableIssue.getCustomFieldValue(productDesignBusinessIdCustomField);

            Timestamp inStoreDate = (Timestamp) mutableIssue.getCustomFieldValue(inStoreDateCustomField);
            Option stockLocate = (Option) mutableIssue.getCustomFieldValue(stockLocateCustomField);
            Timestamp ourStoreDate = (Timestamp) mutableIssue.getCustomFieldValue(ourStoreDateCustomField);

            ApplicationUser faResponsible = (ApplicationUser) mutableIssue.getCustomFieldValue(faResponsibleCustomField);
            Timestamp faDueDate = (Timestamp) mutableIssue.getCustomFieldValue(faDueDateCustomField);

            ApplicationUser reviewer = (ApplicationUser) mutableIssue.getCustomFieldValue(reviewerCustomField);
            ApplicationUser approver = (ApplicationUser) mutableIssue.getCustomFieldValue(approverCustomField);

            if (productDesignBusinessId == null) {
                throw new IllegalArgumentException("业务ID未维护，请联系管理员！");
            }
            JSONObject parmJson = JSON.parseObject(String.valueOf(args.get("parmJson")));
            String approvalResult = String.valueOf(parmJson == null || parmJson.get("approvalResult") == null ? "10" : parmJson.get("approvalResult"));
            Map<String, Object> approvalResultMap = new HashMap<>();
            approvalResultMap.put("businessId", Long.parseLong(productDesignBusinessId));
            approvalResultMap.put("issueKey", mutableIssue.getKey());
            approvalResultMap.put("issueTypeId", mutableIssue.getIssueTypeId());
            approvalResultMap.put("handleResult", Integer.parseInt(approvalResult));//通过-10 驳回-20

            if (reviewer != null) {
                approvalResultMap.put("reviewer", reviewer.getUsername());
            }
            if (approver != null) {
                approvalResultMap.put("approver", approver.getUsername());
            }
            if (inStoreDate != null) {
                //入库回传信息
                approvalResultMap.put("inStoreDate", Utils.getDateFormat(inStoreDate));
                approvalResultMap.put("stockLocate", stockLocate.getOptionId());
            }
            if (ourStoreDate != null) {
                //出库回传信息
                approvalResultMap.put("ourStoreDate", Utils.getDateFormat(ourStoreDate));
            }

            if (faResponsible != null) {
                //回传FA责任人、FA期限
                approvalResultMap.put("faResponsibleAccount", faResponsible.getUsername());
                approvalResultMap.put("faResponsibleName", faResponsible.getDisplayName());
                approvalResultMap.put("faDueDate", Utils.getDateFormat(faDueDate));
            }
            if (Constant.FAAnalyseReportIssueTypeId.toString().equals(mutableIssue.getIssueTypeId()) && "10".equals(approvalResult)) {
//                XsrfTokenGenerator xsrfTokenGenerator = ComponentManager.getComponentInstanceOfType(XsrfTokenGenerator.class);
//                String token = xsrfTokenGenerator.generateToken();
//                String loginToken = Utils.getLoginToken(currentUser.getUsername());
                approvalResultMap.put("userName",currentUser.getUsername());
                String improveEffectEvaluation = (String) mutableIssue.getCustomFieldValue(improveEffectEvaluationCustomField);
                approvalResultMap.put("improveEffectEvaluation", improveEffectEvaluation);
                //回传拆解报告、分析报告到PBI
                Collection<Attachment> attachments = mutableIssue.getAttachments();
                // 获取类别列表信息
                String baseUrl = ComponentAccessor.getApplicationProperties().getString(APKeys.JIRA_BASEURL);
                Map<String, String> headers = new HashMap<>();
//            headers.put("atlas-authorization", ComponentAccessor.getComponent(JiraAuthenticationContext.class).getLoggedInUser().getUsername());
                headers.put("atlas-authorization", reporter.getUsername());
                String callback = HttpUtils.doGet(
                        baseUrl + "/rest/attach-cat/1.0/attachments?issueKey=" + mutableIssue.getKey(),
                        headers
                );
                log.error("获取附件分类响应：" + callback);
                JSONObject jsonObject = JSON.parseObject(callback);

                List<SmartAttachmentCateBean> smartAttachmentCateBeanList = JSONObject.parseArray(jsonObject.getString("categories"), SmartAttachmentCateBean.class);

                Optional<SmartAttachmentCateBean> faBreakReportFirst = smartAttachmentCateBeanList.stream().filter(e -> e.getName().equals("拆解报告")).findFirst();
                if (faBreakReportFirst.isPresent()) {
                    SmartAttachmentCateBean smartAttachmentCateBean = faBreakReportFirst.get();
                    List<Long> attachmentIdList = smartAttachmentCateBean.getAttachmentIds();
                    if (attachmentIdList.isEmpty()) {
                        List<SmartAttachmentCateBean> documents = smartAttachmentCateBean.getDocuments();
                        if (!ObjectUtils.isEmpty(documents)) {//有成组的同名文件，取最新的文件(文件id最大)
                            documents.stream().map(SmartAttachmentCateBean::getAttachmentIds)
                                    .filter(attachmentIds -> !ObjectUtils.isEmpty(attachmentIds))
                                    .map(attachmentIds -> attachmentIds.stream().max(Comparator.comparing(Long::longValue)).orElse(0L))
                                    .forEach(attachmentIdList::add);
                        }
                    }
                    if (!attachmentIdList.isEmpty()) {
                        Attachment attachment = ComponentAccessor.getAttachmentManager().getAttachment(attachmentIdList.get(0));
                        String attachmentUrl = baseUrl + "/secure/attachment/" + attachment.getId() + "/" + URLEncoder.encode(attachment.getFilename(),"UTF-8");
                        approvalResultMap.put("faBreakReportUrl", attachmentUrl);
                        approvalResultMap.put("faBreakReportName", attachment.getFilename());
                    }
                }

                Optional<SmartAttachmentCateBean> faAnalysisReportFirst = smartAttachmentCateBeanList.stream().filter(e -> e.getName().equals("分析报告")).findFirst();
                if (faAnalysisReportFirst.isPresent()) {
                    SmartAttachmentCateBean smartAttachmentCateBean = faAnalysisReportFirst.get();
                    List<Long> attachmentIdList = smartAttachmentCateBean.getAttachmentIds();
                    if (attachmentIdList.isEmpty()) {
                        List<SmartAttachmentCateBean> documents = smartAttachmentCateBean.getDocuments();
                        if (!ObjectUtils.isEmpty(documents)) {//有成组的同名文件，取最新的文件(文件id最大)
                            documents.stream().map(SmartAttachmentCateBean::getAttachmentIds)
                                    .filter(attachmentIds -> !ObjectUtils.isEmpty(attachmentIds))
                                    .map(attachmentIds -> attachmentIds.stream().max(Comparator.comparing(Long::longValue)).orElse(0L))
                                    .forEach(attachmentIdList::add);
                        }
                    }
                    if (!attachmentIdList.isEmpty()) {
                        Attachment attachment = ComponentAccessor.getAttachmentManager().getAttachment(attachmentIdList.get(0));
                        String attachmentUrl = baseUrl + "/secure/attachment/" + attachment.getId() + "/" + URLEncoder.encode(attachment.getFilename(),"UTF-8");
                        approvalResultMap.put("faAnalysisReportUrl", attachmentUrl);
                        approvalResultMap.put("faAnalysisReportName", attachment.getFilename());
                    }
                }



            }

            String data = JSON.toJSONString(approvalResultMap);

            HashMap<String, String> header = new HashMap<>();
            header.put("Content-Type", MediaType.APPLICATION_JSON_VALUE);
            String backLog = "";
            log.error("测试失效审批推送PBI数据>>{}", data);
            if (Constant.testFailureProjectId.equals(mutableIssue.getProjectId())) {
                backLog = HttpUtils.doPostBackLog(Constant.testFailureApprovalUrl, data, header);
            } else if (Constant.testFailureProjectIdTest.equals(mutableIssue.getProjectId())) {
                backLog = HttpUtils.doPostBackLog(Constant.testFailureApprovalUrlTest, data, header);
            }
            JSONObject jsonObject = JSON.parseObject(backLog);
            Boolean success = jsonObject.getBoolean("success");
            Boolean respData = jsonObject.getBoolean("data");
            if (!(Boolean.TRUE.equals(success)) || !(Boolean.TRUE.equals(respData))) {
                //失败，失败原因添加到评论
                String message = jsonObject.getString("message");
                String exceptionClazz = jsonObject.getString("exceptionClazz");
                String comment = "[~083779] \n" + message + "\n" + exceptionClazz;
                ApplicationUser userByName = ComponentAccessor.getUserManager().getUserByName("083779");
                ComponentAccessor.getCommentManager().create(mutableIssue, userByName, comment, true);
            }

            log.error("测试失效审批推送PBI结果>>{}", backLog);


        } catch (Exception e) {
            log.error(Utils.errInfo(e));
            throw new WorkflowException(e);
        }
    }
}
