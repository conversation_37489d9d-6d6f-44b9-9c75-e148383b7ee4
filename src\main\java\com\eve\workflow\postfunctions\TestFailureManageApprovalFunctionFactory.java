package com.eve.workflow.postfunctions;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.atlassian.jira.plugin.workflow.AbstractWorkflowPluginFactory;
import com.atlassian.jira.plugin.workflow.WorkflowPluginFunctionFactory;
import com.opensymphony.workflow.loader.AbstractDescriptor;
import com.opensymphony.workflow.loader.FunctionDescriptor;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/9/2
 */
public class TestFailureManageApprovalFunctionFactory extends AbstractWorkflowPluginFactory implements WorkflowPluginFunctionFactory {
    @Override
    protected void getVelocityParamsForInput(Map<String, Object> map) {
        map.put("approvalResult","10");
    }

    @Override
    protected void getVelocityParamsForEdit(Map<String, Object> map, AbstractDescriptor abstractDescriptor) {
        FunctionDescriptor conditionDescriptor = (FunctionDescriptor) abstractDescriptor;
        JSONObject jsonObject = JSON.parseObject((String) conditionDescriptor.getArgs().get("parmJson"));

//        String jqlConditionEnabled = String.valueOf(jsonObject.get("jqlConditionEnabled"));
//        String jqlCondition = String.valueOf(jsonObject.get("jqlCondition"));
//
//        map.put("jqlConditionEnabled", jqlConditionEnabled);
//        map.put("jqlCondition", jqlCondition);

//        String businessType = String.valueOf(jsonObject.get("businessType") == null ? "" : jsonObject.get("businessType"));
//        String approvalType = String.valueOf(jsonObject.get("approvalType") == null ? "" : jsonObject.get("approvalType"));
        String approvalResult = String.valueOf(jsonObject == null ||jsonObject.get("approvalResult") == null ? "" : jsonObject.get("approvalResult"));

//        map.put("businessType", businessType);
//        map.put("approvalType", approvalType);
        map.put("approvalResult",approvalResult);

    }

    @Override
    protected void getVelocityParamsForView(Map<String, Object> map, AbstractDescriptor abstractDescriptor) {
        if (!(abstractDescriptor instanceof FunctionDescriptor)) {
            throw new IllegalArgumentException("Descriptor must be a FunctionDescriptor.");
        }
        FunctionDescriptor functionDescriptor = (FunctionDescriptor) abstractDescriptor;
        JSONObject jsonObject = JSON.parseObject((String) functionDescriptor.getArgs().get("parmJson"));

//        String jqlConditionEnabled = String.valueOf(jsonObject.get("jqlConditionEnabled"));
//        String jqlCondition = String.valueOf(jsonObject.get("jqlCondition"));
//
//        map.put("jqlConditionEnabled", jqlConditionEnabled);
//        map.put("jqlCondition", jqlCondition);

//        String businessType = String.valueOf(jsonObject.get("businessType") == null ? "" : jsonObject.get("businessType"));
//        String approvalType = String.valueOf(jsonObject.get("approvalType") == null ? "" : jsonObject.get("approvalType"));
        String approvalResult = String.valueOf(jsonObject == null || jsonObject.get("approvalResult") == null ? "" : jsonObject.get("approvalResult"));

//        map.put("businessType", businessType);
//        map.put("approvalType", approvalType);
        map.put("approvalResult", approvalResult);
        map.put("parmJson", jsonObject == null ? "" : jsonObject.toJSONString());
    }

    @Override
    public Map<String, ?> getDescriptorParams(Map<String, Object> map) {
        Map<String,Object> hashMap = new HashMap<>();
        try{
//            String[] businessType = (String[]) map.get("businessType");
//            String[] approvalType = (String[]) map.get("approvalType");
            String[] approvalResult = (String[]) map.get("approvalResult");
//            String[] jqlConditionEnabled = (String[]) map.get("jqlConditionEnabled");
//            String[] jqlCondition = (String[]) map.get("jqlCondition");

            JSONObject jsonObject = new JSONObject();
//            jsonObject.put("businessType",businessType[0]);
//            jsonObject.put("approvalType",approvalType[0]);
            jsonObject.put("approvalResult",approvalResult[0]);
//            jsonObject.put("jqlConditionEnabled", jqlConditionEnabled[0]);
//            jsonObject.put("jqlCondition", jqlCondition[0]);
            hashMap.put("parmJson", jsonObject.toJSONString());

        }catch (Exception e){
            e.printStackTrace();
        }
        return hashMap;
    }
}
