package com.eve.workflow.postfunctions;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.atlassian.jira.component.ComponentAccessor;
import com.atlassian.jira.event.type.EventDispatchOption;
import com.atlassian.jira.issue.MutableIssue;
import com.atlassian.jira.issue.fields.CustomField;
import com.atlassian.jira.plugin.jql.function.CurrentUserFunction;
import com.atlassian.jira.security.xsrf.XsrfTokenGenerator;
import com.atlassian.jira.user.ApplicationUser;
import com.eve.utils.JiraCustomTool;
import com.eve.utils.Utils;
import com.opensymphony.module.propertyset.PropertySet;
import com.opensymphony.workflow.WorkflowException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;

import java.sql.Timestamp;
import java.util.Date;
import java.util.Map;

public class UpdateIssueDueFunction extends JsuWorkflowFunction{

//    long yuanId =11809L;
            //10101L;//测试环境的日期
    //name = 计划第四阶段总结汇报日期  id=11809
    private JiraCustomTool jiraCustomTool;

    public UpdateIssueDueFunction(JiraCustomTool jiraCustomTool) {
        this.jiraCustomTool = jiraCustomTool;
    }

    @Override
    public void doIt(Map transientVars, Map args, PropertySet ps) throws WorkflowException {
        try {
            MutableIssue mutableIssue =super.getIssue(transientVars);
            ApplicationUser currentUser = ComponentAccessor.getJiraAuthenticationContext().getLoggedInUser();
            String fieldSignJson = String.valueOf(args.get("fieldSignJson"));

            JSONObject jsonObject = JSON.parseObject(fieldSignJson);
            String jqlConditionEnabled = String.valueOf(jsonObject.get("jqlConditionEnabled"));
            String jqlCondition = String.valueOf(jsonObject.get("jqlCondition"));

            //需要通过jql校验才执行
            if ("true".equals(jqlConditionEnabled) && !jiraCustomTool.matchJql(mutableIssue, jqlCondition, currentUser)) {
                return;
            }
            Long customFieldId = Long.valueOf(String.valueOf(jsonObject.get("signFieldId")));
            Object offsetDayString = jsonObject.get("offsetDay");
            double offsetDay = Double.parseDouble(ObjectUtils.isEmpty(offsetDayString) ? "0" : String.valueOf(offsetDayString));
            CustomField yuanCustomField = ComponentAccessor
                                            .getCustomFieldManager()
                                            .getCustomFieldObject(customFieldId);
            if (yuanCustomField == null){
                return;
            }
            Timestamp yuanValue = (Timestamp)mutableIssue.getCustomFieldValue(yuanCustomField);
            if (yuanValue == null) {
                return;
            }
            //yuanValue添加offsetDay天
            Date date = DateUtil.offsetDay(yuanValue, (int) Math.round(offsetDay));
            yuanValue = new Timestamp(date.getTime());

            mutableIssue.setDueDate(yuanValue);
//            ApplicationUser currentUser = ComponentAccessor.getJiraAuthenticationContext().getLoggedInUser();
//            ComponentAccessor.getIssueManager().updateIssue(currentUser, mutableIssue, EventDispatchOption.ISSUE_UPDATED, false);
        } catch (Exception e) {
//            Utils.errInfo(e);
            throw new WorkflowException(e);
        }
    }
}
