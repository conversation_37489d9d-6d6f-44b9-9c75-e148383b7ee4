package com.eve.workflow.postfunctions;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.atlassian.jira.component.ComponentAccessor;
import com.atlassian.jira.issue.fields.CustomField;
import com.atlassian.jira.plugin.workflow.AbstractWorkflowPluginFactory;
import com.atlassian.jira.plugin.workflow.WorkflowPluginFunctionFactory;
import com.eve.beans.OptionBean;
import com.eve.services.ServiceUpdateDue;
import com.opensymphony.workflow.loader.AbstractDescriptor;
import com.opensymphony.workflow.loader.FunctionDescriptor;
import org.springframework.util.ObjectUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2022/2/26 16:22
 */
public class UpdateIssueDueFunctionFactory extends AbstractWorkflowPluginFactory implements WorkflowPluginFunctionFactory {

    private ServiceUpdateDue serviceUpdateDue;

    public UpdateIssueDueFunctionFactory(ServiceUpdateDue serviceUpdateDue) {
        this.serviceUpdateDue = serviceUpdateDue;
    }
//给前端查询字段值
    @Override
    protected void getVelocityParamsForInput(Map<String, Object> map) {
        List<OptionBean> optionBeanList = serviceUpdateDue.queryOptionBeanList();
        map.put("optionBeanList",optionBeanList);
    }
//编辑时
    @Override
    protected void getVelocityParamsForEdit(Map<String, Object> map, AbstractDescriptor descriptor) {
        FunctionDescriptor conditionDescriptor = (FunctionDescriptor) descriptor;
        JSONObject jsonObject = JSON.parseObject((String) conditionDescriptor.getArgs().get("fieldSignJson"));

        String jqlConditionEnabled = String.valueOf(jsonObject.get("jqlConditionEnabled"));
        String jqlCondition = String.valueOf(jsonObject.get("jqlCondition"));

        map.put("jqlConditionEnabled", jqlConditionEnabled);
        map.put("jqlCondition", jqlCondition);

        String signFieldId = String.valueOf(jsonObject.get("signFieldId"));
        String fieldType = String.valueOf(jsonObject.get("fieldType"));
        String offsetDay = String.valueOf(jsonObject.get("offsetDay"));

        map.put("signFieldId", signFieldId);
        map.put("fieldType",fieldType);
        map.put("offsetDay", offsetDay);

        List<OptionBean> optionBeanList = serviceUpdateDue.queryOptionBeanList();
        map.put("optionBeanList",optionBeanList);

        map.put("fieldName", ComponentAccessor.getCustomFieldManager().getCustomFieldObject(Long.valueOf(signFieldId)).getFieldName());


        getVelocityParamsForInput(map);
        getVelocityParamsForView(map, descriptor);
    }
//展示字段的值
    @Override
    protected void getVelocityParamsForView(Map<String, Object> map, AbstractDescriptor descriptor) {
        if (!(descriptor instanceof FunctionDescriptor)) {
            throw new IllegalArgumentException("Descriptor must be a FunctionDescriptor.");
        }
        FunctionDescriptor conditionDescriptor = (FunctionDescriptor) descriptor;
        JSONObject jsonObject = JSON.parseObject((String) conditionDescriptor.getArgs().get("fieldSignJson"));

        String jqlConditionEnabled = String.valueOf(jsonObject.get("jqlConditionEnabled"));
        String jqlCondition = String.valueOf(jsonObject.get("jqlCondition"));

        map.put("jqlConditionEnabled", jqlConditionEnabled);
        map.put("jqlCondition", jqlCondition);

        String signFieldId = String.valueOf(jsonObject.get("signFieldId"));
        Object offsetDay = jsonObject.get("offsetDay");
        CustomField customField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(Long.valueOf(signFieldId));

        map.put("fieldName", customField == null ? "字段已删除" : customField.getFieldName());
        map.put("offsetDay", ObjectUtils.isEmpty(offsetDay) ? "0" : String.valueOf(offsetDay));
        map.put("specialSignJson",jsonObject.toJSONString());
    }
// 获取前端的值
    @Override
    public Map<String, ?> getDescriptorParams(Map<String, Object> map) {

        Map<String,Object> hashMap = new HashMap<>();
        try{
            String[] signFieldId = (String[]) map.get("sign_field");
            String[] fieldType = (String[]) map.get("field_type");
            String[] offsetDay = (String[]) map.get("offsetDay");
            String[] jqlConditionEnabled = (String[]) map.get("jqlConditionEnabled");
            String[] jqlCondition = (String[]) map.get("jqlCondition");

            JSONObject jsonObject = new JSONObject();
            jsonObject.put("signFieldId",signFieldId[0]);
            jsonObject.put("fieldType",fieldType[0]);
            jsonObject.put("offsetDay", ObjectUtils.isEmpty(offsetDay[0]) ? "0" : offsetDay[0]);
            jsonObject.put("jqlConditionEnabled", jqlConditionEnabled[0]);
            jsonObject.put("jqlCondition", jqlCondition[0]);
            hashMap.put("fieldSignJson", jsonObject.toJSONString());

        }catch (Exception e){
            e.printStackTrace();
        }
        return hashMap;

    }
}
