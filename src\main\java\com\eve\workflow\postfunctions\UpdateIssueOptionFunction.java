package com.eve.workflow.postfunctions;

import com.alibaba.fastjson.JSONObject;
import com.atlassian.jira.component.ComponentAccessor;
import com.atlassian.jira.event.type.EventDispatchOption;
import com.atlassian.jira.issue.MutableIssue;
import com.atlassian.jira.issue.context.JiraContextNode;
import com.atlassian.jira.issue.context.ProjectContext;
import com.atlassian.jira.issue.customfields.option.Option;
import com.atlassian.jira.issue.customfields.option.Options;
import com.atlassian.jira.issue.fields.CustomField;
import com.atlassian.jira.project.Project;
import com.atlassian.jira.user.ApplicationUser;
import com.eve.utils.Constant;
import com.eve.utils.JiraCustomTool;
import com.opensymphony.module.propertyset.PropertySet;
import com.opensymphony.workflow.WorkflowException;

import java.sql.Timestamp;
import java.util.List;
import java.util.Map;
import java.util.Objects;

public class UpdateIssueOptionFunction extends JsuWorkflowFunction{
    private JiraCustomTool jiraCustomTool;
    com.atlassian.jira.bc.issue.IssueService issueService = ComponentAccessor.getIssueService();

    public UpdateIssueOptionFunction(JiraCustomTool jiraCustomTool) {
        this.jiraCustomTool = jiraCustomTool;
    }

    @Override
    public void doIt(Map transientVars, Map args, PropertySet ps) throws WorkflowException {
        try {
            MutableIssue mutableIssue =super.getIssue(transientVars);
            ApplicationUser currentUser = ComponentAccessor.getJiraAuthenticationContext().getLoggedInUser();
            String fieldSignJson = String.valueOf(args.get("optionsJson"));

            JSONObject jsonObject = JSONObject.parseObject(fieldSignJson);

            Long customFieldId = Long.valueOf(String.valueOf(jsonObject.get("field_field")));
            String fieldValueId = (String.valueOf(jsonObject.get("field_value")));

            String jqlConditionEnabled = String.valueOf(jsonObject.get("jqlConditionEnabled"));
            String jqlCondition = String.valueOf(jsonObject.get("jqlCondition"));

            //需要通过jql校验才执行
            if ("true".equals(jqlConditionEnabled) && !jiraCustomTool.matchJql(mutableIssue, jqlCondition, currentUser)) {
                return;
            }
            CustomField yuanCustomField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(customFieldId);

            //要更新的自定义字段ID
            if (yuanCustomField == null||fieldValueId==null){
                return;
            }
            JiraContextNode jiraContextNode = new ProjectContext(Constant.travelReportProjectId, ComponentAccessor.getProjectManager());
            Options options = yuanCustomField.getOptions("", jiraContextNode);
            List<Project> projectList = ComponentAccessor.getProjectManager().getProjectObjects();
            for (Project project : projectList) {
                if (options != null) {
                    break;
                }
                jiraContextNode = new ProjectContext(project.getId(), ComponentAccessor.getProjectManager());
                options = yuanCustomField.getOptions("", jiraContextNode);
            }
            Option op = null;
            for (Option option : options) {
                	if (Objects.equals(option.getOptionId(), Long.valueOf(fieldValueId))) {
                  			op = option;
                  			break;
                 		}
              	}

            mutableIssue.setCustomFieldValue(yuanCustomField,op);


//            ApplicationUser currentUser = ComponentAccessor.getJiraAuthenticationContext().getLoggedInUser();
//            ComponentAccessor.getIssueManager().updateIssue(currentUser, mutableIssue, EventDispatchOption.ISSUE_UPDATED, false);
        } catch (Exception e) {
            e.printStackTrace();
            throw new WorkflowException("单选值设置出错",e);
        }
    }

}
