package com.eve.workflow.postfunctions;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.atlassian.jira.component.ComponentAccessor;
import com.atlassian.jira.issue.customfields.CustomFieldType;
import com.atlassian.jira.issue.customfields.option.Option;
import com.atlassian.jira.issue.fields.CustomField;
import com.atlassian.jira.issue.issuetype.IssueType;
import com.atlassian.jira.plugin.workflow.AbstractWorkflowPluginFactory;
import com.atlassian.jira.plugin.workflow.WorkflowPluginFunctionFactory;
import com.eve.beans.OptionsValueBean;
import com.eve.beans.ResultBean;
import com.eve.services.ServiceUpdateOption;
import com.opensymphony.workflow.loader.AbstractDescriptor;
import com.opensymphony.workflow.loader.FunctionDescriptor;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/2/22 15:23
 */
public class UpdateIssueOptionFunctionFactory extends AbstractWorkflowPluginFactory implements WorkflowPluginFunctionFactory {

    private static  String customType = "com.atlassian.jira.plugin.system.customfieldtypes:select";
    @Override
    protected void getVelocityParamsForInput(Map<String, Object> map) {
//           ResultBean ResultBeanList = serviceUpdateOption.queryFieldList("com.atlassian.jira.plugin.system.customfieldtypes:select");
//           map.put("ResultBeanList",ResultBeanList);
        List<CustomField> customFieldList = ComponentAccessor.getCustomFieldManager().getCustomFieldObjects();
        List<CustomFieldType<?, ?>> customFieldTypeList = ComponentAccessor.getCustomFieldManager().getCustomFieldTypes();
        List<CustomField> customFieldListOptions = new ArrayList<>();
//        for (CustomFieldType<?, ?> customFieldType : customFieldTypeList) {
//            customFieldType.getName();
//            customFieldType.getKey();
//        }
        for (CustomField customField:customFieldList){
            if (customType.equals(customField.getCustomFieldType().getKey())){
                customFieldListOptions.add(customField);
            }
        }
        map.put("customFieldList", customFieldListOptions);
        map.put("customFieldTypeList", customFieldTypeList);
    }


    @Override
    protected void getVelocityParamsForEdit(Map<String, Object> map, AbstractDescriptor abstractDescriptor) {

        FunctionDescriptor conditionDescriptor = (FunctionDescriptor) abstractDescriptor;
        JSONObject jsonObject = JSON.parseObject((String) conditionDescriptor.getArgs().get("optionsJson"));

        String field_field = String.valueOf(jsonObject.get("field_field"));
        String field_value = String.valueOf(jsonObject.get("field_value"));

        String jqlConditionEnabled = String.valueOf(jsonObject.get("jqlConditionEnabled"));
        String jqlCondition = String.valueOf(jsonObject.get("jqlCondition"));

        map.put("jqlConditionEnabled", jqlConditionEnabled);
        map.put("jqlCondition", jqlCondition);

        List<CustomField> customFieldList = ComponentAccessor.getCustomFieldManager().getCustomFieldObjects();
        List<IssueType> allIssueTypeList = (List<IssueType>) ComponentAccessor.getConstantsManager().getAllIssueTypeObjects();
        List<CustomField> customFieldListOptions = new ArrayList<>();
        for (CustomField customField:customFieldList){
            if (customType.equals(customField.getCustomFieldType().getKey())){
                customFieldListOptions.add(customField);
            }
        }
        map.put("customFieldList", customFieldListOptions);
        map.put("allIssueTypeList", allIssueTypeList);
        map.put("field_field", field_field);
        map.put("field_value", field_value);
    }

    @Override
    protected void getVelocityParamsForView(Map<String, Object> map, AbstractDescriptor descriptor) {
        if (!(descriptor instanceof FunctionDescriptor)) {
            throw new IllegalArgumentException("Descriptor must be a ConditionDescriptor.");
        }
        FunctionDescriptor conditionDescriptor = (FunctionDescriptor) descriptor;
        JSONObject jsonObject = JSON.parseObject((String) conditionDescriptor.getArgs().get("optionsJson"));
        String jqlConditionEnabled = String.valueOf(jsonObject.get("jqlConditionEnabled"));
        String jqlCondition = String.valueOf(jsonObject.get("jqlCondition"));

        map.put("jqlConditionEnabled", jqlConditionEnabled);
        map.put("jqlCondition", jqlCondition);
        String field_field = String.valueOf(jsonObject.get("field_field"));
        String field_value = String.valueOf(jsonObject.get("field_value"));

        CustomField customField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(Long.valueOf(field_field));
        Option option = ComponentAccessor.getOptionsManager().findByOptionId(Long.valueOf(field_value));


        map.put("field_field", customField == null ? "字段已删除" + field_field : customField.getFieldName());
        map.put("field_value", option == null ? "选项已删除" + field_value : option.getValue());
        map.put("optionsJson",jsonObject.toJSONString());
    }

    @Override
    public Map<String, ?> getDescriptorParams(Map<String, Object> map) {
        Map<String,Object> hashMap = new HashMap<>();
        try{

            String[] field_field = (String[]) map.get("field_field");
            String[] field_value = (String[]) map.get("field_value");
            String[] jqlConditionEnabled = (String[]) map.get("jqlConditionEnabled");
            String[] jqlCondition = (String[]) map.get("jqlCondition");
            //JSONObject abcjson = new JSONObject();
            JSONObject resp = new JSONObject();
            resp.put("field_field",field_field[0]);
            resp.put("field_value",field_value[0]);
            resp.put("jqlConditionEnabled", jqlConditionEnabled[0]);
            resp.put("jqlCondition", jqlCondition[0]);
            hashMap.put("optionsJson", resp.toJSONString());

        }catch (Exception e){
            e.printStackTrace();
        }
        return hashMap;
    }
}