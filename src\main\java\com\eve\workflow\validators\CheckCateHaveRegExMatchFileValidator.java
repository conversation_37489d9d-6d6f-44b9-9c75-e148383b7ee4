package com.eve.workflow.validators;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.atlassian.jira.component.ComponentAccessor;
import com.atlassian.jira.issue.MutableIssue;
import com.atlassian.jira.issue.attachment.Attachment;
import com.eve.utils.Utils;
import com.eve.workflow.postfunctions.RunTransitionByParamsFunction;
import com.opensymphony.module.propertyset.PropertySet;
import com.opensymphony.workflow.Validator;
import com.opensymphony.workflow.WorkflowException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @since 2023/4/13
 */
public class CheckCateHaveRegExMatchFileValidator implements Validator {
    private static final Logger log = LoggerFactory.getLogger(CheckCateHaveRegExMatchFileValidator.class);
    @Override
    public void validate(Map transientVars, Map args, PropertySet propertySet) throws WorkflowException {
        MutableIssue mutableIssue = (MutableIssue)transientVars.get("issue");
        try {
            JSONObject jsonObject = JSON.parseObject((String) args.get("paramsJson"));
            String cateName = String.valueOf(jsonObject.get("cateName"));
            String regex = String.valueOf(jsonObject.get("regex"));
            String tipText = String.valueOf(jsonObject.get("tipText"));
            // 创建 Pattern 对象
            Pattern pattern = Pattern.compile(regex);

            List<Map<String, Object>> attachmentCategoriesMapList = Utils.getAttachmentCategoriesMapList(mutableIssue);
            Map<String, Object> map = attachmentCategoriesMapList.stream().filter(e -> cateName.equals(String.valueOf(e.get("name")))).findFirst().orElse(null);
            if (map == null) {
                return;
            }
            Object documents = map.get("documents");
            List<Long> attachmentIdList = new ArrayList<>();
            if (documents == null) {
                for (Map<String, Object> stringObjectMap : (List<Map<String, Object>>) documents) {
                    List<Long> attachmentIds = (List<Long>) stringObjectMap.get("attachmentIds");
                    if (ObjectUtil.isNotEmpty(attachmentIds)) {
                        attachmentIdList.addAll(attachmentIds);
                    }
                }
            }
            List<Long> attachments = (List<Long>) map.get("attachments");
            if (ObjectUtil.isNotEmpty(attachments)) {
                attachmentIdList.addAll(attachments);
            }
            boolean noFind = true;
            for (Long attachmentId : attachmentIdList) {
                Attachment attachment = ComponentAccessor.getAttachmentManager().getAttachment(attachmentId);
                String attachmentFilename = attachment.getFilename();
                Matcher matcher = pattern.matcher(attachmentFilename);
                if (matcher.find()) {
                    noFind = false;
                }
            }
            if (noFind) {
                throw new WorkflowException(ObjectUtil.isEmpty(tipText) ? cateName + "文件分类下无符合" + regex + "正则的文件！" : tipText);
            }

        } catch (Exception e) {
            log.error(Utils.errInfo(e));
            throw new WorkflowException(e);
        }
    }
}
