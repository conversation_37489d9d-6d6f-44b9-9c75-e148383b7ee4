package com.eve.workflow.validators;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.atlassian.jira.plugin.workflow.AbstractWorkflowPluginFactory;
import com.atlassian.jira.plugin.workflow.WorkflowPluginValidatorFactory;
import com.opensymphony.workflow.loader.AbstractDescriptor;
import com.opensymphony.workflow.loader.ValidatorDescriptor;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/4/13
 */
public class CheckCateHaveRegExMatchFileValidatorFactory extends AbstractWorkflowPluginFactory implements WorkflowPluginValidatorFactory {

    @Override
    protected void getVelocityParamsForInput(Map<String, Object> map) {

    }

    @Override
    protected void getVelocityParamsForEdit(Map<String, Object> map, AbstractDescriptor abstractDescriptor) {
        ValidatorDescriptor validatorDescriptor = (ValidatorDescriptor) abstractDescriptor;
        JSONObject jsonObject = JSON.parseObject((String) validatorDescriptor.getArgs().get("paramsJson"));

        String cateName = String.valueOf(jsonObject.get("cateName"));
        String regex = String.valueOf(jsonObject.get("regex"));
        String tipText = String.valueOf(jsonObject.get("tipText"));

        map.put("cateName", cateName);
        map.put("regex", regex);
        map.put("tipText", tipText);
    }

    @Override
    protected void getVelocityParamsForView(Map<String, Object> map, AbstractDescriptor abstractDescriptor) {
        ValidatorDescriptor validatorDescriptor = (ValidatorDescriptor) abstractDescriptor;
        JSONObject jsonObject = JSON.parseObject((String) validatorDescriptor.getArgs().get("paramsJson"));

        String cateName = String.valueOf(jsonObject.get("cateName"));
        String regex = String.valueOf(jsonObject.get("regex"));
        String tipText = String.valueOf(jsonObject.get("tipText"));

        map.put("cateName", cateName);
        map.put("regex", regex);
        map.put("tipText", tipText);
    }

    @Override
    public Map<String, ?> getDescriptorParams(Map<String, Object> map) {
        Map<String,Object> hashMap = new HashMap<>();
        try{
            String[] cateName = (String[]) map.get("cateName");
            String[] regex = (String[]) map.get("regex");
            String[] tipText = (String[]) map.get("tipText");
            JSONObject resp = new JSONObject();
            resp.put("cateName", cateName[0]);
            resp.put("regex",regex[0]);
            resp.put("tipText",tipText[0]);
            hashMap.put("paramsJson", resp.toJSONString());
        }catch (Exception e){
            e.printStackTrace();
        }
        return hashMap;
    }
}
