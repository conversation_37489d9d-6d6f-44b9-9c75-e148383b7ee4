package com.eve.workflow.validators;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.atlassian.jira.component.ComponentAccessor;
import com.atlassian.jira.issue.MutableIssue;
import com.atlassian.jira.issue.changehistory.ChangeHistory;
import com.atlassian.jira.issue.changehistory.ChangeHistoryManager;
import com.atlassian.jira.issue.customfields.option.Option;
import com.atlassian.jira.issue.fields.CustomField;
import com.atlassian.jira.issue.fields.screen.FieldScreen;
import com.atlassian.jira.issue.fields.screen.FieldScreenLayoutItem;
import com.atlassian.jira.issue.fields.screen.FieldScreenManager;
import com.atlassian.jira.issue.fields.screen.FieldScreenTab;
import com.atlassian.jira.issue.history.ChangeItemBean;
import com.atlassian.jira.user.ApplicationUser;
import com.atlassian.jira.workflow.JiraWorkflow;
import com.atlassian.jira.workflow.WorkflowActionsBean;
import com.atlassian.jira.workflow.edit.utilities.ScreenNameResolverImpl;
import com.eve.beans.ReviewCodeBean;
import com.eve.utils.Utils;
import com.opensymphony.module.propertyset.PropertySet;
import com.opensymphony.workflow.InvalidInputException;
import com.opensymphony.workflow.Validator;
import com.opensymphony.workflow.WorkflowException;
import com.opensymphony.workflow.loader.ActionDescriptor;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/10/19
 */
public class CheckFieldIsNotEmptyValidator implements Validator {
    @Override
    public void validate(Map transientVars, Map args, PropertySet propertySet) throws WorkflowException {
        MutableIssue mutableIssue = (MutableIssue)transientVars.get("issue");
//        try {

            JSONObject jsonObject = JSON.parseObject((String) args.get("paramsJson"));

            List<String> checkCustomFiledIdList = JSON.parseArray(String.valueOf(jsonObject.get("checkCustomFiled")), String.class);
//            String tipText = String.valueOf(jsonObject.get("tipText"));

//            List<CustomField> checkCustomFiledList = checkCustomFiledIdList.stream().map(e -> ComponentAccessor.getCustomFieldManager().getCustomFieldObject(e)).collect(Collectors.toList());
            StringBuilder tipContent = new StringBuilder();
            checkCustomFiledIdList.stream().map(e -> ComponentAccessor.getCustomFieldManager().getCustomFieldObject(e))
                    .filter(customField -> customField != null && mutableIssue.getCustomFieldValue(customField) == null)
                    .forEach(customField -> tipContent.append(customField.getFieldName()).append("不能为空! \r\n"));
            if (tipContent.length() != 0) {
                throw new WorkflowException(tipContent.toString());
            }
//        } catch (Exception e) {
//            throw new WorkflowException(e);
//        }
    }

}
