package com.eve.workflow.validators;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.atlassian.jira.component.ComponentAccessor;
import com.atlassian.jira.issue.MutableIssue;
import com.eve.services.CustomToolService;
import com.opensymphony.module.propertyset.PropertySet;
import com.opensymphony.workflow.Validator;
import com.opensymphony.workflow.WorkflowException;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2025/08/19
 */
public class CheckInterfaceTransitionValidator implements Validator {
    @Resource
    private CustomToolService customToolService;

    @Override
    public void validate(Map transientVars, Map args, PropertySet propertySet) throws WorkflowException {
        MutableIssue mutableIssue = (MutableIssue) transientVars.get("issue");
//        try {
        Integer actionId = (Integer) transientVars.get("actionId");
        boolean isCreate = actionId == 1;
        JSONObject jsonObject = JSON.parseObject((String) args.get("paramsJson"));

//            List<String> checkCustomFiledIdList = JSON.parseArray(String.valueOf(jsonObject.get("checkCustomFiled")), String.class);
        String tipText = String.valueOf(jsonObject.get("tipText"));

        if (ObjectUtils.isEmpty(tipText)) {
            tipText = isCreate ? "请到PBI系统新建！" : "请到PBI系统处理！";
        }
        boolean validateFail = !customToolService.stringSet.contains(mutableIssue.getKey());
        if (isCreate) {
            validateFail = !customToolService.stringSet.contains(mutableIssue.getProjectId().toString() + mutableIssue.getIssueTypeId());
        }
        if (validateFail) {
            throw new WorkflowException(tipText);
        }
    }

}
