package com.eve.workflow.validators;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.atlassian.jira.component.ComponentAccessor;
import com.atlassian.jira.issue.fields.CustomField;
import com.atlassian.jira.plugin.workflow.AbstractWorkflowPluginFactory;
import com.atlassian.jira.plugin.workflow.WorkflowPluginValidatorFactory;
import com.opensymphony.workflow.loader.AbstractDescriptor;
import com.opensymphony.workflow.loader.ValidatorDescriptor;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/08/19
 */
public class CheckInterfaceTransitionValidatorFactory extends AbstractWorkflowPluginFactory implements WorkflowPluginValidatorFactory {

    @Override
    protected void getVelocityParamsForInput(Map<String, Object> map) {
//        List<CustomField> customFieldList = ComponentAccessor.getCustomFieldManager().getCustomFieldObjects();
//        List<CustomField> dateCustomFieldListOptions = new ArrayList<>();
//        for (CustomField customField : customFieldList) {
//            if (Constant.dateFieldType.equals(customField.getCustomFieldType().getKey()) ||
//                    Constant.dateTimeFieldType.equals(customField.getCustomFieldType().getKey())) {
//                dateCustomFieldListOptions.add(customField);
//            }
//        }
//        map.put("customFieldList", dateCustomFieldListOptions);
//        map.put("checkCustomFiledList", customFieldList);
        map.put("tipText", "请到PBI系统处理！");
    }

    @Override
    protected void getVelocityParamsForEdit(Map<String, Object> map, AbstractDescriptor abstractDescriptor) {
        ValidatorDescriptor validatorDescriptor = (ValidatorDescriptor) abstractDescriptor;
        JSONObject jsonObject = JSON.parseObject((String) validatorDescriptor.getArgs().get("paramsJson"));

//        List<String> checkCustomFiledIdList = JSON.parseArray(String.valueOf(jsonObject.get("checkCustomFiled")), String.class);
        String tipText = String.valueOf(jsonObject.get("tipText"));
//        List<CustomField> checkCustomFiledList = checkCustomFiledIdList.stream().map(e -> ComponentAccessor.getCustomFieldManager().getCustomFieldObject(e)).collect(Collectors.toList());

//        map.put("checkCustomFiled", checkCustomFiledIdList);
        map.put("tipText", tipText);

//        List<CustomField> customFieldList = ComponentAccessor.getCustomFieldManager().getCustomFieldObjects();

//        map.put("checkCustomFiledList", customFieldList);
    }

    @Override
    protected void getVelocityParamsForView(Map<String, Object> map, AbstractDescriptor abstractDescriptor) {
        ValidatorDescriptor validatorDescriptor = (ValidatorDescriptor) abstractDescriptor;
        JSONObject jsonObject = JSON.parseObject((String) validatorDescriptor.getArgs().get("paramsJson"));

//        List<String> checkCustomFiledList = JSON.parseArray(String.valueOf(jsonObject.get("checkCustomFiled")), String.class);
        String tipText = String.valueOf(jsonObject.get("tipText"));
//        String viewString = "";

//        for (String customFiled : checkCustomFiledList) {
//            CustomField checkCustomFiledCustomField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(customFiled);
//            if (checkCustomFiledCustomField != null) {
//                viewString += checkCustomFiledCustomField.getFieldName() + ",";
//            }
//        }
//        viewString = checkCustomFiledList.stream().map(e->ComponentAccessor.getCustomFieldManager().getCustomFieldObject(e)).filter(Objects::nonNull).map(CustomField::getFieldName).collect(Collectors.joining(","));

//        map.put("checkCustomFiled", viewString);
        map.put("tipText", tipText);
    }

    @Override
    public Map<String, ?> getDescriptorParams(Map<String, Object> map) {
        Map<String,Object> hashMap = new HashMap<>();
        try{
//            String[] checkCustomFiled = (String[]) map.get("checkCustomFiled");
            String[] tipText = (String[]) map.get("tipText");
            JSONObject resp = new JSONObject();
//            resp.put("checkCustomFiled",checkCustomFiled);
            resp.put("tipText",tipText[0]);
            hashMap.put("paramsJson", resp.toJSONString());
        }catch (Exception e){
            e.printStackTrace();
        }
        return hashMap;
    }
}