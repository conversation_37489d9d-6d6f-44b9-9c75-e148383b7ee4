package com.eve.workflow.validators;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.atlassian.jira.component.ComponentAccessor;
import com.atlassian.jira.issue.MutableIssue;
import com.atlassian.jira.issue.fields.CustomField;
import com.atlassian.jira.user.ApplicationUser;
import com.opensymphony.module.propertyset.PropertySet;
import com.opensymphony.workflow.Validator;
import com.opensymphony.workflow.WorkflowException;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/12/30
 */
public class CheckMultiUserCountValidator implements Validator {
    @Override
    public void validate(Map transientVars, Map args, PropertySet propertySet) throws WorkflowException {
        MutableIssue mutableIssue = (MutableIssue)transientVars.get("issue");


        JSONObject jsonObject = JSON.parseObject((String) args.get("paramsJson"));

        String checkCustomFiledId = String.valueOf(jsonObject.get("checkCustomFiled"));
        String isContain = String.valueOf(jsonObject.get("isContain"));
        List<String> containCustomFiledIdList = JSON.parseArray(String.valueOf(jsonObject.get("containCustomFiledList")), String.class);
        String compareType = String.valueOf(jsonObject.get("compareType"));
        String targetUserCount = String.valueOf(jsonObject.get("targetUserCount"));

        CustomField checkCustomFiled = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(checkCustomFiledId);
        List<ApplicationUser> checkApplicationUserList = (List<ApplicationUser>) mutableIssue.getCustomFieldValue(checkCustomFiled);
        if (ObjectUtil.isEmpty(checkApplicationUserList)) {
            checkApplicationUserList = new ArrayList<>();
        }
        if ("lessEqual".equals(compareType) && checkApplicationUserList.size() > Integer.parseInt(targetUserCount)) {
            throw new WorkflowException(checkCustomFiled.getFieldName() + " 字段，选择的用户数量不得多于 " + targetUserCount);
        } else if ("moreEqual".equals(compareType) && checkApplicationUserList.size() < Integer.parseInt(targetUserCount)) {
            throw new WorkflowException(checkCustomFiled.getFieldName() + " 字段，选择的用户数量不得少于 " + targetUserCount);
        }

        List<String> collect = checkApplicationUserList.stream().map(ApplicationUser::getUsername).collect(Collectors.toList());
        List<CustomField> containCustomFiledList = containCustomFiledIdList.stream().map(e -> ComponentAccessor.getCustomFieldManager().getCustomFieldObject(e)).collect(Collectors.toList());
//        List<ApplicationUser> containApplicationUserList = containCustomFiledList.stream().map(e -> (ApplicationUser) mutableIssue.getCustomFieldValue(e)).collect(Collectors.toList());
        for (CustomField e : containCustomFiledList) {
            ApplicationUser applicationUser = (ApplicationUser) mutableIssue.getCustomFieldValue(e);
            if (applicationUser == null || collect.isEmpty()) {
                continue;
            }
            if ("false".equals(isContain) && collect.contains(applicationUser.getUsername())) {
                throw new WorkflowException(checkCustomFiled.getFieldName() + " 字段不可包含以下字段用户 " + e.getFieldName() + ": " + applicationUser.getUsername());
            }
            if ("true".equals(isContain) && !collect.contains(applicationUser.getUsername())) {
                throw new WorkflowException(checkCustomFiled.getFieldName() + " 字段需包含以下字段用户 " + e.getFieldName() + ": " + applicationUser.getUsername());
            }
        }


//        List<String> checkCustomFiledIdList = JSON.parseArray(String.valueOf(jsonObject.get("checkCustomFiled")), String.class);
//
//        StringBuilder tipContent = new StringBuilder();
//        checkCustomFiledIdList.stream().map(e -> ComponentAccessor.getCustomFieldManager().getCustomFieldObject(e))
//                .filter(customField -> customField != null && mutableIssue.getCustomFieldValue(customField) == null)
//                .forEach(customField -> tipContent.append(customField.getFieldName()).append("不能为空! \r\n"));
//        if (tipContent.length() != 0) {
//            throw new WorkflowException(tipContent.toString());
//        }
    }

}
