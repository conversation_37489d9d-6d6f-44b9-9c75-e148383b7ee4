package com.eve.workflow.validators;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.atlassian.jira.component.ComponentAccessor;
import com.atlassian.jira.issue.fields.CustomField;
import com.atlassian.jira.plugin.workflow.AbstractWorkflowPluginFactory;
import com.atlassian.jira.plugin.workflow.WorkflowPluginValidatorFactory;
import com.eve.utils.Constant;
import com.opensymphony.workflow.loader.AbstractDescriptor;
import com.opensymphony.workflow.loader.ValidatorDescriptor;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/12/30
 */
public class CheckMultiUserCountValidatorFactory extends AbstractWorkflowPluginFactory implements WorkflowPluginValidatorFactory {

    @Override
    protected void getVelocityParamsForInput(Map<String, Object> map) {
        List<CustomField> customFieldList = ComponentAccessor.getCustomFieldManager().getCustomFieldObjects();
        List<CustomField> userPickerCustomFieldListOptions = new ArrayList<>();
        List<CustomField> multiUserPickerCustomFieldListOptions = new ArrayList<>();
        for (CustomField customField : customFieldList) {
            if (Constant.multiUserPickerFieldType.equals(customField.getCustomFieldType().getKey())) {
                userPickerCustomFieldListOptions.add(customField);
                multiUserPickerCustomFieldListOptions.add(customField);
            } else if (Constant.userPickerFieldType.equals(customField.getCustomFieldType().getKey())) {
                userPickerCustomFieldListOptions.add(customField);
            }
        }
        map.put("userCustomFieldList", userPickerCustomFieldListOptions);
        map.put("multiUserCustomFieldList", multiUserPickerCustomFieldListOptions);
    }

    @Override
    protected void getVelocityParamsForEdit(Map<String, Object> map, AbstractDescriptor abstractDescriptor) {
        ValidatorDescriptor validatorDescriptor = (ValidatorDescriptor) abstractDescriptor;
        JSONObject jsonObject = JSON.parseObject((String) validatorDescriptor.getArgs().get("paramsJson"));

        String checkCustomFiled = String.valueOf(jsonObject.get("checkCustomFiled"));
        String isContain = String.valueOf(jsonObject.get("isContain"));
        List<String> containCustomFiledIdList = JSON.parseArray(String.valueOf(jsonObject.get("containCustomFiledList")), String.class);
        String compareType = String.valueOf(jsonObject.get("compareType"));
        String targetUserCount = String.valueOf(jsonObject.get("targetUserCount"));
//        List<CustomField> containCustomFiledList = containCustomFiledIdList.stream().map(e -> ComponentAccessor.getCustomFieldManager().getCustomFieldObject(e)).collect(Collectors.toList());

        map.put("checkCustomFiled", checkCustomFiled);
        map.put("isContain", isContain);
        map.put("containCustomFiledList", containCustomFiledIdList);
        map.put("compareType", compareType);
        map.put("targetUserCount", targetUserCount);

        List<CustomField> customFieldList = ComponentAccessor.getCustomFieldManager().getCustomFieldObjects();
        List<CustomField> userPickerCustomFieldListOptions = new ArrayList<>();
        List<CustomField> multiUserPickerCustomFieldListOptions = new ArrayList<>();
        for (CustomField customField : customFieldList) {
            if (Constant.multiUserPickerFieldType.equals(customField.getCustomFieldType().getKey())) {
                userPickerCustomFieldListOptions.add(customField);
                multiUserPickerCustomFieldListOptions.add(customField);
            } else if (Constant.userPickerFieldType.equals(customField.getCustomFieldType().getKey())) {
                userPickerCustomFieldListOptions.add(customField);
            }
        }
        map.put("userCustomFieldList", userPickerCustomFieldListOptions);
        map.put("multiUserCustomFieldList", multiUserPickerCustomFieldListOptions);
    }

    @Override
    protected void getVelocityParamsForView(Map<String, Object> map, AbstractDescriptor abstractDescriptor) {
        ValidatorDescriptor validatorDescriptor = (ValidatorDescriptor) abstractDescriptor;
        JSONObject jsonObject = JSON.parseObject((String) validatorDescriptor.getArgs().get("paramsJson"));

        String checkCustomFiled = String.valueOf(jsonObject.get("checkCustomFiled"));
        String isContain = String.valueOf(jsonObject.get("isContain"));
        List<String> containCustomFiledIdList = JSON.parseArray(String.valueOf(jsonObject.get("containCustomFiledList")), String.class);
        String compareType = String.valueOf(jsonObject.get("compareType"));
        String targetUserCount = String.valueOf(jsonObject.get("targetUserCount"));
//        List<CustomField> containCustomFiledList = containCustomFiledIdList.stream().map(e -> ComponentAccessor.getCustomFieldManager().getCustomFieldObject(e)).collect(Collectors.toList());

//        map.put("checkCustomFiled", checkCustomFiled);
//        map.put("isContain", isContain);
//        map.put("containCustomFiledList", containCustomFiledIdList);
//        map.put("compareType", compareType);
//        map.put("targetUserCount", targetUserCount);

//        for (String customFiled : checkCustomFiledList) {
//            CustomField checkCustomFiledCustomField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(customFiled);
//            if (checkCustomFiledCustomField != null) {
//                viewString += checkCustomFiledCustomField.getFieldName() + ",";
//            }
//        }
        String viewString = containCustomFiledIdList.stream().map(e->ComponentAccessor.getCustomFieldManager().getCustomFieldObject(e)).filter(Objects::nonNull).map(CustomField::getFieldName).collect(Collectors.joining(","));

        map.put("checkCustomFiled", ComponentAccessor.getCustomFieldManager().getCustomFieldObject(checkCustomFiled).getFieldName());
        map.put("isContain", "true".equals(isContain)?"包含":"不包含");
        map.put("containCustomFiledList", viewString);
        map.put("compareType", "lessEqual".equals(compareType) ? "小于等于" : "大于等于");
        map.put("targetUserCount", targetUserCount);
    }

    @Override
    public Map<String, ?> getDescriptorParams(Map<String, Object> map) {
        Map<String,Object> hashMap = new HashMap<>();
        try{
            String[] checkCustomFiled = (String[]) map.get("checkCustomFiled");
            String[] isContain = (String[]) map.get("isContain");
            String[] containCustomFiledList = (String[]) map.get("containCustomFiledList");
            String[] compareType = (String[]) map.get("compareType");
            String[] targetUserCount = (String[]) map.get("targetUserCount");
            JSONObject resp = new JSONObject();
            resp.put("checkCustomFiled",checkCustomFiled[0]);
            resp.put("isContain",isContain[0]);
            resp.put("containCustomFiledList",containCustomFiledList);
            resp.put("compareType",compareType[0]);
            resp.put("targetUserCount",targetUserCount[0]);
            hashMap.put("paramsJson", resp.toJSONString());
        }catch (Exception e){
            e.printStackTrace();
        }
        return hashMap;
    }
}