package com.eve.workflow.validators;

import com.atlassian.jira.component.ComponentAccessor;
import com.atlassian.jira.issue.MutableIssue;
import com.atlassian.jira.issue.fields.CustomField;
import com.eve.beans.CostCenterBean;
import com.eve.beans.InnerOrderTypeBean;
import com.eve.utils.Constant;
import com.opensymphony.module.propertyset.PropertySet;
import com.opensymphony.workflow.InvalidInputException;
import com.opensymphony.workflow.Validator;
import com.opensymphony.workflow.WorkflowException;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/9/7
 */
public class CheckSapCreateProjectParamValidator implements Validator {
    @Override
    public void validate(Map transientVars, Map args, PropertySet ps) throws InvalidInputException, WorkflowException {
        MutableIssue mutableIssue = (MutableIssue)transientVars.get("issue");
        try {
            CustomField innerOrderTypeField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(Constant.innerOrderType);
            CustomField costCenterField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(Constant.costCenter);

            List<InnerOrderTypeBean> innerOrderTypeBeanList = (List<InnerOrderTypeBean>) mutableIssue.getCustomFieldValue(innerOrderTypeField);
            List<CostCenterBean> costCenterBeanList = (List<CostCenterBean>) mutableIssue.getCustomFieldValue(costCenterField);

            if (innerOrderTypeBeanList == null && costCenterBeanList == null) {
                return;
            }
            if (innerOrderTypeBeanList == null) {
                throw new WorkflowException("请选择内部订单类型");
            }
            if (costCenterBeanList == null) {
                throw new WorkflowException("请选择成本中心");
            }
            String companyCode = innerOrderTypeBeanList.get(0).getCompanyCode();
            String bukrs = costCenterBeanList.get(0).getBUKRS();
            if (bukrs == null || companyCode == null) {
                throw new WorkflowException("内部订单类型或成本中心错误，请联系JIRA系统管理员");
            }
            if (!bukrs.equals(companyCode)) {
                throw new WorkflowException("内部订单类型与成本中心的公司代码不一致！");
            }
        } catch (WorkflowException e) {
            throw e;
        } catch (Exception e) {
            throw new WorkflowException(e);
        }
    }
}
