package com.eve.workflow.validators;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.atlassian.jira.component.ComponentAccessor;
import com.atlassian.jira.issue.MutableIssue;
import com.atlassian.jira.issue.changehistory.ChangeHistory;
import com.atlassian.jira.issue.changehistory.ChangeHistoryManager;
import com.atlassian.jira.issue.customfields.option.Option;
import com.atlassian.jira.issue.fields.CustomField;

import com.atlassian.jira.issue.fields.screen.FieldScreen;
import com.atlassian.jira.issue.fields.screen.FieldScreenLayoutItem;
import com.atlassian.jira.issue.fields.screen.FieldScreenManager;
import com.atlassian.jira.issue.fields.screen.FieldScreenTab;
import com.atlassian.jira.issue.history.ChangeItemBean;
import com.atlassian.jira.user.ApplicationUser;
import com.atlassian.jira.workflow.JiraWorkflow;
import com.atlassian.jira.workflow.WorkflowActionsBean;
import com.atlassian.jira.workflow.edit.utilities.ScreenNameResolverImpl;
import com.eve.beans.ReviewCodeBean;
import com.eve.utils.Utils;
import com.opensymphony.module.propertyset.PropertySet;
import com.opensymphony.workflow.Validator;
import com.opensymphony.workflow.WorkflowException;
import com.opensymphony.workflow.loader.ActionDescriptor;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/10/19
 */
public class CompareTimeRequiredFieldValidator implements Validator {
    @Override
    public void validate(Map transientVars, Map args, PropertySet propertySet) throws WorkflowException {
        MutableIssue mutableIssue = (MutableIssue)transientVars.get("issue");
        try {
//            testGetScreen(mutableIssue);
//            testReviewNode(mutableIssue);
            testCaseOption(mutableIssue);
            JSONObject jsonObject = JSON.parseObject((String) args.get("paramsJson"));
            String startTime = String.valueOf(jsonObject.get("startTime"));
            String endTime = String.valueOf(jsonObject.get("endTime"));
            String compareType = String.valueOf(jsonObject.get("compareType"));
            String targetNum = String.valueOf(jsonObject.get("targetNum"));
            String targetCustomField = String.valueOf(jsonObject.get("targetCustomField"));
            String tipText = String.valueOf(jsonObject.get("tipText"));
            if (ObjectUtils.isEmpty(targetNum)) {
                targetNum = "0";
            }
            BigDecimal targetNumBig = new BigDecimal(targetNum);
            BigDecimal numBig;
            CustomField startTimeCustomField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(startTime);
            CustomField endTimeCustomField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(endTime);
            CustomField targetCustomFieldCustomField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(targetCustomField);
            if (startTimeCustomField == null || endTimeCustomField == null || targetCustomFieldCustomField == null) {
                return;
            }
            if (ObjectUtils.isEmpty(tipText)) {
                tipText = targetCustomFieldCustomField.getFieldName() + " 字段不能为空！";
            }
            Date startTimeValue = (Date) mutableIssue.getCustomFieldValue(startTimeCustomField);
            Date endTimeValue = (Date) mutableIssue.getCustomFieldValue(endTimeCustomField);
            Object customFieldValue = mutableIssue.getCustomFieldValue(targetCustomFieldCustomField);
            if (startTimeValue == null || endTimeValue == null) {
                return;
            }
            numBig = new BigDecimal(Utils.diffDate(endTimeValue,startTimeValue));
            switch (compareType) {
                case "<":
                    if (numBig.compareTo(targetNumBig) < 0 && customFieldValue == null) {
                        throw new WorkflowException(tipText);
                    }
                    break;
                case ">":
                    if (numBig.compareTo(targetNumBig) > 0 && customFieldValue == null) {
                        throw new WorkflowException(tipText);
                    }
                    break;
                case "=":
                    if (numBig.compareTo(targetNumBig) == 0 && customFieldValue == null) {
                        throw new WorkflowException(tipText);
                    }
                    break;
                case "<=":
                    if (numBig.compareTo(targetNumBig) < 1 && customFieldValue == null) {
                        throw new WorkflowException(tipText);
                    }
                    break;
                case ">=":
                    if (numBig.compareTo(targetNumBig) > -1 && customFieldValue == null) {
                        throw new WorkflowException(tipText);
                    }
                    break;
                default:
                    break;
            }
        } catch (WorkflowException e) {
            throw e;
        } catch (Exception e) {
            throw new WorkflowException(e);
        }
    }

    public void testGetScreen(MutableIssue mutableIssue) {
        WorkflowActionsBean workflowActionsBean = new WorkflowActionsBean();

        FieldScreenManager fieldScreenManager = ComponentAccessor.getFieldScreenManager();

        JiraWorkflow workflow = ComponentAccessor.getWorkflowManager().getWorkflow(mutableIssue);
        Collection<ActionDescriptor> allActions = workflow.getAllActions();
        List<String> actionParams = new ArrayList<>();
        for (ActionDescriptor actionDescriptor : allActions) {
            int id = actionDescriptor.getId();
            String name = actionDescriptor.getName();
            String actionDescriptorView = actionDescriptor.getView();
            ScreenNameResolverImpl screenNameResolver = new ScreenNameResolverImpl(fieldScreenManager);
            String name1 = screenNameResolver.getScreenName(actionDescriptor);
            String fieldScreenId = (String) actionDescriptor.getMetaAttributes().get("jira.fieldscreen.id");
            FieldScreen fieldScreenForView = workflowActionsBean.getFieldScreenForView(actionDescriptor);
            if (fieldScreenForView != null) {
                List<FieldScreenTab> fieldScreenForViewTabs = fieldScreenForView.getTabs();
                for (FieldScreenTab fieldScreenForViewTab : fieldScreenForViewTabs) {
                    List<FieldScreenLayoutItem> fieldScreenLayoutItems = fieldScreenForViewTab.getFieldScreenLayoutItems();
                    for (FieldScreenLayoutItem fieldScreenLayoutItem : fieldScreenLayoutItems) {
                        String fieldId = fieldScreenLayoutItem.getFieldId();
                    }
                }
            }
            actionParams.add(id + "#" + name + "#" + actionDescriptorView + "#" + name1);
        }
        if (actionParams.isEmpty()) {
            actionParams.add("");
        }
    }

    public Object testReviewNode(MutableIssue mutableIssue) {
        ChangeHistoryManager changeHistoryManager = ComponentAccessor.getChangeHistoryManager();
        List<ChangeHistory> changeHistories = changeHistoryManager.getChangeHistories(mutableIssue);
        List<ChangeItemBean> statusChangeItemBeanList = new ArrayList<>();
        List<ReviewCodeBean> reviewCodeBeanList = new ArrayList<>();
        for (ChangeHistory changeHistory : changeHistories) {
            ApplicationUser authorObject = changeHistory.getAuthorObject();
            ApplicationUser toUser = null;
            ApplicationUser fromUser = null;
            List<ChangeItemBean> changeItemBeans = changeHistory.getChangeItemBeans();
            for (ChangeItemBean changeItemBean : changeItemBeans) {
                if ("status".equals(changeItemBean.getField())) {
                    statusChangeItemBeanList.add(changeItemBean);
                    ChangeItemBean assigneeChangeItemBean;
                    List<ChangeItemBean> collect = changeItemBeans.stream().filter(e -> "assignee".equals(e.getField())).collect(Collectors.toList());
                    if (!ObjectUtils.isEmpty(collect)) {
                        assigneeChangeItemBean = collect.get(0);
                        String from = assigneeChangeItemBean.getFrom();
                        String to = assigneeChangeItemBean.getTo();
                        toUser = ComponentAccessor.getUserManager().getUserByKey(to);
                        fromUser = ComponentAccessor.getUserManager().getUserByKey(from);
                        reviewCodeBeanList.add(new ReviewCodeBean(
                                authorObject.getUsername(),
                                authorObject.getDisplayName(),
                                fromUser==null?"":fromUser.getUsername(),
                                fromUser==null?"":fromUser.getDisplayName(),
                                toUser==null?"":toUser.getUsername(),
                                toUser==null?"":toUser.getDisplayName(),
                                "",
                                changeItemBean));
                    }
                }

            }
        }
        statusChangeItemBeanList = statusChangeItemBeanList.stream().sorted(Comparator.comparing(ChangeItemBean::getCreated).reversed()).collect(Collectors.toList());
        return reviewCodeBeanList;
    }

    public void testCaseOption(MutableIssue mutableIssue) {
        CustomField customField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(10301L);
        if (customField == null) {
            return;
        }
        Map<String, Option> customFieldValue = (Map<String, Option>) mutableIssue.getCustomFieldValue(customField);
        Option option = customFieldValue.get(null);
        Option option1 = customFieldValue.get("1");
    }
}
