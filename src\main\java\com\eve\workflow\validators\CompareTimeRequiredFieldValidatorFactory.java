package com.eve.workflow.validators;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.atlassian.jira.component.ComponentAccessor;
import com.atlassian.jira.issue.customfields.CustomFieldType;
import com.atlassian.jira.issue.fields.CustomField;
import com.atlassian.jira.plugin.workflow.AbstractWorkflowPluginFactory;
import com.atlassian.jira.plugin.workflow.WorkflowPluginValidatorFactory;
import com.eve.utils.Constant;
import com.opensymphony.workflow.loader.AbstractDescriptor;
import com.opensymphony.workflow.loader.FunctionDescriptor;
import com.opensymphony.workflow.loader.ValidatorDescriptor;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/10/19
 */
public class CompareTimeRequiredFieldValidatorFactory extends AbstractWorkflowPluginFactory implements WorkflowPluginValidatorFactory {

    @Override
    protected void getVelocityParamsForInput(Map<String, Object> map) {
        List<CustomField> customFieldList = ComponentAccessor.getCustomFieldManager().getCustomFieldObjects();
        List<CustomField> dateCustomFieldListOptions = new ArrayList<>();
        for (CustomField customField : customFieldList) {
            if (Constant.dateFieldType.equals(customField.getCustomFieldType().getKey()) ||
                    Constant.dateTimeFieldType.equals(customField.getCustomFieldType().getKey())) {
                dateCustomFieldListOptions.add(customField);
            }
        }
        map.put("customFieldList", dateCustomFieldListOptions);
        map.put("targetCustomFieldList", customFieldList);
    }

    @Override
    protected void getVelocityParamsForEdit(Map<String, Object> map, AbstractDescriptor abstractDescriptor) {
        ValidatorDescriptor validatorDescriptor = (ValidatorDescriptor) abstractDescriptor;
        JSONObject jsonObject = JSON.parseObject((String) validatorDescriptor.getArgs().get("paramsJson"));

        String startTime = String.valueOf(jsonObject.get("startTime"));
        String endTime = String.valueOf(jsonObject.get("endTime"));
        String compareType = String.valueOf(jsonObject.get("compareType"));
        String targetNum = String.valueOf(jsonObject.get("targetNum"));
        String targetCustomField = String.valueOf(jsonObject.get("targetCustomField"));
        String tipText = String.valueOf(jsonObject.get("tipText"));
        if (ObjectUtils.isEmpty(targetNum)) {
            targetNum = "0";
        }
        List<CustomField> customFieldList = ComponentAccessor.getCustomFieldManager().getCustomFieldObjects();
        List<CustomField> dateCustomFieldListOptions = new ArrayList<>();
        for (CustomField customField : customFieldList) {
            if (Constant.dateFieldType.equals(customField.getCustomFieldType().getKey()) ||
                    Constant.dateTimeFieldType.equals(customField.getCustomFieldType().getKey())) {
                dateCustomFieldListOptions.add(customField);
            }
        }
        map.put("customFieldList", dateCustomFieldListOptions);
        map.put("targetCustomFieldList", customFieldList);
        map.put("startTime", startTime);
        map.put("endTime", endTime);
        map.put("compareType", compareType);
        map.put("targetNum", targetNum);
        map.put("targetCustomField", targetCustomField);
        map.put("tipText", tipText);
    }

    @Override
    protected void getVelocityParamsForView(Map<String, Object> map, AbstractDescriptor abstractDescriptor) {
        ValidatorDescriptor validatorDescriptor = (ValidatorDescriptor) abstractDescriptor;
//        FunctionDescriptor conditionDescriptor = (FunctionDescriptor) abstractDescriptor;
        JSONObject jsonObject = JSON.parseObject((String) validatorDescriptor.getArgs().get("paramsJson"));

        String startTime = String.valueOf(jsonObject.get("startTime"));
        String endTime = String.valueOf(jsonObject.get("endTime"));
        String compareType = String.valueOf(jsonObject.get("compareType"));
        String targetNum = String.valueOf(jsonObject.get("targetNum"));
        String targetCustomField = String.valueOf(jsonObject.get("targetCustomField"));
        String tipText = String.valueOf(jsonObject.get("tipText"));
        if (ObjectUtils.isEmpty(targetNum)) {
            targetNum = "0";
        }
        CustomField startTimeCustomField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(startTime);
        CustomField endTimeCustomField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(endTime);
        CustomField targetCustomFieldCustomField = ComponentAccessor.getCustomFieldManager().getCustomFieldObject(targetCustomField);

        map.put("startTime", startTimeCustomField == null ? "" : startTimeCustomField.getFieldName());
        map.put("endTime", endTimeCustomField == null ? "" : endTimeCustomField.getFieldName());
        map.put("compareType", compareType);
        map.put("targetNum", targetNum);
        map.put("targetCustomField", targetCustomFieldCustomField == null ? "" : targetCustomFieldCustomField.getFieldName());
        map.put("tipText", tipText);
    }

    @Override
    public Map<String, ?> getDescriptorParams(Map<String, Object> map) {
        Map<String,Object> hashMap = new HashMap<>();
        try{
            String[] startTime = (String[]) map.get("startTime");
            String[] endTime = (String[]) map.get("endTime");
            String[] compareType = (String[]) map.get("compareType");
            String[] targetNum = (String[]) map.get("targetNum");
            String[] targetCustomField = (String[]) map.get("targetCustomField");
            String[] tipText = (String[]) map.get("tipText");
            if (ObjectUtils.isEmpty(targetNum[0])) {
                targetNum[0] = "0";
            }
            JSONObject resp = new JSONObject();
            resp.put("startTime",startTime[0]);
            resp.put("endTime",endTime[0]);
            resp.put("compareType",compareType[0]);
            resp.put("targetNum",targetNum[0]);
            resp.put("targetCustomField",targetCustomField[0]);
            resp.put("tipText",tipText[0]);
            hashMap.put("paramsJson", resp.toJSONString());
        }catch (Exception e){
            e.printStackTrace();
        }
        return hashMap;
    }
}
