<?xml version="1.0" encoding="UTF-8"?>

<atlassian-plugin key="${atlassian.plugin.key}" name="${project.name}" plugins-version="2">
    <plugin-info>
        <description>${project.description}</description>
        <version>${project.version}</version>
        <vendor name="${project.organization.name}" url="${project.organization.url}"/>
        <param name="plugin-icon">images/pluginIcon.png</param>
        <param name="plugin-logo">images/pluginLogo.png</param>
    </plugin-info>

    <ao key="deptproject" name="deptprojectAO">
        <entity>com.eve.ao.DeptProjectAo</entity>
        <entity>com.eve.ao.DeptsAo</entity>
        <entity>com.eve.ao.InnerOrderTypeAo</entity>
        <entity>com.eve.ao.CostCenterAo</entity>
        <entity>com.eve.ao.TripRequestMsgAo</entity>
<!--        <entity>com.eve.ao.ApprovalMsgOpenConfigAo</entity>-->
        <!--        <entity>com.eve.ao.ProgressAo</entity>-->
    </ao>

    <!-- add our i18n resource -->
    <resource type="i18n" name="i18n" location="eve-jira"/>
    <!-- add our web resources -->
    <web-resource key="eve-jira-resources" name="eve-jira WebResources">
        <dependency>com.atlassian.auiplugin:ajs</dependency>
        <dependency>com.atlassian.auiplugin:aui-dialog2</dependency>
        <dependency>com.atlassian.auiplugin:aui-select2</dependency>
        <dependency>com.atlassian.auiplugin:aui-date-picker</dependency>
        <dependency>com.atlassian.auiplugin:aui-restfultable</dependency>
        <dependency>com.atlassian.auiplugin:aui-table-sortable</dependency>
        <dependency>com.atlassian.auiplugin:aui-expander</dependency>
        <dependency>com.atlassian.auiplugin:aui-toggle</dependency>
        <transformation extension="soy">
            <transformer key="soyTransformer"/>
        </transformation>
<!--        <resource type="download" name="sys-oa.css" location="/css/sys-oa.css"/>-->
        <resource type="download" name="jQuery.serialize.js" location="/js/jQuery.serialize.js"/>
        <resource type="download" name="reviewList.js" location="/js/reviewList.js"/>
        <resource type="download" name="images/" location="/images"/>
        <resource type="download" name="projectProcess.js" location="/js/projectProcess.js"/>
        <resource type="download" name="createTechTopic.js" location="/js/createTechTopic.js"/>
<!--        <resource type="download" name="dashboard-item-tutorial.js" location="/js/dashboard-item-tutorial.js"/>-->
        <resource type="download" name="templates/dashboard/dashboard-item.soy.js" location="templates/dashboard/dashboard-item.soy"/>
        <resource type="soy" name="EVE.Dashboard.Item.topic.Static" location="templates/dashboard/dashboard-item.soy"/>
        <context>atl.popup</context>
        <context>atl.admin</context>
        <context>atl.general</context>
        <context>atl.dashboard</context>
    </web-resource>

<!--    <web-resource key="eve-jira-action-condition" name="eve-jira-action-condition">-->
<!--        <resource type="download" name="action-condition.js" location="/js/utils/action-condition.js"/>-->
<!--        <resource type="download" name="action-condition.css" location="/css/utils/action-condition.css"/>-->
<!--        <context>eve-jira-action-condition</context>-->
<!--    </web-resource>-->

    <!--新添加的service务必在此声明，否则会导致Rest接口访问失败-->
    <component-import key="ao" interface="com.atlassian.activeobjects.external.ActiveObjects"/>
    <component key="IssueSevice" name="IssueSevice" class="com.eve.services.IssueService"/>
    <component key="DeptProjectService" name="DeptProjectService" class="com.eve.services.DeptProjectService"/>
    <component key="DeptsService" name="DeptsService" class="com.eve.services.DeptsService"/>
    <component key="ServiceUpdateDue" name="ServiceUpdateDue" class="com.eve.services.ServiceUpdateDue"/>
    <component key="ProjectService" name="ProjectService" class="com.eve.services.ProjectService"/>
    <component key="AttachmentService" name="AttachmentService" class="com.eve.services.AttachmentService"/>
    <component key="ServiceUpdateOption" name="ServiceUpdateOption" class="com.eve.services.ServiceUpdateOption"/>
    <component key="ProgressService" name="ProgressService" class="com.eve.services.ProgressService"/>
    <component key="CopyFieldService" name="CopyFieldService" class="com.eve.services.CopyFieldService"/>
    <component key="SapCreateProjectService" name="SapCreateProjectService" class="com.eve.services.SapCreateProjectService"/>
    <component key="ProjectQueryService" name="ProjectQueryService" class="com.eve.services.ProjectQueryService"/>
    <component key="ShowApprovalNodeService" name="ShowApprovalNodeService" class="com.eve.services.ShowApprovalNodeService"/>
    <component key="ProjectReportService" name="ProjectReportService" class="com.eve.services.ProjectReportService"/>
    <component key="JiraCustomTool" name="JiraCustomTool" class="com.eve.utils.JiraCustomTool"/>
    <component key="ApprovalMsgOpenConfigService" name="ApprovalMsgOpenConfigService" class="com.eve.services.ApprovalMsgOpenConfigService"/>
    <component key="ResultService" name="ResultService" class="com.eve.services.ResultService"/>
    <component key="CustomToolService" name="CustomToolService" class="com.eve.services.CustomToolService"/>
    <component key="TripReportService" name="TripReportService" class="com.eve.services.TripReportService"/>
    <component key="TestFailureService" name="TestFailureService" class="com.eve.services.TestFailureService"/>

    <rest key="oa2jiar-rest" path="/oa2jira" version="1.0">
        <description>rest接口</description>
        <package>com.eve.rest</package>
    </rest>


    <jql-function key="getValidProject" i18n-name-key="getValidProject" name="getValidProject"
                  class="com.eve.jql.ValidProject">
        <description key="project.Valid.Check">去除无权限项目JQL函数</description>
        <fname>getValidProject</fname>
        <list>true</list>
    </jql-function>
    <!--    todo jql函数过滤经办人离职的ISSUE JIRA已存在获取未活跃用户函数-->

<!--    <jql-function key="getValidProject" i18n-name-key="getValidProject" name="getValidProject"-->
<!--                  class="com.eve.jql.ValidProject">-->
<!--        <description key="project.Valid.Check">比较两个时间字段，返回比较结果</description>-->
<!--        <fname>getValidProject</fname>-->
<!--        <list>true</list>-->
<!--    </jql-function>-->

<!--    <web-section key="eve_jira_config"
                 name="eve-jira插件项目配置节"
                 location="atl.jira.proj.config"
                 weight="42">
    </web-section>
    <web-item key="approval_process_display_configuration_item" name="approvalProcess"
              section="atl.jira.proj.config/eve_jira_config"
              weight="13">
        <label>审批流程展示配置1</label>
        <link linkId="bmw-defect-SETTING">/secure/ApprovalMsgOpenConfigAction!mainpage.jspa?projectKey=$projectKeyEncoded</link>
        &lt;!&ndash;        <conditions type="AND">
            <condition class="com.codebarrel.jira.plugin.automation.web.IsLicensedCondition"/>
            <condition class="com.codebarrel.jira.plugin.automation.web.IsJira9OrGreater" />
        </conditions>&ndash;&gt;
    </web-item>
    <webwork1 key="approval-msg-open-config-action" name="approval-msg-open-config-action" roles-required="admin">
        <actions>
            <action alias="ApprovalMsgOpenConfigAction"
                    name="com.eve.actions.ApprovalMsgOpenConfigAction">
                <view name="mainpage">
                    /templates/actions/ApprovalMsg/config.vm
                </view>
            </action>
        </actions>
    </webwork1>

    <web-panel key="approval-msg-web-panel"
               location="atl.jira.view.issue.left.context"
               weight="102">
        <resource name="view" type="velocity"
                  location="templates/webpanel/ApprovalMsg/main.vm"/>
        <label>审批流程信息展示</label>
        <condition class="com.eve.webpanel.ApprovalMsgShowCondition"/>
        <context-provider class="com.eve.webpanel.ApprovalMsgContextProvider"/>
    </web-panel>-->


    <web-section key="dept-project-section" location="admin_plugins_menu">
    </web-section>
    <web-section key="dept-project-actions" location="dept-project-section">
        <label>eve-jira插件配置</label>
    </web-section>
    <web-item key="dept-project-section-webitem-config"
              section="dept-project-section/dept-project-actions" weight="0">
        <label>部门项目设置</label>
        <link linkId="bmw-defect-SETTING">/secure/admin/deptProjectAction!mainpage.jspa</link>
    </web-item>

<!--    <web-section key="sap-create-project-section" location="admin_plugins_menu">-->
<!--    </web-section>-->
<!--    <web-section key="sap-create-project-actions" location="sap-create-project-section">-->
<!--        <label>sap立项配置</label>-->
<!--    </web-section>-->
    <web-item key="sap-create-project-section-webitem-config"
              section="dept-project-section/dept-project-actions" weight="1">
        <label>sap立项配置</label>
        <link linkId="sap-create-project-config">/secure/admin/SapCreateProjectAction!mainpage.jspa</link>
    </web-item>

<!--    <web-item key="cateProjects" name="cateProjects" section="system.top.navigation.bar" weight="1">
        <description key="cateProjects.desc">查看项目</description>
        <label>查看项目</label>
        <link linkId="cateProjects">/secure/admin/catesProjectsAction!mainpage.jspa</link>
    </web-item>-->

    <web-item key="project-process-web-item" name="project-process-web-item" section="jira.issue.tools" weight="08">
        <label>填写进展情况</label>
        <styleClass>projectProcessBtn</styleClass>
        <link linkId="projectProcessLink"/>
        <condition class="com.eve.webitem.ProjectProcessShowCondition"/>
    </web-item>

    <web-item key="review-list-web-item" name="review-list-web-item" section="jira.issue.tools" weight="09">
        <label>批量评审</label>
        <styleClass>reviewListBtn</styleClass>
        <link linkId="reviewListLink"/>
        <condition class="com.eve.webitem.ReviewListCondition"/>
    </web-item>

    <web-item key="create-tech-topic-web-item" name="create-tech-topic-web-item" section="system.top.navigation.bar" weight="08">
        <label>技术课题管理平台</label>
        <styleClass>createTechTopicBtn</styleClass>
        <link linkId="createTechTopicLink"/>
        <condition class="com.eve.webitem.CreateTechTopicCondition"/>
    </web-item>

    <web-item key="pbi-platform-item"
               name="pbi-platform-item"
               section="system.top.navigation.bar"
               weight="07">
        <label>PBI平台</label>
        <link linkId="pbi-platform-item">
            <![CDATA[/secure/PBIDashboardAction!mainpage.jspa]]>
        </link>
        <condition class="com.eve.webitem.PBIDashboardCondition"/>
    </web-item>

    <web-section key="pbi-dashboard-web-section"
                 name="pbi-dashboard-web-section"
                 system="true" location="pbi-platform-item">
        <label>看板</label>
    </web-section>

    <web-item key="topic-dashboard"
              section="pbi-platform-item/pbi-dashboard-web-section" weight="101">
        <label>技术课题看板</label>
        <link linkId="topic-dashboard">
            <![CDATA[/secure/PBIDashboardAction!mainpage.jspa]]>
        </link>
        <condition class="com.eve.webitem.PBIDashboardCondition"/>
    </web-item>

    <webwork1 key="pbi-dashboard-action" name="pbi-dashboard-action" roles-required="user">
        <actions>
            <action alias="PBIDashboardAction"
                    name="com.eve.actions.PBIDashboardAction">
                <view name="mainPage">
                    /templates/actions/PBIDashboard/main.vm
                </view>
            </action>
<!--            <action alias="signRecordAction"-->
<!--                    name="com.hktx.jira.attachment.signature.action.SignRecordAction">-->
<!--                <view name="mainpage">-->
<!--                    /attachment/signature/actions/record/signaturerecord.vm-->
<!--                </view>-->
<!--            </action>-->
<!--            <action alias="signDateSettingAction"-->
<!--                    name="com.hktx.jira.attachment.signature.action.SignDateSettingAction">-->
<!--                <view name="mainpage">-->
<!--                    /attachment/signature/actions/datesetting/main.vm-->
<!--                </view>-->
<!--            </action>-->
        </actions>
    </webwork1>

    <web-panel key="stageattachment"
               location="atl.jira.view.issue.left.context"
               weight="101">
        <resource name="view" type="velocity"
                  location="templates/webpanel/stageAttachment.vm"/>
        <label>待签审文件</label>
        <condition class="com.eve.webpanel.AttachmentShowCondition"/>
        <context-provider class="com.eve.webpanel.AttachmentContextProvider"/>
    </web-panel>

    <webwork1 key="deptproject-setting-action" name="deptproject-setting-action" roles-required="admin">
        <actions>
            <action alias="deptProjectAction"
                    name="com.eve.actions.DeptProjectAction">
                <view name="mainpage">
                    /templates/actions/setting/main.vm
                </view>
            </action>
            <action alias="catesProjectsAction"
                    name="com.eve.actions.CatesProjectsAction">
                <view name="mainpage">
                    /templates/project/main.vm
                </view>
            </action>
            <action alias="SapCreateProjectAction"
                    name="com.eve.actions.SapCreateProjectAction">
                <view name="mainpage">
                    /templates/actions/SapCreateProject/main.vm
                </view>
            </action>

        </actions>
    </webwork1>

    <workflow-function key="reviewPostFunctions"
                       name="reviewPostFunctions"
                       i18n-name-key="sys-post-function.name"
                       class="com.eve.workflow.postfunctions.ReviewPostFunctionFactory">
        <description>子任务评审人同步到父任务参与人</description>
        <function-class>com.eve.workflow.postfunctions.ReviewPostFunction</function-class>
        <orderable>true</orderable>
        <unique>false</unique>
        <deletable>true</deletable>
        <resource type="velocity" name="view" location="templates/postfunctions/reviewView.vm"/>
    </workflow-function>
    <workflow-function key="editFileNamePostFunctions"
                       name="editFileNamePostFunctions"
                       class="com.eve.workflow.postfunctions.EditFileNamePostFunctionFactory">
        <description>修改附件文件名</description>
        <function-class>com.eve.workflow.postfunctions.EditFileNamePostFunction</function-class>
        <orderable>true</orderable>
        <unique>false</unique>
        <deletable>true</deletable>
        <resource type="velocity" name="view" location="templates/postfunctions/editFileNameView.vm"/>
    </workflow-function>
    <workflow-function key="UpdateIssueDue"
                       name="UpdateIssueDue"
                       class="com.eve.workflow.postfunctions.UpdateIssueDueFunctionFactory">
        <description>更改到期日</description>
        <function-class>com.eve.workflow.postfunctions.UpdateIssueDueFunction</function-class>
        <orderable>true</orderable>
        <unique>false</unique>
        <deletable>true</deletable>
        <resource type="velocity" name="view" location="templates/postfunctions/updateIssueDue.vm"/>
        <resource type="velocity" name="input-parameters" location="templates/postfunctions/updateIssueDueinput.vm"/>
        <resource type="velocity" name="edit-parameters" location="templates/postfunctions/updateIssueDueedit.vm"/>
    </workflow-function>
    <workflow-function key="UpdateIssueOption"
                       name="eve-P001:UpdateIssueOption"
                       class="com.eve.workflow.postfunctions.UpdateIssueOptionFunctionFactory">
        <description>eve-P001：设置单选值</description>
        <function-class>com.eve.workflow.postfunctions.UpdateIssueOptionFunction</function-class>
        <orderable>true</orderable>
        <unique>false</unique>
        <deletable>true</deletable>
        <resource type="velocity" name="view" location="templates/postfunctions/updateIssueOption.vm"/>
        <resource type="velocity" name="input-parameters" location="templates/postfunctions/updateIssueOptioninput.vm"/>
        <resource type="velocity" name="edit-parameters" location="templates/postfunctions/updateIssueOptionedit.vm"/>
    </workflow-function>
    <workflow-function key="AddIssueNumber"
                       name="eve:AddIssueNumber"
                       class="com.eve.workflow.postfunctions.AddIssueNumberFuctionFactory">
        <description>生成问题编号</description>
        <function-class>com.eve.workflow.postfunctions.AddIssueNumberFunction</function-class>
        <orderable>true</orderable>
        <unique>true</unique>
        <deletable>true</deletable>
        <resource type="velocity" name="view" location="templates/postfunctions/addIssueNumberView.vm"/>
    </workflow-function>
    <!--  复制字段值  -->
    <workflow-function key="CopyField"
                       name="eve-P002:复制字段值到其他问题"
                       class="com.eve.workflow.postfunctions.CopyFieldFunctionFactory">
        <description>eve-P002：复制特定问题的指定字段值到特定问题的指定字段</description>
        <function-class>com.eve.workflow.postfunctions.CopyFieldFunction</function-class>
        <orderable>true</orderable>
        <unique>false</unique>
        <deletable>true</deletable>
        <resource type="velocity" name="view" location="templates/postfunctions/CopyField.vm"/>
        <resource type="velocity" name="input-parameters" location="templates/postfunctions/CopyFieldEdit.vm"/>
        <resource type="velocity" name="edit-parameters" location="templates/postfunctions/CopyFieldEdit.vm"/>
    </workflow-function>
    <!-- 从父任务复制多个字段到当前任务-->
    <workflow-function key="CopyFieldsFromParent"
                       name="eve-P003:从父任务复制多个字段到当前任务"
                       class="com.eve.workflow.postfunctions.CopyFieldsFromParentFunctionFactory">
        <description>eve-P003:从父任务复制多个字段到当前任务</description>
        <function-class>com.eve.workflow.postfunctions.CopyFieldsFromParentFunction</function-class>
        <orderable>true</orderable>
        <unique>false</unique>
        <deletable>true</deletable>
        <resource type="velocity" name="view" location="templates/postfunctions/CopyFieldsFromParent/view.vm"/>
        <resource type="velocity" name="input-parameters" location="templates/postfunctions/CopyFieldsFromParent/input.vm"/>
        <resource type="velocity" name="edit-parameters" location="templates/postfunctions/CopyFieldsFromParent/edit.vm"/>
    </workflow-function>
    <workflow-function key="SetAssignFromMultiUser"
                       name="eve-P004:选定多用户字段进行循环审批"
                       class="com.eve.workflow.postfunctions.SetAssignFromMultiUserFunctionFactory">
        <description>eve-P004:将多用户字段的第一个用户移动到经办人</description>
        <function-class>com.eve.workflow.postfunctions.SetAssignFromMultiUserFunction</function-class>
        <orderable>true</orderable>
        <unique>false</unique>
        <deletable>true</deletable>
        <resource type="velocity" name="view" location="templates/postfunctions/SetAssignFromMultiUser/view.vm"/>
        <resource type="velocity" name="input-parameters" location="templates/postfunctions/SetAssignFromMultiUser/input.vm"/>
        <resource type="velocity" name="edit-parameters" location="templates/postfunctions/SetAssignFromMultiUser/edit.vm"/>
    </workflow-function>
    <workflow-function key="SetUserFieldValue"
                       name="eve-P005:设置指定用户字段为指定人"
                       class="com.eve.workflow.postfunctions.SetUserFieldValueFunctionFactory">
        <description>eve-P005:设置指定用户字段为指定人</description>
        <function-class>com.eve.workflow.postfunctions.SetUserFieldValueFunction</function-class>
        <orderable>true</orderable>
        <unique>false</unique>
        <deletable>true</deletable>
        <resource type="velocity" name="view" location="templates/postfunctions/SetUserFieldValue/view.vm"/>
        <resource type="velocity" name="input-parameters" location="templates/postfunctions/SetUserFieldValue/edit.vm"/>
        <resource type="velocity" name="edit-parameters" location="templates/postfunctions/SetUserFieldValue/edit.vm"/>
    </workflow-function>
    <workflow-function key="SendEmailFunction"
                       name="eve-P006:发送邮件"
                       class="com.eve.workflow.postfunctions.SendEmailFunctionFactory">
        <description>eve-P006:发送邮件后处理，可指定接收邮件的用户字段，需附加的字段信息</description>
        <function-class>com.eve.workflow.postfunctions.SendEmailFunction</function-class>
        <orderable>true</orderable>
        <unique>false</unique>
        <deletable>true</deletable>
        <resource type="velocity" name="view" location="templates/postfunctions/SendEmailFunction/view.vm"/>
        <resource type="velocity" name="input-parameters" location="templates/postfunctions/SendEmailFunction/edit.vm"/>
        <resource type="velocity" name="edit-parameters" location="templates/postfunctions/SendEmailFunction/edit.vm"/>
    </workflow-function>
    <workflow-function key="RunNextTransitionFunction"
                       name="eve-P007:执行下一转换"
                       class="com.eve.workflow.postfunctions.RunNextTransitionFunctionFactory">
        <description>eve-P007:满足JQL条件时执行下一个转换，用于跳过流程</description>
        <function-class>com.eve.workflow.postfunctions.RunNextTransitionFunction</function-class>
        <orderable>true</orderable>
        <unique>true</unique>
        <deletable>true</deletable>
        <resource type="velocity" name="view" location="templates/postfunctions/RunNextTransition/view.vm"/>
        <resource type="velocity" name="input-parameters" location="templates/postfunctions/RunNextTransition/edit.vm"/>
        <resource type="velocity" name="edit-parameters" location="templates/postfunctions/RunNextTransition/edit.vm"/>
    </workflow-function>
    <workflow-function key="ChangeIssueDescriptionFunction"
                       name="eve-P008:修改问题描述"
                       class="com.eve.workflow.postfunctions.ChangeIssueDescriptionFunctionFactory">
        <description>eve-P008:满足条件时修改问题的描述</description>
        <function-class>com.eve.workflow.postfunctions.ChangeIssueDescriptionFunction</function-class>
        <orderable>true</orderable>
        <unique>false</unique>
        <deletable>true</deletable>
        <resource type="velocity" name="view" location="templates/postfunctions/ChangeIssueDescription/view.vm"/>
        <resource type="velocity" name="input-parameters" location="templates/postfunctions/ChangeIssueDescription/edit.vm"/>
        <resource type="velocity" name="edit-parameters" location="templates/postfunctions/ChangeIssueDescription/edit.vm"/>
    </workflow-function>
    <workflow-function key="SelectFieldToMultiApproval"
                       name="eve-P009:选定两个字段用于会签"
                       class="com.eve.workflow.postfunctions.SelectFieldToMultiApprovalFunctionFactory">
        <description>eve-P009:会签，复制一个在源字段，不在记录字段的人，到经办人，同时把该人员添加到记录字段</description>
        <function-class>com.eve.workflow.postfunctions.SelectFieldToMultiApprovalFunction</function-class>
        <orderable>true</orderable>
        <unique>true</unique>
        <deletable>true</deletable>
        <resource type="velocity" name="view" location="templates/postfunctions/SelectFieldToMultiApproval/view.vm"/>
        <resource type="velocity" name="input-parameters" location="templates/postfunctions/SelectFieldToMultiApproval/edit.vm"/>
        <resource type="velocity" name="edit-parameters" location="templates/postfunctions/SelectFieldToMultiApproval/edit.vm"/>
    </workflow-function>
    <workflow-function key="RunTransitionByParams"
                       name="eve-P010:根据填写的参数执行转换"
                       class="com.eve.workflow.postfunctions.RunTransitionByParamsFunctionFactory">
        <description>eve-P010:执行与当前ISSUE具有选定关系且在指定状态的ISSUE的指定名称的转换</description>
        <function-class>com.eve.workflow.postfunctions.RunTransitionByParamsFunction</function-class>
        <orderable>true</orderable>
        <unique>false</unique>
        <deletable>true</deletable>
        <resource type="velocity" name="view" location="templates/postfunctions/RunTransitionByParams/view.vm"/>
        <resource type="velocity" name="input-parameters" location="templates/postfunctions/RunTransitionByParams/edit.vm"/>
        <resource type="velocity" name="edit-parameters" location="templates/postfunctions/RunTransitionByParams/edit.vm"/>
    </workflow-function>
    <workflow-function key="CopyUserField"
                       name="eve-P011:复制用户字段值到其他问题"
                       class="com.eve.workflow.postfunctions.CopyUserFieldFunctionFactory">
        <description>eve-P011：复制特定问题的用户字段值到特定问题的用户字段</description>
        <function-class>com.eve.workflow.postfunctions.CopyUserFieldFunction</function-class>
        <orderable>true</orderable>
        <unique>false</unique>
        <deletable>true</deletable>
        <resource type="velocity" name="view" location="templates/postfunctions/CopyUserField/view.vm"/>
        <resource type="velocity" name="input-parameters" location="templates/postfunctions/CopyUserField/edit.vm"/>
        <resource type="velocity" name="edit-parameters" location="templates/postfunctions/CopyUserField/edit.vm"/>
    </workflow-function>
    <workflow-function key="AfterTimeRunTransition"
                       name="eve-P012:延迟指定时间后，执行给定的转换"
                       class="com.eve.workflow.postfunctions.AfterTimeRunTransitionFunctionFactory">
        <description>eve-P012：延迟指定时间后，执行给定的转换</description>
        <function-class>com.eve.workflow.postfunctions.AfterTimeRunTransitionFunction</function-class>
        <orderable>true</orderable>
        <unique>false</unique>
        <deletable>true</deletable>
        <resource type="velocity" name="view" location="templates/postfunctions/AfterTimeRunTransition/view.vm"/>
        <resource type="velocity" name="input-parameters" location="templates/postfunctions/AfterTimeRunTransition/edit.vm"/>
        <resource type="velocity" name="edit-parameters" location="templates/postfunctions/AfterTimeRunTransition/edit.vm"/>
    </workflow-function>
    <workflow-function key="CreateSubIssue"
                       name="eve-P013:填写子任务创建条件，创建子任务"
                       class="com.eve.workflow.postfunctions.CreateSubIssueFunctionFactory">
        <description>eve-P013：填写子任务创建条件，创建子任务</description>
        <function-class>com.eve.workflow.postfunctions.CreateSubIssueFunction</function-class>
        <orderable>true</orderable>
        <unique>false</unique>
        <deletable>true</deletable>
        <resource type="velocity" name="view" location="templates/postfunctions/CreateSubIssue/view.vm"/>
        <resource type="velocity" name="input-parameters" location="templates/postfunctions/CreateSubIssue/edit.vm"/>
        <resource type="velocity" name="edit-parameters" location="templates/postfunctions/CreateSubIssue/edit.vm"/>
    </workflow-function>
    <workflow-function key="AddPageToPdf"
                       name="eve-P014:添加页面到指定分类的pdf文件"
                       class="com.eve.workflow.postfunctions.AddPageToPdfFunctionFactory">
        <description>eve-P014：添加页面到指定分类的pdf文件</description>
        <function-class>com.eve.workflow.postfunctions.AddPageToPdfFunction</function-class>
        <orderable>true</orderable>
        <unique>false</unique>
        <deletable>true</deletable>
        <resource type="velocity" name="view" location="templates/postfunctions/AddPageToPdf/view.vm"/>
        <resource type="velocity" name="input-parameters" location="templates/postfunctions/AddPageToPdf/edit.vm"/>
        <resource type="velocity" name="edit-parameters" location="templates/postfunctions/AddPageToPdf/edit.vm"/>
    </workflow-function>
    <workflow-function key="AddNumbersToField"
                       name="eve-P015:计算合计分数"
                       class="com.eve.workflow.postfunctions.AddNumbersToFieldFunctionFactory">
        <description>eve-P015：选择多个源字段，计算合计分数到指定字段</description>
        <function-class>com.eve.workflow.postfunctions.AddNumbersToFieldFunction</function-class>
        <orderable>true</orderable>
        <unique>false</unique>
        <deletable>true</deletable>
        <resource type="velocity" name="view" location="templates/postfunctions/AddNumbersToField/view.vm"/>
        <resource type="velocity" name="input-parameters" location="templates/postfunctions/AddNumbersToField/edit.vm"/>
        <resource type="velocity" name="edit-parameters" location="templates/postfunctions/AddNumbersToField/edit.vm"/>
    </workflow-function>
    <workflow-function key="CalcAverageValue"
                       name="eve-P016:计算平均数值"
                       class="com.eve.workflow.postfunctions.CalcAverageValueFunctionFactory">
        <description>eve-P016：选择多个源字段，计算平均数值到指定字段</description>
        <function-class>com.eve.workflow.postfunctions.CalcAverageValueFunction</function-class>
        <orderable>true</orderable>
        <unique>false</unique>
        <deletable>true</deletable>
        <resource type="velocity" name="view" location="templates/postfunctions/CalcAverageValue/view.vm"/>
        <resource type="velocity" name="input-parameters" location="templates/postfunctions/CalcAverageValue/edit.vm"/>
        <resource type="velocity" name="edit-parameters" location="templates/postfunctions/CalcAverageValue/edit.vm"/>
    </workflow-function>
    <workflow-function key="RemovePdfPage"
                       name="eve-P017:移除指定分类的pdf文件的指定页"
                       class="com.eve.workflow.postfunctions.RemovePdfPageFunctionFactory">
        <description>eve-P017：移除指定分类的pdf文件的指定页</description>
        <function-class>com.eve.workflow.postfunctions.RemovePdfPageFunction</function-class>
        <orderable>true</orderable>
        <unique>false</unique>
        <deletable>true</deletable>
        <resource type="velocity" name="view" location="templates/postfunctions/RemovePdfPage/view.vm"/>
        <resource type="velocity" name="input-parameters" location="templates/postfunctions/RemovePdfPage/edit.vm"/>
        <resource type="velocity" name="edit-parameters" location="templates/postfunctions/RemovePdfPage/edit.vm"/>
    </workflow-function>
    <workflow-function key="AddUsersToMultiUserField"
                       name="eve-P018:添加选择的用户到用户多选字段"
                       class="com.eve.workflow.postfunctions.AddUsersToMultiUserFieldFunctionFactory">
        <description>eve-P018:添加选择的用户到用户多选字段</description>
<!--        <description>eve-P005:设置指定用户字段为指定人</description>-->
        <function-class>com.eve.workflow.postfunctions.AddUsersToMultiUserFieldFunction</function-class>
        <orderable>true</orderable>
        <unique>false</unique>
        <deletable>true</deletable>
        <resource type="velocity" name="view" location="templates/postfunctions/AddUsersToMultiUserField/view.vm"/>
        <resource type="velocity" name="input-parameters" location="templates/postfunctions/AddUsersToMultiUserField/edit.vm"/>
        <resource type="velocity" name="edit-parameters" location="templates/postfunctions/AddUsersToMultiUserField/edit.vm"/>
    </workflow-function>

    <workflow-function key="ProjectCodeCreate"
                       name="eve:动力电池平台/课题项目代号创建"
                       class="com.eve.workflow.postfunctions.ProjectCodeCreateFunctionFactory">
        <description>平台/课题项目代号创建</description>
        <function-class>com.eve.workflow.postfunctions.ProjectCodeCreateFunction</function-class>
        <orderable>true</orderable>
        <unique>true</unique>
        <deletable>true</deletable>
        <resource type="velocity" name="view" location="templates/postfunctions/ProjectCodeCreate/view.vm"/>
    </workflow-function>
    <workflow-function key="SapCreateProject"
                       name="eve:动力电池平台项目sap立项"
                       class="com.eve.workflow.postfunctions.SapCreateProjectFunctionFactory">
        <description>动力电池平台项目sap立项</description>
        <function-class>com.eve.workflow.postfunctions.SapCreateProjectFunction</function-class>
        <orderable>true</orderable>
        <unique>true</unique>
        <deletable>true</deletable>
        <resource type="velocity" name="view" location="templates/postfunctions/SapCreateProject/view.vm"/>
    </workflow-function>
    <workflow-function key="TestFailureManageApproval"
                       name="eve:测试失效管理审核通过"
                       class="com.eve.workflow.postfunctions.TestFailureManageApprovalFunctionFactory">
        <description>测试失效管理审核通过，结果回传PBI系统</description>
        <function-class>com.eve.workflow.postfunctions.TestFailureManageApprovalFunction</function-class>
        <orderable>true</orderable>
        <unique>true</unique>
        <deletable>true</deletable>
        <resource type="velocity" name="view" location="templates/postfunctions/TestFailureManageApproval/view.vm"/>
        <resource type="velocity" name="input-parameters" location="templates/postfunctions/TestFailureManageApproval/edit.vm"/>
        <resource type="velocity" name="edit-parameters" location="templates/postfunctions/TestFailureManageApproval/edit.vm"/>
    </workflow-function>
    <workflow-function key="ArchiveAttachment"
                       name="eve:归档附件到PBI文件库"
                       class="com.eve.workflow.postfunctions.ArchiveAttachmentFunctionFactory">
        <description>eve：归档附件到PBI文件库</description>
        <function-class>com.eve.workflow.postfunctions.ArchiveAttachmentFunction</function-class>
        <orderable>true</orderable>
        <unique>true</unique>
        <deletable>true</deletable>
        <resource type="velocity" name="view" location="templates/postfunctions/ArchiveAttachment/view.vm"/>
        <resource type="velocity" name="input-parameters" location="templates/postfunctions/ArchiveAttachment/edit.vm"/>
        <resource type="velocity" name="edit-parameters" location="templates/postfunctions/ArchiveAttachment/edit.vm"/>
    </workflow-function>
<!--    <workflow-function key="ClearCustomFieldInRelatedIssue"
                       name="eve-P008:清除关联问题的指定自定义字段"
                       class="com.eve.workflow.postfunctions.ClearCustomFieldInRelatedIssueFunctionFactory">
        <description>eve-P008:清除关联问题的指定自定义字段</description>
        <function-class>com.eve.workflow.postfunctions.ClearCustomFieldInRelatedIssueFunction</function-class>
        <orderable>true</orderable>
        <unique>false</unique>
        <deletable>true</deletable>
        <resource type="velocity" name="view" location="templates/postfunctions/ClearCustomFieldInRelatedIssue/view.vm"/>
        <resource type="velocity" name="input-parameters" location="templates/postfunctions/ClearCustomFieldInRelatedIssue/input.vm"/>
        <resource type="velocity" name="edit-parameters" location="templates/postfunctions/ClearCustomFieldInRelatedIssue/edit.vm"/>
    </workflow-function>-->

    <workflow-condition key="JudgeUserCountOfMultiUser"
                        name="eve-C001:判断多用户字段用户数量"
                        class="com.eve.workflow.conditions.JudgeUserCountOfMultiUserConditionFactory">
        <description>选定多用户字段，比较方式，输入用户数量，返回比较结果</description>
        <condition-class>com.eve.workflow.conditions.JudgeUserCountOfMultiUserCondition</condition-class>
        <resource type="velocity" name="view" location="templates/conditions/JudgeUserCountOfMultiUser/view.vm"/>
        <resource type="velocity" name="input-parameters" location="templates/conditions/JudgeUserCountOfMultiUser/edit.vm"/>
        <resource type="velocity" name="edit-parameters" location="templates/conditions/JudgeUserCountOfMultiUser/edit.vm"/>
    </workflow-condition>
    <workflow-condition key="matchJqlInIssue"
                        name="eve-C002:根据JQL语句判断当前是否满足条件"
                        class="com.eve.workflow.conditions.MatchJqlInIssueConditionFactory">
        <description>根据JQL语句判断当前是否满足条件</description>
        <condition-class>com.eve.workflow.conditions.MatchJqlInIssueCondition</condition-class>
        <resource type="velocity" name="view" location="templates/conditions/MatchJqlInIssue/view.vm"/>
        <resource type="velocity" name="input-parameters" location="templates/conditions/MatchJqlInIssue/edit.vm"/>
        <resource type="velocity" name="edit-parameters" location="templates/conditions/MatchJqlInIssue/edit.vm"/>
    </workflow-condition>
    <workflow-condition key="JudgeFieldHasValue"
                        name="eve-C003:选定一个字段，根据字段值返回判断结果"
                        class="com.eve.workflow.conditions.JudgeFieldHasValueConditionFactory">
        <description>选定一个字段，根据字段值是否为空返回是否显示转换</description>
        <condition-class>com.eve.workflow.conditions.JudgeFieldHasValueCondition</condition-class>
        <resource type="velocity" name="view" location="templates/conditions/JudgeFieldHasValue/view.vm"/>
        <resource type="velocity" name="input-parameters" location="templates/conditions/JudgeFieldHasValue/edit.vm"/>
        <resource type="velocity" name="edit-parameters" location="templates/conditions/JudgeFieldHasValue/edit.vm"/>
    </workflow-condition>
    <workflow-condition key="CompareFieldValue"
                        name="eve-C004:比较字段"
                        class="com.eve.workflow.conditions.CompareFieldValueConditionFactory">
        <description>选定两个个字段，根据字段值是否相等返回是否显示转换</description>
        <condition-class>com.eve.workflow.conditions.CompareFieldValueCondition</condition-class>
        <resource type="velocity" name="view" location="templates/conditions/CompareFieldValue/view.vm"/>
        <resource type="velocity" name="input-parameters" location="templates/conditions/CompareFieldValue/edit.vm"/>
        <resource type="velocity" name="edit-parameters" location="templates/conditions/CompareFieldValue/edit.vm"/>
    </workflow-condition>
    <workflow-condition key="MultiselectContainOptionCondition"
                        name="eve-C005:选项字段，多选选项，任一选项符合条件成立"
                        class="com.eve.workflow.conditions.MultiselectContainOptionConditionFactory">
        <description>指定字段包含选择的选项时成立</description>
        <condition-class>com.eve.workflow.conditions.MultiselectContainOptionCondition</condition-class>
        <resource type="velocity" name="view" location="templates/conditions/MultiselectContainOption/view.vm"/>
        <resource type="velocity" name="input-parameters" location="templates/conditions/MultiselectContainOption/edit.vm"/>
        <resource type="velocity" name="edit-parameters" location="templates/conditions/MultiselectContainOption/edit.vm"/>
    </workflow-condition>
    <workflow-condition key="MultiApprovalCheck"
                        name="eve-C006:判断是否需要进行多人签审"
                        class="com.eve.workflow.conditions.MultiApprovalCheckConditionFactory">
        <description>选定两个多用户字段，判断是否需要进行多人签审</description>
        <condition-class>com.eve.workflow.conditions.MultiApprovalCheckCondition</condition-class>
        <resource type="velocity" name="view" location="templates/conditions/MultiApprovalCheck/view.vm"/>
        <resource type="velocity" name="input-parameters" location="templates/conditions/MultiApprovalCheck/edit.vm"/>
        <resource type="velocity" name="edit-parameters" location="templates/conditions/MultiApprovalCheck/edit.vm"/>
    </workflow-condition>
    <workflow-validator key="CheckSapCreateProjectParam"
                        name="eve-V001:校验SAP立项参数合法性"
                        class="com.eve.workflow.validators.CheckSapCreateProjectParamValidatorFactory">
        <description>校验SAP立项参数合法性</description>
        <validator-class>com.eve.workflow.validators.CheckSapCreateProjectParamValidator</validator-class>
        <resource type="velocity" name="view" location="templates/validators/CheckSapCreateProjectParam/view.vm"/>
    </workflow-validator>
    <workflow-validator key="compareTimeRequiredField"
                        name="eve-V002:两时间之差与指定数值比较，成立则选择的字段为必填"
                        class="com.eve.workflow.validators.CompareTimeRequiredFieldValidatorFactory">
        <description>选择两个时间字段，两字段之差与指定数值比较，成立则选择的字段为必填，可定制提示语</description>
        <validator-class>com.eve.workflow.validators.CompareTimeRequiredFieldValidator</validator-class>
        <resource type="velocity" name="view" location="templates/validators/CompareTimeRequiredField/view.vm"/>
        <resource type="velocity" name="input-parameters" location="templates/validators/CompareTimeRequiredField/edit.vm"/>
        <resource type="velocity" name="edit-parameters" location="templates/validators/CompareTimeRequiredField/edit.vm"/>
    </workflow-validator>
    <workflow-validator key="checkCateHaveRegExMatchFile"
                        name="eve-V003:检查指定附件分类"
                        class="com.eve.workflow.validators.CheckCateHaveRegExMatchFileValidatorFactory">
        <description>检查指定附件分类下是否存在满足所输入正则表达式的文件，可定制提示语</description>
        <validator-class>com.eve.workflow.validators.CheckCateHaveRegExMatchFileValidator</validator-class>
        <resource type="velocity" name="view" location="templates/validators/CheckCateHaveRegExMatchFile/view.vm"/>
        <resource type="velocity" name="input-parameters" location="templates/validators/CheckCateHaveRegExMatchFile/edit.vm"/>
        <resource type="velocity" name="edit-parameters" location="templates/validators/CheckCateHaveRegExMatchFile/edit.vm"/>
    </workflow-validator>
    <workflow-validator key="checkFieldIsNotEmpty"
                        name="eve-V004:校验不为空的字段"
                        class="com.eve.workflow.validators.CheckFieldIsNotEmptyValidatorFactory">
        <description>校验选定字段必须有值</description>
        <validator-class>com.eve.workflow.validators.CheckFieldIsNotEmptyValidator</validator-class>
        <resource type="velocity" name="view" location="templates/validators/CheckFieldIsNotEmpty/view.vm"/>
        <resource type="velocity" name="input-parameters" location="templates/validators/CheckFieldIsNotEmpty/edit.vm"/>
        <resource type="velocity" name="edit-parameters" location="templates/validators/CheckFieldIsNotEmpty/edit.vm"/>
    </workflow-validator>
<!--    todo-->
    <workflow-validator key="checkMultiUserCount"
                        name="eve-V005:检查多用户字段数量，是否重复填写用户"
                        class="com.eve.workflow.validators.CheckMultiUserCountValidatorFactory">
        <description>根据输入的条件和数量检查多用户字段，根据输入的用户字段检查是否重复输入用户</description>
        <validator-class>com.eve.workflow.validators.CheckMultiUserCountValidator</validator-class>
        <resource type="velocity" name="view" location="templates/validators/CheckMultiUserCount/view.vm"/>
        <resource type="velocity" name="input-parameters" location="templates/validators/CheckMultiUserCount/edit.vm"/>
        <resource type="velocity" name="edit-parameters" location="templates/validators/CheckMultiUserCount/edit.vm"/>
    </workflow-validator>
    <workflow-validator key="checkInterfaceTransition"
                        name="eve-V006:校验该转换是否由接口执行"
                        class="com.eve.workflow.validators.CheckInterfaceTransitionValidatorFactory">
        <description>校验该转换是否由接口执行，可自定义提示语</description>
        <validator-class>com.eve.workflow.validators.CheckInterfaceTransitionValidator</validator-class>
        <resource type="velocity" name="view" location="templates/validators/CheckInterfaceTransition/view.vm"/>
        <resource type="velocity" name="input-parameters" location="templates/validators/CheckInterfaceTransition/edit.vm"/>
        <resource type="velocity" name="edit-parameters" location="templates/validators/CheckInterfaceTransition/edit.vm"/>
    </workflow-validator>
    <customfield-type key="InnerOrderTypeCustomField" name="InnerOrderTypeCustomField"
                      class="com.eve.customfield.InnerOrderTypeCustomField">
        <description>内部订单类型选择自定义字段</description>
        <valid-searcher></valid-searcher>
        <resource type="velocity" name="edit"
                  location="templates/customefields/InnerOrderTypeCustomField/edit.vm"/>
        <resource type="velocity" name="view"
                  location="templates/customefields/InnerOrderTypeCustomField/view.vm"/>
        <resource type="velocity" name="column-view"
                  location="templates/customefields/InnerOrderTypeCustomField/edit.vm"/>
        <resource type="velocity" name="xml"
                  location="templates/customefields/InnerOrderTypeCustomField/edit.vm"/>
    </customfield-type>
    <customfield-type key="CostCenterCustomField" name="CostCenterCustomField"
                      class="com.eve.customfield.CostCenterCustomField">

        <description>成本中心选择自定义字段</description>
        <valid-searcher></valid-searcher>
        <resource type="velocity" name="edit"
                  location="templates/customefields/CostCenterCustomField/edit.vm"/>
        <resource type="velocity" name="view"
                  location="templates/customefields/CostCenterCustomField/view.vm"/>
        <resource type="velocity" name="column-view"
                  location="templates/customefields/CostCenterCustomField/edit.vm"/>
        <resource type="velocity" name="xml"
                  location="templates/customefields/CostCenterCustomField/edit.vm"/>
    </customfield-type>

    <customfield-type key="multiCascadeCustomField" name="CostCenterCustomField"
                      class="com.eve.customfield.CostCenterCustomField">

        <description>自定义层级级联字段</description>
        <valid-searcher></valid-searcher>
        <resource type="velocity" name="edit"
                  location="templates/customefields/CostCenterCustomField/edit.vm"/>
        <resource type="velocity" name="view"
                  location="templates/customefields/CostCenterCustomField/view.vm"/>
        <resource type="velocity" name="column-view"
                  location="templates/customefields/CostCenterCustomField/edit.vm"/>
        <resource type="velocity" name="xml"
                  location="templates/customefields/CostCenterCustomField/edit.vm"/>
    </customfield-type>
<!--    <customfield-searcher key="multiselectsearcher" name="Multi Select Searcher"-->
<!--                          i18n-name-key="admin.customfield.searcher.multiselectsearcher.name"-->
<!--                          class="com.atlassian.jira.issue.customfields.searchers.MultiSelectSearcher">-->
<!--        <description key="admin.customfield.searcher.multiselectsearcher.desc">Search for multiple values using a single select list.</description>-->

<!--        <resource type="velocity" name="search" location="templates/plugins/fields/edit-searcher/search-multiselect.vm"/>-->
<!--        <resource type="velocity" name="view" location="templates/plugins/fields/view-searcher/view-searcher-multioption.vm"/>-->
<!--        <resource type="velocity" name="label" location="templates/plugins/fields/view-searcher/label-searcher-basictext.vm"/>-->
<!--        <valid-customfield-type package="com.eve.customfield.CostCenterCustomField"" key="CostCenterCustomField"/>-->
<!--    </customfield-searcher>-->

    <dashboard-item key="PBI-TOPIC-KANBAN"
                    i18n-name-key="PBI-TOPIC-KANBAN2"
                    name="PBI-TOPIC-KANBAN2">
        <definition>
            <title>PBI-TOPIC-KANBAN3</title>
            <categories>
                <category>Jira</category>
            </categories>
            <author>
                <name>EVE</name>
            </author>
<!--            <thumbnail location="/download/resources/${atlassian.plugin.key}:eve-jira/images/pluginLogo.png"/>-->
        </definition>
        <description>技术课题pbi看板介绍</description>
        <resource name="view" type="soy" location=":eve-jira-resources/EVE.Dashboard.Item.topic.Static"/>
        <context-provider class="com.eve.dashboard.DashboardItemContextProvider"/>
<!--        <amd-module>dashboard-items/tutorial</amd-module>-->
        <condition class="com.atlassian.jira.plugin.webfragment.conditions.UserLoggedInCondition"/>
    </dashboard-item>
</atlassian-plugin>