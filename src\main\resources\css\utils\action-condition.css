

#actionConditionContainerTitle {
	margin-bottom: 15px;
	font-size: 25px;
}

#actionConditionContainer {
	width: 500px;
	padding-left: 50px;
	padding-right: 50px;
	padding-top: 15px;
	padding-bottom: 20px;
	border: 1px solid #dfe1e6;
	border-radius: 3px;
	margin-top: 15px;
}

#jqlConditionError {
	color: red;
	display: none;
	padding-top: 15px;
}

#jqlConditionApproval {
	color: green;
	display: none;
	padding-top: 15px;
}

#jqlConditionValidate {
	color: #036;
	padding-left: 25px;
}

#jqlConditionValidate:hover {
	cursor: pointer;
}

#jqlConditionContainer {
	text-align: right;
	width: 400px;
	display: none;
	margin-top: 15px;
}

#jqlCondition {
	width: 400px;
	height: 30px;
	padding-left: 26px;
	padding-right: 35px;
	resize: none;
	display: table-row;
	max-width: inherit;
	border: 2px solid #dfe1e6;
	border-radius: 3.01px;
	box-sizing: border-box;
	font-size: inherit;
	font-family: inherit;
	line-height: 1.4285714285714;
	padding: 3px 4px;
}

#jqlCondition:hover {
	background-color: #ebecf0;
	border-color: #dfe1e6;
	color: #172b4d;
}

#jqlCondition:focus {
	background-color: #fff;
	outline: none;
	border-width: 2px;
	border-color: #4c9aff;
}

#selectExecutorIsOrIsNot-input {
	background-color: #fff;
}

#s2id_selectExecutorIsOrIsNot {
	width: 150px;
}

#selectExecutorMembershipType-input {
	background-color: #fff;
}

#s2id_selectExecutorMembershipType {
	width: 150px;
}

#selectMembershipRoles-input {
	background-color: #fff;
}

#s2id_selectMembershipRoles {
	width: 150px;
}

#selectMembershipGroups-field {
	background: white;
	height: 30px;
	width: 150px;
	padding-left: 26px !important;
	padding-right: 35px;
	resize: none;
	display: table-row;
	max-width: inherit;
	border: 2px solid #dfe1e6;
	border-radius: 3.01px;
	box-sizing: border-box;
	font-size: inherit;
	font-family: inherit;
	line-height: 1.4285714285714;
	padding: 3px 4px;
}

#selectMembershipGroups-field:hover {
	background-color: #ebecf0;
	border-color: #dfe1e6;
	color: #172b4d;
}

#selectMembershipGroups-field:focus {
	background-color: #fff;
	outline: none;
	border-width: 2px;
	border-color: #4c9aff;
}

#selectMembershipGroupsContainer,
#selectMembershipRolesContainer {
	display: none;
}

.wholeContainer {
	border: 1px solid #dfe1e6;
	border-radius: 3px;
	padding: 15px;
	margin-top: 5px;
}

#jqlConditionLabel {
	width: 40px !important;
}

#jqlCondition {
	margin-left: -90px !important;
}

.required {
	color: red;
}

.requiredMessage {
	color: red !important;
	display: none;
}