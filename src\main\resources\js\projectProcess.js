var dialog ='<section id="processdialog" class="aui-dialog2 aui-dialog2-medium aui-layer" data-aui-modal="true" role="dialog" aria-hidden="true" style=\'min-height: initial;top: 20px;\'>';
dialog+='<header class="aui-dialog2-header">';
dialog+='<h2 class="aui-dialog2-header-main">进度填写</h2>';
dialog+='</header>';
dialog+='<div class="aui-dialog3-content">';
dialog+='<form class="aui" id="processInput">';
dialog+='<input type="hidden" name="issueId" id=\'issueId\' />';
dialog+='<input type="hidden" name="userName" id=\'userName\' />';
dialog+='<div class="field-group">';
dialog+='<label for="projectProcess">进度</label>';
dialog += '<textarea id="projectProcess" name="projectProcess" class="medium-long-field" cols="60" rows="3"></textarea>';
// dialog += '<textarea id="projectProcess" name="projectProcess" class="medium-long-field" cols="60" rows="3">' + $('#customfield_11235-val')[0].innerText + '</textarea>';
dialog+='</div>';
dialog+='<div class="field-group">';
dialog+='<label for="projectNextStep">下周工作计划</label>';
dialog += '<textarea id="projectNextStep" name="projectNextStep" class="medium-long-field" cols="60" rows="3"></textarea>';
// dialog+='<textarea id="projectNextStep" name="projectNextStep" class="medium-long-field" cols="60" rows="3">'+$('#customfield_11238-val')[0].innerText+'</textarea>';
dialog+='</div>';
dialog+='<div class="field-group">';
dialog+='<label for="projectRisk">风险</label>';
dialog += '<textarea id="projectRisk" name="projectRisk" class="medium-long-field" cols="60" rows="3"></textarea>';
// dialog+='<textarea id="projectRisk" name="projectRisk" class="medium-long-field" cols="60" rows="3">'+$('#customfield_11236-val')[0].innerText+'</textarea>';
dialog+='</div>';
dialog+='<div class="field-group">';
dialog+='<label for="projectStrategy">对策</label>';
dialog += '<textarea id="projectStrategy" name="projectStrategy" class="medium-long-field" cols="60" rows="3"></textarea>';
// dialog+='<textarea id="projectStrategy" name="projectStrategy" class="medium-long-field" cols="60" rows="3">'+$('#customfield_11237-val')[0].innerText+'</textarea>';
dialog+='</div>';
dialog+='</form>';
dialog+='</div>';
dialog+='<footer class="aui-dialog2-footer">';
dialog+='<div class="aui-dialog2-footer-actions">';
dialog+='<button class="aui-button  aui-button-primary" type="button" onclick="updateprocess()">保存</button>';
dialog+='<button class="aui-button" type="button" onclick="closeprocess()">取消</button>';
dialog+='</div>';
dialog+='<div class="aui-dialog2-footer-hint"> </div>';
dialog+='</footer>';
dialog+='</section>';

// <div id="insert">
//     <p>This will be overwritten.</p>
// </div>

// var dialog = ``;
AJS.$(document).delegate('.projectProcessBtn', 'click', function (event) {
    event.preventDefault();
    console.log("填写进度-按钮点击");
    AJS.dialog2("#processdialog").show();
    return false;
});

$(function(){
    console.log("填写进度-初始化");
    $("#jira").append(dialog);
});

function closeprocess() {
    AJS.dialog2("#processdialog").hide();
}

function updateprocess() {

    var url = AJS.contextPath() + "/rest/oa2jira/1.0/tool/update/process";

    $('#issueId').val($('#key-val').attr('rel'));
    $('#userName').val($('#header-details-user-fullname').data('username'));

    var projectProcess = $('#projectProcess').val();
    if (projectProcess == "") {
        alert("请填写进度");
        return;
    }
    var projectNextStep = $('#projectNextStep').val();
    if (projectNextStep == "") {
        alert("请填写下周工作计划");
        return;
    }
    var projectRisk = $('#projectRisk').val();
    if (projectRisk == "") {
        alert("请填写项目风险");
        return;
    }

    var projectStrategy = $('#projectStrategy').val();
    if (projectStrategy == "") {
        alert("请填写对策");
        return;
    }

    var datava = jQuery("#processInput").serializeJSON();

    var data = JSON.stringify(datava);

    jQuery.ajax({
        type: "POST",
        url: url,
        data: data,
        dataType: "json",
        async: false,
        contentType: "application/json",
        success: function (response) {
            if (response.result == true) {
                alert("成功!");
                window.location.reload();
            } else {
                alert(response.message);
            }
        }
    })
    return false;
}
