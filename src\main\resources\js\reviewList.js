// import {hex_md5} from './md5';

AJS.$(document).delegate('.reviewListBtn', 'click', function (event) {
    event.preventDefault();
    var $name = $('#header-details-user-fullname').data('username');
    var $statusName = $('#status-val')[0].innerText;
    console.log("评审清单statusName=" + $statusName);
    // console.log($('#status-val'));
    var redirect = ('/topic_project');
    if ('项目管理部-确认' == $statusName) {
        redirect = ('/topic_pre');
    } else if ('所级项目管理员确认' == $statusName) {
        redirect = ('/topic_project_confirm');
    }
    var $url = 'http://10.1.3.78:8082/user/login?name=' + compileStr($name.toString()) + '&redirect=' + redirect;
    var $project = $('#project-name-val').attr('href');
    // if ()
    var $strlist = $project.split("/");
    var $projectkey = $strlist[$strlist.length - 1];
    if ($projectkey == "WSXEDC" || $projectkey == "XMGLCSJM") {
        $url = 'http://10.1.3.189:88/user/login?name=' + compileStr($name.toString()) + '&redirect=' + redirect;
    }
    $url += '&appCode=platFormManage';
    $url = encodeURI($url);
    window.open($url, '_blank');
});

function compileStr(code){
    code = code + ''
    var c=String.fromCharCode(code.charCodeAt(0)+code.length);  
    for(var i=1;i<code.length;i++){        
        c+=String.fromCharCode(code.charCodeAt(i)+code.charCodeAt(i-1));  
    }
    // console.log(hex_md5(c));
    return escape(c);
}