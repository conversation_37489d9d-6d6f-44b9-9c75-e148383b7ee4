AJS.$(function() {
    getAllProjectRoles();
    initUserConditionEnabled();

    if (AJS.$("#jqlConditionEnabled-input").val() == "true") {
        var toggle = document.getElementById('jqlConditionEnabled');
        toggle.checked = true;
        AJS.$("#jqlCondition").val(AJS.$("#jqlCondition-input").val());
        AJS.$("#jqlConditionWholeContainer").addClass("wholeContainer");
        AJS.$("#jqlConditionContainer").show();
    } else {
        AJS.$("#jqlConditionWholeContainer").removeClass("wholeContainer");
        AJS.$("#jqlConditionContainer").hide();
    }

    document.getElementById("userConditionEnabled").addEventListener('change', function(e) {
        var isChecked = document.getElementById("userConditionEnabled").checked;
        if (isChecked) {
            AJS.$("#userConditionEnabled-input").val("true");
            AJS.$("#userConditionContainer").show();
            initSelectExecutorIsOrIsNot();
            initSelectExecutorMembershipType();
            AJS.$("#userConditionWholeContainer").addClass("wholeContainer");
        } else {
            AJS.$("#userConditionEnabled-input").val("false");
            AJS.$("#userConditionContainer").hide();
            AJS.$("#selectExecutorIsOrIsNot").val("Is");
            AJS.$("#selectExecutorIsOrIsNot-input").val("Is");
            AJS.$(AJS.$(AJS.$("#s2id_selectExecutorIsOrIsNot").children(".select2-choice")[0]).children(".select2-chosen")[0]).text("Is");
            AJS.$("#userConditionWholeContainer").removeClass("wholeContainer");
        }
    });


    AJS.$("#selectExecutorIsOrIsNot").change(function() {
        AJS.$("#selectExecutorIsOrIsNot-input").val(AJS.$(this).val());
    });

    AJS.$("#selectMembershipGroups").change(function() {
        AJS.$("#selectMembershipGroups-input").val(AJS.$(this).val());
    });

    AJS.$("#selectMembershipRoles").change(function() {
        AJS.$("#selectMembershipRoles-input").val(AJS.$(this).val());
    });

    AJS.$("#selectExecutorMembershipType").change(function() {
        AJS.$("#selectExecutorMembershipType-input").val(AJS.$(this).val());
        if (AJS.$("#selectExecutorMembershipType").val() == "Group") {
            initSelectMembershipGroup();
        } else if (AJS.$("#selectExecutorMembershipType").val() == "Project Role") {
            initSelectMembershipRole();
        }
    });

    document.getElementById("jqlConditionEnabled").addEventListener('change', function(e) {
        var isChecked = document.getElementById("jqlConditionEnabled").checked;
        if (isChecked) {
            AJS.$("#jqlConditionEnabled-input").val("true");
            AJS.$("#jqlConditionContainer").show();
            AJS.$("#jqlConditionWholeContainer").addClass("wholeContainer");
        } else {
            AJS.$("#jqlConditionEnabled-input").val("false");
            AJS.$("#jqlConditionContainer").hide();
            AJS.$("#jqlCondition").val("");
            AJS.$("#jqlConditionWholeContainer").removeClass("wholeContainer");
        }
    });

    AJS.$(document).on("change", "#jqlCondition", function(e) {
        AJS.$("#jqlCondition-input").val(AJS.$("#jqlCondition").val());
    });

    AJS.$(document).on("change", "#selectActorType", function(e) {
        AJS.$("#selectActorType-input").val(AJS.$("#selectActorType").val());
    });

    AJS.$(document).on('click', '#jqlConditionValidate', function() {
        var jql = AJS.$("#jqlCondition").val();
        sendJQLToVerification(jql);
    });

    AJS.$("form[name='jiraform']").attr("onsubmit", null);
    AJS.$("form[name='jiraform']").on("aui-valid-submit", function(event) {
        AJS.$("#jqlConditionRequired").hide();
        AJS.$("#selectMembershipGroupsRequired").hide();
        AJS.$("#selectMembershipRolesRequired").hide();
        var shouldPrevent = false;

        var toggleJqlConditionEnabled = document.getElementById('jqlConditionEnabled');
        if (toggleJqlConditionEnabled.checked && (AJS.$("#jqlCondition").val() == null || AJS.$("#jqlCondition").val() == "")) {
            shouldPrevent = true;
            AJS.$("#jqlConditionRequired").show();
        }

        var toggleUserConditionEnabled = document.getElementById('userConditionEnabled');
        if (toggleUserConditionEnabled.checked) {
            if (AJS.$("#selectExecutorMembershipType-input").val() == "Group" && (AJS.$("#selectMembershipGroups-input").val() == null || AJS.$("#selectMembershipGroups-input").val() == "")) {
                shouldPrevent = true;
                AJS.$("#selectMembershipGroupsRequired").show();
            } else if (AJS.$("#selectExecutorMembershipType-input").val() == "Project Role" && (AJS.$("#selectMembershipRoles-input").val() == null || AJS.$("#selectMembershipRoles-input").val() == "")) {
                shouldPrevent = true;
                AJS.$("#selectMembershipRolesRequired").show();
            }
        }

        if (shouldPrevent == true) {
            event.preventDefault();
        }

    });

});

function initUserConditionEnabled() {
    if (AJS.$("#userConditionEnabled-input").val() == "true") {
        var toggle = document.getElementById('userConditionEnabled');
        toggle.checked = true;
        AJS.$("#userConditionContainer").show();
        AJS.$("#userConditionWholeContainer").addClass("wholeContainer");
        initSelectExecutorIsOrIsNot();
        initSelectExecutorMembershipType();
    } else {
        AJS.$("#userConditionContainer").hide();
        AJS.$("#userConditionEnabled-input").val("false");
        AJS.$("#userConditionWholeContainer").removeClass("wholeContainer");
    }
}

function initSelectExecutorIsOrIsNot() {
    AJS.$("#selectExecutorIsOrIsNot").auiSelect2({
        minimumResultsForSearch: -1
    });
    AJS.$("#selectExecutorIsOrIsNot").val(AJS.$("#selectExecutorIsOrIsNot-input").val());
    AJS.$(AJS.$(AJS.$("#s2id_selectExecutorIsOrIsNot").children(".select2-choice")[0]).children(".select2-chosen")[0]).text(AJS.$("#selectExecutorIsOrIsNot-input").val());
    if (AJS.$("#selectExecutorIsOrIsNot").val() == null) {
        AJS.$("#selectExecutorIsOrIsNot").val("Is");
        AJS.$("#selectExecutorIsOrIsNot-input").val("Is");
        AJS.$(AJS.$(AJS.$("#s2id_selectExecutorIsOrIsNot").children(".select2-choice")[0]).children(".select2-chosen")[0]).text("Is");
    }
}

function initSelectExecutorMembershipType() {
    AJS.$("#selectExecutorMembershipType").auiSelect2({
        minimumResultsForSearch: -1
    });
    AJS.$("#selectExecutorMembershipType").val(AJS.$("#selectExecutorMembershipType-input").val());
    AJS.$(AJS.$(AJS.$("#s2id_selectExecutorMembershipType").children(".select2-choice")[0]).children(".select2-chosen")[0]).text(AJS.$("#selectExecutorMembershipType-input").val());
    if (AJS.$("#selectExecutorMembershipType").val() == null) {
        AJS.$("#selectExecutorMembershipType").val("Group");
        AJS.$("#selectExecutorMembershipType-input").val("Group");
        AJS.$(AJS.$(AJS.$("#s2id_selectExecutorMembershipType").children(".select2-choice")[0]).children(".select2-chosen")[0]).text("Group");
    }
    if (AJS.$("#selectExecutorMembershipType").val() == "Group") {
        initSelectMembershipGroup();
    } else if (AJS.$("#selectExecutorMembershipType").val() == "Project Role") {
        initSelectMembershipRole();
    }
}

function initSelectMembershipGroup() {
    AJS.$("#selectMembershipGroupsContainer").show();
    AJS.$("#selectMembershipRolesContainer").hide();
    AJS.$("#selectMembershipGroups").val(AJS.$("#selectMembershipGroups-input").val());
    AJS.$("#selectMembershipGroups-field").val(AJS.$("#selectMembershipGroups-input").val());
    clearSelectMembershipRoles();
}

function initSelectMembershipRole() {
    AJS.$("#selectMembershipGroupsContainer").hide();
    AJS.$("#selectMembershipRolesContainer").show();
    AJS.$("#selectMembershipRoles").val(AJS.$("#selectMembershipRoles-input").val());
    AJS.$(AJS.$(AJS.$("#s2id_selectMembershipRoles").children(".select2-choice")[0]).children(".select2-chosen")[0]).text(AJS.$("#selectMembershipRoles-input").val());
    clearSelectMembershipGroups();
}

function clearSelectMembershipRoles() {
    AJS.$("#selectMembershipRoles").val("");
    AJS.$("#selectMembershipRoles-input").val("");
    AJS.$("#selectMembershipRoles-field").val("");
    AJS.$(AJS.$(AJS.$("#s2id_selectMembershipRoles").children(".select2-choice")[0]).children(".select2-chosen")[0]).text("");
}

function clearSelectMembershipGroups() {
    AJS.$("#selectMembershipGroups").val("");
    AJS.$("#selectMembershipGroups-input").val("");
    AJS.$("#selectMembershipGroups-field").val("");
}

function getAllProjectRoles() {
    var urlParam = AJS.contextPath() + "/rest/api/2/role";
    var urlParamEncode = encodeURI(urlParam);
    jQuery.ajax({
        url: urlParamEncode,
        type: "GET",
        dataType: "json",
        async: false,
        xhrFields: {
            withCredentials: true
        },
        success: function(response) {
            putProjectRoles(response);
        }
    });
    AJS.$("#selectMembershipRoles").auiSelect2();
}

function putProjectRoles(roles) {
    for (var i = 0; i < roles.length; i++) {
        AJS.$("#selectMembershipRoles").append('<option>' + roles[i].name + '</option>');
    }
}

function sendJQLToVerification(jql) {
    jQuery.ajax({
        url: AJS.contextPath() + '/rest/api/2/search?maxResult=1&jql=' + jql,
        type: 'GET',
        success: function(response) {
            AJS.$("#jqlConditionError").hide();
            AJS.$("#jqlConditionApproval").show();
        },
        error: function(xhr, ajaxOptions, thrownError) {
            AJS.$("#jqlConditionError").show();
            AJS.$("#jqlConditionApproval").hide();
        }
    });
}

JIRA.bind(JIRA.Events.NEW_CONTENT_ADDED, function(e, context, reason) {
    if (reason !== JIRA.CONTENT_ADDED_REASON.panelRefreshed) {
        createGroupPicker(context);
    }
});

function createGroupPicker(ctx) {
    new AJS.SingleSelect({
        element: AJS.$("#selectMembershipGroups"),
        submitInputVal: true,
        showDropdownButton: false,
        errorMessage: AJS.format("There is no such user \'\'{0}\'\'.", "'{0}'"),
        ajaxOptions: {
            url: AJS.contextPath() + "/rest/api/2/groups/picker",
            query: true, // keep going back to the sever for each keystroke
            formatResponse: JIRA.GroupPickerUtil.formatResponse
        }
    });
}