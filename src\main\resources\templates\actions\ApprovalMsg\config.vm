<div id="page" class="" align="center">
    <section id="contentApprovalMsg" role="main">
        <div class="aui-page-panel" style="border-bottom-width:0;">
            <div class="aui-tabs horizontal-tabs aui-tabs-disabled">
                <ul class="tabs-menu">
                    <li class='menu-item #if("1" == $!tabId) active-tab #end'>
                        <a href="$baseURL/secure/ApprovalMsgOpenConfigAction!mainpage.jspa?tabId=1&amp;projectKey=$projectKey">
                            审批流程展示启用设置
                        </a>
                    </li>
                    <li class='menu-item #if("2" == $!tabId) active-tab #end'>
                        <a href="$baseURL/secure/ApprovalMsgOpenConfigAction!mainpage.jspa?tabId=2&amp;projectKey=$projectKey">
                            流程进度配置
                        </a>
                    </li>
                    <li class='menu-item #if("3" == $!tabId) active-tab #end'>
                        <a href="$baseURL/secure/ApprovalMsgOpenConfigAction!mainpage.jspa?tabId=3&amp;projectKey=$projectKey">
                            部门与oa字段匹配设置
                        </a>
                    </li>
                </ul>
                <div class="tabs-pane active-pane" id="budget$tabId">
                    #if($tabId==1)
                        #parse("templates/actions/ApprovalMsg/openInProject.vm")
                    #elseif($tabId==2)
                        #parse("templates/actions/ApprovalMsg/deptproject.vm")
                    #elseif($tabId==3)
                        #parse("templates/actions/ApprovalMsg/depts.vm")
                    #end
                </div>
            </div>
        </div>
    </section>
</div>