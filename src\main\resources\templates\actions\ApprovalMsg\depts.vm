$webResourceManager.requireResource("com.atlassian.auiplugin:aui-forms")
$webResourceManager.requireResource("com.atlassian.auiplugin:aui-dialog2")
$webResourceManager.requireResource("com.atlassian.auiplugin:aui-select2")
<form action="$!baseURL/secure/admin/deptProjectAction!mainpage.jspa"
      class="aui user-browser ajs-dirty-warning-exempt"
      id="mainForm"
      name="mainForm"
      method="get">
    <input type="hidden" name="tabId" id="tabId" value="3">
    <div class="form-body">
        <div class="aui-group">
            <div class="aui-item">
                <div class="field-group">
                    <label for="input-sign-name">
                        子部门名称
                    </label>
                    <div class="aui-ss ajax-ss">
                        <input type="text" id="input-sign-name" name="nameMatch" value="$!nameMatch">
                    </div>
                    <div class="aui-ss ajax-ss">
                        <button class="aui-button aui-button-primary" id="button-submit">查询
                        </button>
                        <button id="dialog-show-button" type="button" class="aui-button" onclick="insertButton()">新增
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="aui-page-panel-inner">
        <table id="custom-parser-example" class="aui aui-table-sortable">
            <thead>
            <tr>
                <th width="30px">序号</th>
                <th>子部门名称</th>
                <th>部门名称</th>
                <th>子部门名称</th>
                <th>操作</th>
            <tr>
            </thead>
            <tbody>
            #foreach($bean in $!deptsBeans)
            <tr id="row$!bean.getId()">
                <td>$!velocityCount</td>
                <td>$!bean.getNameMatch()</td>
                <td>$!bean.getDeptId()-$!bean.getDeptName()</td>
                <td>$!bean.getSubDeptId()-$!bean.getSubDeptName()</td>
                <td>
                    <button class="aui-button aui-button-primary" type="button" id="modifyButton$!bean.getId()"
                            onclick="getDeptsById($!bean.getId())">修改
                    </button>
                    <button class="aui-button" type="button" id="delButton$!bean.getId()"
                            onclick="deleteButton($!bean.getId())">删除
                    </button>
                </td>
            </tr>
            #end
            </tbody>
        </table>
    </div>
</form>
<section id="demo-dialog1" class="aui-dialog2 aui-dialog2-medium aui-layer" data-aui-modal="true" role="dialog"
         aria-hidden="true">
    <!-- 这里用ID有时候行有时候不行-->
    <header class="aui-dialog2-header">
        <h2 class="aui-dialog2-header-main">部门与项目对应设置</h2>
        <button class="aui-dialog2-header-close" aria-label="close">
            <span class="aui-icon aui-icon-small aui-iconfont-close-dialog"></span>
        </button>
    </header>
    <div class="aui-dialog3-content">
        <form class="aui" name="subForm" id="subForm">
            <input type="hidden" id="id" name="id" value="">
            <div class="field-group">
                <label for="nameMatch">子部门名称</label>
                <input class="text medium-long-field" type="text" id="nameMatch" name="nameMatch" value="">
            </div>
            <div class="field-group">
                <label for="deptId">部门id</label>
                <select id="deptId" name="deptId" onchange="getChilds(this)" class="select"></select>
            </div>
            <div class="field-group">
                <label for="subDeptId">子部门id</label>
                <select id="subDeptId" name="subDeptId" class="select"></select>
            </div>

        </form>
    </div>
    <footer class="aui-dialog2-footer">
        <div class="aui-dialog2-footer-actions">
            <button class="aui-button  aui-button-primary" type="button" onclick="update()">保存</button>
            <button class="aui-button" type="button" onclick="cancel()">取消</button>
        </div>
        ##
        <div class="aui-dialog2-footer-hint">This is a hint.</div>
    </footer>

</section>


<script type="text/javascript">

    /**
     * 新增按钮
     */
    function insertButton() {
        $('#id').val('')
        $('#deptId').empty()
        $('#subDeptId').empty()
        getParents()
        jQuery("#subForm")[0].reset();
        AJS.dialog2("#demo-dialog1").show();
    }

    function cancel() {
        AJS.dialog2("#demo-dialog1").hide();
    }
    
    function getParents(){
        var url = AJS.contextPath() + "/rest/oa2jira/1.0/dept/parents";
        jQuery.ajax({
            type: "GET",
            url: url,
            data: "",
            dataType: "json",
            async: false,
            contentType: "application/json",
            success: function (response) {
                console.log(response)
                if (response.result == true) {
                    var depts = response.value
                    console.log(response.value)
                    $(depts).each(function(index,item){
                        $("#deptId").append("<option value='"+item.optionId+"'>"+item.optionVal+"</option>")
                    })
                    getChilds($("#deptId"));
                } else {
                    alert(response.message)
                }
            }
        });
    }

    $(function(){
        getParents()
    })
    
    function getChilds(element){
        var value = $(element).val()
        var url = AJS.contextPath() + "/rest/oa2jira/1.0/dept/childs/"+value
        jQuery.ajax({
            type: "GET",
            url: url,
            data: "",
            dataType: "json",
            async: false,
            contentType: "application/json",
            success: function (response) {
                
                if (response.result == true) {
                    $('#subDeptId').empty();
                    var depts = response.value
                     $(depts).each(function(index,item){
                        $("#subDeptId").append("<option value='"+item.optionId+"'>"+item.optionVal+"</option>")
                    })
                } else {
                    alert(response.message)
                }
            }
        });
    }

    function getDeptsById(id) {
        var url = AJS.contextPath() + "/rest/oa2jira/1.0/depts/get/" + id;
        jQuery.ajax({
            type: "GET",
            url: url,
            data: "",
            dataType: "json",
            async: false,
            contentType: "application/json",
            success: function (response) {
                if (response.result == true) {
                    var deptProject = response.value
                    $('#id').val(deptProject.id)
                    $('#nameMatch').val(deptProject.nameMatch)
                    $('#deptId').val(deptProject.deptId)
                    getChilds($("#deptId"));
                    $('#subDeptId').val(deptProject.subDeptId)
                    
                    AJS.dialog2("#demo-dialog1").show();
                } else {
                    alert(response.message)
                }
            }
        });
    }

    function update() {
        var nameMatch = $('#nameMatch').val()
        if (nameMatch == "") {
            alert("请输入子部门名称")
            return;
        }
        var deptId = $('#deptId').val()
        if (deptId == "") {
            alert("请输入部门id")
            return;
        }
        
        var subDeptId = $('#subDeptId').val()
        if (subDeptId == "") {
            alert("请输入子部门id")
            return;
        }
        
        var datava = jQuery("#subForm").serializeJSON();
        var url = AJS.contextPath() + "/rest/oa2jira/1.0/depts/insert";
        console.log(datava);
        var data = JSON.stringify(datava);
        jQuery.ajax({
            type: "POST",
            url: url,
            data: data,
            dataType: "json",
            async: false,
            contentType: "application/json",
            success: function (response) {
                if (response.result == true) {
                    alert("成功")
                    var $id = $('#id').val()
                    if ($id == 'undefined' || !$id || !/[^\s]/.test($id)){
                        cancel()
                        var str_count = $('#custom-parser-example tbody').children('tr').length;
                        var int_count = parseInt(str_count);
                        int_count = int_count+1;
                        $('#custom-parser-example tbody').append(
                        `<tr id="row${response.value}">
                            <td>${int_count}</td>
                            <td>${nameMatch}</td>
                            <td>${deptId}</td>
                            <td>${subDeptId}</td>
                            <td>
                                <button class="aui-button aui-button-primary" type="button" id="modifyButton$!bean.getId()"
                                        onclick="getDeptsById(${response.value})">修改
                                </button>
                                <button class="aui-button" type="button" id="delButton$!bean.getId()"
                                        onclick="deleteButton(${response.value})">删除
                                </button>
                            </td>
                        </tr>`
                        )
                    }else{
                        cancel()
                        $('#row' +  $id).children('td').eq(1).text(nameMatch)
                        $('#row' +  $id).children('td').eq(2).text(deptId)
                        $('#row' +  $id).children('td').eq(3).text(subDeptId)
                    }
                    
                } else {
                    alert(response.message)
                }
            }
        })
    }

    /**
     * 删除按钮
     * @param id
     */
    function deleteButton(id) {
        var url = AJS.contextPath() + "/rest/oa2jira/1.0/depts/delete/" + id;
        jQuery.ajax({
            type: "POST",
            url: url,
            data: "",
            dataType: "json",
            async: false,
            contentType: "application/json",
            success: function (response) {
                if (response.result == true) {
                    alert("成功")
                    $('#row' + id).remove()
                } else {
                    alert(response.message)
                }
            }
        })
    }
</script>