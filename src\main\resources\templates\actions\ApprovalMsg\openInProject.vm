<form action="$!baseURL/secure/ApprovalMsgOpenConfigAction!mainpage.jspa"
      class="aui user-browser ajs-dirty-warning-exempt"
      id="mainForm"
      name=mainForm"
      method="get">
    <input type="hidden" name="tabId" id="tabId" value="1">
    <input type="hidden" name="projectKey" id="projectKey" value=$projectKey>
    <div class="form-body" style="border-bottom: 1px">
        <div class="aui-group">
            <div class="aui-item">
                <div class="field-group">
                    <div>
                        <aui-toggle id="gzip-compression" label="审批流程展示"
                            #if($!openStatus)
                                    checked
                            #end
                        ></aui-toggle>
                        <aui-label for="gzip-compression">审批流程展示</aui-label>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>

<script>
    jQuery("#gzip-compression").change(function (){
        var url = AJS.contextPath() + "/rest/oa2jira/1.0/approval/msg/update/project/open";
        var projectKey = jQuery('#projectKey').val()
        url = url + "?projectKey=" + projectKey;
        ## var data = {
        ##     projectKey:$projectKey,
        ## };
        ## console.log(data);
        jQuery.ajax({
            type: "POST",
            url: url,
            data: "",
            dataType: "json",
            async: false,
            success: function (response) {
                console.log(response)
                if (response.result == true) {
                    window.location.reload()
                } else {
                    alert(response.code + "\n" + response.message)
                }
            }
        })
    })
</script>
