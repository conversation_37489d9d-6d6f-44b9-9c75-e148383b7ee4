#*<div id="page" class="" align="center">
*##*    <section id="content" role="main">
        <div class="aui-page-panel" style="border-bottom-width:0;">
            <div class="aui-tabs horizontal-tabs aui-tabs-disabled">
                <ul class="tabs-menu">
                    <li class='menu-item #if("1" == $!tabId) active-tab #end'>
                        <a href="$baseURL/secure/admin/deptProjectAction!mainpage.jspa?tabId=1">
                            项目与部门设置
                        </a>
                    </li>
                    <li class='menu-item #if("2" == $!tabId) active-tab #end'>
                        <a href="$baseURL/secure/admin/deptProjectAction!mainpage.jspa?tabId=2">
                            部门与oa字段匹配设置
                        </a>
                    </li>
                </ul>
                <div class="tabs-pane active-pane" id="budget$tabId">
                    #if($tabId==1)
                        #parse("templates/actions/setting/deptproject.vm")
                    #elseif($tabId==2)
                        #parse("templates/actions/setting/depts.vm")
                    #end
                </div>
            </div>
        </div>       
    </section>*##*
    <section id="content" role="main">
        <iframe src="http://*********:8082/topic_chart" width="99%" height="700" ></iframe>
    </section>
</div>*#
<div class="aui-page-panel">
    <div class="aui-page-panel-inner">
        <section class="aui-page-panel-item ">
#*            <main id="main" role="main" class="aui-page-panel-content">
                <!-- Your page heading and content goes here -->
            </main>*#
            <div>
                <iframe src="http://*********:8082/topic_chart" class="iframe-size" allowfullscreen style="border:none;"></iframe>
            </div>
##            <iframe src="http://*********:8082/topic_chart" width="99%" height="700" ></iframe>
        </section>
#*        <section class="aui-page-panel-item someotherclass">
        </section>*#
    </div>
</div>


<script type="text/javascript">
##
##    /**
##     * 新增按钮
##     */
##    function insertButton() {
##        $('#id').val('')
##        $('#deptId').empty()
##        $('#subDeptId').empty()
##        getParents()
##        jQuery("#subForm")[0].reset();
##        AJS.dialog2("#demo-dialog1").show();
##    }
##
##    function cancel() {
##        AJS.dialog2("#demo-dialog1").hide();
##    }
##
##    function getParents(){
##        var url = AJS.contextPath() + "/rest/oa2jira/1.0/dept/parents";
##        jQuery.ajax({
##            type: "GET",
##            url: url,
##            data: "",
##            dataType: "json",
##            async: false,
##            contentType: "application/json",
##            success: function (response) {
##                console.log(response)
##                if (response.result == true) {
##                    var depts = response.value
##                    console.log(response.value)
##                    $(depts).each(function(index,item){
##                        $("#deptId").append("<option value='"+item.optionId+"'>"+item.optionVal+"</option>")
##                    })
##                    getChilds($("#deptId"));
##                } else {
##                    alert(response.message)
##                }
##            }
##        });
##    }
##
##    $(function(){
##        getParents()
##    })
##
##    function getChilds(element){
##        var value = $(element).val()
##        var url = AJS.contextPath() + "/rest/oa2jira/1.0/dept/childs/"+value
##        jQuery.ajax({
##            type: "GET",
##            url: url,
##            data: "",
##            dataType: "json",
##            async: false,
##            contentType: "application/json",
##            success: function (response) {
##
##                if (response.result == true) {
##                    $('#subDeptId').empty();
##                    var depts = response.value
##                    $(depts).each(function(index,item){
##                        $("#subDeptId").append("<option value='"+item.optionId+"'>"+item.optionVal+"</option>")
##                    })
##                } else {
##                    alert(response.message)
##                }
##            }
##        });
##    }
##
##    function getDeptsById(id) {
##        var url = AJS.contextPath() + "/rest/oa2jira/1.0/depts/get/" + id;
##        jQuery.ajax({
##            type: "GET",
##            url: url,
##            data: "",
##            dataType: "json",
##            async: false,
##            contentType: "application/json",
##            success: function (response) {
##                if (response.result == true) {
##                    var deptProject = response.value
##                    $('#id').val(deptProject.id)
##                    $('#nameMatch').val(deptProject.nameMatch)
##                    $('#deptId').val(deptProject.deptId)
##                    getChilds($("#deptId"));
##                    $('#subDeptId').val(deptProject.subDeptId)
##
##                    AJS.dialog2("#demo-dialog1").show();
##                } else {
##                    alert(response.message)
##                }
##            }
##        });
##    }
##
##    function update() {
##        var nameMatch = $('#nameMatch').val()
##        if (nameMatch == "") {
##            alert("请输入子部门名称")
##            return;
##        }
##        var deptId = $('#deptId').val()
##        if (deptId == "") {
##            alert("请输入部门id")
##            return;
##        }
##
##        var subDeptId = $('#subDeptId').val()
##        if (subDeptId == "") {
##            alert("请输入子部门id")
##            return;
##        }
##
##        var datava = jQuery("#subForm").serializeJSON();
##        var url = AJS.contextPath() + "/rest/oa2jira/1.0/depts/insert";
##        console.log(datava);
##        var data = JSON.stringify(datava);
##        jQuery.ajax({
##            type: "POST",
##            url: url,
##            data: data,
##            dataType: "json",
##            async: false,
##            contentType: "application/json",
##            success: function (response) {
##                if (response.result == true) {
##                    alert("成功")
##                    var $id = $('#id').val()
##                    if ($id == 'undefined' || !$id || !/[^\s]/.test($id)){
##                        cancel()
##                        var str_count = $('#custom-parser-example tbody').children('tr').length;
##                        var int_count = parseInt(str_count);
##                        int_count = int_count+1;
##                        $('#custom-parser-example tbody').append(
##                                `<tr id="row${response.value}">
##                            <td>${int_count}</td>
##                            <td>${nameMatch}</td>
##                            <td>${deptId}</td>
##                            <td>${subDeptId}</td>
##                            <td>
##                                <button class="aui-button aui-button-primary" type="button" id="modifyButton$!bean.getId()"
##                                        onclick="getDeptsById(${response.value})">修改
##                                </button>
##                                <button class="aui-button" type="button" id="delButton$!bean.getId()"
##                                        onclick="deleteButton(${response.value})">删除
##                                </button>
##                            </td>
##                        </tr>`
##                        )
##                    }else{
##                        cancel()
##                        $('#row' +  $id).children('td').eq(1).text(nameMatch)
##                        $('#row' +  $id).children('td').eq(2).text(deptId)
##                        $('#row' +  $id).children('td').eq(3).text(subDeptId)
##                    }
##
##                } else {
##                    alert(response.message)
##                }
##            }
##        })
##    }
##
##    /**
##     * 删除按钮
##     * @param id
##     */
##    function deleteButton(id) {
##        var url = AJS.contextPath() + "/rest/oa2jira/1.0/depts/delete/" + id;
##        jQuery.ajax({
##            type: "POST",
##            url: url,
##            data: "",
##            dataType: "json",
##            async: false,
##            contentType: "application/json",
##            success: function (response) {
##                if (response.result == true) {
##                    alert("成功")
##                    $('#row' + id).remove()
##                } else {
##                    alert(response.message)
##                }
##            }
##        })
##    }
</script>

<style lang="less">
    .content-size {
        width: 100%;
        height: calc(100vh);
        /*display: flex;*/
        /*align-items: center;*/
    }
    .iframe-size {
        display: block;
        border: none;
        height: 90vh;/*设置高度百分比,一直调到只有一个滚动调为止*/
        width: 100%;
    }

</style>