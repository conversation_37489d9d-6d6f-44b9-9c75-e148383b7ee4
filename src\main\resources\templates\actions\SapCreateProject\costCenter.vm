$webResourceManager.requireResource("com.atlassian.auiplugin:aui-forms")
$webResourceManager.requireResource("com.atlassian.auiplugin:aui-dialog2")
$webResourceManager.requireResource("com.atlassian.auiplugin:aui-select2")
##<meta http-equiv="content-type" content="text/html;charset=utf-8">
<form action="$!baseURL/secure/admin/SapCreateProjectAction!mainpage.jspa"
      class="aui user-browser ajs-dirty-warning-exempt"
      id="mainForm"
      name="mainForm"
      method="get">
    <input type="hidden" name="tabId" id="tabId" value="2">
    <div class="form-body">
        <div class="aui-group">
            <div class="aui-item">
                <div class="field-group">
                    <label for="costCenterName">
                        成本中心名称
                    </label>
                    <div class="aui-ss ajax-ss">
                        <input type="text" id="costCenterName" name="costCenterName" value="$!costCenterName">
                    </div>
                    <div class="aui-ss ajax-ss">
                        <button class="aui-button aui-button-primary" id="button-submit">查询
                        </button>
                        <button id="sync-button" type="button" class="aui-button" onclick="syncCostCenter()">同步
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="aui-page-panel-inner">
        <table id="cost-center-list" class="aui aui-table-sortable">
            <thead>
            <tr>
                <th width="30px">序号</th>
                <th>公司代码</th>
                <th>成本中心代码</th>
                <th>成本中心名称</th>
                <th>利润中心代码</th>
                <th>利润中心名称</th>
                <th>是否在项目中显示</th>
                <th>操作</th>
            <tr>
            </thead>
            <tbody id="cost-center-detail">
##            #foreach($bean in $!resultBean.getValue)
##            <tr id="row$!bean.getId()">
##                <td>$!velocityCount</td>
##                <td>$bean.getBUKRS()</td>
##                <td>$bean.getKOSTL()</td>
##                <td>$bean.getKTEXT()</td>
##                <td>$bean.getLRZXDM()</td>
##                <td>$bean.getLRZXMC()</td>
##            </tr>
##            #end
            </tbody>
        </table>
    </div>
    <div class="processpage"></div>
</form>
<style>
    .processpage{
        padding:8px;
    }
    .processpage a,.processpage span{
        padding: 10px 10px 0;
    }
</style>
<script type="text/javascript">

    function syncCostCenter() {
        var url = AJS.contextPath() + "/rest/oa2jira/1.0/create/project/update/cost";
        jQuery.ajax({
            type: "GET",
            url: url,
            data: "",
            dataType: "json",
            async: false,
            contentType: "application/json",
            success: function (response) {
                console.log(response)
                if (response.result == true) {
                    location.reload();
                } else {
                    alert(response.message)
                }
            }
        });
    }

    jQuery(function(){
        var costCenter = $('#costCenterName').val();
        var url = AJS.contextPath() + "/rest/oa2jira/1.0/create/project/list/cost/0";
        if (!!costCenter) {
            url = url + "/" + costCenter;
        }
        getProcess(url);
    })

    // function submitClick(){
    //     var costCenter = $('#costCenterName').val('')
    //     var url = AJS.contextPath() + "/rest/oa2jira/1.0/create/project/list/cost/0";
    //     if (!!costCenter) {
    //         url = url + "/" + costCenter;
    //     }
    //     getProcess(url);
    // }

    function numClick(obj){
        let p = $(obj).attr('url');
        var costCenter = $('#costCenterName').val();
        let url = AJS.contextPath() + "/rest/oa2jira/1.0/create/project/list/cost/" + p;
        if (!!costCenter) {
            url = url + "/" + costCenter;
        }
        jQuery('tbody#cost-center-detail').html('');
        getProcess(url);
    }

    function getProcess(url) {
        jQuery.ajax({
            type: "GET",
            url: url,
            data: null,
            dataType: "json",
            async: false,
            contentType: "application/json",
            success: function (response) {
                console.log(response);
                if (response.result == true) {
                    $('.processpage').html(pagestr(response.pageNum,response.cur,10,4))
                    jQuery.each(response.value,function(index,item){
                        console.log(item);
                        let $tr = jQuery(`<tr id="row${item.id}">`);
                        let $td = jQuery('<td>').html((index+1)+"");
                            $tr.append($td);
                        jQuery.each(item,function(i,val){
                            if(i == 'id' || i == 'CBLX' ){
                                return true
                            }
                            let $td = jQuery('<td>').html(val);
                                $tr.append($td);
                        });

                        ## let $tr = jQuery(
                        ##     `<tr id="row${item.id()}">
                        ##         <td>${index}</td>
                        ##         <td>${item.BUKRS()}</td>
                        ##         <td>${item.KOSTL()}</td>
                        ##         <td>${item.KTEXT()}</td>
                        ##         <td>${item.LRZXDM()}</td>
                        ##         <td>${item.LRZXMC()}</td>
                        ##     </tr>`);
                        ## console.log($tr);
                        jQuery('tbody#cost-center-detail').append($tr);
                    });
                } else {
                    alert(response.message)
                }
            }
        })
    }

    function pagestr(countdatas,currentpage,everpage,beforlaterln){
        var page= "";
        if(countdatas <= everpage)
            return page;
        var intpart = Math.floor(countdatas/everpage);//整数
        var remainderpart = countdatas%everpage;//余数
        var countpage = intpart;//取整数
        var currentpage = currentpage<0 ? 0:currentpage;
        if(remainderpart==0){//余数判断
            currentpage=currentpage>countpage-1?countpage-1:currentpage;
            var pp=currentpage+1;
            var strbe="";
            var pagebefore=pp-beforlaterln-1;
            for(var i=pp; i > pagebefore; i--){
                if(i>0 && i<pp){
                    strbe="<a class='num' onclick='numClick(this)' href='javascript:void(0);' url='"+(i-1)+"'>"+i+"</a>"+strbe;
                }
            }
            if(pp<=1){strbe="";}else{strbe="<a class='num' onclick='numClick(this)' href='javascript:void(0);' url='0'>首页</a>"+"<a class='num' onclick='numClick(this)' href='javascript:void(0);' url='"+(pp-2)+"'>上一页</a>"+strbe;}
            var strmid="<span class='current'>"+pp+"</span>";
            var strlater="";
            var pagelater=pp+beforlaterln;
            for(var i=pp; i < pagelater; i++){
                if(i<countpage){
                    strlater+="<a class='num' onclick='numClick(this)' href='javascript:void(0);' url='"+(i)+"'>"+(i+1)+"</a>";
                }
            }
            if(pp>=countpage){}else{strlater+="<a class='num' onclick='numClick(this)' href='javascript:void(0);' url='"+pp+"'>下一页</a>"+"<a class='num' onclick='numClick(this)' href='javascript:void(0);' url='"+(countpage-1)+"'>尾页</a>";}
            page=strbe+strmid+strlater;
            /**/
        }else {
            countpage+=1;//总页数
            currentpage=currentpage>countpage-1?countpage-1:currentpage;
            var pp=currentpage+1;
            var strbe="";
            var pagebefore=pp-beforlaterln-1;
            for(var i=pp; i > pagebefore; i--){
                if(i>0 && i<pp){
                    strbe="<a class='num' onclick='numClick(this)' href='javascript:void(0);' url='"+(i-1)+"'>"+i+"</a>"+strbe;
                }
            }
            if(pp<=1){strbe="";}else{strbe="<a class='num' onclick='numClick(this)' href='javascript:void(0);' url='0'>首页</a>"+"<a class='num' onclick='numClick(this)' href='javascript:void(0);' url='"+(pp-2)+"'>上一页</a>"+strbe;}
            var strmid="<span class='current'>"+pp+"</span>";
            var strlater="";
            var pagelater=pp+beforlaterln;
            for(var i=pp; i < pagelater; i++){
                if(i<countpage){
                    strlater+="<a class='num' onclick='numClick(this)' href='javascript:void(0);' url='"+(i)+"'>"+(i+1)+"</a>";
                }
            }
            if(pp>=countpage){}else{strlater+="<a class='num' onclick='numClick(this)' href='javascript:void(0);' url='"+pp+"'>下一页</a>"+"<a class='num' onclick='numClick(this)' href='javascript:void(0);' url='"+(countpage-1)+"'>尾页</a>";}
            page=strbe+strmid+strlater;
        }
        //"<a>共"+countpage+"页</a>"+
        return page;
    }
</script>