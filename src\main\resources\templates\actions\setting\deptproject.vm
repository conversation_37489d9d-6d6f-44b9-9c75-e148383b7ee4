$webResourceManager.requireResource("com.atlassian.auiplugin:aui-forms")
$webResourceManager.requireResource("com.atlassian.auiplugin:aui-dialog2")
$webResourceManager.requireResource("com.atlassian.auiplugin:aui-select2")
<form action="$!baseURL/secure/admin/deptProjectAction!mainpage.jspa"
      class="aui user-browser ajs-dirty-warning-exempt"
      id="mainForm"
      name="mainForm"
      method="get">
    <input type="hidden" name="tabId" id="tabId" value="1">
    <div class="form-body">
        <div class="aui-group">
            <div class="aui-item">
                <div class="field-group">
                    <label for="input-sign-name">
                        部门名称
                    </label>
                    <div class="aui-ss ajax-ss">
                        <input type="text" id="input-sign-name" name="deptName" value="$!deptName">
                    </div>
                    <div class="aui-ss ajax-ss">
                        <button class="aui-button aui-button-primary" id="button-submit">查询
                        </button>
                        <button id="dialog-show-button" type="button" class="aui-button" onclick="insertButton()">新增
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="aui-page-panel-inner">
        <table id="custom-parser-example" class="aui aui-table-sortable">
            <thead>
            <tr>
                <th width="30px">序号</th>
                <th>部门名称</th>
                <th>项目id</th>
                <th>问题类型id</th>
                <th>报告人(工号)</th>
                <th>部门id</th>
                <th>操作</th>
            <tr>
            </thead>
            <tbody>
            #foreach($bean in $!deptProjectBeans)
            <tr id="row$!bean.getId()">
                <td>$!velocityCount</td>
                <td>$bean.getDeptName()</td>
                <td>$bean.getProjectId()</td>
                <td>$bean.getIssueTypeId()</td>
                <td>$bean.getExecutor()</td>
                <td>$bean.getDeptId()</td>
                <td>
                    <button class="aui-button aui-button-primary" type="button" id="modifyButton$!bean.getId()"
                            onclick="getDeptProjectById($!bean.getId())">修改
                    </button>
                    <button class="aui-button" type="button" id="delButton$!bean.getId()"
                            onclick="deleteButton($!bean.getId())">删除
                    </button>
                </td>
            </tr>
            #end
            </tbody>
        </table>
    </div>
</form>
<section id="demo-dialog1" class="aui-dialog2 aui-dialog2-medium aui-layer" data-aui-modal="true" role="dialog"
         aria-hidden="true">
    <!-- 这里用ID有时候行有时候不行-->
    <header class="aui-dialog2-header">
        <h2 class="aui-dialog2-header-main">部门与项目对应设置</h2>
        <button class="aui-dialog2-header-close" aria-label="close">
            <span class="aui-icon aui-icon-small aui-iconfont-close-dialog"></span>
        </button>
    </header>
    <div class="aui-dialog3-content">
        <form class="aui" name="subForm" id="subForm">
            <input type="hidden" id="id" name="id" value="">
            <div class="field-group">
                <label for="deptName">部门名称</label>
                <input class="text medium-long-field" type="text" id="deptName" name="deptName" value="">
            </div>
            <div class="field-group">
                <label for="projectId">项目id</label>
                <input class="text medium-long-field" type="text" id="projectId" name="projectId" value="">
            </div>
            <div class="field-group">
                <label for="issueTypeId">问题类型id</label>
                <input class="text medium-long-field" type="number" id="issueTypeId" name="issueTypeId" value="">
            </div>
            <div class="field-group">
                <label for="executor">报告人(工号)</label>
                <input class="text medium-long-field" type="text" id="executor" name="executor" value="">
            </div>
            <div class="field-group">
                <label for="deptId">部门id</label>
                <select id="deptId" name="deptId" class="select"></select>
            </div>
        </form>
    </div>
    <footer class="aui-dialog2-footer">
        <div class="aui-dialog2-footer-actions">
            <button class="aui-button  aui-button-primary" type="button" onclick="update()">保存</button>
            <button class="aui-button" type="button" onclick="cancel()">取消</button>
        </div>
        ##
        <div class="aui-dialog2-footer-hint">This is a hint.</div>
    </footer>

</section>


<script type="text/javascript">

    /**
     * 新增按钮
     */
    function insertButton() {
        $('#id').val('')
        jQuery("#subForm")[0].reset();
        AJS.dialog2("#demo-dialog1").show();
    }

    function cancel() {
        AJS.dialog2("#demo-dialog1").hide();
    }
    
    function getParents(){
        var url = AJS.contextPath() + "/rest/oa2jira/1.0/dept/parents";
        jQuery.ajax({
            type: "GET",
            url: url,
            data: "",
            dataType: "json",
            async: false,
            contentType: "application/json",
            success: function (response) {
                if (response.result == true) {
                    var depts = response.value
                    $(depts).each(function(index,item){
                        $("#deptId").append("<option value='"+item.optionId+"'>"+item.optionVal+"</option>")
                    })
                } else {
                    alert(response.message)
                }
            }
        });
    }
    
    $(function(){
        getParents()
    })

    function getDeptProjectById(id) {
        var url = AJS.contextPath() + "/rest/oa2jira/1.0/dept/project/get/" + id;
        jQuery.ajax({
            type: "GET",
            url: url,
            data: "",
            dataType: "json",
            async: false,
            contentType: "application/json",
            success: function (response) {
                if (response.result == true) {
                    var deptProject = response.value
                    $('#id').val(deptProject.id)
                    $('#deptName').val(deptProject.deptName)
                    $('#projectId').val(deptProject.projectId)
                    $('#issueTypeId').val(deptProject.issueTypeId)
                    $('#executor').val(deptProject.executor)
                    $("#deptId").val(deptProject.deptId)
                    
                    AJS.dialog2("#demo-dialog1").show();
                } else {
                    alert(response.message)
                }
            }
        });
    }

    function update() {

        var deptName = $('#deptName').val()
        if (deptName == "") {
            alert("请输入部门名称")
            return;
        }
        var projectId = $('#projectId').val()
        if (projectId == "") {
            alert("请输入项目id")
            return;
        }
        var issueTypeId = $('#issueTypeId').val()
        if (issueTypeId == "") {
            alert("请输入问题类型id")
            return;
        }
        
        var executor = $('#executor').val()
        if (executor == "") {
            alert("请输入报告人工号")
            return;
        }
        
        var deptId = $('#deptId').val()
        if (deptId == "") {
            alert("请选择部门")
            return;
        }
        
        var datava = jQuery("#subForm").serializeJSON();
        var url = AJS.contextPath() + "/rest/oa2jira/1.0/dept/project/insert";

        var data = JSON.stringify(datava);
        jQuery.ajax({
            type: "POST",

            url: url,
            data: data,
            dataType: "json",
            async: false,
            contentType: "application/json",
            success: function (response) {
                if (response.result == true) {
                    alert("成功")
                    var $id = $('#id').val()
                    if ($id == 'undefined' || !$id || !/[^\s]/.test($id)){
                        cancel()
                        var str_count = $('#custom-parser-example tbody').children('tr').length;
                        var int_count = parseInt(str_count);
                        int_count = int_count+1;
                        $('#custom-parser-example tbody').append(
                        `<tr id="row${response.value}">
                            <td>${int_count}</td>
                            <td>${deptName}</td>
                            <td>${projectId}</td>
                            <td>${issueTypeId}</td>
                            <td>${executor}</td>
                            <td>${deptId}</td>
                            <td>
                                <button class="aui-button aui-button-primary" type="button" id="modifyButton$!bean.getId()"
                                        onclick="getDeptProjectById(${response.value})">修改
                                </button>
                                <button class="aui-button" type="button" id="delButton$!bean.getId()"
                                        onclick="deleteButton(${response.value})">删除
                                </button>
                            </td>
                        </tr>`
                        )
                    }else{
                        cancel()
                        $('#row' +  $id).children('td').eq(1).text(deptName)
                        $('#row' +  $id).children('td').eq(2).text(projectId)
                        $('#row' +  $id).children('td').eq(3).text(issueTypeId)
                        $('#row' +  $id).children('td').eq(4).text(executor)
                        $('#row' +  $id).children('td').eq(5).text(deptId) 
                    }
                } else {
                    alert(response.message)
                }
            }
        })
    }

    /**
     * 删除按钮
     * @param id
     */
    function deleteButton(id) {
        var url = AJS.contextPath() + "/rest/oa2jira/1.0/dept/project/delete/" + id;
        jQuery.ajax({
            type: "POST",
            url: url,
            data: "",
            dataType: "json",
            async: false,
            contentType: "application/json",
            success: function (response) {
                if (response.result == true) {
                    alert("成功")
                    $('#row' + id).remove()
                } else {
                    alert(response.message)
                }
            }
        })
    }
</script>