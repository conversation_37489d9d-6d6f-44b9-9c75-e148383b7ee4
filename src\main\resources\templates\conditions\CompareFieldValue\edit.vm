<div>
    <tr bgcolor="#ffffff">
        <td bgcolor="#ffffff" nowrap>
            源问题
            <select name="sourceIssue" id="sourceIssue">
                #foreach($param in ${copyTypeMap.keySet()})
                    <option value=$param
                        #if($!sourceIssue == $param) selected="true" #end
                        #if($param == "epic_link_issue" || $param == "sub_issue") disabled="true" #end>
                        ${copyTypeMap.get($param)}</option>
                #end
            </select>
            的字段
            <select name="customField" id="customField" >
                #foreach($bean in $!customFieldList)
                    <option value="$bean.getId()"
                        #if($!customField==$bean.getId()) selected="true" #end>
                        $!bean.getName()
                    </option>
                #end
            </select>
        </td>
    </tr>

    <tr bgcolor="#ffffff">
        <td bgcolor="#ffffff" nowrap>
            与目标问题
            <select name="targetIssue" id="targetIssue">
                #foreach($param in ${copyTypeMap.keySet()})
                    <option value=$param
                        #if($!targetIssue == $param) selected="true" #end
                        #if($param == "epic_link_issue" || $param == "sub_issue") disabled="true" #end>
                        ${copyTypeMap.get($param)}</option>
                #end
            </select>
            的字段
            <select name="customField1" id="customField1" >
                #foreach($bean in $!customFieldList)
                    <option value="$bean.getId()"
                        #if($!customField1==$bean.getId()) selected="true" #end>
                        $!bean.getName()
                    </option>
                #end
            </select>
        </td>
    </tr>

    <tr bgcolor="#ffffff">
        <td bgcolor="#ffffff" nowrap>
            <select name="isEqual" id="isEqual" >
                <option value="true" #if("$!isEqual"=="true")selected="true" #end>相等</option>
                <option value="false" #if("$!isEqual"=="false")selected="true" #end>不相等</option>
            </select>
            时
            <select name="isShow" id="isShow" >
                <option value="true" #if("$!isShow"=="true")selected="true" #end>显示</option>
                <option value="false" #if("$!isShow"=="false")selected="true" #end>不显示</option>
            </select>
        </td>
    </tr>
    <input type="hidden" id="field_label">
</div>
<script type="text/javascript">
    AJS.$("#customField").auiSelect2();
    AJS.$("#customField1").auiSelect2();
</script>