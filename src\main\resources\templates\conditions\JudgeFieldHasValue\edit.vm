<div>
    <tr bgcolor="#ffffff">
        <td bgcolor="#ffffff" nowrap>
            字段
            <select name="customField" id="customField" >
                #foreach($bean in $!customFieldList)
                    <option value="$bean.getId()"
                        #if($!customField==$bean.getId()) selected="true" #end>
                        $!bean.getName()
                    </option>
                #end
            </select>

            <select name="isEmpty" id="isEmpty" >
                <option value="true" #if("$!isEmpty"=="true")selected="true" #end>为空</option>
                <option value="false" #if("$!isEmpty"=="false")selected="true" #end>不为空</option>
            </select>

            时
            <select name="isShow" id="isShow" >
                <option value="true" #if("$!isShow"=="true")selected="true" #end>显示</option>
                <option value="false" #if("$!isShow"=="false")selected="true" #end>不显示</option>
            </select>
        </td>
    </tr>
    <input type="hidden" id="field_label">
</div>
<script type="text/javascript">
    AJS.$("#customField").auiSelect2();
</script>