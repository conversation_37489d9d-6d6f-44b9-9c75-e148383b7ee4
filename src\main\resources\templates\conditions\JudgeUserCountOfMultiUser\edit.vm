<div>
    <tr bgcolor="#ffffff">
        <td bgcolor="#ffffff" nowrap>
            字段
            <select name="multiUser" id="multiUser" >
                #foreach($bean in $!customFieldList)
                    <option value="$bean.getId()"
                        #if($!multiUser==$bean.getId()) selected="true" #end>
                        $!bean.getName()
                    </option>
                #end
            </select>
            用户数量
            <select name="compareType" id="compareType" >
                <option value="less" #if("$!compareType"=="less")selected="true" #end>小于等于</option>
                <option value="more" #if("$!compareType"=="more")selected="true" #end>大于等于</option>
                <option value="equal" #if("$!compareType"=="equal")selected="true" #end>等于</option>
            </select>
            <input type="number" id="targetUserCount" name="targetUserCount"
                #if($targetUserCount)value="$!targetUserCount"#end>
        </td>
    </tr>
    <input type="hidden" id="field_label">
</div>
<script type="text/javascript">
    AJS.$("#multiUser").auiSelect2();
</script>