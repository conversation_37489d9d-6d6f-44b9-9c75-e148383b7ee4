<div>
    <tr bgcolor="#ffffff">
        <td bgcolor="#ffffff" nowrap>
            字段
            <select name="multiUser" id="multiUser" >
                #foreach($bean in $!customFieldList)
                    <option value="$bean.getId()"
                        #if($!multiUser==$bean.getId()) selected="true" #end>
                        $!bean.getName()
                    </option>
                #end
            </select>
            用户数量
            <select name="compareType" id="compareType" >
                <option value="1" selected="true"#if($!compareType=="1")selected="true" #end>小于</option>
                <option value="0" >大于</option>
            </select>
            <input type="number" id="targetUserCount" name="targetUserCount">
        </td>
    </tr>
    <input type="hidden" id="field_label">
</div>
<script type="text/javascript">
    AJS.$("#multiUser").auiSelect2();
</script>