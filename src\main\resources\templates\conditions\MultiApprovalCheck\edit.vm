<div>
    <tr bgcolor="#ffffff">
        <td bgcolor="#ffffff" nowrap>
            <label for="multiUser">需多人审核字段</label>
            <select name="multiUser" id="multiUser" >
                #foreach($bean in $!customFieldList)
                    <option value="$bean.getId()"
                        #if($!multiUser==$bean.getId()) selected="true" #end>
                        $!bean.getName()
                    </option>
                #end
            </select>
##            用户数量
##            <select name="compareType" id="compareType" >
##                <option value="less" #if("$!compareType"=="less")selected="true" #end>小于</option>
##                <option value="more" #if("$!compareType"=="more")selected="true" #end>大于</option>
##                <option value="equal" #if("$!compareType"=="equal")selected="true" #end>等于</option>
##            </select>

        </td>
    </tr>
    <tr bgcolor="#ffffff">
        <td bgcolor="#ffffff" nowrap>
            <label for="multiUser2">已分配审核暂存字段</label>
            <select name="multiUser2" id="multiUser2" >
                #foreach($bean in $!customFieldList)
                    <option value="$bean.getId()"
                        #if($!multiUser2==$bean.getId()) selected="true" #end>
                        $!bean.getName()
                    </option>
                #end
            </select>
        </td>
    </tr>
    <tr bgcolor="#ffffff">
        <td bgcolor="#ffffff" nowrap>
            <label for="showCondition">显示条件</label>
            <select name="showCondition" id="showCondition" >
##                #foreach($bean in $!customFieldList)
                <option value="needApproval"
                    #if($!showCondition == '' || $!showCondition=='needApproval') selected="true" #end>
                    需要会签时显示
                </option>
                <option value="noApproval"
                    #if($!showCondition=='noApproval') selected="true" #end>
                    不需会签时显示
                </option>
##                #end
            </select>
        </td>
    </tr>
    <input type="hidden" id="field_label">
</div>
<script type="text/javascript">
    AJS.$("#multiUser").auiSelect2();
    AJS.$("#multiUser2").auiSelect2();
</script>