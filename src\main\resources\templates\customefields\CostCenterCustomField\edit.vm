#customControlHeader ($action $customField.id $customField.name $fieldLayoutItem.required $displayParameters $auiparams)

<select class="select filter-single-select " id="CostCenterSelect" name="$!customField.id" style="width: 400px">
    <option value="" selected="selected">
        无
    </option>
    <option value="" disabled="disabled">
        公司代码-成本中心名称-成本中心代码
    </option>
    #foreach ($item in $configs.options)
        <option #if ($!value && $!value.indexOf($item.getKOSTL().toString()) != -1) selected="selected"#end value="$item.getKOSTL()">
            $!item.getBUKRS()-$!item.getKTEXT()-$!item.getKOSTL()
        </option>
    #end
</select>
<br/><span style="color: #919191; ">公司代码-成本中心名称-成本中心代码</span>

<script type="text/javascript">
    AJS.$("#CostCenterSelect").auiSelect2();
</script>
#customControlFooter ($action $customField.id $fieldLayoutItem.fieldDescription $displayParameters $auiparams)