#customControlHeader ($action $customField.id $customField.name $fieldLayoutItem.required $displayParameters $auiparams)
<select class="select filter-single-select " id="innerOrderTypeSelect" name="$customField.id" >
    <option value="" selected="selected">无</option>
    #foreach ($item in $configs.options)
        <option #if ($!value && $!value.indexOf($item.getId().toString()) != -1) selected="selected"#end value="$item.getId()">
            $item.getCompanyCode()-$item.getOrderTypeName()-$item.getOrderTypeCode()
        </option>
    #end
</select>
<br/><span style="color: #919191; ">S级、A级课题需要填写成本中心及内部订单类型；B级课题无需填写。</span>
<script type="text/javascript">
    AJS.$("#innerOrderTypeSelect").auiSelect2();
</script>
#customControlFooter ($action $customField.id $fieldLayoutItem.fieldDescription $displayParameters $auiparams)