<div>
    <tr>
        <td>
            <label for="fileCate">文件分类：</label>
            <input name="fileCate" id="fileCate" value="$!fileCate">
            <label for="pageFilePath">新页面路径：</label>
##            <textarea class="text medium-long-field" id="pageFilePath" name="pageFilePath">$!pageFilePath</textarea>
            <select name="pageFilePath" id="pageFilePath">#foreach($map in $pathOptionList)
                <option value=${map.get('code')}
                    #if($!pageFilePath == ${map.get('code')}) selected="true" #end>
                    ${map.get('value')}</option>#end
            </select>
        </td>
    </tr>


    <input type="hidden" id="field_label">
</div>

#parse("templates/utils/eve-jira-jql-condition.vm")

<script type="text/javascript">
    // AJS.$("#transitionId,#transitionUserField").auiSelect2();

</script>