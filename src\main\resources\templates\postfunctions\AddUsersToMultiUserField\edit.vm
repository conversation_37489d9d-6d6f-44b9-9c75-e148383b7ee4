<div>
    <tr bgcolor="#ffffff">
        <td bgcolor="#ffffff" nowrap>
            <label for="multiUserField">请选择需要设置的用户字段：</label>
            <select name="multiUserField" id="multiUserField" >
                #foreach($bean in $!customFieldList)
                    <option value="$bean.getId()"
                        #if($!multiUserField==$bean.getId()) selected="true" #end>
                        $!bean.getName()
                    </option>
                #end
            </select>
        </td>
    </tr>
    <tr bgcolor="#ffffff">
        <td bgcolor="#ffffff" nowrap>
            <label for="userName">请输入用户：</label>
            <input type="text" id="userName" name="userName" #if($!userName!="")value="$!userName"#end >
##            <textarea class="textarea long-field userpickerfield" cols="40" id="userList" name="userList" rows="4" style="" wrap="virtual" autocomplete="off"></textarea>
##            <div class="aui-ss ajax-ss">
##                <select id="userName" name="userName"
##                        class="multi-user-picker js-default-user-picker text"
##                        data-container-class="medium-field">
##                    <optgroup label="All Users" data-weight="1">
##                        <option value="$!userName">$!userName</option>
##                    </optgroup>
##                </select>
##            </div>
#*            <div class="aui-ss ajax-ss" id="single-user-picker-username">
                <select id="username" name="username"
                        class="single-user-picker js-default-user-picker text"
                        data-container-class="medium-field">
                    <optgroup label="All Users" data-weight="1">
                        <option value="$!username">$!username</option>
                    </optgroup>
                </select>

            </div>*#
        </td>
    </tr>
    <input type="hidden" id="field_label">
</div>

#parse("templates/utils/eve-jira-jql-condition.vm")

<script type="text/javascript">
    AJS.$("#multiUserField").auiSelect2();
</script>