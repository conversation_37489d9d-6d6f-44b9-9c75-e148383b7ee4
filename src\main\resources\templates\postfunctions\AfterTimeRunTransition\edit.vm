<div>
    <tr bgcolor="#ffffff">
        <td bgcolor="#ffffff" nowrap>
##            #set($workflowName='test2')
            <label for="delayTime">延迟时间：</label>
            <input type="number" id="delayTime" name="delayTime" value="$!delayTime">
            <select name="timeUnit" id="timeUnit">
##                #foreach($param in ${copyTypeMap.keySet()})
##                    <option value=$param
##                        #if($!source_issue == $param) selected="true" #end
##                        #if($param == "epic_link_issue" || $param == "sub_issue") disabled="true" #end>
##                        ${copyTypeMap.get($param)}</option>
##                #end
                <option value="m" #if($!timeUnit == "m") selected="true" #end>分钟</option>
                <option value="h" #if($!timeUnit == "h") selected="true" #end>小时</option>
                <option value="d" #if($!timeUnit == "d") selected='true' #end>天</option>
            </select>
        </td>
    </tr>
    <tr>
        <td>
            <label for="transitionId">转换：</label>
##            <input name="transitionId" id="transitionId" value="$!transitionId">
##            <textarea class="text medium-long-field" id="fileCate" name="fileCate">$!fileCate</textarea>
            <select name="transitionId" id="transitionId">
##                #foreach($bean in $!transitionList)
##                    <option value="$bean.getId()"#if($!transitionId == $bean.getId())selected="true" #end>
##                        $!bean.getName()（$!bean.getId()）
##                    </option>
##                #end
            </select>
        </td>
    </tr>
    <tr>
        <td>
            <label for="transitionUserField">执行人：</label>
##            <input name="transitionId" id="transitionId" value="$!transitionId">
##            <textarea class="text medium-long-field" id="fileCate" name="fileCate">$!fileCate</textarea>
            <select name="transitionUserField" id="transitionUserField">
                #foreach($bean in $!customFieldList)
                    <option value="$bean.getId()"#if($!transitionUserField == $bean.getId())selected="true" #end>
                        $!bean.getName()
                    </option>
                #end
            </select>
        </td>
    </tr>


    <input type="hidden" id="field_label">
</div>

#parse("templates/utils/eve-jira-jql-condition.vm")

<script type="text/javascript">
    AJS.$("#transitionId,#transitionUserField").auiSelect2();


    window.onload = function() {
        getWorkFlowParams();
        // console.log("页面加载完成====》onload");
    }
    function getWorkFlowParams() {

        const workflowStep = $('#workflowStep').attr('value');
        const workflowName = $('#workflowName').attr('value');
        const workflowMode = $('#workflowMode').attr('value');
        const workflowTransition = $('#workflowTransition').attr('value');

        var url = AJS.contextPath() + "/rest/oa2jira/1.0/tool/workFlow/getTransition";
        var oriData={
            workflowStep: workflowStep,
            workflowName: workflowName,
            workflowMode: workflowMode,
            workflowTransition: workflowTransition
        }
        // var data = JSON.stringify(oriData);
        // console.log(workflowName);
        // console.log(workflowMode);
        console.log(oriData);
        // console.log(data);
        jQuery.ajax({
            url: url,
            type: 'POST',
            dataType: "json",
            // data: data,
            data:JSON.stringify(oriData),
            // async: false,
            contentType: "application/json",
            success: function(response) {
                // console.log(response)
                if (response.result == true) {
                    $("#transitionId option").remove();
                    // $("#optionIdList").append("<option value='-1'>--请选择--</option>")
                    for (var i = 0; i < response.value.length; i++) {
                        ## $("#transitionId").append("<option value='$!bean.getId()'>$!bean.getName()（$!bean.getId()）</option>");
                        ## if ($transitionId && response.value[i].id == $transitionId ){
                            $("#transitionId").append("<option value='"+response.value[i].id+"' #if($!transitionId == "d") selected='true' #end >"+response.value[i].name+"（"+response.value[i].id+"）</option>");
                        // }else {
                        ##     $("#transitionId").append("<option value='"+response.value[i].id+"' #if($!timeUnit == "d") selected='true' #end >"+response.value[i].name+"（"+response.value[i].id+"）</option>");
                        // }
                    }
                    // console.log(response.value)
                } else {
                    alert(response.code + "\n" + response.message)
                }
            },
            error: function(jqXHR, textStatus, errorThrown) {
                console.log('获取转换请求错误：' + errorThrown);
            }
        });


    }

</script>