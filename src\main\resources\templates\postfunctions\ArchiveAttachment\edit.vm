<div>
    <tr bgcolor="#ffffff">
        <td bgcolor="#ffffff" nowrap>
            <label for="archiveCate">请选择归档类型：</label>
            <select name="archiveCate" id="archiveCate">
##                #foreach($param in ${copyTypeMap.keySet()})
##                    <option value=$param
##                        #if($!source_issue == $param) selected="true" #end
##                        #if($param == "epic_link_issue" || $param == "sub_issue") disabled="true" #end>
##                        ${copyTypeMap.get($param)}</option>
##                #end
                <option value="topic" selected="true">
                    课题
                </option>
                <option value="gain">
                    成果
                </option>
            </select>
        </td>
    </tr>
    <tr>
        <td>
            <label for="fileCate">文件类别：</label>
            <textarea class="text medium-long-field" id="fileCate" name="fileCate">$!fileCate</textarea>
        </td>
    </tr>


    <input type="hidden" id="field_label">
</div>

#parse("templates/utils/eve-jira-jql-condition.vm")

<script type="text/javascript">
    // AJS.$("#archiveCate").auiSelect2();
</script>