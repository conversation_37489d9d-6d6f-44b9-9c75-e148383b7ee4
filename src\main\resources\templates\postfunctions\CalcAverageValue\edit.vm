<div>
    <tr>
        <td>
            <label for="sourceFields">源字段：</label>
            <select class="select filter-single-select" name="sourceFields" id="sourceFields" multiple="multiple">
                #foreach($bean in $!customFieldList)
                    <option value="$bean.getId()"
                        #if($!sourceFields.contains($bean.getId())) selected="true" #end>
                        $!bean.getName()
                    </option>
                #end
            </select>
        </td>
    </tr>

    <tr>
        <td>
            <label for="targetField">目标字段：</label>
            <select class="select filter-single-select" name="targetField" id="targetField">
                #foreach($bean in $!customFieldList)
                    <option value="$bean.getId()"
                        #if($!targetField == $bean.getId()) selected="true" #end>
                        $!bean.getName()
                    </option>
                #end
            </select>
        </td>
    </tr>


    <input type="hidden" id="field_label">
</div>

#parse("templates/utils/eve-jira-jql-condition.vm")

<script type="text/javascript">
    AJS.$("#sourceFields,#targetField").auiSelect2();

</script>