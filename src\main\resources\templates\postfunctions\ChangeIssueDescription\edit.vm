<div>
    <div>
        <tr bgcolor="#ffffff">
            <td bgcolor="#ffffff" nowrap>
                字段
                <select name="fieldId" id="fieldId">
                    #foreach($bean in $!customFieldList)
                        <option value="$bean.getId()"
                            #if($!fieldId==$bean.getId()) selected="true" #end>
                            $!bean.getName()
                        </option>
                    #end
                </select>
            </td>
        </tr>
        <tr bgcolor="#ffffff">
            <td bgcolor="#ffffff" nowrap>
                选项
                <select name="optionIdList" id="optionIdList" multiple>
                    #foreach($bean in $!optionsList)
                        <option value="$bean.getOptionId()"
                            #if($!optionIdList&&$!optionIdList.indexOf($bean.getOptionId().toString()) != -1)
                                selected="true" #end>
                            $!bean.getOptionVal()
                        </option>
                    #end
                </select>
            </td>
        </tr>
        <tr>
            <td>
                <textarea rows="4" cols="50" name="descriptionText" id="descriptionText"
                          placeholder="在此输入要更新的描述">$!descriptionText</textarea>
            </td>
        </tr>
    </div>
    <div>
        <tr>
            #parse("templates/utils/eve-jira-jql-condition.vm")
        </tr>
    </div>
</div>
<script type="text/javascript">
    AJS.$("#fieldId").auiSelect2();
    AJS.$("#optionIdList").auiSelect2();

    jQuery("#fieldId").change(function (){

        var value = jQuery("#fieldId").val();

        // if (value==-1){
        //     $("#optionIdList option").remove();
        //     // $("#sign_field").append("<option value='-1'>--请选择--</option>")
        //     return
        // }
        console.log(value);
        var url = AJS.contextPath() + "/rest/oa2jira/1.0/field/option/query/field/" + value.split('_')[1];
        jQuery.ajax({
            type: "GET",
            url: url,
            data: "",
            dataType: "json",
            async: false,
            contentType: "application/json",
            success: function (response) {
                console.log(response)
                if (response.result == true) {
                    $("#optionIdList option").remove();
                    // $("#optionIdList").append("<option value='-1'>--请选择--</option>")
                    for (var i = 0; i < response.value.length; i++) {
                        $("#optionIdList").append("<option value='"+response.value[i].optionId+"'>"+response.value[i].optionVal+"</option>");
                    }
                    console.log(response.value)
                } else {
                    alert(response.code + "\n" + response.message)
                }
            }
        });
    })
</script>