<div>
#*    <form class="aui">
        请选择复制类型：
        <select id="target_issue">
            <option value="current_issue" selected>当前问题</option>
            <option value="parent_issue">父任务</option>
            <option value="sub_issue">子任务</option>
            <option value="epic_issue">EPIC</option>
            <option value="epic_link_issue">EPIC链接的问题</option>
        </select>
    </form>*#

#*    <tr bgcolor="#ffffff">
        <td bgcolor="#ffffff" nowrap>
            请选择复制类型：
            <select name="target_issue" id="target_issue">
                <option value="current_issue" selected>当前问题</option>
                <option value="parent_issue">父任务</option>
                <option value="sub_issue">子任务</option>
                <option value="epic_issue">EPIC</option>
                <option value="epic_link_issue">EPIC链接的问题</option>
            </select>
        </td>
    </tr>*#

#*    <form class="aui">
        请选择复制类型：
        <select id="target_issue">
            #foreach($param in ${copyTypeMap.keySet()})
                <option value=$param >${copyTypeMap.get($param)}</option>
            #end
        </select>
    </form>*#
    <tr bgcolor="#ffffff">
        <td bgcolor="#ffffff" nowrap>
            请选择源问题：
            <select name="source_issue" id="source_issue">
                #foreach($param in ${copyTypeMap.keySet()})
                    <option value=$param
                        #if($param == "current_issue") selected="true" #end
                        #if($param == "epic_link_issue" || $param == "sub_issue") disabled="true" #end>
                        ${copyTypeMap.get($param)}</option>
                #end
            </select>
        </td>
    </tr>
    <tr bgcolor="#ffffff">
        <td bgcolor="#ffffff" nowrap>
            请选择目标问题：
            <select name="target_issue" id="target_issue" multiple="multiple">
                #foreach($param in ${copyTypeMap.keySet()})
                    <option value=$param #if($param == "current_issue") selected="true" #end>${copyTypeMap.get($param)}</option>
                #end
            </select>
        </td>
    </tr>
    <tr bgcolor="#ffffff">
        <td bgcolor="#ffffff" nowrap>
            请选择目标问题类型：
            <select name="issue_type" id="issue_type" multiple="multiple">
##                <option style='display:none'>
                <option value="" selected="ture">不限问题类型</option>
                #foreach($bean in $!allIssueTypeList)
                    <option value="$bean.getId()" >$bean.getName()</option>
                #end
            </select>
        </td>
    </tr>

    <tr bgcolor="#ffffff">
        <td bgcolor="#ffffff" nowrap>
            请选择字段类型：
            <select name="field_type" id="field_type" >
                <option style='display:none'>
                #foreach($bean in $!customFieldTypeList)
                    <option value="$bean.getKey()" #if("com.atlassian.jira.plugin.system.customfieldtypes:userpicker" == $bean.getKey()) selected="true" #end>$bean.getName()</option>
                #end
            </select>
        </td>
    </tr>

    <tr bgcolor="#ffffff">
        <td bgcolor="#ffffff" nowrap>
            请选择源字段：
            <select name="source_field" id="source_field">
                #foreach($bean in $!customFieldList)
                    <option value="$bean.getId()" >$bean.getName()</option>
                #end
            </select>
        </td>
    </tr>
    <tr bgcolor="#ffffff">
        <td bgcolor="#ffffff" nowrap>
            请选择目标字段：
            <select name="target_field" id="target_field" multiple="multiple">
                #foreach($bean in $!customFieldList)
                    <option value="$bean.getId()" #if("assignee" == $bean.getId()) selected="true" #end>$bean.getName()</option>
                #end
            </select>
        </td>
    </tr>
    <input type="hidden" id="field_label">
</div>

#parse("templates/utils/eve-jira-jql-condition.vm")

<script type="text/javascript">
    AJS.$("#source_issue,#target_issue,#issue_type,#field_type,#source_field,#target_field").auiSelect2();
    jQuery("#field_type").change(function (){
        //alert("selectIsChange");
        var value = jQuery("#field_type").val();

        var url = AJS.contextPath() + "/rest/oa2jira/1.0/field/copy/query/" + value;
        jQuery.ajax({
            type: "GET",
            url: url,
            data: "",
            dataType: "json",
            async: false,
            contentType: "application/json",
            success: function (response) {
                var fieldSelect = $('#source_field,#target_field');
                // console.log(response)
                if (response.result == true) {
                    fieldSelect.empty();
                    for (var i = 0; i < response.value.length; i++) {
                        fieldSelect.append("<option value='"+response.value[i].id+"'>"+response.value[i].name+"</option>");
                    }
                    if (response.value.length == 0) {
                        fieldSelect.auiSelect2("val", "");
                    } else {
                        fieldSelect.val(response.value[0].id).trigger("change");
                    }
                    //console.log(response.value)
                } else {
                    alert(response.code + "\n" + response.message)
                }
            }
        });
    })



</script>