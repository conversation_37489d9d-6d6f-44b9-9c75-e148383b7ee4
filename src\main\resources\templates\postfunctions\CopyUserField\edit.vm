<div>
    <tr bgcolor="#ffffff">
        <td bgcolor="#ffffff" nowrap>
            请选择源问题：
            <select name="source_issue" id="source_issue">
                #foreach($param in ${copyTypeMap.keySet()})
                    <option value=$param
                        #if($!source_issue == $param) selected="true" #end
                        #if($param == "epic_link_issue" || $param == "sub_issue") disabled="true" #end>
                        ${copyTypeMap.get($param)}</option>
                #end
            </select>
        </td>
    </tr>
    <tr bgcolor="#ffffff">
        <td bgcolor="#ffffff" nowrap>
            请选择目标问题：
            <select name="target_issue" id="target_issue" multiple="multiple">
                #foreach($param in ${copyTypeMap.keySet()})
                    <option value=$param
                        #if($!target_issue.contains($param)) selected="true" #end>
                        ${copyTypeMap.get($param)}</option>
                #end
            </select>
        </td>
    </tr>
    <tr bgcolor="#ffffff">
        <td bgcolor="#ffffff" nowrap>
            请选择目标问题类型：
            <select name="issue_type" id="issue_type" multiple="multiple">
                <option value="" #if($!issue_type.contains("")) selected="true" #end>不限问题类型</option>
                #foreach($bean in $!allIssueTypeList)
                    <option value="$bean.getId()"
                        #if($!issue_type.contains($bean.getId())) selected="true" #end>
                        $bean.getName()</option>
                #end
            </select>
        </td>
    </tr>
##    <tr bgcolor="#ffffff">
##        <td bgcolor="#ffffff" nowrap>
##            请选择字段类型：
##            <select name="field_type" id="field_type" >
##            <option style='display:none'>
##                #foreach($bean in $!customFieldTypeList)
##                    <option value="$bean.getKey()"
##                        #if($!field_type == $bean.getKey()) selected="true" #end>
##                        $bean.getName()</option>
##                #end
##            </select>
##        </td>
##    </tr>
    <tr bgcolor="#ffffff">
        <td bgcolor="#ffffff" nowrap>
            请选择源字段：
            <select name="source_field" id="source_field">
                #foreach($bean in $!customFieldList)
                    <option value="$bean.getId()"
                        #if($!source_field == $bean.getId()) selected="true" #end>
                        $bean.getName()</option>
                #end
            </select>
        </td>
    </tr>
    <tr bgcolor="#ffffff">
        <td bgcolor="#ffffff" nowrap>
            请选择目标字段：
            <select name="target_field" id="target_field" multiple="multiple">
                #foreach($bean in $!customFieldList)
                    <option value="$bean.getId()"
                        #if($!target_field.contains($bean.getId())) selected="true" #end>
                        $bean.getName()</option>
                #end
            </select>
        </td>
    </tr>
    <input type="hidden" id="field_label">
</div>

#parse("templates/utils/eve-jira-jql-condition.vm")

<script type="text/javascript">
    AJS.$("#source_issue,#target_issue,#issue_type,#field_type,#source_field,#target_field").auiSelect2();
    // jQuery("#field_type").change(function (){
    //     var value = jQuery("#field_type").val();
    //     var url = AJS.contextPath() + "/rest/oa2jira/1.0/field/copy/query/" + value;
    //     jQuery.ajax({
    //         type: "GET",
    //         url: url,
    //         data: "",
    //         dataType: "json",
    //         async: false,
    //         contentType: "application/json",
    //         success: function (response) {
    //             var fieldSelect = $('#source_field,#target_field');
    //             if (response.result == true) {
    //                 fieldSelect.empty();
    //                 for (var i = 0; i < response.value.length; i++) {
    //                     fieldSelect.append("<option value='"+response.value[i].id+"'>"+response.value[i].name+"</option>");
    //                 }
    //                 if (response.value.length == 0) {
    //                     fieldSelect.auiSelect2("val", "");
    //                 } else {
    //                     fieldSelect.val(response.value[0].id).trigger("change");
    //                 }
    //             } else {
    //                 alert(response.code + "\n" + response.message)
    //             }
    //         }
    //     });
    // })
</script>