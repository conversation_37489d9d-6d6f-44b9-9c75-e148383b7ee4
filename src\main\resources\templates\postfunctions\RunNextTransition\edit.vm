<div>
    <tr bgcolor="#ffffff">
        <td bgcolor="#ffffff" nowrap>
            #set($workflowName='test2')
            #set($transitionIdParm=$!transitionId)
            <label for="transitionId">请选择需要执行的转换：</label>
            <select id="transitionId" name="transitionId">
##                #foreach($bean in $!workflowBeanList)
##                    #if($bean.getWorkflowName() == $!workflowName)
##                        #foreach( $transitionEntry in $!bean.getTransitionEntryList())
##                            <option value="$transitionEntry.getTransitionId()"
##                                #if($!transitionId==$transitionEntry.getTransitionId()) selected="true" #end>
##                                $!{transitionEntry.getName()}(${transitionEntry.getTransitionId()})
##                            </option>
##                        #end
##                    #end
##                    $bean.getWorkflowName()、
##                #end
            </select>


            转换ID：$!transitionIdParm
        </td>
    </tr>

</div>

#parse("templates/utils/eve-jira-jql-condition.vm")

<script type="text/javascript">
    AJS.$("#transitionId").auiSelect2();

    function getOption(){
        var workflowName = $('#workflowName').attr('value');

        var url = AJS.contextPath() + "/rest/oa2jira/1.0/field/copy/query/Transition/" + workflowName;
        jQuery.ajax({
            type: "GET",
            url: url,
            data: "",
            dataType: "json",
            async: false,
            contentType: "application/json",
            success: function (response) {
                console.log("3:" + response);
                if (response.result == true) {
                    $('#transitionId').empty();
                    var depts = response.value;
                    $(depts).each(function(index,item){
                        console.log("4:" + item);
                        if ('$transitionIdParm' == item.transitionId) {
                            $("#transitionId").append("<option value='" + item.transitionId + "' selected='selected'>" + item.transitionName + '#' + item.transitionId + "</option>");
                        } else {
                            $("#transitionId").append("<option value='" + item.transitionId + "'>" + item.transitionName + '#' + item.transitionId + "</option>");
                        }
                    })
                } else {
                    alert(response.message);
                }
            }
        });
    }

    $(function () {
        getOption()
          console.log("1:"+'$workflowName');
        // var workflowName = $('workflowName').val();
        // var name = $('#workflowName').attr('value');
##        #set($workflowName = $workflowName1);
//         window.location.reload();
//         console.log("2:"+name);
    });
</script>