<div>
    <tr bgcolor="#ffffff">
        <td bgcolor="#ffffff" nowrap>
            <label for="issueRelation">与当前ISSUE关系：</label>
##            <select name="multiUser" id="multiUser" >
##                #foreach($bean in $!customFieldList)
##                    <option value="$bean.getId()"
##                        #if($!multiUser==$bean.getId()) selected="true" #end>
##                        $!bean.getName()
##                    </option>
##                #end
##            </select>
            <select name="issueRelation" id="issueRelation">
                #foreach($param in ${copyTypeMap.keySet()})
                    <option value=$param
                        #if($!issueRelation == $param) selected="true" #end>
                        ${copyTypeMap.get($param)}</option>
                #end
            </select>
        </td>
    </tr>
    <tr bgcolor="#ffffff">
        <td bgcolor="#ffffff" nowrap>
            <label for="issueStatusList">指定问题状态：</label>
            <select name="issueStatusList" id="issueStatusList" multiple="multiple">
                #foreach($bean in $!allIssueStatusList)
                    <option value="$bean.getId()"
                        #if($!issueStatusList.contains($bean.getId())) selected="true" #end>
                        $bean.getName()</option>
                #end
            </select>
        </td>
    </tr>
    <tr bgcolor="#ffffff">
        <td bgcolor="#ffffff" nowrap>
            <label for="copyCustomFieldOfScreen">是否复制界面自定义字段：</label>
            <select name="copyCustomFieldOfScreen" id="copyCustomFieldOfScreen" multiple="multiple">
##                #foreach($bean in $!allIssueStatusList)
##                    <option value="$bean.getId()"
##                        #if($!issueStatusList.contains($bean.getId())) selected="true" #end>
##                        $bean.getName()</option>
##                #end
                <option value="true"
                    #if($!copyCustomFieldOfScreen.contains("true")) selected="true" #end>
                    复制</option>
                <option value="false"
                    #if($!copyCustomFieldOfScreen.contains("false")) selected="true" #end>
                    不复制</option>
            </select>
        </td>
    </tr>
    <tr bgcolor="#ffffff">
        <td bgcolor="#ffffff" nowrap>
            <label for="transitionName">要执行的转换名：</label>
            <input name="transitionName" id="transitionName" value="$!transitionName">
        </td>
    </tr>
    <input type="hidden" id="field_label">
</div>
<script type="text/javascript">
    AJS.$("#issueRelation").auiSelect2();
    AJS.$("#issueStatusList").auiSelect2();


    const selectElement = document.querySelector(".issueRelation");


    selectElement.addEventListener("change", (event) => {
        //    todo 问题状态选择未指定时，清空全部选项
        if (event.target.value != null && event.target.value == "unAssinge") {
            // event.target.value = ["unAssinge"];
            selectElement.value = ["unAssinge"];
        }
    });
</script>