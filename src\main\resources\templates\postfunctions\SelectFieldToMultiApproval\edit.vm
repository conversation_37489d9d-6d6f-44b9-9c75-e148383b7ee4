<div>
    <tr bgcolor="#ffffff">
        <td bgcolor="#ffffff" nowrap>
            <label for="multiUser">请选择审批人字段：</label>
            <select name="multiUser" id="multiUser" >
                #foreach($bean in $!customFieldList)
                    <option value="$bean.getId()"
                        #if($!multiUser==$bean.getId()) selected="true" #end>
                        $!bean.getName()
                    </option>
                #end
            </select>
        </td>
    </tr>
    <tr bgcolor="#ffffff">
        <td bgcolor="#ffffff" nowrap>
            <label for="multiUser2">请选择记录字段：</label>
            <select name="multiUser2" id="multiUser2" >
                #foreach($bean in $!customFieldList)
                    <option value="$bean.getId()"
                        #if($!multiUser2==$bean.getId()) selected="true" #end>
                        $!bean.getName()
                    </option>
                #end
            </select>
        </td>
    </tr>
    <input type="hidden" id="field_label">
</div>
<script type="text/javascript">
    AJS.$("#multiUser").auiSelect2();
    AJS.$("#multiUser2").auiSelect2();
</script>