<div>
    <tr bgcolor="#ffffff">
        <td bgcolor="#ffffff" nowrap>
            <label for="sendFields">请选择需要邮件内容需要的字段：</label>
            <select id="sendFields" name="sendFields" multiple="multiple">
                #foreach($bean in $!customFieldList)
                    <option value="$bean.getId()"
                        #if($!sendFields.contains($bean.getId())) selected="true" #end>
                        $!bean.getName()
                    </option>
                #end
            </select>
        </td>
    </tr>
    <tr bgcolor="#ffffff">
        <td bgcolor="#ffffff" nowrap>
            <label for="sendUserFields">请选择需要接收邮件的用户字段：</label>
            <select id="sendUserFields" name="sendUserFields" multiple="multiple">
                #foreach($bean in $!userPickerCustomFieldList)
                    <option value="$bean.getId()"
                        #if($!sendUserFields.contains($bean.getId())) selected="true" #end>
                        $!bean.getName()
                    </option>
                #end
            </select>
        </td>
    </tr>
    <tr bgcolor="#ffffff">
        <td bgcolor="#ffffff" nowrap>
            <label for="emailTitle">请输入邮件标题格式（{字段id}添加字段值）：</label>
            <textarea class="textarea" name="emailTitle" id="emailTitle" placeholder="在此输入邮件标题：">#if($!emailTitle != '')$!emailTitle#end</textarea>
        </td>
    </tr>
    <input type="hidden" id="field_label">
</div>

#parse("templates/utils/eve-jira-jql-condition.vm")

<script type="text/javascript">
    AJS.$("#sendFields,#sendUserFields").auiSelect2();
</script>