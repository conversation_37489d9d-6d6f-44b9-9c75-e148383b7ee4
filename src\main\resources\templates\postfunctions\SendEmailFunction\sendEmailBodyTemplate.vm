## 内容模板
<div style="width:100%;overflow-x:scroll;">
    <table cellspacing="2" cellpadding="2" style="width: 1400px;margin: 0;border-collapse: collapse;border-spacing: 0;">
##        <tr>
##            <th colspan="15"
##                style="vertical-align:middle;height: 25px;line-height: 25px;text-align: center;border: 1px solid #b3bed5;background: #eaf0fa;font-weight: normal;font-size:18px">
##                $!emailTitle
##            </th>
##        </tr>

        <tr>
            #foreach($bean in $!fieldNameList)
                <th style="height: 25px;line-height: 25px;text-align: center;border: 1px solid #b3bed5;background: #eaf0fa;font-weight: normal;font-size:14px">
                    $bean
                </th>
            #end
        </tr>

        <tr>
            #foreach($bean in $!fieldValueList)
                <td style="height: 25px;line-height: 25px;text-align: center;border: 1px solid #b3bed5;font-size:12px;font-weight:normal">
                    #if($velocityCount==1)
                        <a style="text-decoration: none ;color:#0d87d8 "
                           href="http://jira.evebattery.com/browse/$!bean">$!bean</a>
                    #else
                        $bean.replaceAll("\n","<br />")
                    #end
                </td>
            #end
        </tr>
    </table>
</div>