<div>
    <tr bgcolor="#ffffff">
        <td bgcolor="#ffffff" nowrap>
            <label for="userField">请选择需要设置的用户字段：</label>
            <select name="userField" id="userField" >
                #foreach($bean in $!customFieldList)
                    <option value="$bean.getId()"
                        #if($!userField==$bean.getId()) selected="true" #end>
                        $!bean.getName()
                    </option>
                #end
            </select>
            <label for="fieldValue">请输入用户：</label>
            <input type="text" id="fieldValue" name="fieldValue" #if($!fieldValue!="")value="$!fieldValue"#end >
        </td>
    </tr>
    <input type="hidden" id="field_label">
</div>

#parse("templates/utils/eve-jira-jql-condition.vm")

<script type="text/javascript">
    AJS.$("#userField").auiSelect2();
</script>