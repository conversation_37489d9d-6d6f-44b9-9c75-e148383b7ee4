<div>
    <tr bgcolor="#ffffff">
        <td bgcolor="#ffffff" nowrap>
            请选择需要设置的用户字段：
            <select name="userField" id="userField" >
                #foreach($bean in $!customFieldList)
                    <option value="$bean.getId()"
                        $!bean.getName()
                    </option>
                #end
            </select>
            请输入用户名（工号）：
            <input type="text" id="fieldValue" name="fieldValue">
        </td>
    </tr>
    <input type="hidden" id="field_label">
</div>
<script type="text/javascript">
    AJS.$("#userField").auiSelect2();
</script>