<div>
#*    <tr bgcolor="#ffffff">
        <td bgcolor="#ffffff" nowrap>
            请选择字段签字类型
            <select name="field_sign" id="field_sign">
                <option value="-1" selected>--请选择--</option>
                #foreach ($bean in $fieldSignBeanList)
                    <option value="$bean.getId()" #if($!fieldSign == $bean.getId())
                            selected="true" #end>$bean.getSignName()</option>
                #end
            </select>
        </td>
    </tr>*#
    <tr bgcolor="#ffffff">
        <td bgcolor="#ffffff" nowrap>
            请选择字段类型
            <select name="field_type" id="field_type">
                #foreach($bean in $!optionBeanList)
                    <option value="$bean.getValue()"
                    #if($bean.getValue()==$!fieldType)
                        selected
                    #end
                    >$!bean.getName()</option>
                #end
            </select>
        </td>
    </tr>
    <tr bgcolor="#ffffff">
        <td bgcolor="#ffffff" nowrap>
            请选择字段:
            <select name="sign_field" id="sign_field">
                <option value="-1">--请选择--</option>
                <option value="$!signFieldId" selected>$!fieldName</option>
            </select>
        </td>
    </tr>
    <tr bgcolor="#ffffff">
        <td bgcolor="#ffffff" nowrap>
            请输入偏移天数:
            <input type="number" name="offsetDay" id="offsetDay"  value="$!offsetDay">
        </td>
    </tr>
</div>
#parse("templates/utils/eve-jira-jql-condition.vm")
<script>

    jQuery("#field_type").change(function (){

        var value = jQuery("#field_type").val();

        if (value==-1){
            $("#sign_field option").remove();
            $("#sign_field").append("<option value='-1'>--请选择--</option>")
            return
        }

        var url = AJS.contextPath() + "/rest/eve/1.0/field/sign/query/field/" + value;
        jQuery.ajax({
            type: "GET",
            url: url,
            data: "",
            dataType: "json",
            async: false,
            contentType: "application/json",
            success: function (response) {
                console.log(response)
                if (response.result == true) {
                    $("#sign_field option").remove();
                    $("#sign_field").append("<option value='-1'>--请选择--</option>")
                    for (var i = 0; i < response.value.length; i++) {
                        $("#sign_field").append("<option value='"+response.value[i].id+"'>"+response.value[i].name+"</option>");
                    }
                    console.log(response.value)
                } else {
                    alert(response.code + "\n" + response.message)
                }
            }
        });
    })



</script>