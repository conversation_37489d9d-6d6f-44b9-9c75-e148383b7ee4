<div>
##    <tr bgcolor="#ffffff">
##        <td bgcolor="#ffffff" nowrap>
##            请选择字段类型
##            <select name="field_type" id="field_type">
##                <option value="-1" selected>--请选择--</option>
##                #foreach($bean in $!customFieldTypeList)
##                    <option value="$bean.getKey()" #if($!field_field == $bean.getName()) selected="true" #end>$bean.name</option>
##                #end
##            </select>
##        </td>
##    </tr>
    <tr bgcolor="#ffffff">
        <td bgcolor="#ffffff" nowrap>
            请选择字段
            <select name="field_field" id="field_field">
                <option value="-1" selected>--请选择--</option>
                #foreach($bean in $!customFieldList)
                    <option value="$bean.getIdAsLong()" #if($!customFieldId == $bean.getIdAsLong()) selected="true" #end>$bean.name</option>
                #end
            </select>
        </td>
    </tr>
    <tr bgcolor="#ffffff">
        <td bgcolor="#ffffff" nowrap>
            请选择选项值:
            <select name="field_value" id="field_value">
                <option value="-1" selected>--请选择--</option>
            </select>
        </td>
    </tr>
    <input type="hidden" id="field_label">
</div>
#parse("templates/utils/eve-jira-jql-condition.vm")
<script>
    // AJS.$("#field_type,#field_field,#field_value").auiSelect2();
    AJS.$("#field_field,#field_value").auiSelect2();

    // jQuery("#field_type").change(function (){
    //
    //     var value = jQuery("#field_type").val();
    //
    //     if (value==-1){
    //         $("#field_field option").remove();
    //         $("#field_field").append("<option value='-1'>--请选择--</option>")
    //         return
    //     }
    //
    //     var url = AJS.contextPath() + "/rest/oa2jira/1.0/field/option/query/field/" + value;
    //     jQuery.ajax({
    //         type: "GET",
    //         url: url,
    //         data: "",
    //         dataType: "json",
    //         async: false,
    //         contentType: "application/json",
    //         success: function (response) {
    //             console.log(response)
    //             if (response.result == true) {
    //                 $("#field_value option").remove();
    //                 $("#field_value").append("<option value='-1'>--请选择--</option>")
    //                 for (var i = 0; i < response.value.length; i++) {
    //                     $("#field_value").append("<option value='"+response.value[i].optionId+"'>"+response.value[i].optionVal+"</option>");
    //                 }
    //                 console.log(response.value)
    //             } else {
    //                 alert(response.code + "\n" + response.message)
    //             }
    //         }
    //     });
    // })

    jQuery("#field_field").change(function (){

        var value = jQuery("#field_field").val();

        if (value==-1){
            $("#field_value option").remove();
            $("#field_value").append("<option value='-1'>--请选择--</option>")
            return
        }

        var url = AJS.contextPath() + "/rest/oa2jira/1.0/field/option/query/field/" + value;
        jQuery.ajax({
            type: "GET",
            url: url,
            data: "",
            dataType: "json",
            async: false,
            contentType: "application/json",
            success: function (response) {
                console.log(response)
                if (response.result == true) {
                    $("#field_value option").remove();
                    $("#field_value").append("<option value='-1'>--请选择--</option>")
                    for (var i = 0; i < response.value.length; i++) {
                        $("#field_value").append("<option value='"+response.value[i].optionId+"'>"+response.value[i].optionVal+"</option>");
                    }
                    console.log(response.value)
                } else {
                    alert(response.code + "\n" + response.message)
                }
            }
        });
    })



</script>