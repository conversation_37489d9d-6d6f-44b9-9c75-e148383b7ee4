<style>
    .aui-page-panel-content{
        padding:0;
    }
    .content{
        display:flex;
        min-height:300px;
    }
    .content .left{
        width:200px;
        border-right:1px solid #dfe1e6;
        padding:15px;
    }
    .content .right{
        flex:1;
        padding:15px;
    }
    .btn {
        float: left;
        width: 22%;
        height:46px;
        background: #ECECEC;
        text-decoration: none;
        color: #333;
        border-color: rgba(0, 0, 0, 0.0);
        display: inline-block;
        padding: 6px 12px;
        font-size: 14px;
        font-weight: 400;
        line-height: 1.42857;
        text-align: center;
        white-space: nowrap;
        margin-right:1px;
        margin-bottom:1px;
    }
    .btn:hover,.btn:focus{
        text-decoration: none;
    }
</style>
<div class="content">
    <div class="left">
        <nav class="aui-navgroup aui-navgroup-vertical">
            <div class="aui-navgroup-inner">
                #if("0" != $!cateId)
                <ul class="aui-nav">
                    #foreach($bean in $!categoryBeans)
                    
                    <li class="#if($!bean.getCategoryId() == $!cateId) aui-nav-selected #end">
                        <a href="$baseURL/secure/admin/catesProjectsAction!mainpage.jspa?cateId=$!bean.getCategoryId()">$bean.getCategoryName()</a>
                    </li>
                    #end
                </ul>
                #else
                <ul class="aui-nav">
                    <li class="aui-nav-selected">
                        <a href="$baseURL/secure/admin/catesProjectsAction!mainpage.jspa">
                            所有项目
                        </a>
                    </li>
                </ul>
                #end
            </div>
        </nav>
    </div>
    <div class="right">
        #foreach($bean in $!projectBeans)
        <a class="btn" target="_blank"
           href="$baseURL/projects/$!bean.getProjectKey()/issues">$bean.getProjectName()</a>
        #end
    </div>

</div>


