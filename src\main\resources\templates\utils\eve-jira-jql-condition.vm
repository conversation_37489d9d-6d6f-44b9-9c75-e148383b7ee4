$webResourceManager.requireResourcesForContext("com.atlassian.auiplugin:aui-experimental-iconfont")
$webResourceManager.requireResourcesForContext("com.atlassian.auiplugin:aui-toggle")
$webResourceManager.requireResourcesForContext("com.atlassian.auiplugin:aui-select")
$webResourceManager.requireResourcesForContext("com.atlassian.auiplugin:aui-select2")
$webResourceManager.requireResourcesForContext("com.atlassian.auiplugin:aui-form-validation")
##$webResourceManager.requireResourcesForContext("eve-jira-action-condition")

<input type="string" style="display:none" value="$jqlConditionEnabled" id="jqlConditionEnabled-input" name="jqlConditionEnabled"/>
<input type="string" style="display:none" value="$jqlCondition" id="jqlCondition-input" name="jqlCondition"/>

<form onsubmit="return false;" class="aui">
</form>

<div id="actionConditionContainer">
    <p id="actionConditionContainerTitle">执行前置条件，满足则执行</p>
    <form onsubmit="return false;" class="aui">
        <div id="jqlConditionWholeContainer">
            <div class="field-group">
                <aui-toggle id="jqlConditionEnabled" label="Use gzip compression"></aui-toggle>
                <label for="jqlConditionEnabled">JQL条件</label>
            </div>
            <div class="field-group" id="jqlConditionContainer">
                <label id="jqlConditionLabel" for="jqlCondition">JQL<b class="required">*</b></label>
                <input id="jqlCondition" style="overflow: hidden; height: 30px;" autocomplete="off"/>
                <div>
                    <span id="jqlConditionError" class="aui-icon aui-icon-small aui-iconfont-error" original-title="Error"></span>
                    <span id="jqlConditionApproval" class="aui-icon aui-icon-small aui-iconfont-approve" original-title="Approve"></span>
                    <span id="jqlConditionValidate">检验JQL有效性</span>
                </div>
                <div id="jqlConditionRequired" class="description requiredMessage">JQL条件为必填</div>
            </div>
        </div>
    </form>
</div>

<script type="text/javascript">

    AJS.$(function() {
        //载入时控制jql条件显示
        if (AJS.$("#jqlConditionEnabled-input").val() == "true") {
            var toggle = document.getElementById('jqlConditionEnabled');
            toggle.checked = true;
            AJS.$("#jqlCondition").val(AJS.$("#jqlCondition-input").val());
            AJS.$("#jqlConditionWholeContainer").addClass("wholeContainer");
            AJS.$("#jqlConditionContainer").show();
        } else {
            AJS.$("#jqlConditionWholeContainer").removeClass("wholeContainer");
            AJS.$("#jqlConditionContainer").hide();
        }
        //开关jql条件
        document.getElementById("jqlConditionEnabled").addEventListener('change', function(e) {
            var isChecked = document.getElementById("jqlConditionEnabled").checked;
            if (isChecked) {
                AJS.$("#jqlConditionEnabled-input").val("true");
                AJS.$("#jqlConditionContainer").show();
                AJS.$("#jqlConditionWholeContainer").addClass("wholeContainer");
            } else {
                AJS.$("#jqlConditionEnabled-input").val("false");
                AJS.$("#jqlConditionContainer").hide();
                AJS.$("#jqlCondition").val("");
                AJS.$("#jqlConditionWholeContainer").removeClass("wholeContainer");
            }
        });

        AJS.$(document).on("change", "#jqlCondition", function(e) {
            AJS.$("#jqlCondition-input").val(AJS.$("#jqlCondition").val());
        });
        //校验jql
        AJS.$(document).on('click', '#jqlConditionValidate', function() {
            var jql = AJS.$("#jqlCondition").val();
            sendJQLToVerification(jql);
        });

        AJS.$("form[name='jiraform']").attr("onsubmit", null);
        AJS.$("form[name='jiraform']").on("aui-valid-submit", function(event) {
            AJS.$("#jqlConditionRequired").hide();
            var shouldPrevent = false;
            var toggleJqlConditionEnabled = document.getElementById('jqlConditionEnabled');
            if (toggleJqlConditionEnabled.checked && (AJS.$("#jqlCondition").val() == null || AJS.$("#jqlCondition").val() == "")) {
                shouldPrevent = true;
                AJS.$("#jqlConditionRequired").show();
            }

            if (shouldPrevent == true) {
                event.preventDefault();
            }

        });

    });


    function sendJQLToVerification(jql) {
        jQuery.ajax({
            url: AJS.contextPath() + '/rest/api/2/search?maxResult=1&jql=' + jql,
            type: 'GET',
            success: function(response) {
                AJS.$("#jqlConditionError").hide();
                AJS.$("#jqlConditionApproval").show();
            },
            error: function(xhr, ajaxOptions, thrownError) {
                AJS.$("#jqlConditionError").show();
                AJS.$("#jqlConditionApproval").hide();
            }
        });
    }

    // JIRA.bind(JIRA.Events.NEW_CONTENT_ADDED, function(e, context, reason) {
    //     if (reason !== JIRA.CONTENT_ADDED_REASON.panelRefreshed) {
    //         createGroupPicker(context);
    //     }
    // });
    //
    // function createGroupPicker(ctx) {
    //     new AJS.SingleSelect({
    //         element: AJS.$("#selectMembershipGroups"),
    //         submitInputVal: true,
    //         showDropdownButton: false,
    //         errorMessage: AJS.format("There is no such user \'\'{0}\'\'.", "'{0}'"),
    //         ajaxOptions: {
    //             url: AJS.contextPath() + "/rest/api/2/groups/picker",
    //             query: true, // keep going back to the sever for each keystroke
    //             formatResponse: JIRA.GroupPickerUtil.formatResponse
    //         }
    //     });
    // }
</script>


<style>


    #actionConditionContainerTitle {
        margin-bottom: 15px;
        font-size: 25px;
    }

    #actionConditionContainer {
        width: 500px;
        padding-left: 50px;
        padding-right: 50px;
        padding-top: 15px;
        padding-bottom: 20px;
        border: 1px solid #dfe1e6;
        border-radius: 3px;
        margin-top: 15px;
    }

    #jqlConditionError {
        color: red;
        display: none;
        padding-top: 15px;
    }

    #jqlConditionApproval {
        color: green;
        display: none;
        padding-top: 15px;
    }

    #jqlConditionValidate {
        color: #036;
        padding-left: 25px;
    }

    #jqlConditionValidate:hover {
        cursor: pointer;
    }

    #jqlConditionContainer {
        text-align: right;
        width: 400px;
        display: none;
        margin-top: 15px;
    }

    #jqlCondition {
        width: 400px;
        height: 30px;
        padding-left: 26px;
        padding-right: 35px;
        resize: none;
        display: table-row;
        max-width: inherit;
        border: 2px solid #dfe1e6;
        border-radius: 3.01px;
        box-sizing: border-box;
        font-size: inherit;
        font-family: inherit;
        line-height: 1.4285714285714;
        padding: 3px 4px;
    }

    #jqlCondition:hover {
        background-color: #ebecf0;
        border-color: #dfe1e6;
        color: #172b4d;
    }

    #jqlCondition:focus {
        background-color: #fff;
        outline: none;
        border-width: 2px;
        border-color: #4c9aff;
    }

    #selectExecutorIsOrIsNot-input {
        background-color: #fff;
    }

    #s2id_selectExecutorIsOrIsNot {
        width: 150px;
    }

    #selectExecutorMembershipType-input {
        background-color: #fff;
    }

    #s2id_selectExecutorMembershipType {
        width: 150px;
    }

    #selectMembershipRoles-input {
        background-color: #fff;
    }

    #s2id_selectMembershipRoles {
        width: 150px;
    }

    #selectMembershipGroups-field {
        background: white;
        height: 30px;
        width: 150px;
        padding-left: 26px !important;
        padding-right: 35px;
        resize: none;
        display: table-row;
        max-width: inherit;
        border: 2px solid #dfe1e6;
        border-radius: 3.01px;
        box-sizing: border-box;
        font-size: inherit;
        font-family: inherit;
        line-height: 1.4285714285714;
        padding: 3px 4px;
    }

    #selectMembershipGroups-field:hover {
        background-color: #ebecf0;
        border-color: #dfe1e6;
        color: #172b4d;
    }

    #selectMembershipGroups-field:focus {
        background-color: #fff;
        outline: none;
        border-width: 2px;
        border-color: #4c9aff;
    }

    #selectMembershipGroupsContainer,
    #selectMembershipRolesContainer {
        display: none;
    }

    .wholeContainer {
        border: 1px solid #dfe1e6;
        border-radius: 3px;
        padding: 15px;
        margin-top: 5px;
    }

    #jqlConditionLabel {
        width: 10px !important;
    }

    #jqlCondition {
        margin-left: 30px !important;
    }

    .required {
        color: red;
    }

    .requiredMessage {
        color: red !important;
        display: none;
    }
</style>