<div>
    <tr bgcolor="#ffffff">
        <td bgcolor="#ffffff" nowrap>
            <label for="cateName">输入分类名称</label>
            <input type="text" id="cateName" name="cateName" class="text" placeholder="在此输入分类名称" #if($cateName)value="$!cateName"#end>
        </td>
        <td bgcolor="#ffffff" nowrap>
            <label for="regex">请输入要匹配的正则表达式</label>
            <input type="text" id="regex" name="regex" class="text" placeholder="在此输入正则表达式" #if($regex)value="$!regex"#end>
        </td>
        <td bgcolor="#ffffff" nowrap>
            <label for="tipText">请输入提示文本</label>
            <textarea class="text-area long-field" id="tipText" name="tipText" style="height: 81px; width: 475px;" placeholder="请在此输入提示语，为空使用默认提示语'XX 文件分类下无符合XX正则的文件！'" >#if($!tipText!="")$!tipText#end</textarea>
        </td>
    </tr>
##    <tr>
##        <td>
##            ##            <input type="text" id="tipText" name="tipText" placeholder="请在此输入"
##            ##                   #if($tipText)value="$!tipText"#end>
##            <textarea class="textarea long-field" id="tipText" name="tipText" style="height: 81px; width: 475px;" placeholder="请在此输入提示语，为空使用默认提示语'XX 字段为必填项！'" rows="10" cols="30">#if($!tipText!="")$!tipText#end</textarea>
##        </td>
##    </tr>
    <input type="hidden" id="field_label">
</div>
##<script type="text/javascript">
##    AJS.$("#startTime,#endTime,#targetCustomField").auiSelect2();
##</script>