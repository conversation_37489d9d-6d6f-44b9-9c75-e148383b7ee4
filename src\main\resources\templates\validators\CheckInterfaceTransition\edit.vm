<div>
##    <tr bgcolor="#ffffff">
##        <td bgcolor="#ffffff" nowrap>
##            <label for="checkCustomFiled">需要校验的字段：</label>
##            <select name="checkCustomFiled" id="checkCustomFiled" multiple>
##                #foreach($bean in $!checkCustomFiledList)
##                    <option value="$bean.getId()"
##                        #if($!checkCustomFiled.contains($bean.getId())) selected="selected" #end>
##                        $!bean.getName()
##                    </option>
##                #end
##            </select>
##        </td>
##    </tr>
    <tr>
        <td>
            <textarea class="textarea long-field" id="tipText" name="tipText" style="height: 81px; width: 475px;" placeholder="请在此输入提示语，为空使用默认提示语'请到PBI系统处理！'" rows="10" cols="30">#if($!tipText!="")$!tipText#end</textarea>
        </td>
    </tr>
    <input type="hidden" id="field_label">
</div>
<script type="text/javascript">
    // AJS.$("#checkCustomFiled").auiSelect2();
</script>