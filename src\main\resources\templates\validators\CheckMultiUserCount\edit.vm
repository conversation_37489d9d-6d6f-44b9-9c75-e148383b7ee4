<div>
    <tr bgcolor="#ffffff">
        <td bgcolor="#ffffff" nowrap>
            <label for="checkCustomFiled">需要校验的字段：</label>
            <select name="checkCustomFiled" id="checkCustomFiled" >
                #foreach($bean in $!multiUserCustomFieldList)
                    <option value="$bean.getId()"
                        #if($!checkCustomFiled.contains($bean.getId())) selected="selected" #end>
                        $!bean.getName()
                    </option>
                #end
            </select>
        </td>
    </tr>
    <tr bgcolor="#ffffff">
        <td bgcolor="#ffffff" nowrap>
            <select name="isContain" id="isContain" >
                <option value="false" #if($!compareType=="false")selected="true" #end>不包含</option>
                <option value="true" #if($!compareType=="true")selected="true" #end>包含</option>
            </select>
            <label for="containCustomFiledList">包括以下字段用户：</label>
            <select name="containCustomFiledList" id="containCustomFiledList" multiple>
                #foreach($bean in $!userCustomFieldList)
                    <option value="$bean.getId()"
                        #if($!containCustomFiledList.contains($bean.getId())) selected="selected" #end>
                        $!bean.getName()
                    </option>
                #end
            </select>
        </td>
    </tr>
    <tr bgcolor="#ffffff">
        <td bgcolor="#ffffff" nowrap>
            <label for="compareType">且数量</label>
            <select name="compareType" id="compareType" >
                <option value="lessEqual" #if($!compareType=="lessEqual")selected="true" #end>小于等于</option>
                <option value="moreEqual" #if($!compareType=="moreEqual")selected="true" #end>大于等于</option>
            </select>
            <input type="number" id="targetUserCount" name="targetUserCount" value="$!targetUserCount">
        </td>
    </tr>
##    <tr>
##        <td>
##            ##            <input type="text" id="tipText" name="tipText" placeholder="请在此输入"
##            ##                   #if($tipText)value="$!tipText"#end>
##            <textarea class="textarea long-field" id="tipText" name="tipText" style="height: 81px; width: 475px;" placeholder="请在此输入提示语，为空使用默认提示语'XX 字段为必填项！'" rows="10" cols="30">#if($!tipText!="")$!tipText#end</textarea>
##        </td>
##    </tr>
    <input type="hidden" id="field_label">
</div>
<script type="text/javascript">
    AJS.$("#checkCustomFiled,#containCustomFiledList").auiSelect2();
</script>