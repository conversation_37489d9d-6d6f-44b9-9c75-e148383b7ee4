<div>
    <tr bgcolor="#ffffff">
        <td bgcolor="#ffffff" nowrap>
            结束时间
            <select name="endTime" id="endTime" >
                #foreach($bean in $!customFieldList)
                    <option value="$bean.getId()"
                        #if($!endTime==$bean.getId()) selected="true" #end>
                        $!bean.getName()
                    </option>
                #end
            </select>

             减去 开始时间
            <select name="startTime" id="startTime" >
                #foreach($bean in $!customFieldList)
                    <option value="$bean.getId()"
                        #if($!startTime==$bean.getId()) selected="true" #end>
                        $!bean.getName()
                    </option>
                #end
            </select>

            的天数
            <select name="compareType" id="compareType" >
                <option value="<" #if($!compareType=="<")selected="true" #end>小于</option>
                <option value=">" #if($!compareType==">")selected="true" #end>大于</option>
                <option value="=" #if($!compareType=="=")selected="true" #end>等于</option>
                <option value="<=" #if($!compareType=="<=")selected="true" #end>小于等于</option>
                <option value=">=" #if($!compareType==">=")selected="true" #end>大于等于</option>
            </select>

            <input type="number" id="targetNum" name="targetNum" placeholder="默认为0"
                   #if($targetNum)value="$!targetNum"#end>

            时，目标字段
            <select name="targetCustomField" id="targetCustomField" >
                #foreach($bean in $!targetCustomFieldList)
                    <option value="$bean.getId()"
                        #if($!targetCustomField==$bean.getId()) selected="true" #end>
                        $!bean.getName()
                    </option>
                #end
            </select>
            为必填字段
        </td>
    </tr>
    <tr>
        <td>
            ##            <input type="text" id="tipText" name="tipText" placeholder="请在此输入"
            ##                   #if($tipText)value="$!tipText"#end>
            <textarea class="textarea long-field" id="tipText" name="tipText" style="height: 81px; width: 475px;" placeholder="请在此输入提示语，为空使用默认提示语'XX 字段为必填项！'" rows="10" cols="30">#if($!tipText!="")$!tipText#end</textarea>
        </td>
    </tr>
    <input type="hidden" id="field_label">
</div>
<script type="text/javascript">
    AJS.$("#startTime,#endTime,#targetCustomField").auiSelect2();
</script>