<div class="aui-page-panel-inner">
    <input id="issueId" type="hidden" value="$!issueId">
    <table class="aui aui-table-sortable">
        <thead>
        <tr>
            <th width="8px">序号</th>
            <th width="150px">状态</th>
            <th width="60px">处理人</th>
            <th width="20px">版本操作</th>
            <th width="40px">操作时间</th>
        <tr>
        </thead>
        <tbody id="bombody">
        </tbody>
    </table>
</div>
<style>
    .aui-tabs.horizontal-tabs>.tabs-pane{
        overflow: auto;
    }
    table.aui{
        width:900px;
    }
</style>
<script type="text/javascript">
    function getBomList() {
        var issueid = $('#issueid').val()
        var url = AJS.contextPath() + "/rest/oa2jira/1.0/techdocs/get/bomlist/"+issueid
        jQuery.ajax({
            type: "GET",
            url: url,
            data: null,
            dataType: "json",
            async: false,
            contentType: "application/json",
            success: function (response) {
                if (response.result == true) {
                    jQuery.each(response.value,function(index,item){
                        let $tr = $('<tr>');
                        let $td = $('<td>').html((index+1)+"");
                            $tr.append($td);
                        jQuery.each(item,function(i,val){
                            if(i=='issueKey' || i == 'issueId' || i== 'no' || i == 'techId' || i=='docDate' || i=='parentKey'){
                                return true
                            }
                            if(i=='fileName'){
                                let url = $('#projecturl').val()+"/"+item.issueKey
                                let $td = $('<td>').html(`
                                    <a href="${url}">${val}</a>
                                `);
                                    $tr.append($td);
                                return true
                            }
                            let $td = $('<td>').html(val);
                                $tr.append($td);
                        });
                        $('tbody#bombody').append($tr);
                    });
                } else {
                    alert(response.message)
                }
            }
        })
    }

    $(function(){
        getBomList()
    })

</script>