<div
        class="aui user-browser ajs-dirty-warning-exempt"
        id="processForm"
        name="processForm">
    <div class="form-body">
        <div class="aui-group">
            <div class="aui-item">
                <div class="ajax-ss">
                    <!--<input class="text medium-long-field" type="text" id="processkey" placeholder="进度关键字"
                           name="processkey" value="" style="width:auto">
                    <button id="getprocessbutton" type="button" class="aui-button" onclick="getproceesbykey()">查询
                    </button>-->
                </div>
            </div>
        </div>
    </div>
    <div class="aui-page-panel-inner">
        <table class="aui aui-table-sortable" style="width:150%;">
            <thead>
            <tr>
                <th width="30px">序号</th>
                <th>进度</th>
                <th>下一步计划</th>
                <th>问题/风险</th>
                <th>对策</th>
                <th>创建时间</th>
                <th>操作</th>
            <tr>
            </thead>
            <tbody id="processbody">
            </tbody>
            
        </table>
        
    </div>
    <div class="processpage"></div>
</div>

<section id="process-dialog" class="aui-dialog2 aui-dialog2-medium aui-layer" data-aui-modal="true" role="dialog"
         aria-hidden="true">
    <!-- 这里用ID有时候行有时候不行-->
    <header class="aui-dialog2-header">
        <h2 class="aui-dialog2-header-main">进展详情</h2>
        <button class="aui-dialog2-header-close" aria-label="close">
            <span class="aui-icon aui-icon-small aui-iconfont-close-dialog"></span>
        </button>
    </header>
    <input type="hidden" id="createDate" name="createDate" value="">
    <div class="aui-dialog3-content">
        <form class="aui" name="processSubForm" id="processSubForm">
            <input type="hidden" id="id" name="id" value="">
            
            <input name="issueId" id="processIssueId" type="hidden" value="$!issueid">
            <div class="field-group">
                <label for="stage">进度</label>
                <textarea class="text medium-long-field" id="stage" name="stage"></textarea>
            </div>
            <div class="field-group">
                <label for="nextStep">下一步计划</label>
                <textarea class="text medium-long-field" id="nextStep" name="nextStep"></textarea>
            </div>
            <div class="field-group">
                <label for="risk">问题/风险</label>
                <textarea class="text medium-long-field" id="risk" name="risk"></textarea>
            </div>
            <div class="field-group">
                <label for="strategy">对策</label>
                <textarea class="text medium-long-field" id="strategy" name="strategy"></textarea>
            </div>
            <!--<div class="field-group">
                <label for="comment">评论</label>
                <textarea class="text medium-long-field" id="comment" name="comment"></textarea>
            </div>-->
        </form>
    </div>
    <footer class="aui-dialog2-footer">
        <div class="aui-dialog2-footer-actions">
            <button class="aui-button  aui-button-primary" type="button" onclick="updateProcess()">保存</button>
            <button class="aui-button" type="button" onclick="closeProcess()">取消</button>
        </div>
        ##
        <div class="aui-dialog2-footer-hint">This is a hint.</div>
    </footer>
</section>

<style>
    table.aui{
        width:900px;
    }
    .aui-tabs.horizontal-tabs>.tabs-pane{
        overflow: auto;
    }
    
    input{
        transition: background-color .2s ease-in-out,border-color .2s ease-in-out;
        border: 2px solid var(--aui-form-field-border-color);
        border-radius: 3.01px;
        box-sizing: border-box;
        font-size: inherit;
        margin: 0;
        vertical-align: baseline;
        height: 2.14285714em;
        line-height: 1.42857143;
        padding: 3px 4px;
    }
    .processpage{
        padding:8px;
    }
    .processpage a,.processpage span{
        padding: 10px 10px 0;
    }

</style>

<script type="text/javascript">
    /**
     * 新增按钮
     */
    function getproceesbykey() {
        let processkey = jQuery('#processkey').val()
        var issueid = jQuery('#processIssueId').val()
        var url = AJS.contextPath() + "/rest/oa2jira/1.0/progress"
        if (processkey != null && processkey != '' && processkey != undefined){
            url = url + "/listbystage/"+issueid+"/"+processkey
        }else{
            url = url  + "/list/"+issueid
        }
        jQuery('tbody#processbody').html('')
        getProcess(url)
    }
   

    function closeProcess() {
        AJS.dialog2("#process-dialog").hide();
    }
    function getProcessById(id) {
        var url = AJS.contextPath() + "/rest/oa2jira/1.0/progress/get/" + id;
        jQuery.ajax({
            type: "GET",
            url: url,
            data: "",
            dataType: "json",
            async: false,
            contentType: "application/json",
            success: function (response) {
                if (response.result == true) {
                    var process = response.value
                    jQuery('#processSubForm #id').val(process.id)
                    jQuery('#issueId').val(process.issueId)
                    jQuery('#stage').val(process.stage)
                    jQuery('#nextStep').val(process.nextStep)
                    jQuery('#risk').val(process.risk)
                    jQuery("#strategy").val(process.strategy)
                    jQuery("#createDate").val(RiQi(process.createDate))
                    AJS.dialog2("#process-dialog").show();
                } else {
                    alert(response.message)
                }
            }
        });
    }
    
    function updateProcess() {
        var stage = jQuery('#stage').val()
        if (stage == "") {
            alert("请输入进度")
            return;
        }
        var nextStep = jQuery('#nextStep').val()
        if (nextStep == "") {
            alert("请输入下一步计划")
            return;
        }
        var risk = jQuery('#risk').val()
        if (risk == "") {
            alert("请输入问题/风险")
            return;
        }
        var strategy = jQuery('#strategy').val()
        if (strategy == "") {
            alert("请输入对策")
            return;
        }
        
        var createDate = jQuery("#createDate").val()
        

        var datava = jQuery("#processSubForm").serializeJSON();
        var url = AJS.contextPath() + "/rest/oa2jira/1.0/progress/insert";
        var data = JSON.stringify(datava);
        jQuery.ajax({
            type: "POST",
            url: url,
            data: data,
            dataType: "json",
            async: false,
            contentType: "application/json",
            success: function (response) {
                if (response.result == true) {
                    
                    var $id = jQuery('#processSubForm #id').val()
                    if ($id == 'undefined' || !$id || !/[^\s]/.test($id)){
                        closeProcess()
                        var str_count = jQuery('tbody#processbody').children('tr').length;
                        var int_count = parseInt(str_count);
                        int_count = int_count+1;
                        jQuery('tbody#processbody').append(
                        `<tr id="row${response.value}">
                            <td>${int_count}</td>
                            <td>${stage}</td>
                            <td>${nextStep}</td>
                            <td>${risk}</td>
                            <td>${strategy}</td>
                            <td>${createDate}</td>
                            <td>
                                <button class="aui-button aui-button-primary" type="button" id="modifyprocessButton$!bean.getId()"
                                        onclick="getProcessById(${response.value})">修改
                                </button>
                                <button class="aui-button" type="button" id="delprocButton$!bean.getId()"
                                        onclick="deleteProcessButton(${response.value})">删除
                                </button>
                            </td>
                        </tr>`
                        )
                    }else{
                        closeProcess()
                        jQuery('#processbody #row' +  $id).children('td').eq(1).text(stage)
                        jQuery('#processbody #row' +  $id).children('td').eq(2).text(nextStep)
                        jQuery('#processbody #row' +  $id).children('td').eq(3).text(risk)
                        jQuery('#processbody #row' +  $id).children('td').eq(4).text(strategy)
                        jQuery('#processbody #row' +  $id).children('td').eq(5).text(createDate)   
                    }
                } else {
                    alert(response.message)
                }
            }
        })
    }
    
    /**
     * 删除按钮
     * @param id
     */
    function deleteProcessButton(id) {
        var url = AJS.contextPath() + "/rest/oa2jira/1.0/progress/delete/" + id;
        jQuery.ajax({
            type: "POST",
            url: url,
            data: "",
            dataType: "json",
            async: false,
            contentType: "application/json",
            success: function (response) {
                if (response.result == true) {
                    alert("成功")
                    jQuery('#row' + id).remove()
                } else {
                    alert(response.message)
                }
            }
        })
    }
    
    function getProcess(url) {
        
        jQuery.ajax({
            type: "GET",
            url: url,
            data: null,
            dataType: "json",
            async: false,
            contentType: "application/json",
            success: function (response) {
                if (response.result == true) {
                
                    $('.processpage').html(pagestr(response.pageNum,response.cur,10,4))
                    
                    jQuery.each(response.value,function(index,item){
                        let $tr = jQuery(`<tr id="row${item.id}">`);
                        let $td = jQuery('<td>').html((index+1)+"");
                        $tr.append($td);
                        jQuery.each(item,function(i,val){
                            
                            if(i == 'id' || i == 'issueId' || i == 'comment'){
                                return true
                            }
                            
                            if(i == 'createDate'){
                                val = RiQi(val)
                            }
                            
                            let $td = jQuery('<td>').html(val);
                            $tr.append($td);
                         });
                         let $_td = jQuery('<td>').html(
                                `<button class="aui-button aui-button-primary" type="button" id="modifyprocessButton$!bean.getId()"
                                        onclick="getProcessById(${item.id})">修改
                                </button>
                                <button class="aui-button" type="button" id="delprocButton$!bean.getId()"
                                        onclick="deleteProcessButton(${item.id})">删除
                                </button>`
                         );
                         $tr.append($_td);
                         jQuery('tbody#processbody').append($tr);
                    });
                } else {
                    alert(response.message)
                }
            }
        })
    }
    
    jQuery(function(){
        var issueid = jQuery('#processIssueId').val()
        var url = AJS.contextPath() + "/rest/oa2jira/1.0/progress"
        url = url  + "/list/"+issueid+"/p/0"
        getProcess(url)

        
    })

    function numClick(obj){
        let p = $(obj).attr('url')
        let _issueid = jQuery('#processIssueId').val()
        let $url =  AJS.contextPath() + "/rest/oa2jira/1.0/progress"
        $url = $url  + "/list/"+_issueid+"/p/"+p
        jQuery('tbody#processbody').html('')
        getProcess($url)
    }


    
    function RiQi(sj)
    {
        var now = new Date(sj);
        var year=now.getFullYear();     
        var   month=now.getMonth()+1;     
        var   date=now.getDate();     
        var   hour=now.getHours();     
        var   minute=now.getMinutes();     
        var   second=now.getSeconds();     
        return   year+"-"+month+"-"+date+"   "+hour+":"+minute+":"+second;     
    }

    function pagestr(countdatas,currentpage,everpage,beforlaterln){
        var page= "";
        if(countdatas <= everpage)
            return page;
        var intpart = Math.floor(countdatas/everpage);//整数
        var remainderpart = countdatas%everpage;//余数
        var countpage = intpart;//取整数
        var currentpage = currentpage<0 ? 0:currentpage;
        if(remainderpart==0){//余数判断
                currentpage=currentpage>countpage-1?countpage-1:currentpage;
                var pp=currentpage+1;
                var strbe="";
                var pagebefore=pp-beforlaterln-1;
                for(var i=pp; i > pagebefore; i--){
                    if(i>0 && i<pp){
                        strbe="<a class='num' onclick='numClick(this)' href='javascript:void(0);' url='"+(i-1)+"'>"+i+"</a>"+strbe;
                    }
                }
               if(pp<=1){strbe="";}else{strbe="<a class='num' onclick='numClick(this)' href='javascript:void(0);' url='0'>首页</a>"+"<a class='num' onclick='numClick(this)' href='javascript:void(0);' url='"+(pp-2)+"'>上一页</a>"+strbe;}
                var strmid="<span class='current'>"+pp+"</span>";
                var strlater="";
                var pagelater=pp+beforlaterln;
                for(var i=pp; i < pagelater; i++){
                    if(i<countpage){
                        strlater+="<a class='num' onclick='numClick(this)' href='javascript:void(0);' url='"+(i)+"'>"+(i+1)+"</a>";
                    }
                }
                if(pp>=countpage){}else{strlater+="<a class='num' onclick='numClick(this)' href='javascript:void(0);' url='"+pp+"'>下一页</a>"+"<a class='num' onclick='numClick(this)' href='javascript:void(0);' url='"+(countpage-1)+"'>尾页</a>";}
                page=strbe+strmid+strlater;
            /**/
         }else {
                countpage+=1;//总页数
                currentpage=currentpage>countpage-1?countpage-1:currentpage;
                var pp=currentpage+1;
                var strbe="";
                var pagebefore=pp-beforlaterln-1;
                for(var i=pp; i > pagebefore; i--){ 
                    if(i>0 && i<pp){
                        strbe="<a class='num' onclick='numClick(this)' href='javascript:void(0);' url='"+(i-1)+"'>"+i+"</a>"+strbe;
                    }
                }
                if(pp<=1){strbe="";}else{strbe="<a class='num' onclick='numClick(this)' href='javascript:void(0);' url='0'>首页</a>"+"<a class='num' onclick='numClick(this)' href='javascript:void(0);' url='"+(pp-2)+"'>上一页</a>"+strbe;}
                var strmid="<span class='current'>"+pp+"</span>";
                var strlater="";
                var pagelater=pp+beforlaterln;
                for(var i=pp; i < pagelater; i++){
                    if(i<countpage){
                        strlater+="<a class='num' onclick='numClick(this)' href='javascript:void(0);' url='"+(i)+"'>"+(i+1)+"</a>";
                    }
                }
                if(pp>=countpage){}else{strlater+="<a class='num' onclick='numClick(this)' href='javascript:void(0);' url='"+pp+"'>下一页</a>"+"<a class='num' onclick='numClick(this)' href='javascript:void(0);' url='"+(countpage-1)+"'>尾页</a>";}
                page=strbe+strmid+strlater;
            }
        //"<a>共"+countpage+"页</a>"+
        return page;
    }
</script>
