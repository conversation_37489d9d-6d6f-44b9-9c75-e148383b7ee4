<div >
##    <ul class="tabs-menu">
##        <li class="menu-item active-tab">
##            <a href="#techdocs">待签审文件</a>
##        </li>
##    </ul>

##    <div class="tabs-pane active-pane"
##         id="attachmentUnSigned">
##        <div class="aui-page-panel-inner"> 488
            <input id="issuekey" type="hidden" value="$!issuekey">
            <table class="aui" style="width: 100%">
                <thead>
                <tr>
                    <th id="attachmentCate" style="width: 80px">文件类别</th>
                    <th id="attachmentName" style="width: 420px">文件名</th>
##                    <th style="width: 60px">用户</th>
##                    <th style="width: 80px">文件类别</th>
##                    <th id="createDate" style="width: 160px">日期</th>
                <tr>
                </thead>
                <tbody id="attachmentBody">
                </tbody>
            </table>
##        </div>
##    </div>
</div>


<script type="text/javascript">
    function getAttachment() {
        var issuekey = $('#issuekey').val()
        // alert("issuekey: "+issuekey)
        var url = AJS.contextPath() + "/rest/oa2jira/1.0/attachment/get/unsigned/"+issuekey
        jQuery.ajax({
            type: "GET",
            url: url,
            data: null,
            dataType: "json",
            async: false,
            contentType: "application/json",
            success: function (response) {
                // console.log("responseResult:")
                // console.log(response)
                if (response.result == true) {
                    jQuery.each(response.value,function(index,item){
                        let $tr = $('<tr>');
                        ## let $td = $('<td>').html((index+1)+"");
                        ##     $tr.append($td);
                        const order = ['attachmentCate', 'attachmentName']; // 指定顺序
                        console.log("待签审文件:")
                        console.log(item)
                        // const values = order.map(key => obj[key]);
                        var i = 0;
                        for (i; i < order.length; i++) {
                            const key = order[i];
                            // const val = item[key];
                            // console.log(key)
                            // console.log(val)
                            // if(i=='attachmentId' || i == 'attachmentType' || i == 'attachmentUrl' ){
                            //     return true
                            // }
                            if (key == 'attachmentCate') {
                                let $td = $('<td headers="attachmentCate">').html(`<span >${item.attachmentCate}</span>`);
                                    $tr.append($td);
                                // return true
                            }
                            if (key == 'attachmentName') {
                                if (item.attachmentType == 'application/pdf') {
                                    let $td = $('<td headers="attachmentName">').html(`
                                    <a href="${item.attachmentUrl}" title="${item.attachmentName}-${item.attachmentAuthor}-${item.createDate} " file-preview-id="${item.attachmentId}" file-preview-title="${item.attachmentName}" file-preview-type="document" resolved>${item.attachmentName}</a>
                                        `);
                                        $tr.append($td);
                                } else {
                                    let $td = $('<td headers="attachmentName">').html(`<a href="${item.attachmentUrl}">${item.attachmentName}</a>`);
                                        $tr.append($td);
                                }

                                // return true
                            }
                            // key == 'createDate' || key == 'attachmentAuthor'

                            ## let $td = $('<td>').html(val);
                            ##     $tr.append($td);
                        };
                        $('tbody#attachmentBody').append($tr);
                    });
                } else {
                    alert(response.message)
                }
            }
        })
    }

    $(function(){
        getAttachment()
    })

</script>

<style>
    .aui-tabs>.tabs-menu .menu-item a:focus{
        box-shadow: none !important;
    }
    /*.aui-tabs.horizontal-tabs>.tabs-pane{*/
    /*    overflow: auto;*/
    /*}*/
    /*table.aui{*/
    /*    width:900px;*/
    /*}*/
</style>